<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.USE_SIP" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.USE_FULL_SCREEN_INTENT" />
    <!-- Android 13+ 需要通知权限 -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS"/>

    <uses-permission android:name="android.permission.WAKE_LOCK"/>
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW"/>
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE"/>
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />

    <application>

        <activity
            android:name=".activity.LinphoneMainActivity"
            android:exported="true"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.AppCompat.NoActionBar">
        </activity>

        <activity
            android:name=".activity.CallIncomingActivity"
            android:exported="true"
            android:screenOrientation="portrait"
            android:showOnLockScreen="true"
            android:turnScreenOn="true"
            android:showWhenLocked="true"
            android:launchMode="singleTask"
            android:excludeFromRecents="true"
            android:taskAffinity=""
            android:theme="@style/Theme.AppCompat.NoActionBar">
        </activity>

        <activity
            android:name=".activity.CallOutgoingActivity"
            android:exported="true"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.AppCompat.NoActionBar">
        </activity>

        <activity
            android:name=".activity.CallConnectActivity"
            android:exported="true"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.AppCompat.NoActionBar">
        </activity>

        <service
            android:name="org.linphone.core.tools.service.CoreService"
            android:foregroundServiceType="phoneCall|camera|microphone"
            android:label="@string/library_name"
            android:stopWithTask="false" />

        <service android:name=".core.VoIPForegroundService"
            android:enabled="true"
            android:exported="false"/>

        <receiver android:name=".broadcast.MyBroadcastReceiver"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <action android:name="com.okcis.siplibrary.CallState" />
            </intent-filter>
        </receiver>
    </application>

</manifest>