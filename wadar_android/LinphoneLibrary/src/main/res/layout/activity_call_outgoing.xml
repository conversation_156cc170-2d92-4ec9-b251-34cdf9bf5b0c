<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center_horizontal"
    android:orientation="vertical"
    android:background="@color/call_background">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <!--<ImageView
            android:id="@+id/call_header"
            android:layout_width="120dp"
            android:layout_height="120dp"
            android:layout_marginTop="147dp"/>-->

        <TextView
            android:id="@+id/call_display_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/white"
            android:textSize="24sp"
            android:layout_marginTop="20dp"
            android:text="132456"/>
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:gravity="center"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/white"
            android:textSize="18sp"
            android:text="@string/waiting_for_answer"/>

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="80dp"
        android:orientation="horizontal">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/micro_enable"
                android:layout_width="68dp"
                android:layout_height="68dp"
                android:background="@drawable/call_mic_selector"/>

            <TextView
                android:id="@+id/micro_enable_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:layout_marginTop="5dp"
                android:textColor="@color/white"
                android:textSize="16sp"
                android:text="@string/microphone"/>

        </LinearLayout>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/hang_up"
                android:layout_width="68dp"
                android:layout_height="68dp"
                android:background="@mipmap/call_hung_up"/>

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:layout_marginTop="5dp"
                android:textColor="@color/white"
                android:textSize="16sp"
                android:text="@string/hang_up"/>

        </LinearLayout>


        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/speaker_enable"
                android:layout_width="68dp"
                android:layout_height="68dp"
                android:background="@drawable/call_speaker_selector"/>

            <TextView
                android:id="@+id/speaker_enable_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:layout_marginTop="5dp"
                android:textColor="@color/white"
                android:textSize="16sp"
                android:text="@string/speaker"/>

        </LinearLayout>

    </LinearLayout>

</LinearLayout>