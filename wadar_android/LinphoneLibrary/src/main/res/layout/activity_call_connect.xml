<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/call_background">

    <RelativeLayout
        android:id="@+id/video_frame_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        android:gravity="center">

        <TextureView
            android:id="@+id/native_video_texture_view"
            android:layout_width="match_parent"
            android:layout_height="480dp" />

        <org.linphone.mediastream.video.capture.CaptureTextureView
            android:id="@+id/local_preview"
            android:layout_width="160dp"
            android:layout_height="200dp"
            android:layout_alignParentRight="true" />

    </RelativeLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:orientation="vertical">

            <!--<ImageView
                android:id="@+id/call_header"
                android:layout_width="120dp"
                android:layout_height="120dp"
                android:layout_marginTop="147dp" />-->

            <TextView
                android:id="@+id/call_display_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:text="132456"
                android:textColor="@color/white"
                android:textSize="24sp" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:gravity="center_horizontal|bottom"
            android:orientation="vertical">

            <Chronometer
                android:id="@+id/call_connect_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="50dp"/>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="80dp"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="vertical">

                <ImageView
                    android:id="@+id/micro_enable"
                    android:layout_width="68dp"
                    android:layout_height="68dp"
                    android:background="@drawable/call_mic_selector" />

                <TextView
                    android:id="@+id/micro_enable_text"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:gravity="center"
                    android:text="@string/microphone"
                    android:textColor="@color/white"
                    android:textSize="16sp" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="vertical">

                <ImageView
                    android:id="@+id/hang_up"
                    android:layout_width="68dp"
                    android:layout_height="68dp"
                    android:background="@mipmap/call_hung_up" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:layout_marginTop="5dp"
                    android:text="@string/hang_up"
                    android:textColor="@color/white"
                    android:textSize="16sp" />

            </LinearLayout>


            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="vertical">

                <ImageView
                    android:id="@+id/speaker_enable"
                    android:layout_width="68dp"
                    android:layout_height="68dp"
                    android:background="@drawable/call_speaker_selector" />

                <TextView
                    android:id="@+id/speaker_enable_text"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:layout_marginTop="5dp"
                    android:text="@string/speaker"
                    android:textColor="@color/white"
                    android:textSize="16sp" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</FrameLayout>