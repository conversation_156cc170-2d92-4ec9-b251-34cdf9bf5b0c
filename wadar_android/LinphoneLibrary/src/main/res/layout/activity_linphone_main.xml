<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center"
    android:background="@color/call_background"
    android:orientation="vertical">

    <TextView
        android:id="@+id/login_state"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/login_no_in"/>

    <Button
        android:id="@+id/login"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="20dp"
        android:visibility="gone"
        android:text="登录"/>

    <Button
        android:id="@+id/login_out"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="20dp"
        android:visibility="gone"
        android:text="注销登录"/>

    <EditText
        android:id="@+id/phone_num_edit"
        android:layout_width="match_parent"
        android:layout_marginHorizontal="100dp"
        android:layout_marginVertical="20dp"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:hint="输入拨打号码"
        android:textColorHint="@color/login_no_in"
        android:textColor="@color/white"/>

    <Button
        android:id="@+id/call_audio_phone"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="20dp"
        android:text="语音呼叫"/>

    <Button
        android:id="@+id/call_video_phone"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="视频呼叫"/>

</LinearLayout>