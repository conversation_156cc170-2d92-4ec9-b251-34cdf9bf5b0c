package com.okcis.siplibrary.activity;

import android.app.Activity;
import android.util.Log;
import android.view.KeyEvent;

import androidx.appcompat.app.AppCompatActivity;

import java.util.AbstractList;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

public class BaseActivity extends AppCompatActivity {
    private static List<Activity> currentActivityList = new LinkedList<>();

    public static synchronized Activity getCurrentActivity() {
        return (currentActivityList.size() > 0)?currentActivityList.get(currentActivityList.size() - 1):null;
    }

    public synchronized void setCurrentActivity(Activity activity) {
        currentActivityList.add(activity);
    }

    public synchronized void removeCurrentActivity(Activity activity){
        for (Activity currentActivity: currentActivityList){
            if (currentActivity == activity){
                currentActivityList.remove(activity);
            }
        }
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }
}
