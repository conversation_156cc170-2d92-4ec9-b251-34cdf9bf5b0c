package com.okcis.siplibrary.utils;

import static android.telephony.TelephonyManager.NETWORK_TYPE_EDGE;
import static android.telephony.TelephonyManager.NETWORK_TYPE_GPRS;
import static android.telephony.TelephonyManager.NETWORK_TYPE_IDEN;

import android.content.Context;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.os.Environment;
import android.util.Log;
import android.webkit.MimeTypeMap;

import org.linphone.core.Address;

import java.io.File;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

public class LinphoneUtils {

    /***
     * 检查网络带宽是否过低
     * @param context
     * @return
     */
    public static boolean checkIfNetworkHasLowBandwidth(Context context){
        ConnectivityManager connMgr =
                (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        NetworkInfo networkInfo = connMgr.getNetworkInfo(ConnectivityManager.TYPE_WIFI);
        if (networkInfo != null && networkInfo.isConnected()) {
            if (networkInfo.getType() == ConnectivityManager.TYPE_MOBILE) {
                if (networkInfo.getSubtype() == NETWORK_TYPE_EDGE
                        || networkInfo.getSubtype() == NETWORK_TYPE_GPRS
                        || networkInfo.getSubtype() == NETWORK_TYPE_IDEN){
                    return true;
                } else {
                    return false;
                }
            }
        }
        // In doubt return false
        return false;
    }

    /***
     * 录音文件地址
     * @param context
     * @param address
     * @return
     */
    public static String getRecordingFilePathForAddress(Context context, Address address) {
        String displayName = FileUtils.getDisplayName(address);
        DateFormat dateFormat = new SimpleDateFormat(
                "dd-MM-yyyy-HH-mm-ss",
                Locale.getDefault()
        );
        String fileName = displayName + "_" + dateFormat.format(new Date()) + ".mkv";
        return FileUtils.getFileStoragePath(context, fileName).getAbsolutePath();
    }


}
