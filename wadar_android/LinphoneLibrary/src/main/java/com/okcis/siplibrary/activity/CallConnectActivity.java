package com.okcis.siplibrary.activity;

import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.Toast;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.resource.bitmap.RoundedCorners;
import com.bumptech.glide.request.RequestOptions;
import com.okcis.siplibrary.R;
import com.okcis.siplibrary.core.LinphoneManager;
import com.okcis.siplibrary.databinding.ActivityCallConnectBinding;
import com.okcis.siplibrary.databinding.ActivityCallIncomingBinding;

import org.linphone.core.AudioDevice;
import org.linphone.core.Call;

public class CallConnectActivity extends BaseActivity {

    private ActivityCallConnectBinding callConnectBinding;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        callConnectBinding = ActivityCallConnectBinding.inflate(getLayoutInflater());
        setContentView(callConnectBinding.getRoot());
        setCurrentActivity(this);
        initView();
    }

    private void initView() {
        Call currentCall = LinphoneManager.getInstance().getCurrentCall();
        if (LinphoneManager.getInstance().isVideoCall(currentCall)){
            callConnectBinding.videoFrameView.setVisibility(View.VISIBLE);
            LinphoneManager.getInstance().setVideoWindowId(
                    callConnectBinding.localPreview,
                    callConnectBinding.nativeVideoTextureView
                    );
        }
        String displayName = currentCall.getRemoteAddress().getDisplayName();
        callConnectBinding.callDisplayName.setText(displayName);
        /*Glide.with(this)
                .load("https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fb-ssl.duitang.com%2Fuploads%2Fitem%2F201712%2F10%2F20171210134053_rMszF.thumb.700_0.jpeg&refer=http%3A%2F%2Fb-ssl.duitang.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1722393070&t=0d7923c0909e4fc04b71fadceb54440f")
                .placeholder(R.drawable.default_header)
                .apply(new RequestOptions().transform(new RoundedCorners(10)))
                .into(callConnectBinding.callHeader);*/

        callConnectBinding.callConnectTime.start();

        boolean defaultMicEnable = LinphoneManager.getInstance().micEnabled();
        callConnectBinding.microEnable.setSelected(defaultMicEnable);
        if (defaultMicEnable){
            callConnectBinding.microEnableText.setText(R.string.microphone);
        } else {
            callConnectBinding.microEnableText.setText(R.string.microphone_off);
        }
        callConnectBinding.microEnable.setOnClickListener(v -> {
            boolean micEnable = !LinphoneManager.getInstance().micEnabled();
            if (micEnable){
                callConnectBinding.microEnable.setSelected(true);
                callConnectBinding.microEnableText.setText(R.string.microphone);
            } else {
                callConnectBinding.microEnable.setSelected(false);
                callConnectBinding.microEnableText.setText(R.string.microphone_off);
            }
            LinphoneManager.getInstance().enableMic(micEnable);
        });


        boolean defaultSpeakerEnable = LinphoneManager.getInstance().speakerEnabled();
        callConnectBinding.speakerEnable.setSelected(defaultSpeakerEnable);
        if (defaultSpeakerEnable){
            callConnectBinding.speakerEnableText.setText(R.string.speaker_on);
        } else {
            if (LinphoneManager.getInstance().isBluetoothAudioRouteCurrentlyUsed()){
                callConnectBinding.speakerEnableText.setText(R.string.bluetooth);
            } else {
                callConnectBinding.speakerEnableText.setText(R.string.speaker);
            }
        }
        callConnectBinding.speakerEnable.setOnClickListener(v -> {
            boolean speakerEnable = !LinphoneManager.getInstance().speakerEnabled();
            if (speakerEnable){
                callConnectBinding.speakerEnable.setSelected(true);
                callConnectBinding.speakerEnableText.setText(R.string.speaker_on);
            } else {
                if (LinphoneManager.getInstance().isBluetoothAudioRouteAvailable()){
                    callConnectBinding.speakerEnable.setSelected(false);
                    callConnectBinding.speakerEnableText.setText(R.string.bluetooth);
                } else {
                    callConnectBinding.speakerEnable.setSelected(false);
                    callConnectBinding.speakerEnableText.setText(R.string.speaker);
                }
            }
            LinphoneManager.getInstance().enableSpeaker(speakerEnable);
        });

        callConnectBinding.hangUp.setOnClickListener(v -> {
            callConnectBinding.callConnectTime.stop();
            int state = LinphoneManager.getInstance().terminateCall(currentCall);
            if (state == 0) {
                finish();
            } else {
                Toast.makeText(this, R.string.call_hang_up_failed, Toast.LENGTH_LONG).show();
            }
        });
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        removeCurrentActivity(this);
    }
}
