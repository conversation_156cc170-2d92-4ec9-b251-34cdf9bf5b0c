package com.okcis.siplibrary.broadcast;

import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

import com.okcis.siplibrary.activity.BaseActivity;
import com.okcis.siplibrary.activity.CallConnectActivity;
import com.okcis.siplibrary.activity.CallIncomingActivity;
import com.okcis.siplibrary.activity.CallOutgoingActivity;
import com.okcis.siplibrary.activity.LinphoneMainActivity;

import org.linphone.core.Call;

public class MyBroadcastReceiver extends BroadcastReceiver {
    @Override
    public void onReceive(Context context, Intent intent) {
        if (intent.getAction() == "com.okcis.siplibrary.CallState"){
            int callState = intent.getIntExtra("CallState", -1);
            if (callState != -1){
                Activity currentActivity = BaseActivity.getCurrentActivity();
                if (callState == Call.State.IncomingReceived.toInt()){
                    Intent startIntent = new Intent(context, CallIncomingActivity.class);
                    startIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                    context.startActivity(startIntent);
                } else if (callState == Call.State.OutgoingInit.toInt()){
                    Intent startIntent = new Intent(context, CallOutgoingActivity.class);
                    startIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                    context.startActivity(startIntent);
                } else if (callState == Call.State.Connected.toInt()){
                    if (currentActivity instanceof CallOutgoingActivity){
                        currentActivity.finish();
                    } else if (currentActivity instanceof CallIncomingActivity){
                        currentActivity.finish();
                    }
                    Intent startIntent = new Intent(context, CallConnectActivity.class);
                    startIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                    context.startActivity(startIntent);
                } else if (callState == Call.State.End.toInt()){
                    if (currentActivity instanceof LinphoneMainActivity){

                    } else if (currentActivity instanceof CallOutgoingActivity){
                        currentActivity.finish();
                    } else if (currentActivity instanceof CallIncomingActivity){
                        currentActivity.finish();
                    } else if (currentActivity instanceof CallConnectActivity){
                        currentActivity.finish();
                    }

                } else if (callState == Call.State.Released.toInt()){
                    if (currentActivity instanceof LinphoneMainActivity){

                    } else if (currentActivity instanceof CallOutgoingActivity){
                        currentActivity.finish();
                    } else if (currentActivity instanceof CallIncomingActivity){
                        currentActivity.finish();
                    } else if (currentActivity instanceof CallConnectActivity){
                        currentActivity.finish();
                    }
                } else if (callState == Call.State.Error.toInt()){
                    if (currentActivity instanceof LinphoneMainActivity){

                    } else if (currentActivity instanceof CallOutgoingActivity){
                        currentActivity.finish();
                    } else if (currentActivity instanceof CallIncomingActivity){
                        currentActivity.finish();
                    } else if (currentActivity instanceof CallConnectActivity){
                        currentActivity.finish();
                    }
                }
            }
        }

    }
}
