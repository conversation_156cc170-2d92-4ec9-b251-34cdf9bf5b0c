package com.okcis.siplibrary.activity;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Build;
import android.os.Bundle;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;

import com.okcis.siplibrary.R;
import com.okcis.siplibrary.core.LinphoneManager;
import com.okcis.siplibrary.databinding.ActivityLinphoneMainBinding;

import org.linphone.core.RegistrationState;
import org.linphone.core.tools.service.CoreService;


@Deprecated//这个已经不用了， 代码在HomeActivity中
public class LinphoneMainActivity extends AppCompatActivity {

    private ActivityLinphoneMainBinding linphoneMainBinding;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        linphoneMainBinding = ActivityLinphoneMainBinding.inflate(getLayoutInflater());
        setContentView(linphoneMainBinding.getRoot());
        registerBroadcastReceiver();
        initView();
    }

    private void registerBroadcastReceiver() {
        IntentFilter filter = new IntentFilter("com.okcis.siplibrary.LoginState");
        registerReceiver(myReceiver, filter);
        LinphoneManager.getInstance().getLoginState();
    }

    private void initView() {
        linphoneMainBinding.login.setOnClickListener(v -> {
            LinphoneManager.getInstance().Login("1031","ocvLA4u03fdChK7g",
                    "*************","7160");
        });
        linphoneMainBinding.loginOut.setOnClickListener(v -> {
            LinphoneManager.getInstance().loginOut();
        });
        linphoneMainBinding.callAudioPhone.setOnClickListener(v -> {
            String number = linphoneMainBinding.phoneNumEdit.getText().toString();
            LinphoneManager.getInstance().startCall(number, false);
        });
        linphoneMainBinding.callVideoPhone.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                String number = linphoneMainBinding.phoneNumEdit.getText().toString();
                LinphoneManager.getInstance().startCall(number, true);
            }
        });
    }
    BroadcastReceiver myReceiver = new BroadcastReceiver(){
        @Override
        public void onReceive(Context context, Intent intent) {
            if (intent.getAction() == "com.okcis.siplibrary.LoginState"){
                int LoginState = intent.getIntExtra("LoginState", -1);
                if (LoginState != -1){
                    if (LoginState == RegistrationState.None.toInt()){
                        linphoneMainBinding.loginState.setText("未登录");
                        linphoneMainBinding.loginState.setTextColor(LinphoneMainActivity.this.getResources().getColor(R.color.login_no_in));
                    } else if (LoginState == RegistrationState.Progress.toInt()){
                        linphoneMainBinding.loginState.setText("登录中");
                        linphoneMainBinding.loginState.setTextColor(LinphoneMainActivity.this.getResources().getColor(R.color.login_in_progress));
                    } else if (LoginState == RegistrationState.Ok.toInt()){
                        linphoneMainBinding.loginState.setText("登录成功");
                        linphoneMainBinding.loginState.setTextColor(LinphoneMainActivity.this.getResources().getColor(R.color.login_success));
                    } else if (LoginState == RegistrationState.Cleared.toInt()){
                        linphoneMainBinding.loginState.setText("已退出登录");
                        linphoneMainBinding.loginState.setTextColor(LinphoneMainActivity.this.getResources().getColor(R.color.login_no_in));
                    } else if (LoginState == RegistrationState.Failed.toInt()){
                        linphoneMainBinding.loginState.setText("登录失败");
                        linphoneMainBinding.loginState.setTextColor(LinphoneMainActivity.this.getResources().getColor(R.color.login_error));
                    } else {
                        linphoneMainBinding.loginState.setText("未知错误");
                        linphoneMainBinding.loginState.setTextColor(LinphoneMainActivity.this.getResources().getColor(R.color.login_error));
                    }
                }
            }
        }
    };

    @Override
    protected void onDestroy() {
        super.onDestroy();
        unregisterReceiver(myReceiver);
    }
}
