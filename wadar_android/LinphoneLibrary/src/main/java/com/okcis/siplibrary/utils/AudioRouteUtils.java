package com.okcis.siplibrary.utils;

import android.util.Log;

import org.linphone.core.AudioDevice;
import org.linphone.core.Call;
import org.linphone.core.Core;

public class AudioRouteUtils {

    /***
     * 切换到听筒
     * @param core
     */
    public static void routeAudioToEarpiece(Core core) {
        if (core.getCallsNb() == 0) {
            Log.e("AudioRouteUtils","[Audio Route Helper] No call found, aborting earpiece audio route change");
            return;
        }
        Call currentCall = core.getCurrentCall();
        if (currentCall == null){
            currentCall = core.getCalls()[0];
        }

        for (AudioDevice audioDevice: core.getAudioDevices()) {
            if (audioDevice.getType() == AudioDevice.Type.Earpiece) {
                Log.i("AudioRouteUtils","[Audio Route Helper Earpiece] 找到听筒音频设备 "+ audioDevice.getDeviceName());
                currentCall.setOutputAudioDevice(audioDevice);
                return;
            }
        }
        Log.e("AudioRouteUtils","[Audio Route Helper] 找不到听筒音频设备");
    }

    /***
     * 切换到扬声器
     * @param core
     */
    public static void routeAudioToSpeaker(Core core) {
        if (core.getCallsNb() == 0) {
            Log.e("AudioRouteUtils","[Audio Route Helper] No call found, aborting speaker audio route change");
            return;
        }
        Call currentCall = core.getCurrentCall();
        if (currentCall == null){
            currentCall = core.getCalls()[0];
        }

        for (AudioDevice audioDevice: core.getAudioDevices()) {
            if (audioDevice.getType() == AudioDevice.Type.Speaker) {
                Log.i("AudioRouteUtils","[Audio Route Helper Speaker] 找到扬声器音频设备 "+ audioDevice.getDeviceName());
                currentCall.setOutputAudioDevice(audioDevice);
                return;
            }
        }
        Log.e("AudioRouteUtils","[Audio Route Helper] 找不到扬声器音频设备");
    }

    /***
     * 切换到蓝牙设备
     * @param core
     */
    public static void routeAudioToBluetooth(Core core) {
        if (core.getCallsNb() == 0) {
            Log.e("AudioRouteUtils", "[Audio Route Helper] No call found, aborting bluetooth audio route change");
            return;
        }
        Call currentCall = core.getCurrentCall();
        if (currentCall == null){
            currentCall = core.getCalls()[0];
        }

        for (AudioDevice audioDevice: core.getAudioDevices()) {
            if (audioDevice.getType() == AudioDevice.Type.Bluetooth) {
                if (audioDevice.hasCapability(AudioDevice.Capabilities.CapabilityPlay)) {
                    Log.i("AudioRouteUtils","[Audio Route Helper] 找到蓝牙音频设备 " + audioDevice.getDeviceName());
                    currentCall.setOutputAudioDevice(audioDevice);
                    return;
                }
            }
        }
        Log.e("AudioRouteUtils","[Audio Route Helper] 找不到蓝牙音频设备");
    }


    /***
     * 切换到耳机
     * @param core
     */
    public static void routeAudioToHeadset(Core core) {
        if (core.getCallsNb() == 0) {
            Log.e("AudioRouteUtils","[Audio Route Helper] No call found, aborting headset audio route change");
            return;
        }
        Call currentCall = core.getCurrentCall();
        if (currentCall == null){
            currentCall = core.getCalls()[0];
        }

        for (AudioDevice audioDevice: core.getAudioDevices()) {
            if (audioDevice.getType() == AudioDevice.Type.Headphones || audioDevice.getType() == AudioDevice.Type.Headset) {
                if (audioDevice.hasCapability(AudioDevice.Capabilities.CapabilityPlay)) {
                    Log.i("AudioRouteUtils","[Audio Route Helper] 找到有线耳机音频设备 " + audioDevice.getDeviceName());
                    currentCall.setOutputAudioDevice(audioDevice);
                    return;
                }
            }
        }
        Log.e("AudioRouteUtils","[Audio Route Helper] 找不到有线耳机音频设备");
    }

    /***
     * 耳机是否可用
     * @param core
     * @return
     */
    public static boolean isHeadsetAudioRouteAvailable(Core core) {
        for (AudioDevice audioDevice : core.getAudioDevices()) {
            if ((audioDevice.getType() == AudioDevice.Type.Headset || audioDevice.getType() == AudioDevice.Type.Headphones) &&
                    audioDevice.hasCapability(AudioDevice.Capabilities.CapabilityPlay)) {
                Log.i("AudioRouteUtils","[Audio Route Helper] 找到耳机设备 " + audioDevice.getDeviceName());
                return true;
            }
        }
        return false;
    }

    /***
     * 当前是否在使用蓝牙设备
     * @param core
     * @return
     */
    public static boolean isBluetoothAudioRouteCurrentlyUsed(Core core) {
        if (core.getCallsNb() == 0) {
            Log.w("AudioRouteUtils","[Audio Route Helper] No call found, so bluetooth audio route isn't used");
            return false;
        }
        Call currentCall = core.getCurrentCall();
        if (currentCall == null){
            currentCall = core.getCalls()[0];
        }

        AudioDevice audioDevice = currentCall.getOutputAudioDevice();
        Log.i("AudioRouteUtils","[Audio Route Helper] 当前正在使用的音频设备是 " + audioDevice.getDeviceName());
        return audioDevice.getType() == AudioDevice.Type.Bluetooth;
    }

    /***
     * 蓝牙设备是否可用
     * @param core
     * @return
     */
    public static boolean isBluetoothAudioRouteAvailable(Core core) {
        for (AudioDevice audioDevice : core.getAudioDevices()) {
            if (audioDevice.getType() == AudioDevice.Type.Bluetooth &&
                    audioDevice.hasCapability(AudioDevice.Capabilities.CapabilityPlay)) {
                Log.i("AudioRouteUtils","[Audio Route Helper] 找到蓝牙音频设备 " + audioDevice.getDeviceName());
                return true;
            }
        }
        return false;
    }
}
