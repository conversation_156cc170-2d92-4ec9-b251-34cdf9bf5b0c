package com.okcis.siplibrary.activity;

import android.os.Bundle;
import android.util.Log;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.resource.bitmap.RoundedCorners;
import com.bumptech.glide.request.RequestOptions;
import com.okcis.siplibrary.R;
import com.okcis.siplibrary.core.LinphoneManager;
import com.okcis.siplibrary.databinding.ActivityCallIncomingBinding;
import com.okcis.siplibrary.databinding.ActivityCallOutgoingBinding;

import org.linphone.core.Call;

public class CallOutgoingActivity extends BaseActivity {

    private ActivityCallOutgoingBinding callOutgoingBinding;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        callOutgoingBinding = ActivityCallOutgoingBinding.inflate(getLayoutInflater());
        setContentView(callOutgoingBinding.getRoot());
        setCurrentActivity(this);
        initView();
    }

    private void initView() {
        Call currentCall = LinphoneManager.getInstance().getCurrentCall();
        String displayName = currentCall.getRemoteAddress().getDisplayName();
        callOutgoingBinding.callDisplayName.setText(displayName);
        /*Glide.with(this)
                .load("https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fb-ssl.duitang.com%2Fuploads%2Fitem%2F201712%2F10%2F20171210134053_rMszF.thumb.700_0.jpeg&refer=http%3A%2F%2Fb-ssl.duitang.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1722393070&t=0d7923c0909e4fc04b71fadceb54440f")
                .placeholder(R.drawable.default_header)
                .apply(new RequestOptions().transform(new RoundedCorners(10)))
                .into(callOutgoingBinding.callHeader);*/
        callOutgoingBinding.microEnable.setSelected(true);
        callOutgoingBinding.microEnable.setOnClickListener(v -> {
            boolean micEnable = !LinphoneManager.getInstance().micEnabled();
            if (micEnable){
                callOutgoingBinding.microEnable.setSelected(true);
                callOutgoingBinding.microEnableText.setText(R.string.microphone);
            } else {
                callOutgoingBinding.microEnable.setSelected(false);
                callOutgoingBinding.microEnableText.setText(R.string.microphone_off);
            }
            LinphoneManager.getInstance().enableMic(micEnable);
        });

        callOutgoingBinding.hangUp.setOnClickListener(v -> {
            int state = LinphoneManager.getInstance().terminateCall(currentCall);
            if (state == 0) {
                finish();
            }
        });

        if (LinphoneManager.getInstance().isBluetoothAudioRouteCurrentlyUsed()){
            callOutgoingBinding.speakerEnableText.setText(R.string.bluetooth);
        }
        callOutgoingBinding.speakerEnable.setOnClickListener(v -> {
            boolean speakerEnable = !LinphoneManager.getInstance().speakerEnabled();
            if (speakerEnable){
                callOutgoingBinding.speakerEnable.setSelected(true);
                callOutgoingBinding.speakerEnableText.setText(R.string.speaker_on);
            } else {
                if (LinphoneManager.getInstance().isBluetoothAudioRouteAvailable()){
                    callOutgoingBinding.speakerEnable.setSelected(false);
                    callOutgoingBinding.speakerEnableText.setText(R.string.bluetooth);
                } else {
                    callOutgoingBinding.speakerEnable.setSelected(false);
                    callOutgoingBinding.speakerEnableText.setText(R.string.speaker);
                }
            }
            LinphoneManager.getInstance().enableSpeaker(speakerEnable);
        });
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        removeCurrentActivity(this);
    }
}
