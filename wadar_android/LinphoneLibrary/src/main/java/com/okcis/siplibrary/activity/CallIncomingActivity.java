package com.okcis.siplibrary.activity;

import android.content.Context;
import android.media.AudioManager;
import android.os.Bundle;
import android.os.PowerManager;

import androidx.annotation.Nullable;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.resource.bitmap.RoundedCorners;
import com.bumptech.glide.request.RequestOptions;
import com.okcis.siplibrary.R;
import com.okcis.siplibrary.core.LinphoneManager;
import com.okcis.siplibrary.databinding.ActivityCallIncomingBinding;

import org.linphone.core.Call;

public class CallIncomingActivity extends BaseActivity {
    private PowerManager.WakeLock wakeLock;
    private ActivityCallIncomingBinding callIncomingBinding;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        callIncomingBinding = ActivityCallIncomingBinding.inflate(getLayoutInflater());
        setContentView(callIncomingBinding.getRoot());
        setCurrentActivity(this);
        initView();

        // 唤醒屏幕
        PowerManager pm = (PowerManager) getSystemService(POWER_SERVICE);
        wakeLock = pm.newWakeLock(
                PowerManager.FULL_WAKE_LOCK | PowerManager.ACQUIRE_CAUSES_WAKEUP,
                "MyApp::IncomingCallWakeLock"
        );
        wakeLock.acquire(2 * 60 * 1000L); // 10分钟超时
    }


    private void initView() {

        Call currentCall = LinphoneManager.getInstance().getCurrentCall();
        String displayName = currentCall.getRemoteAddress().getDisplayName();
        callIncomingBinding.callDisplayName.setText(displayName);

        /*Glide.with(this)
                .load("https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fb-ssl.duitang.com%2Fuploads%2Fitem%2F201712%2F10%2F20171210134053_rMszF.thumb.700_0.jpeg&refer=http%3A%2F%2Fb-ssl.duitang.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1722393070&t=0d7923c0909e4fc04b71fadceb54440f")
                .placeholder(R.drawable.default_header)
                .apply(new RequestOptions().transform(new RoundedCorners(10)))
                .into(callIncomingBinding.callHeader);*/

        callIncomingBinding.answer.setOnClickListener(v -> {
            int state = LinphoneManager.getInstance().answerCall(currentCall);
            if (state == 0) {
                finish();
            }
        });

        callIncomingBinding.hangUp.setOnClickListener(v -> {
            int state = LinphoneManager.getInstance().declineCall(currentCall);
            if (state == 0) {
                AudioManager audioManager = (AudioManager) getApplicationContext().getSystemService(Context.AUDIO_SERVICE);
                if (audioManager != null) {
                    int ringerMode = audioManager.getRingerMode();
                    // 恢复为正常模式
                    audioManager.setRingerMode(AudioManager.RINGER_MODE_NORMAL);
                }
                finish();
            }
        });
    }

    @Override
    protected void onDestroy() {
        if (wakeLock != null && wakeLock.isHeld()) {
            wakeLock.release();
        }
        super.onDestroy();
        removeCurrentActivity(this);
    }
}
