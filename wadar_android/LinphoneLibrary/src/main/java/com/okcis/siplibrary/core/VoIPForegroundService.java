package com.okcis.siplibrary.core;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.app.Service;
import android.content.Intent;
import android.os.Build;
import android.os.IBinder;

import androidx.core.app.NotificationCompat;

import com.okcis.siplibrary.R;

public class VoIPForegroundService extends Service {
    private static final int NOTIFICATION_ID = 123;

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        // 创建通知渠道（Android 8.0+）
        createNotificationChannel();

        // 创建点击通知的 Intent
        Intent mainIntent = new Intent();
        mainIntent.setClassName(getPackageName(), "wvr.wadar.ui.HomeActivity");
        mainIntent.addCategory(Intent.CATEGORY_LAUNCHER);
        mainIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP);
        PendingIntent pendingIntent = PendingIntent.getActivity(
                this,
                0,
                mainIntent,
                PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE
        );

        // 构建通知
        Notification notification = new NotificationCompat.Builder(this, "voip_channel")
                .setContentTitle("语音通话服务运行中")
                .setContentText("若要在后台可接听电话这是必要的")
                .setSmallIcon(R.drawable.call_mic_selector)
                .setPriority(NotificationCompat.PRIORITY_LOW)
                .setContentIntent(pendingIntent) // 绑定点击事件
                .build();

        // 启动前台服务
        startForeground(NOTIFICATION_ID, notification);
        return START_STICKY;
    }

    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                    "voip_channel",
                    "语音通话服务",
                    NotificationManager.IMPORTANCE_LOW
            );
            getSystemService(NotificationManager.class).createNotificationChannel(channel);
        }
    }

    @Override
    public IBinder onBind(Intent intent) { return null; }
}
