
## Start of factory rc

# This file shall not contain path referencing package name, in order to be portable when app is renamed.
# Paths to resources must be set from LinphoneManager, after creating LinphoneCore.

[net]
mtu=1300
force_ice_disablement=0

[sip]
guess_hostname=1
register_only_when_network_is_up=1
auto_net_state_mon=1
auto_answer_replacing_calls=1
ping_with_options=0
use_cpim=1

[sound]
#remove this property for any application that is not Linphone public version itself
ec_calibrator_cool_tones=1

[video]
displaytype=MSAndroidTextureDisplay
auto_resize_preview_to_keep_ratio=1

[misc]
enable_basic_to_client_group_chat_room_migration=0
enable_simple_group_chat_message_state=0
aggregate_imdn=1
notify_each_friend_individually_when_presence_received=0

[app]
activation_code_length=4
prefer_basic_chat_room=1

[assistant]
xmlrpc_url=https://subscribe.linphone.org:444/wizard.php

[lime]
lime_update_threshold=-1

## End of factory rc
