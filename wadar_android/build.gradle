// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:8.8.0'

        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
        maven { url "https://maven.aliyun.com/repository/google"}
        maven { url "https://maven.aliyun.com/repository/releases"}
        maven { url "https://maven.aliyun.com/repository/central"}
        maven { url "https://maven.aliyun.com/repository/public"}
        maven { url "https://maven.aliyun.com/repository/gradle-plugin"}
        maven { url "https://maven.aliyun.com/repository/apache-snapshots"}
        maven { url "https://maven.aliyun.com/nexus/content/groups/public/"}
        maven { url "https://jitpack.io"}
        maven { url 'https://linphone.org/releases/maven_repository/' }
        // flatDir {dirs'libs'}
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}
