<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name">Wadar</string>

    <string name="string_unknown">未知</string>

    <string name="main_title">EspBluFi</string>
    <string name="main_bt_disable_msg">蓝牙不可用</string>
    <string name="main_location_disable_msg">位置信息(GPS)不可用</string>
    <string name="main_menu_settings">设置</string>

    <string name="main_menu_mqtt_config">MQTT配置</string>
    <string name="blufi_function_connect">连接</string>
    <string name="blufi_function_connect_msg">连接设备</string>
    <string name="blufi_function_disconnect">断开</string>
    <string name="blufi_function_disconnect_msg">与设备断开连接</string>
    <string name="blufi_function_security">加密</string>
    <string name="blufi_function_security_msg">与设备加密通信</string>
    <string name="blufi_function_version">版本</string>
    <string name="blufi_function_version_msg">获取设备版本</string>
    <string name="blufi_function_device_status">连接状态</string>
    <string name="blufi_function_device_status_msg">获取设备当前状态</string>
    <string name="blufi_function_configure">配网</string>
    <string name="blufi_function_configure_msg">配置设备模式</string>
    <string name="blufi_function_device_scan">扫描Wi-Fi</string>
    <string name="blufi_function_device_scan_msg">获取设备扫描到的 Wi-Fi 列表</string>
    <string name="blufi_function_custom">自定义</string>
    <string name="blufi_function_custom_msg">发送自定义数据给设备</string>
    <string name="blufi_custom_dialog_title">输入自定义文本</string>

    <string name="configure_title">配置</string>
    <string name="configure_sel_device_mode">选择设备模式</string>
    <string name="configure_softap_security">SoftAP 加密</string>
    <string name="configure_softap_ssid">SoftAP ssid</string>
    <string name="configure_softap_password">SoftAP 密码</string>
    <string name="configure_softap_password_error">SoftAP 密码过短</string>
    <string name="configure_softap_channel">SoftAP 信道</string>
    <string name="configure_softap_max_connection">SoftAP 最大连接数</string>
    <string name="configure_station_sel_wifi">选择 Wi-Fi</string>
    <string name="configure_station_ssid">Wi-Fi ssid</string>
    <string name="configure_station_ssid_error">Wi-Fi ssid 不能为空</string>
    <string name="configure_station_wifi_5g_error">设备不支持 5G Wi-Fi</string>
    <string name="configure_station_wifi_5g_dialog_message">设备不支持 5G Wi-Fi, 继续配网?</string>
    <string name="configure_station_wifi_5g_dialog_continue">继续</string>
    <string name="configure_station_wifi_5g_dialog_cancel">取消</string>
    <string name="configure_station_password">Wi-Fi 密码</string>
    <string name="configure_station_wifi_scanning">正在扫描 Wi-Fi…</string>
    <string name="configure_station_wifi_scanning_nothing">没有扫到 Wi-Fi</string>
    <string name="configure_wifi_disable_msg">Wi-Fi 不可用</string>
    <string name="configure_wifi_no_selected_msg">没有选择 Wi-Fi</string>

    <string name="settings_title">设置</string>
    <string name="settings_category_blufi_key">setting_category_blufi</string>
    <string name="settings_category_blufi_title">BluFi</string>
    <string name="settings_mtu_length_key">settings_key_mtu_length</string>
    <string name="settings_mtu_length_title">设置 mtu 长度</string>
    <string name="settings_mtu_length_hint">设置一个大于 15 的数</string>
    <string name="settings_ble_prefix_key">settings_key_ble_prefix</string>
    <string name="settings_ble_prefix_title">BLE 设备过滤</string>
    <string name="settings_ble_prefix_hint">设备名前缀</string>
    <string name="settings_category_version_title">版本</string>
    <string name="settings_version_key">settings_key_version</string>
    <string name="settings_version_title">APP 版本</string>
    <string name="settings_blufi_version_key">settings_key_blufi_version</string>
    <string name="settings_blufi_version_title">BluFi 库版本</string>
    <string name="settings_upgrade_check_key">settings_upgrade_check</string>
    <string name="settings_upgrade_check_title">检查更新</string>
    <string name="settings_upgrade_check_failed">获取最新版本信息失败</string>
    <string name="settings_upgrade_check_not_found">没有找到更新版本</string>
    <string name="settings_upgrade_check_current_latest">当前APP已经是最新版本</string>
    <string name="settings_upgrade_check_disciver_new">发现新版本APP, 点击开始下载</string>
    <string name="settings_upgrade_dialog_title">发现新版本</string>
    <string name="settings_upgrade_dialog_message">点击[升级]下载APK</string>
    <string name="settings_upgrade_dialog_upgrade">升级</string>
    <string name="mqtt_config_title">MQTT配置</string>
    <string name="mqtt_config_no_data">请先配置MQTT</string>
    <string name="mqtt_config_form_label1">机构ID</string>
    <string name="mqtt_config_form_label2">IP</string>
    <string name="mqtt_config_form_label3">端口</string>
    <string name="mqtt_config_form_label4">WSS</string>
    <string name="mqtt_config_form_label5">账号</string>
    <string name="mqtt_config_form_label6">密码</string>
    <string name="common_input_placeholder">请输入</string>
    <string name="device_info_title">设备信息</string>
    <string name="device_list_title">蓝牙设备列表</string>
    <string name="bluetooth_title">蓝牙设备</string>
    <string name="configure_success">配置成功</string>
    <string name="bluetooth_wifi_form_label1">设备</string>
    <string name="bluetooth_wifi_form_label2">WiFi SSID</string>
    <string name="bluetooth_wifi_form_label3">WiFi 密码</string>
    <string name="bluetooth_mqtt_form_label1">IP</string>
    <string name="bluetooth_mqtt_form_label2">端口</string>
    <string name="common_configure">配置</string>
    <string name="connected">已连接</string>
    <string name="disconnected">已断开</string>
    <string name="device_status">设备状态</string>
    <string name="bluetooth_status">蓝牙状态</string>
    <string name="device_info">设备信息</string>
    <string name="device_code">设备编码</string>
    <string name="company_id">机构ID</string>
    <string name="mqtt_info">MQTT信息</string>
    <string name="account">账号</string>
    <string name="mqtt_configuration">MQTT配置</string>
    <string name="wifi_configuration">WiFi配置</string>
    <string name="room_configuration">雷达配置</string>
    <string name="_3d_view">3D查看</string>
    <string name="start">开始</string>
    <string name="history">历史记录</string>
    <string name="save">保存</string>
    <string name="tcp_port">端口</string>
    <string name="scan_qrcode">扫描设备二维码</string>
    <string name="voip_configuration">VoIP配置</string>
    <string name="voip_call">VoIP呼叫</string>
    <string name="voip_status">VoIP状态</string>
    <string name="connection_status">连接状态</string>
    <string name="voip_info">VoIP信息</string>
    <string name="string_login0">未登录</string>
    <string name="string_login1">登录中</string>
    <string name="string_login2">登录成功</string>
    <string name="string_login3">已退出登录</string>
    <string name="string_login4">登录失败</string>
    <string name="string_login5">未知错误</string>
    <string name="permission_denied_msg">部分权限没有开启，请开启权限使用本应用</string>
    <string name="set_response_time">设置响应时间</string>
    <string name="fall_down">跌倒</string>
    <string name="stay_long">久滞</string>
    <string name="set_response_time_tips">久滞响应时间要求为:10的倍数</string>
    <string name="stay_long_time">久滞响应时间(分钟)</string>
    <string name="error_response_time_empty">响应时间不能为空</string>
    <string name="input_value_0_to_30">请输入0-30之间的值</string>
    <string name="input_multiple_of_10">请输入10的倍数</string>
    <string name="input_valid_number">请输入有效的数字</string>
    <string name="config_pending_activation">配置已下发，等待生效</string>
    <string name="config_activated">配置已生效</string>
    <string name="mqtt_conn_status">MQTT连接状态</string>
    <string name="cancel">取消</string>
</resources>
