<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="20dp">

    <!-- 友好提示 -->
    <TextView
        android:id="@+id/friendly_hint"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:text="@string/set_response_time_tips"
        android:textAppearance="?android:attr/textAppearanceMedium"
        android:textColor="#757575" />

    <!--<LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingBottom="10dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/fall_down"
            android:textAppearance="?android:attr/textAppearanceMedium" />

        <EditText
            android:id="@+id/input_fall_down"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            android:enabled="false"
            android:background="@android:color/darker_gray"
            android:text="5"/>
    </LinearLayout>-->

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingBottom="10dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/stay_long_time"
            android:textAppearance="?android:attr/textAppearanceMedium" />

        <EditText
            android:id="@+id/input_stay_long"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginLeft="30dp"
            android:text="10"
            />
    </LinearLayout>
</LinearLayout>