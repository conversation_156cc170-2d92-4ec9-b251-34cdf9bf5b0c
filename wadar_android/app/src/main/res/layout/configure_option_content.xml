<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/scroll_form"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingLeft="@dimen/activity_horizontal_margin"
    android:paddingTop="@dimen/activity_vertical_margin"
    android:paddingRight="@dimen/activity_horizontal_margin"
    android:paddingBottom="@dimen/activity_vertical_margin"
    app:layout_behavior="@string/appbar_scrolling_view_behavior"
    tools:context=".ui.ConfigureOptionsActivity"
    tools:showIn="@layout/configure_option_content">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <include
            android:id="@+id/selectForm"
            layout="@layout/configure_option_select_form" />

        <include
            android:id="@+id/softapForm"
            layout="@layout/configure_option_softap_form" />

        <include
            android:id="@+id/stationForm"
            layout="@layout/configure_option_station_form" />

        <Button
            android:id="@+id/confirm"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="35dp"
            android:text="@android:string/ok"
            android:textStyle="bold" />
    </LinearLayout>
</ScrollView>
