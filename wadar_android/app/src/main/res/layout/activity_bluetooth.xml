<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".ui.BluetoothActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/AppTheme.AppBarOverlay">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorPrimary"
            app:popupTheme="@style/AppTheme.PopupOverlay" />

    </com.google.android.material.appbar.AppBarLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Bluetooth Status CardView -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/device_status"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginBottom="8dp"
                    android:background="@color/divider_color" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="4dp"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/bluetoothStatusLabel"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/bluetooth_status" />

                    <TextView
                        android:id="@+id/bluetoothStatusTextView"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textSize="16sp" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="4dp"
                    android:orientation="horizontal">
                    <!-- 当前WiFi ssid -->
                    <TextView
                        android:id="@+id/wifiLabel"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="WiFi" />

                    <TextView
                        android:id="@+id/wifiTextView"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textSize="16sp" />
                </LinearLayout>

            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- WiFi Configuration CardView -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="WiFi"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginBottom="8dp"
                    android:background="@color/divider_color" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/bluetooth_wifi_form_label1"
                    android:textSize="16sp" />

                <EditText
                    android:id="@+id/deviceId"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="@string/common_input_placeholder"
                    android:inputType="text" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/bluetooth_wifi_form_label2"
                    android:textSize="16sp" />

                <EditText
                    android:id="@+id/ssid"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="@string/common_input_placeholder"
                    android:inputType="text" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/bluetooth_wifi_form_label3"
                    android:textSize="16sp" />

                <EditText
                    android:id="@+id/wifiPassword"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="@string/common_input_placeholder"
                    android:inputType="text" />

                <Button
                    android:id="@+id/configureButton"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="@string/common_configure" />

            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- WiFi Configuration CardView -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="MQTT"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginBottom="8dp"
                    android:background="@color/divider_color" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/bluetooth_mqtt_form_label1"
                    android:textSize="16sp" />

                <EditText
                    android:id="@+id/ip"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="@string/common_input_placeholder"
                    android:inputType="text" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/bluetooth_mqtt_form_label2"
                    android:textSize="16sp" />

                <EditText
                    android:id="@+id/port"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="@string/common_input_placeholder"
                    android:inputType="number" />

                <Button
                    android:id="@+id/configureMqtt"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="@string/common_configure" />

            </LinearLayout>
        </androidx.cardview.widget.CardView>


    </LinearLayout>
</LinearLayout>
