<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".ui.DeviceInfoActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/AppTheme.AppBarOverlay">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorPrimary"
            app:popupTheme="@style/AppTheme.PopupOverlay" />

    </com.google.android.material.appbar.AppBarLayout>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Device Info Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="4dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:text="@string/device_info"
                        android:textSize="18sp"
                        android:textStyle="bold" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginBottom="8dp"
                        android:background="@color/divider_color" />

                    <!-- Device Code -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="4dp"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/tvDeviceCodeLabel"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/device_code" />

                        <TextView
                            android:id="@+id/tvDeviceCode"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:textAlignment="textEnd" />
                    </LinearLayout>

                    <!-- Company ID -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="4dp"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/tvCompanyIdLabel"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/company_id" />

                        <TextView
                            android:id="@+id/tvCompanyId"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:textAlignment="textEnd" />
                    </LinearLayout>

                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- MQTT Info Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="4dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:text="@string/mqtt_info"
                        android:textSize="18sp"
                        android:textStyle="bold" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginBottom="8dp"
                        android:background="@color/divider_color" />

                    <!-- IP Address -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="4dp"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/tvIpLabel"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="IP" />

                        <TextView
                            android:id="@+id/tvIp"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:textAlignment="textEnd" />
                    </LinearLayout>

                    <!-- TCP Port -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="4dp"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/tvPortLabel"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/tcp_port" />

                        <TextView
                            android:id="@+id/tvPort"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:textAlignment="textEnd" />
                    </LinearLayout>

                    <!-- WSS -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="4dp"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/tvWssLabel"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="WSS" />

                        <TextView
                            android:id="@+id/tvWss"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="3"
                            android:textAlignment="textEnd" />
                    </LinearLayout>

                    <!-- Account -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/tvAccountLabel"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/account" />

                        <TextView
                            android:id="@+id/tvAccount"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:textAlignment="textEnd" />
                    </LinearLayout>

                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- VoIP Info Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="4dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:text="@string/voip_info"
                        android:textSize="18sp"
                        android:textStyle="bold" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginBottom="8dp"
                        android:background="@color/divider_color" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="4dp"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/tvSipIpLabel"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="IP" />

                        <TextView
                            android:id="@+id/tvSipIp"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:textAlignment="textEnd" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="4dp"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/tvSipPortLabel"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/tcp_port" />

                        <TextView
                            android:id="@+id/tvSipPort"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:textAlignment="textEnd" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/tvSipAccountLabel"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/account" />

                        <TextView
                            android:id="@+id/tvSipAccount"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:textAlignment="textEnd" />
                    </LinearLayout>

                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Connection Status Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="4dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:text="@string/connection_status"
                        android:textSize="18sp"
                        android:textStyle="bold" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginBottom="8dp"
                        android:background="@color/divider_color" />

                    <!-- MQTT Status -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="4dp"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/tvMqttStatusLabel"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/mqtt_conn_status" />

                        <TextView
                            android:id="@+id/tvMqttStatus"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:textAlignment="textEnd" />
                    </LinearLayout>

                    <!-- VoIP Status -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="4dp"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/tvVoIPStatusLabel"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/voip_status" />

                        <TextView
                            android:id="@+id/tvVoipStatus"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:textAlignment="textEnd" />
                    </LinearLayout>

                    <!-- Device Status -->
                    <!--<LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/tvDeviceStatusLabel"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Device Status" />

                        <TextView
                            android:id="@+id/tvDeviceStatus"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:textAlignment="textEnd" />
                    </LinearLayout>-->

                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Operation Buttons -->

            <androidx.gridlayout.widget.GridLayout
                android:id="@+id/grid_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                app:columnCount="2"
                app:rowCount="4">

                <!-- MQTT Configuration -->
                <Button
                    android:id="@+id/btnMqttConfig"
                    android:layout_width="0dp"
                    android:layout_height="60dp"
                    android:layout_margin="2dp"
                    android:backgroundTint="@color/colorPrimary"
                    android:text="@string/mqtt_configuration"
                    android:textColor="@color/white"
                    app:layout_column="0"
                    app:layout_columnWeight="1"
                    app:layout_row="0"
                    app:layout_rowWeight="1" />

                <!-- VoIP Configuration -->
                <Button
                    android:id="@+id/btnVoIPConfig"
                    android:layout_width="0dp"
                    android:layout_height="60dp"
                    android:layout_margin="2dp"
                    android:backgroundTint="@color/colorPrimary"
                    android:text="@string/voip_configuration"
                    android:textColor="@color/white"
                    app:layout_column="1"
                    app:layout_columnWeight="1"
                    app:layout_row="0"
                    app:layout_rowWeight="1" />

                <!-- WiFi Configuration -->
                <Button
                    android:id="@+id/btnWifiConfig"
                    android:layout_width="0dp"
                    android:layout_height="60dp"
                    android:layout_margin="2dp"
                    android:backgroundTint="@color/colorPrimary"
                    android:text="@string/wifi_configuration"
                    android:textColor="@color/white"
                    app:layout_column="0"
                    app:layout_columnWeight="1"
                    app:layout_row="1"
                    app:layout_rowWeight="1" />

                <!-- Room Configuration -->
                <Button
                    android:id="@+id/btnRoomConfig"
                    android:layout_width="0dp"
                    android:layout_height="60dp"
                    android:layout_margin="2dp"
                    android:backgroundTint="@color/colorPrimary"
                    android:text="@string/room_configuration"
                    android:textColor="@color/white"
                    app:layout_column="1"
                    app:layout_columnWeight="1"
                    app:layout_row="1"
                    app:layout_rowWeight="1" />

                <!-- 3D View -->
                <Button
                    android:id="@+id/btn3dView"
                    android:layout_width="0dp"
                    android:layout_height="60dp"
                    android:layout_margin="2dp"
                    android:backgroundTint="@color/colorPrimary"
                    android:text="@string/_3d_view"
                    android:textColor="@color/white"
                    app:layout_column="0"
                    app:layout_columnWeight="1"
                    app:layout_row="2"
                    app:layout_rowWeight="1" />

                <!-- VoIP Call -->
                <Button
                    android:id="@+id/btnVoIPCall"
                    android:layout_width="0dp"
                    android:layout_height="60dp"
                    android:layout_margin="2dp"
                    android:backgroundTint="@color/colorPrimary"
                    android:text="@string/voip_call"
                    android:textColor="@color/white"
                    android:enabled="false"
                    app:layout_column="1"
                    app:layout_columnWeight="1"
                    app:layout_row="2"
                    app:layout_rowWeight="1" />

                <Button
                    android:text="@string/set_response_time"
                    android:id="@+id/btnSetResponseTime"
                    android:layout_width="0dp"
                    android:layout_height="60dp"
                    android:layout_margin="2dp"
                    android:backgroundTint="@color/colorPrimary"
                    android:textColor="@color/white"
                    android:enabled="false"
                    app:layout_column="0"
                    app:layout_columnWeight="1"
                    app:layout_row="3"
                    app:layout_rowWeight="1" />
            </androidx.gridlayout.widget.GridLayout>

        </LinearLayout>
    </ScrollView>
</LinearLayout>