<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/stationOptions"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_marginTop="20dp"
    android:orientation="vertical"
    android:visibility="gone">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/configure_station_sel_wifi" />

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/station_wifi_ssid_form"
        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="15dp"
        android:layout_weight="1"
        app:endIconDrawable="@drawable/configure_refresh_icon"
        app:endIconMode="custom">

        <androidx.appcompat.widget.AppCompatAutoCompleteTextView
            android:id="@+id/station_ssid"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="@string/configure_station_ssid"
            android:padding="16dp"
            android:singleLine="true" />

    </com.google.android.material.textfield.TextInputLayout>

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/station_wifi_password_form"
        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="15dp"
        app:passwordToggleEnabled="true">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/station_wifi_password"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="@string/configure_station_password"
            android:inputType="textPassword"
            android:singleLine="true" />

    </com.google.android.material.textfield.TextInputLayout>
</LinearLayout>
