<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewModel"
            type="wvr.wadar.viewmodel.MqttViewModel" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        tools:context=".ui.MqttConfigActivity">

        <com.google.android.material.appbar.AppBarLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:theme="@style/AppTheme.AppBarOverlay">

            <androidx.appcompat.widget.Toolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                android:background="?attr/colorPrimary"
                app:popupTheme="@style/AppTheme.PopupOverlay" />

        </com.google.android.material.appbar.AppBarLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- 机构ID -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/mqtt_config_form_label1"
                android:textSize="16sp" />

            <EditText
                android:id="@+id/et_companyId"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="@string/common_input_placeholder"
                android:inputType="textUri"
                android:text="@={viewModel.mqttConfig.companyId}" />

            <!-- IP地址 -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="@string/mqtt_config_form_label2"
                android:textSize="16sp" />

            <EditText
                android:id="@+id/et_ip"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="@string/common_input_placeholder"
                android:inputType="phone"
                android:text="@={viewModel.mqttConfig.ip}" />

            <!-- 端口 -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="@string/mqtt_config_form_label3"
                android:textSize="16sp" />

            <EditText
                android:id="@+id/et_port"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="@string/common_input_placeholder"
                android:inputType="number"
                android:text="@={viewModel.mqttConfig.port}" />

            <!-- WSS -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="@string/mqtt_config_form_label4"
                android:textSize="16sp" />

            <EditText
                android:id="@+id/et_wss"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="@string/common_input_placeholder"
                android:text="@={viewModel.mqttConfig.wss}" />

            <!-- 账号 -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="@string/mqtt_config_form_label5"
                android:textSize="16sp" />

            <EditText
                android:id="@+id/et_account"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="@string/common_input_placeholder"
                android:text="@={viewModel.mqttConfig.account}" />

            <!-- 密码 -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="@string/mqtt_config_form_label6"
                android:textSize="16sp" />

            <EditText
                android:id="@+id/et_password"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="@string/common_input_placeholder"
                android:inputType="textPassword"
                android:text="@={viewModel.mqttConfig.password}" />

            <!-- 保存按钮 -->
            <Button
                android:id="@+id/btn_save"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:onClick="@{() -> viewModel.onSubmit()}"
                android:text="@string/save" />

        </LinearLayout>
    </LinearLayout>
</layout>