<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".ui.HomeActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/AppTheme.AppBarOverlay">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorPrimary"
            app:popupTheme="@style/AppTheme.PopupOverlay" />

    </com.google.android.material.appbar.AppBarLayout>

    <!-- 输入框和扫码按钮 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp">

        <EditText
            android:id="@+id/etDeviceId"
            android:layout_width="50dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:hint="@string/common_input_placeholder" />

        <wvr.wadar.iconfont.IconFontView
            android:id="@+id/btnScan"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:iconCode="@string/icon_tiaomasaoma"
            app:iconColor="@color/colorPrimary"
            app:iconSize="24dp" />
    </LinearLayout>


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">
        <!-- 开始配置按钮 -->
        <Button
            android:id="@+id/btnStartConfig"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/start" />

        <!-- 历史记录标题 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/history"
            android:textStyle="bold" />

        <!-- 历史记录列表 -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvHistory"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />
    </LinearLayout>
</LinearLayout>