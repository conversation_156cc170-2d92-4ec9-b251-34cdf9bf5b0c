<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/softapOptions"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="15dp"
    android:orientation="vertical"
    android:visibility="gone">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/configure_softap_security" />

    <Spinner
        android:id="@+id/softap_security_sp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:entries="@array/esp_blufi_configure_softap_security" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="15dp"
        android:orientation="horizontal">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="5dp"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/configure_softap_channel" />

            <Spinner
                android:id="@+id/softap_channel"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:entries="@array/esp_blufi_configure_softap_channel" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="5dp"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/configure_softap_max_connection" />

            <Spinner
                android:id="@+id/softap_max_connection"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:entries="@array/esp_blufi_configure_softap_max_connection" />
        </LinearLayout>
    </LinearLayout>

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/softap_ssid_form"
        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="15dp">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/softap_ssid"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="@string/configure_softap_ssid"
            android:singleLine="true" />

    </com.google.android.material.textfield.TextInputLayout>

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/softap_password_form"
        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="15dp"
        app:passwordToggleEnabled="true">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/softap_password"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="@string/configure_softap_password"
            android:inputType="textPassword"
            android:singleLine="true" />

    </com.google.android.material.textfield.TextInputLayout>

</LinearLayout>
