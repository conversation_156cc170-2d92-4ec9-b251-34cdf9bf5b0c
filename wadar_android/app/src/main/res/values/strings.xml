<resources>
    <string name="app_name">Wadar</string>

    <string name="string_unknown">Unknown</string>

    <string name="main_title">EspBluFi</string>
    <string name="main_bt_disable_msg">Bluetooth is disable</string>
    <string name="main_location_disable_msg">Location is disable</string>
    <string name="main_menu_settings">Settings</string>
    <string name="main_menu_mqtt_config">MQTT Config</string>

    <string name="blufi_function_connect">Connect</string>
    <string name="blufi_function_connect_msg">Try connecting device</string>
    <string name="blufi_function_disconnect">Disconnect</string>
    <string name="blufi_function_disconnect_msg">Disconnect device</string>
    <string name="blufi_function_security">Security</string>
    <string name="blufi_function_security_msg">Negotiate security with device</string>
    <string name="blufi_function_version">Version</string>
    <string name="blufi_function_version_msg">Get the device BluFi version</string>
    <string name="blufi_function_device_status">Status</string>
    <string name="blufi_function_device_status_msg">Get the device status</string>
    <string name="blufi_function_configure">Configure</string>
    <string name="blufi_function_configure_msg">Configure network for device</string>
    <string name="blufi_function_device_scan">ScanWifi</string>
    <string name="blufi_function_device_scan_msg">Get ssid list the device scanned</string>
    <string name="blufi_function_custom">Custom</string>
    <string name="blufi_function_custom_msg">Send custom data to device</string>
    <string name="blufi_custom_dialog_title">Input custom text</string>

    <string name="configure_title">Configure</string>
    <string name="configure_sel_device_mode">Select device mode</string>
    <string name="configure_softap_security">SoftAP Security</string>
    <string name="configure_softap_ssid">SoftAP ssid</string>
    <string name="configure_softap_password">SoftAP password</string>
    <string name="configure_softap_password_error">SoftAP password is too short</string>
    <string name="configure_softap_channel">SoftAP channel</string>
    <string name="configure_softap_max_connection">SoftAP max connection</string>
    <string name="configure_station_sel_wifi">Select Wi-Fi</string>
    <string name="configure_station_ssid">Wi-Fi ssid</string>
    <string name="configure_station_ssid_error">Wi-Fi ssid can\'t be empty</string>
    <string name="configure_station_wifi_5g_error">The device does not support 5G Wi-Fi</string>
    <string name="configure_station_wifi_5g_dialog_message">The device does not support 5G Wi-Fi, continue to configure?</string>
    <string name="configure_station_wifi_5g_dialog_continue">Continue</string>
    <string name="configure_station_wifi_5g_dialog_cancel">Cancel</string>
    <string name="configure_station_password">Wi-Fi password</string>
    <string name="configure_station_wifi_scanning">Scanning Wi-Fi…</string>
    <string name="configure_station_wifi_scanning_nothing">Found no Wi-Fi</string>
    <string name="configure_wifi_disable_msg">Wi-Fi is disable</string>
    <string name="configure_wifi_no_selected_msg">No Wi-Fi has selected</string>

    <string name="settings_title">Settings</string>
    <string name="settings_category_blufi_key">setting_category_blufi</string>
    <string name="settings_category_blufi_title">BluFi</string>
    <string name="settings_mtu_length_key">settings_key_mtu_length</string>
    <string name="settings_mtu_length_title">Set mtu length</string>
    <string name="settings_mtu_length_hint">Enter a number great than 15</string>
    <string name="settings_ble_prefix_key">settings_key_ble_prefix</string>
    <string name="settings_ble_prefix_title">BLE devices filter</string>
    <string name="settings_ble_prefix_hint">Device name start with</string>
    <string name="settings_category_version_title">Version</string>
    <string name="settings_version_key">settings_key_version</string>
    <string name="settings_version_title">APP Version</string>
    <string name="settings_blufi_version_key">settings_key_blufi_version</string>
    <string name="settings_blufi_version_title">BluFi lib version</string>
    <string name="settings_upgrade_check_key">settings_upgrade_check</string>
    <string name="settings_upgrade_check_title">Check APP latest version</string>
    <string name="settings_upgrade_check_failed">Get latest release information failed</string>
    <string name="settings_upgrade_check_not_found">Not found new release</string>
    <string name="settings_upgrade_check_current_latest">Current APP is latest</string>
    <string name="settings_upgrade_check_disciver_new">Discover new version APP, Click to download</string>
    <string name="settings_upgrade_dialog_title">Discover new version</string>
    <string name="settings_upgrade_dialog_message">Click [Upgrade] to download APK</string>
    <string name="settings_upgrade_dialog_upgrade">Upgrade</string>
    <string name="mqtt_config_title">MQTT</string>
    <string name="mqtt_config_no_data">Please configure MQTT first</string>
    <string name="mqtt_config_form_label1">Company ID</string>
    <string name="mqtt_config_form_label2">IP</string>
    <string name="mqtt_config_form_label3">TCP</string>
    <string name="mqtt_config_form_label4">WSS</string>
    <string name="mqtt_config_form_label5">Account</string>
    <string name="mqtt_config_form_label6">Password</string>
    <string name="common_input_placeholder">Please enter</string>
    <string name="device_info_title">Device Info</string>
    <string name="device_list_title">Device List</string>
    <string name="bluetooth_title">Bluetooth</string>
    <string name="configure_success">Successfully configured</string>
    <string name="bluetooth_wifi_form_label1">DeviceId</string>
    <string name="bluetooth_wifi_form_label2">WiFi SSID</string>
    <string name="bluetooth_wifi_form_label3">WiFi Password</string>
    <string name="bluetooth_mqtt_form_label1">IP</string>
    <string name="bluetooth_mqtt_form_label2">Port</string>
    <string name="common_configure">Configure</string>
    <string name="connected">Connected</string>
    <string name="disconnected">Disconnected</string>
    <string name="device_status">Device Status</string>
    <string name="bluetooth_status">Bluetooth Status</string>
    <string name="device_info">Device Info</string>
    <string name="device_code">Device code</string>
    <string name="company_id">Company ID</string>
    <string name="mqtt_info">MQTT Info</string>
    <string name="account">Account</string>
    <string name="mqtt_configuration">MQTT Configuration</string>
    <string name="wifi_configuration">WiFi Configuration</string>
    <string name="room_configuration">Radar Configuration</string>
    <string name="_3d_view">3D View</string>
    <string name="start">Start</string>
    <string name="history">History</string>
    <string name="save">Save</string>
    <string name="tcp_port">Port</string>
    <string name="scan_qrcode">Scan the QRCode</string>
    <string name="voip_configuration">VoIP Configuration</string>
    <string name="voip_call">VoIP Call</string>
    <string name="voip_status">VoIP Sratus</string>
    <string name="connection_status">Connection Status</string>
    <string name="voip_info">VoIP Info</string>
    <string name="string_login0">Not Logged In</string>
    <string name="string_login1">Logging In</string>
    <string name="string_login2">Login Successful</string>
    <string name="string_login3">Logged Out</string>
    <string name="string_login4">Login Failed</string>
    <string name="string_login5">Unknown Error</string>
    <string name="permission_denied_msg">Partial permissions are not enabled, please enable permissions to use this application</string>
    <string name="set_response_time">Set Device Response Time</string>
    <string name="fall_down">Fall Down</string>
    <string name="stay_long">Stay Long</string>
    <string name="set_response_time_tips">The Stay Long Response Time must be a multiple of 10</string>
    <string name="stay_long_time">Stay Long Response Time(minute)</string>
    <string name="error_response_time_empty">Response time cannot be empty</string>
    <string name="input_value_0_to_30">Please enter a value between 0 and 30</string>
    <string name="input_multiple_of_10">Please enter a multiple of 10</string>
    <string name="input_valid_number">Please enter a valid number</string>
    <string name="config_pending_activation">Configuration has been delivered and is pending activation</string>
    <string name="config_activated">Configuration activated and is now in effect</string>
    <string name="mqtt_conn_status">MQTT Connection Status</string>
    <string name="cancel">Cancel</string>

</resources>
