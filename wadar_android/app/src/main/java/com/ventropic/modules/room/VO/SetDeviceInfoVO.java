package com.ventropic.modules.room.VO;

import com.ventropic.care.proto.message.DeviceData;

import lombok.Data;

/**
 * 设备信息修改,直接对设备的/xxx/xxx/control通道发，消息内容如
 * {
    "type": "SetDeviceInfoVO",
    "msg": {
        "data": {
            "hotPlaceEn": 0
        },
        "seqId": 1234321
     }
   }
 */

public class SetDeviceInfoVO {
    /**
     * 发送消息体
     */
    private DeviceData data;

    /**
     * 返回消息唯一标识,用于发起方区分消息
     */
    private int seqId;

}
