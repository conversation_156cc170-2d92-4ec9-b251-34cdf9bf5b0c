package com.ventropic.modules.room.VO;

/**
 * <p>
 * 房间信息
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-27
 */
public class RoomVO {

    /**
     * 房间id
     */
    private String roomId;

    /**
     * 房间x长
     */
    private Float roomX;

    /**
     * 房间y长
     */
    private Float roomY;

    /**
     * 房间z长
     */
    private Float roomZ;

    /**
     * 获取房间id
     * @return 房间id
     */
    public String getRoomId() {
        return roomId;
    }

    /**
     * 设置房间id
     * @param roomId 房间id
     */
    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    /**
     * 获取房间x长
     * @return 房间x长
     */
    public Float getRoomX() {
        return roomX;
    }

    /**
     * 设置房间x长
     * @param roomX 房间x长
     */
    public void setRoomX(Float roomX) {
        this.roomX = roomX;
    }

    /**
     * 获取房间y长
     * @return 房间y长
     */
    public Float getRoomY() {
        return roomY;
    }

    /**
     * 设置房间y长
     * @param roomY 房间y长
     */
    public void setRoomY(Float roomY) {
        this.roomY = roomY;
    }

    /**
     * 获取房间z长
     * @return 房间z长
     */
    public Float getRoomZ() {
        return roomZ;
    }

    /**
     * 设置房间z长
     * @param roomZ 房间z长
     */
    public void setRoomZ(Float roomZ) {
        this.roomZ = roomZ;
    }
}
