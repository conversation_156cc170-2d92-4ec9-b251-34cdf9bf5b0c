package com.ventropic.modules.room.VO;

import java.io.Serializable;

import lombok.Data;

/**
 * <p>
 * 设备安装信息
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-27
 */

public class RoomDeviceRes implements Serializable {

    /**
     * 
     */
    private static final long serialVersionUID = 4653113546750128115L;

    /**
     * 设备房间关联表id
     */
    private String roomDeviceId;

    /**
     * 设备z坐标
     */
    private Float x;

    /**
     * 设备z坐标
     */
    private Float y;

    /**
     * 设备z坐标
     */
    private Float z;

    /**
     * 设备atilt倾角，-90到90。
     */
    private Float atilt;

    /**
     * 设备etilt倾角，-90到90。
     */
    private Float etilt;

    /**
     * 设备atilt视角，0到180。
     */
    private Float atiltfov;

    /**
     * 设备etilt视角，0到180。
     */
    private Float etiltfov;

    /**
     * 安装方式 0顶装,1侧装,2角装
     */
    private Integer mount;

}
