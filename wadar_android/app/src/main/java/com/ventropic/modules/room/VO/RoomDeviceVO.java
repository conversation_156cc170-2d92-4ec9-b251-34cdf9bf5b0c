package com.ventropic.modules.room.VO;

import lombok.Data;

/**
 * <p>
 * 设备安装信息
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-27
 */

public class RoomDeviceVO {

    /**
     * 设备房间关联表id
     */
    private String roomDeviceId;

    /**
     * 房间id
     */
    private String roomId;

    /**
     * 设备x坐标
     */
    private Float deviceX;

    /**
     * 设备y坐标
     */
    private Float deviceY;

    /**
     * 设备z坐标
     */
    private Float deviceZ;

    /**
     * 设备atilt倾角，-90到90。
     */
    private Float deviceAtilt;

    /**
     * 设备etilt倾角，-90到90。
     */
    private Float deviceEtilt;

    /**
     * 设备atilt视角，0到180。
     */
    private Float deviceAtiltfov;

    /**
     * 设备etilt视角，0到180。
     */
    private Float deviceEtiltfov;

    /**
     * 安装方式 0顶装,1侧装,2角装
     */
    private Integer deviceMount;


}
