package com.ventropic.modules.device.enums;

/** 	 
 * 升级状态
 * <AUTHOR>
*/
public enum WifiConfigStatusEnum {
    NOT_SEND("0", "不下发"),
	WAIT_SEND("1", "待下发"),
	SENDED("2", "已下发"),
	FAIL("3", "未更新"),
	COMPLETE("4", "已完成");

    private String code;
    private String name;

    WifiConfigStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
