package com.ventropic.modules.room.VO;

import java.io.Serializable;

import lombok.Data;

/**
 * <p>
 * 入口信息
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-27
 */
public class RoomEntranceRes implements Serializable {
    public String getEntranceId() {
        return entranceId;
    }

    public void setEntranceId(String entranceId) {
        this.entranceId = entranceId;
    }

    public Integer getGid() {
        return gid;
    }

    public void setGid(Integer gid) {
        this.gid = gid;
    }

    public String getCls() {
        return cls;
    }

    public void setCls(String cls) {
        this.cls = cls;
    }

    public Integer getDirection() {
        return direction;
    }

    public void setDirection(Integer direction) {
        this.direction = direction;
    }

    public Float getLength() {
        return length;
    }

    public void setLength(Float length) {
        this.length = length;
    }

    public Float getWidth() {
        return width;
    }

    public void setWidth(Float width) {
        this.width = width;
    }

    /**
     * 
     */
    private static final long serialVersionUID = 1562112952756021028L;

    /**
     * 入口id
     */
    private String entranceId;

    /**
     * 设备对应的entry_id
     */
    private Integer gid;

    /**
     * 入口类型，0unknow，1gate，2window
     */
    private String cls;

    /**
     * 初始位置
     */
    private Integer direction;

    /**
     * 距离初始位置距离，米
     */
    private Float length;

    /**
     * 门宽，米
     */
    private Float width;

    /**
     * 门高，米
     */
    private Float hight;


}
