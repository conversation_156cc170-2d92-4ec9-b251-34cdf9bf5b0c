package com.ventropic.modules.room.VO;

import java.io.Serializable;

import lombok.Data;

/**
 * <p>
 * 区域信息
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-27
 */

public class RoomRegionRes implements Serializable {

    /**
     * 
     */
    private static final long serialVersionUID = -4995785428533699223L;

    /**
     * 区域id
     */
    private String regionId;

    /**
     * 设备对应的region_id
     */
    private Integer rid;

    /**
     * 区域类型 0unknow,1bed,2table,3chair
     */
    private String cls;

    /**
     * 位置x，米
     */
    private Float positionX;

    /**
     * 位置y，米
     */
    private Float positionY;

    /**
     * x轴方向长度，米
     */
    private Float scaleX;

    /**
     * y轴方向长度，米
     */
    private Float scaleY;

    /**
     * 床高，米
     */
    private Float scaleZ;

    /**
     * 角度
     */
    private Float rotation;


}
