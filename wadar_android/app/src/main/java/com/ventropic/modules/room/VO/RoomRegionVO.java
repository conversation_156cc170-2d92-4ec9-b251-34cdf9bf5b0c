package com.ventropic.modules.room.VO;

import lombok.Data;

/**
 * <p>
 * 区域信息
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-27
 */

public class RoomRegionVO {

    /**
     * 区域id
     */
    private String regionId;

    /**
     * 房间id
     */
    private String roomId;

    /**
     * 设备对应的region_id
     */
    private Integer regionCode;

    /**
     * 区域类型 0unknow,1bed,2table,3chair
     */
    private Integer regionType;

    /**
     * 位置x，米
     */
    private Float positionX;

    /**
     * 位置y，米
     */
    private Float positionY;

    /**
     * x轴方向长度，米
     */
    private Float scaleX;

    /**
     * y轴方向长度，米
     */
    private Float scaleY;

    /**
     * 床高，米
     */
    private Float scaleZ;

    /**
     * 角度
     */
    private Float rotation;


}
