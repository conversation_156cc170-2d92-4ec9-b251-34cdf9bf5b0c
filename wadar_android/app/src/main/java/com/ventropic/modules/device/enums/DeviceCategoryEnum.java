package com.ventropic.modules.device.enums;

/** 	 
 * 设备状态
 * <AUTHOR> 	  
*/
public enum DeviceCategoryEnum {
    DEVICE_YUAN("1", "与安宝"), DEVICE_LIGHT("2", "灯"), DEVICE_SPEAKER("3", "音响");

    private String code;
    private String name;

    private DeviceCategoryEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
