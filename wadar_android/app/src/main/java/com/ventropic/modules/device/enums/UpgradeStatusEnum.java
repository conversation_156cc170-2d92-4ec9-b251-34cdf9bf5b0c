package com.ventropic.modules.device.enums;

/** 	 
 * 升级状态
 * <AUTHOR>
*/
public enum UpgradeStatusEnum {
    UPGRADE_IN_REQUEST("0", "升级请求中"),
    UPGRADE_REQUEST_SUCCESS("1", "升级请求成功"),
    UPGRADE_REQUEST_FAIL("2", "升级请求失败"),
    UPGRADE_SUCCESS("3", "升级成功"),
    UPGRADE_FAIL("4", "升级失败");

    private String code;
    private String name;

    UpgradeStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
