package com.ventropic.modules.room.VO;

import lombok.Data;

/**
 * <p>
 * 入口信息
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-27
 */

public class RoomEntranceVO {

    /**
     * 入口id
     */
    private String entranceId;

    /**
     * 房间id
     */
    private String roomId;

    /**
     * 设备对应的entry_id
     */
    private Integer entranceCode;

    /**
     * 入口类型，0unknow，1gate，2window
     */
    private Integer entranceType;

    /**
     * 初始位置
     */
    private Integer direction;

    /**
     * 距离初始位置距离，米
     */
    private Float entranceLength;

    /**
     * 门宽，米
     */
    private Float width;

    /**
     * 门高，米
     */
    private Float hight;


}
