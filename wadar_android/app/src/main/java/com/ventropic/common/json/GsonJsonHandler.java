package com.ventropic.common.json;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.nio.charset.StandardCharsets;
import java.util.Map;

/** 
 * 
 * <AUTHOR> 	  
*/
public class GsonJsonHandler implements JsonHandler {
    // 为了不引入依赖，先注释。到Android下再取消
     private final Gson gson = new Gson();
    @Override
    public Map<String, Object> parseTree(String json) {
         return gson.fromJson(json, new TypeToken<Map<String, Object>>() {}.getType());
    }

    @Override
    public <T> T fromJson(String json, Class<T> clazz) {
         return gson.fromJson(json, clazz);
    }

    @Override
    public Map<String, Object> fromJsonToMap(String json) {
         return gson.fromJson(json, new TypeToken<Map<String, Object>>() {}.getType());
    }

    @Override
    public String toJson(Object object) {
         return gson.toJson(object);
    }

    @Override
    public byte[] toJsonAsBytes(Object object) {
         String jsonString = gson.toJson(object);
         return jsonString.getBytes(StandardCharsets.UTF_8);
    }

}
