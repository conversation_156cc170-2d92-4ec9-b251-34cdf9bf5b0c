package com.ventropic.common.json;

import java.util.Map;

/** 
 * 编解码相关需要用在Android，Android中已经使用了Gson，为不将FastJson引入，所以做一工具类
 * <AUTHOR> 	  
*/
public class JsonUtils {
    private static JsonHandler jsonHandler;

    /**
     * 设置 JSON 处理器（由 Android 或 Spring 提供具体实现）
     */
    public static void setJsonHandler(JsonHandler handler) {
        if (jsonHandler == null) {
            jsonHandler = handler;
        }
    }
    
    /**
     * 将 JSON 字符串解析为树形结构（类似 TreeNode）
     */
    public static Map<String, Object> parseTree(String json){
        checkHandler();
        return jsonHandler.parseTree(json);
    }

    /**
     * 将对象序列化为 JSON 字符串
     */
    public static String toJson(Object object) {
        checkHandler();
        return jsonHandler.toJson(object);
    }
    
    /**
     * 将 JSON 字符串解析为 Map<String, Object>
     */
    public static Map<String, Object> fromJsonToMap(String json){
        checkHandler();
        return jsonHandler.fromJsonToMap(json);
    }

    /**
     * 将 JSON 字符串反序列化为指定类型的对象
     */
    public static <T> T fromJson(String json, Class<T> clazz) {
        checkHandler();
        return jsonHandler.fromJson(json, clazz);
    }
    
    /**
     * 将对象序列化为字节数组
     */
    public static byte[] toJsonAsBytes(Object object) {
        checkHandler();
        return jsonHandler.toJsonAsBytes(object);
    }
    
    private static void checkHandler() {
        if (jsonHandler == null) {
            throw new IllegalStateException("JsonHandler is not initialized!");
        }
    }
}
