package com.ventropic.common.json;

import java.util.Map;

/** 	
 * 将处理json相关的单独抽象出来，以便于编解码等核心代码不用与json库相关，这样就可以用在Android 
 * <AUTHOR> 	  
*/
public interface JsonHandler {
    /**
     * 将 JSON 字符串解析为树形结构（类似 TreeNode）
     */
    Map<String, Object> parseTree(String json);

    /**
     * 将 JSON 字符串解析为指定类型的对象
     */
    <T> T fromJson(String json, Class<T> clazz);

    /**
     * 将 JSON 字符串解析为 Map<String, Object>
     */
    Map<String, Object> fromJsonToMap(String json);

    /**
     * 将对象序列化为 JSON 字符串
     */
    String toJson(Object object);
    
    /**
     * 将对象序列化为字节数组
     */
    byte[] toJsonAsBytes(Object object);
}