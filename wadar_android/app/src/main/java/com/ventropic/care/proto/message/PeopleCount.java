package com.ventropic.care.proto.message;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
 @NoArgsConstructor
public class PeopleCount implements CareMessage {
    /**
     * 设备系统时间
     */
    private long ts;

    /**
     * 保留字段
     */
    private int reserved;
    /**
     * 人数
     */
    private int num;

    public long getTs() {
        return ts;
    }

    public void setTs(long ts) {
        this.ts = ts;
    }

    public int getReserved() {
        return reserved;
    }

    public void setReserved(int reserved) {
        this.reserved = reserved;
    }

    public int getNum() {
        return num;
    }

    public void setNum(int num) {
        this.num = num;
    }
}
