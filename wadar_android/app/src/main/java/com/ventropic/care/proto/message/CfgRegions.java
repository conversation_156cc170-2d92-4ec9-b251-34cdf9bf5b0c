package com.ventropic.care.proto.message;


import lombok.NoArgsConstructor;

 @NoArgsConstructor
public class CfgRegions implements CareMessage{
    private Region[] regions;

    public Region[] getRegions() {
        return regions;
    }

    public void setRegions(Region[] regions) {
        this.regions = regions;
    }

     @NoArgsConstructor
    public static class Region {
        private int rid;
        private int cls;
        private float positionX;
        private float positionY;
        private float scaleX;
        private float scaleY;
        private float scaleZ;
        private float rotation;

        public Region set(int rid, int cls, float positionX, float positionY, float scaleX, float scaleY, float scaleZ, float rotation){
            this.rid = rid;
            this.cls = cls;
            this.positionX = positionX;
            this.positionY = positionY;
            this.scaleX = scaleX;
            this.scaleY = scaleY;
            this.scaleZ = scaleZ;
            this.rotation = rotation;
            return this;
        }

        public int getRid() {
            return rid;
        }

        public void setRid(int rid) {
            this.rid = rid;
        }

        public int getCls() {
            return cls;
        }

        public void setCls(int cls) {
            this.cls = cls;
        }

        public float getPositionX() {
            return positionX;
        }

        public void setPositionX(float positionX) {
            this.positionX = positionX;
        }

        public float getPositionY() {
            return positionY;
        }

        public void setPositionY(float positionY) {
            this.positionY = positionY;
        }

        public float getScaleX() {
            return scaleX;
        }

        public void setScaleX(float scaleX) {
            this.scaleX = scaleX;
        }

        public float getScaleY() {
            return scaleY;
        }

        public void setScaleY(float scaleY) {
            this.scaleY = scaleY;
        }

        public float getScaleZ() {
            return scaleZ;
        }

        public void setScaleZ(float scaleZ) {
            this.scaleZ = scaleZ;
        }

        public float getRotation() {
            return rotation;
        }

        public void setRotation(float rotation) {
            this.rotation = rotation;
        }
    }

}
