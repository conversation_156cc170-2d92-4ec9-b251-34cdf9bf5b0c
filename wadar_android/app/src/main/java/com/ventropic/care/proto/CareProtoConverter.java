package com.ventropic.care.proto;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;

import com.ventropic.care.proto.message.CareMessage;
import com.ventropic.rfc.proto.plat.Message;
import com.ventropic.rfc.proto.plat.PlatCodec;
import com.ventropic.rfc.proto.support.AbstractConverter;


public class CareProtoConverter extends AbstractConverter {


    @Override protected void encode(Object msg, ByteArrayOutputStream output) throws Exception {
        ByteArrayOutputStream appOut = new ByteArrayOutputStream();
        Codec.encode((CareMessage)msg, appOut);

        Message dmsg = new Message();
        dmsg.payload = appOut.toByteArray();
        PlatCodec.encode(dmsg, output);
    }

    @Override protected  Object decode(InputStream payload) throws IOException {
        Message msg = PlatCodec.decode(payload);
        return Codec.decode(new ByteArrayInputStream(msg.payload));
    }

    @Override protected Class<?> lookupTypeClass(String type) throws ClassNotFoundException {
        return Class.forName(CareMessage.class.getPackage().getName() + "." + type);
    }
}
