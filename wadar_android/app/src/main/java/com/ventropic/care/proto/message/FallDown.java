package com.ventropic.care.proto.message;


import lombok.NoArgsConstructor;

 @NoArgsConstructor
public class FallDown implements CareMessage{
    private long ts;
    private int tid;
    private short conf;
    private float x;
    private float y;
    private float z;

    public long getTs() {
        return ts;
    }

    public void setTs(long ts) {
        this.ts = ts;
    }

    public int getTid() {
        return tid;
    }

    public void setTid(int tid) {
        this.tid = tid;
    }

    public short getConf() {
        return conf;
    }

    public void setConf(short conf) {
        this.conf = conf;
    }

    public float getX() {
        return x;
    }

    public void setX(float x) {
        this.x = x;
    }

    public float getY() {
        return y;
    }

    public void setY(float y) {
        this.y = y;
    }

    public float getZ() {
        return z;
    }

    public void setZ(float z) {
        this.z = z;
    }
}
