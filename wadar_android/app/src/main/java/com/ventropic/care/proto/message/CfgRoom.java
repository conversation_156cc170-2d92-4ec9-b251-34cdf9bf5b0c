package com.ventropic.care.proto.message;


import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */

@NoArgsConstructor
public class CfgRoom implements CareMessage{
    private CfgArea cfgArea;
    private CfgDevicePosition cfgDevicePosition;
    private CfgRegions cfgRegions;
    private CfgEntrances cfgEntrances;

    public CfgArea getCfgArea() {
        return cfgArea;
    }

    public void setCfgArea(CfgArea cfgArea) {
        this.cfgArea = cfgArea;
    }

    public CfgDevicePosition getCfgDevicePosition() {
        return cfgDevicePosition;
    }

    public void setCfgDevicePosition(CfgDevicePosition cfgDevicePosition) {
        this.cfgDevicePosition = cfgDevicePosition;
    }

    public CfgRegions getCfgRegions() {
        return cfgRegions;
    }

    public void setCfgRegions(CfgRegions cfgRegions) {
        this.cfgRegions = cfgRegions;
    }

    public CfgEntrances getCfgEntrances() {
        return cfgEntrances;
    }

    public void setCfgEntrances(CfgEntrances cfgEntrances) {
        this.cfgEntrances = cfgEntrances;
    }
}
