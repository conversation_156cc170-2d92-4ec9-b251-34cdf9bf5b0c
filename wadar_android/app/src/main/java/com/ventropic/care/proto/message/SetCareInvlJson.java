package com.ventropic.care.proto.message;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */

@EqualsAndHashCode(callSuper=false)
public class SetCareInvlJson extends JsonMessage {

    /**
     * 发送消息体
     */
    private SetData data;

    
    public static class SetData {
        private int fallConfInvl;
        private int stayTooLongInvl;
        private int locaRptInvl;
        private int hbRptInvl;
    }

    /**
     * 覆写该方法是为了能过够打印出父类的属性值
     * @return
     */
    @Override
    public String toString() {
        return "SetCareInvlJson{" +
                "msgId='" + super.getMsgId() + '\'' +
                ", seqId=" + super.getSeqId() +
                ", data=" + data +
                '}';
    }
}
