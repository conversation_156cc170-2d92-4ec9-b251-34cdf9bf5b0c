package com.ventropic.care.proto.message;

import lombok.Data;

/**
 * <AUTHOR>
 */
public class RoomJson {
    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public float[] getArea() {
        return area;
    }

    public void setArea(float[] area) {
        this.area = area;
    }

    public float[] getDevPos() {
        return devPos;
    }

    public void setDevPos(float[] devPos) {
        this.devPos = devPos;
    }

    public float[][] getEntrys() {
        return entrys;
    }

    public void setEntrys(float[][] entrys) {
        this.entrys = entrys;
    }

    public float[][] getRegions() {
        return regions;
    }

    public void setRegions(float[][] regions) {
        this.regions = regions;
    }
    
    private int type;

    /**
     * left, right, back, front, ceil
     */
    private float[] area;
    /**
     * hight, atilt, etilt, afov, efov, mount
     * mount:int32 0:TOP,1:SIDE,2:CORNER
     */
    private float[] devPos;
    /**
     * eid, cls, dir, center, width, hight
     * cls: 0:unknow, 1:gate, 2:window;  direction: 0:North, 1:South, 2:West, 3:East
     */
    private float[][] entrys;
    /**
     * rid, cls, px, py, sx, sy, sz, rotation
     * cls: 0:unknow, 1:bed, 2:table,3:chair
     */
    private float[][] regions;

}
