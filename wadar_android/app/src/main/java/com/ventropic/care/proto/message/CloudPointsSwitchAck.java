package com.ventropic.care.proto.message;


import lombok.NoArgsConstructor;

/**
 * @Class: CfgVsRange
 * @Package: com.ventropic.care.proto.message
 * @Author: Mr_fan
 * @Date: 2021/5/28 10:21
 * @Description:
 */

@NoArgsConstructor
public class CloudPointsSwitchAck implements CareMessage {
    private int ack;

    public int getAck() {
        return ack;
    }

    public void setAck(int ack) {
        this.ack = ack;
    }
}
