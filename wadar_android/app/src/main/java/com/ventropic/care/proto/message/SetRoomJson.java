package com.ventropic.care.proto.message;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */

@EqualsAndHashCode(callSuper=false)
public class SetRoomJson extends JsonMessage {

    /**
     * 发送消息体
     */
    private RoomJson data;

    /**
     * 覆写该方法是为了能过够打印出父类的属性值
     * @return
     */
    @Override
    public String toString() {
        return "SetRoomJson{" +
                "msgId='" + super.getMsgId() + '\'' +
                ", seqId=" + super.getSeqId() +
                ", data=" + data +
                '}';
    }
}
