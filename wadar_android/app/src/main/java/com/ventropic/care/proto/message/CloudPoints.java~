package com.ventropic.care.proto.message;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Class: CloudPointsSwiith
 * @Package: com.ventropic.care.proto.message
 * @Author: Mr_fan
 * @Date: 2021/5/28 14:14
 * @Description:
 */
@Data
@NoArgsConstructor
public class CloudPoints implements CareMessage {
    private short level;
    private int amount;
    private int pointCnt;
    private Frame[] frames;

    @Data @NoArgsConstructor
    public static class Frame {
        private float x;
        private float y;
        private float z;
        private float velocity;
        private float snr;

        public Frame set(float x, float y, float z, float velocity, float snr) {
            this.x = x;
            this.y = y;
            this.z = z;
            this.velocity = velocity;
            this.snr = snr;
            return this;
        }
    }
}