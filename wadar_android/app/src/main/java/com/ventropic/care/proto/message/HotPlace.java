package com.ventropic.care.proto.message;

import lombok.Data;

/** 
 * <AUTHOR> 	  
*/

public class HotPlace implements CareMessage {

    /**
     * 毫秒时间戳
     */
    private long ts;
    /**
     * 协议版本， 当前为1
     */
    private short version;
    private short start; // 1:开始,0:结束
    private short res[];
    private float period;
    private float x;
    private float y;
    private float z;

    public long getTs() {
        return ts;
    }

    public void setTs(long ts) {
        this.ts = ts;
    }

    public short getVersion() {
        return version;
    }

    public void setVersion(short version) {
        this.version = version;
    }

    public short getStart() {
        return start;
    }

    public void setStart(short start) {
        this.start = start;
    }

    public short[] getRes() {
        return res;
    }

    public void setRes(short[] res) {
        this.res = res;
    }

    public float getPeriod() {
        return period;
    }

    public void setPeriod(float period) {
        this.period = period;
    }

    public float getX() {
        return x;
    }

    public void setX(float x) {
        this.x = x;
    }

    public float getY() {
        return y;
    }

    public void setY(float y) {
        this.y = y;
    }

    public float getZ() {
        return z;
    }

    public void setZ(float z) {
        this.z = z;
    }
}
