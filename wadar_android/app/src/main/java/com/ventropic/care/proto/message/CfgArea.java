package com.ventropic.care.proto.message;


import lombok.NoArgsConstructor;

 @NoArgsConstructor
public class CfgArea implements CareMessage{
    private float x;
    private float y;
    private float z;

    public float getX() {
        return x;
    }

    public void setX(float x) {
        this.x = x;
    }

    public float getY() {
        return y;
    }

    public void setY(float y) {
        this.y = y;
    }

    public float getZ() {
        return z;
    }

    public void setZ(float z) {
        this.z = z;
    }
}
