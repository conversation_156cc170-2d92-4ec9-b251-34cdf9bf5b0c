package com.ventropic.care.proto.message;


//import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.NoArgsConstructor;
import lombok.ToString;

@NoArgsConstructor @ToString
public class PeopleEvent implements CareMessage{

    private long ts;
    private short amount;
    private Short version;

//    @JsonInclude(JsonInclude.Include.NON_NULL)
    private TargetLocation[] targets;

    public long getTs() {
        return ts;
    }

    public void setTs(long ts) {
        this.ts = ts;
    }

    public short getAmount() {
        return amount;
    }

    public void setAmount(short amount) {
        this.amount = amount;
    }

    public Short getVersion() {
        return version;
    }

    public void setVersion(Short version) {
        this.version = version;
    }

    public TargetLocation[] getTargets() {
        return targets;
    }

    public void setTargets(TargetLocation[] targets) {
        this.targets = targets;
    }
}
