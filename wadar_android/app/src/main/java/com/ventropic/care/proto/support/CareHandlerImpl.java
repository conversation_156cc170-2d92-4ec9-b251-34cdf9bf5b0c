package com.ventropic.care.proto.support;

import com.ventropic.care.proto.Proto;
import com.ventropic.rfc.proto.support.CareHandler;

import java.io.IOException;

/**
 * <AUTHOR>
 */
public class CareHandlerImpl implements CareHandler {
    @Override
    public byte[] cloudPointsHandle(byte[] appPayload) throws IOException {
        if (appPayload[0] == Proto.Cmd.DEBUG_CLOUD_POINTS){
            return appPayload;
        }
        return null;
    }
}
