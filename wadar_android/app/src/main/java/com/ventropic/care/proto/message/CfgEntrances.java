package com.ventropic.care.proto.message;

import lombok.NoArgsConstructor;

@NoArgsConstructor
public class CfgEntrances implements CareMessage {
    public Entrance[] getEntrances() {
        return entrances;
    }

    public void setEntrances(Entrance[] entrances) {
        this.entrances = entrances;
    }
    private Entrance[] entrances;

    @NoArgsConstructor
    public static class Entrance {
        public short getId() {
            return id;
        }

        public void setId(short id) {
            this.id = id;
        }

        public short getCls() {
            return cls;
        }

        public void setCls(short cls) {
            this.cls = cls;
        }

        public short getDirection() {
            return direction;
        }

        public void setDirection(short direction) {
            this.direction = direction;
        }

        public float getLength() {
            return length;
        }

        public void setLength(float length) {
            this.length = length;
        }

        public float getWidth() {
            return width;
        }

        public void setWidth(float width) {
            this.width = width;
        }

        public float getHight() {
            return hight;
        }

        public void setHight(float hight) {
            this.hight = hight;
        }
        private short id;
        private short cls;
        private short direction;
        private float length;
        private float width;
        private float hight;

        public Entrance set(int id, int cls, int direction, float length, float width, float hight) {
            this.id = (short)id;
            this.cls = (short)cls;
            this.direction = (short)direction;
            this.length = length;
            this.width = width;
            this.hight = hight;
            return this;
        }
    }
}
