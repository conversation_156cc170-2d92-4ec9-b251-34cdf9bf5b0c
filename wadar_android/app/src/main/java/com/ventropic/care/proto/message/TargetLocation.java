package com.ventropic.care.proto.message;

import lombok.Data;
import lombok.NoArgsConstructor;

 @NoArgsConstructor
public class TargetLocation {
    private int tid;
    private int cls;
    private int posture;
    private float x;
    private float y;
    private float z;
    private float length;
    private float width;
    private float thick;
    private float velocity;
    private float acceleration;

     public int getTid() {
         return tid;
     }

     public void setTid(int tid) {
         this.tid = tid;
     }

     public int getCls() {
         return cls;
     }

     public void setCls(int cls) {
         this.cls = cls;
     }

     public int getPosture() {
         return posture;
     }

     public void setPosture(int posture) {
         this.posture = posture;
     }

     public float getX() {
         return x;
     }

     public void setX(float x) {
         this.x = x;
     }

     public float getY() {
         return y;
     }

     public void setY(float y) {
         this.y = y;
     }

     public float getZ() {
         return z;
     }

     public void setZ(float z) {
         this.z = z;
     }

     public float getLength() {
         return length;
     }

     public void setLength(float length) {
         this.length = length;
     }

     public float getWidth() {
         return width;
     }

     public void setWidth(float width) {
         this.width = width;
     }

     public float getThick() {
         return thick;
     }

     public void setThick(float thick) {
         this.thick = thick;
     }

     public float getVelocity() {
         return velocity;
     }

     public void setVelocity(float velocity) {
         this.velocity = velocity;
     }

     public float getAcceleration() {
         return acceleration;
     }

     public void setAcceleration(float acceleration) {
         this.acceleration = acceleration;
     }
 }
