package com.ventropic.care.proto;


public interface Proto {
    public static interface Cmd {
        public final int CARE_HANDSHAKE = 0x00;

        public final int CARE_CFG_REPORTING_INTERVAL = 0x01;
        public final int CARE_CFG_AREA = 0x02;
        public final int CARE_CFG_DEV_POSITION = 0x03;
        public final int CARE_CFG_ENTRANCES = 0x04;
        public final int CARE_CFG_REGIONS = 0x05;
        public final int CARE_CFG_SWITCH_THRESHOLD = 0x06;
        public final int CARE_CFG_VS_RANGE = 0x07;
        public final int CARE_CFG_TRACKER = 0x09;
        public final int CARE_SWITCH_MODE = 0x0b;
        public final int CARE_CFG_ROOM_TYPE = 0x0c;

        public final int CARE_REPORTING_PEOPLE_LOCATION = 0x10;
//        public final int CARE_REPORTING_PEOPLE_STATE = 0x11;// 废弃
        public final int CARE_REPORTING_PEOPLE_BREATHE_HEART = 0x12;
//        public final int CARE_REPORTING_PEOPLE_HEART = 0x13;// 废弃
        public final int CARE_REPORTING_ROOM_ENV = 0x14;
        public final int CARE_REPORTING_PEOPLE_LOCATION2 = 0x15;
        public final int CARE_REPORTING_PEOPLE_BREATHE_HEART2 = 0x17;
        public final int CARE_HOTPLACE = 0x18;
        public final int CARE_APP_STATS_REPORTER = 0x19;
        public final int CARE_APP_APNEA_REPORTER = 0x1a;

//        public final int CARE_PEOPLE_RETENTION = 0x20;
        public final int CARE_PEOPLE_GESTURE = 0x21;
        public final int CARE_PEOPLE_FALLDOWN = 0x22;
        public final int CARE_PEOPLE_STAYING_TOO_LONG = 0x23;
        public final int CARE_PEOPLE_IO_REGION = 0x24;
        public final int CARE_PEOPLE_COUNT = 0x25;

        public final int DEBUG_CLOUD_POINTS_SWITCH = 0x30;
        public final int DEBUG_CLOUD_POINTS = 0x31;
        public final int DEBUG_SWITCH_SCENE = 0x33;

        public final int CARE_JSON = 0x50;
        public final int CARE_HEARTBEAT = 0x51;
    }

    interface CmdJson {
        String SET_ROOM             = "setRoom";            // 设置房间信息
        String GET_ROOM             = "getRoom";            // 获取房间信息
        String CLEAN_ROOM           = "cleanRoom";          // 清除房间信息
        String GET_CARE_STAT        = "getCareStat";        // 设备运行状态
        String SET_CARE_INVL        = "setCareInvl";        // 设置care interval
        String GET_CARE_INVL        = "getCareInvl";        // 获取care interval
        String GET_CARE_INVL_STORE  = "getCareInvlStore";   // 获取存储配置
        String GET_DEV_INFO         = "getDevInfo";         // 获取存储配置
        String SET_DEV_INFO         = "setDevInfo";         // 设置存储配置
        String OTA                  = "ota";                // ota
        String OTA_WHOLE            = "otaWhole";           // 一次OTA
        String POINTS_OPEN_STATE    = "pointsNote";         // 点云开启状态
        String INQUIRY_DEV_INFO    = "inquiryDevInfo";         // 点云开启状态
    }

}
