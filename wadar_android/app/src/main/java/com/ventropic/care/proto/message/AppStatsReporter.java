package com.ventropic.care.proto.message;

import java.util.List;



/** 
 * 行走距离和体动指数
 * <AUTHOR> 	  
*/

public class AppStatsReporter implements CareMessage {

    /**
     * 毫秒时间戳
     */
    private long ts;
    /**
     * 协议版本， 当前为1
     */
    private short version;
    private List<AppStatsItem> data;

    public long getTs() {
        return ts;
    }

    public void setTs(long ts) {
        this.ts = ts;
    }

    public short getVersion() {
        return version;
    }

    public void setVersion(short version) {
        this.version = version;
    }

    public List<AppStatsItem> getData() {
        return data;
    }

    public void setData(List<AppStatsItem> data) {
        this.data = data;
    }
}