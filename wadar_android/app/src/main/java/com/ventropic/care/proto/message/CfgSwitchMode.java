package com.ventropic.care.proto.message;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@NoArgsConstructor
public class CfgSwitchMode implements CareMessage{
    public int getVersion() {
        return version;
    }

    public void setVersion(int version) {
        this.version = version;
    }

    public int getMode() {
        return mode;
    }

    public void setMode(int mode) {
        this.mode = mode;
    }

    public Boolean getAutoSendCfg() {
        return autoSendCfg;
    }

    public void setAutoSendCfg(Boolean autoSendCfg) {
        this.autoSendCfg = autoSendCfg;
    }

    public boolean[] getRes() {
        return res;
    }

    public void setRes(boolean[] res) {
        this.res = res;
    }

    public int[] getRes2() {
        return res2;
    }

    public void setRes2(int[] res2) {
        this.res2 = res2;
    }
    private int version;
    private int mode;
    private Boolean autoSendCfg;
    private boolean[] res;
    private int[] res2;
}
