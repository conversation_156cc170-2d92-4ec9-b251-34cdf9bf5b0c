package com.ventropic.care.proto.message;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@NoArgsConstructor
@EqualsAndHashCode(callSuper=false)
public class Ota extends JsonMessage {
    
    public SetData getData() {
        return data;
    }
    
    public void setData(SetData data) {
        this.data = data;
    }

    /**
     * 发送消息体
     */
    private SetData data;

    public static class SetData {
        private byte[] espMd5;
        private String url;
        
        public byte[] getEspMd5() {
            return espMd5;
        }
        
        public void setEspMd5(byte[] espMd5) {
            this.espMd5 = espMd5;
        }
        
        public String getUrl() {
            return url;
        }
        
        public void setUrl(String url) {
            this.url = url;
        }
    }
}
