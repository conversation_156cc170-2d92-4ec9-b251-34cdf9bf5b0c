package com.ventropic.care.proto.message;


import lombok.NoArgsConstructor;

/**
 * @Class: CloudPointsSwiith
 * @Package: com.ventropic.care.proto.message
 * @Author: Mr_fan
 * @Date: 2021/5/28 14:14
 * @Description:
 */

@NoArgsConstructor
public class CloudPoints implements CareMessage {
    private short level;
    private int amount;
    private int pointCnt;
    private Frame[] frames;

    public short getLevel() {
        return level;
    }

    public void setLevel(short level) {
        this.level = level;
    }

    public int getAmount() {
        return amount;
    }

    public void setAmount(int amount) {
        this.amount = amount;
    }

    public int getPointCnt() {
        return pointCnt;
    }

    public void setPointCnt(int pointCnt) {
        this.pointCnt = pointCnt;
    }

    public Frame[] getFrames() {
        return frames;
    }

    public void setFrames(Frame[] frames) {
        this.frames = frames;
    }

     @NoArgsConstructor
    public static class Frame {
        private float x;
        private float y;
        private float z;
        private float velocity;
        private float snr;

        public Frame set(float x, float y, float z, float velocity, float snr) {
            this.x = x;
            this.y = y;
            this.z = z;
            this.velocity = velocity;
            this.snr = snr;
            return this;
        }

        public float getX() {
            return x;
        }

        public void setX(float x) {
            this.x = x;
        }

        public float getY() {
            return y;
        }

        public void setY(float y) {
            this.y = y;
        }

        public float getZ() {
            return z;
        }

        public void setZ(float z) {
            this.z = z;
        }

        public float getVelocity() {
            return velocity;
        }

        public void setVelocity(float velocity) {
            this.velocity = velocity;
        }

        public float getSnr() {
            return snr;
        }

        public void setSnr(float snr) {
            this.snr = snr;
        }
    }
}