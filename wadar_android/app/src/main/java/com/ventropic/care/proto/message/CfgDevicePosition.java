package com.ventropic.care.proto.message;


import lombok.NoArgsConstructor;

 @NoArgsConstructor
public class CfgDevicePosition implements CareMessage{
    private float x;
    private float y;
    private float z;
    private int atilt;
    private int etilt;
    private int atiltfov;
    private int etiltfov;
    private short mount;

    public float getX() {
        return x;
    }

    public void setX(float x) {
        this.x = x;
    }

    public float getY() {
        return y;
    }

    public void setY(float y) {
        this.y = y;
    }

    public float getZ() {
        return z;
    }

    public void setZ(float z) {
        this.z = z;
    }

    public int getAtilt() {
        return atilt;
    }

    public void setAtilt(int atilt) {
        this.atilt = atilt;
    }

    public int getEtilt() {
        return etilt;
    }

    public void setEtilt(int etilt) {
        this.etilt = etilt;
    }

    public int getAtiltfov() {
        return atiltfov;
    }

    public void setAtiltfov(int atiltfov) {
        this.atiltfov = atiltfov;
    }

    public int getEtiltfov() {
        return etiltfov;
    }

    public void setEtiltfov(int etiltfov) {
        this.etiltfov = etiltfov;
    }

    public short getMount() {
        return mount;
    }

    public void setMount(short mount) {
        this.mount = mount;
    }
}
