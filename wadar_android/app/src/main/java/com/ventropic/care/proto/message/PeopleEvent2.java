package com.ventropic.care.proto.message;


//import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 */

@NoArgsConstructor
@ToString
public class PeopleEvent2 implements CareMessage{

    private long ts;
    private short version;
    private short num;

//    @JsonInclude(JsonInclude.Include.NON_NULL)
    private TargetLocation2[] targets;

}
