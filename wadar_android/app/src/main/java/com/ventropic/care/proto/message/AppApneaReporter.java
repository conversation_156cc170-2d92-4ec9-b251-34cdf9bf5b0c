package com.ventropic.care.proto.message;


import lombok.NoArgsConstructor;

/** 
 * typedef struct {
    uint64_t tms;  // 毫秒时间戳
    uint8_t version; // this is 1
    uint8_t res[3];
        uint32_t count; // 窒息计数
    };
 * 	 
 * <AUTHOR> 	  
*/
 @NoArgsConstructor
public class AppApneaReporter implements CareMessage{
    private long ts;
    private short version;
    private short[] res;
    private int count;

    public long getTs() {
        return ts;
    }

    public void setTs(long ts) {
        this.ts = ts;
    }

    public short getVersion() {
        return version;
    }

    public void setVersion(short version) {
        this.version = version;
    }

    public short[] getRes() {
        return res;
    }

    public void setRes(short[] res) {
        this.res = res;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }
}
