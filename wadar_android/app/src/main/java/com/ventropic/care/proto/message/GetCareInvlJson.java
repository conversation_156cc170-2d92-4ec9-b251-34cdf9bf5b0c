package com.ventropic.care.proto.message;

import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper=false)
public class GetCareInvlJson extends JsonMessage {
    public String getMsgId() {
        return super.getMsgId();
    }

    public void setMsgId(String msgId) {
        super.setMsgId(msgId);
    }

    public Integer getSeqId() {
        return super.getSeqId();
    }

    public void setSeqId(int seqId) {
        super.setSeqId(seqId);
    }

    /**
     * 覆写该方法是为了能过够打印出父类的属性值
     * @return
     */
    @Override
    public String toString() {
        return "GetCareInvlJson{" +
                "msgId='" + super.getMsgId() + '\'' +
                ", seqId=" + super.getSeqId() +
                '}';
    }
}
