package com.ventropic.care.proto.message;


import lombok.NoArgsConstructor;

 @NoArgsConstructor
public class CfgSwitchThreshold implements CareMessage{
    private int moveThreshold;
    private int staticTimeThreshold;

    public int getMoveThreshold() {
        return moveThreshold;
    }

    public void setMoveThreshold(int moveThreshold) {
        this.moveThreshold = moveThreshold;
    }

    public int getStaticTimeThreshold() {
        return staticTimeThreshold;
    }

    public void setStaticTimeThreshold(int staticTimeThreshold) {
        this.staticTimeThreshold = staticTimeThreshold;
    }
}
