package com.ventropic.care.proto.message;


import lombok.NoArgsConstructor;

/**
 * @Class: CfgVsRange
 * @Package: com.ventropic.care.proto.message
 * @Author: Mr_fan
 * @Date: 2021/5/28 10:21
 * @Description:
 */

@NoArgsConstructor
public class CfgTracker implements CareMessage {
    private float stayingTooLongThreshold;

    public float getStayingTooLongThreshold() {
        return stayingTooLongThreshold;
    }

    public void setStayingTooLongThreshold(float stayingTooLongThreshold) {
        this.stayingTooLongThreshold = stayingTooLongThreshold;
    }
}
