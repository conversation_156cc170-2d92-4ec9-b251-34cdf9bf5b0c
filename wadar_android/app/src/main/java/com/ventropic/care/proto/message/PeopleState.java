package com.ventropic.care.proto.message;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Class: PeopleState
 * @Package: com.ventropic.care.proto.message
 * @Author: Mr_fan
 * @Date: 2021/5/28 12:48
 * @Description:
 */

@NoArgsConstructor
public class PeopleState implements CareMessage {
    private long ts;
    private int tid;
    private int state;

    public long getTs() {
        return ts;
    }

    public void setTs(long ts) {
        this.ts = ts;
    }

    public int getTid() {
        return tid;
    }

    public void setTid(int tid) {
        this.tid = tid;
    }

    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }
}
