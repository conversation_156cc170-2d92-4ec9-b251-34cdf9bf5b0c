package com.ventropic.care.proto.message;





/** 
 * 心跳
 * <AUTHOR> 	  
*/

public class Heartbeat implements CareMessage{

    /**
     * 毫秒时间戳
     */
    private long ts;
    /**
     * 协议版本， 当前为1
     */
    private short version;
    private int wifiRssi;
    private int wifiRssiLevel;
    private short peopleNum;
    private int iotRssi;
	private int iotRssiLevel;
//    @JsonIgnore
    private short[] res;
    private short cpuWorkLoad;
    private short cpu1Load;
    private short cpu2Load;
    private short memWorkLoad;
    private short iRamLoad;
    private short dRamLoad;
    private short spiRamLoad;
    private short himemLoad;
    
    //12
//    @JsonIgnore
    private short[] res2;
    
    //区域ID，1为默认区域，0表示老协议，代表占用状态不可用
    private short regionId;
    //区域占用状态，0未占用，1占用
    private short regionOccur;
    //床id
    private short bed1Id;
    //床被占用状态，0未占用，1占用
    private short bed1Occur;
    private short bed2Id;
    private short bed2Occur;
    //86
//    @JsonIgnore
    private short[] res3;
    
    
//    public int getWifiRssiLevel() {
//        return NetUtils.calculateWifiLevel(wifiRssi);
//    }
    
//    public int getIotRssiLevel() {
//        return NetUtils.calculateIotLevel(iotRssi);
//    }

    public long getTs() {
        return ts;
    }

    public void setTs(long ts) {
        this.ts = ts;
    }

    public short getVersion() {
        return version;
    }

    public void setVersion(short version) {
        this.version = version;
    }

    public int getWifiRssi() {
        return wifiRssi;
    }

    public void setWifiRssi(int wifiRssi) {
        this.wifiRssi = wifiRssi;
    }

    public int getWifiRssiLevel() {
        return wifiRssiLevel;
    }

    public void setWifiRssiLevel(int wifiRssiLevel) {
        this.wifiRssiLevel = wifiRssiLevel;
    }

    public short getPeopleNum() {
        return peopleNum;
    }

    public void setPeopleNum(short peopleNum) {
        this.peopleNum = peopleNum;
    }

    public int getIotRssi() {
        return iotRssi;
    }

    public void setIotRssi(int iotRssi) {
        this.iotRssi = iotRssi;
    }

    public int getIotRssiLevel() {
        return iotRssiLevel;
    }

    public void setIotRssiLevel(int iotRssiLevel) {
        this.iotRssiLevel = iotRssiLevel;
    }

    public short[] getRes() {
        return res;
    }

    public void setRes(short[] res) {
        this.res = res;
    }

    public short getCpuWorkLoad() {
        return cpuWorkLoad;
    }

    public void setCpuWorkLoad(short cpuWorkLoad) {
        this.cpuWorkLoad = cpuWorkLoad;
    }

    public short getCpu1Load() {
        return cpu1Load;
    }

    public void setCpu1Load(short cpu1Load) {
        this.cpu1Load = cpu1Load;
    }

    public short getCpu2Load() {
        return cpu2Load;
    }

    public void setCpu2Load(short cpu2Load) {
        this.cpu2Load = cpu2Load;
    }

    public short getMemWorkLoad() {
        return memWorkLoad;
    }

    public void setMemWorkLoad(short memWorkLoad) {
        this.memWorkLoad = memWorkLoad;
    }

    public short getIRamLoad() {
        return iRamLoad;
    }

    public void setIRamLoad(short iRamLoad) {
        this.iRamLoad = iRamLoad;
    }

    public short getDRamLoad() {
        return dRamLoad;
    }

    public void setDRamLoad(short dRamLoad) {
        this.dRamLoad = dRamLoad;
    }

    public short getSpiRamLoad() {
        return spiRamLoad;
    }

    public void setSpiRamLoad(short spiRamLoad) {
        this.spiRamLoad = spiRamLoad;
    }

    public short getHimemLoad() {
        return himemLoad;
    }

    public void setHimemLoad(short himemLoad) {
        this.himemLoad = himemLoad;
    }

    public short[] getRes2() {
        return res2;
    }

    public void setRes2(short[] res2) {
        this.res2 = res2;
    }

    public short getRegionId() {
        return regionId;
    }

    public void setRegionId(short regionId) {
        this.regionId = regionId;
    }

    public short getRegionOccur() {
        return regionOccur;
    }

    public void setRegionOccur(short regionOccur) {
        this.regionOccur = regionOccur;
    }

    public short getBed1Id() {
        return bed1Id;
    }

    public void setBed1Id(short bed1Id) {
        this.bed1Id = bed1Id;
    }

    public short getBed1Occur() {
        return bed1Occur;
    }

    public void setBed1Occur(short bed1Occur) {
        this.bed1Occur = bed1Occur;
    }

    public short getBed2Id() {
        return bed2Id;
    }

    public void setBed2Id(short bed2Id) {
        this.bed2Id = bed2Id;
    }

    public short getBed2Occur() {
        return bed2Occur;
    }

    public void setBed2Occur(short bed2Occur) {
        this.bed2Occur = bed2Occur;
    }

    public short[] getRes3() {
        return res3;
    }

    public void setRes3(short[] res3) {
        this.res3 = res3;
    }
}
