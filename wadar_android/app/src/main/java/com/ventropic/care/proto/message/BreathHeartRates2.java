package com.ventropic.care.proto.message;


import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
 @NoArgsConstructor
public class BreathHeartRates2 implements CareMessage {
    private long ts;
    private int version;
    private int num;
    private BreathHeartRate2[] breathHeartRates;

    public long getTs() {
        return ts;
    }

    public void setTs(long ts) {
        this.ts = ts;
    }

    public int getVersion() {
        return version;
    }

    public void setVersion(int version) {
        this.version = version;
    }

    public int getNum() {
        return num;
    }

    public void setNum(int num) {
        this.num = num;
    }

    public BreathHeartRate2[] getBreathHeartRates() {
        return breathHeartRates;
    }

    public void setBreathHeartRates(BreathHeartRate2[] breathHeartRates) {
        this.breathHeartRates = breathHeartRates;
    }

     @NoArgsConstructor
    public static class BreathHeartRate2 {
        private int tid;
        private int res0;
        private short confidence;
        private int breathBPM;
        private int heartBPM;
        private float breathMM;
        private float heartMM;
        private float x;
        private float y;
        private float z;
        private int res1;

        public BreathHeartRate2 set(int tid, int res0, short confidence, int breathBPM, int heartBPM, float breathMM, float heartMM, float x,float y, float z, int res1) {
            this.tid = tid;
            this.res0 = res0;
            this.confidence = confidence;
            this.breathBPM = breathBPM;
            this.heartBPM = heartBPM;
            this.breathMM = breathMM;
            this.heartMM = heartMM;
            this.x = x;
            this.y = y;
            this.z = z;
            this.res1 = res1;
            return this;
        }

        public int getTid() {
            return tid;
        }

        public void setTid(int tid) {
            this.tid = tid;
        }

        public int getRes0() {
            return res0;
        }

        public void setRes0(int res0) {
            this.res0 = res0;
        }

        public short getConfidence() {
            return confidence;
        }

        public void setConfidence(short confidence) {
            this.confidence = confidence;
        }

        public int getBreathBPM() {
            return breathBPM;
        }

        public void setBreathBPM(int breathBPM) {
            this.breathBPM = breathBPM;
        }

        public int getHeartBPM() {
            return heartBPM;
        }

        public void setHeartBPM(int heartBPM) {
            this.heartBPM = heartBPM;
        }

        public float getBreathMM() {
            return breathMM;
        }

        public void setBreathMM(float breathMM) {
            this.breathMM = breathMM;
        }

        public float getHeartMM() {
            return heartMM;
        }

        public void setHeartMM(float heartMM) {
            this.heartMM = heartMM;
        }

        public float getX() {
            return x;
        }

        public void setX(float x) {
            this.x = x;
        }

        public float getY() {
            return y;
        }

        public void setY(float y) {
            this.y = y;
        }

        public float getZ() {
            return z;
        }

        public void setZ(float z) {
            this.z = z;
        }

        public int getRes1() {
            return res1;
        }

        public void setRes1(int res1) {
            this.res1 = res1;
        }
    }

    public BreathHeartRate toBreathHeartRate(){
        BreathHeartRates2.BreathHeartRate2 breathHeartRate = this.getBreathHeartRates()[0];
        BreathHeartRate msg = new BreathHeartRate();
        msg.setTs(this.getTs());
        msg.setTid(breathHeartRate.getTid());
        msg.setBreath(breathHeartRate.getBreathBPM());
        msg.setHeart(breathHeartRate.getHeartBPM());
        msg.setBconf(breathHeartRate.getConfidence());
        msg.setHconf(breathHeartRate.getConfidence());
        msg.setBreathMM(breathHeartRate.getBreathMM());
        msg.setHeartMM(breathHeartRate.getHeartMM());
        return msg;
    }
}
