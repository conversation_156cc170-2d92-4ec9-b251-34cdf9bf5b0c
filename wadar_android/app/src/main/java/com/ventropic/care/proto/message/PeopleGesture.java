package com.ventropic.care.proto.message;

//import com.fasterxml.jackson.annotation.JsonIgnore;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Class: PeopleGesture
 * @Package: com.ventropic.care.proto.message
 * @Author: Mr_fan
 * @Date: 2021/5/28 14:12
 * @Description:
 */

@NoArgsConstructor
public class PeopleGesture implements CareMessage {
    private long ts;
    /**
     * 协议版本， 当前为1
     */
    private short version;
//    @JsonIgnore
    private short[] res;
    /**
     *  0 NoGesture          无手势
        1 Left to Right      从左到右挥手
        2 Right to Left      从右到左挥手
        3 Up to Down         从上到下挥手
        4 Down to Up         从下到上挥手
        5 CW Twirl           正旋转手指
        6 CCW Twirl          反旋转手指
        7 Push               推手掌
        8 Pull               拉手掌
        9 shine              从收拢到快速张开全部手指
     */
    private int gesture;
    private float confidence;

    public long getTs() {
        return ts;
    }

    public void setTs(long ts) {
        this.ts = ts;
    }

    public short getVersion() {
        return version;
    }

    public void setVersion(short version) {
        this.version = version;
    }

    public short[] getRes() {
        return res;
    }

    public void setRes(short[] res) {
        this.res = res;
    }

    public int getGesture() {
        return gesture;
    }

    public void setGesture(int gesture) {
        this.gesture = gesture;
    }

    public float getConfidence() {
        return confidence;
    }

    public void setConfidence(float confidence) {
        this.confidence = confidence;
    }
}
