package com.ventropic.care.proto.message;

import lombok.NoArgsConstructor;

@Deprecated
@NoArgsConstructor
public class HeartRate implements CareMessage{
    private int ts;
    private int tid;
    private short heart;

    /**
     * 获取ts
     * @return ts
     */
    public int getTs() {
        return ts;
    }

    /**
     * 设置ts
     * @param ts ts
     */
    public void setTs(int ts) {
        this.ts = ts;
    }

    /**
     * 获取tid
     * @return tid
     */
    public int getTid() {
        return tid;
    }

    /**
     * 设置tid
     * @param tid tid
     */
    public void setTid(int tid) {
        this.tid = tid;
    }

    /**
     * 获取heart
     * @return heart
     */
    public short getHeart() {
        return heart;
    }

    /**
     * 设置heart
     * @param heart heart
     */
    public void setHeart(short heart) {
        this.heart = heart;
    }
}
