package com.ventropic.care.proto.message;

import lombok.Data;
import lombok.NoArgsConstructor;

 @NoArgsConstructor
public class RoomEnv implements CareMessage{
    long ts;
    float light;
    float temp;

     public long getTs() {
         return ts;
     }

     public void setTs(long ts) {
         this.ts = ts;
     }

     public float getLight() {
         return light;
     }

     public void setLight(float light) {
         this.light = light;
     }

     public float getTemp() {
         return temp;
     }

     public void setTemp(float temp) {
         this.temp = temp;
     }
 }
