package com.ventropic.care.proto.message;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper=false)
public class GetRoomJson extends JsonMessage {

    /**
     * 覆写该方法是为了能过够打印出父类的属性值
     * @return
     */
    @Override
    public String toString() {
        return "GetRoomJson{" +
                "msgId='" + super.getMsgId() + '\'' +
                ", seqId=" + super.getSeqId() +
                '}';
    }
}
