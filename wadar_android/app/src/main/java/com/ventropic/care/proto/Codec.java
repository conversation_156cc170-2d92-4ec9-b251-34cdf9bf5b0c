package com.ventropic.care.proto;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

import com.google.gson.Gson;
import com.ventropic.care.proto.message.AppApneaReporter;
import com.ventropic.care.proto.message.AppStatsItem;
import com.ventropic.care.proto.message.AppStatsReporter;
import com.ventropic.care.proto.message.BreathHeartRate;
import com.ventropic.care.proto.message.BreathHeartRates2;
import com.ventropic.care.proto.message.CareMessage;
import com.ventropic.care.proto.message.CfgArea;
import com.ventropic.care.proto.message.CfgAreaAck;
import com.ventropic.care.proto.message.CfgDevicePosition;
import com.ventropic.care.proto.message.CfgDevicePositionAck;
import com.ventropic.care.proto.message.CfgEntrances;
import com.ventropic.care.proto.message.CfgEntrancesAck;
import com.ventropic.care.proto.message.CfgRegions;
import com.ventropic.care.proto.message.CfgRegionsAck;
import com.ventropic.care.proto.message.CfgReportingInterval;
import com.ventropic.care.proto.message.CfgReportingIntervalAck;
import com.ventropic.care.proto.message.CfgRoomType;
import com.ventropic.care.proto.message.CfgRoomTypeAck;
import com.ventropic.care.proto.message.CfgSwitchMode;
import com.ventropic.care.proto.message.CfgSwitchModeAck;
import com.ventropic.care.proto.message.CfgSwitchThreshold;
import com.ventropic.care.proto.message.CfgSwitchThresholdAck;
import com.ventropic.care.proto.message.CfgTracker;
import com.ventropic.care.proto.message.CfgTrackerAck;
import com.ventropic.care.proto.message.CfgVsRange;
import com.ventropic.care.proto.message.CfgVsRangeAck;
import com.ventropic.care.proto.message.CloudPoints;
import com.ventropic.care.proto.message.CloudPointsSwitch;
import com.ventropic.care.proto.message.CloudPointsSwitchAck;
import com.ventropic.care.proto.message.DeviceData;
import com.ventropic.care.proto.message.FallDown;
import com.ventropic.care.proto.message.HandShake;
import com.ventropic.care.proto.message.Heartbeat;
import com.ventropic.care.proto.message.HotPlace;
import com.ventropic.care.proto.message.JsonMessage;
import com.ventropic.care.proto.message.OptInOut;
import com.ventropic.care.proto.message.PeopleCount;
import com.ventropic.care.proto.message.PeopleEvent;
import com.ventropic.care.proto.message.PeopleEvent2;
import com.ventropic.care.proto.message.PeopleGesture;
import com.ventropic.care.proto.message.PeopleState;
import com.ventropic.care.proto.message.RoomEnv;
import com.ventropic.care.proto.message.SceneSwitch;
import com.ventropic.care.proto.message.SceneSwitchAck;
import com.ventropic.care.proto.message.StayTooLong;
import com.ventropic.care.proto.message.TargetLocation;
import com.ventropic.rfc.proto.message.json.CleanRoomJsonAck;
import com.ventropic.rfc.proto.message.json.GetCareInvlJsonAck;
import com.ventropic.rfc.proto.message.json.GetCareInvlStoreJsonAck;
import com.ventropic.rfc.proto.message.json.GetCareStatJsonAck;
import com.ventropic.rfc.proto.message.json.GetDevInfoJsonAck;
import com.ventropic.rfc.proto.message.json.GetRoomJsonAck;
import com.ventropic.rfc.proto.message.json.InquiryDevInfo;
import com.ventropic.rfc.proto.message.json.JsonMessageAck;
import com.ventropic.rfc.proto.message.json.OtaAck;
import com.ventropic.rfc.proto.message.json.PointsOpenState;
import com.ventropic.rfc.proto.message.json.SetCareInvlJsonAck;
import com.ventropic.rfc.proto.message.json.SetDevInfoJsonAck;
import com.ventropic.rfc.proto.message.json.SetRoomJsonAck;
import com.ventropic.rfc.proto.support.CodecUtil;

import wvr.wadar.app.BlufiLog;

public class Codec{

    private final static BlufiLog log = new BlufiLog(Codec.class);
    final private static CodecUtil helper = new CodecUtil();
    private static final List<Class<? extends CareMessage>> cmdClasses = new ArrayList<>();
    private static final List<Integer> cmdCodes = new ArrayList<>();

    private static void register(Class<? extends CareMessage> cmdType, int code) {
        cmdClasses.add(cmdType);
        cmdCodes.add(code);
    }

    static {
        // 0x00-0x09
        register(HandShake.class, Proto.Cmd.CARE_HANDSHAKE);
        register(CfgReportingInterval.class, Proto.Cmd.CARE_CFG_REPORTING_INTERVAL);
        register(CfgArea.class, Proto.Cmd.CARE_CFG_AREA);
        register(CfgDevicePosition.class, Proto.Cmd.CARE_CFG_DEV_POSITION);
        register(CfgEntrances.class, Proto.Cmd.CARE_CFG_ENTRANCES);
        register(CfgRegions.class, Proto.Cmd.CARE_CFG_REGIONS);
        register(CfgSwitchThreshold.class, Proto.Cmd.CARE_CFG_SWITCH_THRESHOLD);
        register(CfgVsRange.class, Proto.Cmd.CARE_CFG_VS_RANGE);
        register(CfgTracker.class, Proto.Cmd.CARE_CFG_TRACKER);
        register(CfgSwitchMode.class, Proto.Cmd.CARE_SWITCH_MODE);
        register(CfgRoomType.class, Proto.Cmd.CARE_CFG_ROOM_TYPE);
        // 0x10-0x19
        register(PeopleEvent.class, Proto.Cmd.CARE_REPORTING_PEOPLE_LOCATION);
//        register(PeopleState.class, Proto.Cmd.CARE_REPORTING_PEOPLE_STATE); //废弃
        register(BreathHeartRate.class, Proto.Cmd.CARE_REPORTING_PEOPLE_BREATHE_HEART);
//        register(HeartRate.class, Proto.Cmd.CARE_REPORTING_PEOPLE_HEART); //废弃
        register(RoomEnv.class, Proto.Cmd.CARE_REPORTING_ROOM_ENV);
        register(PeopleEvent2.class, Proto.Cmd.CARE_REPORTING_PEOPLE_LOCATION2);
        register(BreathHeartRates2.class, Proto.Cmd.CARE_REPORTING_PEOPLE_BREATHE_HEART2);
        // 0x20-0x29
//        register(PeopleRetention.class, Proto.Cmd.CARE_PEOPLE_RETENTION);
        register(PeopleGesture.class, Proto.Cmd.CARE_PEOPLE_GESTURE);
        register(FallDown.class, Proto.Cmd.CARE_PEOPLE_FALLDOWN);
        register(StayTooLong.class, Proto.Cmd.CARE_PEOPLE_STAYING_TOO_LONG);
        register(OptInOut.class, Proto.Cmd.CARE_PEOPLE_IO_REGION);
        register(PeopleCount.class, Proto.Cmd.CARE_PEOPLE_COUNT);
        // 0x30-0x39
        register(CloudPointsSwitch.class, Proto.Cmd.DEBUG_CLOUD_POINTS_SWITCH);
        register(CloudPoints.class, Proto.Cmd.DEBUG_CLOUD_POINTS);
        register(SceneSwitch.class, Proto.Cmd.DEBUG_SWITCH_SCENE);

        register(JsonMessage.class, Proto.Cmd.CARE_JSON);
        register(Heartbeat.class, Proto.Cmd.CARE_HEARTBEAT);
        register(HotPlace.class, Proto.Cmd.CARE_HOTPLACE);
        register(AppStatsReporter.class, Proto.Cmd.CARE_APP_STATS_REPORTER);
        register(AppApneaReporter.class, Proto.Cmd.CARE_APP_APNEA_REPORTER);
    }

    // ================================================encode编码========================================================


    public static void encode(CareMessage msg, OutputStream out) throws Exception{
        ByteArrayOutputStream payload = new ByteArrayOutputStream();

        if (msg instanceof HandShake) encodeHandShake((HandShake) msg, payload);
        else if (msg instanceof CfgReportingInterval) encodeReportingInterval((CfgReportingInterval) msg, payload);
        else if (msg instanceof JsonMessage) encodeJsonMessage((JsonMessage)msg, payload);
        else if (msg instanceof CfgArea) encodeConfigArea((CfgArea)msg, payload);
        else if (msg instanceof CfgDevicePosition) encodeDevicePosition((CfgDevicePosition)msg, payload);
        else if (msg instanceof CfgEntrances) encodeCfgEntrances((CfgEntrances)msg, payload);
        else if (msg instanceof CfgRegions) encodeCfgRegions((CfgRegions)msg, payload);
        else if (msg instanceof CfgSwitchThreshold) encodeCfgSwitch((CfgSwitchThreshold)msg, payload);
        else if (msg instanceof CfgVsRange) encodeCfgVs((CfgVsRange)msg, payload);
        else if (msg instanceof CfgTracker) encodeCfgTracker((CfgTracker)msg, payload);
        else if (msg instanceof CfgSwitchMode) encodeCfgSwitchMode((CfgSwitchMode)msg, payload);
        else if (msg instanceof CfgRoomType) encodeCfgRoomType((CfgRoomType)msg, payload);
        else if (msg instanceof CloudPointsSwitch) encodeCloudPointsSwitch((CloudPointsSwitch)msg, payload);
        else if (msg instanceof SceneSwitch) encodeSceneSwitch((SceneSwitch)msg, payload);
        else throw new UnsupportedOperationException("encoding message " + msg);

        // command id
        if (msg instanceof JsonMessage){
            helper.packUInt8(out, Proto.Cmd.CARE_JSON);
        } else {
            helper.packUInt8(out, commandId(msg.getClass()));
        }
        if (payload.size() > 0) out.write(payload.toByteArray());
    }

    private static int commandId(Class<? extends CareMessage> msg){
        return cmdCodes.get(cmdClasses.indexOf(msg));
    }

    /**
     * DEBUG_SWITCH_SCENE 0x33 编码
     * @param msg
     * @param out
     * @throws IOException
     */
    private static void encodeSceneSwitch(SceneSwitch msg, ByteArrayOutputStream out) throws IOException {
        helper.packUInt8(out, msg.getCode());
    }

    /**
     * DEBUG_CLOUD_POINTS_SWITCH 0x30 编码
     * @param msg
     * @param out
     * @throws IOException
     */
    private static void encodeCloudPointsSwitch(CloudPointsSwitch msg, ByteArrayOutputStream out) throws IOException {
        helper.packUInt8(out, msg.getLevel());
    }

    /**
     * CARE_SWITCH_MODE 0x0b 编码
     * @param msg
     * @param out
     * @throws IOException
     */
    private static void encodeCfgSwitchMode(CfgSwitchMode msg, ByteArrayOutputStream out) throws IOException {
        helper.packInt32(out, msg.getVersion());
        helper.packInt32(out, msg.getMode());
        helper.packBoolean(out, msg.getAutoSendCfg());
        boolean[] res = msg.getRes();
        if (res.length != 3){
            throw new IOException("数组长度异常，res："+res.length);
        }
        for (int i=0; i<3; i++){
            helper.packBoolean(out, res[i]);
        }
        int[] res2 = msg.getRes2();
        if (res2.length != 17){
            throw new IOException("数组长度异常，res2："+res2.length);
        }
        for (int i=0; i<17; i++){
            helper.packInt32(out, res2[i]);
        }
    }
    /**
     * CARE_CFG_ROOM_TYPE 0x0c 编码
     * @param msg
     * @param out
     * @throws IOException
     */
    private static void encodeCfgRoomType(CfgRoomType msg, ByteArrayOutputStream out) throws IOException {
        helper.packUInt16(out, msg.getRoomType());
    }

    /**
     * CARE_CFG_TRACKER 0x09 编码
     * @param msg
     * @param out
     * @throws IOException
     */
    private static void encodeCfgTracker(CfgTracker msg, ByteArrayOutputStream out) throws IOException {
        helper.packFloat(out, msg.getStayingTooLongThreshold());
    }

    /**
     * CARE_CFG_VS_RANGE 0x07 编码
     * @param msg
     * @param out
     * @throws IOException
     */
    private static void encodeCfgVs(CfgVsRange msg, ByteArrayOutputStream out) throws IOException {
        helper.packFloat(out, msg.getStart());
        helper.packFloat(out, msg.getEnd());
    }

    /**
     * CARE_CFG_SWITCH_THRESHOLD 0x06 编码
     * @param msg
     * @param out
     * @throws IOException
     */
    private static void encodeCfgSwitch(CfgSwitchThreshold msg, ByteArrayOutputStream out) throws IOException {
        helper.packUInt32(out, msg.getMoveThreshold());
        helper.packUInt32(out, msg.getStaticTimeThreshold());
    }

    /**
     * CARE_CFG_REGIONS 0x05 编码
     * @param msg
     * @param out
     * @throws IOException
     */
    private static void encodeCfgRegions(CfgRegions msg, ByteArrayOutputStream out) throws IOException {
        int num = msg.getRegions() == null ? 0 : msg.getRegions().length;
        helper.packUInt8(out, num);
        for (int i = 0; i < num; i++) {
            CfgRegions.Region region = msg.getRegions()[i];
            helper.packUInt16(out, region.getRid());
            helper.packUInt16(out, region.getCls());
            helper.packFloat(out, region.getPositionX());
            helper.packFloat(out, region.getPositionY());
            helper.packFloat(out, region.getScaleX());
            helper.packFloat(out, region.getScaleY());
            helper.packFloat(out, region.getScaleZ());
            helper.packFloat(out, region.getRotation());
        }
    }

    /**
     * CARE_CFG_GATES 0x04 编码
     * @param msg
     * @param out
     * @throws IOException
     */
    private static void encodeCfgEntrances(CfgEntrances msg, ByteArrayOutputStream out) throws IOException {
        int num = msg.getEntrances() == null ? 0 : msg.getEntrances().length;
        helper.packUInt8(out, num);
        for (int i = 0; i < num; i++) {
            CfgEntrances.Entrance gate = msg.getEntrances()[i];
            helper.packUInt16(out, gate.getId());
            helper.packUInt16(out, gate.getCls());
            helper.packUInt8(out, gate.getDirection());
            helper.packFloat(out, gate.getLength());
            helper.packFloat(out, gate.getWidth());
            helper.packFloat(out, gate.getHight());
        }
    }

    /**
     * CARE_CFG_DEV_POSITION 0x03 编码
     * @param msg
     * @param payload
     * @throws IOException
     */
    private static void encodeDevicePosition(CfgDevicePosition msg, ByteArrayOutputStream payload) throws IOException {
        helper.packFloat(payload, msg.getX());
        helper.packFloat(payload, msg.getY());
        helper.packFloat(payload, msg.getZ());
        helper.packInt16(payload, msg.getAtilt());
        helper.packInt16(payload, msg.getEtilt());
        helper.packUInt16(payload, msg.getAtiltfov());
        helper.packUInt16(payload, msg.getEtiltfov());
        helper.packUInt8(payload, msg.getMount());
    }

    /**
     * CARE_CFG_AREA 0x02 编码
     * @param msg
     * @param out
     * @throws IOException
     */
    private static void encodeConfigArea(CfgArea msg, ByteArrayOutputStream out) throws IOException {
        helper.packFloat(out, msg.getX());
        helper.packFloat(out, msg.getY());
        helper.packFloat(out, msg.getZ());
    }

    /**
     * CARE_CFG_ROOM
     * @param msg
     * @param out
     * @throws IOException
     */
    private static void encodeJsonMessage(JsonMessage msg, ByteArrayOutputStream out) throws IOException {
        String json = new Gson().toJson(msg);
        helper.packString(out, json);
    }

    /**
     * CARE_CFG_REPORTING_INTERVAL 0x01 编码
     * @param msg
     * @param payload
     * @throws Exception
     */
    private static void encodeReportingInterval(CfgReportingInterval msg, ByteArrayOutputStream payload) throws Exception {
        int num = msg.getReporters() == null ? 0 : msg.getReporters().length;
        helper.packUInt8(payload, num);
        for (CfgReportingInterval.Reporter reporter: msg.getReporters()) {
            // 限制0x10-0x13
            if (reporter.getReportingId() < Proto.Cmd.CARE_REPORTING_PEOPLE_LOCATION ||
                    reporter.getReportingId() >= Proto.Cmd.CARE_REPORTING_ROOM_ENV){
                throw new Exception("CfgReportingInterval error:reportingId = " + reporter.getReportingId());
            }
            helper.packUInt8(payload, reporter.getReportingId());
            helper.packUInt32(payload, reporter.getInterval());
        }
    }

    /**
     * CARE_HANDSHAKE 0x00 编码
     * @param message
     * @param payload
     * @throws IOException
     */
    private static void encodeHandShake(HandShake message, OutputStream payload) throws IOException {
        String text = message.getMessage();
        if (text != null && text.length() > 0) payload.write(text.getBytes());
    }

    // ===============================================decode解码=========================================================


    public static CareMessage decode(InputStream input) throws IOException {
        int cmid = input.read();
        int rest = input.available();
        byte[] payload = null;
        if (rest > 0){
            payload = new byte[rest];
            input.read(payload);
        }
        return decodeMessageContent(cmid, payload);
    }

    private static CareMessage decodeMessageContent(int cmdid, byte[] payload) throws IOException {
        if (!cmdCodes.contains(cmdid))  throw new UnsupportedOperationException("Unknown command " + cmdid);
        Class<?> clazz = cmdClasses.get(cmdCodes.indexOf(cmdid));
        if (payload == null) payload = new byte[]{};
        InputStream in = new ByteArrayInputStream(payload);

        // 0x00-0x0f
        if (clazz == HandShake.class) return decodeHandShake(payload);
        else if (clazz == CfgReportingInterval.class) return decodeCfgReportingInterval(in);
        else if (clazz == CfgArea.class) return decodeCfgArea(in);
        else if (clazz == CfgDevicePosition.class) return decodeCfgDevicePosition(in);
        else if (clazz == CfgEntrances.class)  return decodeCfgEntrances(in);
        else if (clazz == CfgRegions.class) return decodeCfgRegions(in);
        else if (clazz == CfgSwitchThreshold.class) return decodeCfgSwitch(in);
        else if (clazz == CfgVsRange.class) return decodeCfgVs(in);
        else if (clazz == CfgTracker.class) return decodeCfgTracker(in);
        else if (clazz == CfgSwitchMode.class) return decodeCfgSwitchMode(in);
        else if (clazz == CfgRoomType.class) return decodeCfgRoomType(in);
        // 0x10-0x1f
        else if (clazz == PeopleEvent.class) return decodePeopleEvent(in);
        else if (clazz == PeopleState.class) return decodePeopleState(in);
        else if (clazz == BreathHeartRate.class) return decodeBreathHeart(in);
        else if (clazz == RoomEnv.class) return decodeRoomEvn(in);
        else if (clazz == PeopleEvent2.class) return decodePeopleEvent2(in);
        else if (clazz == BreathHeartRates2.class) return decodeBreathHeart2(in);
        // 0x20-0x2f
//        else if (clazz == PeopleRetention.class) return decodePeopleRetention(in);
        else if (clazz == PeopleGesture.class) return decodePeopleGesture(in);
        else if (clazz == FallDown.class) return decodeFallDown(in);
        else if (clazz == StayTooLong.class) return decodeStayingTooLong(in);
        else if (clazz == OptInOut.class) return decodeOptInOut(in);
        else if (clazz == PeopleCount.class) return decodePeopleCount(in);
        // 0x30-0x3f
        else if (clazz == CloudPointsSwitch.class) return decodeCloudPointsSwitch(in);
        else if (clazz == CloudPoints.class) return decodeCloudPoints(in);
        else if (clazz == SceneSwitch.class) return decodeSceneSwitch(in);
        // 0x50-0x5f
        else if (clazz == JsonMessage.class) return decodeJsonMessage(in);
        else if (clazz == Heartbeat.class) return decodeHeartbeat(in);
        else if (clazz == HotPlace.class) return decodeHotPlace(in);
        else if (clazz == AppStatsReporter.class) return decodeAppStatsReporter(payload);
        else if (clazz == AppApneaReporter.class) return decodeAppApneaReporter(in);
        else throw new UnsupportedOperationException("Unknown command " + clazz);
    }

    /**
     * CARE_HEARTBEAT 0x51 解码
     * @param in
     * @return
     * @throws IOException
     */
    private static HotPlace decodeHotPlace(InputStream in) throws IOException {
        HotPlace msg = new HotPlace();
        msg.setTs(helper.unpackUInt64(in));
        msg.setVersion(helper.unpackUInt8(in));
        msg.setStart(helper.unpackUInt8(in));
        // uint8_t res[2];
        // short[] res = new short[2];
        // for (int i=0; i<2; i++){
        //     res[i] = helper.unpackUInt8(in);
        // }
        msg.setRes(helper.unpackUInt8Array(in, 2));
        msg.setPeriod(helper.unpackFloat(in));
        msg.setX(helper.unpackFloat(in));
        msg.setY(helper.unpackFloat(in));
        msg.setZ(helper.unpackFloat(in));
        return msg;
    }
    
    /**
     * 行走距离和体动指数
     * <AUTHOR>
     * @param payload
     * @return
     * @throws IOException
     */
    public static AppStatsReporter decodeAppStatsReporter(byte[] payload) throws IOException {
        InputStream in = new ByteArrayInputStream(payload);
        // 去掉前9个字节
        int head = 9;
        int dataLen = payload.length - head;
        AppStatsReporter msg = new AppStatsReporter();
        msg.setTs(helper.unpackUInt64(in));
        msg.setVersion(helper.unpackUInt8(in));
        int fillPos = 6; // 2 (类型和 app) + 4 (bit_table)
        // 后续为两条数据
        if (dataLen >= fillPos) {
            List<AppStatsItem> list = new ArrayList<>();
            AppStatsItem item = null;
            while (in.available() > 0) {
                item = new AppStatsItem();
                item.setType(helper.unpackUInt8(in));
                item.setApp(helper.unpackUInt8(in));
                int bitTable = helper.unpackInt32(in);
                item.setBitTable(bitTable);
                if (bitTable == 0) {
                    continue;
                }
                // span中有多少个值
                int numSpans = Integer.bitCount(bitTable);
                item.setNumSpans(numSpans);
                
                int maxSpans = 30;
                short[] vals = new short[maxSpans];
                
                for (int pos = 0; pos < maxSpans; pos++) {
                    if(pos > numSpans) {
						break;
                    }
                    int mask = 1 << pos;
                    short value = helper.unpackUInt8(in);
                    if ((bitTable & mask) != 0) {
                        vals[pos] = value > 0 ? value : 0;
                    }
                }
                item.setValues(vals);
                list.add(item);
            }
            msg.setData(list);
        }
        return msg;
    }
    /**
     * 呼吸暂停
     * <AUTHOR>
     * @param in
     * @return
     * @throws IOException
     */
    public static AppApneaReporter decodeAppApneaReporter(InputStream in) throws IOException {
        AppApneaReporter msg = new AppApneaReporter();
        msg.setTs(helper.unpackUInt64(in));
        msg.setVersion(helper.unpackUInt8(in));
        msg.setRes(helper.unpackUInt8Array(in, 3));
        msg.setCount(helper.unpackUInt32(in));
        return msg;
    }
    /**
     * CARE_HEARTBEAT 0x51 解码
     * @param in
     * @return
     * @throws IOException
     */
    private static Heartbeat decodeHeartbeat(InputStream in) throws IOException {
        Heartbeat msg = new Heartbeat();
        msg.setTs(helper.unpackUInt64(in));
        msg.setVersion(helper.unpackUInt8(in));
        msg.setWifiRssi(helper.unpackInt8(in));
        msg.setPeopleNum(helper.unpackUInt8(in));
        msg.setIotRssi(helper.unpackInt8(in));
        // uint8_t  res1[6];
        short[] res = new short[6];
        for (int i=0; i<6; i++){
            res[i] = helper.unpackUInt8(in);
        }
        msg.setRes(res);
        msg.setCpuWorkLoad(helper.unpackUInt8(in));
        msg.setCpu1Load(helper.unpackUInt8(in));
        msg.setCpu2Load(helper.unpackUInt8(in));
        msg.setMemWorkLoad(helper.unpackUInt8(in));
        msg.setIRamLoad(helper.unpackUInt8(in));
        msg.setDRamLoad(helper.unpackUInt8(in));
        msg.setSpiRamLoad(helper.unpackUInt8(in));
        msg.setHimemLoad(helper.unpackUInt8(in));
        
        short[] res2 = new short[12];
        for (int i=0; i<12; i++){
            res2[i] = helper.unpackUInt8(in);
        }
        msg.setRes2(res2);
        
        msg.setRegionId(helper.unpackUInt8(in));
        msg.setRegionOccur(helper.unpackUInt8(in));
        
        msg.setBed1Id(helper.unpackUInt8(in));
        msg.setBed1Occur(helper.unpackUInt8(in));
        msg.setBed2Id(helper.unpackUInt8(in));
        msg.setBed2Occur(helper.unpackUInt8(in));
        
        short[] res3 = new short[84];
        for (int i=0; i<84; i++){
            res3[i] = helper.unpackUInt8(in);
        }
            
        return msg;
    }

    /**
     * CARE_JSON 0x50 解码
     * @param in
     * @return
     * @throws IOException
     */
    private static JsonMessageAck decodeJsonMessage(InputStream in) throws IOException {
        int length = in.available();
        String jsonString = helper.unpackString(in, length);
        log.d("原始消息内容为：{}"+ jsonString);
        JsonMessageAck jsonMessageAck = new Gson().fromJson(jsonString, JsonMessageAck.class);
        if (Proto.CmdJson.SET_ROOM.equals(jsonMessageAck.getRespMsgId())){
            return new Gson().fromJson(jsonString, SetRoomJsonAck.class);
        } else if (Proto.CmdJson.GET_ROOM.equals(jsonMessageAck.getRespMsgId())){
            return new Gson().fromJson(jsonString, GetRoomJsonAck.class);
        } else if (Proto.CmdJson.CLEAN_ROOM.equals(jsonMessageAck.getRespMsgId())){
            return new Gson().fromJson(jsonString, CleanRoomJsonAck.class);
        } else if (Proto.CmdJson.GET_CARE_STAT.equals(jsonMessageAck.getRespMsgId())){
            return new Gson().fromJson(jsonString, GetCareStatJsonAck.class);
        } else if (Proto.CmdJson.SET_CARE_INVL.equals(jsonMessageAck.getRespMsgId())){
            return new Gson().fromJson(jsonString, SetCareInvlJsonAck.class);
        } else if (Proto.CmdJson.GET_CARE_INVL.equals(jsonMessageAck.getRespMsgId())){
            return new Gson().fromJson(jsonString, GetCareInvlJsonAck.class);
        } else if (Proto.CmdJson.GET_CARE_INVL_STORE.equals(jsonMessageAck.getRespMsgId())){
            return new Gson().fromJson(jsonString, GetCareInvlStoreJsonAck.class);
        } else if (Proto.CmdJson.GET_DEV_INFO.equals(jsonMessageAck.getRespMsgId())){
            return new Gson().fromJson(jsonString, GetDevInfoJsonAck.class);
        } else if (Proto.CmdJson.SET_DEV_INFO.equals(jsonMessageAck.getRespMsgId())){
            return new Gson().fromJson(jsonString, SetDevInfoJsonAck.class);
            //return new Gson().fromJson(jsonString, JsonMessageAck.class);
        } else if (Proto.CmdJson.OTA.equals(jsonMessageAck.getRespMsgId())){
            return new Gson().fromJson(jsonString, OtaAck.class);
        } else if (Proto.CmdJson.OTA_WHOLE.equals(jsonMessageAck.getRespMsgId())){
            return new Gson().fromJson(jsonString, OtaAck.class);
        } else if (Proto.CmdJson.POINTS_OPEN_STATE.equals(jsonMessageAck.getMsgId())){
            return new Gson().fromJson(jsonString, PointsOpenState.class);
        } else if (Proto.CmdJson.INQUIRY_DEV_INFO.equals(jsonMessageAck.getMsgId())){
            return new Gson().fromJson(jsonString, InquiryDevInfo.class);
        } else {
            throw new UnsupportedOperationException("Unknown json command " + jsonMessageAck);
        }
    }

    /**
     * DEBUG_CLOUD_POINTS_SWITCH 0x30 解码
     * @param in
     * @return
     * @throws IOException
     */
    private static SceneSwitchAck decodeSceneSwitch(InputStream in) throws IOException {
        SceneSwitchAck msg = new SceneSwitchAck();
        msg.setAck(helper.unpackInt32(in));
        return msg;
    }

    /**
     * DEBUG_CLOUD_POINTS 0x31 解码
     * @param in
     * @return
     * @throws IOException
     */
    public static CloudPoints decodeCloudPoints(InputStream in) throws IOException {
        CloudPoints msg = new CloudPoints();
        msg.setLevel(helper.unpackUInt8(in));
        msg.setAmount(helper.unpackUInt32(in));
        msg.setPointCnt(helper.unpackUInt32(in));
        CloudPoints.Frame[] frames = new CloudPoints.Frame[msg.getPointCnt()];
        for (int i = 0; i < frames.length; i ++) {
            CloudPoints.Frame frame = new CloudPoints.Frame();
            frame.setX(helper.unpackFloat(in));
            frame.setY(helper.unpackFloat(in));
            frame.setZ(helper.unpackFloat(in));
            frame.setVelocity(helper.unpackFloat(in));
            frame.setSnr(helper.unpackFloat(in));
            frames[i] = frame;
        }
        msg.setFrames(frames);
        return msg;
    }

    /**
     * DEBUG_CLOUD_POINTS_SWITCH 0x30 解码
     * @param in
     * @return
     * @throws IOException
     */
    private static CloudPointsSwitchAck decodeCloudPointsSwitch(InputStream in) throws IOException {
        CloudPointsSwitchAck msg = new CloudPointsSwitchAck();
        msg.setAck(helper.unpackInt32(in));
        return msg;
    }

    /**
     * CARE_PEOPLE_COUNT 0x25 解码
     * @param in
     * @return
     * @throws IOException
     */
    private static PeopleCount decodePeopleCount(InputStream in) throws IOException {
        PeopleCount msg = new PeopleCount();
        msg.setTs((long)helper.unpackUInt32(in)*1000);
        msg.setReserved(helper.unpackUInt32(in));
        msg.setNum(helper.unpackInt32(in));
        return msg;
    }

    /**
     * CARE_PEOPLE_IO_REGION 0x24 解码
     * @param in
     * @return
     * @throws IOException
     */
    private static OptInOut decodeOptInOut(InputStream in) throws IOException {
        OptInOut msg = new OptInOut();
        msg.setTs((long)helper.unpackUInt32(in)*1000);
        msg.setTid(helper.unpackUInt32(in));
        msg.setEvent(helper.unpackUInt8(in));
        msg.setType(helper.unpackUInt8(in));
        msg.setSubId(helper.unpackUInt16(in));
        msg.setSubType(helper.unpackUInt16(in));
        return msg;
    }

    /**
     * CARE_PEOPLE_STAYING_TOO_LONG 0x23 解码
     * @param in
     * @return
     * @throws IOException
     */
    private static CareMessage decodeStayingTooLong(InputStream in) throws IOException {
        StayTooLong msg = new StayTooLong();
        msg.setTs((long)helper.unpackUInt32(in)*1000);
        msg.setTid(helper.unpackUInt32(in));
        msg.setConf(helper.unpackUInt8(in));
        msg.setX(helper.unpackFloat(in));
        msg.setY(helper.unpackFloat(in));
        msg.setZ(helper.unpackFloat(in));
        return msg;
    }

    /**
     * CARE_PEOPLE_FALLDOWN 0x22 解码
     * @param in
     * @return
     * @throws IOException
     */
    private static CareMessage decodeFallDown(InputStream in) throws IOException{
        FallDown msg = new FallDown();
        msg.setTs((long)helper.unpackUInt32(in)*1000);
        msg.setTid(helper.unpackUInt32(in));
        msg.setConf(helper.unpackShort(in));
        msg.setX(helper.unpackFloat(in));
        msg.setY(helper.unpackFloat(in));
        msg.setZ(helper.unpackFloat(in));
        return msg;
    }

    /**
     * CARE_PEOPLE_GESTURE 0x21 解码
     * @param in
     * @return
     * @throws IOException
     */
    private static CareMessage decodePeopleGesture(InputStream in) throws IOException{
        PeopleGesture msg = new PeopleGesture();
        msg.setTs(helper.unpackUInt64(in));
        msg.setVersion(helper.unpackUInt8(in));
        msg.setRes(helper.unpackUInt8Array(in, 3));
        msg.setGesture(helper.unpackUInt32(in));
        msg.setConfidence(helper.unpackFloat(in));
        return msg;
    }

//    /**
//     * CARE_PEOPLE_RETENTION 0x20 解码 已废弃
//     * @param in
//     * @return
//     * @throws IOException
//     */
//    private static CareMessage decodePeopleRetention(InputStream in) throws IOException{
//        PeopleRetention msg = new PeopleRetention();
//        msg.setTs(helper.unpackUInt32(in));
//        msg.setTid(helper.unpackUInt32(in));
//        msg.setX(helper.unpackFloat(in));
//        msg.setY(helper.unpackFloat(in));
//        return msg;
//    }

    /**
     * CARE_REPORTING_PEOPLE_BREATHE_HEART2 0x17 解码
     * @param in
     * @return
     * @throws IOException
     */
    private static BreathHeartRates2 decodeBreathHeart2(InputStream in) throws IOException{
        BreathHeartRates2 msg = new BreathHeartRates2();
        msg.setTs((long)helper.unpackUInt32(in)*1000);
        msg.setVersion(helper.unpackUInt8(in));
        msg.setNum(helper.unpackUInt8(in));
        BreathHeartRates2.BreathHeartRate2[] breathHeartRates = new BreathHeartRates2.BreathHeartRate2[msg.getNum()];
        for (int i = 0; i < breathHeartRates.length; i ++){
            BreathHeartRates2.BreathHeartRate2 breathHeartRate = new BreathHeartRates2.BreathHeartRate2();
            breathHeartRate.setTid(helper.unpackUInt32(in));
            breathHeartRate.setRes0(helper.unpackUInt8(in));
            breathHeartRate.setConfidence(helper.unpackUInt8(in));
            breathHeartRate.setBreathBPM(helper.unpackUInt8(in));
            breathHeartRate.setHeartBPM(helper.unpackUInt8(in));
            breathHeartRate.setBreathMM((float) helper.unpackInt16(in)/10000);
            breathHeartRate.setHeartMM((float) helper.unpackInt16(in)/10000);
            breathHeartRate.setX((float) helper.unpackInt16(in)/1000);
            breathHeartRate.setY((float) helper.unpackInt16(in)/1000);
            breathHeartRate.setZ((float) helper.unpackInt16(in)/1000);
            breathHeartRate.setRes1(helper.unpackInt16(in));
            breathHeartRates[i] = breathHeartRate;
        }
        msg.setBreathHeartRates(breathHeartRates);
        return msg;
    }

    /**
     * CARE_REPORTING_PEOPLE_LOCATION2 0x15 解码
     * @param in
     * @return
     * @throws IOException
     */
    private static PeopleEvent decodePeopleEvent2(InputStream in) throws IOException{
        PeopleEvent msg = new PeopleEvent();
        msg.setTs((long)helper.unpackUInt32(in)*1000);
        msg.setVersion(helper.unpackUInt8(in));
        msg.setAmount(helper.unpackUInt8(in));
        TargetLocation[] locations = new TargetLocation[msg.getAmount()];
        for (int i = 0; i < locations.length; i ++) {
            TargetLocation location = new TargetLocation();
            location.setTid(helper.unpackUInt32(in));
            location.setCls(helper.unpackUInt16(in));
            location.setPosture(helper.unpackUInt16(in));
            location.setX(helper.unpackFloat(in));
            location.setY(helper.unpackFloat(in));
            location.setZ(helper.unpackFloat(in));
            location.setLength(helper.unpackFloat(in));
            location.setWidth(helper.unpackFloat(in));
            location.setThick(helper.unpackFloat(in));
            location.setVelocity(helper.unpackFloat(in));
            location.setAcceleration(helper.unpackFloat(in));
            locations[i] = location;
        }
        msg.setTargets(locations);
        return msg;
    }

    /**
     * CARE_REPORTING_ROOM_ENV 0x14 解码
     * @param in
     * @return
     * @throws IOException
     */
    private static RoomEnv decodeRoomEvn(InputStream in) throws IOException {
        RoomEnv msg = new RoomEnv();
        msg.setTs((long)helper.unpackUInt32(in)*1000);
        msg.setLight(helper.unpackFloat(in));
        msg.setTemp(helper.unpackFloat(in));
        return msg;
    }

//    /**
//     * CARE_REPORTING_PEOPLE_HEART 0x13 解码 已废弃
//     * @param in
//     * @return
//     * @throws IOException
//     */
//    private static CareMessage decodeHeart(InputStream in) throws IOException{
//        HeartRate msg = new HeartRate();
//        msg.setTs(helper.unpackUInt32(in));
//        msg.setTid(helper.unpackUInt32(in));
//        msg.setHeart(helper.unpackShort(in));
//        return msg;
//    }

    /**
     * CARE_REPORTING_PEOPLE_BREATHE_HEART 0x12 解码
     * @param in
     * @return
     * @throws IOException
     */
    private static BreathHeartRate decodeBreathHeart(InputStream in) throws IOException{
        BreathHeartRate msg = new BreathHeartRate();
        msg.setTs((long)helper.unpackUInt32(in)*1000);
        msg.setTid(helper.unpackUInt32(in));
        msg.setBreath(Math.round((float) helper.unpackUInt16(in)/100));
        msg.setHeart(Math.round((float) helper.unpackUInt16(in)/100));
        msg.setBconf(helper.unpackUInt8(in));
        msg.setHconf(helper.unpackUInt8(in));
        // 兼容心率呼吸新字段
        if (in.available() >= 1){
            msg.setBreathMM((float) helper.unpackInt16(in)/10000);
            msg.setHeartMM((float) helper.unpackInt16(in)/10000);
        }
        return msg;
    }

    /**
     * CARE_REPORTING_PEOPLE_STATE 0x11 解码
     * @param in
     * @return
     * @throws IOException
     */
    private static PeopleState decodePeopleState(InputStream in) throws IOException {
        PeopleState msg = new PeopleState();
        msg.setTs((long)helper.unpackUInt32(in)*1000);
        msg.setTid(helper.unpackUInt32(in));
        msg.setState(helper.unpackUInt32(in));
        return msg;
    }

    /**
     * CARE_REPORTING_PEOPLE_LOCATION 0x10 解码
     * @param in
     * @return
     * @throws IOException
     */
    private static PeopleEvent decodePeopleEvent(InputStream in) throws IOException {
        PeopleEvent msg = new PeopleEvent();
        msg.setTs((long)helper.unpackUInt32(in)*1000);
        msg.setAmount(helper.unpackShort(in));
        TargetLocation[] locations = new TargetLocation[msg.getAmount()];
        for (int i = 0; i < locations.length; i ++) {
            TargetLocation location = new TargetLocation();
            location.setTid(helper.unpackUInt32(in));
            location.setCls(helper.unpackUInt16(in));
            location.setPosture(helper.unpackUInt16(in));
            location.setX(helper.unpackFloat(in));
            location.setY(helper.unpackFloat(in));
            location.setZ(helper.unpackFloat(in));
            location.setLength(helper.unpackFloat(in));
            location.setWidth(helper.unpackFloat(in));
            location.setThick(helper.unpackFloat(in));
            locations[i] = location;
        }
        msg.setTargets(locations);
        return msg;
    }

    /**
     * CARE_SWITCH_MODE 0x0b 解码
     * @param in
     * @return
     * @throws IOException
     */
    private static CareMessage decodeCfgSwitchMode(InputStream in) throws IOException {
        CfgSwitchModeAck msg = new CfgSwitchModeAck();
        msg.setAck(helper.unpackInt32(in));
        return msg;
    }
    /**
     * CARE_CFG_ROOM_TYPE 0x0c 解码
     * @param in
     * @return
     * @throws IOException
     */
    private static CareMessage decodeCfgRoomType(InputStream in) throws IOException {
        CfgRoomTypeAck msg = new CfgRoomTypeAck();
        msg.setAck(helper.unpackInt32(in));
        return msg;
    }

    /**
     * CARE_CFG_TRACKER 0x09 解码
     * @param in
     * @return
     * @throws IOException
     */
    private static CareMessage decodeCfgTracker(InputStream in) throws IOException {
        CfgTrackerAck msg = new CfgTrackerAck();
        msg.setAck(helper.unpackInt32(in));
        return msg;
    }

    /**
     * CARE_CFG_VS_RANGE 0x07 解码
     * @param in
     * @return
     * @throws IOException
     */
    private static CareMessage decodeCfgVs(InputStream in) throws IOException {
        CfgVsRangeAck msg = new CfgVsRangeAck();
        msg.setAck(helper.unpackInt32(in));
        return msg;
    }

    /**
     * 废弃
     * CARE_CFG_VS_RANGE 0x07 解码
     * @param in
     * @return
     * @throws IOException
     */
    private static CareMessage decodeCfgVs1(InputStream in) throws IOException {
        CfgVsRange msg = new CfgVsRange();
        msg.setStart(helper.unpackFloat(in));
        msg.setEnd(helper.unpackFloat(in));
        return msg;
    }

    /**
     * CARE_CFG_SWITCH_THRESHOLD 0x06 解码
     * @param in
     * @return
     * @throws IOException
     */
    private static CareMessage decodeCfgSwitch(InputStream in) throws IOException {
        CfgSwitchThresholdAck msg = new CfgSwitchThresholdAck();
        msg.setAck(helper.unpackInt32(in));
        return msg;
    }

    /**
     * 废弃
     * CARE_CFG_SWITCH_THRESHOLD 0x06 解码
     * @param in
     * @return
     * @throws IOException
     */
    private static CareMessage decodeCfgSwitch1(InputStream in) throws IOException {
        CfgSwitchThreshold msg = new CfgSwitchThreshold();
        msg.setMoveThreshold(helper.unpackUInt32(in));
        msg.setStaticTimeThreshold(helper.unpackUInt32(in));
        return msg;
    }

    /**
     * CARE_CFG_REGIONS 0x05 解码
     * @param in
     * @return
     * @throws IOException
     */
    private static CareMessage decodeCfgRegions(InputStream in) throws IOException {
        CfgRegionsAck msg = new CfgRegionsAck();
        msg.setAck(helper.unpackInt32(in));
        return msg;
    }

    /**
     * 废弃
     * CARE_CFG_REGIONS 0x05 解码
     * @param in
     * @return
     * @throws IOException
     */
    private static CareMessage decodeCfgRegions1(InputStream in) throws IOException {
        CfgRegions msg = new CfgRegions();
        int num = helper.unpackUInt8(in);
        CfgRegions.Region[] regions = new CfgRegions.Region[num];
        for (int i = 0; i < num; i++) {
            CfgRegions.Region region = new CfgRegions.Region();
            region.setRid(helper.unpackUInt32(in));
            region.setCls(helper.unpackUInt32(in));
            region.setPositionX(helper.unpackFloat(in));
            region.setPositionY(helper.unpackFloat(in));
            region.setScaleX(helper.unpackFloat(in));
            region.setScaleY(helper.unpackFloat(in));
            region.setScaleZ(helper.unpackFloat(in));
            region.setRotation(helper.unpackFloat(in));
        }
        msg.setRegions(regions);
        return msg;
    }

    /**
     * CARE_CFG_GATES 0x04 解码
     * @param in
     * @return
     * @throws IOException
     */
    private static CareMessage decodeCfgEntrances(InputStream in) throws IOException {
        CfgEntrancesAck msg = new CfgEntrancesAck();
        msg.setAck(helper.unpackInt32(in));
        return msg;
    }

    /**
     * 废弃
     * CARE_CFG_GATES 0x04 解码
     * @param in
     * @return
     * @throws IOException
     */
    private static CareMessage decodeCfgEntrances1(InputStream in) throws IOException {
        CfgEntrances msg = new CfgEntrances();
        int num = helper.unpackUInt8(in);
        CfgEntrances.Entrance[] gates = new CfgEntrances.Entrance[num];
        for (int i = 0; i < gates.length; i++) {
            CfgEntrances.Entrance gate = new CfgEntrances.Entrance();
            gate.setId(helper.unpackUInt8(in));
            gate.setDirection(helper.unpackUInt8(in));
            gate.setLength(helper.unpackFloat(in));
            gate.setWidth(helper.unpackFloat(in));
            gate.setHight(helper.unpackFloat(in));
            gates[i] = gate;
        }
        msg.setEntrances(gates);
        return msg;
    }

    /**
     * CARE_CFG_DEV_POSITION 0x03 解码
     * @param in
     * @return
     * @throws IOException
     */
    private static CareMessage decodeCfgDevicePosition(InputStream in) throws IOException {
        CfgDevicePositionAck msg = new CfgDevicePositionAck();
        msg.setAck(helper.unpackInt32(in));
        return msg;
    }

    /**
     * 废弃
     * CARE_CFG_DEV_POSITION 0x03 解码
     * @param in
     * @return
     * @throws IOException
     */
    private static CareMessage decodeCfgDevicePosition1(InputStream in) throws IOException {
        CfgDevicePosition msg = new CfgDevicePosition();
        msg.setX(helper.unpackFloat(in));
        msg.setY(helper.unpackFloat(in));
        msg.setZ(helper.unpackFloat(in));
        msg.setAtilt(helper.unpackUInt16(in));
        msg.setEtilt(helper.unpackUInt16(in));
        msg.setAtiltfov(helper.unpackUInt16(in));
        msg.setEtiltfov(helper.unpackUInt16(in));
        msg.setMount(helper.unpackUInt8(in));
        return msg;
    }

    /**
     * CARE_CFG_AREA 0x02 解码
     * @param in
     * @return
     * @throws IOException
     */
    private static CareMessage decodeCfgArea(InputStream in) throws IOException {
        CfgAreaAck msg = new CfgAreaAck();
        msg.setAck(helper.unpackInt32(in));
        return msg;
    }

    /**
     * 废弃
     * CARE_CFG_AREA 0x02 解码
     * @param in
     * @return
     * @throws IOException
     */
    private static CareMessage decodeCfgArea1(InputStream in) throws IOException {
        CfgArea msg = new CfgArea();
        msg.setX(helper.unpackFloat(in));
        msg.setY(helper.unpackFloat(in));
        msg.setZ(helper.unpackFloat(in));
        return msg;
    }

    /**
     * CARE_CFG_REPORTING_INTERVAL 0x01 解码
     * @param in
     * @return
     * @throws IOException
     */
    private static CareMessage decodeCfgReportingInterval(InputStream in) throws IOException {
        CfgReportingIntervalAck msg = new CfgReportingIntervalAck();
        msg.setAck(helper.unpackInt32(in));
        return msg;
    }

    /**
     * 废弃
     * CARE_CFG_REPORTING_INTERVAL 0x01 解码
     * @param in
     * @return
     * @throws IOException
     */
    private static CareMessage decodeCfgReportingInterval1(InputStream in) throws IOException {
        CfgReportingInterval msg = new CfgReportingInterval();
        int num = helper.unpackShort(in);
        CfgReportingInterval.Reporter[] reporters = new CfgReportingInterval.Reporter[num];
        for(int i=0; i < reporters.length; i++) {
            CfgReportingInterval.Reporter reporter = new CfgReportingInterval.Reporter();
            reporter.setReportingId(helper.unpackUInt8(in));
            reporter.setInterval(helper.unpackUInt32(in));
            reporters[i] = reporter;
        }
        msg.setReporters(reporters);
        return msg;
    }

    /**
     * CARE_HANDSHAKE 0x00 解码
     * @param payload
     * @return
     */
    private static HandShake decodeHandShake(byte[] payload) {
        HandShake msg = new HandShake();
        msg.setMessage(payload.length > 0 ? new String(payload, StandardCharsets.UTF_8) : null);
        log.d("handshake: " + msg.getMessage());
        return msg;
    }
}
