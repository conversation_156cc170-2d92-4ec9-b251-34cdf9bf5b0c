package com.ventropic.care.proto.message;

import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
public class BreathHeartRate implements CareMessage{
    public long getTs() {
        return ts;
    }

    public void setTs(long ts) {
        this.ts = ts;
    }

    public int getTid() {
        return tid;
    }

    public void setTid(int tid) {
        this.tid = tid;
    }

    public int getBreath() {
        return breath;
    }

    public void setBreath(int breath) {
        this.breath = breath;
    }

    public int getHeart() {
        return heart;
    }

    public void setHeart(int heart) {
        this.heart = heart;
    }

    public short getBconf() {
        return bconf;
    }

    public void setBconf(short bconf) {
        this.bconf = bconf;
    }

    public short getHconf() {
        return hconf;
    }

    public void setHconf(short hconf) {
        this.hconf = hconf;
    }

    public Float getBreathMM() {
        return breathMM;
    }

    public void setBreathMM(Float breathMM) {
        this.breathMM = breathMM;
    }

    public Float getHeartMM() {
        return heartMM;
    }

    public void setHeartMM(Float heartMM) {
        this.heartMM = heartMM;
    }
    private long ts;
    private int tid;
    private int breath;
    private int heart;
    private short bconf;
    private short hconf;
    private Float breathMM;
    private Float heartMM;

    public BreathHeartRates2 toBreathHeartRates2(){
        BreathHeartRates2 msg = new BreathHeartRates2();
        msg.setTs(this.getTs());
        msg.setVersion(0);
        msg.setNum(1);
        BreathHeartRates2.BreathHeartRate2[] breathHeartRates = new BreathHeartRates2.BreathHeartRate2[1];
        BreathHeartRates2.BreathHeartRate2 bhr1 = new BreathHeartRates2.BreathHeartRate2();
        bhr1.setTid(this.getTid());
        bhr1.setRes0(0);
        bhr1.setConfidence(this.getBconf());
        bhr1.setBreathBPM(this.getBreath());
        bhr1.setHeartBPM(this.getHeart());
        Float breathMM = this.getBreathMM();
        if (null != breathMM) {
            bhr1.setBreathMM(breathMM);
        }
        Float heartMM = this.getHeartMM();
        if (null != heartMM) {
            bhr1.setHeartMM(heartMM);
        }
        bhr1.setX(0);
        bhr1.setY(0);
        bhr1.setZ(0);
        bhr1.setRes1(0);
        breathHeartRates[0] = bhr1;
        msg.setBreathHeartRates(breathHeartRates);
        return msg;
    }
}
