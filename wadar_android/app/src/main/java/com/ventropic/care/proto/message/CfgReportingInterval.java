package com.ventropic.care.proto.message;


import lombok.NoArgsConstructor;

 @NoArgsConstructor
public class CfgReportingInterval implements CareMessage{
    private Reporter[] reporters;

    public Reporter[] getReporters() {
        return reporters;
    }

    public void setReporters(Reporter[] reporters) {
        this.reporters = reporters;
    }

     @NoArgsConstructor
    public static class Reporter {
        private short reportingId;
        private int interval;

        public Reporter set(int id, int interval) {
            reportingId = (short) id;
            this.interval = interval;
            return this;
        }

        public short getReportingId() {
            return reportingId;
        }

        public void setReportingId(short reportingId) {
            this.reportingId = reportingId;
        }

        public int getInterval() {
            return interval;
        }

        public void setInterval(int interval) {
            this.interval = interval;
        }
    }
}
