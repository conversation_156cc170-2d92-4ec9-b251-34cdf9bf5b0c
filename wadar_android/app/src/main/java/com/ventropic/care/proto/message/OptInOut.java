package com.ventropic.care.proto.message;

import lombok.Data;
import lombok.NoArgsConstructor;

 @NoArgsConstructor
public class OptInOut implements CareMessage {
    private long ts;
    private int tid;
    private short event;
    private short type;
    private int subId;
    private int subType;

     public long getTs() {
         return ts;
     }

     public void setTs(long ts) {
         this.ts = ts;
     }

     public int getTid() {
         return tid;
     }

     public void setTid(int tid) {
         this.tid = tid;
     }

     public short getEvent() {
         return event;
     }

     public void setEvent(short event) {
         this.event = event;
     }

     public short getType() {
         return type;
     }

     public void setType(short type) {
         this.type = type;
     }

     public int getSubId() {
         return subId;
     }

     public void setSubId(int subId) {
         this.subId = subId;
     }

     public int getSubType() {
         return subType;
     }

     public void setSubType(int subType) {
         this.subType = subType;
     }
 }
