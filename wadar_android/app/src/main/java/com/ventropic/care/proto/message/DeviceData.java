package com.ventropic.care.proto.message;

import java.io.Serializable;




public class DeviceData implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	/**
	 * uint32_t, 服务器的记录每次改变都要更新这个值（0保留给设备，作初始请求）
	 */
	private int recordVersion;
	/**
	 * 设备运行模式，int32_t, 0:日常模式， 1：测试模式, 2:透传
	 */
	private Integer careMode;
	/**
	 * 应用层心跳间隔，uint32_t 单位ms，
	 */
	private Integer careHeartbeatInvl;
	/**
	 * 固定搜索心率呼吸开关，uint8_t, 1:enable, 0:disable
	 */
	private Integer fixedRscanEn;
	/**
	 * 上传摔倒数据文件开关，uint8_t, 1:enable, 0:disable, default 1
	 */
	private Integer upRecordEn;
	/**
	 * 日志上传级别掩码，uint8_t, default:3(INFO), 0:NONE, 1:ERROR, 2:WARN, 3:INFO, 4:DEBUG
	 */
	private Integer logLevel;
	/**
	 * 和点云开关一样，这里只是会自动打开，沟通后感觉没必要 摔倒自动开点云开关，uint8_t, 1:enable, 0:disable, default 1
	 */
	@Deprecated
	private Integer fallPointsEn;
	/**
	 * 热点开关，uint8_t, 1;enable, 0:disable, default 0
	 */
	private Integer hotPlaceEn;
	/**
	 * 点云上报开关，uint8_t, 1;enable, 0:disable, default 0
	 */
	private Integer cloudPointsEn;
	/**
	 * 透传原始数据开关， uint8_t, 1;enable, 0:disable, default 0；
	 * 注意这个没有记录功能，后期可能会根据应用改成需要透传的时间
	 */
	private Integer transferRawEn;
	/**
	 * 位置数据不变化是否发送开关（location constant report enable）;uint8_t, 1;enable, 0:disable,
	 * default 1；
	 */
	private Integer locaCnstRptEn;
	
	/**
	 * len < 64, https上传文件到这里， 默认mqtt（mqtt自己无ip和url和port时，默认链接端口1883）也连接这里
	 */
	private String domainName;

	private InfoData info;
	private MqttData mqtt;
	private MqttData mqtt2;
	private WifiData wifi;
	private RoomJson room;
	private CareInvl careInvl;
	private RadarData radar;

	public int getRecordVersion() {
		return recordVersion;
	}

	public void setRecordVersion(int recordVersion) {
		this.recordVersion = recordVersion;
	}

	public Integer getCareMode() {
		return careMode;
	}

	public void setCareMode(Integer careMode) {
		this.careMode = careMode;
	}

	public Integer getCareHeartbeatInvl() {
		return careHeartbeatInvl;
	}

	public void setCareHeartbeatInvl(Integer careHeartbeatInvl) {
		this.careHeartbeatInvl = careHeartbeatInvl;
	}

	public Integer getFixedRscanEn() {
		return fixedRscanEn;
	}

	public void setFixedRscanEn(Integer fixedRscanEn) {
		this.fixedRscanEn = fixedRscanEn;
	}

	public Integer getUpRecordEn() {
		return upRecordEn;
	}

	public void setUpRecordEn(Integer upRecordEn) {
		this.upRecordEn = upRecordEn;
	}

	public Integer getLogLevel() {
		return logLevel;
	}

	public void setLogLevel(Integer logLevel) {
		this.logLevel = logLevel;
	}

	public Integer getFallPointsEn() {
		return fallPointsEn;
	}

	public void setFallPointsEn(Integer fallPointsEn) {
		this.fallPointsEn = fallPointsEn;
	}

	public Integer getHotPlaceEn() {
		return hotPlaceEn;
	}

	public void setHotPlaceEn(Integer hotPlaceEn) {
		this.hotPlaceEn = hotPlaceEn;
	}

	public Integer getCloudPointsEn() {
		return cloudPointsEn;
	}

	public void setCloudPointsEn(Integer cloudPointsEn) {
		this.cloudPointsEn = cloudPointsEn;
	}

	public Integer getTransferRawEn() {
		return transferRawEn;
	}

	public void setTransferRawEn(Integer transferRawEn) {
		this.transferRawEn = transferRawEn;
	}

	public Integer getLocaCnstRptEn() {
		return locaCnstRptEn;
	}

	public void setLocaCnstRptEn(Integer locaCnstRptEn) {
		this.locaCnstRptEn = locaCnstRptEn;
	}

	public String getDomainName() {
		return domainName;
	}

	public void setDomainName(String domainName) {
		this.domainName = domainName;
	}

	public InfoData getInfo() {
		return info;
	}

	public void setInfo(InfoData info) {
		this.info = info;
	}

	public MqttData getMqtt() {
		return mqtt;
	}

	public void setMqtt(MqttData mqtt) {
		this.mqtt = mqtt;
	}

	public MqttData getMqtt2() {
		return mqtt2;
	}

	public void setMqtt2(MqttData mqtt2) {
		this.mqtt2 = mqtt2;
	}

	public WifiData getWifi() {
		return wifi;
	}

	public void setWifi(WifiData wifi) {
		this.wifi = wifi;
	}

	public RoomJson getRoom() {
		return room;
	}

	public void setRoom(RoomJson room) {
		this.room = room;
	}

	public CareInvl getCareInvl() {
		return careInvl;
	}

	public void setCareInvl(CareInvl careInvl) {
		this.careInvl = careInvl;
	}

	public RadarData getRadar() {
		return radar;
	}

	public void setRadar(RadarData radar) {
		this.radar = radar;
	}

	
	public static class InfoData implements Serializable {
		/**
         * 
         */
        private static final long serialVersionUID = -2465426941792290032L;
        private String mdid;
		private Long cmei;
		private String coId;
		/**
		 * [year, nonth, day, week]
		 */
		private int[] manuDate;

		public String getMdid() {
			return mdid;
		}

		public void setMdid(String mdid) {
			this.mdid = mdid;
		}

		public Long getCmei() {
			return cmei;
		}

		public void setCmei(Long cmei) {
			this.cmei = cmei;
		}

		public String getCoId() {
			return coId;
		}

		public void setCoId(String coId) {
			this.coId = coId;
		}

		public int[] getManuDate() {
			return manuDate;
		}

		public void setManuDate(int[] manuDate) {
			this.manuDate = manuDate;
		}
	}

	
	public static class MqttData implements Serializable {
		/**
         * 
         */
        private static final long serialVersionUID = 983666609274550812L;
        /**
		 * len < 64        // 当string或ip不为空并且port不为空时mqtt连接这里地址
		 */
		private String url;
		/**
		 *  len < 16
		 */
		private String ip;
		private Integer port;
		/**
		 * len < 32
		 */
		private String username;
		/**
		 * len < 32
		 */
		private String passwd;

		public String getUrl() {
			return url;
		}

		public void setUrl(String url) {
			this.url = url;
		}

		public String getIp() {
			return ip;
		}

		public void setIp(String ip) {
			this.ip = ip;
		}

		public Integer getPort() {
			return port;
		}

		public void setPort(Integer port) {
			this.port = port;
		}

		public String getUsername() {
			return username;
		}

		public void setUsername(String username) {
			this.username = username;
		}

		public String getPasswd() {
			return passwd;
		}

		public void setPasswd(String passwd) {
			this.passwd = passwd;
		}
	}

	
	public static class WifiData implements Serializable {
		/**
         * 
         */
        private static final long serialVersionUID = -5704095224715712885L;
        // 1:覆盖，并重启连接新ssid，0：不更新此项
        private int overWrite;
        private String ssid;
		private String passwd;

		public int getOverWrite() {
			return overWrite;
		}

		public void setOverWrite(int overWrite) {
			this.overWrite = overWrite;
		}

		public String getSsid() {
			return ssid;
		}

		public void setSsid(String ssid) {
			this.ssid = ssid;
		}

		public String getPasswd() {
			return passwd;
		}

		public void setPasswd(String passwd) {
			this.passwd = passwd;
		}
	}

	
	public static class RadarData implements Serializable {
		/**
         * 
         */
        private static final long serialVersionUID = -8545664178976944696L;
        private Float[] calibration;

		public Float[] getCalibration() {
			return calibration;
		}

		public void setCalibration(Float[] calibration) {
			this.calibration = calibration;
		}
	}

	
	public static class CareInvl implements Serializable {
		/**
         * 
         */
        private static final long serialVersionUID = -6943316892703630614L;
        /**
		 * 摔倒延时确认时间（新版本弃用），N,int32 sec， N表示非必填项，
		 */
		private Integer fallConfInvl;
		/**
		 * 久滞延时时间，N, int32 sec
		 */
		private Integer stayTooLongInvl;
		/**
		 * peopleEvent上传间隔,N, >=100 ms 并且是100的整数倍
		 */
		private Integer locaRptInvl;
		/**
		 * 心率呼吸上传间隔,N, >=1000 ms 并且是100的整数倍
		 */
		private Integer hbRptInvl;

		public Integer getFallConfInvl() {
			return fallConfInvl;
		}

		public void setFallConfInvl(Integer fallConfInvl) {
			this.fallConfInvl = fallConfInvl;
		}

		public Integer getStayTooLongInvl() {
			return stayTooLongInvl;
		}

		public void setStayTooLongInvl(Integer stayTooLongInvl) {
			this.stayTooLongInvl = stayTooLongInvl;
		}

		public Integer getLocaRptInvl() {
			return locaRptInvl;
		}

		public void setLocaRptInvl(Integer locaRptInvl) {
			this.locaRptInvl = locaRptInvl;
		}

		public Integer getHbRptInvl() {
			return hbRptInvl;
		}

		public void setHbRptInvl(Integer hbRptInvl) {
			this.hbRptInvl = hbRptInvl;
		}
	}
}