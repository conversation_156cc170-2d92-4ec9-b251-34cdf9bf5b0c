package com.ventropic.rfc.proto.message.json;

import com.ventropic.care.proto.message.GetCareInvlStoreJson;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */

@EqualsAndHashCode(callSuper=false)
public class OtaAck extends JsonMessageAck {

    /**
     * 覆写该方法是为了能过够打印出父类的属性值
     * @return
     */
    @Override
    public String toString() {
        return "OtaAck{" +
                "respMsgId='" + super.getRespMsgId() + '\'' +
                ", seqId=" + super.getSeqId() +
                ", respCode=" + super.getRespCode() +
                ", respCont='" + super.getRespCont() + '\'' +
                '}';
    }
}
