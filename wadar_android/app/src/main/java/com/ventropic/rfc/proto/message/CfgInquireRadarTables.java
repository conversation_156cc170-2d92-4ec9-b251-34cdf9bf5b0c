package com.ventropic.rfc.proto.message;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
 @NoArgsConstructor
public class CfgInquireRadarTables implements PlatMessage {
    /**
     * 1为有效，其它无效
     */
    private int ret;
    /**
     * 雷达默认版本id
     */
    private int defaultMode;
    private CfgInquireRadarTable[] cfgInquireRadarTables;

     @NoArgsConstructor
    public static class CfgInquireRadarTable {
        /**
         * 雷达版本id
         */
        private int mode;
        /**
         * 雷达版本
         */
        private String version;
        private String paraVersion;

        public CfgInquireRadarTable set(int mode, String version,String paraVersion) {
            this.mode = mode;
            this.version = version;
            this.paraVersion = paraVersion;
            return this;
        }
    }
}
