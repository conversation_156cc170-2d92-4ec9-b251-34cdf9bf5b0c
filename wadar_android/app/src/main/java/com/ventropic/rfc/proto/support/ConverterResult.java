package com.ventropic.rfc.proto.support;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

import com.ventropic.care.proto.message.CareMessage;
import com.ventropic.common.json.JsonUtils;
import com.ventropic.modules.room.VO.RoomVO;
import com.ventropic.rfc.proto.message.PlatMessage;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import wvr.wadar.app.BlufiLog;

/** 	 
 * 有原来的RoomVOConverter.Result抽出
*/

public class ConverterResult implements Serializable {
    /**
     * 
     */
    private static final long serialVersionUID = 4797027996984093022L;
    private final BlufiLog log = new BlufiLog(getClass());
    private String type;
    private String mdid;
    private Object msg;

    private static final Map<String, Class<?>> CLASS_CACHE = new HashMap<>();

    public ConverterResult(Object msg,String mdid) {
        this.mdid = mdid;
        this.type = msg.getClass().getSimpleName();
        this.msg = msg;
    }
    
    public ConverterResult() {
    }
    
    public String toJson() {
       return JsonUtils.toJson(this);
    }
    
    public ConverterResult from(String json) throws ClassNotFoundException {
        // 解析 JSON 为树形结构
        Map<String, Object> tree = JsonUtils.parseTree(json);
        Object typeNode = tree.get("type");
        Object msgNode = tree.get("msg");

        if (typeNode == null || msgNode == null || !(typeNode instanceof String)) {
            throw new IllegalArgumentException("json format wrong: " + tree.toString());
        }

        String textValue = (String) typeNode;
        log.i("type:" + textValue);
        log.i("msg:" + msgNode.toString());

        setType(textValue);

        Class<?> lookupTypeClass = lookupTypeClass(getType());
        log.i("class:" + lookupTypeClass);

        // 将 msg 节点解析为 Map<String, Object>
        Map<String, Object> data = JsonUtils.fromJsonToMap(msgNode.toString());
        log.i("data:" + data);

        // 将 Map 转换为目标类型的对象
        setMsg(JsonUtils.fromJson(JsonUtils.toJson(data), lookupTypeClass));
        log.i(this.toString());

        return this;
    }

    private Class<?> lookupTypeClass(String type) throws ClassNotFoundException{
        if (CLASS_CACHE.containsKey(type)) {
            return CLASS_CACHE.get(type);
        }

        // 定义需要搜索的包路径列表
        String[] packages = {
            CareMessage.class.getPackage().getName(),
            PlatMessage.class.getPackage().getName(),
            RoomVO.class.getPackage().getName()
        };
        
        for (String pkg : packages) {
            try {
                Class<?> clazz = Class.forName(pkg + "." + type);
                CLASS_CACHE.put(type, clazz); // 缓存加载到的类
                return clazz;
            } catch (ClassNotFoundException ignored) {
                // 如果当前包中找不到类，继续尝试下一个包
            }
        }

        // 如果所有包中都找不到类，记录日志并抛出异常
        log.e("ClassNotFoundException: " + type);
        throw new ClassNotFoundException("Class not found: " + type);
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getMdid() {
        return mdid;
    }

    public void setMdid(String mdid) {
        this.mdid = mdid;
    }

    public Object getMsg() {
        return msg;
    }

    public void setMsg(Object msg) {
        this.msg = msg;
    }
}
