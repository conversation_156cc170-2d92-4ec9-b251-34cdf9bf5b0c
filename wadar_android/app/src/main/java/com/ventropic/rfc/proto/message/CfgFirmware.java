package com.ventropic.rfc.proto.message;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
 @NoArgsConstructor
public class CfgFirmware implements PlatMessage, Serializable {
    private int fid;
    private String cfg;

    public int getFid() {
        return fid;
    }

    public void setFid(int fid) {
        this.fid = fid;
    }

    public String getCfg() {
        return cfg;
    }

    public void setCfg(String cfg) {
        this.cfg = cfg;
    }
}
