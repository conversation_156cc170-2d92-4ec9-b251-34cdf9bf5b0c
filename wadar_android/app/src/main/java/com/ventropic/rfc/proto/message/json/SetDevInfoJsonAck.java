package com.ventropic.rfc.proto.message.json;

//import com.alibaba.fastjson.annotation.JSONField;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper=false)
public class SetDevInfoJsonAck extends JsonMessageAck {
    
    public Object getData() {
        return data;
    }
    
    public void setData(Object data) {
        this.data = data;
    }
    /**
     * 返回消息体
     */
//    @JSONField(serialize = false)
    private Object data;

    /**
     * 覆写该方法是为了能过够打印出父类的属性值
     * @return
     */
    @Override
    public String toString() {
        return "SetDevInfoJsonAck{" +
                "respMsgId='" + super.getRespMsgId() + '\'' +
                ", seqId=" + super.getSeqId() +
                ", respCode=" + super.getRespCode() +
                ", respCont='" + super.getRespCont() + '\'' +
                ", data=" + data +
                '}';
    }
}
