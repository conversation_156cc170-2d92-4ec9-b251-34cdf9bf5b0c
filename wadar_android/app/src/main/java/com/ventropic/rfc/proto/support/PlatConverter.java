package com.ventropic.rfc.proto.support;

import com.ventropic.rfc.proto.message.PlatMessage;
import com.ventropic.rfc.proto.plat.Message;

/**
 * <AUTHOR>
 */
public interface PlatConverter {

    /**
     * 解析plat层
     * @param msg
     * @return
     * @throws Exception
     */
    PlatMessage decode(Message msg) throws Exception;

    /**
     * 解析plat层为json
     * @param msg
     * @param mdid
     * @return
     * @throws Exception
     */
    String convert(PlatMessage msg, String mdid) throws Exception;

    /**
     * 封装plat层
     * @param msg
     * @param msgId
     * @return
     * @throws Exception
     */
    byte[] encode(PlatMessage msg, int msgId) throws Exception;
}
