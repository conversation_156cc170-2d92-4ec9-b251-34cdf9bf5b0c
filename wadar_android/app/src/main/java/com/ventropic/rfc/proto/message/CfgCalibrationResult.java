package com.ventropic.rfc.proto.message;

import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@NoArgsConstructor
public class CfgCalibrationResult implements PlatMessage, Serializable {

    private int version;
    private String ts;
    private String controlTopic;
    private String mac;
    private int res;

    private float rangeBias;
    private float re00;
    private float im00;
    private float re01;
    private float im01;
    private float re02;
    private float im02;
    private float re03;
    private float im03;
    private float re10;
    private float im10;
    private float re11;
    private float im11;
    private float re12;
    private float im12;
    private float re13;
    private float im13;
    private float re20;
    private float im20;
    private float re21;
    private float im21;
    private float re22;
    private float im22;
    private float re23;
    private float im23;

    public int getVersion() {
        return version;
    }

    public void setVersion(int version) {
        this.version = version;
    }

    public String getTs() {
        return ts;
    }

    public void setTs(String ts) {
        this.ts = ts;
    }

    public String getControlTopic() {
        return controlTopic;
    }

    public void setControlTopic(String controlTopic) {
        this.controlTopic = controlTopic;
    }

    public String getMac() {
        return mac;
    }

    public void setMac(String mac) {
        this.mac = mac;
    }

    public int getRes() {
        return res;
    }

    public void setRes(int res) {
        this.res = res;
    }

    public float getRangeBias() {
        return rangeBias;
    }

    public void setRangeBias(float rangeBias) {
        this.rangeBias = rangeBias;
    }

    public float getRe00() {
        return re00;
    }

    public void setRe00(float re00) {
        this.re00 = re00;
    }

    public float getIm00() {
        return im00;
    }

    public void setIm00(float im00) {
        this.im00 = im00;
    }

    public float getRe01() {
        return re01;
    }

    public void setRe01(float re01) {
        this.re01 = re01;
    }

    public float getIm01() {
        return im01;
    }

    public void setIm01(float im01) {
        this.im01 = im01;
    }

    public float getRe02() {
        return re02;
    }

    public void setRe02(float re02) {
        this.re02 = re02;
    }

    public float getIm02() {
        return im02;
    }

    public void setIm02(float im02) {
        this.im02 = im02;
    }

    public float getRe03() {
        return re03;
    }

    public void setRe03(float re03) {
        this.re03 = re03;
    }

    public float getIm03() {
        return im03;
    }

    public void setIm03(float im03) {
        this.im03 = im03;
    }

    public float getRe10() {
        return re10;
    }

    public void setRe10(float re10) {
        this.re10 = re10;
    }

    public float getIm10() {
        return im10;
    }

    public void setIm10(float im10) {
        this.im10 = im10;
    }

    public float getRe11() {
        return re11;
    }

    public void setRe11(float re11) {
        this.re11 = re11;
    }

    public float getIm11() {
        return im11;
    }

    public void setIm11(float im11) {
        this.im11 = im11;
    }

    public float getRe12() {
        return re12;
    }

    public void setRe12(float re12) {
        this.re12 = re12;
    }

    public float getIm12() {
        return im12;
    }

    public void setIm12(float im12) {
        this.im12 = im12;
    }

    public float getRe13() {
        return re13;
    }

    public void setRe13(float re13) {
        this.re13 = re13;
    }

    public float getIm13() {
        return im13;
    }

    public void setIm13(float im13) {
        this.im13 = im13;
    }

    public float getRe20() {
        return re20;
    }

    public void setRe20(float re20) {
        this.re20 = re20;
    }

    public float getIm20() {
        return im20;
    }

    public void setIm20(float im20) {
        this.im20 = im20;
    }

    public float getRe21() {
        return re21;
    }

    public void setRe21(float re21) {
        this.re21 = re21;
    }

    public float getIm21() {
        return im21;
    }

    public void setIm21(float im21) {
        this.im21 = im21;
    }

    public float getRe22() {
        return re22;
    }

    public void setRe22(float re22) {
        this.re22 = re22;
    }

    public float getIm22() {
        return im22;
    }

    public void setIm22(float im22) {
        this.im22 = im22;
    }

    public float getRe23() {
        return re23;
    }

    public void setRe23(float re23) {
        this.re23 = re23;
    }

    public float getIm23() {
        return im23;
    }

    public void setIm23(float im23) {
        this.im23 = im23;
    }
}
