package com.ventropic.rfc.proto.message.json;

import com.ventropic.care.proto.message.GetCareInvlStoreJson;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */

@EqualsAndHashCode(callSuper=false)
public class InquiryDevInfo extends JsonMessageAck {

    /**
     * 返回消息体
     */
    private AckData data;

    
    public static class AckData {
        private Integer recordVersion;
    }

    /**
     * 覆写该方法是为了能过够打印出父类的属性值
     * @return
     */
    @Override
    public String toString() {
        return "InquiryDevInfo{" +
                "seqId=" + super.getSeqId() +
                ", msgId='" + super.getMsgId() + '\'' +
                ", data=" + data +
                '}';
    }
}
