package com.ventropic.rfc.proto.message;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
 @NoArgsConstructor
public class CfgDeviceInfoAck implements PlatMessage, Serializable {
    private int reserved;
    private int ret;
    private short numTlv = 4;
    private String mdid;
    private ProductionDate productionDate;
    private String cmei;
    private String companyId;

     @NoArgsConstructor
    public static class ProductionDate {
        private int year;
        private int month;
        private int day;
        private int week;

        public CfgDeviceInfoAck.ProductionDate set(int year, int month, int day, int week) {
            this.year = year;
            this.month = month;
            this.day = day;
            this.week = week;
            return this;
        }

         public int getYear() {
             return year;
         }

         public void setYear(int year) {
             this.year = year;
         }

         public int getMonth() {
             return month;
         }

         public void setMonth(int month) {
             this.month = month;
         }

         public int getDay() {
             return day;
         }

         public void setDay(int day) {
             this.day = day;
         }

         public int getWeek() {
             return week;
         }

         public void setWeek(int week) {
             this.week = week;
         }
     }

    public int getReserved() {
        return reserved;
    }

    public void setReserved(int reserved) {
        this.reserved = reserved;
    }

    public int getRet() {
        return ret;
    }

    public void setRet(int ret) {
        this.ret = ret;
    }

    public short getNumTlv() {
        return numTlv;
    }

    public void setNumTlv(short numTlv) {
        this.numTlv = numTlv;
    }

    public String getMdid() {
        return mdid;
    }

    public void setMdid(String mdid) {
        this.mdid = mdid;
    }

    public ProductionDate getProductionDate() {
        return productionDate;
    }

    public void setProductionDate(ProductionDate productionDate) {
        this.productionDate = productionDate;
    }

    public String getCmei() {
        return cmei;
    }

    public void setCmei(String cmei) {
        this.cmei = cmei;
    }

    public String getCompanyId() {
        return companyId;
    }

    public void setCompanyId(String companyId) {
        this.companyId = companyId;
    }
}
