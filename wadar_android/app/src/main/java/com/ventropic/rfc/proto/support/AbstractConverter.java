package com.ventropic.rfc.proto.support;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;

import com.ventropic.care.proto.message.CareMessage;


public abstract class AbstractConverter implements ProtoConverter {
    private static final int CLOUD_PIONTS_HEADER_BYTES = 5;

    @Override public String convert(byte[] payload, String mdid) throws ConvertingException {
        try {
            Object message = decode(new ByteArrayInputStream(payload));
            return new ConverterResult(message,mdid).toJson();
        }catch (Exception e) {
            throw new ConvertingException(e.toString());
        }
    }

    @Override public Object convertObject(byte[] payload) throws ConvertingException {
        try {
            return decode(new ByteArrayInputStream(payload));
        }catch (Exception e) {
            throw new ConvertingException(e.toString());
        }
    }

    @Override public byte[] convertByte(byte[] payload) throws ConvertingException {
        try {
            int newLength = payload.length - CLOUD_PIONTS_HEADER_BYTES;
            byte[] newPayload = new byte[newLength];
            System.arraycopy(payload, CLOUD_PIONTS_HEADER_BYTES, newPayload,0, newLength);

//            // 测试用转CloudPoints实体类
//            CloudPoints cloudPoints = Codec.decodeCloudPoints(new ByteArrayInputStream(newPayload));
//            log.info("cloudPoints_test:"+cloudPoints.toString());

            return newPayload;
        }catch (Exception e) {
            throw new ConvertingException(e.toString());
        }
    }

    @Override
    public byte[] convert(CareMessage msg) throws ConvertingException {
        try {
            ByteArrayOutputStream output = new ByteArrayOutputStream();
            encode(msg, output);
            return output.toByteArray();
        }catch (Exception e) {
            throw new ConvertingException(e.toString());
        }
    }

    @Override
    public byte[] convert(String json) throws ConvertingException {
        try {
            ConverterResult result = new ConverterResult();
            result.from(json);
            ByteArrayOutputStream output = new ByteArrayOutputStream();
            encode(result.getMsg(), output);
            return output.toByteArray();
        }catch (Exception e) {
            throw new ConvertingException(e.toString());
        }
    }

    protected abstract void encode(Object msg, ByteArrayOutputStream output) throws Exception;
    protected abstract Object decode(InputStream payload) throws IOException;
    protected abstract Class<?> lookupTypeClass(String type) throws ClassNotFoundException;
}
