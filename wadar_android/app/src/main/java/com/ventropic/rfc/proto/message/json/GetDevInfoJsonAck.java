package com.ventropic.rfc.proto.message.json;

import lombok.Data;

/**
 * <AUTHOR>
 */

public class GetDevInfoJsonAck extends JsonMessageAck {
    /**
     * 返回消息体
     */
    private AckData data;

    
    public static class AckData {
        private int careMode;
        private int careHeartbeatInvl;
        
        private Info info;
        private Mqtt mqtt;
        private Wifi wifi;
        private Radar radar;
        private Room room;
        private CareInvl careInvl;
        private Cellular cellular;
    }

    
    public static class Info {
        private String mac;
        private String did;
        private String mdid;
        private String netAccessType;
        private long cmei;
        // 公司id
        private String coId;
        // 系统时间[year, nonth, day, week]
        private int[] manuDate;
        // string for cert version
        private String certVer;
        // 系统版本
        private String appVer;
    }

    
    public static class Mqtt {
        private String url;
        private String username;
        private String password;
    }

    
    public static class Wifi {
        private int rssi;
        private String ssid;
    }

    
    public static class Radar {
        // 雷达版本
        private String fwVer;
        // 雷达参数版本
        private String parmVer;
        private Float[] calibration;
    }
    
    
    public static class Room {
        private int type;
        private Float[] area;
        private Float[] devPos;
        private Float[][] entrys;
        private Float[][] regions;
    }
    
    
    public static class CareInvl {
        private int fallConfInvl;
        private int stayTooLongInvl;
        private int LocaRptInvl;
        private int hbRptInvl;
    }
    
    
    public static class Cellular {
        private String imei;
        private String iccid;
    }
    
    

    /**
     * 覆写该方法是为了能过够打印出父类的属性值
     * @return
     */
    @Override
    public String toString() {
        return "GetDevInfoJsonAck{" +
                "respMsgId='" + super.getRespMsgId() + '\'' +
                ", seqId=" + super.getSeqId() +
                ", respCode=" + super.getRespCode() +
                ", respCont='" + super.getRespCont() + '\'' +
                ", data=" + data +
                '}';
    }
}
