package com.ventropic.rfc.proto.message;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
 @NoArgsConstructor
public class CfgUpdateCertPem implements PlatMessage, Serializable {
    private String certPem;

    public String getCertPem() {
        return certPem;
    }

    public void setCertPem(String certPem) {
        this.certPem = certPem;
    }
}
