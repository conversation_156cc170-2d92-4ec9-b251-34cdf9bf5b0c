package com.ventropic.rfc.proto.support;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;

public class CodecUtil {
    final private ByteOrder order;

    public CodecUtil() {
        this(ByteOrder.LITTLE_ENDIAN);
    }

    public CodecUtil(ByteOrder order) {
        this.order = order;
    }

    public float unpackFloat(InputStream in) throws IOException {
        byte[] raw = new byte[4];
        ByteBuffer buf = ByteBuffer.wrap(raw).order(this.order);
        if (in.read(raw) != 4) throw new IOException("Failed to read float");
        return buf.getFloat();
    }

    public long unpackLongFrom6Bytes(InputStream in) throws IOException {
        byte[] raw = new byte[6];
        ByteBuffer buf = ByteBuffer.wrap(raw).order(this.order);
        if (in.read(raw) != 6) throw new IOException("Failed to read long");
        byte[] newRaw = new byte[8];
        System.arraycopy(raw, 0, newRaw,2, 6);
        ByteBuffer newBuf = ByteBuffer.wrap(newRaw).order(this.order);
        return newBuf.getLong();
    }

    public String unpackStringFrom6Bytes(InputStream in) throws IOException {
        byte[] raw = new byte[6];
        if (in.read(raw) != 6) throw new IOException("Failed to read String");
        StringBuilder stringBuilder = new StringBuilder();
        for (byte b: raw){
            stringBuilder.insert(0, String.format("%06x", b));
        }
        return stringBuilder.toString();
    }

    public String unpackString(InputStream in, int length) throws IOException {
        byte[] raw = new byte[length];
        if (in.read(raw) != length) throw new IOException("Failed to read String");
        return new String(raw);
    }
    /**
     * 去掉0
     */
    public String unpackString_0(InputStream in, int length) throws IOException {
        byte[] raw = new byte[length];
        if (in.read(raw) != length) throw new IOException("Failed to read String");
        return new String(raw).replace("\u0000", "");
    }

    public int unpackUInt32(InputStream in) throws IOException {
        byte[] raw = new byte[4];
        ByteBuffer buf = ByteBuffer.wrap(raw).order(this.order);
        if (in.read(raw) != 4) throw new IOException("Failed to read uint32");
        //IDEA will prompt buf.getInt() is equal to this, which is completely wrong.
        return (int)((long) buf.getInt() & 0xffffffff);
    }

    public long intFromUInt32ToLong(int in) {
        String hax = Integer.toHexString(in);
        return Long.parseLong(hax, 16);
    }

    public int unpackInt32(InputStream in) throws IOException {
        byte[] raw = new byte[4];
        ByteBuffer buf = ByteBuffer.wrap(raw).order(this.order);
        if (in.read(raw) != 4) throw new IOException("Failed to read int32");
        return buf.getInt();
    }

    public short unpackUInt8(InputStream in) throws IOException {
        return (short) (in.read() & 0xff);
    }
    
    /**
     * 读取如：uint8_t res[2]这种数据
     * <AUTHOR>
     * @param in
     * @param len 数组长度
     * @return
     * @throws IOException
     */
    public short[] unpackUInt8Array(InputStream in,int len) throws IOException {
        short[] res = new short[len];
        for (int i=0; i<len; i++){
            res[i] = this.unpackUInt8(in);
        }
        return res;
    }

    public short unpackShort(InputStream in) throws IOException {
        return (short) in.read();
    }

    public short unpackInt8(InputStream in) throws IOException {
        byte[] raw = new byte[1];
        ByteBuffer buf = ByteBuffer.wrap(raw).order(this.order);
        if (in.read(raw) != 1) throw new IOException("Failed to read int8");
        return buf.get();
    }

    public int unpackUInt16(InputStream in) throws IOException {
        byte[] raw = new byte[2];
        ByteBuffer buf = ByteBuffer.wrap(raw).order(this.order);
        if (in.read(raw) != 2) throw new IOException("Failed to read uint16");
        return (buf.getShort() & 0xffff);
    }

    public int unpackInt16(InputStream in) throws IOException {
        byte[] raw = new byte[2];
        ByteBuffer buf = ByteBuffer.wrap(raw).order(this.order);
        if (in.read(raw) != 2) throw new IOException("Failed to read uint16");
        return buf.getShort();
    }

    public long unpackUInt64(InputStream in) throws IOException {
        byte[] raw = new byte[8];
        ByteBuffer buf = ByteBuffer.wrap(raw).order(this.order);
        if (in.read(raw) != 8) throw new IOException("Failed to read uint64");
        return buf.getLong();
    }

    public void packUInt32(OutputStream out, int value) throws IOException{
        ByteBuffer buf = ByteBuffer.allocate(4).order(this.order);
        //IDEA will prompt buf.getInt() is equal to this, which is completely wrong.
        out.write(buf.putInt((int)((long) value & 0xffffffff)).array());
    }

    public void packUInt64(OutputStream out, long value) throws IOException{
        ByteBuffer buf = ByteBuffer.allocate(8).order(this.order);
        out.write(buf.putLong(value & 0xffffffff).array());
    }

    public void packInt32(OutputStream out, int value) throws IOException{
        ByteBuffer buf = ByteBuffer.allocate(4).order(this.order);
        out.write(buf.putInt(value).array());
    }

    public void packFloat(OutputStream out, float value) throws IOException {
        ByteBuffer buf = ByteBuffer.allocate(4).order(this.order);
        buf.putFloat(value);
        out.write(buf.array());
    }

    public void packInt16(OutputStream out, int value) throws IOException {
        ByteBuffer buf = ByteBuffer.allocate(2).order(this.order);
        out.write(buf.putShort((short) value).array());
    }

    public void packUInt16(OutputStream out, int value) throws IOException {
        ByteBuffer buf = ByteBuffer.allocate(2).order(this.order);
        out.write(buf.putShort((short) (value & 0xffff)).array());
    }

    public void packUInt8(OutputStream out, int value) throws IOException {
        out.write((byte) (value & 0xff));
    }

    public void packBoolean(OutputStream out, boolean value) throws IOException {
        int v = value? 1: 0;
        out.write((byte) (v & 0xff));
    }

    public void packString(OutputStream out, String value) throws IOException {
        out.write(value.getBytes());
    }

    public void packLengthString(OutputStream out, int length, String value) throws IOException {
        int sLength = value.length();
        if (sLength <= length){
            this.packString(out, value);
            for (int i=0; i < length-sLength; i++){
                this.packUInt8(out, 0);
            }
        }else {
            throw new IOException("String is too long!");
        }
    }

}
