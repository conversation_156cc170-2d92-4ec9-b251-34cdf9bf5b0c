package com.ventropic.rfc.proto.message.json;

import com.ventropic.care.proto.message.GetCareInvlStoreJson;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper=false)
public class GetCareStatJsonAck extends JsonMessageAck {
    public AckData getData() {
        return data;
    }

    public void setData(AckData data) {
        this.data = data;
    }
    /**
     * 返回消息体
     */
    private AckData data;

    public static class AckData {
        public Integer getMode() {
            return mode;
        }

        public void setMode(Integer mode) {
            this.mode = mode;
        }
        private Integer mode;
    }

    /**
     * 覆写该方法是为了能过够打印出父类的属性值
     * @return
     */
    @Override
    public String toString() {
        return "GetCareStatJsonAck{" +
                "respMsgId='" + super.getRespMsgId() + '\'' +
                ", seqId=" + super.getSeqId() +
                ", respCode=" + super.getRespCode() +
                ", respCont='" + super.getRespCont() + '\'' +
                ", data=" + data +
                '}';
    }
}
