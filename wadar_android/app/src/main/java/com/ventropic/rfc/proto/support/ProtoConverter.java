package com.ventropic.rfc.proto.support;

import com.ventropic.care.proto.message.CareMessage;

public interface ProtoConverter {
    class ConvertingException extends Exception {
        public ConvertingException(String message) {
            super(message);
        }
    }
    String convert(byte[] payload, String mdid) throws ConvertingException;
    Object convertObject(byte[] payload) throws ConvertingException;
    byte[] convertByte(byte[] payload) throws ConvertingException;
    byte[] convert(CareMessage json) throws ConvertingException;
    byte[] convert(String json) throws ConvertingException;
}
