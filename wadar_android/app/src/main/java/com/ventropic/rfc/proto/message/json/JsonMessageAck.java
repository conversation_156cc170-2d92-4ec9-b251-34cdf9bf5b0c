package com.ventropic.rfc.proto.message.json;

import com.ventropic.care.proto.message.CareMessage;
import lombok.Data;

/**
 * <AUTHOR>
 */

public class JsonMessageAck implements CareMessage {
    /**
     * 返回消息类型
     */
    private String respMsgId;
    /**
     * 返回消息唯一标识
     */
    private Integer seqId;
    /**
     * 返回状态码
     */
    private Integer respCode;
    /**
     * 返回提示信息
     */
    private String respCont;
    /**
     * 消息id
     */
    private String msgId;

    public String getRespMsgId() {
        return respMsgId;
    }

    public void setRespMsgId(String respMsgId) {
        this.respMsgId = respMsgId;
    }

    public Integer getSeqId() {
        return seqId;
    }

    public void setSeqId(Integer seqId) {
        this.seqId = seqId;
    }

    public Integer getRespCode() {
        return respCode;
    }

    public void setRespCode(Integer respCode) {
        this.respCode = respCode;
    }

    public String getRespCont() {
        return respCont;
    }

    public void setRespCont(String respCont) {
        this.respCont = respCont;
    }

    public String getMsgId() {
        return msgId;
    }

    public void setMsgId(String msgId) {
        this.msgId = msgId;
    }
}
