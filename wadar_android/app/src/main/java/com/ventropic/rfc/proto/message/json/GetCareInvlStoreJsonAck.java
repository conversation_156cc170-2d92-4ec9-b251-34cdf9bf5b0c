package com.ventropic.rfc.proto.message.json;

import com.ventropic.care.proto.message.GetCareInvlStoreJson;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper=false)
public class GetCareInvlStoreJsonAck extends JsonMessageAck {
    public AckData getData() {
        return data;
    }

    public void setData(AckData data) {
        this.data = data;
    }
    /**
     * 返回消息体
     */
    private AckData data;

    public static class AckData {
        public Integer getFallConfInvl() {
            return fallConfInvl;
        }

        public void setFallConfInvl(Integer fallConfInvl) {
            this.fallConfInvl = fallConfInvl;
        }

        public Integer getStayTooLongInvl() {
            return stayTooLongInvl;
        }

        public void setStayTooLongInvl(Integer stayTooLongInvl) {
            this.stayTooLongInvl = stayTooLongInvl;
        }

        public Integer getLocaRptInvl() {
            return locaRptInvl;
        }

        public void setLocaRptInvl(Integer locaRptInvl) {
            this.locaRptInvl = locaRptInvl;
        }

        public Integer getHbRptInvl() {
            return hbRptInvl;
        }

        public void setHbRptInvl(Integer hbRptInvl) {
            this.hbRptInvl = hbRptInvl;
        }
        private Integer fallConfInvl;
        private Integer stayTooLongInvl;
        private Integer locaRptInvl;
        private Integer hbRptInvl;
    }

    /**
     * 覆写该方法是为了能过够打印出父类的属性值
     * @return
     */
    @Override
    public String toString() {
        return "GetCareInvlStoreJsonAck{" +
                "respMsgId='" + super.getRespMsgId() + '\'' +
                ", seqId=" + super.getSeqId() +
                ", respCode=" + super.getRespCode() +
                ", respCont='" + super.getRespCont() + '\'' +
                ", data=" + data +
                '}';
    }
}
