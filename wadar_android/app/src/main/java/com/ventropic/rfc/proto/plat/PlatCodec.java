package com.ventropic.rfc.proto.plat;

import com.ventropic.rfc.proto.message.*;
import com.ventropic.rfc.proto.support.CodecUtil;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

public class PlatCodec {
    private static final int HEADER_BYTES = 4;
    private static final int ESP_URL_LENGTH = 128;
    private static final int RADAR_VERSION_LENGTH = 32;
    private static final int RADAR_URL_LENGTH = 128;

    public static final CodecUtil helper = new CodecUtil();

    public static int opcode(Message msg) {
        return (msg.mid << 6) + (msg.mtype.ordinal() << 4) + (msg.mdir.ordinal());
    }

    public static Message decode(InputStream input) throws IOException {
        int opcode = helper.unpackUInt16(input);
        int length = helper.unpackUInt16(input);

        byte[] payload = null;
        int payloadLen = length - HEADER_BYTES;

        if (payloadLen > 0) {
            payload = new byte[payloadLen];
            if (input.read(payload) != payloadLen) throw new IOException("Failed to read payload");
        }
        if (input.available() !=0)  throw new IOException("no data should be in the buffer");

        int mid = opcode >> 6;
        int mtype = opcode >> 4 & 0b11;
        int mdir = opcode & 0b1111;
        return new Message(mid, Proto.MsgType.values()[mtype], Proto.MsgDir.values()[mdir], payload);
    }

    public static void encode(Message msg, OutputStream out) throws Exception{
        helper.packUInt16(out,  opcode(msg));
        int payload = msg.payload == null || msg.payload.length == 0 ? 0 : msg.payload.length;
        int amount = HEADER_BYTES + payload;
        helper.packUInt16(out, amount);
        if (payload != 0)
            out.write(msg.payload);
    }

    /**
     * RFC_FACTORY_RESULT 0x12 编码
     * @param msg
     * @param out
     * @throws IOException
     */
    public static void encodeCfgFactoryResultAck(CfgFactoryResultAck msg, ByteArrayOutputStream out) throws IOException {
        helper.packInt32(out, msg.getAck());
    }

    /**
     * RFC_CALIBRATION_RESULT 0x13 编码
     * @param msg
     * @param out
     * @throws IOException
     */
    public static void encodeCfgCalibrationResultAck(CfgCalibrationResultAck msg, ByteArrayOutputStream out) throws IOException {
        helper.packInt32(out, msg.getAck());
    }

    /**
     * RFC_SET_DEVICEID 0X01 编码
     * @param msg
     * @param out
     * @throws IOException
     */
    public static void encodeCfgDeviceId(CfgDeviceId msg, ByteArrayOutputStream out) throws IOException {
        helper.packUInt32(out, msg.getMagic());
        helper.packUInt32(out, msg.getState());
        String deviceId = msg.getDeviceId();
        if (deviceId != null && deviceId.length() > 0) {
            out.write(deviceId.getBytes());
            out.write(0);
        }
    }

    /**
     * RFC_OTA_ESP 0x100 编码
     * @param msg
     * @param out
     */
    public static void encodeCfgOtaEsp(CfgOtaEsp msg, ByteArrayOutputStream out) throws IOException {
        byte[] digest = msg.getDigest();
        if (digest != null && digest.length > 0) {
            out.write(digest);
        }
        String url = msg.getUrl();
        if (url != null && url.length() > 0) {
            out.write(url.getBytes());
            for (int i =0; i< ESP_URL_LENGTH-url.length(); i++){
                out.write(0);
            }
        }
    }

    /**
     * RFC_REBOOT 0X02 编码
     * @param msg
     * @param out
     * @throws IOException
     */
    public static void encodeCfgReboot(CfgReboot msg, ByteArrayOutputStream out) throws IOException {
        helper.packUInt32(out, msg.getMagic());
    }

    /**
     * RFC_UPDATE_CERT_PEM 0x104 编码
     * @param msg
     * @param out
     * @throws IOException
     */
    public static void encodeCfgUpdateCertPem(CfgUpdateCertPem msg, ByteArrayOutputStream out) throws IOException {
        String certPem = msg.getCertPem();
        if (certPem != null && certPem.length() > 0) {
            out.write(certPem.getBytes());
            out.write(0);
        }
    }

    /**
     * RFC_FIRMWARE_CFG 0x102 编码
     * @param msg
     * @param out
     * @throws IOException
     */
    public static void encodeCfgFirmware(CfgFirmware msg, ByteArrayOutputStream out) throws IOException {
        helper.packInt32(out, msg.getFid());
        String cfg = msg.getCfg();
        if (cfg != null && cfg.length() > 0) {
            out.write(cfg.getBytes());
            out.write(0);
        }
    }

    /**
     * RFC_OTA_RADAR 0x101 编码
     * @param msg
     * @param out
     * @throws IOException
     */
    public static void encodeCfgOtaRadar(CfgOtaRadar msg, ByteArrayOutputStream out) throws IOException {
        helper.packInt32(out, msg.getFid());
        helper.packInt32(out, msg.getLen());
        byte[] md5 = msg.getMd5();
        if (md5 != null && md5.length > 0) {
            out.write(md5);
        }
        String version = msg.getVersion();
        if (version != null && version.length() > 0) {
            out.write(version.getBytes());
            for (int i =0; i< RADAR_VERSION_LENGTH-version.length(); i++){
                out.write(0);
            }
        }
        String url = msg.getUrl();
        if (url != null && url.length() > 0) {
            out.write(url.getBytes());
            for (int i =0; i< RADAR_URL_LENGTH-url.length(); i++){
                out.write(0);
            }
        }
    }

    /**
     * RFC_TRANSFER 0x21 编码
     * @param msg
     * @param out
     * @throws IOException
     */
    public static void encodeCfgTransfer(CfgTransfer msg, ByteArrayOutputStream out) throws IOException {
        helper.packUInt8(out, msg.getStatus());
    }

    /**
     * RFC_REQUEST_DID 0x30
     * @param msg
     * @param out
     * @throws IOException
     */
    public static void encodeCfgRequestDeviceIdAck(CfgRequestDeviceIdAck msg, ByteArrayOutputStream out) throws IOException {
        helper.packInt32(out, msg.getAck());
    }

    /**
     * RFC_GET_DEVICE_INFO 0x31 编码
     * @param msg
     * @param out
     * @throws IOException
     */
    public static void encodeCfgDeviceInfoAck(CfgDeviceInfoAck msg, ByteArrayOutputStream out) throws IOException {
        helper.packUInt32(out, msg.getReserved());
        helper.packInt32(out, msg.getRet());
        // 增加内容协议数量时，该值要一起变动
        helper.packUInt8(out, msg.getNumTlv());
        // mdid
        helper.packUInt8(out, 0);
        helper.packUInt8(out, 0x01);
        if (null != msg.getMdid()) {
            helper.packUInt16(out, 17);
            helper.packLengthString(out, 17, msg.getMdid());
        }else {
            helper.packUInt16(out, 0);
        }
        // mqttCfg
        helper.packUInt8(out, 0);
        helper.packUInt8(out, 0x03);
        if (null != msg.getCompanyId()) {
            helper.packUInt16(out, 20);
            helper.packLengthString(out, 20, msg.getCompanyId());
        }else {
            helper.packUInt16(out, 0);
        }
        // manu date
        helper.packUInt8(out, 0);
        helper.packUInt8(out, 0x31);
        if (null != msg.getProductionDate()) {
            helper.packUInt16(out, 4);
            CfgDeviceInfoAck.ProductionDate productionDate = msg.getProductionDate();
            helper.packUInt8(out, productionDate.getYear());
            helper.packUInt8(out, productionDate.getMonth());
            helper.packUInt8(out, productionDate.getDay());
            helper.packUInt8(out, productionDate.getWeek());
        }else {
            helper.packUInt16(out, 0);
        }
        // cmei
        helper.packUInt8(out, 0);
        helper.packUInt8(out, 0x41);
        if (null != msg.getCmei()){
            helper.packUInt16(out, 8);
            helper.packUInt64(out, Long.parseLong(msg.getCmei()));
        }else {
            helper.packUInt16(out, 0);
        }
    }

    /**
     * RFC_GET_TIMESTAMP 0X04 编码
     * @param msg
     * @param out
     * @throws IOException
     */
    public static void encodeCfgTimestampAck(CfgTimestampAck msg, ByteArrayOutputStream out) throws IOException {
        helper.packUInt64(out, msg.getTs());
    }

    /**
     * RFC_RESET_CONFIG 0X05 编码
     * @param msg
     * @param out
     * @throws IOException
     */
    public static void encodeCfgResetConfig(CfgResetConfig msg, ByteArrayOutputStream out) throws IOException {
        helper.packUInt32(out, msg.getMagic());
    }
}
