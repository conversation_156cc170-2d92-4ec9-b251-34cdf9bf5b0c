package com.ventropic.rfc.proto.plat;

import java.util.Arrays;

public class Message {
    public int mid;
    public Proto.MsgType mtype;
    public Proto.MsgDir mdir;
    public byte[] payload;

    public Message(){
        this(Proto.MsgId.RFC_APP_MSG, Proto.MsgType.COMMAND, Proto.MsgDir.S2D, null);
    }

    public Message(int mid, Proto.MsgType mtype, Proto.MsgDir mdir, byte[] payload) {
        this.mid = mid;
        this.mtype = mtype;
        this.mdir = mdir;
        this.payload = payload;
    }

    @Override
    public String toString() {
        return "Message{" +
                "mid=" + mid +
                ", mtype=" + mtype +
                ", mdir=" + mdir +
                ", payload=" + Arrays.toString(payload) +
                '}';
    }
}



