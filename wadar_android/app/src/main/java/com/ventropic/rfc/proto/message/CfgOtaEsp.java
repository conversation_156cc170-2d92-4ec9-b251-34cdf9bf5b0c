package com.ventropic.rfc.proto.message;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
 @NoArgsConstructor
public class CfgOtaEsp implements PlatMessage, Serializable {
    private byte[] digest;
    private String url;

    public byte[] getDigest() {
        return digest;
    }

    public void setDigest(byte[] digest) {
        this.digest = digest;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }
}
