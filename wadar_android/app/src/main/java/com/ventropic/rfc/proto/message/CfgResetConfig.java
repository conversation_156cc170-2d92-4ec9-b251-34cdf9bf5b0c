package com.ventropic.rfc.proto.message;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Class: CfgResetConfig
 * @Package: com.ventropic.care.proto.message
 * @Author: fan
 * @Date: 2022/3/15 11:33
 * @Description:
 */

@NoArgsConstructor
public class CfgResetConfig implements PlatMessage {
    private int magic = 0x52535400;

    public int getMagic() {
        return magic;
    }

    public void setMagic(int magic) {
        this.magic = magic;
    }
}
