package com.ventropic.rfc.proto.plat;

public interface Proto {
    // 发送方向
    public enum MsgDir {
        INVALID, S2D, D2S, B2D, D2B, D2D;
    }
    // 消息类型
    public enum MsgType {
        COMMAND, ACK, RES1, RES2;
    }

    public final class MsgId {
        static final public int RFC_HANDSHAKE = 0;
        static final public int RFC_SET_DEVICEID = 0X01;
        static final public int RFC_REBOOT = 0X02;
        static final public int RFC_GET_TIMESTAMP = 0X04;
        static final public int RFC_RESET_CONFIG = 0X05;
        static final public int RFC_APP_MSG = 0x10;
        static final public int RFC_LOG = 0x11;
        static final public int RFC_FACTORY_RESULT = 0x12;
        static final public int RFC_CALIBRATION_RESULT = 0x13;
        static final public int RFC_SWITCH_MODE = 0x20;
        static final public int RFC_TRANSFER = 0x21;
        static final public int RFC_INQUIRY = 0x22;
        static final public int RFC_NOTICE = 0x28;
        static final public int RFC_INQUIRE_RADAR_TABLE = 0x25;
        static final public int RFC_REQUEST_DID = 0x30;
        static final public int RFC_GET_DEVICE_INFO = 0x31;

        static final public int RFC_OTA_ESP = 0x100;
        static final public int RFC_OTA_RADAR = 0x101;
        static final public int RFC_FIRMWARE_CFG = 0x102;
        static final public int RFC_UPDATE_CERT_PEM = 0x104;
        static final public int RFC_MSG_MAX = 0x3ff;

        static final public int CARE_PEOPLE_FALLDOWN = 0x22;
        static final public int CARE_PEOPLE_STAYING_TOO_LONG = 0x23;
        static final public int CARE_PEOPLE_IO = 0x24;
    }
}
