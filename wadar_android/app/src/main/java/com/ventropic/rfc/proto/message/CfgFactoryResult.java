package com.ventropic.rfc.proto.message;

import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@NoArgsConstructor
public class CfgFactoryResult implements PlatMessage, Serializable {

    private int batch;
    private long deviceTime;
    private String controlTopic;
    private long deviceMac;
    private short result;
    private short deviceKey;
    private short radarUart1;
    private short radarUart2;
    private short radarSpi;
    private short radarFastBoot;
    private short espWifi;
    private short espFlash;
    private short espSram;
    private short sensorLight;
    private short sensorAxis6;
    private short sensorAxis3;
    private short sensorTmpr;
    
    private short iot4g;
    private short simCard;
    private String res;
    private String boardVersion;
    private String productionBatch;
    private String chipType;

    public int getBatch() {
        return batch;
    }

    public void setBatch(int batch) {
        this.batch = batch;
    }

    public long getDeviceTime() {
        return deviceTime;
    }

    public void setDeviceTime(long deviceTime) {
        this.deviceTime = deviceTime;
    }

    public String getControlTopic() {
        return controlTopic;
    }

    public void setControlTopic(String controlTopic) {
        this.controlTopic = controlTopic;
    }

    public long getDeviceMac() {
        return deviceMac;
    }

    public void setDeviceMac(long deviceMac) {
        this.deviceMac = deviceMac;
    }

    public short getResult() {
        return result;
    }

    public void setResult(short result) {
        this.result = result;
    }

    public short getDeviceKey() {
        return deviceKey;
    }

    public void setDeviceKey(short deviceKey) {
        this.deviceKey = deviceKey;
    }

    public short getRadarUart1() {
        return radarUart1;
    }

    public void setRadarUart1(short radarUart1) {
        this.radarUart1 = radarUart1;
    }

    public short getRadarUart2() {
        return radarUart2;
    }

    public void setRadarUart2(short radarUart2) {
        this.radarUart2 = radarUart2;
    }

    public short getRadarSpi() {
        return radarSpi;
    }

    public void setRadarSpi(short radarSpi) {
        this.radarSpi = radarSpi;
    }

    public short getRadarFastBoot() {
        return radarFastBoot;
    }

    public void setRadarFastBoot(short radarFastBoot) {
        this.radarFastBoot = radarFastBoot;
    }

    public short getEspWifi() {
        return espWifi;
    }

    public void setEspWifi(short espWifi) {
        this.espWifi = espWifi;
    }

    public short getEspFlash() {
        return espFlash;
    }

    public void setEspFlash(short espFlash) {
        this.espFlash = espFlash;
    }

    public short getEspSram() {
        return espSram;
    }

    public void setEspSram(short espSram) {
        this.espSram = espSram;
    }

    public short getSensorLight() {
        return sensorLight;
    }

    public void setSensorLight(short sensorLight) {
        this.sensorLight = sensorLight;
    }

    public short getSensorAxis6() {
        return sensorAxis6;
    }

    public void setSensorAxis6(short sensorAxis6) {
        this.sensorAxis6 = sensorAxis6;
    }

    public short getSensorAxis3() {
        return sensorAxis3;
    }

    public void setSensorAxis3(short sensorAxis3) {
        this.sensorAxis3 = sensorAxis3;
    }

    public short getSensorTmpr() {
        return sensorTmpr;
    }

    public void setSensorTmpr(short sensorTmpr) {
        this.sensorTmpr = sensorTmpr;
    }

    public short getIot4g() {
        return iot4g;
    }

    public void setIot4g(short iot4g) {
        this.iot4g = iot4g;
    }

    public short getSimCard() {
        return simCard;
    }

    public void setSimCard(short simCard) {
        this.simCard = simCard;
    }

    public String getRes() {
        return res;
    }

    public void setRes(String res) {
        this.res = res;
    }

    public String getBoardVersion() {
        return boardVersion;
    }

    public void setBoardVersion(String boardVersion) {
        this.boardVersion = boardVersion;
    }

    public String getProductionBatch() {
        return productionBatch;
    }

    public void setProductionBatch(String productionBatch) {
        this.productionBatch = productionBatch;
    }

    public String getChipType() {
        return chipType;
    }

    public void setChipType(String chipType) {
        this.chipType = chipType;
    }
}
