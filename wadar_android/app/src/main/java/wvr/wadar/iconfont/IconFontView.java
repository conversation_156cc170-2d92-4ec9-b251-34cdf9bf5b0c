package wvr.wadar.iconfont;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Color;
import android.util.AttributeSet;
import android.util.TypedValue;

import androidx.appcompat.widget.AppCompatTextView;

import wvr.wadar.R;

public class IconFontView extends AppCompatTextView {
    public IconFontView(Context context) {
        super(context);
        init(context, null);
    }

    public IconFontView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context, attrs);
    }

    public IconFontView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs);
    }

    private void init(Context context, AttributeSet attrs) {
        // 设置默认字体
        setTypeface(IconFontManager.getTypeface());

        // 处理自定义属性
        if (attrs != null) {
            TypedArray a = context.obtainStyledAttributes(attrs, R.styleable.IconFontView);

            String iconCode = a.getString(R.styleable.IconFontView_iconCode);
            if (iconCode != null) {
                setIconCode(iconCode);
            }

            int iconSize = a.getDimensionPixelSize(R.styleable.IconFontView_iconSize, -1);
            if (iconSize != -1) {
                setIconSize(iconSize);
            }

            int iconColor = a.getColor(R.styleable.IconFontView_iconColor, Color.BLACK);
            setTextColor(iconColor);

            a.recycle();
        }
    }

    public void setIconCode(String code) {
        setText(code);
    }

    public void setIconSize(float sizeInPx) {
        setTextSize(TypedValue.COMPLEX_UNIT_PX, sizeInPx);
    }
    public void setIconColor(int iconColor) {
        setTextColor(iconColor);
    }
}
