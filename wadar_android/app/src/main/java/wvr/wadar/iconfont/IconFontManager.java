package wvr.wadar.iconfont;

import android.content.Context;
import android.graphics.Typeface;

public class IconFontManager {
    private static Typeface iconTypeface;

    public static void init(Context context, String fontPath) {
        if (iconTypeface == null) {
            iconTypeface = Typeface.createFromAsset(context.getAssets(), fontPath);
        }
    }

    public static Typeface getTypeface() {
        if (iconTypeface == null) {
            throw new IllegalStateException("IconFont not initialized! Call IconFontManager.init() first.");
        }
        return iconTypeface;
    }
}
