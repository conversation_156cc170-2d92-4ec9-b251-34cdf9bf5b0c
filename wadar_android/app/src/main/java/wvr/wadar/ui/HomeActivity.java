package wvr.wadar.ui;

import android.Manifest;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Bundle;
import android.util.Log;
import android.view.Menu;
import android.view.MenuItem;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.okcis.siplibrary.core.LinphoneManager;

import wvr.wadar.R;
import wvr.wadar.app.BaseActivity;
import wvr.wadar.app.BlufiLog;
import wvr.wadar.constants.SettingsConstants;
import wvr.wadar.databinding.ActivityHomeBinding;
import wvr.wadar.model.MqttConfig;
import wvr.wadar.model.VoIPConfig;
import wvr.wadar.ui.adapter.HistoryAdapter;
import wvr.wadar.utils.PermissionsManager;
import wvr.wadar.utils.SPUtil;

import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

public class HomeActivity extends BaseActivity {
    private final BlufiLog log = new BlufiLog(getClass());
    private ActivityHomeBinding binding;
    private final List<String> historyList = new ArrayList<>();
    private HistoryAdapter adapter;
    private static final int SCAN_REQUEST_CODE = 1001;

    private static final int MENU_SETTINGS = 0x01;
    private static final int MENU_MQTT = 0x02;
    private static final int MENU_VOIP = 0x03;

    PermissionsManager permissionsManager;

    private static final int PERMISSION_REQUEST_CODE = 100;
    private final String[] REQUIRED_PERMISSIONS = {
            Manifest.permission.FOREGROUND_SERVICE,
            Manifest.permission.WAKE_LOCK,
            Manifest.permission.SYSTEM_ALERT_WINDOW
    };

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityHomeBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        setSupportActionBar(binding.toolbar);
        initPermissions();
        //读取mqtt配置
        MqttConfig mqttConfig = SPUtil.getObject(SettingsConstants.MQTT_CONFIG_KEY, MqttConfig.class);
        if (mqttConfig == null) {
            startActivity(new Intent(this, MqttConfigActivity.class));
            return;
        }

        // 读取本地数据
        String history = SPUtil.getString(SettingsConstants.HISTORY_KEY, "");
        if (StringUtils.isNotBlank(history)) {
            String[] historyArray = history.split(",");
            for (String deviceId : historyArray) {
                if (!deviceId.isEmpty()) {
                    historyList.add(deviceId);
                }
            }
        }

        // 初始化RecyclerView
        adapter = new HistoryAdapter(historyList);
        binding.rvHistory.setLayoutManager(new LinearLayoutManager(this));
        binding.rvHistory.setAdapter(adapter);

        // 扫码按钮点击
        binding.btnScan.setOnClickListener(v -> {
            startActivityForResult(
                    new Intent(this, ScanActivity.class),
                    SCAN_REQUEST_CODE
            );
        });

        // 开始配置按钮点击
        binding.btnStartConfig.setOnClickListener(v -> navigateToConfig());

        // 历史记录点击
        adapter.setOnItemClickListener(deviceId -> {
            binding.etDeviceId.setText(deviceId);
            navigateToConfig();
        });

        //权限请求
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            List<String> permissionsToRequest = new ArrayList<>();
            for (String permission : REQUIRED_PERMISSIONS) {
                if (ContextCompat.checkSelfPermission(this, permission) != PackageManager.PERMISSION_GRANTED) {
                    permissionsToRequest.add(permission);
                }
            }
            if (!permissionsToRequest.isEmpty()) {
                ActivityCompat.requestPermissions(
                        this,
                        permissionsToRequest.toArray(new String[0]),
                        PERMISSION_REQUEST_CODE
                );
            }
        }
    }

    private void navigateToConfig() {
        String deviceId = binding.etDeviceId.getText().toString();
        if (!deviceId.isEmpty()) {
            // 保存到历史记录
            if (!historyList.contains(deviceId)) {
                historyList.add(0, deviceId);
                SPUtil.putString(SettingsConstants.HISTORY_KEY, String.join(",", historyList));
                adapter.notifyDataSetChanged();
            }

            // 跳转到配置页
            startActivity(new Intent(this, DeviceInfoActivity.class).putExtra("devCode", deviceId));
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == SCAN_REQUEST_CODE && resultCode == RESULT_OK) {
            String scannedCode = data.getStringExtra("SCAN_RESULT");
            binding.etDeviceId.setText(scannedCode);
            navigateToConfig();
        }
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        // menu.add(Menu.NONE, MENU_SETTINGS, 0, R.string.main_menu_settings);
        menu.add(Menu.NONE, MENU_MQTT, 0, R.string.main_menu_mqtt_config);
        menu.add(Menu.NONE, MENU_VOIP, 1, R.string.voip_configuration);
        return super.onCreateOptionsMenu(menu);
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        final int itemId = item.getItemId();
        if (itemId == MENU_SETTINGS) {
            startActivity(new Intent(this, SettingsActivity.class));
            return true;
        } else if (itemId == MENU_MQTT) {
            startActivity(new Intent(this, MqttConfigActivity.class));
            return true;
        } else if (itemId == MENU_VOIP) {
            startActivity(new Intent(this, VoIPConfigActivity.class));
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == PERMISSION_REQUEST_CODE) {
            for (int i = 0; i < grantResults.length; i++) {
                if (grantResults[i] != PackageManager.PERMISSION_GRANTED) {
                    // 处理权限被拒绝的情况（例如提示用户）
                    Log.e("MainActivity", "权限被拒绝: " + permissions[i]);
                }
            }
        }
        permissionsManager.onRequestPermissionsResult(requestCode,permissions,grantResults);
    }

    @Override
    protected void onResume() {
        super.onResume();
        initLinphone();
    }

    private void initPermissions(){
        permissionsManager = new PermissionsManager(this, permissionsCallback);
        if (permissionsManager.checkPermissions()){
            //已经全部授权
            initLinphone();
        } else {
            permissionsManager.requestPermissions();
        }
    }
    private void initLinphone() {
        log.d("初始化Linphone:");
        VoIPConfig config = SPUtil.getObject(SettingsConstants.VOIP_CONFIG_KEY, VoIPConfig.class);
        if(config == null) {
            log.e("没有读取到VoIP配置信息");
            // startActivity(new Intent(this, VoIPConfigActivity.class));
            return;
        }

        LinphoneManager instance = LinphoneManager.getInstance();
        if(instance.isCoreIsStart()){
            return;
        }

        instance.init(this).start();
        instance.Login(config.getAccount(),config.getPassword(),
                    config.getIp(),config.getPort());
    }

    PermissionsManager.PermissionsCallback permissionsCallback = new PermissionsManager.PermissionsCallback() {
        @Override
        public void onPermissionsGranted() {
            //全部授权
            initLinphone();
        }

        @Override
        public void onPermissionsDenied(List<String> deniedPermissions) {
            // 遍历权限
            for (String permission : deniedPermissions) {
                // 提示用户
                log.e("权限被拒绝: " + permission);
            }
            Toast.makeText(HomeActivity.this,R.string.permission_denied_msg , Toast.LENGTH_LONG).show();
        }
    };
}
