package wvr.wadar.ui;

import android.app.AlertDialog;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.graphics.Color;
import android.os.Bundle;
import android.util.Base64;
import android.view.View;
import android.widget.EditText;
import android.widget.Toast;

import wvr.wadar.R;
import wvr.wadar.app.BaseActivity;
import wvr.wadar.app.BlufiLog;
import wvr.wadar.mqtt.MqttMsgHandler;
import wvr.wadar.mqtt.MyMqttAsyncClient;
import wvr.wadar.constants.SettingsConstants;
import wvr.wadar.databinding.ActivityDeviceInfoBinding;
import wvr.wadar.model.MqttConfig;
import wvr.wadar.model.VoIPConfig;
import wvr.wadar.utils.SPUtil;

import com.google.gson.Gson;
import com.okcis.siplibrary.core.LinphoneManager;
import com.ventropic.care.proto.CareProtoConverter;
import com.ventropic.care.proto.message.CfgTracker;
import com.ventropic.common.json.JsonUtils;
import com.ventropic.rfc.proto.support.ConverterResult;
import com.ventropic.rfc.proto.support.ProtoConverter;


import org.eclipse.paho.client.mqttv3.IMqttActionListener;
import org.eclipse.paho.client.mqttv3.IMqttMessageListener;
import org.eclipse.paho.client.mqttv3.IMqttToken;
import org.eclipse.paho.client.mqttv3.MqttConnectOptions;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.eclipse.paho.client.mqttv3.MqttMessage;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class DeviceInfoActivity extends BaseActivity {
    private final BlufiLog mLog = new BlufiLog(getClass());
    private ActivityDeviceInfoBinding binding;

    private MyMqttAsyncClient mqttClient = null;
    private MqttConfig mqttConfig;
    private VoIPConfig voIPconfig;
    private String devCode;
    private String broker;
    private String subTopic = null;
    private String pubTopic = null;

    private MqttMsgHandler mqttMsgHandler = new MqttMsgHandler();

    private ProtoConverter converter = new CareProtoConverter();

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityDeviceInfoBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        // 显示顶部导航
        setSupportActionBar(binding.toolbar);
        // 显示返回按钮
        setHomeAsUpEnable(true);

        // 读取传过来的数据
        Intent intent = getIntent();
        devCode = intent.getStringExtra("devCode");
        if (devCode == null) {
            mLog.e("DeviceInfoActivity: devCode is null");
            Toast.makeText(this, "Device code is null", Toast.LENGTH_SHORT).show();
            finish();
            return;
        }
        mLog.d("DeviceInfoActivity: " + devCode);


        voIPconfig = SPUtil.getObject(SettingsConstants.VOIP_CONFIG_KEY, VoIPConfig.class);

        initMqttData();
        initMqttClient();

        setupDeviceInfo();
        setupMqttInfo();
        setupVoIPInfo();
//        setupConnectionStatus();
        setupOperations();

        registerBroadcastReceiver();
    }

    private void initMqttData() {
        mqttConfig = SPUtil.getObject(SettingsConstants.MQTT_CONFIG_KEY, MqttConfig.class);
        if (mqttConfig == null) {
            startActivity(new Intent(this, MqttConfigActivity.class));
            return;
        }
        broker = "tcp://" + mqttConfig.getIp() + ":" + mqttConfig.getPort();
        subTopic = "D/" + mqttConfig.getCompanyId() + "/+/" + this.devCode + "/+/event";
        pubTopic = "D/" + mqttConfig.getCompanyId() + "/0/" + this.devCode + "/0/control";
    }

    private void initMqttClient() {
        if (mqttClient != null && mqttClient.isConnected()) {
            subscribe();
            return;
        }
        binding.tvMqttStatus.setText(R.string.disconnected);
        // 读取本地数据回填
        ExecutorService executor = Executors.newSingleThreadExecutor();
        executor.execute(new Runnable() {
            @Override
            public void run() {
                try {
                    mqttClient = new MyMqttAsyncClient(DeviceInfoActivity.this, broker, "wadar_android_" + System.currentTimeMillis(), getOption());
                    mqttClient.conn(() -> {
                        subscribe();
                    });
                } catch (MqttException e) {
                    mLog.e("new MyMqttAsyncClient error" + e);
                }
            }
        });
    }

    private void subscribe() {
        binding.btnSetResponseTime.setEnabled(true);
        binding.btnSetResponseTime.setBackgroundColor(getResources().getColor(R.color.colorPrimary));
        binding.tvMqttStatus.setText(R.string.connected);
        mqttClient.subscribe(subTopic, 0,
                new IMqttActionListener() {
                    @Override
                    public void onSuccess(IMqttToken asyncActionToken) {
                        mLog.d("MQTT所有订阅成功");
                    }

                    @Override
                    public void onFailure(IMqttToken asyncActionToken, Throwable exception) {
                        mLog.e("MQTT订阅失败", exception);
                    }
                },
                //this::handle
                new IMqttMessageListener() {
                    @Override
                    public void messageArrived(String topic, MqttMessage message) throws Exception {
                        handle(topic, message);
                    }
                }
        );
    }

    private void handle(String topic, MqttMessage mqttMessage) {
        mqttMsgHandler.handleMessage(this, topic, mqttMessage);
    }

    private void registerBroadcastReceiver() {
        IntentFilter filter = new IntentFilter("com.okcis.siplibrary.LoginState");
        registerReceiver(myReceiver, filter);
        LinphoneManager.getInstance().getLoginState();
    }

    @Override
    protected void onResume() {
        super.onResume();
        // 重新读取本地数据回填
        initMqttData();
        setupMqttInfo();
        if (mqttClient != null && mqttClient.isConnected()) {
            subscribe();
        }
    }

    private void setupDeviceInfo() {
        binding.tvDeviceCode.setText(devCode);
        // 设置其他设备信息...
    }

    private void setupMqttInfo() {
        binding.tvCompanyId.setText(mqttConfig.getCompanyId());
        binding.tvIp.setText(mqttConfig.getIp());
        binding.tvPort.setText(mqttConfig.getPort());
        binding.tvWss.setText(mqttConfig.getWss());
        binding.tvAccount.setText(mqttConfig.getAccount());
    }

    private void setupVoIPInfo() {
        if (voIPconfig != null) {
            binding.tvSipIp.setText(voIPconfig.getIp());
            binding.tvSipPort.setText(voIPconfig.getPort());
            binding.tvSipAccount.setText(voIPconfig.getAccount());
        }
    }

    /*private void setupConnectionStatus() {
        binding.tvMqttStatus.setText("Connected");
        binding.tvDeviceStatus.setText("Connected");
        // 可以设置颜色等状态指示
    }*/

    private void setupOperations() {

        binding.btnMqttConfig.setOnClickListener(v -> {
            startActivity(new Intent(this, MqttConfigActivity.class));
        });

        binding.btnVoIPConfig.setOnClickListener(v -> {
            startActivity(new Intent(this, VoIPConfigActivity.class));
        });

        binding.btnWifiConfig.setOnClickListener(v -> {
            Intent intent = new Intent(this, MainActivity.class);
            intent.putExtra("devCode", devCode);
            startActivity(intent);
        });
        binding.btnRoomConfig.setOnClickListener(v -> {
            String fullPath = generateHtmlFilePath("room");
            Intent intent = new Intent(this, WebViewActivity.class);
            intent.putExtra("htmlFilePath", fullPath);
            intent.putExtra("pageTitle", "ROOM");
            startActivity(intent);
        });
        binding.btn3dView.setOnClickListener(v -> {
            String fullPath = generateHtmlFilePath("3d");
            Intent intent = new Intent(this, WebViewActivity.class);
            intent.putExtra("htmlFilePath", fullPath);
            intent.putExtra("pageTitle", "3D");
            startActivity(intent);
        });

        binding.btnVoIPCall.setEnabled(false);
        binding.btnVoIPCall.setBackgroundColor(Color.GRAY);
        binding.btnVoIPCall.setOnClickListener(v -> {
            LinphoneManager.getInstance().startCall("1008", false);
        });

        binding.btnSetResponseTime.setEnabled(false);
        binding.btnSetResponseTime.setBackgroundColor(Color.GRAY);
        binding.btnSetResponseTime.setOnClickListener(v -> {
            showCustomDialog();
        });
    }

    private String generateHtmlFilePath(String dirName) {
        String jsonBase64 = getParamBase64();
        // 获取当前系统语言
        String language = getResources().getConfiguration().locale.getLanguage();
        mLog.d("language:" + language);
        String lang = "en";
        // 这里可以根据语言设置不同的html文件路径
        // 如果是中文，则lang="zh"
        if (language.equals("zh")) {
            lang = "zh-Hans";
        }
        String filePath = "file:///android_asset/html/" + dirName + "/index.html";
        String fullPath = filePath + "?dataSrc=device&devCode=" + devCode + "&lang=" + lang + "&mqttConfig=" + jsonBase64;
        mLog.d("fullPath:" + fullPath);
        return fullPath;
    }

    private String getParamBase64() {
        Map<String, String> map = new HashMap<>();
        map.put("companyId", mqttConfig.getCompanyId());
        map.put("wss", mqttConfig.getWss());
        map.put("account", mqttConfig.getAccount());
        map.put("password", mqttConfig.getPassword());

        mLog.d("param:" + new Gson().toJson(map));
        String jsonBase64 = Base64.encodeToString(new Gson().toJson(map).getBytes(), Base64.DEFAULT);
        mLog.d("jsonBase64:" + jsonBase64);
        return jsonBase64;
    }

    BroadcastReceiver myReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (intent.getAction() == "com.okcis.siplibrary.LoginState") {
                int LoginState = intent.getIntExtra("LoginState", -1);
                binding.btnVoIPCall.setEnabled(false);
                binding.btnVoIPCall.setBackgroundColor(Color.GRAY);
                if (LoginState != -1) {
                    if (LoginState == 0) {
                        binding.tvVoipStatus.setText(R.string.string_login0);
                    } else if (LoginState == 1) {
                        binding.tvVoipStatus.setText(R.string.string_login1);
                    } else if (LoginState == 2) {
                        binding.tvVoipStatus.setText(R.string.string_login2);
                        binding.btnVoIPCall.setEnabled(true);
                        binding.btnVoIPCall.setBackgroundColor(getResources().getColor(R.color.colorPrimary));
                    } else if (LoginState == 3) {
                        binding.tvVoipStatus.setText(R.string.string_login3);
                    } else if (LoginState == 4) {
                        binding.tvVoipStatus.setText(R.string.string_login4);
                    } else {
                        binding.tvVoipStatus.setText(R.string.string_login5);
                    }
                }
            }
        }
    };

    @Override
    protected void onDestroy() {
        super.onDestroy();
        unregisterReceiver(myReceiver);
        if (mqttClient != null && mqttClient.isConnected()) {
            try {
                mqttClient.unsubscribe(subTopic);
//            mqttClient.disconnect();
            } catch (MqttException e) {
                throw new RuntimeException(e);
            }
        }
    }

    private MqttConnectOptions getOption() {
        MqttConnectOptions tOptions = new MqttConnectOptions();
        tOptions.setUserName(mqttConfig.getAccount());
        tOptions.setPassword(mqttConfig.getPassword().toCharArray());
        tOptions.setMqttVersion(3);
        tOptions.setCleanSession(true);
        tOptions.setAutomaticReconnect(true);
        //重新连接之间的最长等待时间, 缺省值128000
        tOptions.setMaxInflight(300);
        //设置为0为禁用超时处理，这意味着客户端将等待网络连接成功或失败
        //可防止ERROR o.e.p.c.mqttv3.internal.ClientState - Timed out as no activity 错误
        tOptions.setConnectionTimeout(0);
        tOptions.setKeepAliveInterval(120);
        return tOptions;
    }

    private void showCustomDialog() {
        final AlertDialog.Builder dialogBuilder = new AlertDialog.Builder(this);
        View dialogView = getLayoutInflater().inflate(R.layout.set_response_time, null);
        dialogBuilder.setView(dialogView);

        // 定义对话框中的组件
//        final EditText inputFieldOne = dialogView.findViewById(R.id.input_fall_down);
        final EditText inputFieldTwo = dialogView.findViewById(R.id.input_stay_long);
        dialogBuilder.setTitle(R.string.set_response_time);
        dialogBuilder.setPositiveButton(R.string.save, null); // 先设置为 null，在后面手动绑定点击事件
        dialogBuilder.setNegativeButton(R.string.cancel, (dialog, which) -> dialog.cancel());

        AlertDialog alertDialog = dialogBuilder.create();
        alertDialog.show();

        alertDialog.getButton(AlertDialog.BUTTON_POSITIVE)
            .setOnClickListener(v -> {
                String valueTwo = inputFieldTwo.getText().toString();
                boolean isDataValid = true;

                if (valueTwo.isEmpty()) {
                    inputFieldTwo.setError(getString(R.string.error_response_time_empty));
                    isDataValid = false;
                } else {
                    try {
                        int value = Integer.parseInt(valueTwo);

                        if (value < 0 || value > 30) {
                            inputFieldTwo.setError(getString(R.string.input_value_0_to_30));
                            isDataValid = false;
                        } else if (value % 10 != 0) {
                            //请输入10的倍数
                            inputFieldTwo.setError(getString(R.string.input_multiple_of_10));
                            isDataValid = false;
                        }

                        if (isDataValid) {
                            String mqttData = getJudgeTimeMqttData(value, 0);
                            byte[] payload = null;
                            try {
                                payload = converter.convert(mqttData);
                            } catch (ProtoConverter.ConvertingException e) {
                                throw new RuntimeException(e);
                            }
                            mqttClient.publish(pubTopic, payload);
                            Toast.makeText(this, getString(R.string.config_pending_activation), Toast.LENGTH_LONG).show();
                            alertDialog.dismiss();
                        }
                    } catch (NumberFormatException e) {
                        //请输入有效的数字
                        inputFieldTwo.setError(getString(R.string.input_valid_number));
                        isDataValid = false;
                    }
                }
            });

    }

    // 该方法是往dmp发的格式，往设备发无效
    private String getJudgeTimeMqttData_old(int stayTooLongTime, int fallDownTime) {
        Map<String, Object> data = new HashMap<>();
        data.put("type", "JudgeTimeVO");
        Map<String, Object> msg = new HashMap<>();
        msg.put("stayTooLongTime", stayTooLongTime);
        msg.put("fallDownTime", 0); //目前只有久滞修改 TODO
        data.put("msg", msg);
        return JsonUtils.toJson(data);
    }

    private String getJudgeTimeMqttData(int stayTooLongTime, int fallDownTime) {
        CfgTracker cfgTracker = new CfgTracker();
        cfgTracker.setStayingTooLongThreshold(stayTooLongTime);
        return new ConverterResult(cfgTracker, devCode).toJson();
    }
}