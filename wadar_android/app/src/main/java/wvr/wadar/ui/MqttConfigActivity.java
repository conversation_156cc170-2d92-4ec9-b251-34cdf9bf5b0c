package wvr.wadar.ui;

import android.annotation.SuppressLint;
import android.os.Bundle;

import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;
import androidx.lifecycle.ViewModelProvider;

import wvr.wadar.R;
import wvr.wadar.app.BaseViewModelActivity;
import wvr.wadar.constants.SettingsConstants;
import wvr.wadar.databinding.ActivityMqttConfigBinding;
import wvr.wadar.model.MqttConfig;
import wvr.wadar.utils.SPUtil;
import wvr.wadar.viewmodel.MqttViewModel;


@SuppressLint("MissingPermission")
public class MqttConfigActivity extends BaseViewModelActivity<MqttViewModel> {
    private ActivityMqttConfigBinding mBinding;
    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        // 使用DataBinding初始化布局
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_mqtt_config);
        // 将ViewModel绑定到布局中
        mBinding.setViewModel(viewModel);
        // 绑定生命周期,确保绑定能够感知数据的变化。
        mBinding.setLifecycleOwner(this);

        // 显示顶部导航
        setSupportActionBar(mBinding.toolbar);
        // 显示返回按钮
        setHomeAsUpEnable(true);

        // 读取本地数据回填
        MqttConfig mqttConfig = SPUtil.getObject(SettingsConstants.MQTT_CONFIG_KEY, MqttConfig.class);
        if(mqttConfig != null) {
            viewModel.setMqttConfig(mqttConfig);
        }
    }

    @Override
    protected MqttViewModel createViewModel() {
        return new ViewModelProvider(this).get(MqttViewModel.class);
    }

    @Override
    protected void onResume() {
        super.onResume();
    }

    @Override
    protected void onPause() {
        super.onPause();
    }
}
