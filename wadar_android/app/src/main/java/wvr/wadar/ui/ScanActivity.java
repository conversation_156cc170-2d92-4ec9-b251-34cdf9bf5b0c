package wvr.wadar.ui;

import android.content.Intent;
import android.os.Bundle;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;

import wvr.wadar.R;
import com.google.zxing.integration.android.IntentIntegrator;
import com.google.zxing.integration.android.IntentResult;

/**
 * 扫码界面
 */
public class ScanActivity extends AppCompatActivity {
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        // 无需 setContentView()

        // 直接启动扫码功能
        IntentIntegrator integrator = new IntentIntegrator(this);
        integrator.setPrompt(getString(R.string.scan_qrcode));
        integrator.setBeepEnabled(true); // 开启提示音
        integrator.setOrientationLocked(true); // 锁定竖屏
        integrator.initiateScan();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        IntentResult result = IntentIntegrator.parseActivityResult(requestCode, resultCode, data);
        if (result != null) {
            Intent resultIntent = new Intent();
            if (result.getContents() == null) {
                setResult(RESULT_CANCELED);
            } else {
                resultIntent.putExtra("SCAN_RESULT", result.getContents());
                setResult(RESULT_OK, resultIntent);
            }
            finish();
        } else {
            super.onActivityResult(requestCode, resultCode, data);
        }
    }
}
