package wvr.wadar.ui;

import android.annotation.SuppressLint;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothGatt;
import android.bluetooth.BluetoothGattCallback;
import android.bluetooth.BluetoothGattCharacteristic;
import android.bluetooth.BluetoothGattDescriptor;
import android.bluetooth.BluetoothGattService;
import android.bluetooth.BluetoothProfile;
import android.content.Intent;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;
import android.os.Bundle;
import android.text.TextUtils;
import android.widget.Toast;

import wvr.wadar.R;
import wvr.wadar.app.BaseActivity;
import wvr.wadar.app.BlufiLog;
import wvr.wadar.constants.BlufiConstants;
import wvr.wadar.constants.SettingsConstants;
import wvr.wadar.databinding.ActivityBluetoothBinding;
import wvr.wadar.model.MqttConfig;
import wvr.wadar.utils.SPUtil;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Locale;

import blufi.espressif.BlufiCallback;
import blufi.espressif.BlufiClient;
import blufi.espressif.params.BlufiConfigureParams;
import blufi.espressif.params.BlufiParameter;
import blufi.espressif.response.BlufiScanResult;
import blufi.espressif.response.BlufiStatusResponse;
import blufi.espressif.response.BlufiVersionResponse;


@SuppressLint("MissingPermission")
public class BluetoothActivity extends BaseActivity {
    private final BlufiLog mLog = new BlufiLog(getClass());
    private BluetoothDevice mDevice;
    private BlufiClient mBlufiClient;

    private ActivityBluetoothBinding binding;

    private WifiManager mWifiManager;

    private MqttConfig mqttConfig;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityBluetoothBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        setSupportActionBar(binding.toolbar);
        setHomeAsUpEnable(true);

        mDevice = getIntent().getParcelableExtra(BlufiConstants.KEY_BLE_DEVICE);
        assert mDevice != null;
        String deviceName = mDevice.getName() == null ? getString(R.string.string_unknown) : mDevice.getName();
        setTitle(deviceName);

        binding.deviceId.setText(mDevice.getAddress());
        binding.deviceId.setEnabled(false);

        mWifiManager = (WifiManager) getApplicationContext().getSystemService(WIFI_SERVICE);
        String connectionSSID = getConnectionSSID();
        binding.ssid.setText(connectionSSID);

        // 读取本地数据回填
        mqttConfig = SPUtil.getObject(SettingsConstants.MQTT_CONFIG_KEY, MqttConfig.class);
        if (mqttConfig == null) {
            startActivity(new Intent(this, MqttConfigActivity.class));
            return;
        }

        binding.ip.setText(mqttConfig.getIp());
        binding.port.setText(String.valueOf(mqttConfig.getPort()));
        binding.port.setEnabled(false);


        connect();
        binding.configureButton.setOnClickListener(v -> configureWifi());
        binding.configureMqtt.setOnClickListener(v -> configureMqtt());
    }

    private void configureWifi() {
        final BlufiConfigureParams params = checkInfo();
        if (params == null) {
            mLog.w("Generate configure params null");
            return;
        }
        mBlufiClient.configure(params);
        Toast.makeText(this, getString(R.string.configure_success), Toast.LENGTH_SHORT).show();
    }

    private void configureMqtt() {
        String ip = binding.ip.getText().toString();
        String port = binding.port.getText().toString();
        postCustomData(ip);
    }
    private void postCustomData(String str) {
        byte[] data = new byte[28];
        // 定义头部数据
        byte[] header = {
                (byte) 0xC3, 0x00, 0x1C, 0x00,
                (byte) 0x99, 0x11, (byte) 0xAA, (byte) 0xEE,
                0x5B, 0x07, 0x00, 0x00
        };

        // 将 header 数据复制到 data 中
        System.arraycopy(header, 0, data, 0, header.length);
        // 将 str字符串转为字节数组并拼接到 data 中
        byte[] ipBytes = str.getBytes(StandardCharsets.UTF_8);
        System.arraycopy(ipBytes, 0, data, header.length, Math.min(ipBytes.length, data.length - header.length));
        mBlufiClient.postCustomData(data);
    }
    @Override
    protected void onStop() {
        super.onStop();
        if (mBlufiClient != null) {
            mBlufiClient.close();
            mBlufiClient = null;
            mLog.i("mBlufiClient close");
        }
    }
    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (mBlufiClient != null) {
            mBlufiClient.close();
            mBlufiClient = null;
            mLog.i("mBlufiClient close");
        }
    }


    private String getConnectionSSID() {
        if (!mWifiManager.isWifiEnabled()) {
            return null;
        }

        WifiInfo wifiInfo = mWifiManager.getConnectionInfo();
        if (wifiInfo == null) {
            return null;
        }

        String ssid = wifiInfo.getSSID();
        if (ssid.startsWith("\"") && ssid.endsWith("\"") && ssid.length() >= 2) {
            ssid = ssid.substring(1, ssid.length() - 1);
        }

        return ssid;
    }

    private BlufiConfigureParams checkInfo() {
        BlufiConfigureParams params = new BlufiConfigureParams();
        params.setOpMode(BlufiParameter.OP_MODE_STA);
        if (checkSta(params)) {
            return params;
        }
        return null;
    }

    private boolean checkSta(BlufiConfigureParams params) {
        String ssid = binding.ssid.getText().toString();
        if (TextUtils.isEmpty(ssid)) {
            binding.ssid.setError(getString(R.string.configure_station_ssid_error));
            return false;
        }
        if (TextUtils.indexOf(ssid, "5G") != -1) {
            binding.ssid.setError("WIFI 5G is not supported");
        }
        byte[] ssidBytes = (byte[]) binding.ssid.getTag();
        params.setStaSSIDBytes(ssidBytes != null ? ssidBytes : ssid.getBytes());
        String password = binding.wifiPassword.getText().toString();
        params.setStaPassword(password);

        /*int freq = -1;
        if (ssid.equals(getConnectionSSID())) {
            freq = getConnectionFrequncy();
        }
        if (freq == -1) {
            for (ScanResult sr : mWifiList) {
                if (ssid.equals(sr.SSID)) {
                    freq = sr.frequency;
                    break;
                }
            }
        }
        if (is5GHz(freq)) {
            mStationForm.stationSsid.setError(getString(R.string.configure_station_wifi_5g_error));
            new AlertDialog.Builder(this)
                    .setMessage(R.string.configure_station_wifi_5g_dialog_message)
                    .setPositiveButton(R.string.configure_station_wifi_5g_dialog_continue, (dialog, which) -> {
                        finishWithParams(params);
                    })
                    .setNegativeButton(R.string.configure_station_wifi_5g_dialog_cancel, null)
                    .show();
            return false;
        }*/

        return true;
    }

    private void connect() {

        if (mBlufiClient != null) {
            mBlufiClient.close();
            mBlufiClient = null;
        }

        mBlufiClient = new BlufiClient(getApplicationContext(), mDevice);
        mBlufiClient.setGattCallback(new GattCallback());
        mBlufiClient.setBlufiCallback(new BlufiCallbackMain());
        mBlufiClient.setGattWriteTimeout(BlufiConstants.GATT_WRITE_TIMEOUT);
        mBlufiClient.connect();
//        mBlufiClient.negotiateSecurity();

    }

    private void onGattConnected() {
        runOnUiThread(() -> {
            mLog.i("Connected to device");
            binding.bluetoothStatusTextView.setText(R.string.connected);
        });
    }

    private void onGattServiceCharacteristicDiscovered() {
        runOnUiThread(() -> {
            mLog.i("Connected to device");
            binding.bluetoothStatusTextView.setText(R.string.connected);
            mBlufiClient.requestDeviceStatus();
        });
    }

    private void onGattDisconnected() {
        runOnUiThread(() -> {
            mLog.i("Disconnected to device");
            binding.bluetoothStatusTextView.setText(R.string.disconnected);
        });
    }

    private class GattCallback extends BluetoothGattCallback {
        @Override
        public void onConnectionStateChange(BluetoothGatt gatt, int status, int newState) {
            String devAddr = gatt.getDevice().getAddress();
            mLog.d(String.format(Locale.ENGLISH, "onConnectionStateChange addr=%s, status=%d, newState=%d",
                    devAddr, status, newState));
            if (status == BluetoothGatt.GATT_SUCCESS) {
                switch (newState) {
                    case BluetoothProfile.STATE_CONNECTED:
                        onGattConnected();
                        mLog.i(String.format("Connected %s", devAddr));
                        break;
                    case BluetoothProfile.STATE_DISCONNECTED:
                        gatt.close();
                        onGattDisconnected();
                        mLog.i(String.format("Disconnected %s", devAddr));
                        break;
                }
            } else {
                gatt.close();
                onGattDisconnected();
                mLog.i(String.format(Locale.ENGLISH, "Disconnect %s, status=%d", devAddr, status));
            }
        }

        @Override
        public void onMtuChanged(BluetoothGatt gatt, int mtu, int status) {
            mLog.d(String.format(Locale.ENGLISH, "onMtuChanged status=%d, mtu=%d", status, mtu));
            if (status == BluetoothGatt.GATT_SUCCESS) {
                mLog.i(String.format(Locale.ENGLISH, "Set mtu complete, mtu=%d ", mtu));
            } else {
                mBlufiClient.setPostPackageLengthLimit(20);
                mLog.i(String.format(Locale.ENGLISH, "Set mtu failed, mtu=%d, status=%d", mtu, status));
            }

            onGattServiceCharacteristicDiscovered();
        }

        @Override
        public void onServicesDiscovered(BluetoothGatt gatt, int status) {
            mLog.d(String.format(Locale.ENGLISH, "onServicesDiscovered status=%d", status));
            if (status != BluetoothGatt.GATT_SUCCESS) {
                gatt.disconnect();
                mLog.i(String.format(Locale.ENGLISH, "Discover services error status %d", status));
            }
        }

        @Override
        public void onDescriptorWrite(BluetoothGatt gatt, BluetoothGattDescriptor descriptor, int status) {
            mLog.d(String.format(Locale.ENGLISH, "onDescriptorWrite status=%d", status));
            if (descriptor.getUuid().equals(BlufiParameter.UUID_NOTIFICATION_DESCRIPTOR) &&
                    descriptor.getCharacteristic().getUuid().equals(BlufiParameter.UUID_NOTIFICATION_CHARACTERISTIC)) {
                String msg = String.format(Locale.ENGLISH, "Set notification enable %s", (status == BluetoothGatt.GATT_SUCCESS ? " complete" : " failed"));
                mLog.i(msg);
            }
        }

        @Override
        public void onCharacteristicWrite(BluetoothGatt gatt, BluetoothGattCharacteristic characteristic, int status) {
            if (status != BluetoothGatt.GATT_SUCCESS) {
                gatt.disconnect();
                mLog.i(String.format(Locale.ENGLISH, "WriteChar error status %d", status));
            }
        }
    }

    private class BlufiCallbackMain extends BlufiCallback {
        @Override
        public void onGattPrepared(
                BlufiClient client,
                BluetoothGatt gatt,
                BluetoothGattService service,
                BluetoothGattCharacteristic writeChar,
                BluetoothGattCharacteristic notifyChar
        ) {
            if (service == null) {
                mLog.w("Discover service failed");
                gatt.disconnect();
                mLog.i("Discover service failed");
                return;
            }
            if (writeChar == null) {
                mLog.w("Get write characteristic failed");
                gatt.disconnect();
                mLog.i("Get write characteristic failed");
                return;
            }
            if (notifyChar == null) {
                mLog.w("Get notification characteristic failed");
                gatt.disconnect();
                mLog.i("Get notification characteristic failed");
                return;
            }

            mLog.i("Discover service and characteristics success");

            int mtu = BlufiConstants.DEFAULT_MTU_LENGTH;
            mLog.d("Request MTU " + mtu);
            boolean requestMtu = gatt.requestMtu(mtu);
            if (!requestMtu) {
                mLog.w("Request mtu failed");
                mLog.i(String.format(Locale.ENGLISH, "Request mtu %d failed", mtu));
                onGattServiceCharacteristicDiscovered();
            }
        }

        @Override
        public void onNegotiateSecurityResult(BlufiClient client, int status) {
            if (status == STATUS_SUCCESS) {
                mLog.i("Negotiate security complete");
            } else {
                mLog.i("Negotiate security failed， code=" + status);
            }
        }

        @Override
        public void onPostConfigureParams(BlufiClient client, int status) {
            if (status == STATUS_SUCCESS) {
                mLog.i("Post configure params complete");
            } else {
                mLog.i("Post configure params failed, code=" + status);
            }
        }

        @Override
        public void onDeviceStatusResponse(BlufiClient client, int status, BlufiStatusResponse response) {
            if (status == STATUS_SUCCESS) {
                mLog.i(String.format("Receive device status response:\n%s", response.generateValidInfo()));
                String staSSID = response.getStaSSID();
                binding.wifiTextView.setText(staSSID);
            } else {
                mLog.i("Device status response error, code=" + status);
            }
        }

        @Override
        public void onDeviceScanResult(BlufiClient client, int status, List<BlufiScanResult> results) {
            if (status == STATUS_SUCCESS) {
                StringBuilder msg = new StringBuilder();
                msg.append("Receive device scan result:\n");
                for (BlufiScanResult scanResult : results) {
                    msg.append(scanResult.toString()).append("\n");
                }
                mLog.i(msg.toString());
            } else {
                mLog.i("Device scan result error, code=" + status);
            }

//            mContent.blufiDeviceScan.setEnabled(mConnected);
        }

        @Override
        public void onDeviceVersionResponse(BlufiClient client, int status, BlufiVersionResponse response) {
            if (status == STATUS_SUCCESS) {
                mLog.i(String.format("Receive device version: %s", response.getVersionString()));
            } else {
                mLog.i("Device version error, code=" + status);
            }

//            mContent.blufiVersion.setEnabled(mConnected);
        }

        @Override
        public void onPostCustomDataResult(BlufiClient client, int status, byte[] data) {
            String dataStr = new String(data);
            String format = "Post data %s %s";
            if (status == STATUS_SUCCESS) {
                mLog.i(String.format(format, dataStr, "complete"));
            } else {
                mLog.i(String.format(format, dataStr, "failed"));
            }
        }

        @Override
        public void onReceiveCustomData(BlufiClient client, int status, byte[] data) {
            if (status == STATUS_SUCCESS) {
                String customStr = new String(data);
                mLog.i(String.format("Receive custom data:\n%s", customStr));
            } else {
                mLog.i("Receive custom data error, code=" + status);
            }
        }

        @Override
        public void onError(BlufiClient client, int errCode) {
            mLog.i(String.format(Locale.ENGLISH, "Receive error code %d", errCode));
            if (errCode == CODE_GATT_WRITE_TIMEOUT) {
                mLog.i("Gatt write timeout");
                client.close();
                onGattDisconnected();
            } else if (errCode == CODE_WIFI_SCAN_FAIL) {
                mLog.i("Scan failed, please retry later");
//                mContent.blufiDeviceScan.setEnabled(true);
            }
        }
    }
}
