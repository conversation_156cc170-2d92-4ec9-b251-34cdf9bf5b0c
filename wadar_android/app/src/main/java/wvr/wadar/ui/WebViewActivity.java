package wvr.wadar.ui;

import android.content.Intent;
import android.os.Bundle;
import android.webkit.WebSettings;
import android.webkit.WebViewClient;

import wvr.wadar.app.BaseActivity;
import wvr.wadar.databinding.ActivityWebviewBinding;

public class WebViewActivity extends BaseActivity {

    private ActivityWebviewBinding binding;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityWebviewBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        String htmlFilePath = null;
        String pageTitle = null;
        // 从Intent中获取HTML文件路径和页面标题
        Intent intent = getIntent();
        if (intent != null) {
            htmlFilePath = intent.getStringExtra("htmlFilePath");
            pageTitle = intent.getStringExtra("pageTitle");
        }

        if (htmlFilePath == null || !htmlFilePath.startsWith("file:///android_asset/")) {
            // 处理无效路径或未提供路径的情况
            finish(); // 或者显示错误消息
            return;
        }

        // 设置Activity标题
        if (pageTitle != null && !pageTitle.isEmpty()) {
            setTitle(pageTitle);
            // 使用默认标题或不设置
            // setTitle("Default Title");
        }

        WebSettings webSettings = binding.webView.getSettings();
        webSettings.setJavaScriptEnabled(true);
        webSettings.setAllowFileAccess(true);

        binding.webView.setWebViewClient(new WebViewClient());
        binding.webView.loadUrl(htmlFilePath);
    }

    @Override
    protected void onDestroy() {
        // 清除WebView缓存
        binding.webView.clearCache(true);
        binding.webView.clearHistory();
        binding.webView.removeAllViews();
        binding.webView.destroy();
        super.onDestroy();
    }

    @Override
    public void onBackPressed() {
        if (binding.webView.canGoBack()) {
            binding.webView.goBack();
        } else {
            super.onBackPressed();
        }
    }
}
