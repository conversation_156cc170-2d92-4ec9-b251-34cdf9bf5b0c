package wvr.wadar.app;

import android.content.Intent;
import android.os.Bundle;
import android.widget.Toast;

import androidx.annotation.Nullable;
import androidx.lifecycle.Observer;

import wvr.wadar.ui.HomeActivity;
import wvr.wadar.viewmodel.BaseViewModel;

/**
 * 用于有表单的 Activity 的基类
 * @param <T> 继承自 BaseViewModel 的 ViewModel 类型
 *                例如:BaseViewModelActivity<MqttViewModel>
 * <AUTHOR>
 */
public abstract class BaseViewModelActivity<T extends BaseViewModel> extends BaseActivity {
    protected T viewModel;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // 初始化 ViewModel
        viewModel = createViewModel();

        if (viewModel != null) {
            // 观察 toastMessage LiveData
            viewModel.getToastMessage().observe(this, new Observer<String>() {
                @Override
                public void onChanged(String message) {
                    if (message != null && !message.isEmpty()) {
                        Toast.makeText(BaseViewModelActivity.this, message, Toast.LENGTH_SHORT).show();
                    }
                }
            });

            // 观察保存完成事件
            viewModel.getSaveComplete().observe(this, isSaved -> {
                if (isSaved != null && isSaved) {
                    // finish(); // 返回前一个页面
                    //重新前往首页
                    Intent intent = new Intent(BaseViewModelActivity.this, HomeActivity.class);
                    // 清除栈中位于当前Activity 之上的所有Activity
                    intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
                    startActivity(intent);
                }
            });
        }
    }

    // 子类需要实现此方法以提供具体的 ViewModel 实例
    protected abstract T createViewModel();
}
