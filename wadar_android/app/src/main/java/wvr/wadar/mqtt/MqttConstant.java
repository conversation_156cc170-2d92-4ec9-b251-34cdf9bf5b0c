package wvr.wadar.mqtt;

public class MqttConstant {
    public static final String SYSTEM_MQTT = "SYSTEM_MQTT";
    public static final String SYSTEM_TCP = "SYSTEM_TCP";
    public static final String MQTT_DEVICE_PREFIX  = "99OH2E5JPD";
    // 占位符，当心的topic中，机构和mdid都为空时，用0代替
    public static final String MQTT_PLACEHOLDER  = "0";
    public static final String MQTT_D_PREFIX  = "D";
    public static final String MQTT_TEMP_DID_PREFIX  = "VE-";
    public static final String MQTT_DEVICE_CLIENTID = MQTT_DEVICE_PREFIX+"_";
    
    public static final String MQTT_EVENT_SUFFIX  = "/event";
    
    
    public static final String FACTORY_RESULT_TOPIC = "FACTORY/RESULT";
    public static final String CALIBRATION_RESULT_TOPIC = "CALIBRATION/RESULT";

    public static final String SUB_HEX = "99OH2E5JPD/+/event";
    public static final String SUB_HEX_STREAM = "99OH2E5JPD/+/data_rx";
    
    // D/{机构id}/{设备类型}/{mdid}/{did}/event
    public static final String SUB_HEX_DEVMSG = MQTT_D_PREFIX+"/+/+/+/+/event";
    // D/{机构id}/{设备类型}/{mdid}/{did}/data_rx
    public static final String SUB_HEX_D_STREAM = MQTT_D_PREFIX+"/+/+/+/+/data_rx";
    // 桥接过来的消息处理方式
    public static final String MQTT_BRIDGE_PREFIX  = "B";
    public static final String SUB_HEX_BRIDGE_DEVMSG = MQTT_BRIDGE_PREFIX+"/#";
    public static final String SUB_HEX_BRIDGE_STREAM = MQTT_BRIDGE_PREFIX+"/#";

    // 对外接口
    public static final String SUB_JSON = "/+/+/control";// /机构ID/设备ID
//    public static final String SUB_5JPD_JSON = "5JPD/monintor/+/control";
    
    public static final String SUB_CONNECTED = "$SYS/brokers/+/clients/+/connected";
    public static final String SUB_DISCONNECTED = "$SYS/brokers/+/clients/+/disconnected";
    // 回放事件数据Topic的前缀
    public static final String REPLAY_TOPIC_PREFIX = "REPLAY/";

}
