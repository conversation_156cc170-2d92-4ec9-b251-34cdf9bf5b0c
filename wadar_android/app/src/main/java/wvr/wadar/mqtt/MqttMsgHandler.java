package wvr.wadar.mqtt;

import android.content.Context;
import android.widget.Toast;

import com.google.gson.Gson;
import com.ventropic.care.proto.CareProtoConverter;
import com.ventropic.care.proto.support.CareHandlerImpl;
import com.ventropic.rfc.proto.plat.Message;
import com.ventropic.rfc.proto.plat.PlatCodec;
import com.ventropic.rfc.proto.plat.Proto;
import com.ventropic.rfc.proto.support.CareHandler;
import com.ventropic.rfc.proto.support.ProtoConverter;

import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.paho.client.mqttv3.MqttMessage;

import java.io.ByteArrayInputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import wvr.wadar.R;
import wvr.wadar.app.BlufiLog;
import wvr.wadar.utils.TopicParserUtil;
import wvr.wadar.vo.TopicInfo;

public class MqttMsgHandler {
    private final BlufiLog log = new BlufiLog(getClass());

    private final CareHandler careHandler = new CareHandlerImpl();
    private final ProtoConverter converter = new CareProtoConverter();

    /**
     * 处理mqtt消息
     * see dmp com.ventropic.mqtt.service.impl.DecodeServiceImpl.handleYuAnEventFromSource(TopicInfo, String, byte[], int, Date)
     * @param channel
     * @param mqttMessage
     */
    public void handleMessage(Context context,String channel, MqttMessage mqttMessage){
        Date receiptTime = new Date();
        byte[] rawMsgs = mqttMessage.getPayload();
        int qos = mqttMessage.getQos();
        log.d("Received: "+channel+",qos:"+qos);
        try {
            List<byte[]> eventRows = this.getEventRow(new ArrayList<>(), rawMsgs, 0);
            for (byte[] rawMsg : eventRows) {
                TopicInfo topicInfo = TopicParserUtil.parseEventTopic(channel);
                if (ObjectUtils.isEmpty(topicInfo)) {
                    log.w("==非法设备：{}"+channel);
                    return;
                }
                String id = topicInfo.getDid();
                if (StringUtils.isBlank(id)) {
                    log.w("==非法设备：{}"+channel);
                    return;
                }
                String mdid = topicInfo.getMdid();
                if (topicInfo.getCompanyId() == null || mdid == null) {
                    log.w("==设备状态异常：{}"+id);
                    return;
                }

                Message msg = PlatCodec.decode(new ByteArrayInputStream(rawMsg));
                // 不是应用层消息不处理
                if (msg.mid != Proto.MsgId.RFC_APP_MSG) {
                    return;
                }

                byte[] cloudPoints = careHandler.cloudPointsHandle(msg.payload);
                // 点云不处理
                if(cloudPoints!= null && cloudPoints.length > 0) {
                    return;
                }

                String json = converter.convert(rawMsg, mdid);
                handleJson(context, topicInfo, json);
            }
        } catch (Exception e) {
            log.e("handling message from source error  "+ channel+e);
        }
    }

    /**
     * 处理json数据
     * see dmp com.ventropic.mqtt.service.impl.AppMsgHandlerServiceImpl.handle(TopicInfo, String)
     * @param topicInfo
     * @param srcJson
     */
    private void handleJson(Context context, TopicInfo topicInfo, final String srcJson){
        // 避免修改此处修改json影响后续逻辑
        String json = new String(srcJson);
        Map map = new Gson().fromJson(json, Map.class);
        String type = (String) map.get("type");
        String mdid = (String) map.get("mdid");
        String msgJson = new Gson().toJson(map.get("msg"));
        log.d("Received Data: "+msgJson);
        Map msg = new Gson().fromJson(msgJson, Map.class);
        boolean show = false;
        if ("CfgTrackerAck".equals(type)) {
            if ("1".equals(msg.get("ack").toString())) {
                log.d("==CfgTrackerAck==1");
                show = true;
            }
        }
        if(show){
            Toast.makeText(context, R.string.config_activated, Toast.LENGTH_LONG).show();
        }
    }

    /**
     * 获取单个消息体数组
     * @param rowMsg
     * @param msg
     * @param index
     * @return
     */
    public List<byte[]> getEventRow(List<byte[]> rowMsg, byte[] msg, int index){
        int length1 = (msg[2]&0xff);
        int length2 = ((msg[3]&0xff)<<8);
        // 获取消息体长度
        int rowLength = length1 + length2;
        // 复制最前面消息体
        byte[] rowByte = new byte[rowLength];
        System.arraycopy(msg, 0 , rowByte, 0, rowLength);
        // 把最前面消息体放入list
        rowMsg.add(rowByte);

        if(msg.length-rowLength>0) {
            // 复制后面的消息体
            byte[] rowBytes = new byte[msg.length-rowLength];
            System.arraycopy(msg, rowLength, rowBytes, 0, msg.length - rowLength);
            // 循环调用，挨个把单个消息体放入list
            getEventRow(rowMsg, rowBytes, index + 1);
        }
        return rowMsg;
    }
}
