package wvr.wadar.mqtt;

import android.content.Context;

import com.ventropic.common.json.JsonUtils;

import org.eclipse.paho.android.service.MqttAndroidClient;
import org.eclipse.paho.client.mqttv3.IMqttActionListener;
import org.eclipse.paho.client.mqttv3.IMqttDeliveryToken;
import org.eclipse.paho.client.mqttv3.IMqttMessageListener;
import org.eclipse.paho.client.mqttv3.IMqttToken;
import org.eclipse.paho.client.mqttv3.MqttCallbackExtended;
import org.eclipse.paho.client.mqttv3.MqttClientPersistence;
import org.eclipse.paho.client.mqttv3.MqttConnectOptions;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.eclipse.paho.client.mqttv3.MqttSecurityException;

import wvr.wadar.app.BlufiLog;

public class MyMqttAsyncClient extends MqttAndroidClient {

    private final BlufiLog log = new BlufiLog(getClass());
    private MqttConnectOptions options;


    public MyMqttAsyncClient(Context context, String serverURI, String clientId, MqttConnectOptions options) throws MqttException {
        super(context, serverURI, clientId);
        this.options = options;
        log.d("create:" + clientId);
    }

    public void conn(MqttConnnectdService mqttService) throws MqttSecurityException, MqttException {
        String clientId = getClientId();
        MqttCallbackExtended callback = extracted(clientId, mqttService);
        log.d("connect:" + clientId);
        this.setCallback(callback);
        this.connect(options);
    }

    public void conn() throws MqttSecurityException, MqttException {
        String clientId = getClientId();
        MqttCallbackExtended callback = extracted(clientId, null);
        log.d("connect:" + clientId);
        this.setCallback(callback);
        this.connect(options);
    }


    @Override
    public IMqttDeliveryToken publish(String topic, byte[] payload, int qos, boolean retained) {
        log.d("Publish:" + topic + " message:" + JsonUtils.toJson(payload) +
                " qos:" + qos + " retained:" + retained);
        IMqttDeliveryToken token = null;
        try {
            if (!this.isConnected()) {
                log.w("mqtt client reconnect:" + getClientId());
                this.conn();
            }
            token = super.publish(topic, payload, qos, retained, null, null);
        } catch (MqttException e) {
            log.e("publish error", e);
        }
        return token;
    }

    public IMqttDeliveryToken publish(String topic, String message, int qos, boolean retained) {
        log.d("Publish:" + topic + " message:" + message +
                " qos:" + qos + " retained:" + retained);
        IMqttDeliveryToken token = null;
        try {
            if (!this.isConnected()) {
                log.w("mqtt client reconnect:" + getClientId());
                this.conn();
            }
            token = super.publish(topic, message.getBytes(), qos, retained);
        } catch (MqttException e) {
            log.e("publish error", e);
        }
        return token;
    }

    public IMqttDeliveryToken publish(String topic, String message) {
        log.d("Publish:" + topic + " message:" + message);
        IMqttDeliveryToken token = null;
        try {
            if (!this.isConnected()) {
                log.w("mqtt client reconnect:" + getClientId());
                this.conn();
            }
            token = super.publish(topic, new MqttMessage(message.getBytes()));
        } catch (MqttException e) {
            log.e("publish error", e);
        }
        return token;
    }

    public IMqttDeliveryToken publish(String topic, byte[] payload) {
        log.d("Publish:" + topic + " payload:" + JsonUtils.toJson(payload));
        IMqttDeliveryToken token = null;
        try {
            if (!this.isConnected()) {
                log.w("mqtt client reconnect:" + getClientId());
                this.conn();
            }
            token = super.publish(topic, new MqttMessage(payload));
        } catch (MqttException e) {
            log.e("publish error", e);
        }
        return token;
    }

    @Override
    public IMqttToken subscribe(String topicFilter, int qos, IMqttMessageListener messageListener) {
        return this.subscribe(new String[]{topicFilter}, new int[]{qos},
                new IMqttMessageListener[]{messageListener});
    }

    @Override
    public IMqttToken subscribe(String[] topicFilter, int qos[], IMqttMessageListener[] messageListener) {
        log.d("subscribe:" + JsonUtils.toJson(topicFilter));
        try {
            return super.subscribe(topicFilter, qos, null, null, messageListener);
        } catch (MqttException e) {
            log.w("subscribe error", e);
        }
        return null;
    }

    public IMqttToken subscribe(String topicFilter, int qos, IMqttActionListener callback, IMqttMessageListener messageListener) {
        log.d("subscribe:" + JsonUtils.toJson(topicFilter));
        try {
            return super.subscribe(topicFilter, qos, null, callback, messageListener);
        } catch (MqttException e) {
            log.w("subscribe error", e);
        }
        return null;
    }

    private MqttCallbackExtended extracted(String clientId, MqttConnnectdService mqttService) {
        return new MqttCallbackExtended() {
            @Override
            public void connectComplete(boolean b, String s) {
                log.d("connect success:" + getClientId());
                if (mqttService != null) {
                    mqttService.connectComplete();
                }
            }

            @Override
            public void connectionLost(Throwable throwable) {
                log.w("MQTT断开连接：" + getClientId() + throwable);
//                try {
//                    super.reconnect();
//                } catch (MqttException e) {
//                    int code = e.getReasonCode();
//                    log.e("重连" + clientId + "失败！ReasonCode:" + code + e);
//                }
            }

            @Override
            public void messageArrived(String s, MqttMessage mqttMessage) throws Exception {

            }

            @Override
            public void deliveryComplete(IMqttDeliveryToken iMqttDeliveryToken) {
            }
        };
    }

    public MyMqttAsyncClient(Context context, String serverURI, String clientId) {
        super(context, serverURI, clientId);
    }

    public MyMqttAsyncClient(Context ctx, String serverURI, String clientId, Ack ackType) {
        super(ctx, serverURI, clientId, ackType);
    }

    public MyMqttAsyncClient(Context ctx, String serverURI, String clientId, MqttClientPersistence persistence) {
        super(ctx, serverURI, clientId, persistence);
    }

    public MyMqttAsyncClient(Context context, String serverURI, String clientId, MqttClientPersistence persistence, Ack ackType) {
        super(context, serverURI, clientId, persistence, ackType);
    }
}
