package wvr.wadar.vo;

import lombok.Data;

@Data
public class TopicInfo {
    private String topic;
    private Boolean isDevice = false;
    /**
     * 是否新的消息，新消息为
     * D/{机构id}/{设备类型}/{mdid}/{did}/event
     * D/{机构id}/{设备类型}/{mdid}/{did}/control
     * D/{机构id}/{设备类型}/{mdid}/{did}/data_rx
     */
    private Boolean isNewMsg = false;
    private String companyId;
    /**
     * 设备类型，DeviceCategoryEnum
     */
    private String category;
    private String did;
    private String mdid;

    public String getTopic() {
        return topic;
    }

    public void setTopic(String topic) {
        this.topic = topic;
    }

    public Boolean getIsDevice() {
        return isDevice;
    }

    public void setIsDevice(Boolean device) {
        isDevice = device;
    }

    public Boolean getIsNewMsg() {
        return isNewMsg;
    }

    public void setIsNewMsg(Boolean newMsg) {
        isNewMsg = newMsg;
    }

    public String getCompanyId() {
        return companyId;
    }

    public void setCompanyId(String companyId) {
        this.companyId = companyId;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getDid() {
        return did;
    }

    public void setDid(String did) {
        this.did = did;
    }

    public String getMdid() {
        return mdid;
    }

    public void setMdid(String mdid) {
        this.mdid = mdid;
    }
}
