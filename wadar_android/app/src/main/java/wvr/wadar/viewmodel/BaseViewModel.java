package wvr.wadar.viewmodel;

import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;

/**
 * ViewModel 的基类,支持显示 Toast 消息和保存操作是否完成的通知
 *
 * <AUTHOR>
 */
public class BaseViewModel extends ViewModel {
    // 定义一个 LiveData 来存储 Toast 消息
    private final MutableLiveData<String> toastMessage = new MutableLiveData<>();
    // 定义一个 LiveData 来存储保存操作是否完成
    private final MutableLiveData<Boolean> saveComplete = new MutableLiveData<>();

    public LiveData<String> getToastMessage() {
        return toastMessage;
    }

    public LiveData<Boolean> getSaveComplete() {
        return saveComplete;
    }

    public void setToastMessage(String message) {
        toastMessage.setValue(message);
    }

    public void setSaveComplete(boolean isSaved) {
        saveComplete.setValue(isSaved);
    }

}
