package wvr.wadar.viewmodel;

import android.util.Log;

import org.apache.commons.lang3.StringUtils;

import java.util.regex.Pattern;

import wvr.wadar.constants.SettingsConstants;
import wvr.wadar.model.VoIPConfig;
import wvr.wadar.utils.SPUtil;

public class VoIPViewModel extends BaseViewModel {


    // 表单数据对象
    public VoIPConfig config = new VoIPConfig();

    // 提交事件
    public void onSubmit() {
        if (validateInput(config)) {
            // 保存配置信息到 SharedPreferences
            SPUtil.putObject(SettingsConstants.VOIP_CONFIG_KEY, config);
            setToastMessage("保存成功");
        }
    }

    private boolean validateInput(VoIPConfig config) {
        if(config == null) {
            setToastMessage("请填写完整配置信息");
            return false;
        }

        Log.d("VoIPViewModel", config.toString());
        String ip = config.getIp();
        String port = config.getPort();
        if (StringUtils.isAnyBlank(ip, port, config.getAccount(), config.getPassword())) {
            setToastMessage("请填写完整配置信息");
            return false;
        }

        if (!Patterns.IP_ADDRESS.matcher(ip).matches()) {
            setToastMessage("IP地址格式不正确");
            return false;
        }

        try {
            int portNumber = Integer.parseInt(port);
            if (portNumber < 1 || portNumber > 65535) {
                setToastMessage("端口号范围应为1-65535");
                return false;
            }
        } catch (NumberFormatException e) {
            setToastMessage("端口号必须为数字");
            return false;
        }
        // 通知 Activity 保存完成
        setSaveComplete(true);
        return true;
    }

    public void setConfig(VoIPConfig config) {
        this.config = config;
    }

    // IP地址正则验证工具类
    public static class Patterns {
        public static final Pattern IP_ADDRESS = Pattern.compile(
                "^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}" +
                        "(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$");
    }
}
