package wvr.wadar.viewmodel;

import android.util.Log;

import wvr.wadar.constants.SettingsConstants;
import wvr.wadar.model.MqttConfig;
import wvr.wadar.utils.SPUtil;

import org.apache.commons.lang3.StringUtils;

import java.util.regex.Pattern;

public class MqttViewModel extends BaseViewModel {


    // 表单数据对象
    public MqttConfig mqttConfig = new MqttConfig();

    // 提交事件
    public void onSubmit() {
        if (validateInput(mqttConfig)) {
            // 保存配置信息到 SharedPreferences
            SPUtil.putObject(SettingsConstants.MQTT_CONFIG_KEY, mqttConfig);
            setToastMessage("保存成功");
        }
    }

    private boolean validateInput(MqttConfig mqttConfig) {
        if(mqttConfig == null) {
            setToastMessage("请填写完整配置信息");
            return false;
        }

        Log.d("MqttViewModel", mqttConfig.toString());
        String ip = mqttConfig.getIp();
        String port = mqttConfig.getPort();
        if (StringUtils.isAnyBlank(mqttConfig.getCompanyId(), ip, port, mqttConfig.getWss(), mqttConfig.getAccount(), mqttConfig.getPassword())) {
            setToastMessage("请填写完整配置信息");
            return false;
        }

        if (!Patterns.IP_ADDRESS.matcher(ip).matches()) {
            setToastMessage("IP地址格式不正确");
            return false;
        }

        try {
            int portNumber = Integer.parseInt(port);
            if (portNumber < 1 || portNumber > 65535) {
                setToastMessage("端口号范围应为1-65535");
                return false;
            }
        } catch (NumberFormatException e) {
            setToastMessage("端口号必须为数字");
            return false;
        }
        // 通知 Activity 保存完成
        setSaveComplete(true);
        return true;
    }

    public void setMqttConfig(MqttConfig mqttConfig) {
        this.mqttConfig = mqttConfig;
    }

    // IP地址正则验证工具类
    public static class Patterns {
        public static final Pattern IP_ADDRESS = Pattern.compile(
                "^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}" +
                        "(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$");
    }
}
