package wvr.wadar.utils;


import org.apache.commons.lang3.StringUtils;

import wvr.wadar.app.BlufiLog;
import wvr.wadar.mqtt.MqttConstant;
import wvr.wadar.vo.TopicInfo;

/** 	 
 * topic解析工具 	  
 * 	 
 * <AUTHOR> 	  
*/
public class TopicParserUtil {
    private static final BlufiLog log = new BlufiLog(TopicParserUtil.class);
    /**
     * 解析：99OH2E5JPD/CDUB5H/event或D/888666/1/YA0927/did/event
     * @param topic
     * @return
     */
    public static TopicInfo parseEventTopic(String topic) {
        TopicInfo info = new TopicInfo();
        if (StringUtils.isBlank(topic)) {
            log.w("topic为空");
            return info;
        }
        info.setTopic(topic);
        String[] parts = topic.split("/");
        if (parts == null || parts.length < 2) {
            log.w("topic非法:" + topic);
            return info;
        }
        String start = parts[0];
        // 99OH2E5JPD
        if (MqttConstant.MQTT_DEVICE_PREFIX.startsWith(start)) {
            info.setDid(parts[1]);
            info.setIsDevice(true);
            info.setIsNewMsg(false);
            return info;
        }

        // D/{机构id}/{设备类型}/{mdid}/{did}/event
        if (MqttConstant.MQTT_D_PREFIX.startsWith(start)) {
            if (parts.length < 6) {
                log.w("新topic非法:" + topic);
                return info;
            }
            info.setCompanyId(MqttConstant.MQTT_PLACEHOLDER.equals(parts[1]) ? null : parts[1]);
            info.setCategory(parts[2]);
            info.setMdid(MqttConstant.MQTT_PLACEHOLDER.equals(parts[3]) ? null : parts[3]);
            info.setDid(parts[4]);
            info.setIsDevice(true);
            info.setIsNewMsg(true);
            return info;
        }

        return info;
    }

    /**
     * 解析/{机构id}/{mdid}/control
     * @param topic
     * @return
     */
    public static TopicInfo parseControlTopic(String topic) {
        TopicInfo info = new TopicInfo();
        if (StringUtils.isBlank(topic)) {
            log.w("topic为空");
            return info;
        }
        info.setTopic(topic);
        String[] parts = topic.split("/");
        if (parts == null || parts.length < 4) {
            log.w("topic非法:" + topic);
            return info;
        }

        info.setCompanyId(parts[1]);
        info.setMdid(parts[2]);
        return info;
    }

}
