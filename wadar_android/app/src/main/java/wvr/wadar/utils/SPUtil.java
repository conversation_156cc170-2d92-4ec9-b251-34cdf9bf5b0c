package wvr.wadar.utils;

import android.content.Context;
import android.content.SharedPreferences;
import android.util.Log;

import com.google.gson.Gson;

/**
 * SharedPreferences工具类<br>
 * 提供了对SharedPreferences的封装，支持链式调用和多文件操作<br>
 * <code>
 * <br>// 初始化，在Application中初始化<br>
 * SPUtil.init(this, "my_preferences"); // 自定义默认文件名<br>
 * <br>// 写入 <br>
 * SPUtil.edit()
 * .putString("username", "JohnDoe")
 * .putInt("score", 100)
 * .apply();<br>
 * <br>// 读取<br>
 * int score = SPUtil.getInt("score", 0, "game_prefs");<br>
 * boolean isDark = SPUtil.getBoolean("dark_mode", false);<br>
 * <br>// 存储对象<br>
 * User user = new User();<br>
 * user.name = "Alice";<br>
 * user.age = 30;<br>
 * SPUtil.putObject("user", user);<br>
 *
 * <br>// 读取对象<br>
 * User retrievedUser = SPUtil.getObject("user", User.class);<br>
 * if (retrievedUser != null) {<br>
 * Log.d("User", "Name: " + retrievedUser.name);<br>
 * }<br>
 * <br>// 删除<br>
 * SPUtil.remove("theme", "app_prefs");<br>
 * SPUtil.clear(); // 清除默认文件
 * </code>
 */
public class SPUtil {

    private static final String TAG = "SPUtil";
    private static SharedPreferences sp;
    private static String DEFAULT_SP_NAME = "app_preferences";
    private static Context context;
    private static Gson gson;

    // --- 初始化方法 ---

    /**
     * 初始化工具类（使用默认 Gson 配置）
     *
     * @param context 应用上下文（必须为 Application 上下文）
     */
    public static void init(Context context) {
        init(context, DEFAULT_SP_NAME, new Gson());
    }

    /**
     * 初始化工具类（自定义默认 SharedPreferences 文件名）
     *
     * @param context     应用上下文
     * @param defaultName 自定义默认文件名
     */
    public static void init(Context context, String defaultName) {
        init(context, defaultName, new Gson());
    }

    /**
     * 初始化工具类（自定义 Gson 实例）
     *
     * @param context     应用上下文
     * @param defaultName 自定义默认文件名
     * @param gson        自定义 Gson 实例（用于对象序列化）
     */
    public static void init(Context context, String defaultName, Gson gson) {
        SPUtil.context = context.getApplicationContext();
        DEFAULT_SP_NAME = defaultName;
        SPUtil.gson = gson;
        sp = context.getSharedPreferences(DEFAULT_SP_NAME, Context.MODE_PRIVATE);
    }

    // 获取指定文件名的 SharedPreferences 实例
    private static SharedPreferences getSharedPreferences(String fileName) {
        if (context == null) {
            throw new IllegalStateException("SPUtil must be initialized first.");
        }
        if (fileName == null || fileName.isEmpty()) {
            return sp;
        } else {
            return context.getSharedPreferences(fileName, Context.MODE_PRIVATE);
        }
    }

    // --- 链式编辑器支持 ---
    public static class Editor {
        private final SharedPreferences.Editor editor;

        private Editor(SharedPreferences.Editor editor) {
            this.editor = editor;
        }

        /**
         * 存储字符串值到 SharedPreferences
         *
         * @param key   键名
         * @param value 字符串值
         * @return 当前编辑器实例（支持链式调用）
         */
        public Editor putString(String key, String value) {
            editor.putString(key, value);
            return this;
        }

        /**
         * 存储整数值到 SharedPreferences
         *
         * @param key   键名
         * @param value 整数值
         * @return 当前编辑器实例（支持链式调用）
         */
        public Editor putInt(String key, int value) {
            editor.putInt(key, value);
            return this;
        }

        /**
         * 存储布尔值到 SharedPreferences
         *
         * @param key   键名
         * @param value 布尔值
         * @return 当前编辑器实例（支持链式调用）
         */
        public Editor putBoolean(String key, boolean value) {
            editor.putBoolean(key, value);
            return this;
        }

        /**
         * 存储长整型值到 SharedPreferences
         *
         * @param key   键名
         * @param value 长整型值
         * @return 当前编辑器实例（支持链式调用）
         */
        public Editor putLong(String key, long value) {
            editor.putLong(key, value);
            return this;
        }

        /**
         * 存储浮点值到 SharedPreferences
         *
         * @param key   键名
         * @param value 浮点值
         * @return 当前编辑器实例（支持链式调用）
         */
        public Editor putFloat(String key, float value) {
            editor.putFloat(key, value);
            return this;
        }

        /**
         * 从 SharedPreferences 中移除指定键的值
         *
         * @param key 键名
         * @return 当前编辑器实例（支持链式调用）
         */
        public Editor remove(String key) {
            editor.remove(key);
            return this;
        }

        /**
         * 异步提交修改（不会阻塞主线程）
         */
        public void apply() {
            editor.apply();
        }

        /**
         * 同步提交修改（会阻塞当前线程）
         *
         * @return 提交是否成功
         */
        public boolean commit() {
            return editor.commit();
        }
    }

    // 获取编辑器实例（支持链式操作）

    /**
     * 获取默认文件的 SharedPreferences 编辑器
     *
     * @return 编辑器实例（支持链式调用）
     */
    public static Editor edit() {
        return new Editor(sp.edit());
    }

    /**
     * 获取指定文件的 SharedPreferences 编辑器
     *
     * @param fileName 文件名（若为 null 或空则使用默认文件）
     * @return 编辑器实例（支持链式调用）
     */
    public static Editor edit(String fileName) {
        return new Editor(getSharedPreferences(fileName).edit());
    }

    // --- 常用操作 ---
    // 获取值

    /**
     * 从默认文件中获取字符串值
     *
     * @param key      键名
     * @param defValue 默认值（键不存在时返回）
     * @return 字符串值或默认值
     */
    public static String getString(String key, String defValue) {
        return sp.getString(key, defValue);
    }

    /**
     * 从指定文件中获取字符串值
     *
     * @param key      键名
     * @param defValue 默认值（键不存在时返回）
     * @param fileName 文件名（若为 null 或空则使用默认文件）
     * @return 字符串值或默认值
     */
    public static String getString(String key, String defValue, String fileName) {
        return getSharedPreferences(fileName).getString(key, defValue);
    }

    /**
     * 从默认文件中获取整数值
     *
     * @param key      键名
     * @param defValue 默认值（键不存在时返回）
     * @return 整数值或默认值
     */
    public static int getInt(String key, int defValue) {
        return sp.getInt(key, defValue);
    }

    /**
     * 从指定文件中获取整数值
     *
     * @param key      键名
     * @param defValue 默认值（键不存在时返回）
     * @param fileName 文件名（若为 null 或空则使用默认文件）
     * @return 整数值或默认值
     */
    public static int getInt(String key, int defValue, String fileName) {
        return getSharedPreferences(fileName).getInt(key, defValue);
    }

    /**
     * 从默认文件中获取布尔值
     *
     * @param key      键名
     * @param defValue 默认值（键不存在时返回）
     * @return 布尔值或默认值
     */
    public static boolean getBoolean(String key, boolean defValue) {
        return sp.getBoolean(key, defValue);
    }

    /**
     * 从指定文件中获取布尔值
     *
     * @param key      键名
     * @param defValue 默认值（键不存在时返回）
     * @param fileName 文件名（若为 null 或空则使用默认文件）
     * @return 布尔值或默认值
     */
    public static boolean getBoolean(String key, boolean defValue, String fileName) {
        return getSharedPreferences(fileName).getBoolean(key, defValue);
    }

    /**
     * 从默认文件中获取长整型值
     *
     * @param key      键名
     * @param defValue 默认值（键不存在时返回）
     * @return 长整型值或默认值
     */
    public static long getLong(String key, long defValue) {
        return sp.getLong(key, defValue);
    }

    /**
     * 从指定文件中获取长整型值
     *
     * @param key      键名
     * @param defValue 默认值（键不存在时返回）
     * @param fileName 文件名（若为 null 或空则使用默认文件）
     * @return 长整型值或默认值
     */
    public static long getLong(String key, long defValue, String fileName) {
        return getSharedPreferences(fileName).getLong(key, defValue);
    }

    /**
     * 从默认文件中获取浮点值
     *
     * @param key      键名
     * @param defValue 默认值（键不存在时返回）
     * @return 浮点值或默认值
     */
    public static float getFloat(String key, float defValue) {
        return sp.getFloat(key, defValue);
    }

    /**
     * 从指定文件中获取浮点值
     *
     * @param key      键名
     * @param defValue 默认值（键不存在时返回）
     * @param fileName 文件名（若为 null 或空则使用默认文件）
     * @return 浮点值或默认值
     */
    public static float getFloat(String key, float defValue, String fileName) {
        return getSharedPreferences(fileName).getFloat(key, defValue);
    }

    // 存储值

    /**
     * 将字符串值存储到默认文件
     *
     * @param key   键名
     * @param value 字符串值
     */
    public static void putString(String key, String value) {
        sp.edit().putString(key, value).apply();
    }

    /**
     * 将字符串值存储到指定文件
     *
     * @param key      键名
     * @param value    字符串值
     * @param fileName 文件名（若为 null 或空则使用默认文件）
     */
    public static void putString(String key, String value, String fileName) {
        getSharedPreferences(fileName).edit().putString(key, value).apply();
    }

    // --- 对象操作方法 ---

    /**
     * 存储对象到默认文件的 SharedPreferences
     *
     * @param key 键名
     * @param obj 要存储的对象（需可序列化）
     */
    public static <T> void putObject(String key, T obj) {
        String json = gson.toJson(obj);
        sp.edit().putString(key, json).apply();
    }

    /**
     * 从默认文件的 SharedPreferences 中读取对象
     *
     * @param key   键名
     * @param clazz 对象类型（如 User.class）
     * @param <T>   对象类型
     * @return 反序列化后的对象，或 null（若键不存在或解析失败）
     */
    public static <T> T getObject(String key, Class<T> clazz) {
        return getObject(key, clazz, null);
    }

    /**
     * 从指定文件的 SharedPreferences 中读取对象
     *
     * @param key      键名
     * @param clazz    对象类型（如 User.class）
     * @param fileName 文件名（若为 null 或空则使用默认文件）
     * @param <T>      对象类型
     * @return 反序列化后的对象，或 null（若键不存在或解析失败）
     */
    public static <T> T getObject(String key, Class<T> clazz, String fileName) {
        SharedPreferences prefs = getSharedPreferences(fileName);
        String json = prefs.getString(key, null);
        if (json == null) return null;
        try {
            return gson.fromJson(json, clazz);
        } catch (Exception e) {
            Log.e(TAG, "Failed to deserialize object for key: " + key, e);
            return null;
        }
    }

    // 删除操作

    /**
     * 从默认文件中移除指定键的值
     *
     * @param key 键名
     */
    public static void remove(String key) {
        sp.edit().remove(key).apply();
    }

    /**
     * 从指定文件中移除指定键的值
     *
     * @param key      键名
     * @param fileName 文件名（若为 null 或空则使用默认文件）
     */
    public static void remove(String key, String fileName) {
        getSharedPreferences(fileName).edit().remove(key).apply();
    }

    // 清除操作

    /**
     * 清除默认文件的所有键值对
     */
    public static void clear() {
        sp.edit().clear().apply();
    }

    /**
     * 清除指定文件的所有键值对
     *
     * @param fileName 文件名（若为 null 或空则使用默认文件）
     */
    public static void clear(String fileName) {
        getSharedPreferences(fileName).edit().clear().apply();
    }

    // 检查是否存在键

    /**
     * 检查默认文件中是否存在指定键
     *
     * @param key 键名
     * @return 是否存在
     */
    public static boolean contains(String key) {
        return sp.contains(key);
    }

    /**
     * 检查指定文件中是否存在指定键
     *
     * @param key      键名
     * @param fileName 文件名（若为 null 或空则使用默认文件）
     * @return 是否存在
     */
    public static boolean contains(String key, String fileName) {
        return getSharedPreferences(fileName).contains(key);
    }
}
