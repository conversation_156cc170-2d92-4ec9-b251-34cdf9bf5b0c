package wvr.wadar.utils;

import android.Manifest;
import android.app.Activity;
import android.content.pm.PackageManager;

import androidx.annotation.NonNull;
import androidx.core.app.ActivityCompat;

import java.util.ArrayList;
import java.util.List;

public class PermissionsManager {

    private String[] permissions = {
            Manifest.permission.INTERNET,
            Manifest.permission.WRITE_EXTERNAL_STORAGE,
            Manifest.permission.READ_EXTERNAL_STORAGE,
            Manifest.permission.READ_PHONE_STATE,
            Manifest.permission.CAMERA,
            Manifest.permission.RECORD_AUDIO
    };
    List<String> permissionsNeeded = new ArrayList<>();
    private static final int REQUEST_PERMISSIONS_CODE = 1;
    private final Activity activity;
    private final PermissionsCallback callback;

    public interface PermissionsCallback {
        void onPermissionsGranted();
        void onPermissionsDenied(List<String> deniedPermissions);
    }

    public PermissionsManager(Activity activity, PermissionsCallback callback) {
        this.activity = activity;
        this.callback = callback;
    }

    public boolean checkPermissions(){
        permissionsNeeded.clear();
        for (String permission : permissions) {
            if (ActivityCompat.checkSelfPermission(activity, permission) != PackageManager.PERMISSION_GRANTED) {
                permissionsNeeded.add(permission);
            }
        }
        if (permissionsNeeded.isEmpty()) {
            return true;
        } else {
            return false;
        }
    }

    public void requestPermissions() {
         ActivityCompat.requestPermissions(activity, permissionsNeeded.toArray(new String[0]), REQUEST_PERMISSIONS_CODE);
    }

    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        if (requestCode == REQUEST_PERMISSIONS_CODE) {
            List<String> deniedPermissions = new ArrayList<>();
            for (int i = 0; i < grantResults.length; i++) {
                if (grantResults[i] != PackageManager.PERMISSION_GRANTED) {
                    deniedPermissions.add(permissions[i]);
                }
            }

            if (deniedPermissions.isEmpty()) {
                callback.onPermissionsGranted();
            } else {
                callback.onPermissionsDenied(deniedPermissions);
            }
        }
    }
}

