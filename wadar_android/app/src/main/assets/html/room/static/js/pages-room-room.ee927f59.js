(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-room-room"],{"05b4":function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("64aa");var o={props:{gutter:{type:[String,Number],default:uni.$u.props.row.gutter},justify:{type:String,default:uni.$u.props.row.justify},align:{type:String,default:uni.$u.props.row.align}}};e.default=o},"09cb":function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;e.default={props:{}}},"0b37":function(t,e,n){t.exports=n.p+"static/iconfont/iconfont.woff"},"0c7c":function(t,e,n){"use strict";n.d(e,"b",(function(){return o})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){}));var o=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{ref:"u-row",staticClass:"u-row",style:[t.rowStyle],on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.clickHandler.apply(void 0,arguments)}}},[t._t("default")],2)},a=[]},"0eaf":function(t,e,n){"use strict";n("6a54");var o=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.OptInOut=void 0;var a=o(n("80b1")),i=o(n("efe5")),r=function(){function t(){(0,a.default)(this,t),this.ts=null,this.tid=null,this.event=null,this.type=null,this.subId=null,this.subType=null}return(0,i.default)(t,[{key:"setTs",value:function(t){this.ts=t}},{key:"setTid",value:function(t){this.tid=t}},{key:"setEvent",value:function(t){this.event=t}},{key:"setType",value:function(t){this.type=t}},{key:"setSubId",value:function(t){this.subId=t}},{key:"setSubType",value:function(t){this.subType=t}}]),t}();e.OptInOut=r},1162:function(t,e,n){"use strict";n("6a54");var o=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.RoomJson=void 0;var a=o(n("80b1")),i=o(n("efe5")),r=function(){function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,r=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null;(0,a.default)(this,t),this.type=e,this.area=n,this.devPos=o,this.entrys=i,this.regions=r}return(0,i.default)(t,[{key:"setType",value:function(t){this.type=t}},{key:"getType",value:function(){return this.type}},{key:"setArea",value:function(t){this.area=t}},{key:"getArea",value:function(){return this.area}},{key:"setDevPos",value:function(t){this.devPos=t}},{key:"getDevPos",value:function(){return this.devPos}},{key:"setEntrys",value:function(t){this.entrys=t}},{key:"getEntrys",value:function(){return this.entrys}},{key:"setRegions",value:function(t){this.regions=t}},{key:"getRegions",value:function(){return this.regions}}]),t}();e.RoomJson=r},1176:function(t,e,n){"use strict";n("6a54");var o=n("3639").default,a=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("5c47"),n("af8f"),n("e838"),n("bf0f"),n("2797"),n("aa9c"),n("fd3c"),n("bd06"),n("dd2b"),n("e966"),n("d4b5"),n("08eb"),n("18f7");var i=a(n("9b1b")),r=a(n("2634")),s=a(n("2fdc")),c=(a(n("3dad")),a(n("dc2d"))),d=a(n("a59f")),u=o(n("0578")),l=n("5864"),f=n("358f"),v=n("1893"),h=n("6e5d"),p=n("1162"),m=n("b2cc"),g=(o(n("cf2a")),o(n("d27f")),o(n("eedb"))),y=window.innerWidth,b=window.innerHeight,x=window.innerWidth;console.log("innerHeight:",b),console.log("window_w:",y);var _={components:{MyPopup:d.default},data:function(){return{roomPadding:40,fontSize1:12,fontSize2:16,width:375,height:375,dragItemId:null,dragGateIndex:null,dragRegionIndex:null,scale:1,stage:{id:"stage",x:0,y:0,width:y,height:x-15,draggable:!1,scaleX:1,scaleY:1},room:{x:40,y:40,w:310,h:310,editing:!1},devCode:void 0,bedImage:null,device:{},roomData:{},regionList:[],gateList:[],pixel2cmRatio:void 0,dict:{spotType:[{code:"1",text:this.$t("device.room-data.spot-type.1")},{code:"2",text:this.$t("device.room-data.spot-type.2")}],direction:[{code:"0",text:this.$t("device.room-data.direction.0")},{code:"1",text:this.$t("device.room-data.direction.1")},{code:"2",text:this.$t("device.room-data.direction.2")},{code:"3",text:this.$t("device.room-data.direction.3")}],bedSize:[{code:"0.9x2",text:"0.9x2m"},{code:"1.2x2",text:"1.2x2m"},{code:"1.35x2",text:"1.35x2m"},{code:"1.5x2",text:"1.5x2m"},{code:"1.8x2",text:"1.8x2m"},{code:"1.8x2.2",text:"1.8x2.2m"},{code:"2x2",text:"2x2m"},{code:"1.2x1.9",text:"1.2x1.9m"},{code:"1.2x1.9",text:"1.2x1.9m"},{code:"0x0",text:this.$t("device.room-data.customize")}],gates:[{code:1,text:this.$t("pages.room.room.room-data.gate.label10")},{code:0,text:this.$t("pages.room.room.room-data.gate.label20")},{code:2,text:this.$t("pages.room.room.room-data.gate.label30")},{code:3,text:this.$t("pages.room.room.room-data.gate.label40")}],cls:[{code:0,text:this.$t("device.room-data.other")},{code:1,text:this.$t("device.room-data.region.2")},{code:2,text:this.$t("device.room-data.region.3")},{code:3,text:this.$t("device.room-data.region.4")}]},showEditRoom:!1,showEditRegion:!1,showPupopType:void 0,addRegionIdx:void 0,selectBedIndex:void 0,selectBedSize:"",tempBedList:[],selectGateIndex:void 0,tempGateList:[],editRegionType:void 0,editRegionTypeName:"",selectSpotType:2,customBed:{},formBed:{},from:void 0,sourcePage:void 0,roomDataFrom3D:void 0,devScene:3,devHeight:void 0,companyId:void 0,mqttConfig:{mqttBroker:"",mqttUsername:"",mqttPassword:"",device:{topic:{event:void 0,control:void 0}}},roomDetailInfo:void 0,isDataFetched:!1,client:void 0}},mounted:function(){var t=document.documentElement.clientHeight;window.addEventListener("resize",(function(e){var n=document.documentElement.clientHeight;console.log("pageHeight:",t),console.log("clientHeight:",n);var o=t-n,a=document.querySelector(".footer-btns");o>0?(a.style.display="none",console.log("输入法弹出，隐藏提交等内容")):(a.style.display="block",console.log("输入法收起，显示提交等内容"))}))},onLoad:function(t){var e=this;return(0,s.default)((0,r.default)().mark((function n(){var o,a,i,s,c,d,u;return(0,r.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:console.log("2d options:",t),o=t.token,a=t.from,t.devCode,i=t.sourcePage,s=t.roomDataFrom3D,c=t.devScene,d=t.spotType,u=t.devHeight,e.from=a,e.sourcePage=i,e.roomDataFrom3D=s,e.devScene=c||3,e.selectSpotType=d,e.devHeight=u,o&&uni.setStorageSync("token",o),console.log("roomDataFrom3D:"+s),e.devCode?uni.setStorageSync("devCode",e.devCode):e.devCode=uni.getStorageSync("devCode"),uni.hideTabBar(),uni.removeStorageSync("roomData");case 13:case"end":return n.stop()}}),n)})))()},onUnload:function(){this.disconnect()},onLaunch:function(){uni.hideTabBar({})},onShow:function(){var t=this;return(0,s.default)((0,r.default)().mark((function e(){return(0,r.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:console.log("onShow"),t.draw();case 2:case"end":return e.stop()}}),e)})))()},created:function(){var t=this.getQueryParams(),e=t.mqttConfig,n=t.companyId,o=t.devCode;if(this.devCode=o,this.companyId=n||void 0,e){console.log("mqttConfig base64:",e);var a=(0,u.decodeBase64ToJson)(e),i=a.wss,r=(a.ip,a.tcpPort,a.account),s=a.password;n&&r&&s&&i?(this.mqttConfig.mqttBroker=i||void 0,this.mqttConfig.mqttUsername=r||void 0,this.mqttConfig.mqttPassword=s||void 0,console.log("this.mqttConfig:",e),this.do_config(),this.draw()):uni.showToast({duration:2e3,title:"缺少mqtt参数",icon:"none"})}else uni.showToast({duration:2e3,title:"缺少mqtt参数",icon:"none"})},methods:{null2val:u.null2val,getQueryParams:function(){for(var t={},e=window.location.hash.split("?")[1]||window.location.search.substring(1),n=e.split("&"),o=0;o<n.length;o++){var a=n[o].split("=");t[decodeURIComponent(a[0])]=decodeURIComponent(a[1]||"")}return t},draw:function(){var t,e,n,o,a,r=this;console.log("roomDetailInfo：",this.roomDetailInfo),this.devScene=(null===(t=this.roomDetailInfo)||void 0===t?void 0:t.devScene)||3;var s=(null===(e=this.roomDetailInfo)||void 0===e?void 0:e.roomVO)||void 0,c=(null===(n=this.roomDetailInfo)||void 0===n?void 0:n.roomSensorVO)||void 0,d=(null===(o=this.roomDetailInfo)||void 0===o?void 0:o.roomGateVOList)||void 0,u=(null===(a=this.roomDetailInfo)||void 0===a?void 0:a.roomRegionVOList)||void 0;if(this.roomDataFrom3D){console.log("roomDataFrom3D:"+this.roomDataFrom3D);var l=JSON.parse(this.roomDataFrom3D),f=l.room,v=l.device;console.log("rData3d:",f),console.log("devData3d:",v),s={x:parseFloat(f.x).toFixed(2),y:parseFloat(f.y).toFixed(2),z:parseFloat(f.z).toFixed(2)},c={x:parseFloat(v.x).toFixed(2),y:parseFloat(v.y).toFixed(2),z:parseFloat(v.z).toFixed(2),spotType:v.spotType}}s?(this.roomData.x=s.x,this.roomData.y=s.y,this.roomData.z=s.z):this.roomData={x:4,y:5,z:2.8},this.room.w=this.m2pi(this.roomData.x),this.room.h=this.m2pi(this.roomData.y),this.room.m_x=this.roomData.x,this.room.m_y=this.roomData.y,this.room.m_z=this.roomData.z,this.room.editing=!1,console.log("this.room",this.room);var h=10;c?(console.log("roomSensorVO:",c),this.devHeight&&(c.z=this.devHeight),this.selectSpotType=c.spotType,h=2==this.selectSpotType?10:20,this.device={m_x:c.x,m_y:c.y,m_z:c.z,x:this.m2pi(c.x)+this.room.x,y:2==this.selectSpotType?this.room.y+this.room.h-h:this.room.y+this.room.h-this.m2pi(c.y)-20,w:20,h:h,editing:!1}):this.device={m_x:3,m_y:.01,m_z:this.devHeight||2,x:this.room.x+this.room.w/2,y:this.room.y+this.room.h-h,w:20,h:h,editing:!1},console.log("device:",this.device),u&&u.length>0&&(this.regionList=[],u.forEach((function(t,e){var n={m_positionX:t.positionX,m_positionY:t.positionY,m_x:t.scaleX,m_y:t.scaleY,m_z:t.scaleZ,x:r.m2pi(t.positionX-t.scaleX/2)+r.room.x,y:r.m2pi(r.pi2m(r.room.y+r.room.h)-t.positionY-t.scaleY/2),w:r.m2pi(t.scaleX),h:r.m2pi(t.scaleY),z:r.m2pi(t.scaleZ),editing:!1,cls:t.cls};r.regionList.push(n),console.log("region:",n)})));var p=[];d&&d.length>0&&(p=d),this.gateList=[],p.forEach((function(t,e){var n=r.gateConfog(t),o=(0,i.default)((0,i.default)({},n),{},{editing:!1});r.gateList.push(o),console.log("gate:",o)}));this.$nextTick((function(){console.log("刷新Room stage")}))},getDeviceRoomDataCmd:function(){var t=l.DeviceHeaderCodec.encode(h.MsgCodeEnum.S2D,v.CmdIdEnum.CARE_JSON,{msgId:"getRoom",seqId:1234});this.client.publish(this.mqttConfig.device.topic.control,t),console.warn("publish getRoom:"+this.mqttConfig.device.topic.control+"\t "+t)},do_config:function(){this.mqttConfig.device.topic.event="D/"+this.companyId+"/+/"+this.devCode+"/+/event",this.mqttConfig.device.topic.control="D/"+this.companyId+"/0/"+this.devCode+"/0/control",console.log("mqttConfig:",this.mqttConfig),this.do_mqtt(this.mqttConfig)},do_mqtt:function(t){var e=this;if(!this.devCode)return console.log("设备id无效"),void uni.showToast({duration:2e3,title:this.$t("pages.room.room.toast.9"),icon:"none"});console.log("准备连接MQTT:",this.mqttConfig),this.client=c.default.connect(this.mqttConfig.mqttBroker,{username:this.mqttConfig.mqttUsername,password:this.mqttConfig.mqttPassword,clientId:"3d_"+this.devCode+"_"+Math.floor(1e3*Math.random())}),this.client.on("connect",(function(n){console.log("连接成功:"+e.mqttConfig.mqttBroker),e.mqttConnected=!0,e.client.subscribe(t.device.topic.event,(function(e){e?console.log("订阅失败"):console.log("订阅成功:"+t.device.topic.event)})),e.isDataFetched||e.getDeviceRoomDataCmd()})),this.client.on("message",(function(t,n){if(n){var o,a=l.DeviceHeaderCodec.decode(n),i=a.opcode,r=(a.msgcode,a.length,a.payload);if(o=f.DeviceContentCodec.decode(i,r),o&&o.type){var s=o.type,c=new Date,d=c.getHours()+":"+c.getMinutes()+":"+c.getSeconds()+"."+c.getMilliseconds();if("getRoom"==s){console.log(d+"-"+s,o.msg),e.isDataFetched=!0;var u=g.parseDevRoomData(o.msg);console.log("设备中房间信息：",u);var v=e.conventDevData2roomPage(u);console.log("转换成房间配置的结构：",v),e.roomDetailInfo=v,e.draw()}else"setRoom"==s&&(console.log(d+"-"+s,o.msg),uni.showToast({duration:2e3,title:e.$t("success"),icon:"none"}))}else console.warn("接收到的消息无效 res:",o)}else console.log(n)}))},conventDevData2roomPage:function(t){if(t){var e={};return e.roomVO=t.room,e.roomSensorVO={x:t.roomDevice.x,y:t.roomDevice.y,z:t.roomDevice.z,etilt:t.roomDevice.etilt,spotType:0==t.roomDevice.mount?1:2},e.roomGateVOList=t.roomEntrances,e.roomRegionVOList=t.roomRegions.map((function(t){return{rid:t.rid,cls:t.cls,positionX:t.positionX,positionY:t.positionY,scaleX:t.scaleX,scaleY:t.scaleY,scaleZ:t.scaleZ,rotation:t.rotation}})),e.type=t.type,e}},showPupop:function(t){switch(console.log("showPupop type:",t),t){case 888:this.showPupopType="selectBed";break;case 999:this.showPupopType="addGate";break;default:var e=this.dict.cls[t];this.showPupopType="addRegion",this.addRegionIdx=e.code,console.log("regionTitle",e.text);break}},popupHide:function(t){var e=this;switch(this.showPupopType=void 0,this.addRegionIdx=void 0,t){case"addRegion":break;case"selectBed":this.selectBedIndex=void 0,this.tempBedList.length>0&&this.tempBedList.forEach((function(t){var n=e.regionList.findIndex((function(e){return e.m_x===t.m_x&&e.m_y===t.m_y&&e.m_z===t.m_z&&e.m_positionX===t.m_positionX&&e.m_positionY===t.m_positionY}));-1!==n&&e.regionList.splice(n,1)}));break;case"addGate":this.selectGateIndex=void 0,this.tempGateList.length>0&&this.tempGateList.forEach((function(t){var n=e.gateList.findIndex((function(e){return e.direction===t.direction&&e.length===t.length&&e.width===t.width&&e.height===t.height}));-1!==n&&e.gateList.splice(n,1)}));break}},bedSelect:function(t,e){this.selectBedIndex=t;var n=this.dict.bedSize[t];if(this.selectBedSize=n.text,"0x0"!==n.code){var o=n.code.split("x"),a={x:60,y:60,w:this.m2pi(o[0]),h:this.m2pi(o[1]),z:this.m2pi(.45),m_x:parseFloat(o[0]),m_y:parseFloat(o[1]),m_z:.45,editing:!1,cls:e};a.m_positionX=this.pi2m(a.x-this.room.x)+a.m_x/2,a.m_positionY=this.pi2m(this.room.y+this.room.h-(a.y+a.h))+a.m_y/2,this.regionList.push(a),this.tempBedList.push(a),console.log("regionSelect:",a)}else this.addRegionIdx=1,this.showPupopType="addRegion";console.log("regionList:",this.regionList)},saveBed:function(){this.showPupopType=void 0,this.selectBedIndex=void 0,this.regionGateList=[]},saveRegion:function(){if(this.customBed.x>this.room.m_x||this.customBed.y>this.room.m_y||this.customBed.z>this.room.m_z)uni.showToast({duration:2e3,title:this.$t("pages.room.room.toast.1"),icon:"none"});else{var t={x:60,y:60,w:this.m2pi(this.customBed.x),h:this.m2pi(this.customBed.y),z:this.m2pi(this.customBed.z),m_x:parseFloat(this.customBed.x),m_y:parseFloat(this.customBed.y),m_z:parseFloat(this.customBed.z),editing:!1,cls:this.addRegionIdx};this.showPupopType=void 0,this.customBed={},t.m_positionX=this.pi2m(t.x-this.room.x)+t.m_x/2,t.m_positionY=this.pi2m(this.room.y+this.room.h-(t.y+t.h))+t.m_y/2,console.log("bed:",t),this.regionList.push(t),this.tempBedList.push(t),console.log("regionSelect:",t),console.log("regionList:",this.regionList)}},deleteBed:function(t){var e=this;uni.showModal({content:this.$t("pages.room.room.toast.2"),showCancel:!0,success:function(n){n.confirm;var o=n.cancel;o||e.regionList.splice(t,1)}})},startBedEditing:function(t){var e=this.pi2m(this.room.x+this.room.w-(t.x+t.w)),n=this.pi2m(this.room.y+this.room.h-(t.y+t.h));t.m_distance1=parseFloat(e),t.m_distance1_old=parseFloat(e),t.m_distance2=parseFloat(n),t.distance2_old=parseFloat(n),t.m_z_old=parseFloat(t.m_z),t.editing=!0,console.log("startBedEditing:",t)},startDeviceEditing:function(t){console.log("device:",t),t.m_x_old=parseFloat(t.m_x),t.m_y_old=parseFloat(t.m_y),t.m_z_old=parseFloat(t.m_z),t.editing=!0,console.log("startDeviceEditing:",t)},startRoomEditing:function(t){t.editing=!0,console.log("startRoomEditing:",t)},calcLeftDistanceOfRoomM:function(t){return this.pi2m(t-this.room.x)},stopBedEditing:function(t){console.log("item:",t),console.log("this.room:",this.room),this.checkRegionOutOfRoom(t)||(t.m_distance1!=t.m_distance1_old&&(t.x=this.room.x+this.room.w-t.w-this.m2pi(t.m_distance1),t.m_positionX=this.pi2m(t.x-this.room.x)+t.m_x/2),t.m_distance2!=t.m_distance2_old&&(t.y=this.room.y+this.room.h-t.h-this.m2pi(t.m_distance2),t.m_positionY=this.pi2m(this.room.y+this.room.h-(t.y+t.h))+t.m_y/2),t.m_z!=t.m_z_old&&(t.z=this.m2pi(t.m_z)),t.editing=!1,console.log("stopBedEditing:",t),uni.showToast({duration:2e3,title:this.$t("pages.room.room.toast.3"),icon:"none"}))},stopDeviceEditing:function(t){console.log("item:",t),console.log("this.room:",this.room),this.checkDeviceOutOfRoom(t)||(t.m_x!=t.m_x_old&&(t.x=this.m2pi(t.m_x)+this.room.x),t.m_y!=t.m_y_old&&(t.y=this.room.y+this.room.h-t.h-this.m2pi(t.m_y)),t.m_z!=t.m_z_old&&(t.z=this.m2pi(t.m_z)),t.editing=!1,console.log("stopBedEditing:",t),uni.showToast({duration:2e3,title:this.$t("pages.room.room.toast.3"),icon:"none"}))},checkRegionOutOfRoom:function(t){return(t.m_distance1<0||t.m_distance1>this.room.m_x||t.m_distance2<0||t.m_distance2>this.room.m_y||t.m_z<0||t.m_z>this.room.m_z)&&(uni.showToast({duration:2e3,title:this.$t("pages.room.room.toast.1"),icon:"none"}),!0)},checkDeviceOutOfRoom:function(t){return(t.x<0||t.m_x>this.room.m_x||t.m_y<0||t.m_y>this.room.m_y||t.m_z<0||t.m_z>this.room.m_z)&&(uni.showToast({duration:2e3,title:this.$t("pages.room.room.toast.1"),icon:"none"}),!0)},stopRoomEditing:function(t){this.room.m_x>10||this.room.m_y>10||this.room.m_z>10?uni.showToast({duration:2e3,title:this.$t("pages.room.room.toast.4"),icon:"none"}):(t.w=parseFloat(this.m2pi(this.room.m_x)),t.h=parseFloat(this.m2pi(this.room.m_y)),t.editing=!1,console.log("stopRoomEditing:",t),uni.showToast({duration:2e3,title:this.$t("pages.room.room.toast.5"),icon:"none"}))},gateSelect:function(t){this.selectGateIndex=t;var e={direction:t,length:1.4,width:.8,height:2,editing:!1},n=this.gateConfog(e);this.gateList.push(n),this.tempGateList.push(n),console.log("gateSelect:",n)},saveGate:function(){this.tempGateList=[],this.showPupopType=void 0,this.selectGateIndex=void 0},deleteGate:function(t){var e=this;uni.showModal({content:this.$t("pages.room.room.toast.2"),showCancel:!0,success:function(n){n.confirm;var o=n.cancel;o||e.gateList.splice(t,1)}})},changeDevScene:function(t){this.devScene=t},spotTypeSelect:function(t){this.selectSpotType=t,2==this.selectSpotType?(this.device.h=10,this.device.y=this.room.y+this.room.h-10,this.device.m_z=2,this.device.m_y=.01):(this.device.h=20,this.device.y=this.device.y-10,this.device.m_z=this.room.m_z)},questionClick:function(t){1==t||(2==t?uni.showToast({duration:3e3,title:this.$t("pages.room.room.toast.6"),icon:"none",position:"bottom"}):3==t&&uni.showToast({duration:3e3,title:this.$t("pages.room.room.toast.7"),icon:"none",position:"bottom"}))},calcHateDistance:function(t){var e=0;switch(t.direction){case 0:e=this.pi2m(this.room.y+this.room.h-(t.y+t.h));break;case 1:e=this.pi2m(t.x-this.room.x);break;case 2:e=this.pi2m(t.y-this.room.y);break;case 3:e=this.pi2m(this.room.x+this.room.w-(t.x+t.w));break}return parseFloat(e)},startGateEditing:function(t){var e=this.calcHateDistance(t);t.m_distance1=parseFloat(e),t.m_distance1_old=parseFloat(e),t.m_width_old=parseFloat(t.m_width),console.log("startGateEditing:",t),t.editing=!0},stopGateEditing:function(t){if(t.m_distance1!=t.m_distance1_old)switch(t.direction){case 0:if(this.checkGateOutOfRoomY(t))return;t.y=this.room.y+this.room.h-t.h-this.m2pi(t.m_distance1);break;case 1:if(this.checkGateOutOfRoomX(t))return;t.x=this.m2pi(t.m_distance1)+this.room.x;break;case 2:if(this.checkGateOutOfRoomY(t))return;t.y=this.m2pi(t.m_distance1)+this.room.y;break;case 3:if(this.checkGateOutOfRoomX(t))return;t.x=this.room.x+this.room.w-t.w-this.m2pi(t.m_distance1);break}t.m_width!=t.m_width_old&&(t.w=this.m2pi(t.m_width)),t.editing=!1,t.m_x=parseFloat(t.m_distance1)+t.m_width/2,console.log("stopGateEditing:",t),uni.showToast({duration:2e3,title:this.$t("pages.room.room.toast.3"),icon:"none"})},checkGateOutOfRoomX:function(t){return(t.m_distance1<0||t.m_distance1>this.room.m_x||t.m_width<0||t.m_width>this.room.m_x)&&(uni.showToast({duration:2e3,title:this.$t("pages.room.room.toast.1"),icon:"none"}),!0)},checkGateOutOfRoomY:function(t){return(t.m_distance1<0||t.m_distance1>this.room.m_y||t.m_width<0||t.m_width>this.room.m_y)&&(uni.showToast({duration:2e3,title:this.$t("pages.room.room.toast.1"),icon:"none"}),!0)},changePixel2cmRatio:function(){var t=this.roomData.x>this.roomData.y?this.roomData.x:this.roomData.y;this.pixel2cmRatio=310/(100*t),console.log("每厘米所占像素："+this.pixel2cmRatio)},m2pi:function(t){if(!t)return 0;this.pixel2cmRatio||this.changePixel2cmRatio();var e=100*parseFloat(t)*this.pixel2cmRatio;return parseFloat(e)},pi2m:function(t){if(!t)return 0;this.pixel2cmRatio||this.changePixel2cmRatio();var e=t/100/this.pixel2cmRatio;return parseFloat(e.toFixed(2))},gateConfog:function(t){var e,n,o,a,i=t.direction,r=t.length,s=t.width,c=t.editing,d=this.m2pi(s);console.log("门宽度：",d);var u=this.m2pi(r);console.log("门离左墙距离：",u);switch(i){case 0:e=this.room.x,n=this.room.y+this.room.h-u-d/2,o=8,a=d;break;case 1:e=this.room.x+u-d/2,n=this.room.y,o=d,a=8;break;case 2:e=this.room.x+this.room.w-8,n=this.room.x+u-d/2,o=8,a=d;break;case 3:e=this.room.x+this.room.w-u-d/2,n=this.room.y+this.room.h-8,o=d,a=8;break}return{m_x:r,m_width:s,x:e,y:n,w:o,h:a,direction:i,editing:c}},limitX:function(t,e,n){var o;return o=e.x<t.x?t.x:e.x>t.w+t.x-n.w?t.w+t.x-n.w:e.x,o},limitY:function(t,e,n){var o;return o=e.y<t.y?t.y:e.y>t.h+t.y-n.h?t.h+t.y-n.h:e.y,o},borderLimt:function(t,e,n){var o,a;switch(console.log("parent:",t),console.log("pos",e),console.log("obj",n),n.direction){case 0:o=e.x!=t.x?t.x:n.x,a=this.limitY(t,e,n);break;case 1:o=this.limitX(t,e,n),a=e.y!=t.y?t.y:n.y;break;case 2:o=e.x!=t.x+t.w-n.w?t.x+t.w-n.w:n.x,a=this.limitY(t,e,n);break;case 3:o=this.limitX(t,e,n),a=e.y!=t.y+t.h-n.h?t.y+t.h-n.h:n.y;break}return{x:o,y:a}},areaLimt:function(t,e,n){return{x:this.limitX(t,e,n),y:this.limitY(t,e,n)}},handleDragstart:function(t){var e,n;console.log("handleDragstart:",t.target.id()),this.dragItemId=t.target.id(),"gate"==(null===(e=t.target.attrs)||void 0===e?void 0:e.type)?(this.dragGateIndex=t.target.attrs.index,console.log("dragGateIndex:",this.dragGateIndex)):"region"==(null===(n=t.target.attrs)||void 0===n?void 0:n.type)&&(this.dragRegionIndex=t.target.attrs.index,console.log("dragRegionIndex:",this.dragRegionIndex))},bedDragBoundFunc:function(t){return this.areaLimt(this.room,t,this.regionList[this.dragRegionIndex])},deviceDragBoundFunc:function(t){return 2==this.selectSpotType?this.borderLimt(this.room,t,(0,i.default)((0,i.default)({},this.device),{},{direction:3})):this.areaLimt(this.room,t,this.device)},gateDragBoundFunc:function(t){return this.borderLimt(this.room,t,this.gateList[this.dragGateIndex])},handleDragend:function(t){var e,n;if(console.log("handleDragend:",t),console.log(t.target.attrs),"room"===t.target.id()?(this.room.x=t.target.attrs.x,this.room.y=t.target.attrs.y):"device"===t.target.id()&&(this.device.x=t.target.attrs.x,this.device.y=t.target.attrs.y,this.device.m_x=this.pi2m(this.device.x-this.room.x),this.device.m_y=this.pi2m(this.room.y+this.room.h-(this.device.y+this.device.h)),console.log("this.device",this.device)),"region"==(null===(e=t.target.attrs)||void 0===e?void 0:e.type)){var o=t.target.attrs.index,a=this.regionList[o],i=t.target.attrs.x,r=t.target.attrs.y;t.target.attrs.x-2<this.room.x?(console.log("超出房间左边"),i=this.room.x):t.target.attrs.x>this.room.x+this.room.w-a.w+2&&(console.log("超出房间右边"),i=this.room.x+this.room.w-a.w),t.target.attrs.y-2<this.room.y?(console.log("超出房间上边"),r=this.room.y):t.target.attrs.y>this.room.y+this.room.h-a.h+2&&(console.log("超出房间下边"),r=this.room.y+this.room.h-a.h),a.x=i,a.y=r,a.m_positionX=this.pi2m(a.x-this.room.x)+a.m_x/2,a.m_positionY=this.pi2m(this.room.y+this.room.h-(a.y+a.h))+a.m_y/2,console.log("regionList[idx]",a)}if("gate"==(null===(n=t.target.attrs)||void 0===n?void 0:n.type)){var s=t.target.attrs.index,c=this.gateList[s];c.x=t.target.attrs.x,c.y=t.target.attrs.y;var d=this.calcHateDistance(c);c.m_x=d+c.m_width/2,console.log("gateList[idx]",c)}this.dragItemId=null,this.dragGateIndex=null,this.dragRegionIndex=null},bedDblclick:function(t){var e;if(console.log("bedDblclick:",t),console.log(t.target.attrs),"region"==(null===(e=t.target.attrs)||void 0===e?void 0:e.type)){var n=t.target.attrs.index,o=this.regionList[n],a=(t.target,o.h),i=o.w;t.target.width(a),t.target.height(i),o.h=i,o.w=a;var r=o.m_x,s=o.m_y;o.m_x=s,o.m_y=r,o.m_positionX=this.pi2m(o.x-this.room.x)+o.m_x/2,o.m_positionY=this.pi2m(this.room.y+this.room.h-(o.y+o.h))+o.m_y/2,t.target.getLayer().batchDraw(),console.log("item:",o)}},stageClick:function(t){console.log(t.target.attrs)},movevToButtom:function(t){var e,n;console.log("bedDblclick:",t),console.log(t.target.attrs),"region"!=(null===(e=t.target.attrs)||void 0===e?void 0:e.type)&&"gate"!=(null===(n=t.target.attrs)||void 0===n?void 0:n.type)||(t.target.moveToBottom(),t.target.getLayer().batchDraw())},submitRoomData:function(){var t=this;return(0,s.default)((0,r.default)().mark((function e(){var n,o,a,s,c,d,u,f,p,m;return(0,r.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t.devCode||(console.log("无效的devCode"),uni.showModal({content:t.$t("pages.room.room.toast.9"),showCancel:!1,confirmText:"关闭"})),n={roomVO:{roomId:t.devCode,x:parseFloat(t.room.m_x),y:parseFloat(t.room.m_y),z:parseFloat(t.room.m_z)},roomSensorVO:{roomId:t.devCode,x:parseFloat(t.device.m_x),y:parseFloat(t.device.m_y),z:parseFloat(t.device.m_z),spotType:parseInt(t.selectSpotType),etilt:2==t.selectSpotType?30:90}},2==t.selectSpotType&&(n.roomVO.leftX=parseFloat(t.device.m_x),n.roomVO.rightX=parseFloat(t.room.m_x)-parseFloat(t.device.m_x)),t.gateList){for(o=[],a=0;a<t.gateList.length;a++)console.log("gateList[i]:",t.gateList[a]),s={roomId:t.devCode,gid:a+1,direction:t.gateList[a].direction,length:parseFloat(t.gateList[a].m_x),width:parseFloat(t.gateList[a].m_width),height:2},o.push(s);n=(0,i.default)((0,i.default)({},n),{},{roomGateVOList:o})}if(t.regionList){for(c=[],d=0;d<t.regionList.length;d++)u=t.regionList[d],console.log("regionList[i]:",u),f={roomId:t.devCode,rid:d+1,cls:parseInt(u.cls),positionX:parseFloat(u.m_positionX),positionY:parseFloat(u.m_positionY),scaleX:parseFloat(u.m_x),scaleY:parseFloat(u.m_y),scaleZ:parseFloat(u.m_z),rotation:0},c.push(f);n=(0,i.default)((0,i.default)({},n),{},{roomRegionVOList:c})}console.log("params:",n),p=t.convertRoomData(n),console.log("msgdata:",JSON.stringify(p)),m=l.DeviceHeaderCodec.encode(h.MsgCodeEnum.S2D,v.CmdIdEnum.CARE_JSON,p),t.client.publish(t.mqttConfig.device.topic.control,m),uni.showToast({duration:2e3,title:t.$t("pages.room.room.toast.8"),icon:"none"}),console.warn("publish setRoom:"+t.mqttConfig.device.topic.control+"\t "+m);case 12:case"end":return e.stop()}}),e)})))()},convertRoomData:function(t){var e,n=new m.SetRoomJson,o=t,a=o.roomVO,i=o.roomSensorVO,r=o.roomGateVOList,s=o.roomRegionVOList,c=[0-i.x,a.x-i.x,0-i.y,a.y-i.y,a.z],d=[i.z||0,i.atilt||0,i.etilt||90,i.atiltfov||60,i.etiltfov||60,1==i.spotType?0:1];console.log("devPos:",d);var u,l=r.length;e=0===l?[]:Array.from({length:l},(function(t,e){var n=r[e];return[n.gid,n.cls,n.direction||0,n.length||0,n.width||0,n.height||2]}));var f=s.length;u=0===f?[]:Array.from({length:f},(function(t,e){var n=s[e];return[n.rid,n.cls,n.positionX-i.x||0,n.positionY-i.y||0,n.scaleX||0,n.scaleY||0,n.scaleZ||0,n.rotation||0]}));var v=new p.RoomJson(this.devScene||3,c,d,e,u);return n.setMsgId("setRoom"),n.setSeqId(Math.floor(1e4*Math.random())),n.setData(v),n},disconnect:function(){this.client.end(!0,(function(){console.log("room MQTT已断开连接")}))}}};e.default=_},1269:function(t,e,n){"use strict";n("6a54");var o=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=o(n("2634")),i=o(n("2fdc"));n("64aa"),n("bf0f");var r=o(n("05b4")),s={name:"u-row",mixins:[uni.$u.mpMixin,uni.$u.mixin,r.default],data:function(){return{}},computed:{uJustify:function(){return"end"==this.justify||"start"==this.justify?"flex-"+this.justify:"around"==this.justify||"between"==this.justify?"space-"+this.justify:this.justify},uAlignItem:function(){return"top"==this.align?"flex-start":"bottom"==this.align?"flex-end":this.align},rowStyle:function(){var t={alignItems:this.uAlignItem,justifyContent:this.uJustify};return this.gutter&&(t.marginLeft=uni.$u.addUnit(-Number(this.gutter)/2),t.marginRight=uni.$u.addUnit(-Number(this.gutter)/2)),uni.$u.deepMerge(t,uni.$u.addStyle(this.customStyle))}},methods:{clickHandler:function(t){this.$emit("click")},getComponentWidth:function(){var t=this;return(0,i.default)((0,a.default)().mark((function e(){return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,uni.$u.sleep();case 2:return e.abrupt("return",new Promise((function(e){t.$uGetRect(".u-row").then((function(t){e(t.width)}))})));case 3:case"end":return e.stop()}}),e)})))()}}};e.default=s},"12cc":function(t,e,n){"use strict";var o=n("fe5f"),a=n.n(o);a.a},1320:function(t,e,n){var o=n("c86c");e=o(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */uni-view[data-v-b2a05bc2], uni-scroll-view[data-v-b2a05bc2], uni-swiper-item[data-v-b2a05bc2]{display:flex;flex-direction:column;flex-shrink:0;flex-grow:0;flex-basis:auto;align-items:stretch;align-content:flex-start}.u-overlay[data-v-b2a05bc2]{position:fixed;top:0;left:0;width:100%;height:100%;background-color:rgba(0,0,0,.7)}',""]),t.exports=e},"17ea":function(t,e,n){"use strict";n.r(e);var o=n("47339"),a=n("f854");for(var i in a)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(i);n("d55b");var r=n("828b"),s=Object(r["a"])(a["default"],o["b"],o["c"],!1,null,"186edb96",null,!1,o["a"],void 0);e["default"]=s.exports},"17f2":function(t,e,n){"use strict";n.r(e);var o=n("1176"),a=n.n(o);for(var i in o)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(i);e["default"]=a.a},1893:function(t,e,n){"use strict";n("6a54");var o=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.CmdIdEnum=void 0,e.getNameByValue=function(t){for(var e=0,n=Object.entries(i);e<n.length;e++){var o=(0,a.default)(n[e],2),r=o[0],s=o[1];if(s===t)return r}return"Unknown CMD ID"},e.getValueByName=function(t){return i[t]||null};var a=o(n("5de6"));n("0829");var i={CARE_HANDSHAKE:0,CARE_CFG_REPORTING_INTERVAL:1,CARE_CFG_SWITCH_THRESHOLD:6,CARE_SWITCH_MODE:11,CARE_CFG_ROOM_TYPE:12,CARE_CFG_DOWNWARD:10,CARE_REPORTING_PEOPLE_BREATHE_HEART:18,CARE_REPORTING_ROOM_ENV:20,CARE_REPORTING_PEOPLE_LOCATION2:21,CARE_REPORTING_PEOPLE_BREATHE_HEART2:21,CARE_HOTPLACE:24,CARE_APP_STATS_REPORTER:25,CARE_APP_APNEA_REPORTER:26,CARE_PEOPLE_GESTURE:33,CARE_PEOPLE_FALLDOWN:34,CARE_PEOPLE_STAYING_TOO_LONG:35,CARE_PEOPLE_IO_REGION:36,CARE_PEOPLE_COUNT:37,CARE_PRE_FALLDOWN:38,DEBUG_CLOUD_POINTS_SWITCH:48,DEBUG_CLOUD_POINTS:49,DEBUG_SYNERGY_VS:50,DEBUG_SWITCH_SCENE:51,DEBUG_PROD_FALL_EVENT:52,DEBUG_GET_STATE:53,INQUERY_RADAR_CFG:64,CARE_JSON:80,CARE_HEARTBEAT:81,SET_CARE_HEARTBEAT_INTERVAL:82};e.CmdIdEnum=i},"1bf2":function(t,e,n){"use strict";n("6a54");var o=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=o(n("09cb")),i={name:"u-safe-bottom",mixins:[uni.$u.mpMixin,uni.$u.mixin,a.default],data:function(){return{safeAreaBottomHeight:0,isNvue:!1}},computed:{style:function(){return uni.$u.deepMerge({},uni.$u.addStyle(this.customStyle))}},mounted:function(){}};e.default=i},"213f":function(t,e,n){var o=n("a8af");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var a=n("967d").default;a("0b97ed93",o,!0,{sourceMap:!1,shadowMode:!1})},"22fb":function(t,e,n){"use strict";var o=n("213f"),a=n.n(o);a.a},"24d2":function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("64aa");var o={props:{span:{type:[String,Number],default:uni.$u.props.col.span},offset:{type:[String,Number],default:uni.$u.props.col.offset},justify:{type:String,default:uni.$u.props.col.justify},align:{type:String,default:uni.$u.props.col.align},textAlign:{type:String,default:uni.$u.props.col.textAlign}}};e.default=o},"26df":function(t,e,n){"use strict";n.r(e);var o=n("7a8e"),a=n("4a06");for(var i in a)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(i);n("a6c5");var r=n("828b"),s=Object(r["a"])(a["default"],o["b"],o["c"],!1,null,"30282a05",null,!1,o["a"],void 0);e["default"]=s.exports},"286a":function(t,e,n){"use strict";n("6a54");var o=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.BreathHeartRates=e.BreathHeartRate=void 0;var a=o(n("80b1")),i=o(n("efe5")),r=function(){function t(){(0,a.default)(this,t),this.tid=null,this.res0=null,this.confidence=null,this.breathBPM=null,this.heartBPM=null,this.breathMM=null,this.heartMM=null,this.x=null,this.y=null,this.z=null,this.res1=null}return(0,i.default)(t,[{key:"setTid",value:function(t){this.tid=t}},{key:"setRes0",value:function(t){this.res0=t}},{key:"setConfidence",value:function(t){this.confidence=t}},{key:"setBreathBPM",value:function(t){this.breathBPM=t}},{key:"setHeartBPM",value:function(t){this.heartBPM=t}},{key:"setBreathMM",value:function(t){this.breathMM=t}},{key:"setHeartMM",value:function(t){this.heartMM=t}},{key:"setX",value:function(t){this.x=t}},{key:"setY",value:function(t){this.y=t}},{key:"setZ",value:function(t){this.z=t}},{key:"setRes1",value:function(t){this.res1=t}}]),t}();e.BreathHeartRate=r;var s=function(){function t(){(0,a.default)(this,t),this.ts=null,this.version=null,this.num=null,this.breathHeartRates=[]}return(0,i.default)(t,[{key:"setTs",value:function(t){this.ts=t}},{key:"setVersion",value:function(t){this.version=t}},{key:"setNum",value:function(t){this.num=t}},{key:"setBreathHeartRates",value:function(t){this.breathHeartRates=t}}]),t}();e.BreathHeartRates=s},"2ab1":function(t,e,n){"use strict";n("6a54");var o=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=o(n("2634")),i=o(n("2fdc"));n("bf0f"),n("c223");o(n("5592"));var r=function(t){return{enter:"u-".concat(t,"-enter u-").concat(t,"-enter-active"),"enter-to":"u-".concat(t,"-enter-to u-").concat(t,"-enter-active"),leave:"u-".concat(t,"-leave u-").concat(t,"-leave-active"),"leave-to":"u-".concat(t,"-leave-to u-").concat(t,"-leave-active")}},s={methods:{clickHandler:function(){this.$emit("click")},vueEnter:function(){var t=this,e=r(this.mode);this.status="enter",this.$emit("beforeEnter"),this.inited=!0,this.display=!0,this.classes=e.enter,this.$nextTick((0,i.default)((0,a.default)().mark((function n(){return(0,a.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.next=2,uni.$u.sleep(20);case 2:t.$emit("enter"),t.transitionEnded=!1,t.$emit("afterEnter"),t.classes=e["enter-to"];case 6:case"end":return n.stop()}}),n)}))))},vueLeave:function(){var t=this;if(this.display){var e=r(this.mode);this.status="leave",this.$emit("beforeLeave"),this.classes=e.leave,this.$nextTick((function(){t.transitionEnded=!1,t.$emit("leave"),setTimeout(t.onTransitionEnd,t.duration),t.classes=e["leave-to"]}))}},onTransitionEnd:function(){this.transitionEnded||(this.transitionEnded=!0,this.$emit("leave"===this.status?"afterLeave":"afterEnter"),!this.show&&this.display&&(this.display=!1,this.inited=!1))}}};e.default=s},"2cc7":function(t,e,n){"use strict";n("6a54");var o=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.HotPlace=void 0;var a=o(n("80b1")),i=o(n("efe5")),r=function(){function t(){(0,a.default)(this,t),this.ts=null,this.version=null,this.start=null,this.res=[],this.period=null,this.x=null,this.y=null,this.z=null}return(0,i.default)(t,[{key:"setTs",value:function(t){this.ts=t}},{key:"setVersion",value:function(t){this.version=t}},{key:"setStart",value:function(t){this.start=t}},{key:"setRes",value:function(t){this.res=t}},{key:"setPeriod",value:function(t){this.period=t}},{key:"setX",value:function(t){this.x=t}},{key:"setY",value:function(t){this.y=t}},{key:"setZ",value:function(t){this.z=t}}]),t}();e.HotPlace=r},"2e31":function(t,e,n){"use strict";var o=n("f574"),a=n.n(o);a.a},"2ec5":function(t,e,n){"use strict";t.exports=function(t,e){return e||(e={}),t=t&&t.__esModule?t.default:t,"string"!==typeof t?t:(/^['"].*['"]$/.test(t)&&(t=t.slice(1,-1)),e.hash&&(t+=e.hash),/["'() \t\n]/.test(t)||e.needQuotes?'"'.concat(t.replace(/"/g,'\\"').replace(/\n/g,"\\n"),'"'):t)}},"305b":function(t,e,n){var o=n("c86c");e=o(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */uni-view[data-v-91d5fe04], uni-scroll-view[data-v-91d5fe04], uni-swiper-item[data-v-91d5fe04]{display:flex;flex-direction:column;flex-shrink:0;flex-grow:0;flex-basis:auto;align-items:stretch;align-content:flex-start}.u-col[data-v-91d5fe04]{padding:0;box-sizing:border-box}.u-col-0[data-v-91d5fe04]{width:0}.u-col-1[data-v-91d5fe04]{width:calc(100%/12)}.u-col-2[data-v-91d5fe04]{width:calc(100%/12 * 2)}.u-col-3[data-v-91d5fe04]{width:calc(100%/12 * 3)}.u-col-4[data-v-91d5fe04]{width:calc(100%/12 * 4)}.u-col-5[data-v-91d5fe04]{width:calc(100%/12 * 5)}.u-col-6[data-v-91d5fe04]{width:calc(100%/12 * 6)}.u-col-7[data-v-91d5fe04]{width:calc(100%/12 * 7)}.u-col-8[data-v-91d5fe04]{width:calc(100%/12 * 8)}.u-col-9[data-v-91d5fe04]{width:calc(100%/12 * 9)}.u-col-10[data-v-91d5fe04]{width:calc(100%/12 * 10)}.u-col-11[data-v-91d5fe04]{width:calc(100%/12 * 11)}.u-col-12[data-v-91d5fe04]{width:calc(100%/12 * 12)}',""]),t.exports=e},3389:function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o={props:{bgColor:{type:String,default:uni.$u.props.statusBar.bgColor}}};e.default=o},3483:function(t,e,n){"use strict";n.r(e);var o=n("7454"),a=n("e91c");for(var i in a)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(i);n("12cc");var r=n("828b"),s=Object(r["a"])(a["default"],o["b"],o["c"],!1,null,"91d5fe04",null,!1,o["a"],void 0);e["default"]=s.exports},"358f":function(t,e,n){"use strict";(function(t){n("6a54");var o=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.DeviceContentCodec=void 0,n("f7a5"),n("5ef2"),n("0c26"),n("aa9c"),n("c9b5"),n("bf0f"),n("ab80"),n("1851");var a=o(n("80b1")),i=o(n("efe5")),r=n("7fa8"),s=n("1893"),c=n("4ab4"),d=n("c8f6"),u=n("c305"),l=n("b997"),f=n("45e2"),v=n("286a"),h=n("d1ab"),p=n("a6f4"),m=n("503e"),g=n("8da3"),y=n("0eaf"),b=n("2cc7"),x=n("8aa6"),_=n("6e17"),w=function(){function e(){(0,a.default)(this,e)}return(0,i.default)(e,null,[{key:"decode",value:function(t,e){var n=t.msgId;t.msgType,t.direction;switch(n){case c.PlatformProtoEnum.RFC_TRANSFER:return this.decodeTransferMsg(e);case c.PlatformProtoEnum.RFC_APP_MSG:return this.decodeAppMsg(e)}}},{key:"decodeTransferMsg",value:function(t){var e=new r.BinaryReader(t),n=new u.BaseAck;return n.ack=e.readUInt32(),new d.JsonMsg("CfgTransferAck",n)}},{key:"decodeAppMsg",value:function(t){var e={},n="",o=t.readUInt8(0),a=t.slice(1);switch(o){case s.CmdIdEnum.CARE_JSON:var i=JSON.parse(a);i.respMsgId&&(n=i.respMsgId),e=i.data;break;case s.CmdIdEnum.CARE_HANDSHAKE:n="HandShake",e=this.decodeHandShake(a);break;case s.CmdIdEnum.CARE_HEARTBEAT:n="Heartbeat",e=this.decodeHeartbeat(a);break;case s.CmdIdEnum.CARE_REPORTING_PEOPLE_LOCATION2:n="PeopleEvent",e=this.decodePeopleEvent(a);break;case s.CmdIdEnum.CARE_PRE_FALLDOWN:n="FallDown",e=this.decodeFallDown(a);break;case s.CmdIdEnum.CARE_PEOPLE_STAYING_TOO_LONG:n="StayTooLong",e=this.decodeStayTooLong(a);break;case s.CmdIdEnum.DEBUG_CLOUD_POINTS:n="CloudPoints",e=this.decodeCloudPoints(a);break;case s.CmdIdEnum.CARE_PEOPLE_GESTURE:n="PeopleGesture",e=this.decodePeopleGesture(a);break;case s.CmdIdEnum.CARE_REPORTING_PEOPLE_BREATHE_HEART2:n="BreathHeartRates2",e=this.decodeBreatheHeart(a);break;case s.CmdIdEnum.CARE_PEOPLE_IO_REGION:n="OptInOut",e=this.decodeOptInOut(a);break;case s.CmdIdEnum.CARE_HOTPLACE:n="HotPlace",e=this.decodeHotPlace(a);break;case s.CmdIdEnum.CARE_APP_STATS_REPORTER:n="AppStatsReporter",e=this.decodeAppStatsReporter(a);break;case s.CmdIdEnum.DEBUG_CLOUD_POINTS_SWITCH:n="CloudPointsSwitchAck",e=this.decodeCloudPointsSwitchAck(a);break}return new d.JsonMsg(n,e)}},{key:"decodeHandShake_old",value:function(t){var e=new _.HandShake;if(t.length>0){var n=new TextDecoder("utf-8");e.setMessage(n.decode(t))}else e.setMessage(null);return console.debug("handshake: "+e.getMessage()),e}},{key:"decodeHandShake",value:function(e){var n,o=new _.HandShake;if(0===e.length)return console.debug("handshake: empty payload"),o;try{var a=new TextDecoder("utf-8");n=a.decode(e);var i=n.indexOf("\0");-1!==i&&(n=n.slice(0,i)),o.setMessage(n.trim())}catch(s){console.error("Failed to decode handshake message as UTF-8:",s),o.setMessage(null)}if(null!==o.getMessage()&&t.isBuffer(e)){var r=(new TextEncoder).encode(o.getMessage());e.slice(r.length)}return console.debug("handshake: "+o.getMessage()),o}},{key:"decodeHeartbeat",value:function(t){var e=new r.BinaryReader(t),n=new x.Heartbeat;return n.setTs(e.readUInt64()),n.setVersion(e.readUInt8()),n.setWifiRssi(e.readInt8()),n.setPeopleNum(e.readUInt8()),n.setIotRssi(e.readInt8()),n.setRes(e.readUInt8Array(6)),n.setCpuWorkLoad(e.readUInt8()),n.setCpu1Load(e.readUInt8()),n.setCpu2Load(e.readUInt8()),n.setMemWorkLoad(e.readUInt8()),n.setIRamLoad(e.readUInt8()),n.setDRamLoad(e.readUInt8()),n.setSpiRamLoad(e.readUInt8()),n.setHimemLoad(e.readUInt8()),n.setRes2(e.readUInt8Array(12)),n.setRegionId(e.readUInt8()),n.setRegionOccur(e.readUInt8()),n.setBed1Id(e.readUInt8()),n.setBed1Occur(e.readUInt8()),n.setBed2Id(e.readUInt8()),n.setBed2Occur(e.readUInt8()),n.setRes3(e.readUInt8Array(84)),n}},{key:"decodePeopleEvent",value:function(t){var e=new r.BinaryReader(t),n=new l.PeopleEvent;n.setTs(1e3*e.readUInt32()),n.setVersion(e.readUInt8());var o=e.readUInt8();n.setAmount(o);for(var a=[],i=0;i<o;i++){var s=new l.TargetLocation;s.tid=e.readUInt32(),s.cls=e.readUInt16(),s.posture=e.readUInt16(),s.x=e.readFloat(),s.y=e.readFloat(),s.z=e.readFloat(),s.length=e.readFloat(),s.width=e.readFloat(),s.thick=e.readFloat(),s.velocity=e.readFloat(),s.acceleration=e.readFloat(),a.push(s)}return n.setTargets(a),n}},{key:"decodeFallDown",value:function(t){var e=new r.BinaryReader(t),n=new m.FallDown;return n.setTs(1e3*e.readUInt32()),n.setTid(e.readUInt32()),n.setConf(e.readUInt8()),n.setX(e.readFloat()),n.setY(e.readFloat()),n.setZ(e.readFloat()),n}},{key:"decodeStayTooLong",value:function(t){var e=new r.BinaryReader(t),n=new p.StayTooLong;return n.setTs(1e3*e.readUInt32()),n.setTid(e.readUInt32()),n.setConf(e.readUInt8()),n.setX(e.readFloat()),n.setY(e.readFloat()),n.setZ(e.readFloat()),n}},{key:"decodeCloudPoints",value:function(t){var e=new r.BinaryReader(t),n=new f.CloudPoints;n.setLevel(e.readUInt8()),n.setAmount(e.readUInt32());var o=e.readUInt32();n.setPointCnt(o);for(var a=[],i=0;i<o;i++){var s=new f.Frame;s.setX(e.readFloat()),s.setY(e.readFloat()),s.setZ(e.readFloat()),s.setVelocity(e.readFloat()),s.setSnr(e.readFloat()),a.push(s)}return n.setFrames(a),n}},{key:"decodePeopleGesture",value:function(t){var e=new r.BinaryReader(t),n=new g.PeopleGesture;return n.setTs(e.readUInt64()),n.setVersion(e.readUInt8()),n.setRes(e.readUInt8Array(3)),n.setGesture(e.readUInt32()),n.setConfidence(e.readFloat()),n}},{key:"decodeBreatheHeart",value:function(t){var e=new r.BinaryReader(t),n=new v.BreathHeartRates;n.setTs(e.readUInt32()),n.setVersion(e.readUInt8());var o=e.readUInt8();n.setNum(o);for(var a=[],i=0;i<o;i++){var s=new v.BreathHeartRate;s.setTid(e.readUInt32()),s.setRes0(e.readUInt8()),s.setConfidence(e.readUInt8()),s.setBreathBPM(e.readUInt8()),s.setHeartBPM(e.readUInt8()),s.setBreathMM(e.readInt16()/1e4),s.setHeartMM(e.readInt16()/1e4),s.setX(e.readInt16()/1e3),s.setY(e.readInt16()/1e3),s.setZ(e.readInt16()/1e3),s.setRes1(e.readInt16()),a.push(s)}return n.setBreathHeartRates(a),n}},{key:"decodeOptInOut",value:function(t){var e=new r.BinaryReader(t),n=new y.OptInOut;return n.setTs(1e3*e.readUInt32()),n.setTid(e.readUInt32()),n.setEvent(e.readUInt8()),n.setType(e.readUInt8()),n.setSubId(e.readUInt16()),n.setSubType(e.readUInt16()),n}},{key:"decodeHotPlace",value:function(t){var e=new r.BinaryReader(t),n=new b.HotPlace;return n.setTs(e.readUInt64()),n.setVersion(e.readUInt8()),n.setStart(e.readUInt8()),n.setRes(e.readUInt8Array(2)),n.setPeriod(e.readFloat()),n.setX(e.readFloat()),n.setY(e.readFloat()),n.setZ(e.readFloat()),n}},{key:"decodeAppStatsReporter",value:function(t){var e=new r.BinaryReader(t,9),n=new h.AppStatsReporter;n.setTs(e.readUInt64()),n.setVersion(e.readUInt8());var o=[];while(e.available()>0&&e.canRead(7)){var a=new h.AppStatsItem;a.setType(e.readUInt8()),a.setApp(e.readUInt8());var i=e.readInt32();if(null===i)break;if(a.setBitTable(i),0!==i){var s=i.toString(2).split("0").join("").length;a.setNumSpans(s);for(var c=Array(30).fill(0),d=0;d<30;d++){if(d>=s||!e.canRead(1))break;var u=1<<d,l=e.readUInt8();0!==(i&u)&&(c[d]=l>0?l:0)}a.setValues(c),o.push(a)}}return n.setData(o),n}},{key:"decodeCloudPointsSwitchAck",value:function(t){var e=new r.BinaryReader(t),n=new u.BaseAck;return n.ack=e.readUInt8(),n}}]),e}();e.DeviceContentCodec=w}).call(this,n("12e3").Buffer)},"37a6":function(t,e,n){"use strict";n.r(e);var o=n("6a6a"),a=n("b148");for(var i in a)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(i);n("2e31");var r=n("828b"),s=Object(r["a"])(a["default"],o["b"],o["c"],!1,null,"b2a05bc2",null,!1,o["a"],void 0);e["default"]=s.exports},"38d58":function(t,e,n){var o=n("99c2");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var a=n("967d").default;a("5da2bfdc",o,!0,{sourceMap:!1,shadowMode:!1})},"3a46":function(t,e,n){"use strict";var o=n("7371"),a=n.n(o);a.a},"3ddf":function(t,e,n){"use strict";n.r(e);var o=n("e184"),a=n("6720");for(var i in a)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(i);n("22fb");var r=n("828b"),s=Object(r["a"])(a["default"],o["b"],o["c"],!1,null,"eca591a4",null,!1,o["a"],void 0);e["default"]=s.exports},4214:function(t,e,n){var o=n("c86c");e=o(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.popup-top[data-v-2b676027]{width:%?686?%;height:%?88?%;margin:%?32?% 0 %?16?% %?32?%}.popup-top .cancel[data-v-2b676027]{font-size:%?32?%;font-weight:700;text-align:left;color:#01b09a}.popup-top .confirm[data-v-2b676027]{font-size:%?32?%;font-weight:700;text-align:right;color:#01b09a}.popup-top .title[data-v-2b676027]{font-size:%?36?%;text-align:center;font-weight:700;color:#515151}.popup-top .selected[data-v-2b676027]{margin-top:%?16?%;font-size:%?26?%;text-align:center;color:#999;white-space:nowrap}.popup-centent[data-v-2b676027]{padding:%?20?%;margin-bottom:%?32?%}',""]),t.exports=e},"45e2":function(t,e,n){"use strict";n("6a54");var o=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.Frame=e.CloudPoints=void 0;var a=o(n("80b1")),i=o(n("efe5")),r=function(){function t(){(0,a.default)(this,t),this.x=null,this.y=null,this.z=null,this.velocity=null,this.snr=null}return(0,i.default)(t,[{key:"setX",value:function(t){this.x=t}},{key:"setY",value:function(t){this.y=t}},{key:"setZ",value:function(t){this.z=t}},{key:"setVelocity",value:function(t){this.velocity=t}},{key:"setSnr",value:function(t){this.snr=t}}]),t}();e.Frame=r;var s=function(){function t(){(0,a.default)(this,t),this.level=null,this.amount=null,this.pointCnt=null,this.frames=[]}return(0,i.default)(t,[{key:"setLevel",value:function(t){this.level=t}},{key:"setAmount",value:function(t){this.amount=t}},{key:"setPointCnt",value:function(t){this.pointCnt=t}},{key:"setFrames",value:function(t){this.frames=t}}]),t}();e.CloudPoints=s},47339:function(t,e,n){"use strict";n.d(e,"b",(function(){return o})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){}));var o=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticClass:"u-status-bar",style:[this.style]},[this._t("default")],2)},a=[]},4886:function(t,e,n){"use strict";var o=n("aa18"),a=n.n(o);a.a},"4a06":function(t,e,n){"use strict";n.r(e);var o=n("acfb"),a=n.n(o);for(var i in o)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(i);e["default"]=a.a},"4ab4":function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.PlatformProtoEnum=void 0;e.PlatformProtoEnum={RFC_HANDSHAKE:0,RFC_SET_DEVICEID:1,RFC_REBOOT:2,RFC_GET_TIMESTAMP:4,RFC_RESET_CONFIG:5,RFC_APP_MSG:16,RFC_LOG:17,RFC_FACTORY_RESULT:18,RFC_CALIBRATION_RESULT:19,RFC_SWITCH_MODE:32,RFC_TRANSFER:33,RFC_INQUIRY:34,RFC_NOTICE:40,RFC_INQUIRE_RADAR_TABLE:37,RFC_REQUEST_DID:48,RFC_GET_DEVICE_INFO:49,RFC_OTA_ESP:256,RFC_OTA_RADAR:257,RFC_FIRMWARE_CFG:258,RFC_UPDATE_CERT_PEM:260,RFC_MSG_MAX:1023,CARE_PEOPLE_FALLDOWN:34,CARE_PEOPLE_STAYING_TOO_LONG:35,CARE_PEOPLE_IO:36}},"503e":function(t,e,n){"use strict";n("6a54");var o=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.FallDown=void 0;var a=o(n("80b1")),i=o(n("efe5")),r=function(){function t(){(0,a.default)(this,t),this.ts=null,this.tid=null,this.conf=null,this.x=null,this.y=null,this.z=null}return(0,i.default)(t,[{key:"setTs",value:function(t){this.ts=t}},{key:"setTid",value:function(t){this.tid=t}},{key:"setConf",value:function(t){this.conf=t}},{key:"setX",value:function(t){this.x=t}},{key:"setY",value:function(t){this.y=t}},{key:"setZ",value:function(t){this.z=t}}]),t}();e.FallDown=r},5058:function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return i})),n.d(e,"a",(function(){return o}));var o={uPopup:n("26df").default},a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("u-popup",{attrs:{mode:"bottom",show:t.visible},on:{close:function(e){arguments[0]=e=t.$handleEvent(e),t.onCancel.apply(void 0,arguments)}}},[n("v-uni-view",{staticClass:"r-flex jc-sb popup-top"},[n("v-uni-text",{staticClass:"cancel",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onCancel.apply(void 0,arguments)}}},[t._v("取消")]),n("v-uni-view",{staticClass:"c-flex jc-sb"},[t.title?n("v-uni-text",{staticClass:"title"},[t._v(t._s(t.title))]):t._e(),t.subtitle?n("v-uni-text",{staticClass:"selected"},[t._v(t._s(t.subtitle))]):t._e()],1),n("v-uni-text",{staticClass:"confirm",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onConfirm.apply(void 0,arguments)}}},[t._v("确定")])],1),n("v-uni-view",{staticClass:"popup-centent"},[t._t("default")],2)],1)},i=[]},"54f2":function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("64aa");var o={props:{show:{type:Boolean,default:uni.$u.props.transition.show},mode:{type:String,default:uni.$u.props.transition.mode},duration:{type:[String,Number],default:uni.$u.props.transition.duration},timingFunction:{type:String,default:uni.$u.props.transition.timingFunction}}};e.default=o},5592:function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;e.default={fade:{enter:{opacity:0},"enter-to":{opacity:1},leave:{opacity:1},"leave-to":{opacity:0}},"fade-up":{enter:{opacity:0,transform:"translateY(100%)"},"enter-to":{opacity:1,transform:"translateY(0)"},leave:{opacity:1,transform:"translateY(0)"},"leave-to":{opacity:0,transform:"translateY(100%)"}},"fade-down":{enter:{opacity:0,transform:"translateY(-100%)"},"enter-to":{opacity:1,transform:"translateY(0)"},leave:{opacity:1,transform:"translateY(0)"},"leave-to":{opacity:0,transform:"translateY(-100%)"}},"fade-left":{enter:{opacity:0,transform:"translateX(-100%)"},"enter-to":{opacity:1,transform:"translateY(0)"},leave:{opacity:1,transform:"translateY(0)"},"leave-to":{opacity:0,transform:"translateX(-100%)"}},"fade-right":{enter:{opacity:0,transform:"translateX(100%)"},"enter-to":{opacity:1,transform:"translateY(0)"},leave:{opacity:1,transform:"translateY(0)"},"leave-to":{opacity:0,transform:"translateX(100%)"}},"slide-up":{enter:{transform:"translateY(100%)"},"enter-to":{transform:"translateY(0)"},leave:{transform:"translateY(0)"},"leave-to":{transform:"translateY(100%)"}},"slide-down":{enter:{transform:"translateY(-100%)"},"enter-to":{transform:"translateY(0)"},leave:{transform:"translateY(0)"},"leave-to":{transform:"translateY(-100%)"}},"slide-left":{enter:{transform:"translateX(-100%)"},"enter-to":{transform:"translateY(0)"},leave:{transform:"translateY(0)"},"leave-to":{transform:"translateX(-100%)"}},"slide-right":{enter:{transform:"translateX(100%)"},"enter-to":{transform:"translateY(0)"},leave:{transform:"translateY(0)"},"leave-to":{transform:"translateX(100%)"}},zoom:{enter:{transform:"scale(0.95)"},"enter-to":{transform:"scale(1)"},leave:{transform:"scale(1)"},"leave-to":{transform:"scale(0.95)"}},"fade-zoom":{enter:{opacity:0,transform:"scale(0.95)"},"enter-to":{opacity:1,transform:"scale(1)"},leave:{opacity:1,transform:"scale(1)"},"leave-to":{opacity:0,transform:"scale(0.95)"}}}},5864:function(t,e,n){"use strict";(function(t){n("6a54");var o=n("3639").default,a=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.DeviceHeaderCodec=void 0,n("d4b5"),n("7a76"),n("c9b5"),n("c223"),n("f7a5");var i=a(n("fcf3")),r=a(n("80b1")),s=a(n("efe5")),c=(o(n("9437")),function(){function e(){(0,r.default)(this,e)}return(0,s.default)(e,null,[{key:"encode",value:function(e,n,o){if("object"===(0,i.default)(o)&&null!==o)o=JSON.stringify(o);else if("string"!==typeof o)throw new TypeError("Payload must be a string or an object.");var a=t.alloc(2);a.writeUInt16LE(e,0);var r=t.byteLength(o,"utf8")+5,s=t.alloc(2);s.writeUInt16LE(r,0);var c=t.from([n]),d=t.from(o,"utf8");return t.concat([a,s,c,d])}},{key:"decode_old",value:function(t){if(t.length<5)throw new Error("Invalid message format.");var e=t.readUInt16LE(0),n=this.extractOpcodeFromInt(e),o=t.readUInt16LE(2),a=t.readUInt8(4),i=t.slice(5),r={opcode:n,msgcode:e,length:o,cmdid:a,payload:i};return r}},{key:"decode",value:function(t){if(t.length<5)throw new Error("Invalid message format.");var e=t.readUInt16LE(0),n=this.extractOpcodeFromInt(e),o=t.readUInt16LE(2),a=t.slice(4),i={opcode:n,msgcode:e,length:o,payload:a};return i}},{key:"extractOpcodeFromInt",value:function(t){t&=65535;var e=t>>6&1023,n=t>>4&3,o=15&t;return{msgId:e,msgType:n,direction:o}}},{key:"extractOpcodeFromBytes",value:function(t){if(!Array.isArray(t)||2!==t.length)throw new Error("Input must be an array of two bytes.");var e=t[1]<<8|t[0],n=e>>6&1023,o=e>>4&3,a=15&e;return{msgId:n,msgType:o,direction:a}}}]),e}());e.DeviceHeaderCodec=c}).call(this,n("12e3").Buffer)},"58fd":function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("64aa");var o={props:{show:{type:Boolean,default:uni.$u.props.popup.show},overlay:{type:Boolean,default:uni.$u.props.popup.overlay},mode:{type:String,default:uni.$u.props.popup.mode},duration:{type:[String,Number],default:uni.$u.props.popup.duration},closeable:{type:Boolean,default:uni.$u.props.popup.closeable},overlayStyle:{type:[Object,String],default:uni.$u.props.popup.overlayStyle},closeOnClickOverlay:{type:Boolean,default:uni.$u.props.popup.closeOnClickOverlay},zIndex:{type:[String,Number],default:uni.$u.props.popup.zIndex},safeAreaInsetBottom:{type:Boolean,default:uni.$u.props.popup.safeAreaInsetBottom},safeAreaInsetTop:{type:Boolean,default:uni.$u.props.popup.safeAreaInsetTop},closeIconPos:{type:String,default:uni.$u.props.popup.closeIconPos},round:{type:[Boolean,String,Number],default:uni.$u.props.popup.round},zoom:{type:Boolean,default:uni.$u.props.popup.zoom},bgColor:{type:String,default:uni.$u.props.popup.bgColor},overlayOpacity:{type:[Number,String],default:uni.$u.props.popup.overlayOpacity}}};e.default=o},"5aac":function(t,e,n){"use strict";n("6a54");var o=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.CmdJson=void 0;var a=o(n("80b1")),i=o(n("efe5")),r=function(){function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;(0,a.default)(this,t),this.msgId=e,this.seqId=n}return(0,i.default)(t,[{key:"getMsgId",value:function(){return this.msgId}},{key:"setMsgId",value:function(t){this.msgId=t}},{key:"getSeqId",value:function(){return this.seqId}},{key:"setSeqId",value:function(t){this.seqId=t}}]),t}();e.CmdJson=r},"5c04":function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return i})),n.d(e,"a",(function(){return o}));var o={uIcon:n("764e").default,uInput:n("6cc6").default,uRow:n("ca17").default,uCol:n("3483").default,uButton:n("66bc").default},a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{staticClass:"main"},[n("v-uni-view",{staticClass:"stage"},[n("v-stage",{ref:"stage",attrs:{config:t.stage},on:{dragstart:function(e){arguments[0]=e=t.$handleEvent(e),t.handleDragstart.apply(void 0,arguments)},dragend:function(e){arguments[0]=e=t.$handleEvent(e),t.handleDragend.apply(void 0,arguments)},dbclick:function(e){arguments[0]=e=t.$handleEvent(e),t.bedDblclick.apply(void 0,arguments)},dbltap:function(e){arguments[0]=e=t.$handleEvent(e),t.bedDblclick.apply(void 0,arguments)},click:function(e){arguments[0]=e=t.$handleEvent(e),t.stageClick.apply(void 0,arguments)}}},[n("v-layer",[n("v-group",[t.pixel2cmRatio?n("v-text",{attrs:{config:{id:"textRoomL",x:6,y:(t.room.y+t.room.h)/2,text:t.pi2m(t.room.h),fontSize:14,fill:"#01B09A"}}}):t._e(),t.pixel2cmRatio?n("v-text",{attrs:{config:{id:"textRoomT",x:(t.room.x+t.room.w)/2,y:t.roomPadding/2,text:t.pi2m(t.room.w),fontSize:14,fill:"#01B09A"}}}):t._e(),n("v-rect",{ref:"room",attrs:{config:{id:"room",x:t.room.x,y:t.room.y,width:t.room.w,height:t.room.h,stroke:"#999999",fill:"#F9FFFC",strokeWidth:4,draggable:!1,opacity:1,shadowColor:"#000000",shadowOffsetX:3,shadowOffsetY:3,shadowBlur:10,shadowOpacity:.36}}})],1),n("v-group",[t._l(t.regionList,(function(e,o){return n("v-rect",{key:"region"+o,ref:"bed",refInFor:!0,attrs:{config:{id:"region_"+o,type:"region",index:o,x:e.x,y:e.y,width:e.w,height:e.h,fill:"white",stroke:"#01B09A",strokeWidth:4,cornerRadius:4,draggable:!0,dragBoundFunc:t.bedDragBoundFunc}},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.bedDblclick.apply(void 0,arguments)},mousemove:function(e){arguments[0]=e=t.$handleEvent(e),t.handleMouseMove.apply(void 0,arguments)}}})})),t._l(t.gateList,(function(e,o){return n("v-rect",{key:"gate"+o,ref:"gate",refInFor:!0,attrs:{config:{id:"gate_"+o,type:"gate",index:o,x:e.x,y:e.y,width:e.w,height:e.h,fill:"blue",draggable:!0,dragBoundFunc:t.gateDragBoundFunc,fill:"white",stroke:"#01B09A",strokeWidth:2,hitStrokeWidth:30}}})})),n("v-rect",{ref:"device",attrs:{config:{id:"device",type:"device",x:t.device.x,y:t.device.y,width:t.device.w,height:t.device.h,cornerRadius:2,hitStrokeWidth:20,fill:"#01B09A",draggable:!0,dragBoundFunc:t.deviceDragBoundFunc,opacity:.6}}}),n("v-line",{attrs:{config:{id:"deviceLineL",points:[t.room.x,t.device.y+t.device.h/2,t.device.x,t.device.y+t.device.h/2],stroke:"#01B09A",strokeWidth:1,lineCap:"round",lineJoin:"round",tension:1}}}),n("v-text",{attrs:{config:{id:"deviceTextL",x:(t.device.x+t.room.x)/2,y:t.device.y-8,text:t.pi2m(t.device.x-t.room.x),fontSize:t.fontSize1,fill:"#01B09A"}}}),1==t.selectSpotType?n("v-group",[n("v-line",{ref:"deviceLineB",attrs:{config:{id:"deviceLineB",points:[t.device.x+t.device.w/2,t.room.y+t.room.h,t.device.x+t.device.w/2,t.device.y+t.device.h],stroke:"#01AF99",strokeWidth:1,lineCap:"round",lineJoin:"round",tension:1}}}),n("v-text",{ref:"deviceTextB",attrs:{config:{id:"deviceTextB",x:t.device.x+t.device.w/2,y:(t.device.y+t.device.h+t.room.y+t.room.h)/2-t.fontSize1,text:t.pi2m(t.room.y+t.room.h-(t.device.y+t.device.h)),fontSize:t.fontSize1,fill:"#01AF99"}}})],1):t._e()],2)],1),n("v-layer",t._l(t.gateList,(function(e,o){return n("v-group",{key:"gat"+o},["1"==e.direction?n("v-group",[n("v-line",{ref:"gateLineL",refInFor:!0,attrs:{config:{id:"gateLineL",points:[t.room.x,e.y+e.h/2,e.x,e.y+e.h/2],stroke:"#FF7D17",strokeWidth:1,lineCap:"round",lineJoin:"round",tension:1}}}),n("v-text",{ref:"gateTextL",refInFor:!0,attrs:{config:{id:"gateTextL",x:(e.x+t.room.x)/2,y:"1"==e.direction?e.y+e.h/2+5:e.y+e.h/2-t.fontSize1,text:t.pi2m(e.x-t.room.x),fontSize:t.fontSize1,fill:"#FF7D17"}}})],1):"3"==e.direction?n("v-group",[n("v-line",{ref:"gateLineR",refInFor:!0,attrs:{config:{id:"gateLineR",points:[t.room.x+t.room.w,e.y+e.h/2,e.x+e.w,e.y+e.h/2],stroke:"#FF7D17",strokeWidth:1,lineCap:"round",lineJoin:"round",tension:1}}}),n("v-text",{ref:"gateTextR",refInFor:!0,attrs:{config:{id:"gateTextR",x:(e.x+e.w+t.room.w)/2,y:"1"==e.direction?e.y+e.h/2+5:e.y+e.h/2-t.fontSize1,text:t.pi2m(t.room.x+t.room.w-(e.x+e.w)),fontSize:t.fontSize1,fill:"#FF7D17"}}})],1):"2"==e.direction?n("v-group",[n("v-line",{ref:"gateLineT",refInFor:!0,attrs:{config:{id:"gateLineT",points:[e.x+e.w/2,t.room.y,e.x+e.w/2,e.y],stroke:"#FF7D17",strokeWidth:1,lineCap:"round",lineJoin:"round",tension:1}}}),n("v-text",{ref:"gateTextT",refInFor:!0,attrs:{config:{id:"gateTextT",x:"0"==e.direction?e.x+e.w/2:e.x+e.w/2-2*t.fontSize1,y:(e.y+t.roomPadding)/2,text:t.pi2m(e.y-t.room.y),fontSize:t.fontSize1,fill:"#FF7D17"}}})],1):"0"==e.direction?n("v-group",[n("v-line",{ref:"gateLineB",refInFor:!0,attrs:{config:{id:"gateLineB",points:[e.x+e.w/2,t.room.y+t.room.h,e.x+e.w/2,e.y+e.h],stroke:"#FF7D17",strokeWidth:1,lineCap:"round",lineJoin:"round",tension:1}}}),n("v-text",{ref:"gateTextB",refInFor:!0,attrs:{config:{id:"gateTextB",x:"0"==e.direction?e.x+e.w/2:e.x+e.w/2-2*t.fontSize1,y:(e.y+e.h+t.room.y+t.room.h)/2-t.fontSize1,text:t.pi2m(t.room.y+t.room.h-(e.y+e.h)),fontSize:t.fontSize1,fill:"#FF7D17"}}})],1):t._e()],1)})),1),n("v-layer",t._l(t.regionList,(function(e,o){return n("v-group",{key:"region"+o},[n("v-text",{attrs:{config:{id:"bedName",x:e.x+e.w/2,y:e.y+e.h/2,text:t.dict.cls[e.cls].text,fontSize:t.fontSize1,fill:"#3D3D3D"}}}),n("v-line",{ref:"bedLineT",refInFor:!0,attrs:{config:{id:"bedLineT",points:[e.x+e.w/2,t.room.y,e.x+e.w/2,e.y],stroke:"#eeeeee",strokeWidth:1,lineCap:"round",lineJoin:"round",tension:1}}}),n("v-text",{ref:"bedTextT",refInFor:!0,attrs:{config:{id:"bedTextT",x:e.x+e.w/2,y:(e.y+t.roomPadding)/2,text:t.pi2m(e.y-t.room.y),fontSize:t.fontSize1,fill:"#eeeeee"}}}),n("v-line",{ref:"bedLineB",refInFor:!0,attrs:{config:{id:"bedLineB",points:[e.x+e.w/2,t.room.y+t.room.h,e.x+e.w/2,e.y+e.h],stroke:"#01AF99",strokeWidth:1,lineCap:"round",lineJoin:"round",tension:1}}}),n("v-text",{ref:"bedTextB",refInFor:!0,attrs:{config:{id:"bedTextB",x:e.x+e.w/2,y:(e.y+e.h+t.room.y+t.room.h)/2-t.fontSize1,text:t.pi2m(t.room.y+t.room.h-(e.y+e.h)),fontSize:t.fontSize1,fill:"#01AF99"}}}),n("v-line",{ref:"bedLineL",refInFor:!0,attrs:{config:{id:"bedLineL",points:[t.room.x,e.y+e.h/2,e.x,e.y+e.h/2],stroke:"#eeeeee",strokeWidth:1,lineCap:"round",lineJoin:"round",tension:1}}}),n("v-text",{ref:"bedTextL",refInFor:!0,attrs:{config:{id:"bedTextL",x:(e.x+t.room.x)/2,y:e.y+e.h/2+5,text:t.pi2m(e.x-t.room.x),fontSize:t.fontSize1,fill:"#eeeeee"}}}),n("v-line",{ref:"bedLineR",refInFor:!0,attrs:{config:{id:"bedLineR",points:[t.room.x+t.room.w,e.y+e.h/2,e.x+e.w,e.y+e.h/2],stroke:"#01AF99",strokeWidth:1,lineCap:"round",lineJoin:"round",tension:1}}}),n("v-text",{ref:"bedTextR",refInFor:!0,attrs:{config:{id:"bedTextR",x:(e.x+e.w+t.room.x+t.room.w)/2,y:e.y+e.h/2+5,text:t.pi2m(t.room.x+t.room.w-(e.x+e.w)),fontSize:t.fontSize1,fill:"#01AF99"}}})],1)})),1)],1),n("v-uni-view",{staticClass:"c-flex jc-c ta-c stage_descr"},[n("v-uni-view",{staticClass:"desc"},[t._v(t._s(t.$t("pages.room.room.stage.desc")))]),n("v-uni-view",{staticClass:"unit"},[t._v(t._s(t.$t("pages.room.room.stage.unit")))])],1)],1),n("v-uni-view",{staticClass:"room_data"},[n("v-uni-view",{staticClass:"c-flex"},[n("v-uni-view",{staticClass:"c-flex room_data_item"},[n("v-uni-view",{staticClass:"r-flex jc-sb"},[n("v-uni-view",{staticClass:"r-flex jc-sb"},[n("v-uni-text",{staticClass:"name",staticStyle:{"margin-right":"8rpx"}},[t._v(t._s(t.$t("device.room-data.room")))]),n("u-icon",{attrs:{name:"question-circle",color:"#666",size:"18"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.questionClick(3)}}})],1)],1),t.room.editing?n("v-uni-view",{staticClass:"r-flex jc-sb data"},[n("v-uni-view",{staticClass:"c-flex"},[n("v-uni-view",{staticClass:"r-flex"},[n("v-uni-text",{staticClass:"text"},[t._v(t._s(t.$t("pages.room.room.room-data.size")))]),n("u-input",{staticClass:"value",staticStyle:{"background-color":"#eeeeee"},attrs:{type:"digit",maxlength:"4","auto-blur":!0},on:{blur:function(e){arguments[0]=e=t.$handleEvent(e),t.stopRoomEditing(t.room)}},model:{value:t.room.m_x,callback:function(e){t.$set(t.room,"m_x",e)},expression:"room.m_x"}}),n("v-uni-text",{staticClass:"x"},[t._v("x")]),n("u-input",{staticClass:"value",staticStyle:{"background-color":"#eeeeee"},attrs:{type:"digit",maxlength:"4","auto-blur":!0},on:{blur:function(e){arguments[0]=e=t.$handleEvent(e),t.stopRoomEditing(t.room)}},model:{value:t.room.m_y,callback:function(e){t.$set(t.room,"m_y",e)},expression:"room.m_y"}})],1)],1),n("v-uni-view",{staticClass:"c-flex"},[n("v-uni-view",{staticClass:"r-flex"},[n("v-uni-text",{staticClass:"text"},[t._v(t._s(t.$t("pages.room.room.room-data.height")))]),n("u-input",{staticClass:"value",staticStyle:{"background-color":"#eeeeee"},attrs:{type:"digit",maxlength:"4","auto-blur":!0},on:{blur:function(e){arguments[0]=e=t.$handleEvent(e),t.stopRoomEditing(t.room)}},model:{value:t.room.m_z,callback:function(e){t.$set(t.room,"m_z",e)},expression:"room.m_z"}})],1)],1)],1):n("v-uni-view",{staticClass:"r-flex jc-sb data",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.startRoomEditing(t.room)}}},[n("v-uni-view",{staticClass:"c-flex"},[n("v-uni-view",{staticClass:"r-flex"},[n("v-uni-text",{staticClass:"text"},[t._v(t._s(t.$t("pages.room.room.room-data.size")))]),n("v-uni-text",{staticClass:"value"},[t._v(t._s(t.room.m_x))]),n("v-uni-text",{staticClass:"x"},[t._v("x")]),n("v-uni-text",{staticClass:"value"},[t._v(t._s(t.room.m_y))])],1)],1),n("v-uni-view",{staticClass:"c-flex"},[n("v-uni-view",{staticClass:"r-flex"},[n("v-uni-text",{staticClass:"text"},[t._v(t._s(t.$t("pages.room.room.room-data.height")))]),n("v-uni-text",{staticClass:"value"},[t._v(t._s(t.room.m_z))])],1)],1)],1)],1),n("v-uni-view",{staticClass:"c-flex room_data_item"},[n("v-uni-view",{staticClass:"r-flex jc-sb"},[n("v-uni-view",[n("v-uni-text",{staticClass:"name"},[t._v(t._s(t.$t("device.room-data.device")))])],1)],1),t.device.editing?n("v-uni-view",{staticClass:"r-flex jc-sb data"},[n("v-uni-view",{staticClass:"c-flex"},[n("v-uni-view",{staticClass:"r-flex"},[n("v-uni-text",{staticClass:"text"},[t._v(t._s(t.$t("pages.room.room.room-data.distance")))]),n("u-input",{staticClass:"value",staticStyle:{"background-color":"#eeeeee"},attrs:{type:"digit",maxlength:"4","auto-blur":!0},on:{blur:function(e){arguments[0]=e=t.$handleEvent(e),t.stopDeviceEditing(t.device)}},model:{value:t.device.m_x,callback:function(e){t.$set(t.device,"m_x",e)},expression:"device.m_x"}}),n("v-uni-text",{staticClass:"x"},[t._v("x")]),n("u-input",{staticClass:"value",staticStyle:{"background-color":"#eeeeee"},attrs:{type:"digit",maxlength:"4","auto-blur":!0},on:{blur:function(e){arguments[0]=e=t.$handleEvent(e),t.stopDeviceEditing(t.device)}},model:{value:t.device.m_y,callback:function(e){t.$set(t.device,"m_y",e)},expression:"device.m_y"}})],1)],1),n("v-uni-view",{staticClass:"c-flex"},[n("v-uni-view",{staticClass:"r-flex"},[n("v-uni-text",{staticClass:"text"},[t._v(t._s(t.$t("pages.room.room.room-data.height")))]),n("u-input",{staticClass:"value",staticStyle:{"background-color":"#eeeeee"},attrs:{type:"digit",maxlength:"4","auto-blur":!0},on:{blur:function(e){arguments[0]=e=t.$handleEvent(e),t.stopDeviceEditing(t.device)}},model:{value:t.device.m_z,callback:function(e){t.$set(t.device,"m_z",e)},expression:"device.m_z"}})],1)],1)],1):n("v-uni-view",{staticClass:"r-flex jc-sb data",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.startDeviceEditing(t.device)}}},[n("v-uni-view",{staticClass:"c-flex"},[n("v-uni-view",{staticClass:"r-flex"},[n("v-uni-text",{staticClass:"text"},[t._v(t._s(t.$t("pages.room.room.room-data.distance")))]),n("v-uni-text",{staticClass:"value"},[t._v(t._s(t.device.m_x))]),n("v-uni-text",{staticClass:"x"},[t._v("x")]),n("v-uni-text",{staticClass:"value"},[t._v(t._s(t.device.m_y))])],1)],1),n("v-uni-view",{staticClass:"c-flex"},[n("v-uni-view",{staticClass:"r-flex"},[n("v-uni-text",{staticClass:"text"},[t._v(t._s(t.$t("pages.room.room.room-data.height")))]),n("v-uni-text",{staticClass:"value"},[t._v(t._s(t.device.m_z))])],1)],1)],1)],1),t._l(t.regionList,(function(e,o){return n("v-uni-view",{key:"region"+o,staticClass:"c-flex room_data_item"},[n("v-uni-view",{staticClass:"r-flex jc-sb"},[n("v-uni-view",[n("v-uni-text",{staticClass:"name"},[t._v(t._s(t.dict.cls[e.cls].text))]),n("v-uni-text",{staticClass:"desr"},[t._v(t._s("("+t.$t("pages.room.room.room-data.size")+":"+t.pi2m(e.w)+"x"+t.pi2m(e.h)+"m)"))])],1),n("v-uni-text",{staticClass:"icon iconfont icon-lajixiangshanchu delete",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.deleteBed(o)}}})],1),e.editing?n("v-uni-view",{staticClass:"r-flex jc-sb data"},[n("v-uni-view",{staticClass:"c-flex"},[n("v-uni-view",{staticClass:"r-flex"},[n("v-uni-text",{staticClass:"text"},[t._v(t._s(t.$t("pages.room.room.room-data.distance")))]),n("u-input",{staticClass:"value",staticStyle:{"background-color":"#eeeeee"},attrs:{type:"digit",maxlength:"4","auto-blur":!0},on:{blur:function(n){arguments[0]=n=t.$handleEvent(n),t.stopBedEditing(e)}},model:{value:e.m_distance1,callback:function(n){t.$set(e,"m_distance1",n)},expression:"bed.m_distance1"}}),n("v-uni-text",{staticClass:"x"},[t._v("x")]),n("u-input",{staticClass:"value",staticStyle:{"background-color":"#eeeeee"},attrs:{type:"digit",maxlength:"4","auto-blur":!0},on:{blur:function(n){arguments[0]=n=t.$handleEvent(n),t.stopBedEditing(e)}},model:{value:e.m_distance2,callback:function(n){t.$set(e,"m_distance2",n)},expression:"bed.m_distance2"}})],1)],1),n("v-uni-view",{staticClass:"c-flex"},[n("v-uni-view",{staticClass:"r-flex"},[n("v-uni-text",{staticClass:"text"},[t._v(t._s(t.$t("pages.room.room.room-data.height")))]),n("u-input",{staticClass:"value",staticStyle:{"background-color":"#eeeeee"},attrs:{type:"digit",maxlength:"4","auto-blur":!0},on:{blur:function(n){arguments[0]=n=t.$handleEvent(n),t.stopBedEditing(e)}},model:{value:e.m_z,callback:function(n){t.$set(e,"m_z",n)},expression:"bed.m_z"}})],1)],1)],1):n("v-uni-view",{staticClass:"r-flex jc-sb data",on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.startBedEditing(e)}}},[n("v-uni-view",{staticClass:"c-flex"},[n("v-uni-view",{staticClass:"r-flex"},[n("v-uni-text",{staticClass:"text"},[t._v(t._s(t.$t("pages.room.room.room-data.distance")))]),n("v-uni-text",{staticClass:"value"},[t._v(t._s(t.pi2m(t.room.x+t.room.w-(e.x+e.w))))]),n("v-uni-text",{staticClass:"x"},[t._v("x")]),n("v-uni-text",{staticClass:"value"},[t._v(t._s(t.pi2m(t.room.y+t.room.h-(e.y+e.h))))])],1)],1),n("v-uni-view",{staticClass:"c-flex"},[n("v-uni-view",{staticClass:"r-flex"},[n("v-uni-text",{staticClass:"text"},[t._v(t._s(t.$t("pages.room.room.room-data.height")))]),n("v-uni-text",{staticClass:"value"},[t._v(t._s(e.m_z))])],1)],1)],1)],1)})),t._l(t.gateList,(function(e,o){return n("v-uni-view",{key:o},[n("v-uni-view",{staticClass:"c-flex room_data_item"},[n("v-uni-view",{staticClass:"r-flex jc-sb"},[n("v-uni-view",[n("v-uni-text",{staticClass:"name"},[t._v(t._s(t.dict.direction[e.direction].text+" "+t.$t("device.room-data.region.1")))])],1),n("v-uni-text",{staticClass:"icon iconfont icon-lajixiangshanchu delete",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.deleteGate(o)}}})],1),e.editing?n("v-uni-view",{staticClass:"r-flex jc-sb data"},[n("v-uni-view",{staticClass:"c-flex"},[n("v-uni-view",{staticClass:"r-flex"},[n("v-uni-text",{staticClass:"text"},[t._v(t._s(t.$t("pages.room.room.room-data.distance")))]),n("u-input",{staticClass:"value",staticStyle:{"background-color":"#eeeeee"},attrs:{type:"digit",maxlength:"4","auto-blur":!0},on:{blur:function(n){arguments[0]=n=t.$handleEvent(n),t.stopGateEditing(e)}},model:{value:e.m_distance1,callback:function(n){t.$set(e,"m_distance1",n)},expression:"item.m_distance1"}})],1)],1),n("v-uni-view",{staticClass:"c-flex"},[n("v-uni-view",{staticClass:"r-flex"},[n("v-uni-text",{staticClass:"text"},[t._v(t._s(t.$t("pages.room.room.room-data.gate-width")))]),n("u-input",{staticClass:"value",staticStyle:{"background-color":"#eeeeee"},attrs:{type:"digit",maxlength:"4","auto-blur":!0},on:{blur:function(n){arguments[0]=n=t.$handleEvent(n),t.stopGateEditing(e)}},model:{value:e.m_width,callback:function(n){t.$set(e,"m_width",n)},expression:"item.m_width"}})],1)],1)],1):n("v-uni-view",{staticClass:"r-flex jc-sb data",on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.startGateEditing(e)}}},[n("v-uni-view",{staticClass:"c-flex"},[n("v-uni-view",{staticClass:"r-flex"},[n("v-uni-text",{staticClass:"text"},[t._v(t._s(t.$t("pages.room.room.room-data.distance")))]),"1"==e.direction?n("v-uni-text",{staticClass:"value"},[t._v(t._s(t.pi2m(e.x-t.room.x)))]):"2"==e.direction?n("v-uni-text",{staticClass:"value"},[t._v(t._s(t.pi2m(e.y-t.room.y)))]):"3"==e.direction?n("v-uni-text",{staticClass:"value"},[t._v(t._s(t.pi2m(t.room.x+t.room.w-(e.x+e.w))))]):"0"==e.direction?n("v-uni-text",{staticClass:"value"},[t._v(t._s(t.pi2m(t.room.y+t.room.h-(e.y+e.h))))]):t._e()],1)],1),n("v-uni-view",{staticClass:"c-flex"},[n("v-uni-view",{staticClass:"r-flex"},[n("v-uni-text",{staticClass:"text"},[t._v(t._s(t.$t("pages.room.room.room-data.gate-width")))]),n("v-uni-text",{staticClass:"value"},[t._v(t._s(e.m_width))])],1)],1)],1)],1)],1)}))],2)],1),n("v-uni-view",{staticClass:"popup-aera"},["selectBed"==t.showPupopType?n("MyPopup",{attrs:{title:t.$t("add")+" "+t.$t("device.room-data.region.2"),subtitle:t.$t("selected")+":"+t.selectBedSize+t.$t("pages.room.room.room-data.size"),visible:!0},on:{cancel:function(e){arguments[0]=e=t.$handleEvent(e),t.popupHide("selectBed")},confirm:function(e){arguments[0]=e=t.$handleEvent(e),t.saveBed.apply(void 0,arguments)}}},[n("v-uni-view",{staticClass:"r-flex jc-sa grid-bed"},t._l(t.dict.bedSize,(function(e,o){return n("v-uni-view",{key:o,staticClass:"c-flex jc-c bed-item",style:{"background-color":t.selectBedIndex===o?"#EEFFFD":"#EEEEEE"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.bedSelect(o,1)}}},[n("v-uni-text",{staticClass:"text",style:{color:t.selectBedIndex===o?"#01B09A":"#333333"}},[t._v(t._s(e.text))])],1)})),1)],1):"addRegion"==t.showPupopType?n("MyPopup",{attrs:{title:t.$t("add")+" "+(t.addRegionIdx&&t.dict.cls[t.addRegionIdx].text),visible:!0},on:{cancel:function(e){arguments[0]=e=t.$handleEvent(e),t.popupHide("addRegion")},confirm:function(e){arguments[0]=e=t.$handleEvent(e),t.saveRegion.apply(void 0,arguments)}}},[n("u-row",{attrs:{gutter:"16"}},[n("u-col",{attrs:{span:"3"}},[n("v-uni-text",[t._v(t._s(t.$t("pages.room.room.room-data.size"))+"(m):")])],1),n("u-col",{attrs:{span:"4"}},[n("u-input",{attrs:{type:"digit",placeholder:t.$t("input.placeholder"),required:!0},model:{value:t.customBed.x,callback:function(e){t.$set(t.customBed,"x",e)},expression:"customBed.x"}})],1),n("u-col",{attrs:{span:"1"}},[t._v("x")]),n("u-col",{attrs:{span:"4"}},[n("u-input",{attrs:{type:"digit",placeholder:t.$t("input.placeholder"),required:!0},model:{value:t.customBed.y,callback:function(e){t.$set(t.customBed,"y",e)},expression:"customBed.y"}})],1)],1),n("u-row",{staticStyle:{"margin-top":"16rpx"},attrs:{gutter:"16"}},[n("u-col",{attrs:{span:"3"}},[n("v-uni-text",[t._v(t._s(t.$t("pages.room.room.room-data.height"))+"(m)")])],1),n("u-col",{attrs:{span:"4"}},[n("u-input",{attrs:{type:"digit",placeholder:t.$t("input.placeholder"),required:!0},model:{value:t.customBed.z,callback:function(e){t.$set(t.customBed,"z",e)},expression:"customBed.z"}})],1)],1)],1):"addGate"==t.showPupopType?n("MyPopup",{attrs:{title:t.$t("add")+" "+t.$t("device.room-data.region.1"),subtitle:t.$t("pages.room.room.room-data.add-gate-desc"),visible:!0},on:{cancel:function(e){arguments[0]=e=t.$handleEvent(e),t.popupHide("addGate")},confirm:function(e){arguments[0]=e=t.$handleEvent(e),t.saveGate.apply(void 0,arguments)}}},[n("v-uni-view",{staticClass:"c-flex jc-c ta-c"},[n("v-uni-view",{staticClass:"r-flex jc-c"},[n("v-uni-view",{staticClass:"r-flex jc-c bed-item",style:{"background-color":1===t.selectGateIndex?"#EEFFFD":"#EEEEEE"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.gateSelect(1)}}},[n("v-uni-text",{staticClass:"text",style:{color:1===t.selectGateIndex?"#01B09A":"#333333"}},[t._v(t._s(t.$t("pages.room.room.room-data.gate.label10")))])],1)],1),n("v-uni-view",{staticClass:"r-flex jc-sa"},[n("v-uni-view",{staticClass:"r-flex jc-c bed-item",style:{"background-color":0===t.selectGateIndex?"#EEFFFD":"#EEEEEE"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.gateSelect(0)}}},[n("v-uni-text",{staticClass:"text",style:{color:0===t.selectGateIndex?"#01B09A":"#333333"}},[t._v(t._s(t.$t("pages.room.room.room-data.gate.label20")))])],1),n("v-uni-view",{staticClass:"r-flex jc-c bed-item",style:{"background-color":2===t.selectGateIndex?"#EEFFFD":"#EEEEEE"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.gateSelect(2)}}},[n("v-uni-text",{staticClass:"text",style:{color:2===t.selectGateIndex?"#01B09A":"#333333"}},[t._v(t._s(t.$t("pages.room.room.room-data.gate.label30")))])],1)],1),n("v-uni-view",{staticClass:"r-flex jc-c"},[n("v-uni-view",{staticClass:"r-flex jc-c bed-item",style:{"background-color":3===t.selectGateIndex?"#EEFFFD":"#EEEEEE"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.gateSelect(3)}}},[n("v-uni-text",{staticClass:"text",style:{color:3===t.selectGateIndex?"#01B09A":"#333333"}},[t._v(t._s(t.$t("pages.room.room.room-data.gate.label40")))])],1)],1)],1)],1):t._e()],1),n("v-uni-view",{staticClass:"footer-btns"},[n("u-row",{attrs:{gutter:"16"}},[n("u-col",{attrs:{span:"3"}},[n("v-uni-text",{staticClass:"text"},[t._v(t._s(t.$t("device.room-data.sence")))])],1),n("u-col",{attrs:{span:"9"}},[n("v-uni-view",{staticClass:"r-flex jc-sb"},[n("v-uni-view",{staticClass:"r-flex jc-c tag",style:{"background-color":1==t.devScene?"#01B09A":"#EEEEEE"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.changeDevScene(1)}}},[n("v-uni-text",{staticClass:"text",style:{color:1==t.devScene?"#FFF":"#333333"}},[t._v(t._s(t.$t("device.room-data.sence.1")))])],1),n("v-uni-view",{staticClass:"r-flex jc-c tag",style:{"background-color":2==t.devScene?"#01B09A":"#EEEEEE"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.changeDevScene(2)}}},[n("v-uni-text",{staticClass:"text",style:{color:2==t.devScene?"#FFF":"#333333"}},[t._v(t._s(t.$t("device.room-data.sence.2")))])],1),n("v-uni-view",{staticClass:"r-flex jc-c tag",style:{"background-color":3==t.devScene?"#01B09A":"#EEEEEE"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.changeDevScene(3)}}},[n("v-uni-text",{staticClass:"text",style:{color:3==t.devScene?"#FFF":"#333333"}},[t._v(t._s(t.$t("device.room-data.sence.3")))])],1),n("v-uni-view",{staticClass:"r-flex jc-c tag",style:{"background-color":4==t.devScene?"#01B09A":"#EEEEEE"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.changeDevScene(4)}}},[n("v-uni-text",{staticClass:"text",style:{color:4==t.devScene?"#FFF":"#333333"}},[t._v(t._s(t.$t("device.room-data.sence.4")))])],1)],1)],1)],1),n("u-row",{staticStyle:{"margin-top":"16rpx"},attrs:{gutter:"16"}},[n("u-col",{attrs:{span:"3"}},[n("v-uni-text",{staticClass:"text"},[t._v(t._s(t.$t("device.room-data.spot-type")))])],1),n("u-col",{attrs:{span:"5"}},[n("v-uni-view",{staticClass:"r-flex jc-sb"},[n("v-uni-view",{staticClass:"r-flex jc-c tag",style:{"background-color":2==t.selectSpotType?"#01B09A":"#EEEEEE"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.spotTypeSelect(2)}}},[n("v-uni-text",{staticClass:"text",style:{color:2==t.selectSpotType?"#FFF":"#333333"}},[t._v(t._s(t.$t("device.room-data.spot-type.2")))])],1),n("v-uni-view",{staticClass:"r-flex jc-c tag",style:{"background-color":1==t.selectSpotType?"#01B09A":"#EEEEEE"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.spotTypeSelect(1)}}},[n("v-uni-text",{staticClass:"text",style:{color:1==t.selectSpotType?"#FFF":"#333333"}},[t._v(t._s(t.$t("device.room-data.spot-type.1")))])],1)],1)],1)],1),n("u-row",{staticStyle:{"margin-top":"32rpx"},attrs:{gutter:"16"}},[n("u-col",{attrs:{span:"3"}},[n("v-uni-view",{staticClass:"r-flex"},[n("v-uni-text",{staticClass:"text",staticStyle:{"margin-right":"6rpx"}},[t._v(t._s(t.$t("device.room-data.region")))]),n("u-icon",{attrs:{name:"question-circle",color:"#666",size:"18"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.questionClick(2)}}})],1)],1),n("u-col",{attrs:{span:"9"}},[n("v-uni-view",{staticClass:"r-flex jc-sa"},[n("v-uni-view",{staticClass:"c-flex ta-c",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.showPupop(999)}}},[n("v-uni-text",{staticClass:"icon iconfont icon-men tubiao"}),n("v-uni-text",{staticClass:"text"},[t._v(t._s(t.$t("device.room-data.region.1")))])],1),n("v-uni-view",{staticClass:"c-flex ta-c",staticStyle:{"margin-left":"60rpx"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.showPupop(888)}}},[n("v-uni-text",{staticClass:"icon iconfont icon-chuang tubiao"}),n("v-uni-text",{staticClass:"text"},[t._v(t._s(t.$t("device.room-data.region.2")))])],1),n("v-uni-view",{staticClass:"c-flex ta-c",staticStyle:{"margin-left":"60rpx"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.showPupop(2)}}},[n("v-uni-text",{staticClass:"icon iconfont icon-zhuozi tubiao"}),n("v-uni-text",{staticClass:"text"},[t._v(t._s(t.$t("device.room-data.region.3")))])],1),n("v-uni-view",{staticClass:"c-flex ta-c",staticStyle:{"margin-left":"60rpx"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.showPupop(3)}}},[n("v-uni-text",{staticClass:"icon iconfont icon-shafa tubiao"}),n("v-uni-text",{staticClass:"text"},[t._v(t._s(t.$t("device.room-data.region.4")))])],1)],1)],1)],1),n("u-button",{staticClass:"next-btn",attrs:{type:"primary",size:"medium"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.submitRoomData.apply(void 0,arguments)}}},[t._v(t._s(t.$t("btn.save")))])],1)],1)},i=[]},"5cfa":function(t,e,n){"use strict";n("6a54");var o=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=o(n("2634")),i=o(n("2fdc")),r=o(n("24d2")),s={name:"u-col",mixins:[uni.$u.mpMixin,uni.$u.mixin,r.default],data:function(){return{width:0,parentData:{gutter:0},gridNum:12}},computed:{uJustify:function(){return"end"==this.justify||"start"==this.justify?"flex-"+this.justify:"around"==this.justify||"between"==this.justify?"space-"+this.justify:this.justify},uAlignItem:function(){return"top"==this.align?"flex-start":"bottom"==this.align?"flex-end":this.align},colStyle:function(){var t={paddingLeft:uni.$u.addUnit(uni.$u.getPx(this.parentData.gutter)/2),paddingRight:uni.$u.addUnit(uni.$u.getPx(this.parentData.gutter)/2),alignItems:this.uAlignItem,justifyContent:this.uJustify,textAlign:this.textAlign,flex:"0 0 ".concat(100/this.gridNum*this.span,"%"),marginLeft:100/12*this.offset+"%"};return uni.$u.deepMerge(t,uni.$u.addStyle(this.customStyle))}},mounted:function(){this.init()},methods:{init:function(){var t=this;return(0,i.default)((0,a.default)().mark((function e(){return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.updateParentData(),e.next=3,t.parent.getComponentWidth();case 3:t.width=e.sent;case 4:case"end":return e.stop()}}),e)})))()},updateParentData:function(){this.getParentData("u-row")},clickHandler:function(t){this.$emit("click")}}};e.default=s},"607d":function(t,e,n){"use strict";n.r(e);var o=n("5c04"),a=n("17f2");for(var i in a)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(i);n("9aa7");var r=n("828b"),s=Object(r["a"])(a["default"],o["b"],o["c"],!1,null,"38170268",null,!1,o["a"],void 0);e["default"]=s.exports},"6314e":function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return i})),n.d(e,"a",(function(){return o}));var o={uIcon:n("764e").default},a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{staticClass:"u-input",class:t.inputClass,style:[t.wrapperStyle]},[n("v-uni-view",{staticClass:"u-input__content"},[t.prefixIcon||t.$slots.prefix?n("v-uni-view",{staticClass:"u-input__content__prefix-icon"},[t._t("prefix",[n("u-icon",{attrs:{name:t.prefixIcon,size:"18",customStyle:t.prefixIconStyle}})])],2):t._e(),n("v-uni-view",{staticClass:"u-input__content__field-wrapper",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.clickHandler.apply(void 0,arguments)}}},[n("v-uni-input",{staticClass:"u-input__content__field-wrapper__field",style:[t.inputStyle],attrs:{type:t.type,focus:t.focus,cursor:t.cursor,value:t.innerValue,"auto-blur":t.autoBlur,disabled:t.disabled||t.readonly,maxlength:t.maxlength,placeholder:t.placeholder,"placeholder-style":t.placeholderStyle,"placeholder-class":t.placeholderClass,"confirm-type":t.confirmType,"confirm-hold":t.confirmHold,"hold-keyboard":t.holdKeyboard,"cursor-spacing":t.cursorSpacing,"adjust-position":t.adjustPosition,"selection-end":t.selectionEnd,"selection-start":t.selectionStart,password:t.password||"password"===t.type||!1,ignoreCompositionEvent:t.ignoreCompositionEvent},on:{input:function(e){arguments[0]=e=t.$handleEvent(e),t.onInput.apply(void 0,arguments)},blur:function(e){arguments[0]=e=t.$handleEvent(e),t.onBlur.apply(void 0,arguments)},focus:function(e){arguments[0]=e=t.$handleEvent(e),t.onFocus.apply(void 0,arguments)},confirm:function(e){arguments[0]=e=t.$handleEvent(e),t.onConfirm.apply(void 0,arguments)},keyboardheightchange:function(e){arguments[0]=e=t.$handleEvent(e),t.onkeyboardheightchange.apply(void 0,arguments)}}})],1),t.isShowClear?n("v-uni-view",{staticClass:"u-input__content__clear",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onClear.apply(void 0,arguments)}}},[n("u-icon",{attrs:{name:"close",size:"11",color:"#ffffff",customStyle:"line-height: 12px"}})],1):t._e(),t.suffixIcon||t.$slots.suffix?n("v-uni-view",{staticClass:"u-input__content__subfix-icon"},[t._t("suffix",[n("u-icon",{attrs:{name:t.suffixIcon,size:"18",customStyle:t.suffixIconStyle}})])],2):t._e()],1)],1)},i=[]},6720:function(t,e,n){"use strict";n.r(e);var o=n("1bf2"),a=n.n(o);for(var i in o)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(i);e["default"]=a.a},"67fa":function(t,e,n){var o=n("cc67");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var a=n("967d").default;a("0037a46e",o,!0,{sourceMap:!1,shadowMode:!1})},6992:function(t,e,n){"use strict";var o=n("742e"),a=n.n(o);a.a},"69e2":function(t,e,n){var o=n("b43d");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var a=n("967d").default;a("087eedf4",o,!0,{sourceMap:!1,shadowMode:!1})},"6a6a":function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return i})),n.d(e,"a",(function(){return o}));var o={uTransition:n("7285").default},a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("u-transition",{attrs:{show:t.show,"custom-class":"u-overlay",duration:t.duration,"custom-style":t.overlayStyle},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.clickHandler.apply(void 0,arguments)}}},[t._t("default")],2)},i=[]},"6b48":function(t,e,n){"use strict";n("6a54");var o=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=o(n("3389")),i={name:"u-status-bar",mixins:[uni.$u.mpMixin,uni.$u.mixin,a.default],data:function(){return{}},computed:{style:function(){var t={};return t.height=uni.$u.addUnit(uni.$u.sys().statusBarHeight,"px"),t.backgroundColor=this.bgColor,uni.$u.deepMerge(t,uni.$u.addStyle(this.customStyle))}}};e.default=i},"6c6d":function(t,e,n){var o=n("c86c");e=o(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */uni-view[data-v-3fa5722e], uni-scroll-view[data-v-3fa5722e], uni-swiper-item[data-v-3fa5722e]{display:flex;flex-direction:column;flex-shrink:0;flex-grow:0;flex-basis:auto;align-items:stretch;align-content:flex-start}.u-row[data-v-3fa5722e]{display:flex;flex-direction:row}',""]),t.exports=e},"6cc6":function(t,e,n){"use strict";n.r(e);var o=n("6314e"),a=n("6e7c");for(var i in a)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(i);n("6992");var r=n("828b"),s=Object(r["a"])(a["default"],o["b"],o["c"],!1,null,"4df7d26d",null,!1,o["a"],void 0);e["default"]=s.exports},"6e17":function(t,e,n){"use strict";n("6a54");var o=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.HandShake=void 0;var a=o(n("80b1")),i=o(n("efe5")),r=function(){function t(){(0,a.default)(this,t),this.message=null}return(0,i.default)(t,[{key:"setMessage",value:function(t){this.message=t}},{key:"getMessage",value:function(){return this.message}}]),t}();e.HandShake=r},"6e5d":function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.MsgCodeEnum=void 0;e.MsgCodeEnum={S2D:1025,S2D_ACK:1042,D2S:1026,D2S_ACK:1041}},"6e7c":function(t,e,n){"use strict";n.r(e);var o=n("745f"),a=n.n(o);for(var i in o)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(i);e["default"]=a.a},7285:function(t,e,n){"use strict";n.r(e);var o=n("f1d6"),a=n("e914");for(var i in a)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(i);n("9723");var r=n("828b"),s=Object(r["a"])(a["default"],o["b"],o["c"],!1,null,"a75f7a08",null,!1,o["a"],void 0);e["default"]=s.exports},7371:function(t,e,n){var o=n("6c6d");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var a=n("967d").default;a("0c012db0",o,!0,{sourceMap:!1,shadowMode:!1})},"742e":function(t,e,n){var o=n("e7b0");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var a=n("967d").default;a("4db797e0",o,!0,{sourceMap:!1,shadowMode:!1})},7454:function(t,e,n){"use strict";n.d(e,"b",(function(){return o})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){}));var o=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{ref:"u-col",staticClass:"u-col",class:["u-col-"+t.span],style:[t.colStyle],on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.clickHandler.apply(void 0,arguments)}}},[t._t("default")],2)},a=[]},"745f":function(t,e,n){"use strict";n("6a54");var o=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("c223"),n("aa9c");var a=o(n("a001")),i={name:"u-input",mixins:[uni.$u.mpMixin,uni.$u.mixin,a.default],data:function(){return{innerValue:"",focused:!1,firstChange:!0,changeFromInner:!1,innerFormatter:function(t){return t}}},watch:{value:{immediate:!0,handler:function(t,e){this.innerValue=t,!1===this.firstChange&&!1===this.changeFromInner&&this.valueChange(),this.firstChange=!1,this.changeFromInner=!1}}},computed:{isShowClear:function(){var t=this.clearable,e=this.readonly,n=this.focused,o=this.innerValue;return!!t&&!e&&!!n&&""!==o},inputClass:function(){var t=[],e=this.border,n=(this.disabled,this.shape);return"surround"===e&&(t=t.concat(["u-border","u-input--radius"])),t.push("u-input--".concat(n)),"bottom"===e&&(t=t.concat(["u-border-bottom","u-input--no-radius"])),t.join(" ")},wrapperStyle:function(){var t={};return this.disabled&&(t.backgroundColor=this.disabledColor),"none"===this.border?t.padding="0":(t.paddingTop="6px",t.paddingBottom="6px",t.paddingLeft="9px",t.paddingRight="9px"),uni.$u.deepMerge(t,uni.$u.addStyle(this.customStyle))},inputStyle:function(){var t={color:this.color,fontSize:uni.$u.addUnit(this.fontSize),textAlign:this.inputAlign};return t}},methods:{setFormatter:function(t){this.innerFormatter=t},onInput:function(t){var e=this,n=t.detail||{},o=n.value,a=void 0===o?"":o,i=this.formatter||this.innerFormatter,r=i(a);this.innerValue=a,this.$nextTick((function(){e.innerValue=r,e.valueChange()}))},onBlur:function(t){var e=this;this.$emit("blur",t.detail.value),uni.$u.sleep(50).then((function(){e.focused=!1})),uni.$u.formValidate(this,"blur")},onFocus:function(t){this.focused=!0,this.$emit("focus")},onConfirm:function(t){this.$emit("confirm",this.innerValue)},onkeyboardheightchange:function(){this.$emit("keyboardheightchange")},valueChange:function(){var t=this,e=this.innerValue;this.$nextTick((function(){t.$emit("input",e),t.changeFromInner=!0,t.$emit("change",e),uni.$u.formValidate(t,"change")}))},onClear:function(){var t=this;this.innerValue="",this.$nextTick((function(){t.valueChange(),t.$emit("clear")}))},clickHandler:function(){}}};e.default=i},"78e3":function(t,e,n){t.exports=n.p+"static/iconfont/iconfont.woff2"},"7a8e":function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return i})),n.d(e,"a",(function(){return o}));var o={uOverlay:n("37a6").default,uTransition:n("7285").default,uStatusBar:n("17ea").default,uIcon:n("764e").default,uSafeBottom:n("3ddf").default},a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{staticClass:"u-popup"},[t.overlay?n("u-overlay",{attrs:{show:t.show,duration:t.overlayDuration,customStyle:t.overlayStyle,opacity:t.overlayOpacity},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.overlayClick.apply(void 0,arguments)}}}):t._e(),n("u-transition",{attrs:{show:t.show,customStyle:t.transitionStyle,mode:t.position,duration:t.duration},on:{afterEnter:function(e){arguments[0]=e=t.$handleEvent(e),t.afterEnter.apply(void 0,arguments)},click:function(e){arguments[0]=e=t.$handleEvent(e),t.clickHandler.apply(void 0,arguments)}}},[n("v-uni-view",{staticClass:"u-popup__content",style:[t.contentStyle],on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.noop.apply(void 0,arguments)}}},[t.safeAreaInsetTop?n("u-status-bar"):t._e(),t._t("default"),t.closeable?n("v-uni-view",{staticClass:"u-popup__content__close",class:["u-popup__content__close--"+t.closeIconPos],attrs:{"hover-class":"u-popup__content__close--hover","hover-stay-time":"150"},on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.close.apply(void 0,arguments)}}},[n("u-icon",{attrs:{name:"close",color:"#909399",size:"18",bold:!0}})],1):t._e(),t.safeAreaInsetBottom?n("u-safe-bottom"):t._e()],2)],1)],1)},i=[]},"7fa8":function(t,e,n){"use strict";(function(t){n("6a54");var o=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.BinaryReader=void 0,n("aa9c");var a=o(n("80b1")),i=o(n("efe5")),r=function(){function e(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;(0,a.default)(this,e),this.data=t,this.offset=n}return(0,i.default)(e,[{key:"readUInt8",value:function(){if(!this.canRead(1))return null;var e=this.data instanceof t?this.data.readUInt8(this.offset):this.data.getUint8(this.offset);return this.offset+=1,e}},{key:"readInt8",value:function(){if(!this.canRead(1))return null;var e=this.data instanceof t?this.data.readInt8(this.offset):this.data.getInt8(this.offset);return this.offset+=1,e}},{key:"readUInt8Array",value:function(t){for(var e=[],n=0;n<t;n++)e.push(this.readUInt8());return e}},{key:"readUInt16",value:function(){if(!this.canRead(2))return null;var e=this.data instanceof t?this.data.readUInt16LE(this.offset):this.data.getUint16(this.offset,!0);return this.offset+=2,e}},{key:"readInt16",value:function(){if(!this.canRead(2))return null;var e=this.data instanceof t?this.data.readInt16LE(this.offset):this.data.getInt16(this.offset,!0);return this.offset+=2,e}},{key:"readUInt32",value:function(){if(!this.canRead(4))return null;var e=this.data instanceof t?this.data.readUInt32LE(this.offset):this.data.getUint32(this.offset,!0);return this.offset+=4,e}},{key:"readInt32",value:function(){if(!this.canRead(4))return null;var e=this.data instanceof t?this.data.readInt32LE(this.offset):this.data.getInt32(this.offset,!0);return this.offset+=4,e}},{key:"readFloat",value:function(){if(!this.canRead(4))return null;var e=this.data instanceof t?this.data.readFloatLE(this.offset):this.data.getFloat32(this.offset,!0);return this.offset+=4,e}},{key:"readDouble",value:function(){if(!this.canRead(8))return null;var e=this.data instanceof t?this.data.readDoubleLE(this.offset):this.data.getFloat64(this.offset,!0);return this.offset+=8,e}},{key:"readInt64",value:function(){if(!this.canRead(8))return null;var e,n;this.data instanceof t?(e=this.data.readUInt32LE(this.offset+4),n=this.data.readInt32LE(this.offset)):(e=this.data.getUint32(this.offset+4,!0),n=this.data.getInt32(this.offset,!0));var o=BigInt(n)*BigInt(4294967296)+BigInt(e);return this.offset+=8,o}},{key:"readUInt64",value:function(){if(!this.canRead(8))return null;var e,n;this.data instanceof t?(e=this.data.readUInt32LE(this.offset),n=this.data.readUInt32LE(this.offset+4)):(e=this.data.getUint32(this.offset,!0),n=this.data.getUint32(this.offset+4,!0));var o=BigInt(e)+(BigInt(n)<<BigInt(32));return this.offset+=8,o}},{key:"available",value:function(){return this.data.length-this.offset}},{key:"canRead",value:function(t){var e=this.available()>=t;return e||console.error("数据不够"),e}}]),e}();e.BinaryReader=r}).call(this,n("12e3").Buffer)},8736:function(t,e,n){t.exports=n.p+"static/iconfont/iconfont.ttf"},"8aa6":function(t,e,n){"use strict";n("6a54");var o=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.Heartbeat=void 0;var a=o(n("80b1")),i=o(n("efe5")),r=function(){function t(){(0,a.default)(this,t),this.ts=null,this.version=null,this.wifiRssi=null,this.peopleNum=null,this.iotRssi=null,this.res=[],this.cpuWorkLoad=null,this.cpu1Load=null,this.cpu2Load=null,this.memWorkLoad=null,this.iRamLoad=null,this.dRamLoad=null,this.spiRamLoad=null,this.himemLoad=null,this.res2=[],this.regionId=null,this.regionOccur=null,this.bed1Id=null,this.bed1Occur=null,this.bed2Id=null,this.bed2Occur=null,this.res3=[]}return(0,i.default)(t,[{key:"setTs",value:function(t){this.ts=t}},{key:"setVersion",value:function(t){this.version=t}},{key:"setWifiRssi",value:function(t){this.wifiRssi=t}},{key:"setPeopleNum",value:function(t){this.peopleNum=t}},{key:"setIotRssi",value:function(t){this.iotRssi=t}},{key:"setRes",value:function(t){this.res=t}},{key:"setCpuWorkLoad",value:function(t){this.cpuWorkLoad=t}},{key:"setCpu1Load",value:function(t){this.cpu1Load=t}},{key:"setCpu2Load",value:function(t){this.cpu2Load=t}},{key:"setMemWorkLoad",value:function(t){this.memWorkLoad=t}},{key:"setIRamLoad",value:function(t){this.iRamLoad=t}},{key:"setDRamLoad",value:function(t){this.dRamLoad=t}},{key:"setSpiRamLoad",value:function(t){this.spiRamLoad=t}},{key:"setHimemLoad",value:function(t){this.himemLoad=t}},{key:"setRes2",value:function(t){this.res2=t}},{key:"setRegionId",value:function(t){this.regionId=t}},{key:"setRegionOccur",value:function(t){this.regionOccur=t}},{key:"setBed1Id",value:function(t){this.bed1Id=t}},{key:"setBed1Occur",value:function(t){this.bed1Occur=t}},{key:"setBed2Id",value:function(t){this.bed2Id=t}},{key:"setBed2Occur",value:function(t){this.bed2Occur=t}},{key:"setRes3",value:function(t){this.res3=t}}]),t}();e.Heartbeat=r},"8da3":function(t,e,n){"use strict";n("6a54");var o=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.PeopleGesture=void 0;var a=o(n("80b1")),i=o(n("efe5")),r=function(){function t(){(0,a.default)(this,t),this.ts=null,this.version=null,this.res=[],this.gesture=null,this.confidence=null}return(0,i.default)(t,[{key:"setTs",value:function(t){this.ts=t}},{key:"setVersion",value:function(t){this.version=t}},{key:"setRes",value:function(t){this.res=t}},{key:"setGesture",value:function(t){this.gesture=t}},{key:"setConfidence",value:function(t){this.confidence=t}}]),t}();e.PeopleGesture=r},9437:function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.readNBytes=void 0,n("7a76"),n("c9b5"),n("aa9c");e.readNBytes=function(t,e,n){if(t.length<e+n)throw new Error("Buffer does not contain enough bytes at the specified offset.");for(var o=[],a=0;a<n;a++)o.push(t.readUInt8(e+a));return o}},9723:function(t,e,n){"use strict";var o=n("67fa"),a=n.n(o);a.a},9908:function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o={name:"MyPopup",props:{visible:{type:Boolean,default:!1},title:{type:String,default:void 0},subtitle:{type:String,default:void 0}},methods:{onCancel:function(){this.$emit("cancel")},onConfirm:function(){this.$emit("confirm")}}};e.default=o},"99c2":function(t,e,n){var o=n("c86c");e=o(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */uni-view[data-v-30282a05], uni-scroll-view[data-v-30282a05], uni-swiper-item[data-v-30282a05]{display:flex;flex-direction:column;flex-shrink:0;flex-grow:0;flex-basis:auto;align-items:stretch;align-content:flex-start}.u-popup[data-v-30282a05]{flex:1}.u-popup__content[data-v-30282a05]{background-color:#fff;position:relative}.u-popup__content--round-top[data-v-30282a05]{border-top-left-radius:0;border-top-right-radius:0;border-bottom-left-radius:10px;border-bottom-right-radius:10px}.u-popup__content--round-left[data-v-30282a05]{border-top-left-radius:0;border-top-right-radius:10px;border-bottom-left-radius:0;border-bottom-right-radius:10px}.u-popup__content--round-right[data-v-30282a05]{border-top-left-radius:10px;border-top-right-radius:0;border-bottom-left-radius:10px;border-bottom-right-radius:0}.u-popup__content--round-bottom[data-v-30282a05]{border-top-left-radius:10px;border-top-right-radius:10px;border-bottom-left-radius:0;border-bottom-right-radius:0}.u-popup__content--round-center[data-v-30282a05]{border-top-left-radius:10px;border-top-right-radius:10px;border-bottom-left-radius:10px;border-bottom-right-radius:10px}.u-popup__content__close[data-v-30282a05]{position:absolute}.u-popup__content__close--hover[data-v-30282a05]{opacity:.4}.u-popup__content__close--top-left[data-v-30282a05]{top:15px;left:15px}.u-popup__content__close--top-right[data-v-30282a05]{top:15px;right:15px}.u-popup__content__close--bottom-left[data-v-30282a05]{bottom:15px;left:15px}.u-popup__content__close--bottom-right[data-v-30282a05]{right:15px;bottom:15px}',""]),t.exports=e},"9aa7":function(t,e,n){"use strict";var o=n("d7e1"),a=n.n(o);a.a},a001:function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("64aa");var o={props:{value:{type:[String,Number],default:uni.$u.props.input.value},type:{type:String,default:uni.$u.props.input.type},fixed:{type:Boolean,default:uni.$u.props.input.fixed},disabled:{type:Boolean,default:uni.$u.props.input.disabled},disabledColor:{type:String,default:uni.$u.props.input.disabledColor},clearable:{type:Boolean,default:uni.$u.props.input.clearable},password:{type:Boolean,default:uni.$u.props.input.password},maxlength:{type:[String,Number],default:uni.$u.props.input.maxlength},placeholder:{type:String,default:uni.$u.props.input.placeholder},placeholderClass:{type:String,default:uni.$u.props.input.placeholderClass},placeholderStyle:{type:[String,Object],default:uni.$u.props.input.placeholderStyle},showWordLimit:{type:Boolean,default:uni.$u.props.input.showWordLimit},confirmType:{type:String,default:uni.$u.props.input.confirmType},confirmHold:{type:Boolean,default:uni.$u.props.input.confirmHold},holdKeyboard:{type:Boolean,default:uni.$u.props.input.holdKeyboard},focus:{type:Boolean,default:uni.$u.props.input.focus},autoBlur:{type:Boolean,default:uni.$u.props.input.autoBlur},disableDefaultPadding:{type:Boolean,default:uni.$u.props.input.disableDefaultPadding},cursor:{type:[String,Number],default:uni.$u.props.input.cursor},cursorSpacing:{type:[String,Number],default:uni.$u.props.input.cursorSpacing},selectionStart:{type:[String,Number],default:uni.$u.props.input.selectionStart},selectionEnd:{type:[String,Number],default:uni.$u.props.input.selectionEnd},adjustPosition:{type:Boolean,default:uni.$u.props.input.adjustPosition},inputAlign:{type:String,default:uni.$u.props.input.inputAlign},fontSize:{type:[String,Number],default:uni.$u.props.input.fontSize},color:{type:String,default:uni.$u.props.input.color},prefixIcon:{type:String,default:uni.$u.props.input.prefixIcon},prefixIconStyle:{type:[String,Object],default:uni.$u.props.input.prefixIconStyle},suffixIcon:{type:String,default:uni.$u.props.input.suffixIcon},suffixIconStyle:{type:[String,Object],default:uni.$u.props.input.suffixIconStyle},border:{type:String,default:uni.$u.props.input.border},readonly:{type:Boolean,default:uni.$u.props.input.readonly},shape:{type:String,default:uni.$u.props.input.shape},formatter:{type:[Function,null],default:uni.$u.props.input.formatter},ignoreCompositionEvent:{type:Boolean,default:!0}}};e.default=o},a59f:function(t,e,n){"use strict";n.r(e);var o=n("5058"),a=n("e2de");for(var i in a)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(i);n("4886");var r=n("828b"),s=Object(r["a"])(a["default"],o["b"],o["c"],!1,null,"2b676027",null,!1,o["a"],void 0);e["default"]=s.exports},a6c5:function(t,e,n){"use strict";var o=n("38d58"),a=n.n(o);a.a},a6f4:function(t,e,n){"use strict";n("6a54");var o=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.StayTooLong=void 0;var a=o(n("80b1")),i=o(n("efe5")),r=function(){function t(){(0,a.default)(this,t),this.ts=null,this.tid=null,this.conf=null,this.x=null,this.y=null,this.z=null}return(0,i.default)(t,[{key:"setTs",value:function(t){this.ts=t}},{key:"setTid",value:function(t){this.tid=t}},{key:"setConf",value:function(t){this.conf=t}},{key:"setX",value:function(t){this.x=t}},{key:"setY",value:function(t){this.y=t}},{key:"setZ",value:function(t){this.z=t}}]),t}();e.StayTooLong=r},a7003:function(t,e,n){"use strict";n("6a54");var o=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=o(n("ed4e")),i={name:"u-overlay",mixins:[uni.$u.mpMixin,uni.$u.mixin,a.default],computed:{overlayStyle:function(){var t={position:"fixed",top:0,left:0,right:0,zIndex:this.zIndex,bottom:0,"background-color":"rgba(0, 0, 0, ".concat(this.opacity,")")};return uni.$u.deepMerge(t,uni.$u.addStyle(this.customStyle))}},methods:{clickHandler:function(){this.$emit("click")}}};e.default=i},a8af:function(t,e,n){var o=n("c86c");e=o(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.u-safe-bottom[data-v-eca591a4]{width:100%}',""]),t.exports=e},aa18:function(t,e,n){var o=n("4214");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var a=n("967d").default;a("5b9574d5",o,!0,{sourceMap:!1,shadowMode:!1})},acfb:function(t,e,n){"use strict";n("6a54");var o=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=o(n("58fd")),i={name:"u-popup",mixins:[uni.$u.mpMixin,uni.$u.mixin,a.default],data:function(){return{overlayDuration:this.duration+50}},watch:{show:function(t,e){}},computed:{transitionStyle:function(){var t={zIndex:this.zIndex,position:"fixed",display:"flex"};return t[this.mode]=0,"left"===this.mode||"right"===this.mode?uni.$u.deepMerge(t,{bottom:0,top:0}):"top"===this.mode||"bottom"===this.mode?uni.$u.deepMerge(t,{left:0,right:0}):"center"===this.mode?uni.$u.deepMerge(t,{alignItems:"center","justify-content":"center",top:0,left:0,right:0,bottom:0}):void 0},contentStyle:function(){var t={},e=uni.$u.sys();e.safeAreaInsets;if("center"!==this.mode&&(t.flex=1),this.bgColor&&(t.backgroundColor=this.bgColor),this.round){var n=uni.$u.addUnit(this.round);"top"===this.mode?(t.borderBottomLeftRadius=n,t.borderBottomRightRadius=n):"bottom"===this.mode?(t.borderTopLeftRadius=n,t.borderTopRightRadius=n):"center"===this.mode&&(t.borderRadius=n)}return uni.$u.deepMerge(t,uni.$u.addStyle(this.customStyle))},position:function(){return"center"===this.mode?this.zoom?"fade-zoom":"fade":"left"===this.mode?"slide-left":"right"===this.mode?"slide-right":"bottom"===this.mode?"slide-up":"top"===this.mode?"slide-down":void 0}},methods:{overlayClick:function(){this.closeOnClickOverlay&&this.$emit("close")},close:function(t){this.$emit("close")},afterEnter:function(){this.$emit("open")},clickHandler:function(){"center"===this.mode&&this.overlayClick(),this.$emit("click")}}};e.default=i},b148:function(t,e,n){"use strict";n.r(e);var o=n("a7003"),a=n.n(o);for(var i in o)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(i);e["default"]=a.a},b260:function(t,e,n){"use strict";n.r(e);var o=n("1269"),a=n.n(o);for(var i in o)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(i);e["default"]=a.a},b2cc:function(t,e,n){"use strict";n("6a54");var o=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.SetRoomJson=void 0;var a=o(n("80b1")),i=o(n("efe5")),r=o(n("acb1")),s=o(n("cad9")),c=n("5aac"),d=function(t){(0,r.default)(n,t);var e=(0,s.default)(n);function n(){var t,o=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;return(0,a.default)(this,n),t=e.call(this),t.data=o,t}return(0,i.default)(n,[{key:"setData",value:function(t){this.data=t}},{key:"getData",value:function(){return this.data}}]),n}(c.CmdJson);e.SetRoomJson=d},b43d:function(t,e,n){var o=n("c86c");e=o(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.u-status-bar[data-v-186edb96]{width:100%}',""]),t.exports=e},b997:function(t,e,n){"use strict";n("6a54");var o=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.TargetLocation=e.PeopleEvent=void 0;var a=o(n("efe5")),i=o(n("80b1")),r=(0,a.default)((function t(){(0,i.default)(this,t),this.tid=null,this.cls=null,this.posture=null,this.x=null,this.y=null,this.z=null,this.length=null,this.width=null,this.thick=null,this.velocity=null,this.acceleration=null}));e.TargetLocation=r;var s=function(){function t(){(0,i.default)(this,t),this.ts=null,this.version=null,this.amount=null,this.targets=[]}return(0,a.default)(t,[{key:"setTs",value:function(t){this.ts=t}},{key:"setVersion",value:function(t){this.version=t}},{key:"setAmount",value:function(t){this.amount=t}},{key:"setTargets",value:function(t){this.targets=t}}]),t}();e.PeopleEvent=s},c305:function(t,e,n){"use strict";n("6a54");var o=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.BaseAck=void 0;var a=o(n("efe5")),i=o(n("80b1")),r=(0,a.default)((function t(){(0,i.default)(this,t),this.ack=null}));e.BaseAck=r},c3f6:function(t,e,n){var o=n("c86c"),a=n("2ec5"),i=n("78e3"),r=n("0b37"),s=n("8736");e=o(!1);var c=a(i),d=a(r),u=a(s);e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.u-button--primary[data-v-38170268]{color:#fff;background-color:#01b09a;border-color:#01b09a;border-width:1px;border-style:solid}@font-face{font-family:iconfont; /* Project id 3019780 */src:url('+c+') format("woff2"),url('+d+') format("woff"),url('+u+') format("truetype")}.iconfont[data-v-38170268]{font-family:iconfont!important;font-size:16px;font-style:normal;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}.icon-person-nan[data-v-38170268]::before{content:"\\e6c0"}.icon-person-nv[data-v-38170268]::before{content:"\\e6c1"}.icon-bofang1[data-v-38170268]::before{content:"\\e6bf"}.icon-a-4Gxinhao-4[data-v-38170268]::before{content:"\\e728"}.icon-a-4Gxinhao-2[data-v-38170268]::before{content:"\\e729"}.icon-a-4Gxinhao-1[data-v-38170268]::before{content:"\\e72a"}.icon-a-4Gxinhao-3[data-v-38170268]::before{content:"\\e72b"}.icon-youren[data-v-38170268]::before{content:"\\e6be"}.icon-chakanbaogao[data-v-38170268]::before{content:"\\e6bd"}.icon-shishitongji[data-v-38170268]::before{content:"\\e6bc"}.icon-zaichuang[data-v-38170268]::before{content:"\\e6bb"}.icon-jinggaodanchuang[data-v-38170268]::before{content:"\\e6ba"}.icon-guadianhua[data-v-38170268]::before{content:"\\e7a3"}.icon-dadianhua[data-v-38170268]::before{content:"\\e743"}.icon-shengyinguanbi[data-v-38170268]::before{content:"\\e8b9"}.icon-shengyin[data-v-38170268]::before{content:"\\e8ba"}.icon-qiantui[data-v-38170268]::before{content:"\\e6b9"}.icon-houla[data-v-38170268]::before{content:"\\e6b8"}.icon-kuaisuzhangkai[data-v-38170268]::before{content:"\\e6b7"}.icon-fanxuanzhuan[data-v-38170268]::before{content:"\\e6b5"}.icon-zhengxuanzhuan[data-v-38170268]::before{content:"\\e6b6"}.icon-wangxiahuishou[data-v-38170268]::before{content:"\\e6b3"}.icon-wangshanghuishou[data-v-38170268]::before{content:"\\e6b4"}.icon-wangyouhuishou[data-v-38170268]::before{content:"\\e6b1"}.icon-wangzuohuishou[data-v-38170268]::before{content:"\\e6b0"}.icon-wenhao[data-v-38170268]::before{content:"\\e6af"}.icon-wancheng[data-v-38170268]::before{content:"\\e6ae"}.icon-zanting[data-v-38170268]::before{content:"\\e6ad"}.icon-bofang[data-v-38170268]::before{content:"\\e6ac"}.icon-suoxiao[data-v-38170268]::before{content:"\\e6ab"}.icon-gengduo[data-v-38170268]::before{content:"\\e69c"}.icon-fangda[data-v-38170268]::before{content:"\\e6aa"}.icon-shebeiguanli[data-v-38170268]::before{content:"\\e6a9"}.icon-anzhuangweizhi[data-v-38170268]::before{content:"\\e6a0"}.icon-shebeianzhuang[data-v-38170268]::before{content:"\\e6a2"}.icon-jihuoshebei[data-v-38170268]::before{content:"\\e6a3"}.icon-shiyongxiaochengxu[data-v-38170268]::before{content:"\\e6a4"}.icon-jiatingguanli[data-v-38170268]::before{content:"\\e6a5"}.icon-fenxiangguanli[data-v-38170268]::before{content:"\\e6a7"}.icon-changjianwenti[data-v-38170268]::before{content:"\\e6a8"}.icon-qiangmiananzhuang[data-v-38170268]::before{content:"\\e69f"}.icon-shebeidejihuoyubangding1[data-v-38170268]::before{content:"\\e69e"}.icon-sqgl[data-v-38170268]::before{content:"\\e71b"}.icon-shishimoshi[data-v-38170268]::before{content:"\\e6b2"}.icon-ren[data-v-38170268]::before{content:"\\e69b"}.icon-renti[data-v-38170268]::before{content:"\\eccd"}.icon-guiji1[data-v-38170268]::before{content:"\\e699"}.icon-xiankuang[data-v-38170268]::before{content:"\\e69a"}.icon-zuobiaozhou[data-v-38170268]::before{content:"\\e698"}.icon-a-dianyunfenge3Ddianyun[data-v-38170268]::before{content:"\\e696"}.icon-dianyun[data-v-38170268]::before{content:"\\e697"}.icon-zhalan[data-v-38170268]::before{content:"\\e695"}.icon-yugang[data-v-38170268]::before{content:"\\e691"}.icon-dianzi[data-v-38170268]::before{content:"\\e692"}.icon-chuanglian[data-v-38170268]::before{content:"\\e693"}.icon-guizi[data-v-38170268]::before{content:"\\e694"}.icon-guanbi1[data-v-38170268]::before{content:"\\e690"}.icon-matong[data-v-38170268]::before{content:"\\e68f"}.icon-shafa[data-v-38170268]::before{content:"\\e68e"}.icon-zhuozi[data-v-38170268]::before{content:"\\e68d"}.icon-lajixiangshanchu[data-v-38170268]::before{content:"\\e68c"}.icon-chuang[data-v-38170268]::before{content:"\\e68b"}.icon-men[data-v-38170268]::before{content:"\\e689"}.icon-gaojing[data-v-38170268]::before{content:"\\e688"}.icon-wuxinhao[data-v-38170268]::before{content:"\\e68a"}.icon-cuowu[data-v-38170268]::before{content:"\\e687"}.icon-zhuanyi[data-v-38170268]::before{content:"\\e670"}.icon-gaojing1[data-v-38170268]::before{content:"\\e66f"}.icon-shuaxin[data-v-38170268]::before{content:"\\e66c"}.icon-dianhua[data-v-38170268]::before{content:"\\e66d"}.icon-tianjia[data-v-38170268]::before{content:"\\e66e"}.icon-shishidingwei[data-v-38170268]::before{content:"\\e66a"}.icon-suishenbaoshebeitubiao[data-v-38170268]::before{content:"\\e666"}.icon-caidan[data-v-38170268]::before{content:"\\e667"}.icon-yuanbaoshebei[data-v-38170268]::before{content:"\\e665"}.icon-logo[data-v-38170268]::before{content:"\\e664"}.icon-zengpin[data-v-38170268]::before{content:"\\e663"}.icon-a-120feiyongshunshidianfu1[data-v-38170268]::before{content:"\\e662"}.icon-a-120feiyongshunshidianfu[data-v-38170268]::before{content:"\\e65f"}.icon-a-xiezhuhujiaojianhurenji120[data-v-38170268]::before{content:"\\e660"}.icon-a-zhuanyejiuyuanzhidao[data-v-38170268]::before{content:"\\e661"}.icon-a-24xiaoshizhuanyerengongkefu[data-v-38170268]::before{content:"\\e65d"}.icon-xitongxiaoxi1[data-v-38170268]::before{content:"\\e65b"}.icon-jinggao[data-v-38170268]::before{content:"\\e658"}.icon-xiangxia[data-v-38170268]::before{content:"\\e655"}.icon-xiangshang[data-v-38170268]::before{content:"\\e656"}.icon-shaixuan[data-v-38170268]::before{content:"\\e64b"}.icon-jinjilianxiren3[data-v-38170268]::before{content:"\\e657"}.icon-dingdan2[data-v-38170268]::before{content:"\\e646"}.icon-guanbi[data-v-38170268]::before{content:"\\e649"}.icon-huiyuanka[data-v-38170268]::before{content:"\\e648"}.icon-beijianhuren2[data-v-38170268]::before{content:"\\e64a"}.icon-houtui[data-v-38170268]::before{content:"\\e647"}.icon-qianjin1[data-v-38170268]::before{content:"\\e633"}.icon-tiaomasaoma[data-v-38170268]::before{content:"\\e632"}.icon-wodefuwu1[data-v-38170268]::before{content:"\\e61f"}.icon-dianfujilu[data-v-38170268]::before{content:"\\e620"}.icon-qianjin[data-v-38170268]::before{content:"\\e61e"}.icon-WIFI-4[data-v-38170268]::before{content:"\\e673"}.icon-WIFI-3[data-v-38170268]::before{content:"\\e683"}.icon-WIFI-2[data-v-38170268]::before{content:"\\e684"}.icon-WIFI-1[data-v-38170268]::before{content:"\\e685"}.icon-WIFI-0[data-v-38170268]::before{content:"\\e686"}.icon-sanjiaoxing[data-v-38170268]::before{content:"\\e613"}.icon-quanbu[data-v-38170268]::before{content:"\\e61c"}.icon-fuwujilu[data-v-38170268]::before{content:"\\f010"}.icon-wodefuwu[data-v-38170268]::before{content:"\\e61d"}.icon-weibiaoti-1-02[data-v-38170268]::before{content:"\\e8dc"}.icon-xiugai[data-v-38170268]::before{content:"\\e645"}.icon-shanchu[data-v-38170268]::before{content:"\\e611"}.icon-a-icon_menchumen[data-v-38170268]::before{content:"\\e654"}.icon-shijian4[data-v-38170268]::before{content:"\\e65a"}.icon-jingzhikeshu[data-v-38170268]::before{content:"\\e709"}.icon-keting[data-v-38170268]::before{content:"\\e64e"}.icon-woshi[data-v-38170268]::before{content:"\\e64d"}.icon-anzhuangchenggong[data-v-38170268]::before{content:"\\e6e4"}.icon-ic_anzhuangshebei[data-v-38170268]::before{content:"\\e63c"}.icon-lanyawifi[data-v-38170268]::before{content:"\\e6e2"}.icon-fanhuishouye[data-v-38170268]::before{content:"\\e610"}.icon-tixingshixin[data-v-38170268]::before{content:"\\ec72"}.icon-gaojingkongxin[data-v-38170268]::before{content:"\\ed1b"}.icon-icon-xuanzejinru[data-v-38170268]::before{content:"\\e642"}.icon-fanhuizujian[data-v-38170268]::before{content:"\\e63d"}.icon-xialazujian[data-v-38170268]::before{content:"\\e63e"}.icon-gengduoxuanze[data-v-38170268]::before{content:"\\e63f"}.icon-dayukuangxuanze[data-v-38170268]::before{content:"\\e640"}.icon-touxiang[data-v-38170268]::before{content:"\\e641"}.icon-shezhi[data-v-38170268]::before{content:"\\e60f"}.icon-xitongxiaoxi[data-v-38170268]::before{content:"\\e643"}.icon-bangzhuzhongxin[data-v-38170268]::before{content:"\\e653"}.icon-fangdajing[data-v-38170268]::before{content:"\\e644"}.icon-shouye[data-v-38170268]::before{content:"\\e671"}.icon-shouyexuanzhongzhuangtai[data-v-38170268]::before{content:"\\e674"}.icon-wodeweixuanzhong[data-v-38170268]::before{content:"\\e675"}.icon-wode[data-v-38170268]::before{content:"\\e676"}.icon-baogaoweixuanzhong[data-v-38170268]::before{content:"\\e677"}.icon-baogao[data-v-38170268]::before{content:"\\e678"}.icon-shurumimatubiao1[data-v-38170268]::before{content:"\\e679"}.icon-anquan11[data-v-38170268]::before{content:"\\e67a"}.icon-yanjing-biyan1[data-v-38170268]::before{content:"\\e6e3"}.icon-yanjingzhengkai1[data-v-38170268]::before{content:"\\e67b"}.icon-shijian31[data-v-38170268]::before{content:"\\e67c"}.icon-ico_jiudianguanli_shebeiweixiudengji1[data-v-38170268]::before{content:"\\e67d"}.icon-zaichuangshijian[data-v-38170268]::before{content:"\\e67e"}.icon-lishishijian[data-v-38170268]::before{content:"\\e67f"}.icon-beijianhuren1[data-v-38170268]::before{content:"\\e680"}.icon-jinjilianxiren1[data-v-38170268]::before{content:"\\e681"}.icon-dingdan1[data-v-38170268]::before{content:"\\e682"}.icon-ribao[data-v-38170268]::before{content:"\\ea3f"}.icon-lichuangjiance[data-v-38170268]::before{content:"\\e63b"}.icon-shijian3[data-v-38170268]::before{content:"\\e8b8"}.icon-shishidongtai[data-v-38170268]::before{content:"\\e639"}.icon-zhenduanbaogao[data-v-38170268]::before{content:"\\e638"}.icon-renyuanjinchu[data-v-38170268]::before{content:"\\e652"}.icon-weixintongzhi[data-v-38170268]::before{content:"\\e6d8"}.icon-qita[data-v-38170268]::before{content:"\\e637"}.icon-weishengjian[data-v-38170268]::before{content:"\\e636"}.icon-huodongshuju[data-v-38170268]::before{content:"\\e631"}.icon-xuanzhong1[data-v-38170268]::before{content:"\\e60e"}.icon-dingwei[data-v-38170268]::before{content:"\\e6a6"}.icon-weixuanzhong[data-v-38170268]::before{content:"\\e630"}.icon-xuanzhong[data-v-38170268]::before{content:"\\e651"}.icon-ico_jiudianguanli_shebeiweixiudengji[data-v-38170268]::before{content:"\\e852"}.icon-tianjiazujian[data-v-38170268]::before{content:"\\e62f"}.icon-rili[data-v-38170268]::before{content:"\\e62e"}.icon-zuidatingliushichang[data-v-38170268]::before{content:"\\e62d"}.icon-huodongjuli[data-v-38170268]::before{content:"\\e62c"}.icon-renyuanzhuangtai[data-v-38170268]::before{content:"\\e62b"}.icon-duanxintongzhi[data-v-38170268]::before{content:"\\e62a"}.icon-yuyintongzhi[data-v-38170268]::before{content:"\\e629"}.icon-huxijiance[data-v-38170268]::before{content:"\\e628"}.icon-xinshuaijiance[data-v-38170268]::before{content:"\\e627"}.icon-jiuzhijiance[data-v-38170268]::before{content:"\\e626"}.icon-shuaidaojiance[data-v-38170268]::before{content:"\\e625"}.icon-dingdan[data-v-38170268]::before{content:"\\e623"}.icon-jinjilianxiren[data-v-38170268]::before{content:"\\e622"}.icon-beijianhuren[data-v-38170268]::before{content:"\\e621"}.icon-anquan1[data-v-38170268]::before{content:"\\e8ab"}.icon-shurumimatubiao[data-v-38170268]::before{content:"\\e634"}.icon-denglu-yonghu[data-v-38170268]::before{content:"\\e669"}.icon-yanjing-biyan[data-v-38170268]::before{content:"\\e63a"}.icon-jianchabaogao[data-v-38170268]::before{content:"\\e61b"}.icon-shebeigaojing[data-v-38170268]::before{content:"\\e61a"}.icon-shijian2[data-v-38170268]::before{content:"\\e619"}.icon-xinshuai[data-v-38170268]::before{content:"\\e618"}.icon-huxi[data-v-38170268]::before{content:"\\e617"}.icon-paozhuoderen[data-v-38170268]::before{content:"\\e616"}.icon-baogaolimiandetubiao[data-v-38170268]::before{content:"\\e612"}.icon-shuomingshu[data-v-38170268]::before{content:"\\e69d"}.icon-caozuoshuomingshushuomingshu[data-v-38170268]::before{content:"\\e600"}.icon-zhinengmensuo[data-v-38170268]::before{content:"\\e60d"}.icon-dangtianshichang[data-v-38170268]::before{content:"\\e7ee"}.icon-Dxuanzhuan[data-v-38170268]::before{content:"\\e64c"}.icon-yewan[data-v-38170268]::before{content:"\\e66b"}.icon-icon03[data-v-38170268]::before{content:"\\e60c"}.icon-baitian-qing[data-v-38170268]::before{content:"\\e672"}.icon-huodonggaojing[data-v-38170268]::before{content:"\\e615"}.icon-listziyouhuodong[data-v-38170268]::before{content:"\\e716"}.icon-shijian1[data-v-38170268]::before{content:"\\e635"}.icon-shijian[data-v-38170268]::before{content:"\\e614"}.icon-chakan[data-v-38170268]::before{content:"\\e65c"}.icon-winfo-icon-chakanbaogao[data-v-38170268]::before{content:"\\e64f"}.icon-wuyinsi1[data-v-38170268]::before{content:"\\e60b"}.icon-shoushi[data-v-38170268]::before{content:"\\e60a"}.icon-tizheng[data-v-38170268]::before{content:"\\e607"}.icon-zitai[data-v-38170268]::before{content:"\\e606"}.icon-guiji[data-v-38170268]::before{content:"\\e605"}.icon-wuyinsi[data-v-38170268]::before{content:"\\e604"}.icon-anquan[data-v-38170268]::before{content:"\\e603"}.icon-gaojingdu[data-v-38170268]::before{content:"\\e602"}.icon-tubiao1[data-v-38170268]::before{content:"\\e601"}.icon-caozuorizhi[data-v-38170268]::before{content:"\\e668"}.icon-rizhiguanli[data-v-38170268]::before{content:"\\e65e"}.icon-xitongrizhi[data-v-38170268]::before{content:"\\e608"}.icon-shujucaiji[data-v-38170268]::before{content:"\\e6a1"}.icon-xiaochengxu[data-v-38170268]::before{content:"\\e624"}.icon-fangjianweihu[data-v-38170268]::before{content:"\\e659"}.icon-connectdevice[data-v-38170268]::before{content:"\\e6e7"}.icon-disconnectdevice[data-v-38170268]::before{content:"\\e6ea"}.icon-qiyexinxi[data-v-38170268]::before{content:"\\e609"}.icon-shebei[data-v-38170268]::before{content:"\\e650"}.icon-web-icon-[data-v-38170268]::before{content:"\\e70d"}.main[data-v-38170268]{background-color:#fff;overflow:auto}.main .stage[data-v-38170268]{box-shadow:%?0?% %?24?% %?48?% %?0?% rgba(0,0,0,.04);border-radius:%?16?%}.main .stage .stage_descr .desc[data-v-38170268]{height:%?28?%;font-size:%?24?%;color:#999}.main .stage .stage_descr .unit[data-v-38170268]{height:%?28?%;margin-top:%?28?%;font-size:%?22?%;color:#3d3d3d}.main .room_data[data-v-38170268]{padding-bottom:%?500?%}.main .room_data .room_data_item[data-v-38170268]{margin:%?24?% %?24?% 0 %?24?%;padding:%?24?%;box-shadow:%?0?% %?24?% %?48?% %?0?% rgba(0,0,0,.04);border-radius:%?16?%}.main .room_data .room_data_item .name[data-v-38170268]{color:#333;font-size:%?32?%}.main .room_data .room_data_item .desr[data-v-38170268]{color:#999;font-size:%?22?%;margin-left:%?16?%}.main .room_data .room_data_item .delete[data-v-38170268]{font-size:%?52?%}.main .room_data .room_data_item .data[data-v-38170268]{margin-top:%?16?%}.main .room_data .room_data_item .data .text[data-v-38170268]{width:%?106?%;line-height:%?64?%;font-size:%?26?%;color:#666}.main .room_data .room_data_item .data .x[data-v-38170268]{font-size:%?30?%;color:#666;padding-left:%?16?%;padding-right:%?16?%}.main .room_data .room_data_item .data .value[data-v-38170268]{width:%?100?%;font-size:%?40?%;color:#333}.main .popup-aera .grid-bed[data-v-38170268]{flex-wrap:wrap;margin:%?32?%}.main .popup-aera .bed-item[data-v-38170268]{width:%?180?%;height:%?72?%;background-color:#eee;border-radius:%?16?%;margin-left:%?16?%;margin-right:%?16?%;margin-top:%?32?%}.main .popup-aera .text[data-v-38170268]{color:#333;font-size:%?32?%;font-weight:500;text-align:center;white-space:nowrap;line-height:%?36?%}.footer-btns[data-v-38170268]{position:fixed;left:%?0?%;bottom:%?0?%;width:100%;padding-top:%?10?%;padding-left:%?20?%;padding-right:%?20?%;padding-bottom:%?40?%;box-sizing:border-box;z-index:999;margin:0 auto;background-color:#fff}.footer-btns .next-btn[data-v-38170268]{margin-top:%?20?%}.footer-btns .text[data-v-38170268]{font-size:%?32?%;color:#666}.footer-btns .tubiao[data-v-38170268]{font-size:%?64?%;color:#666}.footer-btns .tag[data-v-38170268]{min-width:%?96?%;padding:%?12?%;background-color:#eee;border-radius:%?16?%}',""]),t.exports=e},c639:function(t,e,n){"use strict";n("6a54");var o=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=o(n("9b1b")),i=o(n("54f2")),r=o(n("2ab1")),s={name:"u-transition",data:function(){return{inited:!1,viewStyle:{},status:"",transitionEnded:!1,display:!1,classes:""}},computed:{mergeStyle:function(){var t=this.viewStyle,e=this.customStyle;return(0,a.default)((0,a.default)({transitionDuration:"".concat(this.duration,"ms"),transitionTimingFunction:this.timingFunction},uni.$u.addStyle(e)),t)}},mixins:[uni.$u.mpMixin,uni.$u.mixin,r.default,i.default],watch:{show:{handler:function(t){t?this.vueEnter():this.vueLeave()},immediate:!0}}};e.default=s},c8f6:function(t,e,n){"use strict";n("6a54");var o=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.JsonMsg=void 0;var a=o(n("efe5")),i=o(n("80b1")),r=(0,a.default)((function t(e,n){(0,i.default)(this,t),this.type=e,this.msg=n}));e.JsonMsg=r},ca17:function(t,e,n){"use strict";n.r(e);var o=n("0c7c"),a=n("b260");for(var i in a)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(i);n("3a46");var r=n("828b"),s=Object(r["a"])(a["default"],o["b"],o["c"],!1,null,"3fa5722e",null,!1,o["a"],void 0);e["default"]=s.exports},cc67:function(t,e,n){var o=n("c86c");e=o(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */uni-view[data-v-a75f7a08], uni-scroll-view[data-v-a75f7a08], uni-swiper-item[data-v-a75f7a08]{display:flex;flex-direction:column;flex-shrink:0;flex-grow:0;flex-basis:auto;align-items:stretch;align-content:flex-start}\r\n/**\r\n * vue版本动画内置的动画模式有如下：\r\n * fade：淡入\r\n * zoom：缩放\r\n * fade-zoom：缩放淡入\r\n * fade-up：上滑淡入\r\n * fade-down：下滑淡入\r\n * fade-left：左滑淡入\r\n * fade-right：右滑淡入\r\n * slide-up：上滑进入\r\n * slide-down：下滑进入\r\n * slide-left：左滑进入\r\n * slide-right：右滑进入\r\n */.u-fade-enter-active[data-v-a75f7a08],\r\n.u-fade-leave-active[data-v-a75f7a08]{transition-property:opacity}.u-fade-enter[data-v-a75f7a08],\r\n.u-fade-leave-to[data-v-a75f7a08]{opacity:0}.u-fade-zoom-enter[data-v-a75f7a08],\r\n.u-fade-zoom-leave-to[data-v-a75f7a08]{-webkit-transform:scale(.95);transform:scale(.95);opacity:0}.u-fade-zoom-enter-active[data-v-a75f7a08],\r\n.u-fade-zoom-leave-active[data-v-a75f7a08]{transition-property:opacity,-webkit-transform;transition-property:transform,opacity;transition-property:transform,opacity,-webkit-transform}.u-fade-down-enter-active[data-v-a75f7a08],\r\n.u-fade-down-leave-active[data-v-a75f7a08],\r\n.u-fade-left-enter-active[data-v-a75f7a08],\r\n.u-fade-left-leave-active[data-v-a75f7a08],\r\n.u-fade-right-enter-active[data-v-a75f7a08],\r\n.u-fade-right-leave-active[data-v-a75f7a08],\r\n.u-fade-up-enter-active[data-v-a75f7a08],\r\n.u-fade-up-leave-active[data-v-a75f7a08]{transition-property:opacity,-webkit-transform;transition-property:opacity,transform;transition-property:opacity,transform,-webkit-transform}.u-fade-up-enter[data-v-a75f7a08],\r\n.u-fade-up-leave-to[data-v-a75f7a08]{-webkit-transform:translate3d(0,100%,0);transform:translate3d(0,100%,0);opacity:0}.u-fade-down-enter[data-v-a75f7a08],\r\n.u-fade-down-leave-to[data-v-a75f7a08]{-webkit-transform:translate3d(0,-100%,0);transform:translate3d(0,-100%,0);opacity:0}.u-fade-left-enter[data-v-a75f7a08],\r\n.u-fade-left-leave-to[data-v-a75f7a08]{-webkit-transform:translate3d(-100%,0,0);transform:translate3d(-100%,0,0);opacity:0}.u-fade-right-enter[data-v-a75f7a08],\r\n.u-fade-right-leave-to[data-v-a75f7a08]{-webkit-transform:translate3d(100%,0,0);transform:translate3d(100%,0,0);opacity:0}.u-slide-down-enter-active[data-v-a75f7a08],\r\n.u-slide-down-leave-active[data-v-a75f7a08],\r\n.u-slide-left-enter-active[data-v-a75f7a08],\r\n.u-slide-left-leave-active[data-v-a75f7a08],\r\n.u-slide-right-enter-active[data-v-a75f7a08],\r\n.u-slide-right-leave-active[data-v-a75f7a08],\r\n.u-slide-up-enter-active[data-v-a75f7a08],\r\n.u-slide-up-leave-active[data-v-a75f7a08]{transition-property:-webkit-transform;transition-property:transform;transition-property:transform,-webkit-transform}.u-slide-up-enter[data-v-a75f7a08],\r\n.u-slide-up-leave-to[data-v-a75f7a08]{-webkit-transform:translate3d(0,100%,0);transform:translate3d(0,100%,0)}.u-slide-down-enter[data-v-a75f7a08],\r\n.u-slide-down-leave-to[data-v-a75f7a08]{-webkit-transform:translate3d(0,-100%,0);transform:translate3d(0,-100%,0)}.u-slide-left-enter[data-v-a75f7a08],\r\n.u-slide-left-leave-to[data-v-a75f7a08]{-webkit-transform:translate3d(-100%,0,0);transform:translate3d(-100%,0,0)}.u-slide-right-enter[data-v-a75f7a08],\r\n.u-slide-right-leave-to[data-v-a75f7a08]{-webkit-transform:translate3d(100%,0,0);transform:translate3d(100%,0,0)}.u-zoom-enter-active[data-v-a75f7a08],\r\n.u-zoom-leave-active[data-v-a75f7a08]{transition-property:-webkit-transform;transition-property:transform;transition-property:transform,-webkit-transform}.u-zoom-enter[data-v-a75f7a08],\r\n.u-zoom-leave-to[data-v-a75f7a08]{-webkit-transform:scale(.95);transform:scale(.95)}',""]),t.exports=e},cf2a:function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.TRANSFER_ON=e.TRANSFER_OFF=e.CLOUD_POINTS_ON=e.CLOUD_POINTS_OFF=void 0;e.CLOUD_POINTS_ON="01 04 06 00 30 01";e.CLOUD_POINTS_OFF="01 04 06 00 30 00";e.TRANSFER_ON="41 08 05 00 01";e.TRANSFER_OFF="41 08 05 00 00"},d1ab:function(t,e,n){"use strict";n("6a54");var o=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.AppStatsReporter=e.AppStatsItem=void 0,n("bf0f"),n("de6c");var a=o(n("80b1")),i=o(n("efe5")),r=function(){function t(){(0,a.default)(this,t),this.type=null,this.app=null,this.bitTable=null,this.numSpans=null,this.values=[]}return(0,i.default)(t,[{key:"setType",value:function(t){this.type=t}},{key:"setApp",value:function(t){this.app=t}},{key:"setBitTable",value:function(t){this.bitTable=t}},{key:"setNumSpans",value:function(t){this.numSpans=t}},{key:"setValues",value:function(t){this.values=t}}]),t}();e.AppStatsItem=r;var s=function(){function t(){(0,a.default)(this,t),this.ts=null,this.version=null,this.data=[]}return(0,i.default)(t,[{key:"setTs",value:function(t){this.ts=t}},{key:"setVersion",value:function(t){this.version=t}},{key:"setData",value:function(t){this.data=t}}]),t}();e.AppStatsReporter=s},d27f:function(t,e,n){"use strict";(function(t){n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.hexString2Uint8Array=e.hexString2Buffer=void 0,n("4db2"),n("bf0f"),n("c976"),n("4d8f"),n("7b97"),n("668a"),n("c5b7"),n("8ff5"),n("2378"),n("641a"),n("64e0"),n("cce3"),n("efba"),n("d009"),n("bd7d"),n("7edd"),n("d798"),n("f547"),n("5e54"),n("b60a"),n("8c18"),n("12973"),n("f991"),n("198e"),n("8557"),n("63b1"),n("1954"),n("1cf1"),n("fd3c"),n("5c47"),n("2c10"),n("e966"),n("a1c1");e.hexString2Uint8Array=function(t){return new Uint8Array(t.match(/[\da-fA-F]{2}/g).map((function(t){return parseInt(t,16)})))};e.hexString2Buffer=function(e){return t.from(e.replace(/\s+/g,""),"hex")}}).call(this,n("12e3").Buffer)},d55b:function(t,e,n){"use strict";var o=n("69e2"),a=n.n(o);a.a},d7e1:function(t,e,n){var o=n("c3f6");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var a=n("967d").default;a("1d261bbe",o,!0,{sourceMap:!1,shadowMode:!1})},e184:function(t,e,n){"use strict";n.d(e,"b",(function(){return o})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){}));var o=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticClass:"u-safe-bottom",class:[!this.isNvue&&"u-safe-area-inset-bottom"],style:[this.style]})},a=[]},e2de:function(t,e,n){"use strict";n.r(e);var o=n("9908"),a=n.n(o);for(var i in o)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(i);e["default"]=a.a},e7b0:function(t,e,n){var o=n("c86c");e=o(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */uni-view[data-v-4df7d26d], uni-scroll-view[data-v-4df7d26d], uni-swiper-item[data-v-4df7d26d]{display:flex;flex-direction:column;flex-shrink:0;flex-grow:0;flex-basis:auto;align-items:stretch;align-content:flex-start}.u-input[data-v-4df7d26d]{display:flex;flex-direction:row;align-items:center;justify-content:space-between;flex:1}.u-input--radius[data-v-4df7d26d], .u-input--square[data-v-4df7d26d]{border-radius:4px}.u-input--no-radius[data-v-4df7d26d]{border-radius:0}.u-input--circle[data-v-4df7d26d]{border-radius:100px}.u-input__content[data-v-4df7d26d]{flex:1;display:flex;flex-direction:row;align-items:center;justify-content:space-between}.u-input__content__field-wrapper[data-v-4df7d26d]{position:relative;display:flex;flex-direction:row;margin:0;flex:1}.u-input__content__field-wrapper__field[data-v-4df7d26d]{line-height:26px;text-align:left;color:#303133;height:24px;font-size:15px;flex:1}.u-input__content__clear[data-v-4df7d26d]{width:20px;height:20px;border-radius:100px;background-color:#c6c7cb;display:flex;flex-direction:row;align-items:center;justify-content:center;-webkit-transform:scale(.82);transform:scale(.82);margin-left:4px}.u-input__content__subfix-icon[data-v-4df7d26d]{margin-left:4px}.u-input__content__prefix-icon[data-v-4df7d26d]{margin-right:4px}',""]),t.exports=e},e914:function(t,e,n){"use strict";n.r(e);var o=n("c639"),a=n.n(o);for(var i in o)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(i);e["default"]=a.a},e91c:function(t,e,n){"use strict";n.r(e);var o=n("5cfa"),a=n.n(o);for(var i in o)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(i);e["default"]=a.a},ed4e:function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("64aa");var o={props:{show:{type:Boolean,default:uni.$u.props.overlay.show},zIndex:{type:[String,Number],default:uni.$u.props.overlay.zIndex},duration:{type:[String,Number],default:uni.$u.props.overlay.duration},opacity:{type:[String,Number],default:uni.$u.props.overlay.opacity}}};e.default=o},eedb:function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.parseDevRoomData=void 0,n("e838"),n("fd3c");e.parseDevRoomData=function(t){if(console.log("parseDevRoomData start:",t),t){var e={};return e.room={x:parseFloat(t.area[1]-t.area[0]).toFixed(2),y:parseFloat(t.area[3]-t.area[2]).toFixed(2),z:parseFloat(t.area[4]).toFixed(2)},e.roomDevice={x:0-parseFloat(t.area[0]).toFixed(2),y:0-parseFloat(t.area[2]).toFixed(2),z:parseFloat(t.devPos[0]).toFixed(2),atilt:t.devPos[1],etilt:t.devPos[2],atiltfov:t.devPos[3],etiltfov:t.devPos[4],mount:t.devPos[5]},e.roomEntrances=t.entrys.map((function(t){return{gid:t[0],cls:t[1],direction:t[2],length:parseFloat(t[3]).toFixed(2),width:parseFloat(t[4]).toFixed(2),hight:parseFloat(t[5]).toFixed(2)}})),e.roomRegions=t.regions.map((function(e){return{rid:e[0],cls:e[1],positionX:parseFloat(e[2]-t.area[0]).toFixed(2),positionY:parseFloat(e[3]-t.area[2]).toFixed(2),scaleX:parseFloat(e[4]).toFixed(2),scaleY:parseFloat(e[5]).toFixed(2),scaleZ:parseFloat(e[6]).toFixed(2),rotation:e[7]}})),e.type=t.type,console.log("parseDevRoomData end:",e),e}}},f1d6:function(t,e,n){"use strict";n.d(e,"b",(function(){return o})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){}));var o=function(){var t=this,e=t.$createElement,n=t._self._c||e;return t.inited?n("v-uni-view",{ref:"u-transition",staticClass:"u-transition",class:t.classes,style:[t.mergeStyle],on:{touchmove:function(e){arguments[0]=e=t.$handleEvent(e),t.noop.apply(void 0,arguments)},click:function(e){arguments[0]=e=t.$handleEvent(e),t.clickHandler.apply(void 0,arguments)}}},[t._t("default")],2):t._e()},a=[]},f574:function(t,e,n){var o=n("1320");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var a=n("967d").default;a("7bc5bc38",o,!0,{sourceMap:!1,shadowMode:!1})},f854:function(t,e,n){"use strict";n.r(e);var o=n("6b48"),a=n.n(o);for(var i in o)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(i);e["default"]=a.a},fe5f:function(t,e,n){var o=n("305b");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var a=n("967d").default;a("2d938aa3",o,!0,{sourceMap:!1,shadowMode:!1})}}]);