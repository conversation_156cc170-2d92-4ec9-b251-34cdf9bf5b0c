(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-index-index~pages-room-room"],{"014e":function(e,t,r){var n=r("c86c");t=n(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */uni-view[data-v-76271e56], uni-scroll-view[data-v-76271e56], uni-swiper-item[data-v-76271e56]{display:flex;flex-direction:column;flex-shrink:0;flex-grow:0;flex-basis:auto;align-items:stretch;align-content:flex-start}.u-button[data-v-76271e56]{width:100%}.u-button__text[data-v-76271e56]{white-space:nowrap;line-height:1}.u-button[data-v-76271e56]:before{position:absolute;top:50%;left:50%;width:100%;height:100%;border:inherit;border-radius:inherit;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%);opacity:0;content:" ";background-color:#000;border-color:#000}.u-button--active[data-v-76271e56]:before{opacity:.15}.u-button__icon + .u-button__text[data-v-76271e56]:not(:empty), .u-button__loading-text[data-v-76271e56]{margin-left:4px}.u-button--plain.u-button--primary[data-v-76271e56]{color:#3c9cff}.u-button--plain.u-button--info[data-v-76271e56]{color:#909399}.u-button--plain.u-button--success[data-v-76271e56]{color:#5ac725}.u-button--plain.u-button--error[data-v-76271e56]{color:#f56c6c}.u-button--plain.u-button--warning[data-v-76271e56]{color:#f56c6c}.u-button[data-v-76271e56]{height:40px;position:relative;align-items:center;justify-content:center;display:flex;flex-direction:row;box-sizing:border-box;flex-direction:row}.u-button__text[data-v-76271e56]{font-size:15px}.u-button__loading-text[data-v-76271e56]{font-size:15px;margin-left:4px}.u-button--large[data-v-76271e56]{width:100%;height:50px;padding:0 15px}.u-button--normal[data-v-76271e56]{padding:0 12px;font-size:14px}.u-button--small[data-v-76271e56]{min-width:60px;height:30px;padding:0 8px;font-size:12px}.u-button--mini[data-v-76271e56]{height:22px;font-size:10px;min-width:50px;padding:0 8px}.u-button--disabled[data-v-76271e56]{opacity:.5}.u-button--info[data-v-76271e56]{color:#323233;background-color:#fff;border-color:#ebedf0;border-width:1px;border-style:solid}.u-button--success[data-v-76271e56]{color:#fff;background-color:#5ac725;border-color:#5ac725;border-width:1px;border-style:solid}.u-button--primary[data-v-76271e56]{color:#fff;background-color:#3c9cff;border-color:#3c9cff;border-width:1px;border-style:solid}.u-button--error[data-v-76271e56]{color:#fff;background-color:#f56c6c;border-color:#f56c6c;border-width:1px;border-style:solid}.u-button--warning[data-v-76271e56]{color:#fff;background-color:#f9ae3d;border-color:#f9ae3d;border-width:1px;border-style:solid}.u-button--block[data-v-76271e56]{display:flex;flex-direction:row;width:100%}.u-button--circle[data-v-76271e56]{border-top-right-radius:100px;border-top-left-radius:100px;border-bottom-left-radius:100px;border-bottom-right-radius:100px}.u-button--square[data-v-76271e56]{border-bottom-left-radius:3px;border-bottom-right-radius:3px;border-top-left-radius:3px;border-top-right-radius:3px}.u-button__icon[data-v-76271e56]{min-width:1em;line-height:inherit!important;vertical-align:top}.u-button--plain[data-v-76271e56]{background-color:#fff}.u-button--hairline[data-v-76271e56]{border-width:.5px!important}',""]),e.exports=t},"030a":function(e,t,r){"use strict";e.exports="function"==typeof Bun&&Bun&&"string"==typeof Bun.version},"0578":function(e,t,r){"use strict";r("6a54"),r("0c26");var n=r("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.arrayIntersect=void 0,t.calculateStats=function(e,t){var r=e.map((function(e){return e[t]}));if(!r||0==r.length)return{max:0,min:0,average:0,median:0};var n=Math.max.apply(Math,(0,o.default)(r)),i=Math.min.apply(Math,(0,o.default)(r)),s=r.reduce((function(e,t){return e+t}),0)/r.length,a=r.sort((function(e,t){return e-t})),c=a.length%2===0?(a[a.length/2]+a[a.length/2-1])/2:a[Math.floor(a.length/2)];return{max:n,min:i,average:s.toFixed(0),median:c}},t.cloneObj=t.clickCall=void 0,t.convertTimeToValues=function(e,t,r){if(!e)return;return e.map((function(e){var n=Math.floor((new Date(e.startX)-t)/(r-t)*1e3),i=Math.floor((new Date(e.endX)-t)/(r-t)*1e3);return[n,i,e.height]}))},t.convertToMonthDayFormat=function(e){if(!e)return"";var t=new Date(e),r=t.getMonth()+1,n=t.getDate();return r+"/"+n},t.dateFormat=t.copy=void 0,t.debounce=function(e,t){var r;return function(){var n=this,i=arguments;r&&clearTimeout(r),r=setTimeout((function(){e.apply(n,i)}),t)}},t.decodeBase64ToJson=function(e){try{var t=decodeURIComponent(atob(e)),r=JSON.parse(t);return r}catch(n){return void console.error("解码失败:",n)}},t.encodeJsonToBase64=function(e){var t=JSON.stringify(e),r=btoa(encodeURIComponent(t));return r},t.formatDate=void 0,t.formatSecond2String=function(e){var t=Math.floor(e/3600),r=Math.floor(e%3600/60),n=e%60,i="";t&&(i+=t+"小时");r&&(i+=r+"分钟");n&&(i+=n+"秒");return i},t.getBirthDate=function(e){var t,r,n;return 15===e.length||18===e.length?(15===e.length?(t=e.substring(6,8),r=e.substring(8,10),n=e.substring(10,12)):18===e.length&&(t=e.substring(6,10),r=e.substring(10,12),n=e.substring(12,14)),t+"-"+r+"-"+n):void console.log("无法提取出生年月")},t.getNextDay=function(e){if(!e)return"";var t=new Date(e);return t.setDate(t.getDate()+1),t.toISOString().split("T")[0]},t.getPreviousDay=function(e){if(!e)return"";var t=new Date(e);return t.setDate(t.getDate()-1),t.toISOString().split("T")[0]},t.getSFMByM=t.getSFM=void 0,t.getUrlParam=function(e,t){var r=new RegExp("(^|&)"+t+"=([^&]*)(&|$)"),n=e.substr(e.indexOf("?")+1).match(r);return null!=n?decodeURIComponent(n[2]):""},t.isObject=t.isNull=t.isEmptyObject=t.isEmpty=t.isArray=t.inArray=void 0,t.isUrl=function(e){return/^(?:(?:https?|ftp):\/\/)?(?:www\.)?(?:[a-z0-9-]+\.)*[a-z0-9-]+\.[a-z]{2,}(?:\/[^\s]*)?$/i.test(p(e))},t.isUrlNeedHttp=function(e){return/^https?:\/\/(([a-zA-Z0-9_-])+(\.)?)*(:\d+)?(\/((\.)?(\?)?=?&?[a-zA-Z0-9_-](\?)?)*)*$/i.test(p(e))},t.jsonToGetParams=function(e){var t=new URLSearchParams;return function e(r,n){for(var i in r)if(r.hasOwnProperty(i)){var o=r[i],a=Array.isArray(r)?"":i,c=n?"".concat(n,"[").concat(a,"]"):a;if(null!==o&&"object"===(0,s.default)(o))e(o,c);else{var u=c||i;t.append(u,o)}}}(e),t.toString()},t.jsonToGetParams4app=function(e){var t=[];return function e(r,n){for(var i in r)if(r.hasOwnProperty(i)){var o=r[i],a=Array.isArray(r)?"":i,c=n?"".concat(n,"[").concat(encodeURIComponent(a),"]"):encodeURIComponent(a);if(null!==o&&"object"===(0,s.default)(o))e(o,c);else{var u=c||encodeURIComponent(a);t.push("".concat(u,"=").concat(encodeURIComponent(o)))}}}(e),t.join("&")},t.limitTime=function(e,t,r){return h(e)>h(t)?(r&&uni.showModal({content:"最晚不迟于".concat(t,",已改为").concat(t),showCancel:!1,confirmText:"关闭"}),t):e},t.objForEach=t.null2val=t.longtapCopy=void 0,t.roundToDecimal=function(e,t){if(!e)return 0;return e.toFixed(t)},t.throttle=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:100,r=null;return function(){var n=arguments,i=this;r||(r=setTimeout((function(){e.apply(i,n),r=null}),t))}},t.timeToMinutes=h,t.trim=p,t.urlEncode=void 0,t.validateCharNum=function(e){return/^[a-z0-9]+$/i.test(e)},t.validateCnName=function(e){return/^[\u4e00-\u9fa5]{2,10}$/.test(e)},t.validateEnName=function(e){return/^[A-Za-z]*(\s[A-Za-z]*)*$/.test(e)};var i=n(r("5de6")),o=n(r("b7c7")),s=n(r("fcf3"));r("5c47"),r("a1c1"),r("bf0f"),r("2797"),r("aa9c"),r("dc8a"),r("c9b5"),r("ab80"),r("23f4"),r("7d2f"),r("9c4e"),r("795c"),r("8f71"),r("5ef2"),r("fe6b"),r("64aa"),r("d4b5"),r("0506"),r("2c10"),r("fd3c"),r("473f"),r("4100"),r("c223"),r("18f7"),r("de6c"),r("2425"),r("d5c6"),r("5a56"),r("f074"),r("15d1");t.formatDate=function(e){return e.replace(/\-/g,"/")};t.urlEncode=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=[],r=function(r){var n=e[r];if(!n)return"continue";l(n)?n.forEach((function(e){t.push(r+"="+e)})):t.push(r+"="+n)};for(var n in e)r(n);return t.join("&")};t.objForEach=function(e,t){Object.keys(e).forEach((function(r){t(e[r],r)}))};t.inArray=function(e,t){for(var r in t)if(t[r]==e)return!0;return!1};var a=function(e,t){var r,n={"Y+":t.getFullYear().toString(),"m+":(t.getMonth()+1).toString(),"d+":t.getDate().toString(),"H+":t.getHours().toString(),"M+":t.getMinutes().toString(),"S+":t.getSeconds().toString()};for(var i in n)r=new RegExp("("+i+")").exec(e),r&&(e=e.replace(r[1],1==r[1].length?n[i]:n[i].padStart(r[1].length,"0")));return e};t.dateFormat=a;var c=function(e){return 0===Object.keys(e).length};t.isEmptyObject=c;var u=function(e){return"[object Object]"===Object.prototype.toString.call(e)};t.isObject=u;var l=function(e){return"[object Array]"===Object.prototype.toString.call(e)};t.isArray=l;t.isEmpty=function(e){return l(e)?0===e.length:u(e)?c(e):!e};t.cloneObj=function e(t){var r=t.constructor===Array?[]:{};if("object"===(0,s.default)(t)){for(var n in t)r[n]="object"===(0,s.default)(t[n])?e(t[n]):t[n];return r}};t.arrayIntersect=function(e,t){return e.filter((function(e){return t.indexOf(e)>-1}))};t.getSFM=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"H:i:s",r={};r.H=Number.parseInt(e/3600),r.i=Number.parseInt((e-3600*r.H)/60),r.s=Number.parseInt(e-3600*r.H-60*r.i),r.H<10&&(r.H="0"+r.H),r.i<10&&(r.i="0"+r.i),r.s<10&&(r.s="0"+r.s);var n=t.replace("H",r.H).replace("i",r.i).replace("s",r.s);return n};t.getSFMByM=function(e){var t={};t.H=Number.parseInt(e/3600),t.i=Number.parseInt((e-3600*t.H)/60),t.s=Number.parseInt(e-3600*t.H-60*t.i),t.H<10&&(t.H="0"+t.H),t.i<10&&(t.i="0"+t.i);var r=a.replace("H",t.H).replace("i",t.i);return r};t.longtapCopy=function(e){uni.setClipboardData({data:e,success:function(){uni.showToast({title:"复制成功"})}})};t.copy=function(e){uni.setClipboardData({data:e,success:function(){uni.showToast({title:"复制成功"})}})};t.clickCall=function(e){uni.makePhoneCall({phoneNumber:e,success:function(){console.log("拨号")}})};var f=function(e){var t=(0,s.default)(e);return"[object Undefined]"===Object.prototype.toString.call(e)||("[object String]"===Object.prototype.toString.call(e)||"[object Array]"===Object.prototype.toString.call(e)?0==e.length:"[object Object]"===Object.prototype.toString.call(e)?"{}"==JSON.stringify(e):"number"===t?!e&&0!=e:"boolean"!==t&&"string"!==t||!param)};t.isNull=f;function p(e){return e.replace(/^\s+|\s+$/g,"")}function h(e){var t=e.split(":").map(Number),r=(0,i.default)(t,2),n=r[0],o=r[1];return 60*n+o}t.null2val=function(e,t){return f(e)?t:e}},"0743":function(e,t,r){"use strict";r("6a54");var n=r("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,r("08eb"),r("18f7");var i=n(r("582a")),o={name:"u-loading-icon",mixins:[uni.$u.mpMixin,uni.$u.mixin,i.default],data:function(){return{array12:Array.from({length:12}),aniAngel:360,webviewHide:!1,loading:!1}},computed:{otherBorderColor:function(){var e=uni.$u.colorGradient(this.color,"#ffffff",100)[80];return"circle"===this.mode?this.inactiveColor?this.inactiveColor:e:"transparent"}},watch:{show:function(e){}},mounted:function(){this.init()},methods:{init:function(){setTimeout((function(){}),20)},addEventListenerToWebview:function(){var e=this,t=getCurrentPages(),r=t[t.length-1],n=r.$getAppWebview();n.addEventListener("hide",(function(){e.webviewHide=!0})),n.addEventListener("show",(function(){e.webviewHide=!1}))}}};t.default=o},"07d7":function(e,t,r){"use strict";r("aa9c"),r("f7a5");var n=r("7125").Duplex,i=r("2c2e"),o=r("dc92");function s(e){if(!(this instanceof s))return new s(e);if("function"===typeof e){this._callback=e;var t=function(e){this._callback&&(this._callback(e),this._callback=null)}.bind(this);this.on("pipe",(function(e){e.on("error",t)})),this.on("unpipe",(function(e){e.removeListener("error",t)})),e=null}o._init.call(this,e),n.call(this)}i(s,n),Object.assign(s.prototype,o.prototype),s.prototype._new=function(e){return new s(e)},s.prototype._write=function(e,t,r){this._appendBuffer(e),"function"===typeof r&&r()},s.prototype._read=function(e){if(!this.length)return this.push(null);e=Math.min(e,this.length),this.push(this.slice(0,e)),this.consume(e)},s.prototype.end=function(e){n.prototype.end.call(this,e),this._callback&&(this._callback(null,this.slice()),this._callback=null)},s.prototype._destroy=function(e,t){this._bufs.length=0,this.length=0,t(e)},s.prototype._isBufferList=function(e){return e instanceof s||e instanceof o||s.isBufferList(e)},s.isBufferList=o.isBufferList,e.exports=s,e.exports.BufferListStream=s,e.exports.BufferList=o},"0b62":function(e,t,r){"use strict";(function(t){r("7a76"),r("c9b5"),r("aa9c"),r("80e3"),r("4db2"),r("bf0f");var n,i,o,s=r("7125").Transform,a=r("3e6ee"),c=!1;e.exports=function(e,r){if(r.hostname=r.hostname||r.host,!r.hostname)throw new Error("Could not determine host. Specify host manually.");var u="MQIsdp"===r.protocolId&&3===r.protocolVersion?"mqttv3.1":"mqtt";(function(e){e.hostname||(e.hostname="localhost"),e.path||(e.path="/"),e.wsOptions||(e.wsOptions={})})(r);var l=function(e,t){var r="alis"===e.protocol?"wss":"ws",n=r+"://"+e.hostname+e.path;return e.port&&80!==e.port&&443!==e.port&&(n=r+"://"+e.hostname+":"+e.port+e.path),"function"===typeof e.transformWsUrl&&(n=e.transformWsUrl(n,e,t)),n}(r,e);return n=r.my,n.connectSocket({url:l,protocols:u}),i=function(){var e=new s;return e._write=function(e,t,r){n.sendSocketMessage({data:e.buffer,success:function(){r()},fail:function(){r(new Error)}})},e._flush=function(e){n.closeSocket({success:function(){e()}})},e}(),o=a.obj(),function(){c||(c=!0,n.onSocketOpen((function(){o.setReadable(i),o.setWritable(i),o.emit("connect")})),n.onSocketMessage((function(e){if("string"===typeof e.data){var r=t.from(e.data,"base64");i.push(r)}else{var n=new FileReader;n.addEventListener("load",(function(){var e=n.result;e=e instanceof ArrayBuffer?t.from(e):t.from(e,"utf8"),i.push(e)})),n.readAsArrayBuffer(e.data)}})),n.onSocketClose((function(){o.end(),o.destroy()})),n.onSocketError((function(e){o.destroy(e)})))}(),o}}).call(this,r("12e3").Buffer)},"0c01":function(e,t,r){"use strict";r("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n={props:{openType:String},methods:{onGetUserInfo:function(e){this.$emit("getuserinfo",e.detail)},onContact:function(e){this.$emit("contact",e.detail)},onGetPhoneNumber:function(e){this.$emit("getphonenumber",e.detail)},onError:function(e){this.$emit("error",e.detail)},onLaunchApp:function(e){this.$emit("launchapp",e.detail)},onOpenSetting:function(e){this.$emit("opensetting",e.detail)}}};t.default=n},"0ec1":function(e,t,r){"use strict";var n=r("ca99f"),i=Object.keys||function(e){var t=[];for(var r in e)t.push(r);return t};e.exports=f;var o=Object.create(r("8ce8"));o.inherits=r("2c2e");var s=r("46dd"),a=r("edd4");o.inherits(f,s);for(var c=i(a.prototype),u=0;u<c.length;u++){var l=c[u];f.prototype[l]||(f.prototype[l]=a.prototype[l])}function f(e){if(!(this instanceof f))return new f(e);s.call(this,e),a.call(this,e),e&&!1===e.readable&&(this.readable=!1),e&&!1===e.writable&&(this.writable=!1),this.allowHalfOpen=!0,e&&!1===e.allowHalfOpen&&(this.allowHalfOpen=!1),this.once("end",p)}function p(){this.allowHalfOpen||this._writableState.ended||n.nextTick(h,this)}function h(e){e.end()}Object.defineProperty(f.prototype,"writableHighWaterMark",{enumerable:!1,get:function(){return this._writableState.highWaterMark}}),Object.defineProperty(f.prototype,"destroyed",{get:function(){return void 0!==this._readableState&&void 0!==this._writableState&&(this._readableState.destroyed&&this._writableState.destroyed)},set:function(e){void 0!==this._readableState&&void 0!==this._writableState&&(this._readableState.destroyed=e,this._writableState.destroyed=e)}}),f.prototype._destroy=function(e,t){this.push(null),this.end(),n.nextTick(t,e)}},"0f55":function(e,t,r){"use strict";e.exports=s;var n=r("0ec1"),i=Object.create(r("8ce8"));function o(e,t){var r=this._transformState;r.transforming=!1;var n=r.writecb;if(!n)return this.emit("error",new Error("write callback called multiple times"));r.writechunk=null,r.writecb=null,null!=t&&this.push(t),n(e);var i=this._readableState;i.reading=!1,(i.needReadable||i.length<i.highWaterMark)&&this._read(i.highWaterMark)}function s(e){if(!(this instanceof s))return new s(e);n.call(this,e),this._transformState={afterTransform:o.bind(this),needTransform:!1,transforming:!1,writecb:null,writechunk:null,writeencoding:null},this._readableState.needReadable=!0,this._readableState.sync=!1,e&&("function"===typeof e.transform&&(this._transform=e.transform),"function"===typeof e.flush&&(this._flush=e.flush)),this.on("prefinish",a)}function a(){var e=this;"function"===typeof this._flush?this._flush((function(t,r){c(e,t,r)})):c(this,null,null)}function c(e,t,r){if(t)return e.emit("error",t);if(null!=r&&e.push(r),e._writableState.length)throw new Error("Calling transform done when ws.length != 0");if(e._transformState.transforming)throw new Error("Calling transform done when still transforming");return e.push(null)}i.inherits=r("2c2e"),i.inherits(s,n),s.prototype.push=function(e,t){return this._transformState.needTransform=!1,n.prototype.push.call(this,e,t)},s.prototype._transform=function(e,t,r){throw new Error("_transform() is not implemented")},s.prototype._write=function(e,t,r){var n=this._transformState;if(n.writecb=r,n.writechunk=e,n.writeencoding=t,!n.transforming){var i=this._readableState;(n.needTransform||i.needReadable||i.length<i.highWaterMark)&&this._read(i.highWaterMark)}},s.prototype._read=function(e){var t=this._transformState;null!==t.writechunk&&t.writecb&&!t.transforming?(t.transforming=!0,this._transform(t.writechunk,t.writeencoding,t.afterTransform)):t.needTransform=!0},s.prototype._destroy=function(e,t){var r=this;n.prototype._destroy.call(this,e,(function(e){t(e),r.emit("close")}))}},1:function(e,t){},"11aa":function(e,t){var r=1e3,n=6e4,i=60*n,o=24*i;function s(e,t,r,n){var i=t>=1.5*r;return Math.round(e/r)+" "+n+(i?"s":"")}e.exports=function(e,t){t=t||{};var a=typeof e;if("string"===a&&e.length>0)return function(e){if(e=String(e),e.length>100)return;var t=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(!t)return;var s=parseFloat(t[1]),a=(t[2]||"ms").toLowerCase();switch(a){case"years":case"year":case"yrs":case"yr":case"y":return 315576e5*s;case"weeks":case"week":case"w":return 6048e5*s;case"days":case"day":case"d":return s*o;case"hours":case"hour":case"hrs":case"hr":case"h":return s*i;case"minutes":case"minute":case"mins":case"min":case"m":return s*n;case"seconds":case"second":case"secs":case"sec":case"s":return s*r;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return s;default:return}}(e);if("number"===a&&isFinite(e))return t.long?function(e){var t=Math.abs(e);if(t>=o)return s(e,t,o,"day");if(t>=i)return s(e,t,i,"hour");if(t>=n)return s(e,t,n,"minute");if(t>=r)return s(e,t,r,"second");return e+" ms"}(e):function(e){var t=Math.abs(e);if(t>=o)return Math.round(e/o)+"d";if(t>=i)return Math.round(e/i)+"h";if(t>=n)return Math.round(e/n)+"m";if(t>=r)return Math.round(e/r)+"s";return e+"ms"}(e);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}},"14bd":function(e,t,r){function n(t){return e.exports=n=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},e.exports.__esModule=!0,e.exports["default"]=e.exports,n(t)}r("8a8d"),r("926e"),e.exports=n,e.exports.__esModule=!0,e.exports["default"]=e.exports},1552:function(e,t,r){"use strict";r("6a54");var n=r("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,r("5ef2");n(r("a932")),n(r("0c01"));var i=n(r("9e1c")),o={name:"u-button",mixins:[uni.$u.mpMixin,uni.$u.mixin,i.default],data:function(){return{}},computed:{bemClass:function(){return this.color?this.bem("button",["shape","size"],["disabled","plain","hairline"]):this.bem("button",["type","shape","size"],["disabled","plain","hairline"])},loadingColor:function(){return this.plain?this.color?this.color:uni.$u.config.color["u-".concat(this.type)]:"info"===this.type?"#c9c9c9":"rgb(200, 200, 200)"},iconColorCom:function(){return this.iconColor?this.iconColor:this.plain?this.color?this.color:this.type:"info"===this.type?"#000000":"#ffffff"},baseColor:function(){var e={};return this.color&&(e.color=this.plain?this.color:"white",this.plain||(e["background-color"]=this.color),-1!==this.color.indexOf("gradient")?(e.borderTopWidth=0,e.borderRightWidth=0,e.borderBottomWidth=0,e.borderLeftWidth=0,this.plain||(e.backgroundImage=this.color)):(e.borderColor=this.color,e.borderWidth="1px",e.borderStyle="solid")),e},nvueTextStyle:function(){var e={};return"info"===this.type&&(e.color="#323233"),this.color&&(e.color=this.plain?this.color:"white"),e.fontSize=this.textSize+"px",e},textSize:function(){var e=14,t=this.size;return"large"===t&&(e=16),"normal"===t&&(e=14),"small"===t&&(e=12),"mini"===t&&(e=10),e}},methods:{clickHandler:function(){var e=this;this.disabled||this.loading||uni.$u.throttle((function(){e.$emit("click")}),this.throttleTime)},getphonenumber:function(e){this.$emit("getphonenumber",e)},getuserinfo:function(e){this.$emit("getuserinfo",e)},error:function(e){this.$emit("error",e)},opensetting:function(e){this.$emit("opensetting",e)},launchapp:function(e){this.$emit("launchapp",e)},agreeprivacyauthorization:function(e){this.$emit("agreeprivacyauthorization",e)}}};t.default=o},"172d":function(e,t,r){(function(n){t.formatArgs=function(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;const r="color: "+this.color;t.splice(1,0,r,"color: inherit");let n=0,i=0;t[0].replace(/%[a-zA-Z%]/g,e=>{"%%"!==e&&(n++,"%c"===e&&(i=n))}),t.splice(i,0,r)},t.save=function(e){try{e?t.storage.setItem("debug",e):t.storage.removeItem("debug")}catch(r){}},t.load=function(){let e;try{e=t.storage.getItem("debug")}catch(r){}!e&&"undefined"!==typeof n&&"env"in n&&(e=Object({NODE_ENV:"production",VUE_APP_DARK_MODE:"false",VUE_APP_INDEX_CSS_HASH:"2da1efab",VUE_APP_INDEX_DARK_CSS_HASH:"aeec55f8",VUE_APP_NAME:"yuan_room",VUE_APP_PLATFORM:"h5",BASE_URL:"./"}).DEBUG);return e},t.useColors=function(){if("undefined"!==typeof window&&window.process&&("renderer"===window.process.type||window.process.__nwjs))return!0;if("undefined"!==typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))return!1;return"undefined"!==typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!==typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!==typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/)&&parseInt(RegExp.$1,10)>=31||"undefined"!==typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)},t.storage=function(){try{return localStorage}catch(e){}}(),t.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],t.log=console.debug||console.log||(()=>{}),e.exports=r("6502")(t);const{formatters:i}=e.exports;i.j=function(e){try{return JSON.stringify(e)}catch(t){return"[UnexpectedJSONParseError]: "+t.message}}}).call(this,r("28d0"))},"1b42":function(e,t){e.exports=function e(t,r){if(t&&r)return e(t)(r);if("function"!==typeof t)throw new TypeError("need wrapper function");return Object.keys(t).forEach((function(e){n[e]=t[e]})),n;function n(){for(var e=new Array(arguments.length),r=0;r<e.length;r++)e[r]=arguments[r];var n=t.apply(this,e),i=e[e.length-1];return"function"===typeof n&&n!==i&&Object.keys(i).forEach((function(e){n[e]=i[e]})),n}}},"1dc2":function(e,t,r){"use strict";r("c1a3"),r("bf0f"),r("18f7"),r("de6c"),r("2797"),r("aa9c"),r("7a76"),r("c9b5");var n=r("abeb"),i=r("7125").Readable,o={objectMode:!0},s={clean:!0};function a(e){if(!(this instanceof a))return new a(e);this.options=e||{},this.options=n(s,e),this._inflights=new Map}a.prototype.put=function(e,t){return this._inflights.set(e.messageId,e),t&&t(),this},a.prototype.createStream=function(){var e=new i(o),t=!1,r=[],n=0;return this._inflights.forEach((function(e,t){r.push(e)})),e._read=function(){!t&&n<r.length?this.push(r[n++]):this.push(null)},e.destroy=function(){if(!t){var e=this;t=!0,setTimeout((function(){e.emit("close")}),0)}},e},a.prototype.del=function(e,t){return e=this._inflights.get(e.messageId),e?(this._inflights.delete(e.messageId),t(null,e)):t&&t(new Error("missing packet")),this},a.prototype.get=function(e,t){return e=this._inflights.get(e.messageId),e?t(null,e):t&&t(new Error("missing packet")),this},a.prototype.close=function(e){this.options.clean&&(this._inflights=null),e&&e()},e.exports=a},2:function(e,t){},"270c":function(e,t,r){(function(t){var n=r("883d").default,i=r("4db6").default,o=r("59a6").default,s=r("feb3").default;r("c223");var a=r("c7f5"),c=r("bf30");var u=function(e){"use strict";o(a,e);var r=s(a);function a(){var e;return n(this,a),e=r.call(this),e._array=new Array(20),e._i=0,e}return i(a,[{key:"write",value:function(e){return this._array[this._i++]=e,!0}},{key:"concat",value:function(){var e,r=0,n=new Array(this._array.length),i=this._array,o=0;for(e=0;e<i.length&&void 0!==i[e];e++)"string"!==typeof i[e]?n[e]=i[e].length:n[e]=t.byteLength(i[e]),r+=n[e];var s=t.allocUnsafe(r);for(e=0;e<i.length&&void 0!==i[e];e++)"string"!==typeof i[e]?(i[e].copy(s,o),o+=n[e]):(s.write(i[e],o),o+=n[e]);return s}}]),a}(c);e.exports=function(e,t){var r=new u;return a(e,r,t),r.concat()}}).call(this,r("12e3").Buffer)},"28d0":function(e,t,r){t.nextTick=function(e){var t=Array.prototype.slice.call(arguments);t.shift(),setTimeout((function(){e.apply(null,t)}),0)},t.platform=t.arch=t.execPath=t.title="browser",t.pid=1,t.browser=!0,t.env={},t.argv=[],t.binding=function(e){throw new Error("No such module. (Possibly not yet loaded)")},function(){var e,n="/";t.cwd=function(){return n},t.chdir=function(t){e||(e=r("a3fc")),n=e.resolve(t,n)}}(),t.exit=t.kill=t.umask=t.dlopen=t.uptime=t.memoryUsage=t.uvCounters=function(){},t.features={}},"2bb3":function(e,t,r){"use strict";var n=r("29d8");e.exports=/(?:ipad|iphone|ipod).*applewebkit/i.test(n)},"2c2e":function(e,t){"function"===typeof Object.create?e.exports=function(e,t){t&&(e.super_=t,e.prototype=Object.create(t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}))}:e.exports=function(e,t){if(t){e.super_=t;var r=function(){};r.prototype=t.prototype,e.prototype=new r,e.prototype.constructor=e}}},"2c61":function(e,t,r){"use strict";var n,i,o,s,a=r("85c1"),c=r("9f9e"),u=r("ae5c"),l=r("474f"),f=r("338c"),p=r("af9e"),h=r("3794"),d=r("37ad"),g=r("3f57"),b=r("7f28"),m=r("2bb3"),v=r("db06"),y=a.setImmediate,_=a.clearImmediate,w=a.process,S=a.Dispatch,k=a.Function,x=a.MessageChannel,C=a.String,E=0,I={};p((function(){n=a.location}));var O=function(e){if(f(I,e)){var t=I[e];delete I[e],t()}},M=function(e){return function(){O(e)}},P=function(e){O(e.data)},T=function(e){a.postMessage(C(e),n.protocol+"//"+n.host)};y&&_||(y=function(e){b(arguments.length,1);var t=l(e)?e:k(e),r=d(arguments,1);return I[++E]=function(){c(t,void 0,r)},i(E),E},_=function(e){delete I[e]},v?i=function(e){w.nextTick(M(e))}:S&&S.now?i=function(e){S.now(M(e))}:x&&!m?(o=new x,s=o.port2,o.port1.onmessage=P,i=u(s.postMessage,s)):a.addEventListener&&l(a.postMessage)&&!a.importScripts&&n&&"file:"!==n.protocol&&!p(T)?(i=T,a.addEventListener("message",P,!1)):i="onreadystatechange"in g("script")?function(e){h.appendChild(g("script"))["onreadystatechange"]=function(){h.removeChild(this),O(e)}}:function(e){setTimeout(M(e),0)}),e.exports={set:y,clear:_}},3:function(e,t){},"33ee":function(e,t,r){var n=r("c86c");t=n(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */uni-view[data-v-26861ad0], uni-scroll-view[data-v-26861ad0], uni-swiper-item[data-v-26861ad0]{display:flex;flex-direction:column;flex-shrink:0;flex-grow:0;flex-basis:auto;align-items:stretch;align-content:flex-start}.u-loading-icon[data-v-26861ad0]{flex-direction:row;align-items:center;justify-content:center;color:#c8c9cc}.u-loading-icon__text[data-v-26861ad0]{margin-left:4px;color:#606266;font-size:14px;line-height:20px}.u-loading-icon__spinner[data-v-26861ad0]{width:30px;height:30px;position:relative;box-sizing:border-box;max-width:100%;max-height:100%;-webkit-animation:u-rotate-data-v-26861ad0 1s linear infinite;animation:u-rotate-data-v-26861ad0 1s linear infinite}.u-loading-icon__spinner--semicircle[data-v-26861ad0]{border-width:2px;border-color:transparent;border-top-right-radius:100px;border-top-left-radius:100px;border-bottom-left-radius:100px;border-bottom-right-radius:100px;border-style:solid}.u-loading-icon__spinner--circle[data-v-26861ad0]{border-top-right-radius:100px;border-top-left-radius:100px;border-bottom-left-radius:100px;border-bottom-right-radius:100px;border-width:2px;border-top-color:#e5e5e5;border-right-color:#e5e5e5;border-bottom-color:#e5e5e5;border-left-color:#e5e5e5;border-style:solid}.u-loading-icon--vertical[data-v-26861ad0]{flex-direction:column}[data-v-26861ad0]:host{font-size:0;line-height:1}.u-loading-icon__spinner--spinner[data-v-26861ad0]{-webkit-animation-timing-function:steps(12);animation-timing-function:steps(12)}.u-loading-icon__text[data-v-26861ad0]:empty{display:none}.u-loading-icon--vertical .u-loading-icon__text[data-v-26861ad0]{margin:6px 0 0;color:#606266}.u-loading-icon__dot[data-v-26861ad0]{position:absolute;top:0;left:0;width:100%;height:100%}.u-loading-icon__dot[data-v-26861ad0]:before{display:block;width:2px;height:25%;margin:0 auto;background-color:currentColor;border-radius:40%;content:" "}.u-loading-icon__dot[data-v-26861ad0]:nth-of-type(1){-webkit-transform:rotate(30deg);transform:rotate(30deg);opacity:1}.u-loading-icon__dot[data-v-26861ad0]:nth-of-type(2){-webkit-transform:rotate(60deg);transform:rotate(60deg);opacity:.9375}.u-loading-icon__dot[data-v-26861ad0]:nth-of-type(3){-webkit-transform:rotate(90deg);transform:rotate(90deg);opacity:.875}.u-loading-icon__dot[data-v-26861ad0]:nth-of-type(4){-webkit-transform:rotate(120deg);transform:rotate(120deg);opacity:.8125}.u-loading-icon__dot[data-v-26861ad0]:nth-of-type(5){-webkit-transform:rotate(150deg);transform:rotate(150deg);opacity:.75}.u-loading-icon__dot[data-v-26861ad0]:nth-of-type(6){-webkit-transform:rotate(180deg);transform:rotate(180deg);opacity:.6875}.u-loading-icon__dot[data-v-26861ad0]:nth-of-type(7){-webkit-transform:rotate(210deg);transform:rotate(210deg);opacity:.625}.u-loading-icon__dot[data-v-26861ad0]:nth-of-type(8){-webkit-transform:rotate(240deg);transform:rotate(240deg);opacity:.5625}.u-loading-icon__dot[data-v-26861ad0]:nth-of-type(9){-webkit-transform:rotate(270deg);transform:rotate(270deg);opacity:.5}.u-loading-icon__dot[data-v-26861ad0]:nth-of-type(10){-webkit-transform:rotate(300deg);transform:rotate(300deg);opacity:.4375}.u-loading-icon__dot[data-v-26861ad0]:nth-of-type(11){-webkit-transform:rotate(330deg);transform:rotate(330deg);opacity:.375}.u-loading-icon__dot[data-v-26861ad0]:nth-of-type(12){-webkit-transform:rotate(1turn);transform:rotate(1turn);opacity:.3125}@-webkit-keyframes u-rotate-data-v-26861ad0{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}@keyframes u-rotate-data-v-26861ad0{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}',""]),e.exports=t},"35fd":function(e,t,r){"use strict";var n=r("8bdb"),i=r("85c1"),o=r("2c61").clear;n({global:!0,bind:!0,enumerable:!0,forced:i.clearImmediate!==o},{clearImmediate:o})},"3a39":function(e,t,r){"use strict";r.r(t);var n=r("1552"),i=r.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){r.d(t,e,(function(){return n[e]}))}(o);t["default"]=i.a},"3a7e":function(e,t,r){r("7a76"),r("c9b5"),e.exports=function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e},e.exports.__esModule=!0,e.exports["default"]=e.exports},"3e6ee":function(e,t,r){(function(t,n){var i=r("7125"),o=r("cd70"),s=r("2c2e"),a=r("5f37"),c=t.from&&t.from!==Uint8Array.from?t.from([0]):new t([0]),u=function(e,t){e._corked?e.once("uncork",t):t()},l=function(e,t){return function(r){r?function(e,t){e._autoDestroy&&e.destroy(t)}(e,"premature close"===r.message?null:r):t&&!e._ended&&e.end()}},f=function(e,t,r){if(!(this instanceof f))return new f(e,t,r);i.Duplex.call(this,r),this._writable=null,this._readable=null,this._readable2=null,this._autoDestroy=!r||!1!==r.autoDestroy,this._forwardDestroy=!r||!1!==r.destroy,this._forwardEnd=!r||!1!==r.end,this._corked=1,this._ondrain=null,this._drained=!1,this._forwarding=!1,this._unwrite=null,this._unread=null,this._ended=!1,this.destroyed=!1,e&&this.setWritable(e),t&&this.setReadable(t)};s(f,i.Duplex),f.obj=function(e,t,r){return r||(r={}),r.objectMode=!0,r.highWaterMark=16,new f(e,t,r)},f.prototype.cork=function(){1===++this._corked&&this.emit("cork")},f.prototype.uncork=function(){this._corked&&0===--this._corked&&this.emit("uncork")},f.prototype.setWritable=function(e){if(this._unwrite&&this._unwrite(),this.destroyed)e&&e.destroy&&e.destroy();else if(null!==e&&!1!==e){var t=this,r=o(e,{writable:!0,readable:!1},l(this,this._forwardEnd)),i=function(){var e=t._ondrain;t._ondrain=null,e&&e()};this._unwrite&&n.nextTick(i),this._writable=e,this._writable.on("drain",i),this._unwrite=function(){t._writable.removeListener("drain",i),r()},this.uncork()}else this.end()},f.prototype.setReadable=function(e){if(this._unread&&this._unread(),this.destroyed)e&&e.destroy&&e.destroy();else{if(null===e||!1===e)return this.push(null),void this.resume();var t=this,r=o(e,{writable:!1,readable:!0},l(this)),n=function(){t._forward()},s=function(){t.push(null)};this._drained=!0,this._readable=e,this._readable2=e._readableState?e:function(e){return new i.Readable({objectMode:!0,highWaterMark:16}).wrap(e)}(e),this._readable2.on("readable",n),this._readable2.on("end",s),this._unread=function(){t._readable2.removeListener("readable",n),t._readable2.removeListener("end",s),r()},this._forward()}},f.prototype._read=function(){this._drained=!0,this._forward()},f.prototype._forward=function(){if(!this._forwarding&&this._readable2&&this._drained){var e;this._forwarding=!0;while(this._drained&&null!==(e=a(this._readable2)))this.destroyed||(this._drained=this.push(e));this._forwarding=!1}},f.prototype.destroy=function(e){if(!this.destroyed){this.destroyed=!0;var t=this;n.nextTick((function(){t._destroy(e)}))}},f.prototype._destroy=function(e){if(e){var t=this._ondrain;this._ondrain=null,t?t(e):this.emit("error",e)}this._forwardDestroy&&(this._readable&&this._readable.destroy&&this._readable.destroy(),this._writable&&this._writable.destroy&&this._writable.destroy()),this.emit("close")},f.prototype._write=function(e,t,r){return this.destroyed?r():this._corked?u(this,this._write.bind(this,e,t,r)):e===c?this._finish(r):this._writable?void(!1===this._writable.write(e)?this._ondrain=r:r()):r()},f.prototype._finish=function(e){var t=this;this.emit("preend"),u(this,(function(){(function(e,t){e?e._writableState&&e._writableState.finished?t():e._writableState?e.end(t):(e.end(),t()):t()})(t._forwardEnd&&t._writable,(function(){!1===t._writableState.prefinished&&(t._writableState.prefinished=!0),t.emit("prefinish"),u(t,e)}))}))},f.prototype.end=function(e,t,r){return"function"===typeof e?this.end(null,null,e):"function"===typeof t?this.end(e,null,t):(this._ended=!0,e&&this.write(e),this._writableState.ending||this.write(c),i.Writable.prototype.end.call(this,r))},e.exports=f}).call(this,r("12e3").Buffer,r("28d0"))},"3eae":function(e,t,r){"use strict";e.exports=function(){throw new Error("ws does not work in the browser. Browser clients must use the native WebSocket object")}},"3f21":function(e,t,r){"use strict";function n(e){for(var t=e.split("/"),r=0;r<t.length;r++)if("+"!==t[r]){if("#"===t[r])return r===t.length-1;if(-1!==t[r].indexOf("+")||-1!==t[r].indexOf("#"))return!1}return!0}r("5ef2"),e.exports={validateTopics:function(e){if(0===e.length)return"empty_topic_list";for(var t=0;t<e.length;t++)if(!n(e[t]))return e[t];return null}}},4:function(e,t){},"414f":function(e,t,r){var n=r("f579");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);var i=r("967d").default;i("085b0cbc",n,!0,{sourceMap:!1,shadowMode:!1})},4609:function(e,t,r){"use strict";function n(e,t,r){var n=this;this._callback=e,this._args=r,this._interval=setInterval(e,t,this._args),this.reschedule=function(e){e||(e=n._interval),n._interval&&clearInterval(n._interval),n._interval=setInterval(n._callback,e,n._args)},this.clear=function(){n._interval&&(clearInterval(n._interval),n._interval=void 0)},this.destroy=function(){n._interval&&clearInterval(n._interval),n._callback=void 0,n._interval=void 0,n._args=void 0}}r("7a76"),r("c9b5"),e.exports=function(){if("function"!==typeof arguments[0])throw new Error("callback needed");if("number"!==typeof arguments[1])throw new Error("interval needed");var e;if(arguments.length>0){e=new Array(arguments.length-2);for(var t=0;t<e.length;t++)e[t]=arguments[t+2]}return new n(arguments[0],arguments[1],e)}},"46dd":function(e,t,r){"use strict";(function(t,n){var i=r("ca99f");e.exports=y;var o,s=r("b0e4");y.ReadableState=v;r("bf30").EventEmitter;var a=function(e,t){return e.listeners(t).length},c=r("51c3"),u=r("5f79").Buffer,l=t.Uint8Array||function(){};var f=Object.create(r("8ce8"));f.inherits=r("2c2e");var p=r(1),h=void 0;h=p&&p.debuglog?p.debuglog("stream"):function(){};var d,g=r("f874"),b=r("c741");f.inherits(y,c);var m=["error","close","destroy","pause","resume"];function v(e,t){o=o||r("0ec1"),e=e||{};var n=t instanceof o;this.objectMode=!!e.objectMode,n&&(this.objectMode=this.objectMode||!!e.readableObjectMode);var i=e.highWaterMark,s=e.readableHighWaterMark,a=this.objectMode?16:16384;this.highWaterMark=i||0===i?i:n&&(s||0===s)?s:a,this.highWaterMark=Math.floor(this.highWaterMark),this.buffer=new g,this.length=0,this.pipes=null,this.pipesCount=0,this.flowing=null,this.ended=!1,this.endEmitted=!1,this.reading=!1,this.sync=!0,this.needReadable=!1,this.emittedReadable=!1,this.readableListening=!1,this.resumeScheduled=!1,this.destroyed=!1,this.defaultEncoding=e.defaultEncoding||"utf8",this.awaitDrain=0,this.readingMore=!1,this.decoder=null,this.encoding=null,e.encoding&&(d||(d=r("5f85").StringDecoder),this.decoder=new d(e.encoding),this.encoding=e.encoding)}function y(e){if(o=o||r("0ec1"),!(this instanceof y))return new y(e);this._readableState=new v(e,this),this.readable=!0,e&&("function"===typeof e.read&&(this._read=e.read),"function"===typeof e.destroy&&(this._destroy=e.destroy)),c.call(this)}function _(e,t,r,n,i){var o,s=e._readableState;null===t?(s.reading=!1,function(e,t){if(t.ended)return;if(t.decoder){var r=t.decoder.end();r&&r.length&&(t.buffer.push(r),t.length+=t.objectMode?1:r.length)}t.ended=!0,k(e)}(e,s)):(i||(o=function(e,t){var r;(function(e){return u.isBuffer(e)||e instanceof l})(t)||"string"===typeof t||void 0===t||e.objectMode||(r=new TypeError("Invalid non-string/buffer chunk"));return r}(s,t)),o?e.emit("error",o):s.objectMode||t&&t.length>0?("string"===typeof t||s.objectMode||Object.getPrototypeOf(t)===u.prototype||(t=function(e){return u.from(e)}(t)),n?s.endEmitted?e.emit("error",new Error("stream.unshift() after end event")):w(e,s,t,!0):s.ended?e.emit("error",new Error("stream.push() after EOF")):(s.reading=!1,s.decoder&&!r?(t=s.decoder.write(t),s.objectMode||0!==t.length?w(e,s,t,!1):C(e,s)):w(e,s,t,!1))):n||(s.reading=!1));return function(e){return!e.ended&&(e.needReadable||e.length<e.highWaterMark||0===e.length)}(s)}function w(e,t,r,n){t.flowing&&0===t.length&&!t.sync?(e.emit("data",r),e.read(0)):(t.length+=t.objectMode?1:r.length,n?t.buffer.unshift(r):t.buffer.push(r),t.needReadable&&k(e)),C(e,t)}Object.defineProperty(y.prototype,"destroyed",{get:function(){return void 0!==this._readableState&&this._readableState.destroyed},set:function(e){this._readableState&&(this._readableState.destroyed=e)}}),y.prototype.destroy=b.destroy,y.prototype._undestroy=b.undestroy,y.prototype._destroy=function(e,t){this.push(null),t(e)},y.prototype.push=function(e,t){var r,n=this._readableState;return n.objectMode?r=!0:"string"===typeof e&&(t=t||n.defaultEncoding,t!==n.encoding&&(e=u.from(e,t),t=""),r=!0),_(this,e,t,!1,r)},y.prototype.unshift=function(e){return _(this,e,null,!0,!1)},y.prototype.isPaused=function(){return!1===this._readableState.flowing},y.prototype.setEncoding=function(e){return d||(d=r("5f85").StringDecoder),this._readableState.decoder=new d(e),this._readableState.encoding=e,this};function S(e,t){return e<=0||0===t.length&&t.ended?0:t.objectMode?1:e!==e?t.flowing&&t.length?t.buffer.head.data.length:t.length:(e>t.highWaterMark&&(t.highWaterMark=function(e){return e>=8388608?e=8388608:(e--,e|=e>>>1,e|=e>>>2,e|=e>>>4,e|=e>>>8,e|=e>>>16,e++),e}(e)),e<=t.length?e:t.ended?t.length:(t.needReadable=!0,0))}function k(e){var t=e._readableState;t.needReadable=!1,t.emittedReadable||(h("emitReadable",t.flowing),t.emittedReadable=!0,t.sync?i.nextTick(x,e):x(e))}function x(e){h("emit readable"),e.emit("readable"),M(e)}function C(e,t){t.readingMore||(t.readingMore=!0,i.nextTick(E,e,t))}function E(e,t){var r=t.length;while(!t.reading&&!t.flowing&&!t.ended&&t.length<t.highWaterMark){if(h("maybeReadMore read 0"),e.read(0),r===t.length)break;r=t.length}t.readingMore=!1}function I(e){h("readable nexttick read 0"),e.read(0)}function O(e,t){t.reading||(h("resume read 0"),e.read(0)),t.resumeScheduled=!1,t.awaitDrain=0,e.emit("resume"),M(e),t.flowing&&!t.reading&&e.read(0)}function M(e){var t=e._readableState;h("flow",t.flowing);while(t.flowing&&null!==e.read());}function P(e,t){return 0===t.length?null:(t.objectMode?r=t.buffer.shift():!e||e>=t.length?(r=t.decoder?t.buffer.join(""):1===t.buffer.length?t.buffer.head.data:t.buffer.concat(t.length),t.buffer.clear()):r=function(e,t,r){var n;e<t.head.data.length?(n=t.head.data.slice(0,e),t.head.data=t.head.data.slice(e)):n=e===t.head.data.length?t.shift():r?function(e,t){var r=t.head,n=1,i=r.data;e-=i.length;while(r=r.next){var o=r.data,s=e>o.length?o.length:e;if(s===o.length?i+=o:i+=o.slice(0,e),e-=s,0===e){s===o.length?(++n,r.next?t.head=r.next:t.head=t.tail=null):(t.head=r,r.data=o.slice(s));break}++n}return t.length-=n,i}(e,t):function(e,t){var r=u.allocUnsafe(e),n=t.head,i=1;n.data.copy(r),e-=n.data.length;while(n=n.next){var o=n.data,s=e>o.length?o.length:e;if(o.copy(r,r.length-e,0,s),e-=s,0===e){s===o.length?(++i,n.next?t.head=n.next:t.head=t.tail=null):(t.head=n,n.data=o.slice(s));break}++i}return t.length-=i,r}(e,t);return n}(e,t.buffer,t.decoder),r);var r}function T(e){var t=e._readableState;if(t.length>0)throw new Error('"endReadable()" called on non-empty stream');t.endEmitted||(t.ended=!0,i.nextTick(A,t,e))}function A(e,t){e.endEmitted||0!==e.length||(e.endEmitted=!0,t.readable=!1,t.emit("end"))}function j(e,t){for(var r=0,n=e.length;r<n;r++)if(e[r]===t)return r;return-1}y.prototype.read=function(e){h("read",e),e=parseInt(e,10);var t=this._readableState,r=e;if(0!==e&&(t.emittedReadable=!1),0===e&&t.needReadable&&(t.length>=t.highWaterMark||t.ended))return h("read: emitReadable",t.length,t.ended),0===t.length&&t.ended?T(this):k(this),null;if(e=S(e,t),0===e&&t.ended)return 0===t.length&&T(this),null;var n,i=t.needReadable;return h("need readable",i),(0===t.length||t.length-e<t.highWaterMark)&&(i=!0,h("length less than watermark",i)),t.ended||t.reading?(i=!1,h("reading or ended",i)):i&&(h("do read"),t.reading=!0,t.sync=!0,0===t.length&&(t.needReadable=!0),this._read(t.highWaterMark),t.sync=!1,t.reading||(e=S(r,t))),n=e>0?P(e,t):null,null===n?(t.needReadable=!0,e=0):t.length-=e,0===t.length&&(t.ended||(t.needReadable=!0),r!==e&&t.ended&&T(this)),null!==n&&this.emit("data",n),n},y.prototype._read=function(e){this.emit("error",new Error("_read() is not implemented"))},y.prototype.pipe=function(e,t){var r=this,o=this._readableState;switch(o.pipesCount){case 0:o.pipes=e;break;case 1:o.pipes=[o.pipes,e];break;default:o.pipes.push(e);break}o.pipesCount+=1,h("pipe count=%d opts=%j",o.pipesCount,t);var c=(!t||!1!==t.end)&&e!==n.stdout&&e!==n.stderr,u=c?f:_;function l(t,n){h("onunpipe"),t===r&&n&&!1===n.hasUnpiped&&(n.hasUnpiped=!0,function(){h("cleanup"),e.removeListener("close",v),e.removeListener("finish",y),e.removeListener("drain",p),e.removeListener("error",m),e.removeListener("unpipe",l),r.removeListener("end",f),r.removeListener("end",_),r.removeListener("data",b),d=!0,!o.awaitDrain||e._writableState&&!e._writableState.needDrain||p()}())}function f(){h("onend"),e.end()}o.endEmitted?i.nextTick(u):r.once("end",u),e.on("unpipe",l);var p=function(e){return function(){var t=e._readableState;h("pipeOnDrain",t.awaitDrain),t.awaitDrain&&t.awaitDrain--,0===t.awaitDrain&&a(e,"data")&&(t.flowing=!0,M(e))}}(r);e.on("drain",p);var d=!1;var g=!1;function b(t){h("ondata"),g=!1;var n=e.write(t);!1!==n||g||((1===o.pipesCount&&o.pipes===e||o.pipesCount>1&&-1!==j(o.pipes,e))&&!d&&(h("false write response, pause",r._readableState.awaitDrain),r._readableState.awaitDrain++,g=!0),r.pause())}function m(t){h("onerror",t),_(),e.removeListener("error",m),0===a(e,"error")&&e.emit("error",t)}function v(){e.removeListener("finish",y),_()}function y(){h("onfinish"),e.removeListener("close",v),_()}function _(){h("unpipe"),r.unpipe(e)}return r.on("data",b),function(e,t,r){if("function"===typeof e.prependListener)return e.prependListener(t,r);e._events&&e._events[t]?s(e._events[t])?e._events[t].unshift(r):e._events[t]=[r,e._events[t]]:e.on(t,r)}(e,"error",m),e.once("close",v),e.once("finish",y),e.emit("pipe",r),o.flowing||(h("pipe resume"),r.resume()),e},y.prototype.unpipe=function(e){var t=this._readableState,r={hasUnpiped:!1};if(0===t.pipesCount)return this;if(1===t.pipesCount)return e&&e!==t.pipes||(e||(e=t.pipes),t.pipes=null,t.pipesCount=0,t.flowing=!1,e&&e.emit("unpipe",this,r)),this;if(!e){var n=t.pipes,i=t.pipesCount;t.pipes=null,t.pipesCount=0,t.flowing=!1;for(var o=0;o<i;o++)n[o].emit("unpipe",this,r);return this}var s=j(t.pipes,e);return-1===s||(t.pipes.splice(s,1),t.pipesCount-=1,1===t.pipesCount&&(t.pipes=t.pipes[0]),e.emit("unpipe",this,r)),this},y.prototype.on=function(e,t){var r=c.prototype.on.call(this,e,t);if("data"===e)!1!==this._readableState.flowing&&this.resume();else if("readable"===e){var n=this._readableState;n.endEmitted||n.readableListening||(n.readableListening=n.needReadable=!0,n.emittedReadable=!1,n.reading?n.length&&k(this):i.nextTick(I,this))}return r},y.prototype.addListener=y.prototype.on,y.prototype.resume=function(){var e=this._readableState;return e.flowing||(h("resume"),e.flowing=!0,function(e,t){t.resumeScheduled||(t.resumeScheduled=!0,i.nextTick(O,e,t))}(this,e)),this},y.prototype.pause=function(){return h("call pause flowing=%j",this._readableState.flowing),!1!==this._readableState.flowing&&(h("pause"),this._readableState.flowing=!1,this.emit("pause")),this},y.prototype.wrap=function(e){var t=this,r=this._readableState,n=!1;for(var i in e.on("end",(function(){if(h("wrapped end"),r.decoder&&!r.ended){var e=r.decoder.end();e&&e.length&&t.push(e)}t.push(null)})),e.on("data",(function(i){if(h("wrapped data"),r.decoder&&(i=r.decoder.write(i)),(!r.objectMode||null!==i&&void 0!==i)&&(r.objectMode||i&&i.length)){var o=t.push(i);o||(n=!0,e.pause())}})),e)void 0===this[i]&&"function"===typeof e[i]&&(this[i]=function(t){return function(){return e[t].apply(e,arguments)}}(i));for(var o=0;o<m.length;o++)e.on(m[o],this.emit.bind(this,m[o]));return this._read=function(t){h("wrapped _read",t),n&&(n=!1,e.resume())},this},Object.defineProperty(y.prototype,"readableHighWaterMark",{enumerable:!1,get:function(){return this._readableState.highWaterMark}}),y._fromList=P}).call(this,r("0ee4"),r("28d0"))},"4c25":function(e,t,r){t.parser=r("66fd").parser,t.generate=r("270c"),t.writeToStream=r("c7f5")},"4cf1":function(e,t,r){r("01a2"),r("e39c"),r("bf0f"),r("844d"),r("18f7"),r("de6c"),r("7a76"),r("c9b5");var n=r("79b7");e.exports=function(e,t){var r="undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=n(e))||t&&e&&"number"===typeof e.length){r&&(e=r);var i=0,o=function(){};return{s:o,n:function(){return i>=e.length?{done:!0}:{done:!1,value:e[i++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var s,a=!0,c=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return a=e.done,e},e:function(e){c=!0,s=e},f:function(){try{a||null==r["return"]||r["return"]()}finally{if(c)throw s}}}},e.exports.__esModule=!0,e.exports["default"]=e.exports},"4db6":function(e,t,r){r("6a54");var n=r("8bcf");function i(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,n(i.key),i)}}e.exports=function(e,t,r){return t&&i(e.prototype,t),r&&i(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e},e.exports.__esModule=!0,e.exports["default"]=e.exports},"519e":function(e,t,r){"use strict";var n=r("c425"),i=r("a8e2");function o(){this.protocol=null,this.slashes=null,this.auth=null,this.host=null,this.port=null,this.hostname=null,this.hash=null,this.search=null,this.query=null,this.pathname=null,this.path=null,this.href=null}t.parse=y,t.resolve=function(e,t){return y(e,!1,!0).resolve(t)},t.resolveObject=function(e,t){return e?y(e,!1,!0).resolveObject(t):t},t.format=function(e){i.isString(e)&&(e=y(e));return e instanceof o?e.format():o.prototype.format.call(e)},t.Url=o;var s=/^([a-z0-9.+-]+:)/i,a=/:[0-9]*$/,c=/^(\/\/?(?!\/)[^\?\s]*)(\?[^\s]*)?$/,u=["{","}","|","\\","^","`"].concat(["<",">",'"',"`"," ","\r","\n","\t"]),l=["'"].concat(u),f=["%","/","?",";","#"].concat(l),p=["/","?","#"],h=/^[+a-z0-9A-Z_-]{0,63}$/,d=/^([+a-z0-9A-Z_-]{0,63})(.*)$/,g={javascript:!0,"javascript:":!0},b={javascript:!0,"javascript:":!0},m={http:!0,https:!0,ftp:!0,gopher:!0,file:!0,"http:":!0,"https:":!0,"ftp:":!0,"gopher:":!0,"file:":!0},v=r("d67b");function y(e,t,r){if(e&&i.isObject(e)&&e instanceof o)return e;var n=new o;return n.parse(e,t,r),n}o.prototype.parse=function(e,t,r){if(!i.isString(e))throw new TypeError("Parameter 'url' must be a string, not "+typeof e);var o=e.indexOf("?"),a=-1!==o&&o<e.indexOf("#")?"?":"#",u=e.split(a);u[0]=u[0].replace(/\\/g,"/"),e=u.join(a);var y=e;if(y=y.trim(),!r&&1===e.split("#").length){var _=c.exec(y);if(_)return this.path=y,this.href=y,this.pathname=_[1],_[2]?(this.search=_[2],this.query=t?v.parse(this.search.substr(1)):this.search.substr(1)):t&&(this.search="",this.query={}),this}var w=s.exec(y);if(w){w=w[0];var S=w.toLowerCase();this.protocol=S,y=y.substr(w.length)}if(r||w||y.match(/^\/\/[^@\/]+@[^@\/]+/)){var k="//"===y.substr(0,2);!k||w&&b[w]||(y=y.substr(2),this.slashes=!0)}if(!b[w]&&(k||w&&!m[w])){for(var x,C,E=-1,I=0;I<p.length;I++){var O=y.indexOf(p[I]);-1!==O&&(-1===E||O<E)&&(E=O)}C=-1===E?y.lastIndexOf("@"):y.lastIndexOf("@",E),-1!==C&&(x=y.slice(0,C),y=y.slice(C+1),this.auth=decodeURIComponent(x)),E=-1;for(I=0;I<f.length;I++){O=y.indexOf(f[I]);-1!==O&&(-1===E||O<E)&&(E=O)}-1===E&&(E=y.length),this.host=y.slice(0,E),y=y.slice(E),this.parseHost(),this.hostname=this.hostname||"";var M="["===this.hostname[0]&&"]"===this.hostname[this.hostname.length-1];if(!M)for(var P=this.hostname.split(/\./),T=(I=0,P.length);I<T;I++){var A=P[I];if(A&&!A.match(h)){for(var j="",N=0,R=A.length;N<R;N++)A.charCodeAt(N)>127?j+="x":j+=A[N];if(!j.match(h)){var B=P.slice(0,I),U=P.slice(I+1),L=A.match(d);L&&(B.push(L[1]),U.unshift(L[2])),U.length&&(y="/"+U.join(".")+y),this.hostname=B.join(".");break}}}this.hostname.length>255?this.hostname="":this.hostname=this.hostname.toLowerCase(),M||(this.hostname=n.toASCII(this.hostname));var q=this.port?":"+this.port:"",F=this.hostname||"";this.host=F+q,this.href+=this.host,M&&(this.hostname=this.hostname.substr(1,this.hostname.length-2),"/"!==y[0]&&(y="/"+y))}if(!g[S])for(I=0,T=l.length;I<T;I++){var $=l[I];if(-1!==y.indexOf($)){var D=encodeURIComponent($);D===$&&(D=escape($)),y=y.split($).join(D)}}var z=y.indexOf("#");-1!==z&&(this.hash=y.substr(z),y=y.slice(0,z));var H=y.indexOf("?");if(-1!==H?(this.search=y.substr(H),this.query=y.substr(H+1),t&&(this.query=v.parse(this.query)),y=y.slice(0,H)):t&&(this.search="",this.query={}),y&&(this.pathname=y),m[S]&&this.hostname&&!this.pathname&&(this.pathname="/"),this.pathname||this.search){q=this.pathname||"";var V=this.search||"";this.path=q+V}return this.href=this.format(),this},o.prototype.format=function(){var e=this.auth||"";e&&(e=encodeURIComponent(e),e=e.replace(/%3A/i,":"),e+="@");var t=this.protocol||"",r=this.pathname||"",n=this.hash||"",o=!1,s="";this.host?o=e+this.host:this.hostname&&(o=e+(-1===this.hostname.indexOf(":")?this.hostname:"["+this.hostname+"]"),this.port&&(o+=":"+this.port)),this.query&&i.isObject(this.query)&&Object.keys(this.query).length&&(s=v.stringify(this.query));var a=this.search||s&&"?"+s||"";return t&&":"!==t.substr(-1)&&(t+=":"),this.slashes||(!t||m[t])&&!1!==o?(o="//"+(o||""),r&&"/"!==r.charAt(0)&&(r="/"+r)):o||(o=""),n&&"#"!==n.charAt(0)&&(n="#"+n),a&&"?"!==a.charAt(0)&&(a="?"+a),r=r.replace(/[?#]/g,(function(e){return encodeURIComponent(e)})),a=a.replace("#","%23"),t+o+r+a+n},o.prototype.resolve=function(e){return this.resolveObject(y(e,!1,!0)).format()},o.prototype.resolveObject=function(e){if(i.isString(e)){var t=new o;t.parse(e,!1,!0),e=t}for(var r=new o,n=Object.keys(this),s=0;s<n.length;s++){var a=n[s];r[a]=this[a]}if(r.hash=e.hash,""===e.href)return r.href=r.format(),r;if(e.slashes&&!e.protocol){for(var c=Object.keys(e),u=0;u<c.length;u++){var l=c[u];"protocol"!==l&&(r[l]=e[l])}return m[r.protocol]&&r.hostname&&!r.pathname&&(r.path=r.pathname="/"),r.href=r.format(),r}if(e.protocol&&e.protocol!==r.protocol){if(!m[e.protocol]){for(var f=Object.keys(e),p=0;p<f.length;p++){var h=f[p];r[h]=e[h]}return r.href=r.format(),r}if(r.protocol=e.protocol,e.host||b[e.protocol])r.pathname=e.pathname;else{var d=(e.pathname||"").split("/");while(d.length&&!(e.host=d.shift()));e.host||(e.host=""),e.hostname||(e.hostname=""),""!==d[0]&&d.unshift(""),d.length<2&&d.unshift(""),r.pathname=d.join("/")}if(r.search=e.search,r.query=e.query,r.host=e.host||"",r.auth=e.auth,r.hostname=e.hostname||e.host,r.port=e.port,r.pathname||r.search){var g=r.pathname||"",v=r.search||"";r.path=g+v}return r.slashes=r.slashes||e.slashes,r.href=r.format(),r}var y=r.pathname&&"/"===r.pathname.charAt(0),_=e.host||e.pathname&&"/"===e.pathname.charAt(0),w=_||y||r.host&&e.pathname,S=w,k=r.pathname&&r.pathname.split("/")||[],x=(d=e.pathname&&e.pathname.split("/")||[],r.protocol&&!m[r.protocol]);if(x&&(r.hostname="",r.port=null,r.host&&(""===k[0]?k[0]=r.host:k.unshift(r.host)),r.host="",e.protocol&&(e.hostname=null,e.port=null,e.host&&(""===d[0]?d[0]=e.host:d.unshift(e.host)),e.host=null),w=w&&(""===d[0]||""===k[0])),_)r.host=e.host||""===e.host?e.host:r.host,r.hostname=e.hostname||""===e.hostname?e.hostname:r.hostname,r.search=e.search,r.query=e.query,k=d;else if(d.length)k||(k=[]),k.pop(),k=k.concat(d),r.search=e.search,r.query=e.query;else if(!i.isNullOrUndefined(e.search)){if(x){r.hostname=r.host=k.shift();var C=!!(r.host&&r.host.indexOf("@")>0)&&r.host.split("@");C&&(r.auth=C.shift(),r.host=r.hostname=C.shift())}return r.search=e.search,r.query=e.query,i.isNull(r.pathname)&&i.isNull(r.search)||(r.path=(r.pathname?r.pathname:"")+(r.search?r.search:"")),r.href=r.format(),r}if(!k.length)return r.pathname=null,r.search?r.path="/"+r.search:r.path=null,r.href=r.format(),r;for(var E=k.slice(-1)[0],I=(r.host||e.host||k.length>1)&&("."===E||".."===E)||""===E,O=0,M=k.length;M>=0;M--)E=k[M],"."===E?k.splice(M,1):".."===E?(k.splice(M,1),O++):O&&(k.splice(M,1),O--);if(!w&&!S)for(;O--;O)k.unshift("..");!w||""===k[0]||k[0]&&"/"===k[0].charAt(0)||k.unshift(""),I&&"/"!==k.join("/").substr(-1)&&k.push("");var P=""===k[0]||k[0]&&"/"===k[0].charAt(0);if(x){r.hostname=r.host=P?"":k.length?k.shift():"";C=!!(r.host&&r.host.indexOf("@")>0)&&r.host.split("@");C&&(r.auth=C.shift(),r.host=r.hostname=C.shift())}return w=w||r.host&&k.length,w&&!P&&k.unshift(""),k.length?r.pathname=k.join("/"):(r.pathname=null,r.path=null),i.isNull(r.pathname)&&i.isNull(r.search)||(r.path=(r.pathname?r.pathname:"")+(r.search?r.search:"")),r.auth=e.auth||r.auth,r.slashes=r.slashes||e.slashes,r.href=r.format(),r},o.prototype.parseHost=function(){var e=this.host,t=a.exec(e);t&&(t=t[0],":"!==t&&(this.port=t.substr(1)),e=e.substr(0,e.length-t.length)),e&&(this.hostname=e)}},"51b9":function(e,t,r){function n(t,r){return e.exports=n=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},e.exports.__esModule=!0,e.exports["default"]=e.exports,n(t,r)}r("8a8d"),e.exports=n,e.exports.__esModule=!0,e.exports["default"]=e.exports},"51c3":function(e,t,r){e.exports=r("bf30").EventEmitter},"54cb":function(e,t,r){"use strict";var n=r("d4f8"),i=r.n(n);i.a},"56c9":function(e,t,r){r("9e15"),r("884b"),r("01a2"),r("e39c"),r("bf0f"),r("7a76"),r("c9b5"),r("64aa");var n=r("bdbb")["default"];e.exports=function(e,t){if("object"!==n(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var i=r.call(e,t||"default");if("object"!==n(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)},e.exports.__esModule=!0,e.exports["default"]=e.exports},"582a":function(e,t,r){"use strict";r("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,r("64aa");var n={props:{show:{type:Boolean,default:uni.$u.props.loadingIcon.show},color:{type:String,default:uni.$u.props.loadingIcon.color},textColor:{type:String,default:uni.$u.props.loadingIcon.textColor},vertical:{type:Boolean,default:uni.$u.props.loadingIcon.vertical},mode:{type:String,default:uni.$u.props.loadingIcon.mode},size:{type:[String,Number],default:uni.$u.props.loadingIcon.size},textSize:{type:[String,Number],default:uni.$u.props.loadingIcon.textSize},text:{type:[String,Number],default:uni.$u.props.loadingIcon.text},timingFunction:{type:String,default:uni.$u.props.loadingIcon.timingFunction},duration:{type:[String,Number],default:uni.$u.props.loadingIcon.duration},inactiveColor:{type:String,default:uni.$u.props.loadingIcon.inactiveColor}}};t.default=n},"59a6":function(e,t,r){r("7a76"),r("c9b5"),r("6a54");var n=r("51b9");e.exports=function(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&n(e,t)},e.exports.__esModule=!0,e.exports["default"]=e.exports},"5a67":function(e,t,r){(function(t){r("fd3c");var n=e.exports;for(var i in n.types={0:"reserved",1:"connect",2:"connack",3:"publish",4:"puback",5:"pubrec",6:"pubrel",7:"pubcomp",8:"subscribe",9:"suback",10:"unsubscribe",11:"unsuback",12:"pingreq",13:"pingresp",14:"disconnect",15:"auth"},n.codes={},n.types){var o=n.types[i];n.codes[o]=i}for(var s in n.CMD_SHIFT=4,n.CMD_MASK=240,n.DUP_MASK=8,n.QOS_MASK=3,n.QOS_SHIFT=1,n.RETAIN_MASK=1,n.VARBYTEINT_MASK=127,n.VARBYTEINT_FIN_MASK=128,n.VARBYTEINT_MAX=268435455,n.SESSIONPRESENT_MASK=1,n.SESSIONPRESENT_HEADER=t.from([n.SESSIONPRESENT_MASK]),n.CONNACK_HEADER=t.from([n.codes.connack<<n.CMD_SHIFT]),n.USERNAME_MASK=128,n.PASSWORD_MASK=64,n.WILL_RETAIN_MASK=32,n.WILL_QOS_MASK=24,n.WILL_QOS_SHIFT=3,n.WILL_FLAG_MASK=4,n.CLEAN_SESSION_MASK=2,n.CONNECT_HEADER=t.from([n.codes.connect<<n.CMD_SHIFT]),n.properties={sessionExpiryInterval:17,willDelayInterval:24,receiveMaximum:33,maximumPacketSize:39,topicAliasMaximum:34,requestResponseInformation:25,requestProblemInformation:23,userProperties:38,authenticationMethod:21,authenticationData:22,payloadFormatIndicator:1,messageExpiryInterval:2,contentType:3,responseTopic:8,correlationData:9,maximumQoS:36,retainAvailable:37,assignedClientIdentifier:18,reasonString:31,wildcardSubscriptionAvailable:40,subscriptionIdentifiersAvailable:41,sharedSubscriptionAvailable:42,serverKeepAlive:19,responseInformation:26,serverReference:28,topicAlias:35,subscriptionIdentifier:11},n.propertiesCodes={},n.properties){var a=n.properties[s];n.propertiesCodes[a]=s}function c(e){return[0,1,2].map((function(r){return[0,1].map((function(i){return[0,1].map((function(o){var s=t.alloc(1);return s.writeUInt8(n.codes[e]<<n.CMD_SHIFT|(i?n.DUP_MASK:0)|r<<n.QOS_SHIFT|o,0,!0),s}))}))}))}n.propertiesTypes={sessionExpiryInterval:"int32",willDelayInterval:"int32",receiveMaximum:"int16",maximumPacketSize:"int32",topicAliasMaximum:"int16",requestResponseInformation:"byte",requestProblemInformation:"byte",userProperties:"pair",authenticationMethod:"string",authenticationData:"binary",payloadFormatIndicator:"byte",messageExpiryInterval:"int32",contentType:"string",responseTopic:"string",correlationData:"binary",maximumQoS:"int8",retainAvailable:"byte",assignedClientIdentifier:"string",reasonString:"string",wildcardSubscriptionAvailable:"byte",subscriptionIdentifiersAvailable:"byte",sharedSubscriptionAvailable:"byte",serverKeepAlive:"int16",responseInformation:"string",serverReference:"string",topicAlias:"int16",subscriptionIdentifier:"var"},n.PUBLISH_HEADER=c("publish"),n.SUBSCRIBE_HEADER=c("subscribe"),n.SUBSCRIBE_OPTIONS_QOS_MASK=3,n.SUBSCRIBE_OPTIONS_NL_MASK=1,n.SUBSCRIBE_OPTIONS_NL_SHIFT=2,n.SUBSCRIBE_OPTIONS_RAP_MASK=1,n.SUBSCRIBE_OPTIONS_RAP_SHIFT=3,n.SUBSCRIBE_OPTIONS_RH_MASK=3,n.SUBSCRIBE_OPTIONS_RH_SHIFT=4,n.SUBSCRIBE_OPTIONS_RH=[0,16,32],n.SUBSCRIBE_OPTIONS_NL=4,n.SUBSCRIBE_OPTIONS_RAP=8,n.SUBSCRIBE_OPTIONS_QOS=[0,1,2],n.UNSUBSCRIBE_HEADER=c("unsubscribe"),n.ACKS={unsuback:c("unsuback"),puback:c("puback"),pubcomp:c("pubcomp"),pubrel:c("pubrel"),pubrec:c("pubrec")},n.SUBACK_HEADER=t.from([n.codes.suback<<n.CMD_SHIFT]),n.VERSION3=t.from([3]),n.VERSION4=t.from([4]),n.VERSION5=t.from([5]),n.VERSION131=t.from([131]),n.VERSION132=t.from([132]),n.QOS=[0,1,2].map((function(e){return t.from([e])})),n.EMPTY={pingreq:t.from([n.codes.pingreq<<4,0]),pingresp:t.from([n.codes.pingresp<<4,0]),disconnect:t.from([n.codes.disconnect<<4,0])}}).call(this,r("12e3").Buffer)},"5ebd":function(e,t,r){"use strict";(function(t,n){r("bf0f"),r("7a76"),r("c9b5"),r("18f7"),r("de6c"),r("dc89"),r("2425"),r("80e3"),r("4db2"),r("aa9c"),r("c223");var i=r("3eae"),o=r("172d")("mqttjs:ws"),s=r("3e6ee"),a=r("7125").Transform,c=["rejectUnauthorized","ca","cert","key","pfx","passphrase"],u="undefined"!==typeof t&&"browser"===t.title||"function"===typeof r;function l(e,t){var r=e.protocol+"://"+e.hostname+":"+e.port+e.path;return"function"===typeof e.transformWsUrl&&(r=e.transformWsUrl(r,e,t)),r}function f(e){var t=e;return e.hostname||(t.hostname="localhost"),e.port||("wss"===e.protocol?t.port=443:t.port=80),e.path||(t.path="/"),e.wsOptions||(t.wsOptions={}),u||"wss"!==e.protocol||c.forEach((function(r){e.hasOwnProperty(r)&&!e.wsOptions.hasOwnProperty(r)&&(t.wsOptions[r]=e[r])})),t}e.exports=u?function(e,t){var r;o("browserStreamBuilder");var i=function(e){var t=f(e);if(t.hostname||(t.hostname=t.host),!t.hostname){if("undefined"===typeof document)throw new Error("Could not determine host. Specify host manually.");var r=new URL(document.URL);t.hostname=r.hostname,t.port||(t.port=r.port)}return void 0===t.objectMode&&(t.objectMode=!(!0===t.binary||void 0===t.binary)),t}(t),c=i.browserBufferSize||524288,u=t.browserBufferTimeout||1e3,p=!t.objectMode,h=function(e,t){var r="MQIsdp"===t.protocolId&&3===t.protocolVersion?"mqttv3.1":"mqtt",n=l(t,e),i=new WebSocket(n,[r]);return i.binaryType="arraybuffer",i}(e,t),d=function(e,t,r){var n=new a({objectModeMode:e.objectMode});return n._write=t,n._flush=r,n}(t,(function e(t,r,i){h.bufferedAmount>c&&setTimeout(e,u,t,r,i);p&&"string"===typeof t&&(t=n.from(t,"utf8"));try{h.send(t)}catch(o){return i(o)}i()}),(function(e){h.close(),e()}));t.objectMode||(d._writev=_),d.on("close",(function(){h.close()}));var g="undefined"!==typeof h.addEventListener;function b(){r.setReadable(d),r.setWritable(d),r.emit("connect")}function m(){r.end(),r.destroy()}function v(e){r.destroy(e)}function y(e){var t=e.data;t=t instanceof ArrayBuffer?n.from(t):n.from(t,"utf8"),d.push(t)}function _(e,t){for(var r=new Array(e.length),i=0;i<e.length;i++)"string"===typeof e[i].chunk?r[i]=n.from(e[i],"utf8"):r[i]=e[i].chunk;this._write(n.concat(r),"binary",t)}return h.readyState===h.OPEN?r=d:(r=r=s(void 0,void 0,t),t.objectMode||(r._writev=_),g?h.addEventListener("open",b):h.onopen=b),r.socket=h,g?(h.addEventListener("close",m),h.addEventListener("error",v),h.addEventListener("message",y)):(h.onclose=m,h.onerror=v,h.onmessage=y),r}:function(e,t){o("streamBuilder");var r=f(t),n=l(r,e),s=function(e,t,r){o("createWebSocket"),o("protocol: "+r.protocolId+" "+r.protocolVersion);var n="MQIsdp"===r.protocolId&&3===r.protocolVersion?"mqttv3.1":"mqtt";o("creating new Websocket for url: "+t+" and protocol: "+n);var s=new i(t,[n],r.wsOptions);return s}(0,n,r),a=i.createWebSocketStream(s,r.wsOptions);return a.url=n,s.on("close",(function(){a.destroy()})),a}}).call(this,r("28d0"),r("12e3").Buffer)},"5f37":function(e,t){e.exports=function(e){var t=e._readableState;return t?t.objectMode||"number"===typeof e._duplexState?e.read():e.read(function(e){if(e.buffer.length)return e.buffer.head?e.buffer.head.data.length:e.buffer[0].length;return e.length}(t)):null}},"5f79":function(e,t,r){
/*! safe-buffer. MIT License. Feross Aboukhadijeh <https://feross.org/opensource> */
var n=r("12e3"),i=n.Buffer;function o(e,t){for(var r in e)t[r]=e[r]}function s(e,t,r){return i(e,t,r)}i.from&&i.alloc&&i.allocUnsafe&&i.allocUnsafeSlow?e.exports=n:(o(n,t),t.Buffer=s),s.prototype=Object.create(i.prototype),o(i,s),s.from=function(e,t,r){if("number"===typeof e)throw new TypeError("Argument must not be a number");return i(e,t,r)},s.alloc=function(e,t,r){if("number"!==typeof e)throw new TypeError("Argument must be a number");var n=i(e);return void 0!==t?"string"===typeof r?n.fill(t,r):n.fill(t):n.fill(0),n},s.allocUnsafe=function(e){if("number"!==typeof e)throw new TypeError("Argument must be a number");return i(e)},s.allocUnsafeSlow=function(e){if("number"!==typeof e)throw new TypeError("Argument must be a number");return n.SlowBuffer(e)}},"5f85":function(e,t,r){"use strict";var n=r("5f79").Buffer,i=n.isEncoding||function(e){switch(e=""+e,e&&e.toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":case"raw":return!0;default:return!1}};function o(e){var t;switch(this.encoding=function(e){var t=function(e){if(!e)return"utf8";var t;while(1)switch(e){case"utf8":case"utf-8":return"utf8";case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return"utf16le";case"latin1":case"binary":return"latin1";case"base64":case"ascii":case"hex":return e;default:if(t)return;e=(""+e).toLowerCase(),t=!0}}(e);if("string"!==typeof t&&(n.isEncoding===i||!i(e)))throw new Error("Unknown encoding: "+e);return t||e}(e),this.encoding){case"utf16le":this.text=c,this.end=u,t=4;break;case"utf8":this.fillLast=a,t=4;break;case"base64":this.text=l,this.end=f,t=3;break;default:return this.write=p,void(this.end=h)}this.lastNeed=0,this.lastTotal=0,this.lastChar=n.allocUnsafe(t)}function s(e){return e<=127?0:e>>5===6?2:e>>4===14?3:e>>3===30?4:e>>6===2?-1:-2}function a(e){var t=this.lastTotal-this.lastNeed,r=function(e,t,r){if(128!==(192&t[0]))return e.lastNeed=0,"�";if(e.lastNeed>1&&t.length>1){if(128!==(192&t[1]))return e.lastNeed=1,"�";if(e.lastNeed>2&&t.length>2&&128!==(192&t[2]))return e.lastNeed=2,"�"}}(this,e);return void 0!==r?r:this.lastNeed<=e.length?(e.copy(this.lastChar,t,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal)):(e.copy(this.lastChar,t,0,e.length),void(this.lastNeed-=e.length))}function c(e,t){if((e.length-t)%2===0){var r=e.toString("utf16le",t);if(r){var n=r.charCodeAt(r.length-1);if(n>=55296&&n<=56319)return this.lastNeed=2,this.lastTotal=4,this.lastChar[0]=e[e.length-2],this.lastChar[1]=e[e.length-1],r.slice(0,-1)}return r}return this.lastNeed=1,this.lastTotal=2,this.lastChar[0]=e[e.length-1],e.toString("utf16le",t,e.length-1)}function u(e){var t=e&&e.length?this.write(e):"";if(this.lastNeed){var r=this.lastTotal-this.lastNeed;return t+this.lastChar.toString("utf16le",0,r)}return t}function l(e,t){var r=(e.length-t)%3;return 0===r?e.toString("base64",t):(this.lastNeed=3-r,this.lastTotal=3,1===r?this.lastChar[0]=e[e.length-1]:(this.lastChar[0]=e[e.length-2],this.lastChar[1]=e[e.length-1]),e.toString("base64",t,e.length-r))}function f(e){var t=e&&e.length?this.write(e):"";return this.lastNeed?t+this.lastChar.toString("base64",0,3-this.lastNeed):t}function p(e){return e.toString(this.encoding)}function h(e){return e&&e.length?this.write(e):""}t.StringDecoder=o,o.prototype.write=function(e){if(0===e.length)return"";var t,r;if(this.lastNeed){if(t=this.fillLast(e),void 0===t)return"";r=this.lastNeed,this.lastNeed=0}else r=0;return r<e.length?t?t+this.text(e,r):this.text(e,r):t||""},o.prototype.end=function(e){var t=e&&e.length?this.write(e):"";return this.lastNeed?t+"�":t},o.prototype.text=function(e,t){var r=function(e,t,r){var n=t.length-1;if(n<r)return 0;var i=s(t[n]);if(i>=0)return i>0&&(e.lastNeed=i-1),i;if(--n<r||-2===i)return 0;if(i=s(t[n]),i>=0)return i>0&&(e.lastNeed=i-2),i;if(--n<r||-2===i)return 0;if(i=s(t[n]),i>=0)return i>0&&(2===i?i=0:e.lastNeed=i-3),i;return 0}(this,e,t);if(!this.lastNeed)return e.toString("utf8",t);this.lastTotal=r;var n=e.length-(r-this.lastNeed);return e.copy(this.lastChar,0,n),e.toString("utf8",t,n)},o.prototype.fillLast=function(e){if(this.lastNeed<=e.length)return e.copy(this.lastChar,this.lastTotal-this.lastNeed,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal);e.copy(this.lastChar,this.lastTotal-this.lastNeed,0,e.length),this.lastNeed-=e.length}},6502:function(e,t,r){e.exports=function(e){function t(e){let r,i,o,s=null;function a(...e){if(!a.enabled)return;const n=a,i=Number(new Date),o=i-(r||i);n.diff=o,n.prev=r,n.curr=i,r=i,e[0]=t.coerce(e[0]),"string"!==typeof e[0]&&e.unshift("%O");let s=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,(r,i)=>{if("%%"===r)return"%";s++;const o=t.formatters[i];if("function"===typeof o){const t=e[s];r=o.call(n,t),e.splice(s,1),s--}return r}),t.formatArgs.call(n,e);const c=n.log||t.log;c.apply(n,e)}return a.namespace=e,a.useColors=t.useColors(),a.color=t.selectColor(e),a.extend=n,a.destroy=t.destroy,Object.defineProperty(a,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==s?s:(i!==t.namespaces&&(i=t.namespaces,o=t.enabled(e)),o),set:e=>{s=e}}),"function"===typeof t.init&&t.init(a),a}function n(e,r){const n=t(this.namespace+("undefined"===typeof r?":":r)+e);return n.log=this.log,n}function i(e){return e.toString().substring(2,e.toString().length-2).replace(/\.\*\?$/,"*")}return t.debug=t,t.default=t,t.coerce=function(e){if(e instanceof Error)return e.stack||e.message;return e},t.disable=function(){const e=[...t.names.map(i),...t.skips.map(i).map(e=>"-"+e)].join(",");return t.enable(""),e},t.enable=function(e){let r;t.save(e),t.namespaces=e,t.names=[],t.skips=[];const n=("string"===typeof e?e:"").split(/[\s,]+/),i=n.length;for(r=0;r<i;r++)n[r]&&(e=n[r].replace(/\*/g,".*?"),"-"===e[0]?t.skips.push(new RegExp("^"+e.slice(1)+"$")):t.names.push(new RegExp("^"+e+"$")))},t.enabled=function(e){if("*"===e[e.length-1])return!0;let r,n;for(r=0,n=t.skips.length;r<n;r++)if(t.skips[r].test(e))return!1;for(r=0,n=t.names.length;r<n;r++)if(t.names[r].test(e))return!0;return!1},t.humanize=r("11aa"),t.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach(r=>{t[r]=e[r]}),t.names=[],t.skips=[],t.formatters={},t.selectColor=function(e){let r=0;for(let t=0;t<e.length;t++)r=(r<<5)-r+e.charCodeAt(t),r|=0;return t.colors[Math.abs(r)%t.colors.length]},t.enable(t.load()),t}},"66bc":function(e,t,r){"use strict";r.r(t);var n=r("fa9e"),i=r("3a39");for(var o in i)["default"].indexOf(o)<0&&function(e){r.d(t,e,(function(){return i[e]}))}(o);r("aa04");var s=r("828b"),a=Object(s["a"])(i["default"],n["b"],n["c"],!1,null,"76271e56",null,!1,n["a"],void 0);t["default"]=a.exports},"66fd":function(e,t,r){var n=r("883d").default,i=r("4db6").default,o=r("59a6").default,s=r("feb3").default;r("7a76"),r("c9b5"),r("7f48"),r("f7a5"),r("aa9c"),r("bf0f"),r("ab80");var a=r("07d7"),c=r("bf30"),u=r("837f"),l=r("5a67"),f=r("172d")("mqtt-packet:parser"),p=function(e){"use strict";o(r,e);var t=s(r);function r(){var e;return n(this,r),e=t.call(this),e.parser=e.constructor.parser,e}return i(r,[{key:"_resetState",value:function(){f("_resetState: resetting packet, error, _list, and _stateCounter"),this.packet=new u,this.error=null,this._list=a(),this._stateCounter=0}},{key:"parse",value:function(e){this.error&&this._resetState(),this._list.append(e),f("parse: current state: %s",this._states[this._stateCounter]);while((-1!==this.packet.length||this._list.length>0)&&this[this._states[this._stateCounter]]()&&!this.error)this._stateCounter++,f("parse: state complete. _stateCounter is now: %d",this._stateCounter),f("parse: packet.length: %d, buffer list length: %d",this.packet.length,this._list.length),this._stateCounter>=this._states.length&&(this._stateCounter=0);return f("parse: exited while loop. packet: %d, buffer list length: %d",this.packet.length,this._list.length),this._list.length}},{key:"_parseHeader",value:function(){var e=this._list.readUInt8(0);return this.packet.cmd=l.types[e>>l.CMD_SHIFT],this.packet.retain=0!==(e&l.RETAIN_MASK),this.packet.qos=e>>l.QOS_SHIFT&l.QOS_MASK,this.packet.dup=0!==(e&l.DUP_MASK),f("_parseHeader: packet: %o",this.packet),this._list.consume(1),!0}},{key:"_parseLength",value:function(){var e=this._parseVarByteNum(!0);return e&&(this.packet.length=e.value,this._list.consume(e.bytes)),f("_parseLength %d",e.value),!!e}},{key:"_parsePayload",value:function(){f("_parsePayload: payload %O",this._list);var e=!1;if(0===this.packet.length||this._list.length>=this.packet.length){switch(this._pos=0,this.packet.cmd){case"connect":this._parseConnect();break;case"connack":this._parseConnack();break;case"publish":this._parsePublish();break;case"puback":case"pubrec":case"pubrel":case"pubcomp":this._parseConfirmation();break;case"subscribe":this._parseSubscribe();break;case"suback":this._parseSuback();break;case"unsubscribe":this._parseUnsubscribe();break;case"unsuback":this._parseUnsuback();break;case"pingreq":case"pingresp":break;case"disconnect":this._parseDisconnect();break;case"auth":this._parseAuth();break;default:this._emitError(new Error("Not supported"))}e=!0}return f("_parsePayload complete result: %s",e),e}},{key:"_parseConnect",value:function(){var e,t,r,n;f("_parseConnect");var i={},o=this.packet,s=this._parseString();if(null===s)return this._emitError(new Error("Cannot parse protocolId"));if("MQTT"!==s&&"MQIsdp"!==s)return this._emitError(new Error("Invalid protocolId"));if(o.protocolId=s,this._pos>=this._list.length)return this._emitError(new Error("Packet too short"));if(o.protocolVersion=this._list.readUInt8(this._pos),o.protocolVersion>=128&&(o.bridgeMode=!0,o.protocolVersion=o.protocolVersion-128),3!==o.protocolVersion&&4!==o.protocolVersion&&5!==o.protocolVersion)return this._emitError(new Error("Invalid protocol version"));if(this._pos++,this._pos>=this._list.length)return this._emitError(new Error("Packet too short"));if(i.username=this._list.readUInt8(this._pos)&l.USERNAME_MASK,i.password=this._list.readUInt8(this._pos)&l.PASSWORD_MASK,i.will=this._list.readUInt8(this._pos)&l.WILL_FLAG_MASK,i.will&&(o.will={},o.will.retain=0!==(this._list.readUInt8(this._pos)&l.WILL_RETAIN_MASK),o.will.qos=(this._list.readUInt8(this._pos)&l.WILL_QOS_MASK)>>l.WILL_QOS_SHIFT),o.clean=0!==(this._list.readUInt8(this._pos)&l.CLEAN_SESSION_MASK),this._pos++,o.keepalive=this._parseNum(),-1===o.keepalive)return this._emitError(new Error("Packet too short"));if(5===o.protocolVersion){var a=this._parseProperties();Object.getOwnPropertyNames(a).length&&(o.properties=a)}var c=this._parseString();if(null===c)return this._emitError(new Error("Packet too short"));if(o.clientId=c,f("_parseConnect: packet.clientId: %s",o.clientId),i.will){if(5===o.protocolVersion){var u=this._parseProperties();Object.getOwnPropertyNames(u).length&&(o.will.properties=u)}if(e=this._parseString(),null===e)return this._emitError(new Error("Cannot parse will topic"));if(o.will.topic=e,f("_parseConnect: packet.will.topic: %s",o.will.topic),t=this._parseBuffer(),null===t)return this._emitError(new Error("Cannot parse will payload"));o.will.payload=t,f("_parseConnect: packet.will.paylaod: %s",o.will.payload)}if(i.username){if(n=this._parseString(),null===n)return this._emitError(new Error("Cannot parse username"));o.username=n,f("_parseConnect: packet.username: %s",o.username)}if(i.password){if(r=this._parseBuffer(),null===r)return this._emitError(new Error("Cannot parse password"));o.password=r}return this.settings=o,f("_parseConnect: complete"),o}},{key:"_parseConnack",value:function(){f("_parseConnack");var e=this.packet;if(this._list.length<1)return null;if(e.sessionPresent=!!(this._list.readUInt8(this._pos++)&l.SESSIONPRESENT_MASK),5===this.settings.protocolVersion)this._list.length>=2?e.reasonCode=this._list.readUInt8(this._pos++):e.reasonCode=0;else{if(this._list.length<2)return null;e.returnCode=this._list.readUInt8(this._pos++)}if(-1===e.returnCode||-1===e.reasonCode)return this._emitError(new Error("Cannot parse return code"));if(5===this.settings.protocolVersion){var t=this._parseProperties();Object.getOwnPropertyNames(t).length&&(e.properties=t)}f("_parseConnack: complete")}},{key:"_parsePublish",value:function(){f("_parsePublish");var e=this.packet;if(e.topic=this._parseString(),null===e.topic)return this._emitError(new Error("Cannot parse topic"));if(!(e.qos>0)||this._parseMessageId()){if(5===this.settings.protocolVersion){var t=this._parseProperties();Object.getOwnPropertyNames(t).length&&(e.properties=t)}e.payload=this._list.slice(this._pos,e.length),f("_parsePublish: payload from buffer list: %o",e.payload)}}},{key:"_parseSubscribe",value:function(){f("_parseSubscribe");var e,t,r,n,i,o,s,a=this.packet;if(1!==a.qos)return this._emitError(new Error("Wrong subscribe header"));if(a.subscriptions=[],this._parseMessageId()){if(5===this.settings.protocolVersion){var c=this._parseProperties();Object.getOwnPropertyNames(c).length&&(a.properties=c)}while(this._pos<a.length){if(e=this._parseString(),null===e)return this._emitError(new Error("Cannot parse topic"));if(this._pos>=a.length)return this._emitError(new Error("Malformed Subscribe Payload"));t=this._parseByte(),r=t&l.SUBSCRIBE_OPTIONS_QOS_MASK,o=0!==(t>>l.SUBSCRIBE_OPTIONS_NL_SHIFT&l.SUBSCRIBE_OPTIONS_NL_MASK),i=0!==(t>>l.SUBSCRIBE_OPTIONS_RAP_SHIFT&l.SUBSCRIBE_OPTIONS_RAP_MASK),n=t>>l.SUBSCRIBE_OPTIONS_RH_SHIFT&l.SUBSCRIBE_OPTIONS_RH_MASK,s={topic:e,qos:r},5===this.settings.protocolVersion?(s.nl=o,s.rap=i,s.rh=n):this.settings.bridgeMode&&(s.rh=0,s.rap=!0,s.nl=!0),f("_parseSubscribe: push subscription `%s` to subscription",s),a.subscriptions.push(s)}}}},{key:"_parseSuback",value:function(){f("_parseSuback");var e=this.packet;if(this.packet.granted=[],this._parseMessageId()){if(5===this.settings.protocolVersion){var t=this._parseProperties();Object.getOwnPropertyNames(t).length&&(e.properties=t)}while(this._pos<this.packet.length)this.packet.granted.push(this._list.readUInt8(this._pos++))}}},{key:"_parseUnsubscribe",value:function(){f("_parseUnsubscribe");var e=this.packet;if(e.unsubscriptions=[],this._parseMessageId()){if(5===this.settings.protocolVersion){var t=this._parseProperties();Object.getOwnPropertyNames(t).length&&(e.properties=t)}while(this._pos<e.length){var r=this._parseString();if(null===r)return this._emitError(new Error("Cannot parse topic"));f("_parseUnsubscribe: push topic `%s` to unsubscriptions",r),e.unsubscriptions.push(r)}}}},{key:"_parseUnsuback",value:function(){f("_parseUnsuback");var e=this.packet;if(!this._parseMessageId())return this._emitError(new Error("Cannot parse messageId"));if(5===this.settings.protocolVersion){var t=this._parseProperties();Object.getOwnPropertyNames(t).length&&(e.properties=t),e.granted=[];while(this._pos<this.packet.length)this.packet.granted.push(this._list.readUInt8(this._pos++))}}},{key:"_parseConfirmation",value:function(){f("_parseConfirmation: packet.cmd: `%s`",this.packet.cmd);var e=this.packet;if(this._parseMessageId(),5===this.settings.protocolVersion&&(e.length>2?(e.reasonCode=this._parseByte(),f("_parseConfirmation: packet.reasonCode `%d`",e.reasonCode)):e.reasonCode=0,e.length>3)){var t=this._parseProperties();Object.getOwnPropertyNames(t).length&&(e.properties=t)}return!0}},{key:"_parseDisconnect",value:function(){var e=this.packet;if(f("_parseDisconnect"),5===this.settings.protocolVersion){this._list.length>0?e.reasonCode=this._parseByte():e.reasonCode=0;var t=this._parseProperties();Object.getOwnPropertyNames(t).length&&(e.properties=t)}return f("_parseDisconnect result: true"),!0}},{key:"_parseAuth",value:function(){f("_parseAuth");var e=this.packet;if(5!==this.settings.protocolVersion)return this._emitError(new Error("Not supported auth packet for this version MQTT"));e.reasonCode=this._parseByte();var t=this._parseProperties();return Object.getOwnPropertyNames(t).length&&(e.properties=t),f("_parseAuth: result: true"),!0}},{key:"_parseMessageId",value:function(){var e=this.packet;return e.messageId=this._parseNum(),null===e.messageId?(this._emitError(new Error("Cannot parse messageId")),!1):(f("_parseMessageId: packet.messageId %d",e.messageId),!0)}},{key:"_parseString",value:function(e){var t=this._parseNum(),r=t+this._pos;if(-1===t||r>this._list.length||r>this.packet.length)return null;var n=this._list.toString("utf8",this._pos,r);return this._pos+=t,f("_parseString: result: %s",n),n}},{key:"_parseStringPair",value:function(){return f("_parseStringPair"),{name:this._parseString(),value:this._parseString()}}},{key:"_parseBuffer",value:function(){var e=this._parseNum(),t=e+this._pos;if(-1===e||t>this._list.length||t>this.packet.length)return null;var r=this._list.slice(this._pos,t);return this._pos+=e,f("_parseBuffer: result: %o",r),r}},{key:"_parseNum",value:function(){if(this._list.length-this._pos<2)return-1;var e=this._list.readUInt16BE(this._pos);return this._pos+=2,f("_parseNum: result: %s",e),e}},{key:"_parse4ByteNum",value:function(){if(this._list.length-this._pos<4)return-1;var e=this._list.readUInt32BE(this._pos);return this._pos+=4,f("_parse4ByteNum: result: %s",e),e}},{key:"_parseVarByteNum",value:function(e){f("_parseVarByteNum");var t,r=0,n=1,i=0,o=!1,s=this._pos?this._pos:0;while(r<4&&s+r<this._list.length){if(t=this._list.readUInt8(s+r++),i+=n*(t&l.VARBYTEINT_MASK),n*=128,0===(t&l.VARBYTEINT_FIN_MASK)){o=!0;break}if(this._list.length<=r)break}return!o&&4===r&&this._list.length>=r&&this._emitError(new Error("Invalid variable byte integer")),s&&(this._pos+=r),o=!!o&&(e?{bytes:r,value:i}:i),f("_parseVarByteNum: result: %o",o),o}},{key:"_parseByte",value:function(){var e;return this._pos<this._list.length&&(e=this._list.readUInt8(this._pos),this._pos++),f("_parseByte: result: %o",e),e}},{key:"_parseByType",value:function(e){switch(f("_parseByType: type: %s",e),e){case"byte":return 0!==this._parseByte();case"int8":return this._parseByte();case"int16":return this._parseNum();case"int32":return this._parse4ByteNum();case"var":return this._parseVarByteNum();case"string":return this._parseString();case"pair":return this._parseStringPair();case"binary":return this._parseBuffer()}}},{key:"_parseProperties",value:function(){f("_parseProperties");var e=this._parseVarByteNum(),t=this._pos,r=t+e,n={};while(this._pos<r){var i=this._parseByte();if(!i)return this._emitError(new Error("Cannot parse property code type")),!1;var o=l.propertiesCodes[i];if(!o)return this._emitError(new Error("Unknown property")),!1;if("userProperties"!==o)n[o]?(Array.isArray(n[o])||(n[o]=[n[o]]),n[o].push(this._parseByType(l.propertiesTypes[o]))):n[o]=this._parseByType(l.propertiesTypes[o]);else{n[o]||(n[o]=Object.create(null));var s=this._parseByType(l.propertiesTypes[o]);if(n[o][s.name])if(Array.isArray(n[o][s.name]))n[o][s.name].push(s.value);else{var a=n[o][s.name];n[o][s.name]=[a],n[o][s.name].push(s.value)}else n[o][s.name]=s.value}}return n}},{key:"_newPacket",value:function(){return f("_newPacket"),this.packet&&(this._list.consume(this.packet.length),f("_newPacket: parser emit packet: packet.cmd: %s, packet.payload: %s, packet.length: %d",this.packet.cmd,this.packet.payload,this.packet.length),this.emit("packet",this.packet)),f("_newPacket: new packet"),this.packet=new u,this._pos=0,!0}},{key:"_emitError",value:function(e){f("_emitError"),this.error=e,this.emit("error",e)}}],[{key:"parser",value:function(e){return this instanceof r?(this.settings=e||{},this._states=["_parseHeader","_parseLength","_parsePayload","_newPacket"],this._resetState(),this):(new r).parser(e)}}]),r}(c);e.exports=p},"6b03":function(e,t,r){"use strict";var n=r(3),i=r("172d")("mqttjs:tcp");e.exports=function(e,t){var r,o;return t.port=t.port||1883,t.hostname=t.hostname||t.host||"localhost",r=t.port,o=t.hostname,i("port %d and host %s",r,o),n.createConnection(r,o)}},7125:function(e,t,r){t=e.exports=r("46dd"),t.Stream=t,t.Readable=t,t.Writable=r("edd4"),t.Duplex=r("0ec1"),t.Transform=r("0f55"),t.PassThrough=r("7f8f")},7627:function(e,t,r){"use strict";r("35fd"),r("855b")},"764e":function(e,t,r){"use strict";r.r(t);var n=r("89d2"),i=r("f6e0");for(var o in i)["default"].indexOf(o)<0&&function(e){r.d(t,e,(function(){return i[e]}))}(o);r("e03a");var s=r("828b"),a=Object(s["a"])(i["default"],n["b"],n["c"],!1,null,"59765974",null,!1,n["a"],void 0);t["default"]=a.exports},7891:function(e,t,r){"use strict";r.r(t);var n=r("0743"),i=r.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){r.d(t,e,(function(){return n[e]}))}(o);t["default"]=i.a},"79b7":function(e,t,r){r("f7a5"),r("bf0f"),r("08eb"),r("18f7"),r("5c47"),r("0506");var n=r("e476");e.exports=function(e,t){if(e){if("string"===typeof e)return n(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?n(e,t):void 0}},e.exports.__esModule=!0,e.exports["default"]=e.exports},"7c7d":function(e,t,r){var n=r("014e");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);var i=r("967d").default;i("1a8da346",n,!0,{sourceMap:!1,shadowMode:!1})},"7e5f":function(e,t,r){"use strict";r.d(t,"b",(function(){return n})),r.d(t,"c",(function(){return i})),r.d(t,"a",(function(){}));var n=function(){var e=this,t=e.$createElement,r=e._self._c||t;return e.show?r("v-uni-view",{staticClass:"u-loading-icon",class:[e.vertical&&"u-loading-icon--vertical"],style:[e.$u.addStyle(e.customStyle)]},[e.webviewHide?e._e():r("v-uni-view",{ref:"ani",staticClass:"u-loading-icon__spinner",class:["u-loading-icon__spinner--"+e.mode],style:{color:e.color,width:e.$u.addUnit(e.size),height:e.$u.addUnit(e.size),borderTopColor:e.color,borderBottomColor:e.otherBorderColor,borderLeftColor:e.otherBorderColor,borderRightColor:e.otherBorderColor,"animation-duration":e.duration+"ms","animation-timing-function":"semicircle"===e.mode||"circle"===e.mode?e.timingFunction:""}},["spinner"===e.mode?e._l(e.array12,(function(e,t){return r("v-uni-view",{key:t,staticClass:"u-loading-icon__dot"})})):e._e()],2),e.text?r("v-uni-text",{staticClass:"u-loading-icon__text",style:{fontSize:e.$u.addUnit(e.textSize),color:e.textColor}},[e._v(e._s(e.text))]):e._e()],1):e._e()},i=[]},"7f8f":function(e,t,r){"use strict";e.exports=o;var n=r("0f55"),i=Object.create(r("8ce8"));function o(e){if(!(this instanceof o))return new o(e);n.call(this,e)}i.inherits=r("2c2e"),i.inherits(o,n),o.prototype._transform=function(e,t,r){r(null,e)}},"837f":function(e,t,r){var n=r("4db6").default,i=r("883d").default,o=n((function e(){"use strict";i(this,e),this.cmd=null,this.retain=!1,this.qos=0,this.dup=!1,this.length=-1,this.topic=null,this.payload=null}));e.exports=o},"855b":function(e,t,r){"use strict";var n=r("8bdb"),i=r("85c1"),o=r("2c61").set,s=r("caba"),a=i.setImmediate?s(o,!1):o;n({global:!0,bind:!0,enumerable:!0,forced:i.setImmediate!==a},{setImmediate:a})},"883d":function(e,t,r){r("7a76"),r("c9b5"),e.exports=function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")},e.exports.__esModule=!0,e.exports["default"]=e.exports},"88fd":function(e,t,r){(function(t){function r(e){try{if(!t.localStorage)return!1}catch(n){return!1}var r=t.localStorage[e];return null!=r&&"true"===String(r).toLowerCase()}e.exports=function(e,t){if(r("noDeprecation"))return e;var n=!1;return function(){if(!n){if(r("throwDeprecation"))throw new Error(t);r("traceDeprecation")?console.trace(t):console.warn(t),n=!0}return e.apply(this,arguments)}}}).call(this,r("0ee4"))},"89d2":function(e,t,r){"use strict";r.d(t,"b",(function(){return n})),r.d(t,"c",(function(){return i})),r.d(t,"a",(function(){}));var n=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("v-uni-view",{staticClass:"u-icon",class:["u-icon--"+e.labelPos],on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.clickHandler.apply(void 0,arguments)}}},[e.isImg?r("v-uni-image",{staticClass:"u-icon__img",style:[e.imgStyle,e.$u.addStyle(e.customStyle)],attrs:{src:e.name,mode:e.imgMode}}):r("v-uni-text",{staticClass:"u-icon__icon",class:e.uClasses,style:[e.iconStyle,e.$u.addStyle(e.customStyle)],attrs:{"hover-class":e.hoverClass}},[e._v(e._s(e.icon))]),""!==e.label?r("v-uni-text",{staticClass:"u-icon__label",style:{color:e.labelColor,fontSize:e.$u.addUnit(e.labelSize),marginLeft:"right"==e.labelPos?e.$u.addUnit(e.space):0,marginTop:"bottom"==e.labelPos?e.$u.addUnit(e.space):0,marginRight:"left"==e.labelPos?e.$u.addUnit(e.space):0,marginBottom:"top"==e.labelPos?e.$u.addUnit(e.space):0}},[e._v(e._s(e.label))]):e._e()],1)},i=[]},"8bcf":function(e,t,r){var n=r("bdbb")["default"],i=r("56c9");e.exports=function(e){var t=i(e,"string");return"symbol"===n(t)?t:String(t)},e.exports.__esModule=!0,e.exports["default"]=e.exports},"8ce8":function(e,t,r){function n(e){return Object.prototype.toString.call(e)}t.isArray=function(e){return Array.isArray?Array.isArray(e):"[object Array]"===n(e)},t.isBoolean=function(e){return"boolean"===typeof e},t.isNull=function(e){return null===e},t.isNullOrUndefined=function(e){return null==e},t.isNumber=function(e){return"number"===typeof e},t.isString=function(e){return"string"===typeof e},t.isSymbol=function(e){return"symbol"===typeof e},t.isUndefined=function(e){return void 0===e},t.isRegExp=function(e){return"[object RegExp]"===n(e)},t.isObject=function(e){return"object"===typeof e&&null!==e},t.isDate=function(e){return"[object Date]"===n(e)},t.isError=function(e){return"[object Error]"===n(e)||e instanceof Error},t.isFunction=function(e){return"function"===typeof e},t.isPrimitive=function(e){return null===e||"boolean"===typeof e||"number"===typeof e||"string"===typeof e||"symbol"===typeof e||"undefined"===typeof e},t.isBuffer=r("12e3").Buffer.isBuffer},"90c3":function(e,t,r){"use strict";function n(e,t){return Object.prototype.hasOwnProperty.call(e,t)}e.exports=function(e,t,r,o){t=t||"&",r=r||"=";var s={};if("string"!==typeof e||0===e.length)return s;var a=/\+/g;e=e.split(t);var c=1e3;o&&"number"===typeof o.maxKeys&&(c=o.maxKeys);var u=e.length;c>0&&u>c&&(u=c);for(var l=0;l<u;++l){var f,p,h,d,g=e[l].replace(a,"%20"),b=g.indexOf(r);b>=0?(f=g.substr(0,b),p=g.substr(b+1)):(f=g,p=""),h=decodeURIComponent(f),d=decodeURIComponent(p),n(s,h)?i(s[h])?s[h].push(d):s[h]=[s[h],d]:s[h]=d}return s};var i=Array.isArray||function(e){return"[object Array]"===Object.prototype.toString.call(e)}},"9e1c":function(e,t,r){"use strict";r("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,r("64aa");var n={props:{hairline:{type:Boolean,default:uni.$u.props.button.hairline},type:{type:String,default:uni.$u.props.button.type},size:{type:String,default:uni.$u.props.button.size},shape:{type:String,default:uni.$u.props.button.shape},plain:{type:Boolean,default:uni.$u.props.button.plain},disabled:{type:Boolean,default:uni.$u.props.button.disabled},loading:{type:Boolean,default:uni.$u.props.button.loading},loadingText:{type:[String,Number],default:uni.$u.props.button.loadingText},loadingMode:{type:String,default:uni.$u.props.button.loadingMode},loadingSize:{type:[String,Number],default:uni.$u.props.button.loadingSize},openType:{type:String,default:uni.$u.props.button.openType},formType:{type:String,default:uni.$u.props.button.formType},appParameter:{type:String,default:uni.$u.props.button.appParameter},hoverStopPropagation:{type:Boolean,default:uni.$u.props.button.hoverStopPropagation},lang:{type:String,default:uni.$u.props.button.lang},sessionFrom:{type:String,default:uni.$u.props.button.sessionFrom},sendMessageTitle:{type:String,default:uni.$u.props.button.sendMessageTitle},sendMessagePath:{type:String,default:uni.$u.props.button.sendMessagePath},sendMessageImg:{type:String,default:uni.$u.props.button.sendMessageImg},showMessageCard:{type:Boolean,default:uni.$u.props.button.showMessageCard},dataName:{type:String,default:uni.$u.props.button.dataName},throttleTime:{type:[String,Number],default:uni.$u.props.button.throttleTime},hoverStartTime:{type:[String,Number],default:uni.$u.props.button.hoverStartTime},hoverStayTime:{type:[String,Number],default:uni.$u.props.button.hoverStayTime},text:{type:[String,Number],default:uni.$u.props.button.text},icon:{type:String,default:uni.$u.props.button.icon},iconColor:{type:String,default:uni.$u.props.button.icon},color:{type:String,default:uni.$u.props.button.color}}};t.default=n},"9e2e":function(e,t,r){"use strict";r("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={"uicon-level":"","uicon-column-line":"","uicon-checkbox-mark":"","uicon-folder":"","uicon-movie":"","uicon-star-fill":"","uicon-star":"","uicon-phone-fill":"","uicon-phone":"","uicon-apple-fill":"","uicon-chrome-circle-fill":"","uicon-backspace":"","uicon-attach":"","uicon-cut":"","uicon-empty-car":"","uicon-empty-coupon":"","uicon-empty-address":"","uicon-empty-favor":"","uicon-empty-permission":"","uicon-empty-news":"","uicon-empty-search":"","uicon-github-circle-fill":"","uicon-rmb":"","uicon-person-delete-fill":"","uicon-reload":"","uicon-order":"","uicon-server-man":"","uicon-search":"","uicon-fingerprint":"","uicon-more-dot-fill":"","uicon-scan":"","uicon-share-square":"","uicon-map":"","uicon-map-fill":"","uicon-tags":"","uicon-tags-fill":"","uicon-bookmark-fill":"","uicon-bookmark":"","uicon-eye":"","uicon-eye-fill":"","uicon-mic":"","uicon-mic-off":"","uicon-calendar":"","uicon-calendar-fill":"","uicon-trash":"","uicon-trash-fill":"","uicon-play-left":"","uicon-play-right":"","uicon-minus":"","uicon-plus":"","uicon-info":"","uicon-info-circle":"","uicon-info-circle-fill":"","uicon-question":"","uicon-error":"","uicon-close":"","uicon-checkmark":"","uicon-android-circle-fill":"","uicon-android-fill":"","uicon-ie":"","uicon-IE-circle-fill":"","uicon-google":"","uicon-google-circle-fill":"","uicon-setting-fill":"","uicon-setting":"","uicon-minus-square-fill":"","uicon-plus-square-fill":"","uicon-heart":"","uicon-heart-fill":"","uicon-camera":"","uicon-camera-fill":"","uicon-more-circle":"","uicon-more-circle-fill":"","uicon-chat":"","uicon-chat-fill":"","uicon-bag-fill":"","uicon-bag":"","uicon-error-circle-fill":"","uicon-error-circle":"","uicon-close-circle":"","uicon-close-circle-fill":"","uicon-checkmark-circle":"","uicon-checkmark-circle-fill":"","uicon-question-circle-fill":"","uicon-question-circle":"","uicon-share":"","uicon-share-fill":"","uicon-shopping-cart":"","uicon-shopping-cart-fill":"","uicon-bell":"","uicon-bell-fill":"","uicon-list":"","uicon-list-dot":"","uicon-zhihu":"","uicon-zhihu-circle-fill":"","uicon-zhifubao":"","uicon-zhifubao-circle-fill":"","uicon-weixin-circle-fill":"","uicon-weixin-fill":"","uicon-twitter-circle-fill":"","uicon-twitter":"","uicon-taobao-circle-fill":"","uicon-taobao":"","uicon-weibo-circle-fill":"","uicon-weibo":"","uicon-qq-fill":"","uicon-qq-circle-fill":"","uicon-moments-circel-fill":"","uicon-moments":"","uicon-qzone":"","uicon-qzone-circle-fill":"","uicon-baidu-circle-fill":"","uicon-baidu":"","uicon-facebook-circle-fill":"","uicon-facebook":"","uicon-car":"","uicon-car-fill":"","uicon-warning-fill":"","uicon-warning":"","uicon-clock-fill":"","uicon-clock":"","uicon-edit-pen":"","uicon-edit-pen-fill":"","uicon-email":"","uicon-email-fill":"","uicon-minus-circle":"","uicon-minus-circle-fill":"","uicon-plus-circle":"","uicon-plus-circle-fill":"","uicon-file-text":"","uicon-file-text-fill":"","uicon-pushpin":"","uicon-pushpin-fill":"","uicon-grid":"","uicon-grid-fill":"","uicon-play-circle":"","uicon-play-circle-fill":"","uicon-pause-circle-fill":"","uicon-pause":"","uicon-pause-circle":"","uicon-eye-off":"","uicon-eye-off-outline":"","uicon-gift-fill":"","uicon-gift":"","uicon-rmb-circle-fill":"","uicon-rmb-circle":"","uicon-kefu-ermai":"","uicon-server-fill":"","uicon-coupon-fill":"","uicon-coupon":"","uicon-integral":"","uicon-integral-fill":"","uicon-home-fill":"","uicon-home":"","uicon-hourglass-half-fill":"","uicon-hourglass":"","uicon-account":"","uicon-plus-people-fill":"","uicon-minus-people-fill":"","uicon-account-fill":"","uicon-thumb-down-fill":"","uicon-thumb-down":"","uicon-thumb-up":"","uicon-thumb-up-fill":"","uicon-lock-fill":"","uicon-lock-open":"","uicon-lock-opened-fill":"","uicon-lock":"","uicon-red-packet-fill":"","uicon-photo-fill":"","uicon-photo":"","uicon-volume-off-fill":"","uicon-volume-off":"","uicon-volume-fill":"","uicon-volume":"","uicon-red-packet":"","uicon-download":"","uicon-arrow-up-fill":"","uicon-arrow-down-fill":"","uicon-play-left-fill":"","uicon-play-right-fill":"","uicon-rewind-left-fill":"","uicon-rewind-right-fill":"","uicon-arrow-downward":"","uicon-arrow-leftward":"","uicon-arrow-rightward":"","uicon-arrow-upward":"","uicon-arrow-down":"","uicon-arrow-right":"","uicon-arrow-left":"","uicon-arrow-up":"","uicon-skip-back-left":"","uicon-skip-forward-right":"","uicon-rewind-right":"","uicon-rewind-left":"","uicon-arrow-right-double":"","uicon-arrow-left-double":"","uicon-wifi-off":"","uicon-wifi":"","uicon-empty-data":"","uicon-empty-history":"","uicon-empty-list":"","uicon-empty-page":"","uicon-empty-order":"","uicon-man":"","uicon-woman":"","uicon-man-add":"","uicon-man-add-fill":"","uicon-man-delete":"","uicon-man-delete-fill":"","uicon-zh":"","uicon-en":""}},a362:function(e,t,r){r("7a76"),r("c9b5");var n=r("bdbb")["default"],i=r("3a7e");e.exports=function(e,t){if(t&&("object"===n(t)||"function"===typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return i(e)},e.exports.__esModule=!0,e.exports["default"]=e.exports},a3fc:function(e,t,r){(function(e){function r(e,t){for(var r=0,n=e.length-1;n>=0;n--){var i=e[n];"."===i?e.splice(n,1):".."===i?(e.splice(n,1),r++):r&&(e.splice(n,1),r--)}if(t)for(;r--;r)e.unshift("..");return e}function n(e,t){if(e.filter)return e.filter(t);for(var r=[],n=0;n<e.length;n++)t(e[n],n,e)&&r.push(e[n]);return r}t.resolve=function(){for(var t="",i=!1,o=arguments.length-1;o>=-1&&!i;o--){var s=o>=0?arguments[o]:e.cwd();if("string"!==typeof s)throw new TypeError("Arguments to path.resolve must be strings");s&&(t=s+"/"+t,i="/"===s.charAt(0))}return t=r(n(t.split("/"),(function(e){return!!e})),!i).join("/"),(i?"/":"")+t||"."},t.normalize=function(e){var o=t.isAbsolute(e),s="/"===i(e,-1);return e=r(n(e.split("/"),(function(e){return!!e})),!o).join("/"),e||o||(e="."),e&&s&&(e+="/"),(o?"/":"")+e},t.isAbsolute=function(e){return"/"===e.charAt(0)},t.join=function(){var e=Array.prototype.slice.call(arguments,0);return t.normalize(n(e,(function(e,t){if("string"!==typeof e)throw new TypeError("Arguments to path.join must be strings");return e})).join("/"))},t.relative=function(e,r){function n(e){for(var t=0;t<e.length;t++)if(""!==e[t])break;for(var r=e.length-1;r>=0;r--)if(""!==e[r])break;return t>r?[]:e.slice(t,r-t+1)}e=t.resolve(e).substr(1),r=t.resolve(r).substr(1);for(var i=n(e.split("/")),o=n(r.split("/")),s=Math.min(i.length,o.length),a=s,c=0;c<s;c++)if(i[c]!==o[c]){a=c;break}var u=[];for(c=a;c<i.length;c++)u.push("..");return u=u.concat(o.slice(a)),u.join("/")},t.sep="/",t.delimiter=":",t.dirname=function(e){if("string"!==typeof e&&(e+=""),0===e.length)return".";for(var t=e.charCodeAt(0),r=47===t,n=-1,i=!0,o=e.length-1;o>=1;--o)if(t=e.charCodeAt(o),47===t){if(!i){n=o;break}}else i=!1;return-1===n?r?"/":".":r&&1===n?"/":e.slice(0,n)},t.basename=function(e,t){var r=function(e){"string"!==typeof e&&(e+="");var t,r=0,n=-1,i=!0;for(t=e.length-1;t>=0;--t)if(47===e.charCodeAt(t)){if(!i){r=t+1;break}}else-1===n&&(i=!1,n=t+1);return-1===n?"":e.slice(r,n)}(e);return t&&r.substr(-1*t.length)===t&&(r=r.substr(0,r.length-t.length)),r},t.extname=function(e){"string"!==typeof e&&(e+="");for(var t=-1,r=0,n=-1,i=!0,o=0,s=e.length-1;s>=0;--s){var a=e.charCodeAt(s);if(47!==a)-1===n&&(i=!1,n=s+1),46===a?-1===t?t=s:1!==o&&(o=1):-1!==t&&(o=-1);else if(!i){r=s+1;break}}return-1===t||-1===n||0===o||1===o&&t===n-1&&t===r+1?"":e.slice(t,n)};var i="b"==="ab".substr(-1)?function(e,t,r){return e.substr(t,r)}:function(e,t,r){return t<0&&(t=e.length+t),e.substr(t,r)}}).call(this,r("28d0"))},a658:function(e,t,r){"use strict";r("7a76"),r("c9b5");var n=r(4),i=r("172d")("mqttjs:tls");e.exports=function(e,t){var r;function o(n){t.rejectUnauthorized&&e.emit("error",n),r.end()}return t.port=t.port||8883,t.host=t.hostname||t.host||"localhost",t.servername=t.host,t.rejectUnauthorized=!1!==t.rejectUnauthorized,delete t.path,i("port %d host %s rejectUnauthorized %b",t.port,t.host,t.rejectUnauthorized),r=n.connect(t),r.on("secureConnect",(function(){t.rejectUnauthorized&&!r.authorized?r.emit("error",new Error("TLS not authorized")):r.removeListener("error",o)})),r.on("error",o),r}},a8e2:function(e,t,r){"use strict";e.exports={isString:function(e){return"string"===typeof e},isObject:function(e){return"object"===typeof e&&null!==e},isNull:function(e){return null===e},isNullOrUndefined:function(e){return null==e}}},a932:function(e,t,r){"use strict";r("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n={props:{lang:String,sessionFrom:String,sendMessageTitle:String,sendMessagePath:String,sendMessageImg:String,showMessageCard:Boolean,appParameter:String,formType:String,openType:String}};t.default=n},aa04:function(e,t,r){"use strict";var n=r("7c7d"),i=r.n(n);i.a},abeb:function(e,t){e.exports=function(){for(var e={},t=0;t<arguments.length;t++){var n=arguments[t];for(var i in n)r.call(n,i)&&(e[i]=n[i])}return e};var r=Object.prototype.hasOwnProperty},bf30:function(e,t,r){"use strict";var n,i="object"===typeof Reflect?Reflect:null,o=i&&"function"===typeof i.apply?i.apply:function(e,t,r){return Function.prototype.apply.call(e,t,r)};n=i&&"function"===typeof i.ownKeys?i.ownKeys:Object.getOwnPropertySymbols?function(e){return Object.getOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e))}:function(e){return Object.getOwnPropertyNames(e)};var s=Number.isNaN||function(e){return e!==e};function a(){a.init.call(this)}e.exports=a,e.exports.once=function(e,t){return new Promise((function(r,n){function i(r){e.removeListener(t,o),n(r)}function o(){"function"===typeof e.removeListener&&e.removeListener("error",i),r([].slice.call(arguments))}m(e,t,o,{once:!0}),"error"!==t&&function(e,t,r){"function"===typeof e.on&&m(e,"error",t,r)}(e,i,{once:!0})}))},a.EventEmitter=a,a.prototype._events=void 0,a.prototype._eventsCount=0,a.prototype._maxListeners=void 0;var c=10;function u(e){if("function"!==typeof e)throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof e)}function l(e){return void 0===e._maxListeners?a.defaultMaxListeners:e._maxListeners}function f(e,t,r,n){var i,o,s;if(u(r),o=e._events,void 0===o?(o=e._events=Object.create(null),e._eventsCount=0):(void 0!==o.newListener&&(e.emit("newListener",t,r.listener?r.listener:r),o=e._events),s=o[t]),void 0===s)s=o[t]=r,++e._eventsCount;else if("function"===typeof s?s=o[t]=n?[r,s]:[s,r]:n?s.unshift(r):s.push(r),i=l(e),i>0&&s.length>i&&!s.warned){s.warned=!0;var a=new Error("Possible EventEmitter memory leak detected. "+s.length+" "+String(t)+" listeners added. Use emitter.setMaxListeners() to increase limit");a.name="MaxListenersExceededWarning",a.emitter=e,a.type=t,a.count=s.length,function(e){console&&console.warn&&console.warn(e)}(a)}return e}function p(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,0===arguments.length?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function h(e,t,r){var n={fired:!1,wrapFn:void 0,target:e,type:t,listener:r},i=p.bind(n);return i.listener=r,n.wrapFn=i,i}function d(e,t,r){var n=e._events;if(void 0===n)return[];var i=n[t];return void 0===i?[]:"function"===typeof i?r?[i.listener||i]:[i]:r?function(e){for(var t=new Array(e.length),r=0;r<t.length;++r)t[r]=e[r].listener||e[r];return t}(i):b(i,i.length)}function g(e){var t=this._events;if(void 0!==t){var r=t[e];if("function"===typeof r)return 1;if(void 0!==r)return r.length}return 0}function b(e,t){for(var r=new Array(t),n=0;n<t;++n)r[n]=e[n];return r}function m(e,t,r,n){if("function"===typeof e.on)n.once?e.once(t,r):e.on(t,r);else{if("function"!==typeof e.addEventListener)throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof e);e.addEventListener(t,(function i(o){n.once&&e.removeEventListener(t,i),r(o)}))}}Object.defineProperty(a,"defaultMaxListeners",{enumerable:!0,get:function(){return c},set:function(e){if("number"!==typeof e||e<0||s(e))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+e+".");c=e}}),a.init=function(){void 0!==this._events&&this._events!==Object.getPrototypeOf(this)._events||(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},a.prototype.setMaxListeners=function(e){if("number"!==typeof e||e<0||s(e))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+e+".");return this._maxListeners=e,this},a.prototype.getMaxListeners=function(){return l(this)},a.prototype.emit=function(e){for(var t=[],r=1;r<arguments.length;r++)t.push(arguments[r]);var n="error"===e,i=this._events;if(void 0!==i)n=n&&void 0===i.error;else if(!n)return!1;if(n){var s;if(t.length>0&&(s=t[0]),s instanceof Error)throw s;var a=new Error("Unhandled error."+(s?" ("+s.message+")":""));throw a.context=s,a}var c=i[e];if(void 0===c)return!1;if("function"===typeof c)o(c,this,t);else{var u=c.length,l=b(c,u);for(r=0;r<u;++r)o(l[r],this,t)}return!0},a.prototype.addListener=function(e,t){return f(this,e,t,!1)},a.prototype.on=a.prototype.addListener,a.prototype.prependListener=function(e,t){return f(this,e,t,!0)},a.prototype.once=function(e,t){return u(t),this.on(e,h(this,e,t)),this},a.prototype.prependOnceListener=function(e,t){return u(t),this.prependListener(e,h(this,e,t)),this},a.prototype.removeListener=function(e,t){var r,n,i,o,s;if(u(t),n=this._events,void 0===n)return this;if(r=n[e],void 0===r)return this;if(r===t||r.listener===t)0===--this._eventsCount?this._events=Object.create(null):(delete n[e],n.removeListener&&this.emit("removeListener",e,r.listener||t));else if("function"!==typeof r){for(i=-1,o=r.length-1;o>=0;o--)if(r[o]===t||r[o].listener===t){s=r[o].listener,i=o;break}if(i<0)return this;0===i?r.shift():function(e,t){for(;t+1<e.length;t++)e[t]=e[t+1];e.pop()}(r,i),1===r.length&&(n[e]=r[0]),void 0!==n.removeListener&&this.emit("removeListener",e,s||t)}return this},a.prototype.off=a.prototype.removeListener,a.prototype.removeAllListeners=function(e){var t,r,n;if(r=this._events,void 0===r)return this;if(void 0===r.removeListener)return 0===arguments.length?(this._events=Object.create(null),this._eventsCount=0):void 0!==r[e]&&(0===--this._eventsCount?this._events=Object.create(null):delete r[e]),this;if(0===arguments.length){var i,o=Object.keys(r);for(n=0;n<o.length;++n)i=o[n],"removeListener"!==i&&this.removeAllListeners(i);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if(t=r[e],"function"===typeof t)this.removeListener(e,t);else if(void 0!==t)for(n=t.length-1;n>=0;n--)this.removeListener(e,t[n]);return this},a.prototype.listeners=function(e){return d(this,e,!0)},a.prototype.rawListeners=function(e){return d(this,e,!1)},a.listenerCount=function(e,t){return"function"===typeof e.listenerCount?e.listenerCount(t):g.call(e,t)},a.prototype.listenerCount=g,a.prototype.eventNames=function(){return this._eventsCount>0?n(this._events):[]}},c425:function(e,t,r){(function(e,n){var i;/*! https://mths.be/punycode v1.4.1 by @mathias */(function(o){t&&t.nodeType,e&&e.nodeType;var s="object"==typeof n&&n;s.global!==s&&s.window!==s&&s.self;var a,c=2147483647,u=/^xn--/,l=/[^\x20-\x7E]/,f=/[\x2E\u3002\uFF0E\uFF61]/g,p={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},h=Math.floor,d=String.fromCharCode;function g(e){throw new RangeError(p[e])}function b(e,t){var r=e.length,n=[];while(r--)n[r]=t(e[r]);return n}function m(e,t){var r=e.split("@"),n="";r.length>1&&(n=r[0]+"@",e=r[1]),e=e.replace(f,".");var i=e.split("."),o=b(i,t).join(".");return n+o}function v(e){var t,r,n=[],i=0,o=e.length;while(i<o)t=e.charCodeAt(i++),t>=55296&&t<=56319&&i<o?(r=e.charCodeAt(i++),56320==(64512&r)?n.push(((1023&t)<<10)+(1023&r)+65536):(n.push(t),i--)):n.push(t);return n}function y(e){return b(e,(function(e){var t="";return e>65535&&(e-=65536,t+=d(e>>>10&1023|55296),e=56320|1023&e),t+=d(e),t})).join("")}function _(e){return e-48<10?e-22:e-65<26?e-65:e-97<26?e-97:36}function w(e,t){return e+22+75*(e<26)-((0!=t)<<5)}function S(e,t,r){var n=0;for(e=r?h(e/700):e>>1,e+=h(e/t);e>455;n+=36)e=h(e/35);return h(n+36*e/(e+38))}function k(e){var t,r,n,i,o,s,a,u,l,f,p=[],d=e.length,b=0,m=128,v=72;for(r=e.lastIndexOf("-"),r<0&&(r=0),n=0;n<r;++n)e.charCodeAt(n)>=128&&g("not-basic"),p.push(e.charCodeAt(n));for(i=r>0?r+1:0;i<d;){for(o=b,s=1,a=36;;a+=36){if(i>=d&&g("invalid-input"),u=_(e.charCodeAt(i++)),(u>=36||u>h((c-b)/s))&&g("overflow"),b+=u*s,l=a<=v?1:a>=v+26?26:a-v,u<l)break;f=36-l,s>h(c/f)&&g("overflow"),s*=f}t=p.length+1,v=S(b-o,t,0==o),h(b/t)>c-m&&g("overflow"),m+=h(b/t),b%=t,p.splice(b++,0,m)}return y(p)}function x(e){var t,r,n,i,o,s,a,u,l,f,p,b,m,y,_,k=[];for(e=v(e),b=e.length,t=128,r=0,o=72,s=0;s<b;++s)p=e[s],p<128&&k.push(d(p));n=i=k.length,i&&k.push("-");while(n<b){for(a=c,s=0;s<b;++s)p=e[s],p>=t&&p<a&&(a=p);for(m=n+1,a-t>h((c-r)/m)&&g("overflow"),r+=(a-t)*m,t=a,s=0;s<b;++s)if(p=e[s],p<t&&++r>c&&g("overflow"),p==t){for(u=r,l=36;;l+=36){if(f=l<=o?1:l>=o+26?26:l-o,u<f)break;_=u-f,y=36-f,k.push(d(w(f+_%y,0))),u=h(_/y)}k.push(d(w(u,0))),o=S(r,m,n==i),r=0,++n}++r,++t}return k.join("")}a={version:"1.4.1",ucs2:{decode:v,encode:y},decode:k,encode:x,toASCII:function(e){return m(e,(function(e){return l.test(e)?"xn--"+x(e):e}))},toUnicode:function(e){return m(e,(function(e){return u.test(e)?k(e.slice(4).toLowerCase()):e}))}},i=function(){return a}.call(t,r,t,e),void 0===i||(e.exports=i)})()}).call(this,r("dc84")(e),r("0ee4"))},c5a6:function(e,t,r){"use strict";var n=function(e){switch(typeof e){case"string":return e;case"boolean":return e?"true":"false";case"number":return isFinite(e)?e:"";default:return""}};e.exports=function(e,t,r,a){return t=t||"&",r=r||"=",null===e&&(e=void 0),"object"===typeof e?o(s(e),(function(s){var a=encodeURIComponent(n(s))+r;return i(e[s])?o(e[s],(function(e){return a+encodeURIComponent(n(e))})).join(t):a+encodeURIComponent(n(e[s]))})).join(t):a?encodeURIComponent(n(a))+r+encodeURIComponent(n(e)):""};var i=Array.isArray||function(e){return"[object Array]"===Object.prototype.toString.call(e)};function o(e,t){if(e.map)return e.map(t);for(var r=[],n=0;n<e.length;n++)r.push(t(e[n],n));return r}var s=Object.keys||function(e){var t=[];for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.push(r);return t}},c741:function(e,t,r){"use strict";var n=r("ca99f");function i(e,t){e.emit("error",t)}e.exports={destroy:function(e,t){var r=this,o=this._readableState&&this._readableState.destroyed,s=this._writableState&&this._writableState.destroyed;return o||s?(t?t(e):!e||this._writableState&&this._writableState.errorEmitted||n.nextTick(i,this,e),this):(this._readableState&&(this._readableState.destroyed=!0),this._writableState&&(this._writableState.destroyed=!0),this._destroy(e||null,(function(e){!t&&e?(n.nextTick(i,r,e),r._writableState&&(r._writableState.errorEmitted=!0)):t&&t(e)})),this)},undestroy:function(){this._readableState&&(this._readableState.destroyed=!1,this._readableState.reading=!1,this._readableState.ended=!1,this._readableState.endEmitted=!1),this._writableState&&(this._writableState.destroyed=!1,this._writableState.ended=!1,this._writableState.ending=!1,this._writableState.finished=!1,this._writableState.errorEmitted=!1)}}},c7f5:function(e,t,r){(function(t){var n=r("4cf1").default,i=r("bdbb").default;r("7a76"),r("c9b5"),r("6a54"),r("dc8a"),r("c223"),r("bf0f"),r("ab80"),r("473f"),r("7f48"),r("2797");var o=r("5a67"),s=t.allocUnsafe(0),a=t.from([0]),c=r("f807"),u=r("ca99f").nextTick,l=r("172d")("mqtt-packet:writeToStream"),f=c.cache,p=c.generateNumber,h=c.generateCache,d=c.genBufVariableByteInt,g=c.generate4ByteBuffer,b=x,m=!0;function v(e,r,c){switch(l("generate called"),r.cork&&(r.cork(),u(y,r)),m&&(m=!1,h()),l("generate: packet.cmd: %s",e.cmd),e.cmd){case"connect":return function(e,r,n){var s=e||{},a=s.protocolId||"MQTT",c=s.protocolVersion||4,u=s.will,l=s.clean,f=s.keepalive||0,p=s.clientId||"",h=s.username,d=s.password,g=s.properties;void 0===l&&(l=!0);var m=0;if(!a||"string"!==typeof a&&!t.isBuffer(a))return r.emit("error",new Error("Invalid protocolId")),!1;m+=a.length+2;if(3!==c&&4!==c&&5!==c)return r.emit("error",new Error("Invalid protocol version")),!1;m+=1;if(("string"===typeof p||t.isBuffer(p))&&(p||c>=4)&&(p||l))m+=t.byteLength(p)+2;else{if(c<4)return r.emit("error",new Error("clientId must be supplied before 3.1.1")),!1;if(1*l===0)return r.emit("error",new Error("clientId must be given if cleanSession set to 0")),!1}if("number"!==typeof f||f<0||f>65535||f%1!==0)return r.emit("error",new Error("Invalid keepalive")),!1;m+=2;if(m+=1,5===c){var v=I(r,g);if(!v)return!1;m+=v.length}if(u){if("object"!==i(u))return r.emit("error",new Error("Invalid will")),!1;if(!u.topic||"string"!==typeof u.topic)return r.emit("error",new Error("Invalid will topic")),!1;if(m+=t.byteLength(u.topic)+2,m+=2,u.payload){if(!(u.payload.length>=0))return r.emit("error",new Error("Invalid will payload")),!1;"string"===typeof u.payload?m+=t.byteLength(u.payload):m+=u.payload.length}var y={};if(5===c){if(y=I(r,u.properties),!y)return!1;m+=y.length}}var _=!1;if(null!=h){if(!A(h))return r.emit("error",new Error("Invalid username")),!1;_=!0,m+=t.byteLength(h)+2}if(null!=d){if(!_)return r.emit("error",new Error("Username is required to use password")),!1;if(!A(d))return r.emit("error",new Error("Invalid password")),!1;m+=T(d)+2}r.write(o.CONNECT_HEADER),w(r,m),E(r,a),s.bridgeMode&&(c+=128);r.write(131===c?o.VERSION131:132===c?o.VERSION132:4===c?o.VERSION4:5===c?o.VERSION5:o.VERSION3);var k=0;k|=null!=h?o.USERNAME_MASK:0,k|=null!=d?o.PASSWORD_MASK:0,k|=u&&u.retain?o.WILL_RETAIN_MASK:0,k|=u&&u.qos?u.qos<<o.WILL_QOS_SHIFT:0,k|=u?o.WILL_FLAG_MASK:0,k|=l?o.CLEAN_SESSION_MASK:0,r.write(t.from([k])),b(r,f),5===c&&v.write();E(r,p),u&&(5===c&&y.write(),S(r,u.topic),E(r,u.payload));null!=h&&E(r,h);null!=d&&E(r,d);return!0}(e,r);case"connack":return function(e,r,n){var i=n?n.protocolVersion:4,s=e||{},c=5===i?s.reasonCode:s.returnCode,u=s.properties,l=2;if("number"!==typeof c)return r.emit("error",new Error("Invalid return code")),!1;var f=null;if(5===i){if(f=I(r,u),!f)return!1;l+=f.length}r.write(o.CONNACK_HEADER),w(r,l),r.write(s.sessionPresent?o.SESSIONPRESENT_HEADER:a),r.write(t.from([c])),null!=f&&f.write();return!0}(e,r,c);case"publish":return function(e,r,n){l("publish: packet: %o",e);var i=n?n.protocolVersion:4,a=e||{},c=a.qos||0,u=a.retain?o.RETAIN_MASK:0,f=a.topic,p=a.payload||s,h=a.messageId,d=a.properties,g=0;if("string"===typeof f)g+=t.byteLength(f)+2;else{if(!t.isBuffer(f))return r.emit("error",new Error("Invalid topic")),!1;g+=f.length+2}t.isBuffer(p)?g+=p.length:g+=t.byteLength(p);if(c&&"number"!==typeof h)return r.emit("error",new Error("Invalid messageId")),!1;c&&(g+=2);var m=null;if(5===i){if(m=I(r,d),!m)return!1;g+=m.length}r.write(o.PUBLISH_HEADER[c][a.dup?1:0][u?1:0]),w(r,g),b(r,T(f)),r.write(f),c>0&&b(r,h);null!=m&&m.write();return l("publish: payload: %o",p),r.write(p)}(e,r,c);case"puback":case"pubrec":case"pubrel":case"pubcomp":return function(e,r,n){var s=n?n.protocolVersion:4,a=e||{},c=a.cmd||"puback",u=a.messageId,l=a.dup&&"pubrel"===c?o.DUP_MASK:0,f=0,p=a.reasonCode,h=a.properties,d=5===s?3:2;"pubrel"===c&&(f=1);if("number"!==typeof u)return r.emit("error",new Error("Invalid messageId")),!1;var g=null;if(5===s&&"object"===i(h)){if(g=O(r,h,n,d),!g)return!1;d+=g.length}r.write(o.ACKS[c][f][l][0]),w(r,d),b(r,u),5===s&&r.write(t.from([p]));null!==g&&g.write();return!0}(e,r,c);case"subscribe":return function(e,r,s){l("subscribe: packet: ");var a=s?s.protocolVersion:4,c=e||{},u=c.dup?o.DUP_MASK:0,f=c.messageId,p=c.subscriptions,h=c.properties,d=0;if("number"!==typeof f)return r.emit("error",new Error("Invalid messageId")),!1;d+=2;var g=null;if(5===a){if(g=I(r,h),!g)return!1;d+=g.length}if("object"!==i(p)||!p.length)return r.emit("error",new Error("Invalid subscriptions")),!1;for(var m=0;m<p.length;m+=1){var v=p[m].topic,y=p[m].qos;if("string"!==typeof v)return r.emit("error",new Error("Invalid subscriptions - invalid topic")),!1;if("number"!==typeof y)return r.emit("error",new Error("Invalid subscriptions - invalid qos")),!1;if(5===a){var _=p[m].nl||!1;if("boolean"!==typeof _)return r.emit("error",new Error("Invalid subscriptions - invalid No Local")),!1;var k=p[m].rap||!1;if("boolean"!==typeof k)return r.emit("error",new Error("Invalid subscriptions - invalid Retain as Published")),!1;var x=p[m].rh||0;if("number"!==typeof x||x>2)return r.emit("error",new Error("Invalid subscriptions - invalid Retain Handling")),!1}d+=t.byteLength(v)+2+1}l("subscribe: writing to stream: %o",o.SUBSCRIBE_HEADER),r.write(o.SUBSCRIBE_HEADER[1][u?1:0][0]),w(r,d),b(r,f),null!==g&&g.write();var C,E=!0,O=n(p);try{for(O.s();!(C=O.n()).done;){var M=C.value,P=M.topic,T=M.qos,A=+M.nl,j=+M.rap,N=M.rh,R=void 0;S(r,P),R=o.SUBSCRIBE_OPTIONS_QOS[T],5===a&&(R|=A?o.SUBSCRIBE_OPTIONS_NL:0,R|=j?o.SUBSCRIBE_OPTIONS_RAP:0,R|=N?o.SUBSCRIBE_OPTIONS_RH[N]:0),E=r.write(t.from([R]))}}catch(B){O.e(B)}finally{O.f()}return E}(e,r,c);case"suback":return function(e,r,n){var s=n?n.protocolVersion:4,a=e||{},c=a.messageId,u=a.granted,l=a.properties,f=0;if("number"!==typeof c)return r.emit("error",new Error("Invalid messageId")),!1;f+=2;if("object"!==i(u)||!u.length)return r.emit("error",new Error("Invalid qos vector")),!1;for(var p=0;p<u.length;p+=1){if("number"!==typeof u[p])return r.emit("error",new Error("Invalid qos vector")),!1;f+=1}var h=null;if(5===s){if(h=O(r,l,n,f),!h)return!1;f+=h.length}r.write(o.SUBACK_HEADER),w(r,f),b(r,c),null!==h&&h.write();return r.write(t.from(u))}(e,r,c);case"unsubscribe":return function(e,r,n){var s=n?n.protocolVersion:4,a=e||{},c=a.messageId,u=a.dup?o.DUP_MASK:0,l=a.unsubscriptions,f=a.properties,p=0;if("number"!==typeof c)return r.emit("error",new Error("Invalid messageId")),!1;p+=2;if("object"!==i(l)||!l.length)return r.emit("error",new Error("Invalid unsubscriptions")),!1;for(var h=0;h<l.length;h+=1){if("string"!==typeof l[h])return r.emit("error",new Error("Invalid unsubscriptions")),!1;p+=t.byteLength(l[h])+2}var d=null;if(5===s){if(d=I(r,f),!d)return!1;p+=d.length}r.write(o.UNSUBSCRIBE_HEADER[1][u?1:0][0]),w(r,p),b(r,c),null!==d&&d.write();for(var g=!0,m=0;m<l.length;m++)g=S(r,l[m]);return g}(e,r,c);case"unsuback":return function(e,r,n){var s=n?n.protocolVersion:4,a=e||{},c=a.messageId,u=a.dup?o.DUP_MASK:0,l=a.granted,f=a.properties,p=a.cmd,h=2;if("number"!==typeof c)return r.emit("error",new Error("Invalid messageId")),!1;if(5===s){if("object"!==i(l)||!l.length)return r.emit("error",new Error("Invalid qos vector")),!1;for(var d=0;d<l.length;d+=1){if("number"!==typeof l[d])return r.emit("error",new Error("Invalid qos vector")),!1;h+=1}}var g=null;if(5===s){if(g=O(r,f,n,h),!g)return!1;h+=g.length}r.write(o.ACKS[p][0][u][0]),w(r,h),b(r,c),null!==g&&g.write();5===s&&r.write(t.from(l));return!0}(e,r,c);case"pingreq":case"pingresp":return function(e,t,r){return t.write(o.EMPTY[e.cmd])}(e,r);case"disconnect":return function(e,r,n){var i=n?n.protocolVersion:4,s=e||{},a=s.reasonCode,c=s.properties,u=5===i?1:0,l=null;if(5===i){if(l=O(r,c,n,u),!l)return!1;u+=l.length}r.write(t.from([o.codes.disconnect<<4])),w(r,u),5===i&&r.write(t.from([a]));null!==l&&l.write();return!0}(e,r,c);case"auth":return function(e,r,n){var i=n?n.protocolVersion:4,s=e||{},a=s.reasonCode,c=s.properties,u=5===i?1:0;5!==i&&r.emit("error",new Error("Invalid mqtt version for auth packet"));var l=O(r,c,n,u);if(!l)return!1;u+=l.length,r.write(t.from([o.codes.auth<<4])),w(r,u),r.write(t.from([a])),null!==l&&l.write();return!0}(e,r,c);default:return r.emit("error",new Error("Unknown command")),!1}}function y(e){e.uncork()}Object.defineProperty(v,"cacheNumbers",{get:function(){return b===x},set:function(e){e?(f&&0!==Object.keys(f).length||(m=!0),b=x):(m=!1,b=C)}});var _={};function w(e,t){if(t>o.VARBYTEINT_MAX)return e.emit("error",new Error("Invalid variable byte integer: ".concat(t))),!1;var r=_[t];return r||(r=d(t),t<16384&&(_[t]=r)),l("writeVarByteInt: writing to stream: %o",r),e.write(r)}function S(e,r){var n=t.byteLength(r);return b(e,n),l("writeString: %s",r),e.write(r,"utf8")}function k(e,t,r){S(e,t),S(e,r)}function x(e,t){return l("writeNumberCached: number: %d",t),l("writeNumberCached: %o",f[t]),e.write(f[t])}function C(e,t){var r=p(t);return l("writeNumberGenerated: %o",r),e.write(r)}function E(e,t){"string"===typeof t?S(e,t):t?(b(e,t.length),e.write(t)):b(e,0)}function I(e,r){if("object"!==i(r)||null!=r.length)return{length:1,write:function(){P(e,{},0)}};var n=0;function s(r,n){var s=o.propertiesTypes[r],a=0;switch(s){case"byte":if("boolean"!==typeof n)return e.emit("error",new Error("Invalid ".concat(r,": ").concat(n))),!1;a+=2;break;case"int8":if("number"!==typeof n||n<0||n>255)return e.emit("error",new Error("Invalid ".concat(r,": ").concat(n))),!1;a+=2;break;case"binary":if(n&&null===n)return e.emit("error",new Error("Invalid ".concat(r,": ").concat(n))),!1;a+=1+t.byteLength(n)+2;break;case"int16":if("number"!==typeof n||n<0||n>65535)return e.emit("error",new Error("Invalid ".concat(r,": ").concat(n))),!1;a+=3;break;case"int32":if("number"!==typeof n||n<0||n>4294967295)return e.emit("error",new Error("Invalid ".concat(r,": ").concat(n))),!1;a+=5;break;case"var":if("number"!==typeof n||n<0||n>268435455)return e.emit("error",new Error("Invalid ".concat(r,": ").concat(n))),!1;a+=1+t.byteLength(d(n));break;case"string":if("string"!==typeof n)return e.emit("error",new Error("Invalid ".concat(r,": ").concat(n))),!1;a+=3+t.byteLength(n.toString());break;case"pair":if("object"!==i(n))return e.emit("error",new Error("Invalid ".concat(r,": ").concat(n))),!1;a+=Object.getOwnPropertyNames(n).reduce((function(e,r){var i=n[r];return Array.isArray(i)?e+=i.reduce((function(e,n){return e+=3+t.byteLength(r.toString())+2+t.byteLength(n.toString()),e}),0):e+=3+t.byteLength(r.toString())+2+t.byteLength(n[r].toString()),e}),0);break;default:return e.emit("error",new Error("Invalid property ".concat(r,": ").concat(n))),!1}return a}if(r)for(var a in r){var c=0,u=0,l=r[a];if(Array.isArray(l))for(var f=0;f<l.length;f++){if(u=s(a,l[f]),!u)return!1;c+=u}else{if(u=s(a,l),!u)return!1;c=u}if(!c)return!1;n+=c}var p=t.byteLength(d(n));return{length:p+n,write:function(){P(e,r,n)}}}function O(e,t,r,n){var i=["reasonString","userProperties"],o=r&&r.properties&&r.properties.maximumPacketSize?r.properties.maximumPacketSize:0,s=I(e,t);if(o)while(n+s.length>o){var a=i.shift();if(!a||!t[a])return!1;delete t[a],s=I(e,t)}return s}function M(e,r,n){var i=o.propertiesTypes[r];switch(i){case"byte":e.write(t.from([o.properties[r]])),e.write(t.from([+n]));break;case"int8":e.write(t.from([o.properties[r]])),e.write(t.from([n]));break;case"binary":e.write(t.from([o.properties[r]])),E(e,n);break;case"int16":e.write(t.from([o.properties[r]])),b(e,n);break;case"int32":e.write(t.from([o.properties[r]])),function(e,t){var r=g(t);l("write4ByteNumber: %o",r),e.write(r)}(e,n);break;case"var":e.write(t.from([o.properties[r]])),w(e,n);break;case"string":e.write(t.from([o.properties[r]])),S(e,n);break;case"pair":Object.getOwnPropertyNames(n).forEach((function(i){var s=n[i];Array.isArray(s)?s.forEach((function(n){e.write(t.from([o.properties[r]])),k(e,i.toString(),n.toString())})):(e.write(t.from([o.properties[r]])),k(e,i.toString(),s.toString()))}));break;default:return e.emit("error",new Error("Invalid property ".concat(r," value: ").concat(n))),!1}}function P(e,t,r){for(var n in w(e,r),t)if(Object.prototype.hasOwnProperty.call(t,n)&&null!==t[n]){var i=t[n];if(Array.isArray(i))for(var o=0;o<i.length;o++)M(e,n,i[o]);else M(e,n,i)}}function T(e){return e?e instanceof t?e.length:t.byteLength(e):0}function A(e){return"string"===typeof e||e instanceof t}e.exports=v}).call(this,r("12e3").Buffer)},ca99f:function(e,t,r){"use strict";(function(t){"undefined"===typeof t||!t.version||0===t.version.indexOf("v0.")||0===t.version.indexOf("v1.")&&0!==t.version.indexOf("v1.8.")?e.exports={nextTick:function(e,r,n,i){if("function"!==typeof e)throw new TypeError('"callback" argument must be a function');var o,s,a=arguments.length;switch(a){case 0:case 1:return t.nextTick(e);case 2:return t.nextTick((function(){e.call(null,r)}));case 3:return t.nextTick((function(){e.call(null,r,n)}));case 4:return t.nextTick((function(){e.call(null,r,n,i)}));default:o=new Array(a-1),s=0;while(s<o.length)o[s++]=arguments[s];return t.nextTick((function(){e.apply(null,o)}))}}}:e.exports=t}).call(this,r("28d0"))},caba:function(e,t,r){"use strict";var n=r("85c1"),i=r("9f9e"),o=r("474f"),s=r("030a"),a=r("29d8"),c=r("37ad"),u=r("7f28"),l=n.Function,f=/MSIE .\./.test(a)||s&&function(){var e=n.Bun.version.split(".");return e.length<3||"0"===e[0]&&(e[1]<3||"3"===e[1]&&"0"===e[2])}();e.exports=function(e,t){var r=t?2:1;return f?function(n,s){var a=u(arguments.length,1)>r,f=o(n)?n:l(n),p=a?c(arguments,r):[],h=a?function(){i(f,this,p)}:f;return t?e(h,s):e(h)}:e}},cd70:function(e,t,r){(function(t){var n=r("eecb"),i=function(){},o=function(e,r,s){if("function"===typeof r)return o(e,null,r);r||(r={}),s=n(s||i);var a=e._writableState,c=e._readableState,u=r.readable||!1!==r.readable&&e.readable,l=r.writable||!1!==r.writable&&e.writable,f=!1,p=function(){e.writable||h()},h=function(){l=!1,u||s.call(e)},d=function(){u=!1,l||s.call(e)},g=function(t){s.call(e,t?new Error("exited with error code: "+t):null)},b=function(t){s.call(e,t)},m=function(){t.nextTick(v)},v=function(){if(!f)return(!u||c&&c.ended&&!c.destroyed)&&(!l||a&&a.ended&&!a.destroyed)?void 0:s.call(e,new Error("premature close"))},y=function(){e.req.on("finish",h)};return!function(e){return e.setHeader&&"function"===typeof e.abort}(e)?l&&!a&&(e.on("end",p),e.on("close",p)):(e.on("complete",h),e.on("abort",m),e.req?y():e.on("request",y)),function(e){return e.stdio&&Array.isArray(e.stdio)&&3===e.stdio.length}(e)&&e.on("exit",g),e.on("end",d),e.on("finish",h),!1!==r.error&&e.on("error",b),e.on("close",m),function(){f=!0,e.removeListener("complete",h),e.removeListener("abort",m),e.removeListener("request",y),e.req&&e.req.removeListener("finish",h),e.removeListener("end",p),e.removeListener("close",p),e.removeListener("finish",h),e.removeListener("exit",g),e.removeListener("end",d),e.removeListener("error",b),e.removeListener("close",m)}};e.exports=o}).call(this,r("28d0"))},cdb7:function(e,t,r){r("bf0f"),r("7996"),r("6a88"),e.exports=function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}},e.exports.__esModule=!0,e.exports["default"]=e.exports},cf80:function(e,t,r){"use strict";var n=r("8bdb"),i=r("1aad");n({target:"Math",stat:!0},{trunc:i})},d06e:function(e,t,r){"use strict";r("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,r("64aa");var n={props:{name:{type:String,default:uni.$u.props.icon.name},color:{type:String,default:uni.$u.props.icon.color},size:{type:[String,Number],default:uni.$u.props.icon.size},bold:{type:Boolean,default:uni.$u.props.icon.bold},index:{type:[String,Number],default:uni.$u.props.icon.index},hoverClass:{type:String,default:uni.$u.props.icon.hoverClass},customPrefix:{type:String,default:uni.$u.props.icon.customPrefix},label:{type:[String,Number],default:uni.$u.props.icon.label},labelPos:{type:String,default:uni.$u.props.icon.labelPos},labelSize:{type:[String,Number],default:uni.$u.props.icon.labelSize},labelColor:{type:String,default:uni.$u.props.icon.labelColor},space:{type:[String,Number],default:uni.$u.props.icon.space},imgMode:{type:String,default:uni.$u.props.icon.imgMode},width:{type:[String,Number],default:uni.$u.props.icon.width},height:{type:[String,Number],default:uni.$u.props.icon.height},top:{type:[String,Number],default:uni.$u.props.icon.top},stop:{type:Boolean,default:uni.$u.props.icon.stop}}};t.default=n},d292:function(e,t,r){"use strict";(function(t){r("7a76"),r("c9b5"),r("80e3"),r("4db2"),r("bf0f"),r("aa9c");var n,i,o,s=r("7125").Transform,a=r("3e6ee");e.exports=function(e,r){if(r.hostname=r.hostname||r.host,!r.hostname)throw new Error("Could not determine host. Specify host manually.");var c="MQIsdp"===r.protocolId&&3===r.protocolVersion?"mqttv3.1":"mqtt";(function(e){e.hostname||(e.hostname="localhost"),e.path||(e.path="/"),e.wsOptions||(e.wsOptions={})})(r);var u=function(e,t){var r="wxs"===e.protocol?"wss":"ws",n=r+"://"+e.hostname+e.path;return e.port&&80!==e.port&&443!==e.port&&(n=r+"://"+e.hostname+":"+e.port+e.path),"function"===typeof e.transformWsUrl&&(n=e.transformWsUrl(n,e,t)),n}(r,e);n=wx.connectSocket({url:u,protocols:[c]}),i=function(){var e=new s;return e._write=function(e,t,r){n.send({data:e.buffer,success:function(){r()},fail:function(e){r(new Error(e))}})},e._flush=function(e){n.close({success:function(){e()}})},e}(),o=a.obj(),o._destroy=function(e,t){n.close({success:function(){t&&t(e)}})};var l=o.destroy;return o.destroy=function(){o.destroy=l;var e=this;setTimeout((function(){n.close({fail:function(){e._destroy(new Error)}})}),0)}.bind(o),function(){n.onOpen((function(){o.setReadable(i),o.setWritable(i),o.emit("connect")})),n.onMessage((function(e){var r=e.data;r=r instanceof ArrayBuffer?t.from(r):t.from(r,"utf8"),i.push(r)})),n.onClose((function(){o.end(),o.destroy()})),n.onError((function(e){o.destroy(new Error(e.errMsg))}))}(),o}}).call(this,r("12e3").Buffer)},d4f8:function(e,t,r){var n=r("33ee");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);var i=r("967d").default;i("5311164e",n,!0,{sourceMap:!1,shadowMode:!1})},d67b:function(e,t,r){"use strict";t.decode=t.parse=r("90c3"),t.encode=t.stringify=r("c5a6")},d7989:function(e,t,r){"use strict";(function(t,n){var i=r("bdbb").default;r("7627"),r("c9b5"),r("bf0f"),r("ab80"),r("2797"),r("dc8a"),r("7a76"),r("aa9c"),r("4626"),r("5ef2");var o=r("bf30").EventEmitter,s=r("1dc2"),a=r("4c25"),c=r("7125").Writable,u=r("2c2e"),l=r("4609"),f=r("3f21"),p=r("abeb"),h=r("172d")("mqttjs:client"),d=t?t.nextTick:function(e){setTimeout(e,0)},g=n.setImmediate||function(e){d(e)},b={keepalive:60,reschedulePings:!0,protocolId:"MQTT",protocolVersion:4,reconnectPeriod:1e3,connectTimeout:3e4,clean:!0,resubscribe:!0},m=["ECONNREFUSED","EADDRINUSE","ECONNRESET","ENOTFOUND"],v={0:"",1:"Unacceptable protocol version",2:"Identifier rejected",3:"Server unavailable",4:"Bad username or password",5:"Not authorized",16:"No matching subscribers",17:"No subscription existed",128:"Unspecified error",129:"Malformed Packet",130:"Protocol Error",131:"Implementation specific error",132:"Unsupported Protocol Version",133:"Client Identifier not valid",134:"Bad User Name or Password",135:"Not authorized",136:"Server unavailable",137:"Server busy",138:"Banned",139:"Server shutting down",140:"Bad authentication method",141:"Keep Alive timeout",142:"Session taken over",143:"Topic Filter invalid",144:"Topic Name invalid",145:"Packet identifier in use",146:"Packet Identifier not found",147:"Receive Maximum exceeded",148:"Topic Alias invalid",149:"Packet too large",150:"Message rate too high",151:"Quota exceeded",152:"Administrative action",153:"Payload format invalid",154:"Retain not supported",155:"QoS not supported",156:"Use another server",157:"Server moved",158:"Shared Subscriptions not supported",159:"Connection rate exceeded",160:"Maximum connect time",161:"Subscription Identifiers not supported",162:"Wildcard Subscriptions not supported"};function y(e,t,r){h("sendPacket :: packet: %O",t),h("sendPacket :: emitting `packetsend`"),e.emit("packetsend",t),h("sendPacket :: writing to stream");var n=a.writeToStream(t,e.stream,e.options);h("sendPacket :: writeToStream result %s",n),!n&&r?(h("sendPacket :: handle events on `drain` once through callback."),e.stream.once("drain",r)):r&&(h("sendPacket :: invoking cb"),r())}function _(e){e&&(h("flush: queue exists? %b",!!e),Object.keys(e).forEach((function(t){"function"===typeof e[t].cb&&(e[t].cb(new Error("Connection closed")),delete e[t])})))}function w(e,t,r,n){h("storeAndSend :: store packet with cmd %s to outgoingStore",t.cmd),e.outgoingStore.put(t,(function(i){if(i)return r&&r(i);n(),y(e,t,r)}))}function S(e){h("nop ::",e)}function k(e,t){var r,n=this;if(!(this instanceof k))return new k(e,t);for(r in this.options=t||{},b)"undefined"===typeof this.options[r]?this.options[r]=b[r]:this.options[r]=t[r];h("MqttClient :: options.protocol",t.protocol),h("MqttClient :: options.protocolVersion",t.protocolVersion),h("MqttClient :: options.username",t.username),h("MqttClient :: options.keepalive",t.keepalive),h("MqttClient :: options.reconnectPeriod",t.reconnectPeriod),h("MqttClient :: options.rejectUnauthorized",t.rejectUnauthorized),this.options.clientId="string"===typeof t.clientId?t.clientId:function(){return"mqttjs_"+Math.random().toString(16).substr(2,8)}(),h("MqttClient :: clientId",this.options.clientId),this.options.customHandleAcks=5===t.protocolVersion&&t.customHandleAcks?t.customHandleAcks:function(){arguments[3](0)},this.streamBuilder=e,this.outgoingStore=t.outgoingStore||new s,this.incomingStore=t.incomingStore||new s,this.queueQoSZero=void 0===t.queueQoSZero||t.queueQoSZero,this._resubscribeTopics={},this.messageIdToTopic={},this.pingTimer=null,this.connected=!1,this.disconnecting=!1,this.queue=[],this.connackTimer=null,this.reconnectTimer=null,this._storeProcessing=!1,this._packetIdsDuringStoreProcessing={},this.nextId=Math.max(1,Math.floor(65535*Math.random())),this.outgoing={},this._firstConnection=!0,this.on("connect",(function(){var e=this.queue;h("connect :: sending queued packets"),function t(){var r,i=e.shift();h("deliver :: entry %o",i),i&&(r=i.packet,h("deliver :: call _sendPacket for %o",r),n._sendPacket(r,(function(e){i.cb&&i.cb(e),t()})))}()})),this.on("close",(function(){h("close :: connected set to `false`"),this.connected=!1,h("close :: clearing connackTimer"),clearTimeout(this.connackTimer),h("close :: clearing ping timer"),null!==n.pingTimer&&(n.pingTimer.clear(),n.pingTimer=null),h("close :: calling _setupReconnect"),this._setupReconnect()})),o.call(this),h("MqttClient :: setting up stream"),this._setupStream()}u(k,o),k.prototype._setupStream=function(){var e,t=this,r=this,n=new c,o=a.parser(this.options),s=null,u=[];function l(){if(u.length)d(f);else{var e=s;s=null,e()}}function f(){h("work :: getting next packet in queue");var e=u.shift();if(e)h("work :: packet pulled from queue"),r._handlePacket(e,l);else{h("work :: no packets in queue");var t=s;s=null,h("work :: done flag is %s",!!t),t&&t()}}if(h("_setupStream :: calling method to clear reconnect"),this._clearReconnect(),h("_setupStream :: using streamBuilder provided to client to create stream"),this.stream=this.streamBuilder(this),o.on("packet",(function(e){h("parser :: on packet push to packets array."),u.push(e)})),n._write=function(e,t,r){s=r,h("writable stream :: parsing buffer"),o.parse(e),f()},h("_setupStream :: pipe stream to writable stream"),this.stream.pipe(n),this.stream.on("error",(function(e){h("streamErrorHandler :: error",e.message),m.includes(e.code)?(h("streamErrorHandler :: emitting error"),r.emit("error",e)):S(e)})),this.stream.on("close",(function(){h("(%s)stream :: on close",r.options.clientId),function(e){e&&(h("flushVolatile :: deleting volatile messages from the queue and setting their callbacks as error function"),Object.keys(e).forEach((function(t){e[t].volatile&&"function"===typeof e[t].cb&&(e[t].cb(new Error("Connection closed")),delete e[t])})))}(r.outgoing),h("stream: emit close to MqttClient"),r.emit("close")})),h("_setupStream: sending packet `connect`"),e=Object.create(this.options),e.cmd="connect",y(this,e),o.on("error",this.emit.bind(this,"error")),this.options.properties){if(!this.options.properties.authenticationMethod&&this.options.properties.authenticationData)return r.end((function(){return t.emit("error",new Error("Packet has no Authentication Method"))})),this;if(this.options.properties.authenticationMethod&&this.options.authPacket&&"object"===i(this.options.authPacket)){var g=p({cmd:"auth",reasonCode:0},this.options.authPacket);y(this,g)}}this.stream.setMaxListeners(1e3),clearTimeout(this.connackTimer),this.connackTimer=setTimeout((function(){h("!!connectTimeout hit!! Calling _cleanUp with force `true`"),r._cleanUp(!0)}),this.options.connectTimeout)},k.prototype._handlePacket=function(e,t){var r=this.options;if(5===r.protocolVersion&&r.properties&&r.properties.maximumPacketSize&&r.properties.maximumPacketSize<e.length)return this.emit("error",new Error("exceeding packets size "+e.cmd)),this.end({reasonCode:149,properties:{reasonString:"Maximum packet size was exceeded"}}),this;switch(h("_handlePacket :: emitting packetreceive"),this.emit("packetreceive",e),e.cmd){case"publish":this._handlePublish(e,t);break;case"puback":case"pubrec":case"pubcomp":case"suback":case"unsuback":this._handleAck(e),t();break;case"pubrel":this._handlePubrel(e,t);break;case"connack":this._handleConnack(e),t();break;case"pingresp":this._handlePingresp(e),t();break;case"disconnect":this._handleDisconnect(e),t();break;default:break}},k.prototype._checkDisconnecting=function(e){return this.disconnecting&&(e?e(new Error("client disconnecting")):this.emit("error",new Error("client disconnecting"))),this.disconnecting},k.prototype.publish=function(e,t,r,n){var i;h("publish :: message `%s` to topic `%s`",t,e);var o=this.options;"function"===typeof r&&(n=r,r=null);if(r=p({qos:0,retain:!1,dup:!1},r),this._checkDisconnecting(n))return this;switch(i={cmd:"publish",topic:e,payload:t,qos:r.qos,retain:r.retain,messageId:this._nextId(),dup:r.dup},5===o.protocolVersion&&(i.properties=r.properties,(!o.properties&&i.properties&&i.properties.topicAlias||r.properties&&o.properties&&(r.properties.topicAlias&&o.properties.topicAliasMaximum&&r.properties.topicAlias>o.properties.topicAliasMaximum||!o.properties.topicAliasMaximum&&r.properties.topicAlias))&&delete i.properties.topicAlias),h("publish :: qos",r.qos),r.qos){case 1:case 2:this.outgoing[i.messageId]={volatile:!1,cb:n||S},this._storeProcessing?(h("_storeProcessing enabled"),this._packetIdsDuringStoreProcessing[i.messageId]=!1,this._storePacket(i,void 0,r.cbStorePut)):(h("MqttClient:publish: packet cmd: %s",i.cmd),this._sendPacket(i,void 0,r.cbStorePut));break;default:this._storeProcessing?(h("_storeProcessing enabled"),this._storePacket(i,n,r.cbStorePut)):(h("MqttClient:publish: packet cmd: %s",i.cmd),this._sendPacket(i,n,r.cbStorePut));break}return this},k.prototype.subscribe=function(){for(var e,t=new Array(arguments.length),r=0;r<arguments.length;r++)t[r]=arguments[r];var n,i=[],o=t.shift(),s=o.resubscribe,a=t.pop()||S,c=t.pop(),u=this,l=this.options.protocolVersion;if(delete o.resubscribe,"string"===typeof o&&(o=[o]),"function"!==typeof a&&(c=a,a=S),n=f.validateTopics(o),null!==n)return g(a,new Error("Invalid topic "+n)),this;if(this._checkDisconnecting(a))return h("subscribe: discconecting true"),this;var d={qos:0};if(5===l&&(d.nl=!1,d.rap=!1,d.rh=0),c=p(d,c),Array.isArray(o)?o.forEach((function(e){if(h("subscribe: array topic %s",e),!u._resubscribeTopics.hasOwnProperty(e)||u._resubscribeTopics[e].qos<c.qos||s){var t={topic:e,qos:c.qos};5===l&&(t.nl=c.nl,t.rap=c.rap,t.rh=c.rh,t.properties=c.properties),h("subscribe: pushing topic `%s` and qos `%s` to subs list",t.topic,t.qos),i.push(t)}})):Object.keys(o).forEach((function(e){if(h("subscribe: object topic %s",e),!u._resubscribeTopics.hasOwnProperty(e)||u._resubscribeTopics[e].qos<o[e].qos||s){var t={topic:e,qos:o[e].qos};5===l&&(t.nl=o[e].nl,t.rap=o[e].rap,t.rh=o[e].rh,t.properties=c.properties),h("subscribe: pushing `%s` to subs list",t),i.push(t)}})),e={cmd:"subscribe",subscriptions:i,qos:1,retain:!1,dup:!1,messageId:this._nextId()},c.properties&&(e.properties=c.properties),i.length){if(this.options.resubscribe){h("subscribe :: resubscribe true");var b=[];i.forEach((function(e){if(u.options.reconnectPeriod>0){var t={qos:e.qos};5===l&&(t.nl=e.nl||!1,t.rap=e.rap||!1,t.rh=e.rh||0,t.properties=e.properties),u._resubscribeTopics[e.topic]=t,b.push(e.topic)}})),u.messageIdToTopic[e.messageId]=b}return this.outgoing[e.messageId]={volatile:!0,cb:function(e,t){if(!e)for(var r=t.granted,n=0;n<r.length;n+=1)i[n].qos=r[n];a(e,i)}},h("subscribe :: call _sendPacket"),this._sendPacket(e),this}a(null,[])},k.prototype.unsubscribe=function(){for(var e={cmd:"unsubscribe",qos:1,messageId:this._nextId()},t=this,r=new Array(arguments.length),n=0;n<arguments.length;n++)r[n]=arguments[n];var o=r.shift(),s=r.pop()||S,a=r.pop();return"string"===typeof o&&(o=[o]),"function"!==typeof s&&(a=s,s=S),this._checkDisconnecting(s)||("string"===typeof o?e.unsubscriptions=[o]:Array.isArray(o)&&(e.unsubscriptions=o),this.options.resubscribe&&e.unsubscriptions.forEach((function(e){delete t._resubscribeTopics[e]})),"object"===i(a)&&a.properties&&(e.properties=a.properties),this.outgoing[e.messageId]={volatile:!0,cb:s},h("unsubscribe: call _sendPacket"),this._sendPacket(e)),this},k.prototype.end=function(e,t,r){var n=this;function o(){h("end :: closeStores: closing incoming and outgoing stores"),n.disconnected=!0,n.incomingStore.close((function(e){n.outgoingStore.close((function(t){if(h("end :: closeStores: emitting end"),n.emit("end"),r){var i=e||t;h("end :: closeStores: invoking callback with args"),r(i)}}))})),n._deferredReconnect&&n._deferredReconnect()}function s(){h("end :: (%s) :: finish :: calling _cleanUp with force %s",n.options.clientId,e),n._cleanUp(e,(function(){h("end :: finish :: calling process.nextTick on closeStores"),d(o.bind(n))}),t)}return h("end :: (%s)",this.options.clientId),null!=e&&"boolean"===typeof e||(r=t||S,t=e,e=!1,"object"!==i(t)&&(r=t,t=null,"function"!==typeof r&&(r=S))),"object"!==i(t)&&(r=t,t=null),h("end :: cb? %s",!!r),r=r||S,this.disconnecting?(r(),this):(this._clearReconnect(),this.disconnecting=!0,!e&&Object.keys(this.outgoing).length>0?(h("end :: (%s) :: calling finish in 10ms once outgoing is empty",n.options.clientId),this.once("outgoingEmpty",setTimeout.bind(null,s,10))):(h("end :: (%s) :: immediately calling finish",n.options.clientId),s()),this)},k.prototype.removeOutgoingMessage=function(e){var t=this.outgoing[e]?this.outgoing[e].cb:null;return delete this.outgoing[e],this.outgoingStore.del({messageId:e},(function(){t(new Error("Message removed"))})),this},k.prototype.reconnect=function(e){h("client reconnect");var t=this,r=function(){e?(t.options.incomingStore=e.incomingStore,t.options.outgoingStore=e.outgoingStore):(t.options.incomingStore=null,t.options.outgoingStore=null),t.incomingStore=t.options.incomingStore||new s,t.outgoingStore=t.options.outgoingStore||new s,t.disconnecting=!1,t.disconnected=!1,t._deferredReconnect=null,t._reconnect()};return this.disconnecting&&!this.disconnected?this._deferredReconnect=r:r(),this},k.prototype._reconnect=function(){var e=this;h("_reconnect: emitting reconnect to client"),this.emit("reconnect"),this.connected?(this.end((function(){e._setupStream()})),h("client already connected. disconnecting first.")):(h("_reconnect: calling _setupStream"),this._setupStream())},k.prototype._setupReconnect=function(){var e=this;!e.disconnecting&&!e.reconnectTimer&&e.options.reconnectPeriod>0?(this.reconnecting||(h("_setupReconnect :: emit `offline` state"),this.emit("offline"),h("_setupReconnect :: set `reconnecting` to `true`"),this.reconnecting=!0),h("_setupReconnect :: setting reconnectTimer for %d ms",e.options.reconnectPeriod),e.reconnectTimer=setInterval((function(){h("reconnectTimer :: reconnect triggered!"),e._reconnect()}),e.options.reconnectPeriod)):h("_setupReconnect :: doing nothing...")},k.prototype._clearReconnect=function(){h("_clearReconnect : clearing reconnect timer"),this.reconnectTimer&&(clearInterval(this.reconnectTimer),this.reconnectTimer=null)},k.prototype._cleanUp=function(e,t){var r=arguments[2];if(t&&(h("_cleanUp :: done callback provided for on stream close"),this.stream.on("close",t)),h("_cleanUp :: forced? %s",e),e)0===this.options.reconnectPeriod&&this.options.clean&&_(this.outgoing),h("_cleanUp :: (%s) :: destroying stream",this.options.clientId),this.stream.destroy();else{var n=p({cmd:"disconnect"},r);h("_cleanUp :: (%s) :: call _sendPacket with disconnect packet",this.options.clientId),this._sendPacket(n,g.bind(null,this.stream.end.bind(this.stream)))}this.disconnecting||(h("_cleanUp :: client not disconnecting. Clearing and resetting reconnect."),this._clearReconnect(),this._setupReconnect()),null!==this.pingTimer&&(h("_cleanUp :: clearing pingTimer"),this.pingTimer.clear(),this.pingTimer=null),t&&!this.connected&&(h("_cleanUp :: (%s) :: removing stream `done` callback `close` listener",this.options.clientId),this.stream.removeListener("close",t),t())},k.prototype._sendPacket=function(e,t,r){if(h("_sendPacket :: (%s) ::  start",this.options.clientId),r=r||S,!this.connected)return h("_sendPacket :: client not connected. Storing packet offline."),void this._storePacket(e,t,r);switch(this._shiftPingInterval(),e.cmd){case"publish":break;case"pubrel":return void w(this,e,t,r);default:return void y(this,e,t)}switch(e.qos){case 2:case 1:w(this,e,t,r);break;case 0:default:y(this,e,t);break}h("_sendPacket :: (%s) ::  end",this.options.clientId)},k.prototype._storePacket=function(e,t,r){h("_storePacket :: packet: %o",e),h("_storePacket :: cb? %s",!!t),r=r||S,0===(e.qos||0)&&this.queueQoSZero||"publish"!==e.cmd?this.queue.push({packet:e,cb:t}):e.qos>0?(t=this.outgoing[e.messageId]?this.outgoing[e.messageId].cb:null,this.outgoingStore.put(e,(function(e){if(e)return t&&t(e);r()}))):t&&t(new Error("No connection to broker"))},k.prototype._setupPingTimer=function(){h("_setupPingTimer :: keepalive %d (seconds)",this.options.keepalive);var e=this;!this.pingTimer&&this.options.keepalive&&(this.pingResp=!0,this.pingTimer=l((function(){e._checkPing()}),1e3*this.options.keepalive))},k.prototype._shiftPingInterval=function(){this.pingTimer&&this.options.keepalive&&this.options.reschedulePings&&this.pingTimer.reschedule(1e3*this.options.keepalive)},k.prototype._checkPing=function(){h("_checkPing :: checking ping..."),this.pingResp?(h("_checkPing :: ping response received. Clearing flag and sending `pingreq`"),this.pingResp=!1,this._sendPacket({cmd:"pingreq"})):(h("_checkPing :: calling _cleanUp with force true"),this._cleanUp(!0))},k.prototype._handlePingresp=function(){this.pingResp=!0},k.prototype._handleConnack=function(e){h("_handleConnack");var t=this.options,r=t.protocolVersion,n=5===r?e.reasonCode:e.returnCode;if(clearTimeout(this.connackTimer),e.properties&&(e.properties.topicAliasMaximum&&(t.properties||(t.properties={}),t.properties.topicAliasMaximum=e.properties.topicAliasMaximum),e.properties.serverKeepAlive&&t.keepalive&&(t.keepalive=e.properties.serverKeepAlive,this._shiftPingInterval()),e.properties.maximumPacketSize&&(t.properties||(t.properties={}),t.properties.maximumPacketSize=e.properties.maximumPacketSize)),0===n)this.reconnecting=!1,this._onConnect(e);else if(n>0){var i=new Error("Connection refused: "+v[n]);i.code=n,this.emit("error",i)}},k.prototype._handlePublish=function(e,t){h("_handlePublish: packet %o",e),t="undefined"!==typeof t?t:S;var r=e.topic.toString(),n=e.payload,i=e.qos,o=e.messageId,s=this,a=this.options,c=[0,16,128,131,135,144,145,151,153];switch(h("_handlePublish: qos %d",i),i){case 2:a.customHandleAcks(r,n,e,(function(r,n){return r instanceof Error||(n=r,r=null),r?s.emit("error",r):-1===c.indexOf(n)?s.emit("error",new Error("Wrong reason code for pubrec")):void(n?s._sendPacket({cmd:"pubrec",messageId:o,reasonCode:n},t):s.incomingStore.put(e,(function(){s._sendPacket({cmd:"pubrec",messageId:o},t)})))}));break;case 1:a.customHandleAcks(r,n,e,(function(i,a){return i instanceof Error||(a=i,i=null),i?s.emit("error",i):-1===c.indexOf(a)?s.emit("error",new Error("Wrong reason code for puback")):(a||s.emit("message",r,n,e),void s.handleMessage(e,(function(e){if(e)return t&&t(e);s._sendPacket({cmd:"puback",messageId:o,reasonCode:a},t)})))}));break;case 0:this.emit("message",r,n,e),this.handleMessage(e,t);break;default:h("_handlePublish: unknown QoS. Doing nothing.");break}},k.prototype.handleMessage=function(e,t){t()},k.prototype._handleAck=function(e){var t,r=e.messageId,n=e.cmd,i=null,o=this.outgoing[r]?this.outgoing[r].cb:null,s=this;if(o){switch(h("_handleAck :: packet type",n),n){case"pubcomp":case"puback":var a=e.reasonCode;a&&a>0&&16!==a&&(t=new Error("Publish error: "+v[a]),t.code=a,o(t,e)),delete this.outgoing[r],this.outgoingStore.del(e,o);break;case"pubrec":i={cmd:"pubrel",qos:2,messageId:r};var c=e.reasonCode;c&&c>0&&16!==c?(t=new Error("Publish error: "+v[c]),t.code=c,o(t,e)):this._sendPacket(i);break;case"suback":delete this.outgoing[r];for(var u=0;u<e.granted.length;u++)if(0!==(128&e.granted[u])){var l=this.messageIdToTopic[r];l&&l.forEach((function(e){delete s._resubscribeTopics[e]}))}o(null,e);break;case"unsuback":delete this.outgoing[r],o(null);break;default:s.emit("error",new Error("unrecognized packet type"))}this.disconnecting&&0===Object.keys(this.outgoing).length&&this.emit("outgoingEmpty")}else h("_handleAck :: Server sent an ack in error. Ignoring.")},k.prototype._handlePubrel=function(e,t){h("handling pubrel packet"),t="undefined"!==typeof t?t:S;var r=e.messageId,n=this,i={cmd:"pubcomp",messageId:r};n.incomingStore.get(e,(function(e,r){e?n._sendPacket(i,t):(n.emit("message",r.topic,r.payload,r),n.handleMessage(r,(function(e){if(e)return t(e);n.incomingStore.del(r,S),n._sendPacket(i,t)})))}))},k.prototype._handleDisconnect=function(e){this.emit("disconnect",e)},k.prototype._nextId=function(){var e=this.nextId++;return 65536===this.nextId&&(this.nextId=1),e},k.prototype.getLastMessageId=function(){return 1===this.nextId?65535:this.nextId-1},k.prototype._resubscribe=function(e){h("_resubscribe");var t=Object.keys(this._resubscribeTopics);if(!this._firstConnection&&(this.options.clean||5===this.options.protocolVersion&&!e.sessionPresent)&&t.length>0)if(this.options.resubscribe)if(5===this.options.protocolVersion){h("_resubscribe: protocolVersion 5");for(var r=0;r<t.length;r++){var n={};n[t[r]]=this._resubscribeTopics[t[r]],n.resubscribe=!0,this.subscribe(n,{properties:n[t[r]].properties})}}else this._resubscribeTopics.resubscribe=!0,this.subscribe(this._resubscribeTopics);else this._resubscribeTopics={};this._firstConnection=!1},k.prototype._onConnect=function(e){if(this.disconnected)this.emit("connect",e);else{var t=this;this._setupPingTimer(),this._resubscribe(e),this.connected=!0,function r(){var n=t.outgoingStore.createStream();function i(){t._storeProcessing=!1,t._packetIdsDuringStoreProcessing={}}function o(){n.destroy(),n=null,i()}t.once("close",o),n.on("error",(function(e){i(),t.removeListener("close",o),t.emit("error",e)})),n.on("end",(function(){var n=!0;for(var s in t._packetIdsDuringStoreProcessing)if(!t._packetIdsDuringStoreProcessing[s]){n=!1;break}n?(i(),t.removeListener("close",o),t.emit("connect",e)):r()})),function e(){if(n){t._storeProcessing=!0;var r,i=n.read(1);i?t._packetIdsDuringStoreProcessing[i.messageId]?e():t.disconnecting||t.reconnectTimer?n.destroy&&n.destroy():(r=t.outgoing[i.messageId]?t.outgoing[i.messageId].cb:null,t.outgoing[i.messageId]={volatile:!1,cb:function(t,n){r&&r(t,n),e()}},t._packetIdsDuringStoreProcessing[i.messageId]=!0,t._sendPacket(i)):n.once("readable",e)}}()}()}},e.exports=k}).call(this,r("28d0"),r("0ee4"))},dc2d:function(e,t,r){"use strict";(function(t){var n=r("bdbb").default;r("5c47"),r("2c10"),r("64aa"),r("7a76"),r("c9b5"),r("a1c1"),r("5ef2"),r("8f71"),r("bf0f");var i=r("d7989"),o=r("1dc2"),s=r("519e"),a=r("abeb"),c=r("172d")("mqttjs"),u={};function l(e,t){if(c("connecting to an MQTT broker..."),"object"!==n(e)||t||(t=e,e=null),t=t||{},e){var r=s.parse(e,!0);if(null!=r.port&&(r.port=Number(r.port)),t=a(r,t),null===t.protocol)throw new Error("Missing protocol");t.protocol=t.protocol.replace(/:$/,"")}if(function(e){var t;e.auth&&(t=e.auth.match(/^(.+):(.+)$/),t?(e.username=t[1],e.password=t[2]):e.username=e.auth)}(t),t.query&&"string"===typeof t.query.clientId&&(t.clientId=t.query.clientId),t.cert&&t.key){if(!t.protocol)throw new Error("Missing secure protocol key");if(-1===["mqtts","wss","wxs","alis"].indexOf(t.protocol))switch(t.protocol){case"mqtt":t.protocol="mqtts";break;case"ws":t.protocol="wss";break;case"wx":t.protocol="wxs";break;case"ali":t.protocol="alis";break;default:throw new Error('Unknown protocol for secure connection: "'+t.protocol+'"!')}}if(!u[t.protocol]){var o=-1!==["mqtts","wss"].indexOf(t.protocol);t.protocol=["mqtt","mqtts","ws","wss","wx","wxs","ali","alis"].filter((function(e,t){return(!o||t%2!==0)&&"function"===typeof u[e]}))[0]}if(!1===t.clean&&!t.clientId)throw new Error("Missing clientId for unclean clients");t.protocol&&(t.defaultProtocol=t.protocol);var l=new i((function(e){return t.servers&&(e._reconnectCount&&e._reconnectCount!==t.servers.length||(e._reconnectCount=0),t.host=t.servers[e._reconnectCount].host,t.port=t.servers[e._reconnectCount].port,t.protocol=t.servers[e._reconnectCount].protocol?t.servers[e._reconnectCount].protocol:t.defaultProtocol,t.hostname=t.host,e._reconnectCount++),c("calling streambuilder for",t.protocol),u[t.protocol](e,t)}),t);return l.on("error",(function(){})),l}"undefined"!==typeof t&&"browser"!==t.title||"function"!==typeof r?(u.mqtt=r("6b03"),u.tcp=r("6b03"),u.ssl=r("a658"),u.tls=r("a658"),u.mqtts=r("a658")):(u.wx=r("d292"),u.wxs=r("d292"),u.ali=r("0b62"),u.alis=r("0b62")),u.ws=r("5ebd"),u.wss=r("5ebd"),e.exports=l,e.exports.connect=l,e.exports.MqttClient=i,e.exports.Store=o}).call(this,r("28d0"))},dc45:function(e,t,r){"use strict";r.r(t);var n=r("7e5f"),i=r("7891");for(var o in i)["default"].indexOf(o)<0&&function(e){r.d(t,e,(function(){return i[e]}))}(o);r("54cb");var s=r("828b"),a=Object(s["a"])(i["default"],n["b"],n["c"],!1,null,"26861ad0",null,!1,n["a"],void 0);t["default"]=a.exports},dc92:function(e,t,r){"use strict";r("01a2"),r("e39c"),r("bf0f"),r("6a54"),r("f7a5"),r("c223"),r("c9b5"),r("ab80"),r("cf80"),r("64aa"),r("aa9c"),r("5ef2"),r("7a76");var n=r("12e3"),i=n.Buffer,o=Symbol.for("BufferList");function s(e){if(!(this instanceof s))return new s(e);s._init.call(this,e)}s._init=function(e){Object.defineProperty(this,o,{value:!0}),this._bufs=[],this.length=0,e&&this.append(e)},s.prototype._new=function(e){return new s(e)},s.prototype._offset=function(e){if(0===e)return[0,0];for(var t=0,r=0;r<this._bufs.length;r++){var n=t+this._bufs[r].length;if(e<n||r===this._bufs.length-1)return[r,e-t];t=n}},s.prototype._reverseOffset=function(e){for(var t=e[0],r=e[1],n=0;n<t;n++)r+=this._bufs[n].length;return r},s.prototype.get=function(e){if(!(e>this.length||e<0)){var t=this._offset(e);return this._bufs[t[0]][t[1]]}},s.prototype.slice=function(e,t){return"number"===typeof e&&e<0&&(e+=this.length),"number"===typeof t&&t<0&&(t+=this.length),this.copy(null,0,e,t)},s.prototype.copy=function(e,t,r,n){if(("number"!==typeof r||r<0)&&(r=0),("number"!==typeof n||n>this.length)&&(n=this.length),r>=this.length)return e||i.alloc(0);if(n<=0)return e||i.alloc(0);var o=!!e,s=this._offset(r),a=n-r,c=a,u=o&&t||0,l=s[1];if(0===r&&n===this.length){if(!o)return 1===this._bufs.length?this._bufs[0]:i.concat(this._bufs,this.length);for(var f=0;f<this._bufs.length;f++)this._bufs[f].copy(e,u),u+=this._bufs[f].length;return e}if(c<=this._bufs[s[0]].length-l)return o?this._bufs[s[0]].copy(e,t,l,l+c):this._bufs[s[0]].slice(l,l+c);o||(e=i.allocUnsafe(a));for(var p=s[0];p<this._bufs.length;p++){var h=this._bufs[p].length-l;if(!(c>h)){this._bufs[p].copy(e,u,l,l+c),u+=h;break}this._bufs[p].copy(e,u,l),u+=h,c-=h,l&&(l=0)}return e.length>u?e.slice(0,u):e},s.prototype.shallowSlice=function(e,t){if(e=e||0,t="number"!==typeof t?this.length:t,e<0&&(e+=this.length),t<0&&(t+=this.length),e===t)return this._new();var r=this._offset(e),n=this._offset(t),i=this._bufs.slice(r[0],n[0]+1);return 0===n[1]?i.pop():i[i.length-1]=i[i.length-1].slice(0,n[1]),0!==r[1]&&(i[0]=i[0].slice(r[1])),this._new(i)},s.prototype.toString=function(e,t,r){return this.slice(t,r).toString(e)},s.prototype.consume=function(e){if(e=Math.trunc(e),Number.isNaN(e)||e<=0)return this;while(this._bufs.length){if(!(e>=this._bufs[0].length)){this._bufs[0]=this._bufs[0].slice(e),this.length-=e;break}e-=this._bufs[0].length,this.length-=this._bufs[0].length,this._bufs.shift()}return this},s.prototype.duplicate=function(){for(var e=this._new(),t=0;t<this._bufs.length;t++)e.append(this._bufs[t]);return e},s.prototype.append=function(e){if(null==e)return this;if(e.buffer)this._appendBuffer(i.from(e.buffer,e.byteOffset,e.byteLength));else if(Array.isArray(e))for(var t=0;t<e.length;t++)this.append(e[t]);else if(this._isBufferList(e))for(var r=0;r<e._bufs.length;r++)this.append(e._bufs[r]);else"number"===typeof e&&(e=e.toString()),this._appendBuffer(i.from(e));return this},s.prototype._appendBuffer=function(e){this._bufs.push(e),this.length+=e.length},s.prototype.indexOf=function(e,t,r){if(void 0===r&&"string"===typeof t&&(r=t,t=void 0),"function"===typeof e||Array.isArray(e))throw new TypeError('The "value" argument must be one of type string, Buffer, BufferList, or Uint8Array.');if("number"===typeof e?e=i.from([e]):"string"===typeof e?e=i.from(e,r):this._isBufferList(e)?e=e.slice():Array.isArray(e.buffer)?e=i.from(e.buffer,e.byteOffset,e.byteLength):i.isBuffer(e)||(e=i.from(e)),t=Number(t||0),isNaN(t)&&(t=0),t<0&&(t=this.length+t),t<0&&(t=0),0===e.length)return t>this.length?this.length:t;for(var n=this._offset(t),o=n[0],s=n[1];o<this._bufs.length;o++){var a=this._bufs[o];while(s<a.length){var c=a.length-s;if(c>=e.length){var u=a.indexOf(e,s);if(-1!==u)return this._reverseOffset([o,u]);s=a.length-e.length+1}else{var l=this._reverseOffset([o,s]);if(this._match(l,e))return l;s++}}s=0}return-1},s.prototype._match=function(e,t){if(this.length-e<t.length)return!1;for(var r=0;r<t.length;r++)if(this.get(e+r)!==t[r])return!1;return!0},function(){var e={readDoubleBE:8,readDoubleLE:8,readFloatBE:4,readFloatLE:4,readInt32BE:4,readInt32LE:4,readUInt32BE:4,readUInt32LE:4,readInt16BE:2,readInt16LE:2,readUInt16BE:2,readUInt16LE:2,readInt8:1,readUInt8:1,readIntBE:null,readIntLE:null,readUIntBE:null,readUIntLE:null};for(var t in e)(function(t){s.prototype[t]=null===e[t]?function(e,r){return this.slice(e,e+r)[t](0,r)}:function(){var r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;return this.slice(r,r+e[t])[t](0)}})(t)}(),s.prototype._isBufferList=function(e){return e instanceof s||s.isBufferList(e)},s.isBufferList=function(e){return null!=e&&e[o]},e.exports=s},e03a:function(e,t,r){"use strict";var n=r("414f"),i=r.n(n);i.a},e476:function(e,t){e.exports=function(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n},e.exports.__esModule=!0,e.exports["default"]=e.exports},edd4:function(e,t,r){"use strict";(function(t,n){var i=r("ca99f");function o(e){var t=this;this.next=null,this.entry=null,this.finish=function(){(function(e,t,r){var n=e.entry;e.entry=null;while(n){var i=n.callback;t.pendingcb--,i(r),n=n.next}t.corkedRequestsFree?t.corkedRequestsFree.next=e:t.corkedRequestsFree=e})(t,e)}}e.exports=m;var s,a=!t.browser&&["v0.10","v0.9."].indexOf(t.version.slice(0,5))>-1?setImmediate:i.nextTick;m.WritableState=b;var c=Object.create(r("8ce8"));c.inherits=r("2c2e");var u={deprecate:r("88fd")},l=r("51c3"),f=r("5f79").Buffer,p=n.Uint8Array||function(){};var h,d=r("c741");function g(){}function b(e,t){s=s||r("0ec1"),e=e||{};var n=t instanceof s;this.objectMode=!!e.objectMode,n&&(this.objectMode=this.objectMode||!!e.writableObjectMode);var c=e.highWaterMark,u=e.writableHighWaterMark,l=this.objectMode?16:16384;this.highWaterMark=c||0===c?c:n&&(u||0===u)?u:l,this.highWaterMark=Math.floor(this.highWaterMark),this.finalCalled=!1,this.needDrain=!1,this.ending=!1,this.ended=!1,this.finished=!1,this.destroyed=!1;var f=!1===e.decodeStrings;this.decodeStrings=!f,this.defaultEncoding=e.defaultEncoding||"utf8",this.length=0,this.writing=!1,this.corked=0,this.sync=!0,this.bufferProcessing=!1,this.onwrite=function(e){(function(e,t){var r=e._writableState,n=r.sync,o=r.writecb;if(function(e){e.writing=!1,e.writecb=null,e.length-=e.writelen,e.writelen=0}(r),t)(function(e,t,r,n,o){--t.pendingcb,r?(i.nextTick(o,n),i.nextTick(k,e,t),e._writableState.errorEmitted=!0,e.emit("error",n)):(o(n),e._writableState.errorEmitted=!0,e.emit("error",n),k(e,t))})(e,r,n,t,o);else{var s=w(r);s||r.corked||r.bufferProcessing||!r.bufferedRequest||_(e,r),n?a(y,e,r,s,o):y(e,r,s,o)}})(t,e)},this.writecb=null,this.writelen=0,this.bufferedRequest=null,this.lastBufferedRequest=null,this.pendingcb=0,this.prefinished=!1,this.errorEmitted=!1,this.bufferedRequestCount=0,this.corkedRequestsFree=new o(this)}function m(e){if(s=s||r("0ec1"),!h.call(m,this)&&!(this instanceof s))return new m(e);this._writableState=new b(e,this),this.writable=!0,e&&("function"===typeof e.write&&(this._write=e.write),"function"===typeof e.writev&&(this._writev=e.writev),"function"===typeof e.destroy&&(this._destroy=e.destroy),"function"===typeof e.final&&(this._final=e.final)),l.call(this)}function v(e,t,r,n,i,o,s){t.writelen=n,t.writecb=s,t.writing=!0,t.sync=!0,r?e._writev(i,t.onwrite):e._write(i,o,t.onwrite),t.sync=!1}function y(e,t,r,n){r||function(e,t){0===t.length&&t.needDrain&&(t.needDrain=!1,e.emit("drain"))}(e,t),t.pendingcb--,n(),k(e,t)}function _(e,t){t.bufferProcessing=!0;var r=t.bufferedRequest;if(e._writev&&r&&r.next){var n=t.bufferedRequestCount,i=new Array(n),s=t.corkedRequestsFree;s.entry=r;var a=0,c=!0;while(r)i[a]=r,r.isBuf||(c=!1),r=r.next,a+=1;i.allBuffers=c,v(e,t,!0,t.length,i,"",s.finish),t.pendingcb++,t.lastBufferedRequest=null,s.next?(t.corkedRequestsFree=s.next,s.next=null):t.corkedRequestsFree=new o(t),t.bufferedRequestCount=0}else{while(r){var u=r.chunk,l=r.encoding,f=r.callback,p=t.objectMode?1:u.length;if(v(e,t,!1,p,u,l,f),r=r.next,t.bufferedRequestCount--,t.writing)break}null===r&&(t.lastBufferedRequest=null)}t.bufferedRequest=r,t.bufferProcessing=!1}function w(e){return e.ending&&0===e.length&&null===e.bufferedRequest&&!e.finished&&!e.writing}function S(e,t){e._final((function(r){t.pendingcb--,r&&e.emit("error",r),t.prefinished=!0,e.emit("prefinish"),k(e,t)}))}function k(e,t){var r=w(t);return r&&(function(e,t){t.prefinished||t.finalCalled||("function"===typeof e._final?(t.pendingcb++,t.finalCalled=!0,i.nextTick(S,e,t)):(t.prefinished=!0,e.emit("prefinish")))}(e,t),0===t.pendingcb&&(t.finished=!0,e.emit("finish"))),r}c.inherits(m,l),b.prototype.getBuffer=function(){var e=this.bufferedRequest,t=[];while(e)t.push(e),e=e.next;return t},function(){try{Object.defineProperty(b.prototype,"buffer",{get:u.deprecate((function(){return this.getBuffer()}),"_writableState.buffer is deprecated. Use _writableState.getBuffer instead.","DEP0003")})}catch(e){}}(),"function"===typeof Symbol&&Symbol.hasInstance&&"function"===typeof Function.prototype[Symbol.hasInstance]?(h=Function.prototype[Symbol.hasInstance],Object.defineProperty(m,Symbol.hasInstance,{value:function(e){return!!h.call(this,e)||this===m&&(e&&e._writableState instanceof b)}})):h=function(e){return e instanceof this},m.prototype.pipe=function(){this.emit("error",new Error("Cannot pipe, not readable"))},m.prototype.write=function(e,t,r){var n=this._writableState,o=!1,s=!n.objectMode&&function(e){return f.isBuffer(e)||e instanceof p}(e);return s&&!f.isBuffer(e)&&(e=function(e){return f.from(e)}(e)),"function"===typeof t&&(r=t,t=null),s?t="buffer":t||(t=n.defaultEncoding),"function"!==typeof r&&(r=g),n.ended?function(e,t){var r=new Error("write after end");e.emit("error",r),i.nextTick(t,r)}(this,r):(s||function(e,t,r,n){var o=!0,s=!1;return null===r?s=new TypeError("May not write null values to stream"):"string"===typeof r||void 0===r||t.objectMode||(s=new TypeError("Invalid non-string/buffer chunk")),s&&(e.emit("error",s),i.nextTick(n,s),o=!1),o}(this,n,e,r))&&(n.pendingcb++,o=function(e,t,r,n,i,o){if(!r){var s=function(e,t,r){e.objectMode||!1===e.decodeStrings||"string"!==typeof t||(t=f.from(t,r));return t}(t,n,i);n!==s&&(r=!0,i="buffer",n=s)}var a=t.objectMode?1:n.length;t.length+=a;var c=t.length<t.highWaterMark;c||(t.needDrain=!0);if(t.writing||t.corked){var u=t.lastBufferedRequest;t.lastBufferedRequest={chunk:n,encoding:i,isBuf:r,callback:o,next:null},u?u.next=t.lastBufferedRequest:t.bufferedRequest=t.lastBufferedRequest,t.bufferedRequestCount+=1}else v(e,t,!1,a,n,i,o);return c}(this,n,s,e,t,r)),o},m.prototype.cork=function(){var e=this._writableState;e.corked++},m.prototype.uncork=function(){var e=this._writableState;e.corked&&(e.corked--,e.writing||e.corked||e.finished||e.bufferProcessing||!e.bufferedRequest||_(this,e))},m.prototype.setDefaultEncoding=function(e){if("string"===typeof e&&(e=e.toLowerCase()),!(["hex","utf8","utf-8","ascii","binary","base64","ucs2","ucs-2","utf16le","utf-16le","raw"].indexOf((e+"").toLowerCase())>-1))throw new TypeError("Unknown encoding: "+e);return this._writableState.defaultEncoding=e,this},Object.defineProperty(m.prototype,"writableHighWaterMark",{enumerable:!1,get:function(){return this._writableState.highWaterMark}}),m.prototype._write=function(e,t,r){r(new Error("_write() is not implemented"))},m.prototype._writev=null,m.prototype.end=function(e,t,r){var n=this._writableState;"function"===typeof e?(r=e,e=null,t=null):"function"===typeof t&&(r=t,t=null),null!==e&&void 0!==e&&this.write(e,t),n.corked&&(n.corked=1,this.uncork()),n.ending||n.finished||function(e,t,r){t.ending=!0,k(e,t),r&&(t.finished?i.nextTick(r):e.once("finish",r));t.ended=!0,e.writable=!1}(this,n,r)},Object.defineProperty(m.prototype,"destroyed",{get:function(){return void 0!==this._writableState&&this._writableState.destroyed},set:function(e){this._writableState&&(this._writableState.destroyed=e)}}),m.prototype.destroy=d.destroy,m.prototype._undestroy=d.undestroy,m.prototype._destroy=function(e,t){this.end(),t(e)}}).call(this,r("28d0"),r("0ee4"))},eecb:function(e,t,r){var n=r("1b42");function i(e){var t=function(){return t.called?t.value:(t.called=!0,t.value=e.apply(this,arguments))};return t.called=!1,t}function o(e){var t=function(){if(t.called)throw new Error(t.onceError);return t.called=!0,t.value=e.apply(this,arguments)},r=e.name||"Function wrapped with `once`";return t.onceError=r+" shouldn't be called more than once",t.called=!1,t}e.exports=n(i),e.exports.strict=n(o),i.proto=i((function(){Object.defineProperty(Function.prototype,"once",{value:function(){return i(this)},configurable:!0}),Object.defineProperty(Function.prototype,"onceStrict",{value:function(){return o(this)},configurable:!0})}))},f579:function(e,t,r){var n=r("c86c");t=n(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */uni-view[data-v-59765974], uni-scroll-view[data-v-59765974], uni-swiper-item[data-v-59765974]{display:flex;flex-direction:column;flex-shrink:0;flex-grow:0;flex-basis:auto;align-items:stretch;align-content:flex-start}@font-face{font-family:uicon-iconfont;src:url(https://at.alicdn.com/t/font_2225171_8kdcwk4po24.ttf) format("truetype")}.u-icon[data-v-59765974]{display:flex;align-items:center}.u-icon--left[data-v-59765974]{flex-direction:row-reverse;align-items:center}.u-icon--right[data-v-59765974]{flex-direction:row;align-items:center}.u-icon--top[data-v-59765974]{flex-direction:column-reverse;justify-content:center}.u-icon--bottom[data-v-59765974]{flex-direction:column;justify-content:center}.u-icon__icon[data-v-59765974]{font-family:uicon-iconfont;position:relative;display:flex;flex-direction:row;align-items:center}.u-icon__icon--primary[data-v-59765974]{color:#3c9cff}.u-icon__icon--success[data-v-59765974]{color:#5ac725}.u-icon__icon--error[data-v-59765974]{color:#f56c6c}.u-icon__icon--warning[data-v-59765974]{color:#f9ae3d}.u-icon__icon--info[data-v-59765974]{color:#909399}.u-icon__img[data-v-59765974]{height:auto;will-change:transform}.u-icon__label[data-v-59765974]{line-height:1}',""]),e.exports=t},f6e0:function(e,t,r){"use strict";r.r(t);var n=r("fcaf"),i=r.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){r.d(t,e,(function(){return n[e]}))}(o);t["default"]=i.a},f807:function(e,t,r){(function(t){r("f7a5");var n={},i=t.isBuffer(t.from([1,2]).subarray(0,1));function o(e){var r=t.allocUnsafe(2);return r.writeUInt8(e>>8,0),r.writeUInt8(255&e,1),r}e.exports={cache:n,generateCache:function(){for(var e=0;e<65536;e++)n[e]=o(e)},generateNumber:o,genBufVariableByteInt:function(e){var r=0,n=0,o=t.allocUnsafe(4);do{r=e%128|0,e=e/128|0,e>0&&(r|=128),o.writeUInt8(r,n++)}while(e>0&&n<4);return e>0&&(n=0),i?o.subarray(0,n):o.slice(0,n)},generate4ByteBuffer:function(e){var r=t.allocUnsafe(4);return r.writeUInt32BE(e,0),r}}}).call(this,r("12e3").Buffer)},f874:function(e,t,r){"use strict";var n=r("5f79").Buffer,i=r(2);function o(e,t,r){e.copy(t,r)}e.exports=function(){function e(){(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")})(this,e),this.head=null,this.tail=null,this.length=0}return e.prototype.push=function(e){var t={data:e,next:null};this.length>0?this.tail.next=t:this.head=t,this.tail=t,++this.length},e.prototype.unshift=function(e){var t={data:e,next:this.head};0===this.length&&(this.tail=t),this.head=t,++this.length},e.prototype.shift=function(){if(0!==this.length){var e=this.head.data;return 1===this.length?this.head=this.tail=null:this.head=this.head.next,--this.length,e}},e.prototype.clear=function(){this.head=this.tail=null,this.length=0},e.prototype.join=function(e){if(0===this.length)return"";var t=this.head,r=""+t.data;while(t=t.next)r+=e+t.data;return r},e.prototype.concat=function(e){if(0===this.length)return n.alloc(0);if(1===this.length)return this.head.data;var t=n.allocUnsafe(e>>>0),r=this.head,i=0;while(r)o(r.data,t,i),i+=r.data.length,r=r.next;return t},e}(),i&&i.inspect&&i.inspect.custom&&(e.exports.prototype[i.inspect.custom]=function(){var e=i.inspect({length:this.length});return this.constructor.name+" "+e})},fa9e:function(e,t,r){"use strict";r.d(t,"b",(function(){return i})),r.d(t,"c",(function(){return o})),r.d(t,"a",(function(){return n}));var n={uLoadingIcon:r("dc45").default,uIcon:r("764e").default},i=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("v-uni-button",{staticClass:"u-button u-reset-button",class:e.bemClass,style:[e.baseColor,e.$u.addStyle(e.customStyle)],attrs:{"hover-start-time":Number(e.hoverStartTime),"hover-stay-time":Number(e.hoverStayTime),"form-type":e.formType,"open-type":e.openType,"app-parameter":e.appParameter,"hover-stop-propagation":e.hoverStopPropagation,"send-message-title":e.sendMessageTitle,"send-message-path":e.sendMessagePath,lang:e.lang,"data-name":e.dataName,"session-from":e.sessionFrom,"send-message-img":e.sendMessageImg,"show-message-card":e.showMessageCard,"hover-class":e.disabled||e.loading?"":"u-button--active"},on:{getphonenumber:function(t){arguments[0]=t=e.$handleEvent(t),e.getphonenumber.apply(void 0,arguments)},getuserinfo:function(t){arguments[0]=t=e.$handleEvent(t),e.getuserinfo.apply(void 0,arguments)},error:function(t){arguments[0]=t=e.$handleEvent(t),e.error.apply(void 0,arguments)},opensetting:function(t){arguments[0]=t=e.$handleEvent(t),e.opensetting.apply(void 0,arguments)},launchapp:function(t){arguments[0]=t=e.$handleEvent(t),e.launchapp.apply(void 0,arguments)},agreeprivacyauthorization:function(t){arguments[0]=t=e.$handleEvent(t),e.agreeprivacyauthorization.apply(void 0,arguments)},click:function(t){arguments[0]=t=e.$handleEvent(t),e.clickHandler.apply(void 0,arguments)}}},[e.loading?[r("u-loading-icon",{attrs:{mode:e.loadingMode,size:1.15*e.loadingSize,color:e.loadingColor}}),r("v-uni-text",{staticClass:"u-button__loading-text",style:[{fontSize:e.textSize+"px"}]},[e._v(e._s(e.loadingText||e.text))])]:[e.icon?r("u-icon",{attrs:{name:e.icon,color:e.iconColorCom,size:1.35*e.textSize,customStyle:{marginRight:"2px"}}}):e._e(),e._t("default",[r("v-uni-text",{staticClass:"u-button__text",style:[{fontSize:e.textSize+"px"}]},[e._v(e._s(e.text))])])]],2)},o=[]},fcaf:function(e,t,r){"use strict";r("6a54");var n=r("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,r("aa9c"),r("4626"),r("5ac7"),r("5ef2");var i=n(r("9e2e")),o=n(r("d06e")),s={name:"u-icon",data:function(){return{}},mixins:[uni.$u.mpMixin,uni.$u.mixin,o.default],computed:{uClasses:function(){var e=[];return e.push(this.customPrefix+"-"+this.name),this.color&&uni.$u.config.type.includes(this.color)&&e.push("u-icon__icon--"+this.color),e},iconStyle:function(){var e={};return e={fontSize:uni.$u.addUnit(this.size),lineHeight:uni.$u.addUnit(this.size),fontWeight:this.bold?"bold":"normal",top:uni.$u.addUnit(this.top)},this.color&&!uni.$u.config.type.includes(this.color)&&(e.color=this.color),e},isImg:function(){return-1!==this.name.indexOf("/")},imgStyle:function(){var e={};return e.width=this.width?uni.$u.addUnit(this.width):uni.$u.addUnit(this.size),e.height=this.height?uni.$u.addUnit(this.height):uni.$u.addUnit(this.size),e},icon:function(){return i.default["uicon-"+this.name]||this.name}},methods:{clickHandler:function(e){this.$emit("click",this.index),this.stop&&this.preventEvent(e)}}};t.default=s},fe6b:function(e,t,r){"use strict";var n=r("8bdb"),i=r("2c57");n({target:"Number",stat:!0,forced:Number.parseInt!==i},{parseInt:i})},feb3:function(e,t,r){r("6a88"),r("bf0f"),r("7996");var n=r("14bd"),i=r("cdb7"),o=r("a362");e.exports=function(e){var t=i();return function(){var r,i=n(e);if(t){var s=n(this).constructor;r=Reflect.construct(i,arguments,s)}else r=i.apply(this,arguments);return o(this,r)}},e.exports.__esModule=!0,e.exports["default"]=e.exports}}]);