.r-flex {
	display: flex;
	flex-direction: row;
	align-items: center;
	&-1 {
		flex: 1;
	}
}
.c-flex {
	display: flex;
	flex-direction: column;
	justify-items: center;
	&-1 {
		flex: 1;
	}
}

.vc-flex {
	display: flex;
	align-items: center;
}
.vr-flex {
	display: flex;
	justify-content: center;
}
.vcr-flex {
	display: flex;
	align-items: center;
	justify-content: center;
}

.ta-c {
	text-align: center;
}
.ta-r {
	text-align: right;
}
.jc-sb{
	justify-content: space-between;
}
.jc-sa{
	justify-content: space-around;
}
.jc-fe{
	justify-content: flex-end
}
.jc-c{
	justify-content: center
}


// 自动生成代码的样式
 .flex-row {
   display: flex;
   flex-direction: row;
 }
 
 .flex-col {
   display: flex;
   flex-direction: column;
 }
 
.justify-start {
  justify-content: flex-start;
}

.justify-end {
  justify-content: flex-end;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-around {
  justify-content: space-around;
}

.justify-evenly {
  justify-content: space-evenly;
}

.items-start {
  align-items: flex-start;
}

.items-end {
  align-items: flex-end;
}

.items-center {
  align-items: center;
}

.items-baseline {
  align-items: baseline;
}

.items-stretch {
  align-items: stretch;
}

.self-start {
  align-self: flex-start;
}

.self-end {
  align-self: flex-end;
}

.self-center {
  align-self: center;
}

.self-baseline {
  align-self: baseline;
}

.self-stretch {
  align-self: stretch;
}

.flex-1 {
  flex: 1 1 0%;
}

.flex-auto {
  flex: 1 1 auto;
}

.grow {
  flex-grow: 1;
}

.grow-0 {
  flex-grow: 0;
}

.shrink {
  flex-shrink: 1;
}

.shrink-0 {
  flex-shrink: 0;
}

.relative {
  position: relative;
}