(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-index-index"],{"5b87":function(t,e,n){"use strict";n.r(e);var i=n("aafc"),o=n("6397");for(var a in o)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(a);n("6c75");var s=n("828b"),c=Object(s["a"])(o["default"],i["b"],i["c"],!1,null,"c8b55cf2",null,!1,i["a"],void 0);e["default"]=c.exports},6397:function(t,e,n){"use strict";n.r(e);var i=n("fdf4"),o=n.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(a);e["default"]=o.a},"6c75":function(t,e,n){"use strict";var i=n("7f55"),o=n.n(i);o.a},"7f55":function(t,e,n){var i=n("f4f7");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var o=n("967d").default;o("365eac28",i,!0,{sourceMap:!1,shadowMode:!1})},aafc:function(t,e,n){"use strict";n.d(e,"b",(function(){return o})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){return i}));var i={uButton:n("66bc").default},o=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{staticClass:"container"},[n("v-uni-view",{staticClass:"flex-row justify-center items-center py-4"},[n("v-uni-text",{staticClass:"text-xl font-bold"},[t._v(t._s(t.$t("pages.index.index.mqtt-info")))])],1),n("v-uni-view",{staticClass:"flex-col px-4 space-y-4"},[n("v-uni-view",{staticClass:"flex-row justify-between items-center border-b pb-2"},[n("v-uni-text",{staticClass:"text-base font-medium"},[t._v(t._s(t.$t("pages.index.index.mqtt-info.label10")))]),n("v-uni-text",{staticClass:"text-base"},[t._v(t._s(t.devCode))])],1),n("v-uni-view",{staticClass:"flex-row justify-between items-center border-b pb-2"},[n("v-uni-text",{staticClass:"text-base font-medium"},[t._v(t._s(t.$t("pages.index.index.mqtt-info.label20")))]),n("v-uni-text",{staticClass:"text-base"},[t._v(t._s(t.mqttConfig.companyId))])],1),n("v-uni-view",{staticClass:"flex-row justify-between items-center border-b pb-2"},[n("v-uni-text",{staticClass:"text-base font-medium"},[t._v(t._s(t.$t("pages.index.index.mqtt-info.label30")))]),n("v-uni-text",{staticClass:"text-base"},[t._v(t._s(t.mqttConfig.ip))])],1),n("v-uni-view",{staticClass:"flex-row justify-between items-center border-b pb-2"},[n("v-uni-text",{staticClass:"text-base font-medium"},[t._v(t._s(t.$t("pages.index.index.mqtt-info.label40")))]),n("v-uni-text",{staticClass:"text-base"},[t._v(t._s(t.mqttConfig.tcpPort))])],1),n("v-uni-view",{staticClass:"flex-row justify-between items-center border-b pb-2"},[n("v-uni-text",{staticClass:"text-base font-medium"},[t._v(t._s(t.$t("pages.index.index.mqtt-info.label50")))]),n("v-uni-text",{staticClass:"text-base"},[t._v(t._s(t.mqttConfig.wss))])],1),n("v-uni-view",{staticClass:"flex-row justify-between items-center border-b pb-2"},[n("v-uni-text",{staticClass:"text-base font-medium"},[t._v(t._s(t.$t("pages.index.index.mqtt-info.label60")))]),n("v-uni-text",{staticClass:"text-base"},[t._v(t._s(t.mqttConfig.account))])],1)],1),n("v-uni-view",{staticClass:"flex-row justify-center items-center py-4"},[n("v-uni-text",{staticClass:"text-xl font-bold"},[t._v(t._s(t.$t("pages.index.index.conn-status")))])],1),n("v-uni-view",{staticClass:"flex-col px-4 space-y-4"},[n("v-uni-view",{staticClass:"flex-row justify-between items-center border-b pb-2"},[n("v-uni-text",{staticClass:"text-base font-medium"},[t._v(t._s(t.$t("pages.index.index.conn-status.label10")))]),n("v-uni-text",{staticClass:"text-base"},[t._v(t._s(t.mqttConnected?t.$t("connected"):t.$t("disconnected")))])],1),n("v-uni-view",{staticClass:"flex-row justify-between items-center border-b pb-2"},[n("v-uni-text",{staticClass:"text-base font-medium"},[t._v(t._s(t.$t("pages.index.index.conn-status.label20")))]),n("v-uni-text",{staticClass:"text-base"},[t._v(t._s(t.devConnected?t.$t("connected"):t.$t("disconnected")))])],1)],1),n("v-uni-view",{staticClass:"flex-row justify-center items-center py-4"},[n("v-uni-text",{staticClass:"text-xl font-bold"},[t._v(t._s(t.$t("pages.index.index.operation")))])],1),t.page>0?n("v-uni-view",{staticClass:"flex-col"},[n("v-uni-view",{staticClass:"flex-row space-x-4 mt-6 px-4"},[n("u-button",{attrs:{type:"primary"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.setMqtt.apply(void 0,arguments)}}},[t._v(t._s(t.$t("pages.index.index.operation.label10")))]),n("u-button",{attrs:{type:"primary"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.setWifi.apply(void 0,arguments)}}},[t._v(t._s(t.$t("pages.index.index.operation.label20")))])],1),n("v-uni-view",{staticClass:"flex-row space-x-4 mt-6 px-4"},[n("u-button",{attrs:{type:"primary",disabled:!t.devConnected},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.next.apply(void 0,arguments)}}},[t._v(t._s(t.$t("pages.index.index.operation.label30")))]),n("u-button",{attrs:{type:"primary",disabled:!t.devConnected},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.to3d.apply(void 0,arguments)}}},[t._v(t._s(t.$t("pages.index.index.operation.label40")))])],1)],1):n("v-uni-view",{staticClass:"flex-row justify-end space-x-4 mt-6 px-4"},[n("u-button",{attrs:{type:"primary",disabled:!t.devConnected},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.next.apply(void 0,arguments)}}},[t._v(t._s(t.$t("pages.index.index.operation.label30")))])],1)],1)},a=[]},f4f7:function(t,e,n){var i=n("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.u-button--primary[data-v-c8b55cf2]{color:#fff;background-color:#01b09a;border-color:#01b09a;border-width:1px;border-style:solid}.container[data-v-c8b55cf2]{padding-top:20px}.py-4[data-v-c8b55cf2]{padding-top:1rem;padding-bottom:1rem}.px-4[data-v-c8b55cf2]{padding-left:1rem;padding-right:1rem}.space-y-4 > * + *[data-v-c8b55cf2]{margin-top:1rem}.border-b[data-v-c8b55cf2]{border-bottom-width:1px;border-bottom-style:solid;border-bottom-color:#e5e7eb}.pb-2[data-v-c8b55cf2]{padding-bottom:.5rem}.text-xl[data-v-c8b55cf2]{font-size:1.25rem;line-height:1.75rem}.text-base[data-v-c8b55cf2]{font-size:1rem;line-height:1.5rem}.font-bold[data-v-c8b55cf2]{font-weight:700}.font-medium[data-v-c8b55cf2]{font-weight:500}.space-x-4 > * + *[data-v-c8b55cf2]{margin-left:1rem}',""]),t.exports=e},fdf4:function(t,e,n){"use strict";n("6a54");var i=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("c223"),n("5c47"),n("af8f"),n("dc8a"),n("5ef2"),n("4626"),n("5ac7"),n("f7a5"),n("c9b5"),n("bf0f"),n("ab80");var o=i(n("2634")),a=i(n("2fdc")),s=i(n("dc2d")),c=n("0578"),d={data:function(){return{devCode:void 0,mqttConnected:!1,devConnected:!1,applicationLocale:"zh-Hans",countdown:100,timer:null,client:void 0,mqttConfigBase64:"",mqttConfig:{},page:0}},onLoad:function(){},onUnload:function(){this.timer&&clearInterval(this.timer),this.disconnect()},created:function(){var t=this;return(0,a.default)((0,o.default)().mark((function e(){return(0,o.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.initUrlData();case 2:0!=t.page&&t.checkMqtt();case 3:case"end":return e.stop()}}),e)})))()},methods:{initUrlData:function(){var t=this.getQueryParams();if(t){if(console.log("param:",t),!t.devCode)return console.log("缺少设备编码"),void uni.showToast({duration:2e3,title:this.$t("validate.error"),icon:"none"});if(this.devCode=t.devCode,this.page=t.page||0,this.applicationLocale=t.lang||"zh-Hans",uni.setLocale(this.applicationLocale),this.$i18n.locale=this.applicationLocale,t.mqttConfig){this.mqttConfigBase64=t.mqttConfig;var e=(0,c.decodeBase64ToJson)(t.mqttConfig),n=e.wss,i=e.ip,o=e.companyId,a=e.tcpPort,s=e.account,d=e.password;if(console.log("param json:",(0,c.decodeBase64ToJson)(t.mqttConfig)),!o||!s||!d||!n)return uni.showToast({duration:2e3,title:this.$t("validate.error"),icon:"none"}),void console.log("缺少必要参数");this.mqttConfig={ip:i||void 0,wss:n||void 0,tcpPort:a||void 0,account:s||void 0,password:d||void 0,companyId:o||void 0},console.log("this.mqttConfig:",this.mqttConfig),uni.removeStorageSync("room_mqttConfig"),uni.setStorageSync("room_mqttConfig",this.mqttConfig),0==this.page&&(console.log("this.page==0"),uni.reLaunch({url:"/pages/room/room?dataSrc=device&devCode=".concat(this.devCode,"&companyId=").concat(this.mqttConfig.companyId,"&mqttConfig=").concat(this.mqttConfigBase64)}))}else uni.showToast({duration:2e3,title:this.$t("validate.error"),icon:"none"})}else uni.showToast({duration:1e3,title:this.$t("validate.error"),icon:"none"})},startCountdown:function(){var t=this;uni.showLoading({title:this.countdown+"s",mask:!0}),this.timer=setInterval((function(){t.countdown--,t.countdown<=0?(clearInterval(t.timer),uni.hideLoading()):uni.showLoading({title:t.countdown+"s",mask:!0})}),1e3)},getQueryParams_old:function(){for(var t={},e=window.location.hash.split("?")[1]||window.location.search.substring(1),n=e.split("&"),i=0;i<n.length;i++){var o=n[i].split("=");t[decodeURIComponent(o[0])]=decodeURIComponent(o[1]||"")}return Object.keys(t).length>0?t:void 0},getQueryParams:function(t){var e={},n=t||window.location.href,i=n.indexOf("#"),o=n.indexOf("?"),a="";if(-1!==i&&(-1===o||i<o)){var s=n.substring(i);a=s.includes("?")?s.split("?")[1]:""}else a=n.substring(o+1,-1!==i?i:void 0);if(a)for(var c=a.split("&"),d=0;d<c.length;d++){var r=c[d].split("=");r.length>=2?e[decodeURIComponent(r[0])]=decodeURIComponent(r.slice(1).join("=")):1===r.length&&(e[decodeURIComponent(r[0])]="")}return Object.keys(e).length>0?e:void 0},next:function(){this.disconnect(),uni.navigateTo({url:"/pages/room/room?dataSrc=device&devCode=".concat(this.devCode,"&companyId=").concat(this.mqttConfig.companyId,"&mqttConfig=").concat(this.mqttConfigBase64)})},setMqtt:function(){this.disconnect(),webUni.navigateTo({url:"/pages/mqtt/mqtt"})},setWifi:function(){this.disconnect(),webUni.navigateTo({url:"/pagesBluetooth/index/index?devCode=".concat(this.devCode)})},to3d:function(){this.disconnect();var t="../3d/index.html?dataSrc=device&topic=".concat(this.devCode,"&lang=").concat(this.applicationLocale,"&companyId=").concat(this.mqttConfig.companyId,"&mqttConfig=").concat(this.mqttConfigBase64);setTimeout((function(){window.location.href=t}),20)},checkMqtt:function(){this.mqttConfig||(uni.showToast({duration:1e3,title:this.$t("validate.error"),icon:"none"}),setTimeout((function(){webUni.navigateTo({url:"/pages/mqtt/mqtt"})}),1e3)),this.connect()},connect:function(){var t=this;try{this.startCountdown();var e=this.mqttConfig.wss;this.client=s.default.connect(e,{username:this.mqttConfig.account,password:this.mqttConfig.password,clientId:"app_"+Math.floor(1e3*Math.random())}),this.client.on("connect",(function(n){console.log("连接成功:"+e),t.mqttConnected=!0;var i="D/"+t.mqttConfig.companyId+"/+/"+t.devCode+"/+/event";t.client.subscribe(i,(function(t){t?(console.log("订阅失败"),uni.hideLoading()):console.log("订阅成功:"+i)}))})),this.client.on("message",(function(e,n){console.log("message:",n),t.devConnected||(uni.hideLoading(),uni.showToast({duration:2e3,title:t.$t("device.online"),icon:"none"})),t.countdown=0,t.devConnected=!0}))}catch(n){uni.showToast({duration:6e3,title:n.toString(),icon:"none"})}},disconnect:function(){this.client.end(!0,(function(){console.log("index MQTT已断开连接")}))}}};e.default=d}}]);