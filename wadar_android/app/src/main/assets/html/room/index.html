<!DOCTYPE html><html lang=en><head><meta charset=UTF-8><link rel=stylesheet href=./static/index.css><script>var coverSupport = 'CSS' in window && typeof CSS.supports === 'function' && (CSS.supports('top: env(a)') ||
				CSS.supports('top: constant(a)'))
			document.write(
				'<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0' +
				(coverSupport ? ', viewport-fit=cover' : '') + '" />')</script><script src=./static/js/vconsole.min.js></script><script src=./static/js/uni.webview.1.5.4.js></script><script>// 待触发 `UniAppJSBridgeReady` 事件后，即可调用 uni 的 API。
		      document.addEventListener('UniAppJSBridgeReady', function() {
		        webUni.postMessage({
		            data: {
		                action: 'message'
		            }
		        });
		        webUni.getEnv(function(res) {
		            console.log('当前环境：' + JSON.stringify(res));
					// alert('当前环境：' + JSON.stringify(res))
		        });
		
		        document.getElementById('postMessage').addEventListener('click', function() {
		          webUni.postMessage({
		            data: {
		              action: 'message'
		            }
		          });
		        });
		      });</script><title></title></head><body><div id=app></div><script src=./static/js/chunk-vendors.0ebb1759.js></script><script src=./static/js/index.9eb6f8f1.js></script></body></html>