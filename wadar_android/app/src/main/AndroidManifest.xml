<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.INTERNET" />

    <uses-permission android:name="android.permission.USE_SIP" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <!-- Android 10及以下 照片和视频 音乐与音频-->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />


    <uses-permission android:name="android.permission.CAMERA"
        tools:ignore="PermissionImpliesUnsupportedChromeOsHardware" />

    <!-- 照片和视频权限 -->
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" /> <!-- Android 13+ -->
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" /> <!-- Android 13+ -->

    <!-- 音乐与音频权限 -->
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" /> <!-- Android 13+ -->

    <!-- Android 14 版本创建mqtt会闪退 -->
    <!-- Android 12 (API 31) 及以上 -->
    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
    <!-- Android 13 (API 33) 及以上（需符合 Google Play 政策） -->
    <uses-permission android:name="android.permission.USE_EXACT_ALARM" />


    <uses-feature
        android:name="android.hardware.bluetooth_le"
        android:required="true" />
    <uses-feature android:name="android.hardware.sip.voip" />
    <uses-feature android:name="android.hardware.wifi" />
    <uses-feature android:name="android.hardware.microphone" />


    <application
        android:name=".app.BlufiApp"
        android:allowBackup="true"
        android:fullBackupContent="@xml/backup_descriptor"
        android:requestLegacyExternalStorage="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:supportsRtl="true"
        android:theme="@style/AppTheme"
        android:usesCleartextTraffic="true"
        tools:ignore="GoogleAppIndexingWarning">
        <activity
            android:name=".ui.MainActivity"
            android:exported="true"
            android:label="@string/device_list_title"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar">
        </activity>
        <activity
            android:name=".ui.HomeActivity"
            android:exported="true"
            android:label="@string/app_name"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name=".ui.BlufiActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".ui.ConfigureOptionsActivity"
            android:exported="false"
            android:label="@string/configure_title"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".ui.SettingsActivity"
            android:exported="false"
            android:label="@string/settings_title"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".ui.MqttConfigActivity"
            android:exported="false"
            android:label="@string/mqtt_config_title"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".ui.VoIPConfigActivity"
            android:exported="false"
            android:label="@string/voip_configuration"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".ui.ScanActivity"
            android:exported="false"
            android:label="@string/settings_title"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".ui.DeviceInfoActivity"
            android:exported="false"
            android:label="@string/device_info_title"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".ui.WebViewActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".ui.BluetoothActivity"
            android:exported="false"
            android:label="@string/bluetooth_title"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />

        <service android:name="org.eclipse.paho.android.service.MqttService" />
    </application>

</manifest>