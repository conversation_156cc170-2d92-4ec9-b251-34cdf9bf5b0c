apply plugin: 'com.android.application'

android {
    compileSdk 33

    defaultConfig {
        applicationId "wvr.wadar.app"
        minSdk 23
        targetSdk 33
        versionCode 29
        versionName "1.6.3"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }
    buildFeatures {
        viewBinding true
        dataBinding true
        buildConfig true
    }
//    sourceSets {
//        main {
//            jniLibs.srcDirs = ['src/main/jniLibs']
//        }
//    }
    namespace 'wvr.wadar'
}

dependencies {
    implementation fileTree(include: ['*.jar'], dir: 'libs')

    implementation 'com.google.android.material:material:1.6.1'
    implementation 'androidx.appcompat:appcompat:1.4.2'
    implementation 'androidx.gridlayout:gridlayout:1.0.0'
    implementation 'androidx.preference:preference:1.2.0'

    implementation 'io.reactivex.rxjava3:rxjava:3.1.4'
    implementation 'io.reactivex.rxjava3:rxandroid:3.0.0'
    // 增加GSON依赖
    implementation 'com.google.code.gson:gson:2.9.0'
    // 扫码依赖
    implementation 'com.journeyapps:zxing-android-embedded:4.3.0'
    // commons-lang3
    implementation 'org.apache.commons:commons-lang3:3.14.0'
    // pjsip
//    implementation 'org.pjsip:pjsip-android:2.11'

     //implementation(name: 'linphone-sdk-android-4.3.0-beta', ext: 'aar')
     //implementation(name: 'linphone-sdk-android-5.4.6', ext: 'aar')
     //implementation 'androidx.media:media:1.4.0'

    // mqtt
    implementation 'org.eclipse.paho:org.eclipse.paho.client.mqttv3:1.2.5'
    // 原版停止更新，不兼容30以上版本,修改源码
     implementation 'org.eclipse.paho:org.eclipse.paho.android.service:1.1.1'
//     implementation 'io.github.mayzs:paho.mqtt.android:1.2.1'


    // lombok
    implementation 'org.projectlombok:lombok:1.18.28'

    // 引入androidx.lifecycle包后导致kotlin相关库版本报错
    // 排除协程库的旧版本 Kotlin 依赖
    implementation('org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3') {
        exclude group: 'org.jetbrains.kotlin', module: 'kotlin-stdlib-jdk8'
    }
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3'

    implementation project(":lib-blufi")
    implementation project(":LinphoneLibrary")
}

configurations.all {
    resolutionStrategy {
        // 强制 Kotlin 标准库版本
        force 'org.jetbrains.kotlin:kotlin-stdlib:1.8.10'
        force 'org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.10'
        force 'org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.10'

        // 强制协程库的 Kotlin 版本
        force 'org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.3' // 升级到兼容 Kotlin 1.8.10 的版本
        force 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3'
    }
}

