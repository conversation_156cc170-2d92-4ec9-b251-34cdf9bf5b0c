<script>
	// #ifdef (H5 || H5-HEJIA)
	(function() {
		if (typeof WeixinJSBridge == "object" && typeof WeixinJSBridge.invoke == "function") { //判断程序运行环境是否是微信浏览器(微信内置的浏览器)
			handleFontSize();
		} else {
		if (document.addEventListener) {
			document.addEventListener("WeixinJSBridgeReady", handleFontSize, false);
		} else if (document.attachEvent) {
			document.attachEvent("WeixinJSBridgeReady", handleFontSize);
			document.attachEvent("onWeixinJSBridgeReady", handleFontSize);
		}
	}
	function handleFontSize() {
		// 设置网页字体为默认大小
		WeixinJSBridge.invoke('setFontSizeCallback', {
			'fontSize': 0
		});
		// 重写设置网页字体大小的事件
		WeixinJSBridge.on('menu:setfont', function() {
			WeixinJSBridge.invoke('setFontSizeCallback', {
				'fontSize': 0
			});
		});
	}
	})();
	// #endif
	var windowHeight;
	
	var timeout = 30000; // 30s
	var timeoutObj = null;
	export default {
		// 此处globalData为了演示其作用，不是uView框架的一部分
		globalData: {
			username: '白居易',
			roomData:{}
		},
		onLaunch(option) {
			console.log('app onLaunch uni.getSystemInfoSync()', uni.getSystemInfoSync())
			// this.autoUpdate();
			// 1.1.0版本之前关于http拦截器代码，已平滑移动到/common/http.interceptor.js中
			// 注意，需要在/main.js中实例化Vue之后引入如下(详见文档说明)：
			// import httpInterceptor from '@/common/http.interceptor.js'
			// Vue.use(httpInterceptor, app)
			// process.env.VUE_APP_PLATFORM 为通过js判断平台名称的方法，结果分别如下：
			/**
			 * h5，app-plus(nvue下也为app-plus)，mp-weixin，mp-alipay......
			 */
			if (option.path && option.path.indexOf('supplement') !== -1) {
				//#ifdef MP-WEIXIN
				wx.hideHomeButton()
				//#endif
			} else {
				// #ifndef H5
				let _token = uni.getStorageSync('token')
				if (!_token && (!location || location.pathname !== '/')) {
					// #ifndef H5
					uni.reLaunch({
						url: '/pages/auth/login/index'
					})
					// #endif
				}
				// #endif
			}
			// #ifdef H5
			let _pageMapping = {};
			uni.getSystemInfo({
				success: (res) => {
					if (!windowHeight) {
						windowHeight = res.windowHeight + res.windowTop + 'px';
					}
					let _page = document.querySelector('uni-page').getAttribute("data-page");
					if (!_pageMapping[_page]) {
						_pageMapping[_page] = windowHeight;
					}
					document.querySelector('uni-page').style.height = _pageMapping[_page] || (res.safeArea.height + 'px');
				}
			});
			setInterval(() => {
				// if (!windowHeight) {
				// 	windowHeight = uni.getSystemInfoSync().windowHeight + uni.upx2px(82) + 'px';
				// 	console.log('fetch window height value.', windowHeight)
				// }
				// let _currHeight = document.querySelector('uni-page').style.height;
				// if (_currHeight !== windowHeight) {
				// 	document.querySelector('uni-page').style.height = windowHeight;
				// 	console.log('set window height value.')
				// }
				uni.getSystemInfo({
					success: (res) => {
						if (!windowHeight) {
							windowHeight = res.windowHeight + res.windowTop;
							console.log('fetch window height value.', windowHeight)
						}
						let _page = document.querySelector('uni-page').getAttribute("data-page");
						// let _wxBottomHeight = 0;
						// let ua = window.navigator.userAgent.toLowerCase()
						// if (ua.match(/MicroMessenger/i) == 'micromessenger' && _pageMapping[_page] && uni.getSystemInfoSync().platform) {
						// 	if (+(_pageMapping[_page].replace('px', '')) - (res.windowHeight + res.windowTop) === 87) {
						// 		_wxBottomHeight = 87;
						// 	}
						// }
						// let _divHeight = document.querySelector('uni-page').getAttribute("data-height");
						// let _currHeight = document.querySelector('uni-page').style.offsetHeight;
						// console.log('_currHeight', _divHeight)
						// console.log('_currHeight', document.querySelector('uni-page').style)
						// if (!_divHeight) {
						// 	document.querySelector('uni-page').style.height = res.safeArea.height;
						// 	document.querySelector('uni-page').setAttribute("data-height", res.safeArea.height);
						// 	_pageMapping[_page] = windowHeight;
						// 	console.log('set window height value.', windowHeight)
						// }
						if (!_pageMapping[_page]) {
							_pageMapping[_page] = windowHeight;
						}
						// _pageMapping['pagesDevice/index'] = '748px'
						// _pageMapping['pagesDevice/notActive'] = '748px'
						// if (_pageMapping[_page] && _wxBottomHeight) {
						// console.log('_wxBottomHeight', _pageMapping[_page], _wxBottomHeight);
						// 	_pageMapping[_page] = _pageMapping[_page] - _wxBottomHeight
						// }
						// console.log('_wxBottomHeight', _page, _wxBottomHeight);
						// console.log(_pageMapping);
						
						let ua = window.navigator.userAgent.toLowerCase()
						if (ua.match(/MicroMessenger/i) == 'micromessenger' && uni.getSystemInfoSync().platform === 'ios') {
						} else {
							document.querySelector('uni-page').style.height = (_pageMapping[_page] || res.safeArea.height + 'px');
						}
					}
				});
			}, 3000)
			// #endif
		},
		onShow(options) {
			console.log("app show:",options)
			this.autoUpdate();
			const referrerInfo = options.referrerInfo;
			const data = referrerInfo?.extraData?.data;
			console.log("room data:",data)
			// getApp().globalData.roomData = data;
			if(data && this.globalData){
				this.globalData.roomData = data;
			}
			// this.checkOpenSocket();
		},
		onHide() {
			// uni.closeSocket();
		},
		methods: {
			openConnection() {
				// 打开连接
				// uni.closeSocket(); // 确保已经关闭后再重新打开
				uni.connectSocket({
					url: `${this.$u.http.config.wsBaseUrl}/pinanbao/member/ws?token=${uni.getStorageSync('token')}`,
					success(res) {
						console.log('连接成功 connectSocket=', res);
					},
					fail(err) {
						console.log('连接失败 connectSocket=', err);
					}
				});
				uni.onSocketOpen(res => {
					console.log('连接成功');
				});
				this.onSocketMessage(); // 打开成功监听服务器返回的消息
			},
			checkOpenSocket() {
				uni.sendSocketMessage({
					data: 'ping',
					success: res => {
						return;
					},
					fail: err => {
						console.log(err)
						// 未连接打开websocket连接
						this.openConnection();
					}
				});
			},
			// 打开成功监听服务器返回的消息
			onSocketMessage() {
				// 消息
				timeout = 30000;
				timeoutObj = null;
				uni.onSocketMessage(res => {
					console.log(res)
					this.getSocketMsg(res.data); // 监听到有新服务器消息
				});
			},
			// 监听到有新服务器消息
			getSocketMsg(resData) {
				// 监听到服务器消息
				uni.showModal({
					title: '体征异常',
					content: resData || '发生异常情况, 请关注',
					confirmText: '我知道了',
					showCancel: false,
				})
				// this.reset();  // 检测心跳reset,防止长时间连接导致连接关闭
			},
			// 检测心跳reset
			reset() {
				clearInterval(timeoutObj);
				this.start(); // 启动心跳
			},
			// 启动心跳 start
			start() {
				timeoutObj = setInterval(function() {
					uni.sendSocketMessage({
						data: 'ping',
						success: res => {
							console.log('连接中....');
						},
						fail: err => {
							console.log('连接失败重新连接....');
							this.openConnection();
						}
					});
				}, this.timeout);
			},
			// 强制用户更新小程序
			autoUpdate() {
				console.log("检查新版本");
				const updateManager = uni.getUpdateManager() // 小程序版本更新管理器
				updateManager.onCheckForUpdate(res => { // 检测新版本后的回调
					if(res.hasUpdate) { // 如果有新版本提醒并进行强制升级
						console.log("有新版本");
						uni.showModal({
							content: '新版本已经准备好，是否重启小程序？',
							showCancel: false,
							confirmText: '确定',
							success: res => {
								if (res.confirm) {
									updateManager.onUpdateReady(res => { // 新版本下载完成的回调
										console.log("重启后使用新版本");
										updateManager.applyUpdate() // 强制当前小程序应用上新版本并重启
									})
				
									updateManager.onUpdateFailed(res => { // 新版本下载失败的回调
										// 新版本下载失败，提示用户删除后通过冷启动重新打开
										console.log("新版本下载失败");
										uni.showModal({
											content: '下载失败，请删除当前小程序后重新打开',
											showCancel: false,
											confirmText: '知道了'
										})
									})
								}
							}
						})
					}
				}) 
				console.log("检查新版本完成");
			},
		}
		// onShow() {
		// 		document.querySelector('uni-page').style.height = uni.getSystemInfoSync().windowHeight + 'px';
		// 		console.log("uni.getSystemInfoSync().windowHeight + 'px'", uni.getSystemInfoSync().windowHeight + 'px');
		// }
	}
</script>

<style lang="scss">
	@import "uview-ui/index.scss";
	@import "common/demo.scss";
	uni-rich-text img {
		max-width: 100% !important;
	}
	
	//#ifdef H5
	html, body {
		overflow: hidden;
	}
	uni-page {
		// height: calc(100vh);
		overflow: hidden;
	}
	uni-page-body {
		height: 100%;
		overflow: auto;
	}
	//#endif
</style>
