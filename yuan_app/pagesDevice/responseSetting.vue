<template>
	<view class="device-response-setting-page">
		<view class="top-header-line"></view>
		<view class="tip">请设置紧急联系人，当设备告警，与安系统将第一时间为您联系您的紧急联系人。</view>
		<view>
			<view class="czx">
			<u-field
				v-model="form.fallResponseTime"
				style="background-color:lightgray"
				type="number"
				label="摔倒响应时间"
				placeholder="请输入"
				disabled
				required
			>
				<span slot="right" style="color: #5E5D5D;">分</span>
			</u-field>
			</view>
			<u-field
				v-model="form.longlagResponseTime"
				type="number"
				label="久滞响应时间"
				placeholder="请输入"
				required
			>
				<span slot="right" style="color: #5E5D5D;">分</span>
			</u-field>
			<!-- <u-field
				v-model="form.fallingbedResponseTime"
				type="number"
				label="坠床响应时间"
				placeholder="请输入"
				required
			>
				<span slot="right" style="color: #5E5D5D;">分</span>
			</u-field> -->
		</view>
		
		<view v-if="dev.selfBind === true" class="footer-btns">
			<!-- <u-button v-if="checked === true" type="primary" shape="circle" class="scan-code-btn" @click="scan">下一步</u-button> -->
			<!-- <u-button type="primary" shape="circle" class="next-btn" @click="handleSave">保存</u-button>
			<u-button v-if="form.id" type="primary" shape="circle" class="next-btn" plain @click="handleOpenDeleteDialog">删除</u-button> -->
			
			<u-button shape="circle" class="next-btn diy-btn" size="medium" 
			:custom-style="{ 'width': '100%', 'color': 'white', 'background-color': '#01B09A' }"  hover-class="none"
			@click="handleSave">保存</u-button>
			<u-button v-if="form.id" shape="circle" class="next-btn diy-btn" size="medium" 
			:custom-style="{ 'width': '100%', 'color': '#333', 'background-color': '#f7f7f7' }"  hover-class="none"
			@click="handleOpenDeleteDialog">删除</u-button>
		</view>
		
		<!-- <u-modal v-model="deleteDialog" title="您确定要删除配置信息？" :title-style="{color: '#ff1f1f'}" :show-cancel-button="true" @confirm="handleConfirmDelete">
			<view class="slot-content" style="padding: 20rpx; text-align: center; color: #999; font-size: 26rpx;">
				是否确认删除被监护人
			</view>
		</u-modal> -->
	</view>
</template>

<script>
	export default {
		data() {
			return {
				form: {
					fallResponseTime: 3,
					longlagResponseTime: 10,
					fallingbedResponseTime: 1,
				},
				dev: {}
			}
		},
		onLoad(option) {
			this.fetchDeviceInfo();
		},
		methods: {
			fetchDeviceInfo() {
				uni.showLoading({
					title: '加载中...',
					mask: true
				})
				this.$u.api.fetchMyDeviceInfo({ devId: uni.getStorageSync('devId') }).then(res => {
					if (res) {
						this.dev = res;
						this.form = { 
							//TODO 设备只支持3，所有的都显示为3
							fallResponseTime: 3,
							// fallResponseTime: res.fallResponseTime,
							longlagResponseTime: res.longlagResponseTime,
							fallingbedResponseTime: res.fallingbedResponseTime
						}
					}
				}).catch(err => {
					this.form = {}
				}).finally(() => {
					setTimeout(() => {
						uni.hideLoading();
					}, 1000)
				})
			},
			isNumber(n) {
				return !isNaN(parseFloat(n)) && isFinite(n);
			},
			handleSave() {
				if ((!this.form.fallResponseTime && (isNaN(+this.form.fallResponseTime) || this.form.fallResponseTime === '')) 
					|| !this.form.longlagResponseTime 
					|| (!this.form.fallingbedResponseTime && (isNaN(+this.form.fallingbedResponseTime) || this.form.fallingbedResponseTime === ''))) {
					uni.showModal({ content: '请检查“*”必填项是否填写正确', showCancel: false, confirmText: '关闭' })
					return;
				}
				if (!this.isNumber(this.form.fallResponseTime) || !this.isNumber(this.form.longlagResponseTime) || !this.isNumber(this.form.fallingbedResponseTime)) {
					uni.showModal({ content: '请检查填写内容是合法数字', showCancel: false, confirmText: '关闭' })
					return;
				}
				// if (parseInt(this.form.longlagResponseTime) <= 0) {
				// 	uni.showModal({ content: '请检查填写内容是否大于0', showCancel: false, confirmText: '关闭' })
				// 	return;
				// }
				if (parseInt(this.form.longlagResponseTime) < 10 || parseInt(this.form.longlagResponseTime) % 10 !== 0) {
					uni.showModal({ content: '久滞响应时间格式为: 10的倍数', showCancel: false, confirmText: '关闭' })
					return;
				}
				// if (parseInt(this.form.longlagResponseTime) != 10 && parseInt(this.form.longlagResponseTime) !=20) {
				// 	uni.showModal({ content: '久滞响应时间当前支持10或20分钟，其他时间我们正在拼命开发中', showCancel: false, confirmText: '关闭' })
				// 	return;
				// }
				
				let _params = {
					id: uni.getStorageSync('devId'),
					...this.form,
					removeLoading: true,
				}
				uni.showLoading({
					title: '保存中...',
					mask: true
				})
				this.$u.api.execUpdateMyDevice(_params).then(res => {
					uni.hideLoading();
					uni.showToast({ duration: 2000, title: '设置设备响应时间成功', icon: 'none' })
					setTimeout(() => {
						uni.navigateBack({
							delta: 1
						})
					}, 1000);
				}).catch(err => {
					uni.hideLoading();
					console.log('err', err)
					uni.showToast({ duration: 2000, title: err.message || '发生未知错误', icon: 'none' })
				}).finally(() => {
				})
			},
		}
	}
</script>

<style lang="scss">
::v-deep .u-round-circle {
	&::after {
		border: none !important;
	}
}
</style>
<style lang="scss" scoped>
	.czx:first-child{
		background-color: lightgray!important;
	}	
.device-response-setting-page {
	padding: 29rpx;
	.tip {
		color: #8B8B8B;
		margin-bottom: 20px;
	}
	.icon-reqired {
		color: red;
		display: inline-block;
		vertical-align: middle;
		font-size: 40rpx;
		position: relative;
		top: 4rpx;
		left: 6rpx;
	}
	::v-deep .u-label {
		font-size: 32rpx;
		width: 250rpx;
		display: block;
		color: #0D0D0D;
		flex: initial !important;
		&::before {
			font-size: 40rpx;
			top: 10rpx;
		}
		.u-label-text {
			padding-left: 20rpx;
			font-weight: bold;
			box-sizing: border-box;
		}
	}
	.direction-field {
		::v-deep .u-field-inner {
			height: 120rpx;
			align-items: baseline;
		}
	}
	.footer-btns {
		position: absolute;
		bottom: 40rpx;
		left: 40rpx;
		right: 40rpx;
		margin: 0 auto;
		.next-btn {
			display: block;
			margin-top: 20rpx;
		}
	}
	// ::v-deep .uni-easyinput__content {
	// 	padding: 14rpx 6rpx;
	// }
	::v-deep .u-model__footer__button {
		height: 88rpx;
		line-height: 88rpx;
	}
	::v-deep .u-field {
		padding: 28rpx;
	}
}
</style>
