<template>
	<view>
		<web-view v-if="roomUrl" :webview-styles="webviewStyles" :src="roomUrl" @message="reciveMessage"></web-view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				cmccUrl: this.$u.http.config.cmccUrl,
				staticBaseUrl: this.$u.http.config.staticBaseUrl,
				roomUrl: undefined,
				webviewStyles: {
					progress: {
						color: '#FF3333'
					}
				}
			}
		},
		onLoad: function(option) {
			const {voipNumber,token,memberId} = option;
			// let url = "https://web.sdk.qcloud.com/trtc/webrtc/demo/detect/index.html"
			let url = this.cmccUrl+"/pagesDevice/call/call?voipNumber="+voipNumber+"&token="+token;
			// let url = this.staticBaseUrl+"/call2";
			this.roomUrl = url;
			console.log("进入webview", this.roomUrl)
		},
		methods: {
			reciveMessage(data) {
				// uni.showToast({
				// 	title: "reciveMessage接收到消息：" + JSON.stringify(data.detail),
				// 	duration: 2000,
				// 	icon: 'none'
				// });
				console.log("接收到消息：" + JSON.stringify(data.detail));
			}
		}
	}
</script>

<style>
</style>
