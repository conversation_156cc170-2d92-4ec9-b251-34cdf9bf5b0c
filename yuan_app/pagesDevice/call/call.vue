<template>
	<view class="container">
		<view v-if="calling" class="timer">呼叫中...</view>
		<view v-if="timer>0" class="timer">通话时长：{{ formatTimer }}</view>
		<view class="media">
			<view class="top-box">
				<audio autoplay ref="remoteAudio" id="remoteAudio"></audio>
				<!-- <audio ref="remoteAudio" id="remoteAudio"></audio> -->
			</view>
		</view>
		<view class="btn">
			<text v-if="canCall" class="icon iconfont icon-dadianhua dianhua" style="color: forestgreen;"
				@click="startCall"></text>
			<text v-else class="icon iconfont icon-guadianhua dianhua" style="color: red;" @click="hangUpCall"></text>
		</view>
	</view>

</template>

<script>
	import * as util from '@/utils/util';
	import {
		UserAgent,
		Registerer,
		RegistererState,
		Inviter,
		SessionState
	} from '../common/sip-0.21.2.min.js'

	export default {
		name: "Call",
		data() {
			return {
				memberId:null,
				deviceVoipNumber: null,
				domain: 'fs.rfcare.cn',
				port: '7443',
				userNumber: null,
				userPassword: null,
				server: 'wss://fs.rfcare.cn:7443',
				currentSession: null,
				inComingNumber: null,
				isRegistered: false,
				isConnected: false, // 是否接通
				userAgent: null,
				registerer: null,
				audio: null, // 音频
				myHangup: false, // 是否我方挂断
				// 能打电话
				canCall: true,
				// 能挂电话
				canHangup: false,
				calling:false,
				interval: undefined,
				formatTimer: undefined,
				timer: 0,
			}
		},
		async onLoad(option) {
			console.log("option:", option);
			// #ifdef H5
			uni.hideTabBar()
			// #endif
			const {
				voipNumber,
				token
			} = option;
			
			if (token) {
				uni.setStorageSync('token', token);
			}
			if (!voipNumber) {
				console.log('创建URI失败')
				uni.showToast({
					duration: 2000,
					title: '缺少必要参数',
					icon: 'none'
				})
				return;
			}
			this.deviceVoipNumber = voipNumber;
			// 获取登录用户信息
			await this.getMemberDetailInfo();
			this.handleRegister()
		},
		beforeDestroy() {
			this.handleUnRegister()
		},
		mounted() {
			console.log("mounted");
			// try{
			//     this.audio = document.getElementById("remoteAudio");
			// 	console.log("this.audio:",this.audio)
			// 	console.log("this.audio.srcObject:",this.audio.srcObject)
			// }catch(e){
			// 	console.log("mounted:",e)
			// }
		},
		methods: {
			async getMemberDetailInfo(){
				let member;
				// 非小程序
				//#ifndef MP-WEIXIN
				member = await this.$u.api.fetchMemberDetailInfo({});
				uni.setStorageSync( 'member', member);
				//#endif
				
				// 小程序
				//#ifdef MP-WEIXIN
				member = uni.getStorageSync('member');
				//#endif
				
				if(member && member.voipNumber && member.voipPassword){
					this.userNumber = member.voipNumber;
					this.userPassword = member.voipPassword;
				}else{
					uni.showToast({
						duration: 2000,
						title: '用户信息错误',
						icon: 'none'
					})
				}
			},
			handleRegister() {
				console.log("handleRegister");
				let url = `sip:${this.userNumber}@${this.domain}:${this.port}`;
				console.log(url);
				const uri = UserAgent.makeURI(url)
				if (!uri) {
					console.log('创建URI失败')
				}
				const transportOptions = {
					server: this.server,
					traceSip: false,
				}
				const userAgentOptions = {
					authorizationUsername: this.userNumber,
					authorizationPassword: this.userPassword,
					displayName: this.userNumber,
					transportOptions,
					uri,
					delegate: {
						onInvite
					}
				}
				let userAgent = new UserAgent(userAgentOptions)
				this.userAgent = userAgent;
				this.registerer = new Registerer(userAgent)
				userAgent.start().then(() => {
					this.registerer.register();
				});
				let that = this;
				this.registerer.stateChange.addListener((newState) => {
					switch (newState) {
						case RegistererState.Unregistered:
							console.log('退出登录')
							break;
						case RegistererState.Registered:
							that.isRegistered = true;
							that.canCall = true;
							break;
						case RegistererState.Initial:
							console.log('语音用户登录Initial')
							break;
						case RegistererState.Terminated:
							console.log('语音用户登录Terminated')
							break;
					}
				})


				function onInvite(invitation) {
					that.currentSession = invitation
					that.inComingNumber = invitation.remoteIdentity.uri.user
					invitation.stateChange.addListener((state) => {
						that.sessionStateEvent(state, invitation)
					})
				}
			},

			sessionStateEvent(state, session) {
				switch (state) {
					case SessionState.Initial:
						console.log("拨号初始化");
						break;
					case SessionState.Establishing:
						console.log("通话建立中");
						this.calling = true;
						break;
					case SessionState.Established:
						console.log("已建立通话");
						this.calling = false;
						this.isConnected = true;
						this.canHangup = true;
						this.setupRemoteMedia(session);
						this.interval = setInterval(() => {
							this.timer++;
							this.formatTimer = this.formatTime(this.timer);
						}, 1000);
						break;
					case SessionState.Terminating:
						console.log("终止中");
						// fall through
					case SessionState.Terminated:
						console.log("已结束");
						this.endedHandle();
						break;
					default:
						throw new Error("Unknown session state.");
				}
			},
			// h5中可以，uniapp中不行
			setupRemoteMediaxxx(session) {
				const sessionDescriptionHandler = session.sessionDescriptionHandler;
				console.log("设置远程媒体");
				sessionDescriptionHandler.peerConnection.getReceivers().forEach(receiver => {
					console.log(receiver)
					console.log(receiver.track.kind)
					if (receiver.track.kind === 'audio') {
						this.audio.srcObject = new MediaStream([receiver.track]);
					}
				});
			},
			setupRemoteMedia(session) {
				const sessionDescriptionHandler = session.sessionDescriptionHandler;
				console.log("设置远程媒体");
				const remoteStream = new MediaStream()
				sessionDescriptionHandler.peerConnection.getReceivers().forEach(receiver => {
					console.log(receiver)
					console.log(receiver.track.kind)
					if (receiver.track.kind === 'audio') {
						remoteStream.addTrack(receiver.track)
						this.$refs.remoteAudio.$refs.audio.srcObject = remoteStream;
						this.$refs.remoteAudio.$refs.audio.play();
					}
				});
			},
			endedHandle() {
				this.clearMedia("audio");
				// if (this.myHangup) {
				uni.showToast({
					duration: 2000,
					title: '通话结束',
					icon: 'none'
				})
				// } else {
				// 	uni.showToast({
				// 		duration: 2000,
				// 		title: '对方已挂断!',
				// 		icon: 'none'
				// 	})
				// }
				this.myHangup = false;
				this.canHangup = false;
				this.canCall = true;
				this.currentSession = null;
				clearInterval(this.interval);
			},

			startCall(isVideo = false) {
				this.timer = 0;
				if (this.userAgent) {
					try {
						const target = UserAgent.makeURI(`sip:${this.deviceVoipNumber}@${this.domain}`);
						if (!target) {
							throw new Error("Failed to create target URI.");
						}

						console.log("this.userAgent.call");
						this.inviter = new Inviter(this.userAgent, target, {
							sessionDescriptionHandlerOptions: {
								constraints: {
									audio: true,
									video: false
								}
							}
						});
						this.currentSession = this.inviter;
						this.canCall = false;
						this.inviter.invite({
							requestDelegate: {
								onReject: (resp) => {
									console.log(resp, 'inviter-onReject')
									if (resp.statusCode == 500) {
										console.log('对方不在线')
									} else {
										console.log('对方拒接了')
									}
								},
								onAccept: (resp) => {
									console.log(resp, 'inviter-onAccept')
								},
								onProgress: (resp) => {
									console.log(resp, 'inviter-onProgress')
								},
								onRedirect: (resp) => {
									console.log(resp, 'inviter-onRedirect')
								},
								onTrying: (resp) => {
									console.log(resp, 'inviter-onTrying')
								},

							}
						})
						this.inviter.stateChange.addListener((state) => {
							console.log(`Session state changed to ${state}`);
							this.sessionStateEvent(state, this.inviter);
						});
					} catch (error) {
						uni.showToast({
							duration: 2000,
							title: '呼叫失败!',
							icon: 'none'
						})
						console.error("呼叫失败：", error);
					}
				} else {
					uni.showToast({
						duration: 2000,
						title: '用户代理未初始化!',
						icon: 'none'
					})
				}
			},
			hangUpCall() {
				this.myHangup = true;
				this.canHangup = false;
				this.calling = false;
				const session = this.currentSession
				switch (session.state) {
					case SessionState.Initial:
					case SessionState.Establishing:
						if(this.timer>0){
							 session.reject();
						}else{
							session.cancel();
						}
						// if (session instanceOf Inviter) {
						//   // An unestablished outgoing session
						//   session.cancel();
						// } else {
						//   // An unestablished incoming session
						//   session.reject();
						// }
						this.endedHandle();
						break;
					case SessionState.Established:
						// An established session
						session.bye();
						this.endedHandle();
						break;
					case SessionState.Terminating:
					case SessionState.Terminated:
						// Cannot terminate a session that is already terminated
						break;
					default:
						session.cancel();
						this.endedHandle();
				}
				this.currentSession = null;
			},
			clearMediaxxx(mediaNameOrStream) {
				let mediaSrcObject = this[mediaNameOrStream].srcObject;
				if (mediaSrcObject) {
					let tracks = mediaSrcObject.getTracks();
					for (let i = 0; i < tracks.length; i++) {
						tracks[i].stop();
					}
				}
				this[mediaNameOrStream].srcObject = null;
			},
			clearMedia(mediaNameOrStream) {
				this.$refs.remoteAudio.$refs.audio.srcObject = null
				this.$refs.remoteAudio.$refs.audio.pause()
			},
			handleUnRegister() {
				if (this.userAgent) {
					this.isRegistered = false
					registerer.unregister()
				}
			},
			formatTime(seconds) {
				let hours = Math.floor(seconds / 3600);
				let minutes = Math.floor((seconds % 3600) / 60);
				let remainingSeconds = seconds % 60;
				return `${hours}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
			},
		}
	}
</script>

<style lang="scss">
	page {
		background: #f7f7f7;
	}

	// #ifdef H5
	page {
		height: 100%;
	}

	// #endif
</style>
<style lang="scss" scoped>
	@import url("/static/css/iconfont.css");

	.container {
		width: 100vw;
		height: 100vh;
		overflow: hidden;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		align-items: center;
		// background: linear-gradient(to bottom right, #4B79A1, #282E54);
		background: linear-gradient(to bottom right, #0C164D, #0C354B, #282E54, #181818);
	}

	.timer {
		font-size: 24px;
		color: azure;
		margin-top: 20px;
	}

	.media {}

	.btn {
		width: 100vw;
		display: flex;
		justify-content: space-around;
		align-items: center;
		margin-bottom: 100rpx;

		.dianhua {
			font-size: 128rpx;
		}
	}
</style>