<template>
	<view class="device-station-info-page">
		<view class="top-header-line"></view>
		
		<view class="top-title">服务站简介</view>
		<view class="station-desc">{{ station.introduction || '暂未设置服务站简介' }}</view>
		
		<view class="info-row">
			<image src="../static/images/device/icon-address.png" />
			<view class="field-name">服务站地址</view>
			<view class="field-value">{{ station.addr || '暂未设置服务地址信息' }}</view>
		</view>
		<view class="info-row">
			<image src="../static/images/device/icon-phone.png" />
			<view class="field-name">服务站电话</view>
			<view class="field-value">{{ station.phone || '' }}</view>
		</view>
		
	</view>
</template>

<script>
	export default {
		data() {
			return {
				station: {
					
				}
			}
		},
		onLoad(option) {
			this.id = option.id;
			if (option.id) {
				this.fetchDetailInfo(option.id)
			}
		},
		methods: {
			fetchDetailInfo(id) {
				uni.showLoading({
					title: '加载中...',
					mask: true
				})
				this.$u.api.fetchMyDevStationInfo({ stationId: id }).then(res => {
					if (res) {
						this.station = res
					}
				}).catch(err => {
					this.station = {}
				}).finally(() => {
					setTimeout(() => {
						uni.hideLoading();
					}, 1000)
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
.device-station-info-page {
	padding: 29rpx;
	.top-title {
		font-size: 36rpx;
		color: #333;
		margin-top: 10rpx;
		margin-bottom: 24rpx;
	}
	.station-desc {
		color: #5E5D5D;
		font-size: 30rpx;
		margin-top: 10rpx;
		margin-bottom: 40rpx;
		// text-indent: 50rpx;
		line-height: 48rpx;
	}
	.info-row {
		margin-top: 30rpx;
		font-size: 32rpx;
		image {
			width: 36rpx;
			height: 36rpx;
			vertical-align: middle;
			margin-right: 16rpx;
			position: relative;
			top: -2rpx;
		}
		.field-name {
			display: inline-block;
			font-weight: bold;
			color: #454444;
		}
		.field-value {
			// display: inline-block;
			color: #5E5D5D;
			margin-top: 16rpx;
		}
	}
}
</style>
