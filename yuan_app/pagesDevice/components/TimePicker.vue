<!-- src/components/TimePicker.vue -->

<template>
	<view>
		<slot></slot>

		<!-- 时间选择器 -->
		<u-select mode="mutil-column" v-model="timePickerShow" :list="timeColumns" @confirm="onConfirm"
			@cancel="onCancel"></u-select>
	</view>
</template>

<script>
	export default {
		name: 'TimePicker',
		props: {
			hourMin:{
				type:Number,
				default:0,
			},
			hourMax:{
				type:Number,
				default:23,
			},
			minuteMin:{
				type:Number,
				default:0,
			},
			minuteMax:{
				type:Number,
				default:59,
			},
		},
		data() {
			return {
				timePickerShow: false,
				timeColumns: []
			};
		},
		methods: {
			initializeTimeData() {
				const hours = [];
				const minutes = [];

				for (let i = this.hourMin; i <= this.hourMax; i++) {
					hours.push({
						value: i < 10 ? '0' + i : '' + i,
						label: i < 10 ? '0' + i : '' + i
					});
				}
				for (let i = this.minuteMin; i <= this.minuteMax;) {
					minutes.push({
						value: i < 10 ? '0' + i : '' + i,
						label: i < 10 ? '0' + i : '' + i
					});
					i=i+5;
				}

				return {
					hours,
					minutes
				};
			},
			openTimePicker() {
				const {
					hours,
					minutes
				} = this.initializeTimeData();
				this.timePickerShow = true;
				this.timeColumns = [
					hours,
					minutes
				];
				console.log('timeColumns:', this.timeColumns);
			},
			onConfirm(e) {
				const hour = e[0]?.value || '00';
				const minute = e[1]?.value || '00';

				const selectedTime = `${hour}:${minute}`;
				console.log("selectedTime:" + selectedTime);
				this.$emit('input', selectedTime); // 更新 v-model
				this.$emit('change', selectedTime); // 发出 change 事件
				this.timePickerShow = false;
			},
			onCancel() {
				this.timePickerShow = false;
			}
		}
	};
</script>

<style scoped>
	/* 添加一些样式 */
</style>