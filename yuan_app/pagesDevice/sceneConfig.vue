<template>
	<view class="device-tag-config-page">
		<view class="top-header-line"></view>
		
		<view class="item-block">
			<view class="sub-title" @click="showEditForm">
				设备名称：{{deviceInfo.devName || deviceInfo.devCode || ""}}
				<u-icon size="32" name="edit-pen-fill" style="float: right;"></u-icon>
			</view>
			<u-row class="room-area-wrap">
				<u-col span="6">
					<view class="area-block" :class="{ 'actived': areaActived === 'livingRoom' }" @click="handleChangeArea('livingRoom')">
						<text class="icon iconfont icon-keting" style="margin-bottom: 10rpx; font-size: 75rpx;" :style="{ 'color': areaActived === 'livingRoom' ? 'white' : '#01B09A' }"></text>
						<!-- <image class="img" :src="'../../../static/images/device/icon-tag-sofa-' + (areaActived === 'livingRoom' ? 'normal' : 'actived') + '.png'"></image> -->
						<view class="text">客厅</view>
					</view>
				</u-col>
				<u-col span="6">
					<view class="area-block" :class="{ 'actived': areaActived === 'bedroom' }" @click="handleChangeArea('bedroom')">
						<text class="icon iconfont icon-woshi" style="margin-bottom: 10rpx; font-size: 75rpx" :style="{ 'color': areaActived === 'bedroom' ? 'white' : '#01B09A' }"></text>
						<!-- <image class="img" :src="'../../../static/images/device/icon-tag-bed-' + (areaActived === 'bedroom' ? 'normal' : 'actived') + '.png'"></image> -->
						<view class="text">卧室</view>
					</view>
				</u-col>
				<u-col span="6">
					<view class="area-block" :class="{ 'actived': areaActived === 'toilet' }" @click="handleChangeArea('toilet')">
						<text class="icon iconfont icon-weishengjian" style="margin-bottom: 10rpx; font-size: 75rpx;" :style="{ 'color': areaActived === 'toilet' ? 'white' : '#01B09A' }"></text>
						<!-- <image class="img" :src="'../../../static/images/device/icon-tag-toilet-' + (areaActived === 'toilet' ? 'normal' : 'actived') + '.png'"></image> -->
						<view class="text">卫生间</view>
					</view>
				</u-col>
				<u-col span="6">
					<view class="area-block" :class="{ 'actived': areaActived === 'other' }" @click="handleChangeArea('other')">
						<text class="icon iconfont icon-qita" style="margin-bottom: 10rpx; font-size: 75rpx" :style="{ 'color': areaActived === 'other' ? 'white' : '#01B09A' }"></text>
						<!-- <image class="img" :src="'../../../static/images/device/icon-tag-home-' + (areaActived === 'other' ? 'normal' : 'actived') + '.png'"></image> -->
						<view class="text">其他</view>
					</view>
				</u-col>
			</u-row>
		</view>

		<view class="footer-btns">
			<u-button shape="circle" class="next-btn diy-btn"
				size="medium" :custom-style="{ 'width': '100%', 'color': 'white', 'background-color': '#01B09A' }"
				hover-class="none" @click="$navTo(`pagesDevice/config/wifi-notice`)">下一步</u-button>
		</view>
		
	</view>
</template>

<script>
	export default {
		data() {
			return {
				value: '',
				deviceName: '',
				deviceNameDialog: false,
				areaActived: undefined,
				isChange: false,
				deviceInfo: {
					id: undefined,
					devCode: undefined,
					devName: undefined,
					devScene: undefined,
					houseAddr: undefined,
					houseId: undefined,
					latitude: undefined,
					longitude: undefined,
					memberId: undefined,
					memberPhone: undefined,
					selfBind: undefined,
				},
				source: '',
			}
		},
		onLoad(option) {
			this.source = option.source
			this.fetchDeviceInfo();
		},
		methods: {
			handleChangeArea(areaCode) {
				if (this.deviceInfo.selfBind) {
					this.isChange = true
					// this.areaActived = this.areaActived === areaCode ? '' : areaCode
					if (this.areaActived !== areaCode) {
						this.areaActived = areaCode
						this.handleSave()
					}
					this.areaActived = areaCode
				}
			},
			handleOpenNameDialog() {
				this.deviceNameDialog = true
				this.value = this.deviceInfo.devName
			},
			handleConfirmNameDialog() {
				this.deviceInfo.devName = this.value
				this.isChange = true
				this.submitRename();
			},
			fetchDeviceInfo() {
				uni.showLoading({
					title: '加载中...',
					mask: true
				})
				// this.$u.api.fetchDevInstallationInfo({ devCode: uni.getStorageSync('devCode') }).then(res => {
				this.$u.api.fetchMyDeviceInfo({ devId: uni.getStorageSync('devId') }).then(res => {
					if (res) {
						this.deviceInfo = res
						console.log('res', res)
						// 1客厅，2卫生间，3卧室，4其他
						if (res.devScene === '1') {
							this.areaActived = 'livingRoom'
						} else if (res.devScene === '2') {
							this.areaActived = 'toilet'
						} else if (res.devScene === '3') {
							this.areaActived = 'bedroom'
						} else if (res.devScene === '4') {
							this.areaActived = 'other'
						}
					}
				}).catch(err => {
					this.deviceInfo = {}
				}).finally(() => {
					setTimeout(() => {
						uni.hideLoading();
					}, 1000)
				})
			},
			handleSave() {
				let _devScene = undefined;
				// 1客厅，2卫生间，3卧室，4其他
				if (this.areaActived === 'livingRoom') {
					_devScene = 1
				} else if (this.areaActived === 'toilet') {
					_devScene = 2
				} else if (this.areaActived === 'bedroom') {
					_devScene = 3
				} else if (this.areaActived === 'other') {
					_devScene = 4
				}
				let _params = {
					id: uni.getStorageSync('devId'),
					devScene: _devScene,
					houseId: this.deviceInfo.houseId
				}
				uni.showLoading({
					title: '保存中...',
					mask: true
				})
				this.$u.api.execUpdateMyDevice(_params).then(res => {
					uni.hideLoading();
					uni.showToast({ duration: 1000, title: '保存标签成功', icon: 'none' })
					if (this.source === 'roomConfig') {
						setTimeout(() => {
							uni.navigateBack({
								delta: 1
							})
						}, 1000)
					}
				}).catch(err => {
					uni.hideLoading();
					uni.showToast({ duration: 2000, title: err.message || '发生未知错误', icon: 'none' })
				})
			},
			showEditForm() {
				const that = this;
				const {id, devName, devCode} = this.deviceInfo;
				uni.showModal({
					title: '修改设备名称',
					content: devName || devCode || '',
					editable: true,
					showCancel: true,
					confirmColor: "#01B09A",
					success: ({cancel, content}) => {
						if (cancel === true || !content) {
							that.deviceInfo.devName = devName;
							return;
						}
						if (devName != content) {
							that.$u.api.execUpdateDeviceName({ id, devName: content }).then(res => {
								that.deviceInfo.devName = content;
							}).catch(err => {
								console.error(err);
							});
						}
					}
				});
			}
		}
	}
</script>

<style lang="scss" scoped>
@import url("/static/css/iconfont.css");
.device-tag-config-page {
	.item-block {
		// box-shadow: 0px 8rpx 24rpx 0rpx rgba(0, 0, 0, 0.1);
		// border-radius: 16rpx;
		padding: 40rpx 20rpx;
		margin: 30rpx;
		font-size: 32rpx;
		margin-top: 0rpx;
	}
	.title {
		font-size: 32rpx;
		color: #0D0D0D;
		// font-weight: bold;
	}
	.sub-title {
		font-size: 30rpx;
		color: #8B8B8B;
		margin-top: 10rpx;
		margin-bottom: 30rpx;
	}
	.room-area-wrap {
		u-col {
			&:nth-child(2n + 1) {
				::v-deep .u-col {
					padding: 0rpx 8rpx 0rpx 0rpx !important;
				}
			}
			&:nth-child(2n + 0) {
				::v-deep .u-col {
					padding: 0rpx 0rpx 0rpx 8rpx !important;
				}
			}
			&:nth-child(n + 1) {
				::v-deep .u-col {
					margin-top: 20rpx;
				}
			}
		}
		.area-block {
			width: 100%;
			padding: 46rpx 0rpx;
			padding-left: 30rpx;
			background: #F6F7F9;
			border-radius: 10rpx;
			text-align: center;
			//#ifdef H5
			margin-bottom: 20rpx;
			//#endif
			.icon {
				font-size: 50rpx;
				vertical-align: middle;
			}
			.img {
				display: inline-block;
				width: 76rpx;
				height: 52rpx;
			}
			.text {
				display: inline-block;
				margin-left: 30rpx;
				width: 100rpx;
				text-align: left;
			}
			&.actived {
				background: #01B09A;
				color: white;
			}
		}
	}
	.footer-btns {
		position: absolute;
		bottom: 40rpx;
		left: 60rpx;
		right: 60rpx;
		margin: 0 auto;
		.next-btn {
			display: block;
			margin-top: 20rpx;
		}
	}
	
	::v-deep .uni-easyinput__content {
		padding: 14rpx 6rpx;
	}
	::v-deep .u-model__footer__button {
		height: 88rpx;
		line-height: 88rpx;
	}
}
</style>
