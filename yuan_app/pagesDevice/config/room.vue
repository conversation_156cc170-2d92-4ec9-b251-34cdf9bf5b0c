<template>
	<view class="main">
		<!-- <v-stage ref="stage" :config="stage" @dragstart="handleDragstart" @dragend="handleDragend" 
			@click="movevToButtom"
			@tap="movevToButtom"
			@mousedown="movevToButtom"
			@touchstar="movevToButtom"
			@dbclick="movevToButtom"
			@dbltap="movevToButtom"> -->
		<!-- stage边框说明 -->
		<view class="stage">
			<v-stage ref="stage" :config="stage" @dragstart="handleDragstart" @dragend="handleDragend"
				@dbclick="bedDblclick" @dbltap="bedDblclick" @click="stageClick" @tap="stageClick">
				<v-layer>
					<v-group>
						<!-- <v-text ref="textStageT" :config="{
						id:'textStageT',
						x:width/2,
						y:2,
						text:'前',
						fontSize: fontSize2,
						fill:'gray'
					}">
					</v-text>
					<v-text ref="textStageR" :config="{
						id:'textStageR',
						x:width-fontSize2-2,
						y:height/2,
						text:'右',
						fontSize: fontSize2,
						fill:'gray'
					}">
					</v-text>
					<v-text ref="textStageB" :config="{
						id:'textStageB',
						x:width/2,
						y:height-fontSize2-2,
						text:'后',
						fontSize: fontSize2,
						fill:'gray'
					}">
					</v-text>
					<v-text ref="textStageL" :config="{
						id:'textStageL',
						x:2,
						y:height/2,
						text:'左',
						fontSize: fontSize2,
						fill:'gray'
					}"> -->
						</v-text>
						<!-- 房间尺寸,像素和米的换算没出来前不显示-->
						<v-text v-if="pixel2cmRatio" :config="{
							id:'textRoomL',
							x:6,
							y:(room.y+room.h)/2,
							text:pi2m(room.h),
							fontSize: 14,
							fill:'#01B09A'
						}">
						</v-text>
						<v-text v-if="pixel2cmRatio" :config="{
							id:'textRoomT',
							x:(room.x+room.w)/2,
							y:roomPadding/2,
							text:pi2m(room.w),
							fontSize: 14,
							fill:'#01B09A'
						}">
						</v-text>
						<v-rect ref="room" :config="{
							id:'room',
							x: room.x,
							y: room.y,
							width: room.w,
							height: room.h,
							stroke: '#999999',
							fill:'#F9FFFC',
							strokeWidth: 4,
							draggable: false,
							opacity:1,
							shadowColor: '#000000',
							shadowOffsetX:3,
							shadowOffsetY:3,
							shadowBlur: 10,
							shadowOpacity:0.36,
						}" />
					</v-group>
					<v-group>

						<v-rect v-for="(item, index) in regionList" :key="`region${index}`" ref="bed"
							@click="bedDblclick" @mousemove="handleMouseMove" :config="{
							id:'region_'+index,
							type:'region',// 自定义的属性
							index:index,
							x: item.x,
							y: item.y,
							width: item.w,
							height: item.h,
							fill: 'white',
							stroke: '#01B09A',
							strokeWidth: 4,
							cornerRadius:4,
							draggable: true,
							dragBoundFunc:bedDragBoundFunc,
							//offsetX: 40,
							//offsetY: 30,
							//offsetX: bed.x+bed.w/2, 
							//offsetY: bed.y+bed.h/2,
						}" />
						<!-- <v-image ref="bed" @click="bedDblclick" @mousemove="handleMouseMove" :config="{
							id:'bed',
							x: bed.x,
							y: bed.y,
							width: bed.w,
							height: bed.h,
							draggable: true,
							image:bedImage,
						}" /> -->
						<v-rect v-for="(item, index) in gateList" :key="`gate${index}`" ref="gate" :config="{
							id:'gate_'+index,
							type:'gate',// 自定义的属性
							index:index,// 自定义的属性
							x: item.x,
							y: item.y,
							width: item.w,
							height: item.h,
							fill: 'blue',
							draggable: true,
							dragBoundFunc:gateDragBoundFunc,
							fill: 'white',
							stroke: '#01B09A',
							strokeWidth: 2,
							hitStrokeWidth: 30,
						}" />
						<v-rect ref="device" :config="{
							id:'device',
							type:'device',
							x: device.x,
							y: device.y,
							width: device.w,
							height: device.h,
							cornerRadius:2,
							hitStrokeWidth: 20,
							fill: '#01B09A',
							draggable: true,
							dragBoundFunc:deviceDragBoundFunc,
							opacity:0.6,
						}" />
						<!-- 设备左边线条和文字-->
						<v-line :config="{id:'deviceLineL',
							points:[room.x,device.y+device.h/2,
									device.x,device.y+device.h/2],
							stroke: '#01B09A',
							strokeWidth: 1,
							lineCap: 'round',
							lineJoin: 'round',
							tension: 1}">
						</v-line>
						<v-text :config="{
							id:'deviceTextL',
							x:(device.x+room.x)/2,
							y:device.y-8,
							text:pi2m(device.x-room.x),
							fontSize: fontSize1,
							fill:'#01B09A'
						}">
						</v-text>
						<v-group v-if="selectSpotType == 1">
							<!-- 设备顶部线条和文字
							<v-line ref="deviceLineT" :config="{id:'deviceLineT',
								points:[device.x+device.w/2,room.y,
										device.x+device.w/2,device.y],
								stroke: '#01B09A',
								strokeWidth: 1,
								lineCap: 'round',
								lineJoin: 'round',
								tension: 1
							}">
							</v-line>
							<v-text ref="deviceTextT" :config="{
								id:'deviceTextT',
								x:device.direction == '0'?(device.x+device.w/2):(device.x+device.w/2)-2*fontSize1,
								y:(device.y+roomPadding)/2,
								text:pi2m(device.y-room.y),
								fontSize: fontSize1,
								fill:'#01B09A'
							}">
							</v-text>-->
							<!-- 底部线条和文字-->
							<v-line ref="deviceLineB" :config="{id:'deviceLineB',
								points:[device.x+device.w/2,room.y+room.h,
										device.x+device.w/2,device.y+device.h],
								stroke: '#01AF99',
								strokeWidth: 1,
								lineCap: 'round',
								lineJoin: 'round',
								tension: 1
							}">
							</v-line>
							<v-text ref="deviceTextB" :config="{
								id:'deviceTextB',
								x:(device.x+device.w/2),
								y:(device.y+device.h+room.y+room.h)/2-fontSize1,
								text:pi2m((room.y+room.h)-(device.y+device.h)),
								fontSize: fontSize1,
								fill:'#01AF99'
							}">
							</v-text>
						</v-group>
					</v-group>
				</v-layer>

				<!-- 门的图层-->
				<v-layer>
					<v-group v-for="(item, index) in gateList" :key="`gat${index}`">
						<!--字得放上面不然影响拖拽
					<v-text :config="{
						id:'gateName'+index,
						x:(item.x+item.w/2)-fontSize1/2+1,
						y:item.y+item.h/2,
						text:'门',
						fontSize: fontSize1,
						fill:'gray'
					}">
					</v-text>
					<v-rect ref="gate" :config="{
						id:'gate_'+index,
						type:'gate',// 自定义的属性
						index:index,// 自定义的属性
						x: item.x,
						y: item.y,
						width: item.w,
						height: item.h,
						fill: 'blue',
						draggable: true,
						opacity: 0.3,
						}" /> -->

						<v-group v-if="item.direction == '1'">
							<!-- 左边线条和文字-->
							<v-line ref="gateLineL" :config="{id:'gateLineL',
								points:[room.x,item.y+item.h/2,
										item.x,item.y+item.h/2],
								stroke: '#FF7D17',
								strokeWidth: 1,
								lineCap: 'round',
								lineJoin: 'round',
								tension: 1}">
							</v-line>
							<v-text ref="gateTextL" :config="{
								id:'gateTextL',
								x:(item.x+room.x)/2,
								y:item.direction == '1'?item.y+item.h/2+5:item.y+item.h/2-fontSize1,
								text:pi2m(item.x-room.x),
								fontSize: fontSize1,
								fill:'#FF7D17'
							}">
							</v-text>
						</v-group>
						<v-group v-else-if="item.direction == '3'">
							<!-- 右边线条和文字-->
							<v-line ref="gateLineR" :config="{id:'gateLineR',
								points:[room.x+room.w,item.y+item.h/2,
										item.x+item.w,item.y+item.h/2],
								stroke: '#FF7D17',
								strokeWidth: 1,
								lineCap: 'round',
								lineJoin: 'round',
								tension: 1}">
							</v-line>
							<v-text ref="gateTextR" :config="{
								id:'gateTextR',
								x:(item.x+item.w+room.w)/2,
								y:item.direction == '1'?item.y+item.h/2+5:item.y+item.h/2-fontSize1,
								text:pi2m((room.x+room.w)-(item.x+item.w)),
								fontSize: fontSize1,
								fill:'#FF7D17'
							}">
							</v-text>
						</v-group>
						<v-group v-else-if="item.direction == '2'">
							<!-- 顶部线条和文字-->
							<v-line ref="gateLineT" :config="{id:'gateLineT',
								points:[item.x+item.w/2,room.y,
										item.x+item.w/2,item.y],
								stroke: '#FF7D17',
								strokeWidth: 1,
								lineCap: 'round',
								lineJoin: 'round',
								tension: 1
							}">
							</v-line>
							<v-text ref="gateTextT" :config="{
								id:'gateTextT',
								x:item.direction == '0'?(item.x+item.w/2):(item.x+item.w/2)-2*fontSize1,
								y:(item.y+roomPadding)/2,
								text:pi2m(item.y-room.y),
								fontSize: fontSize1,
								fill:'#FF7D17'
							}">
							</v-text>
						</v-group>
						<v-group v-else-if="item.direction == '0'">
							<!-- 底部线条和文字-->
							<v-line ref="gateLineB" :config="{id:'gateLineB',
								points:[item.x+item.w/2,room.y+room.h,
										item.x+item.w/2,item.y+item.h],
								stroke: '#FF7D17',
								strokeWidth: 1,
								lineCap: 'round',
								lineJoin: 'round',
								tension: 1
							}">
							</v-line>
							<v-text ref="gateTextB" :config="{
								id:'gateTextB',
								x:item.direction == '0'?(item.x+item.w/2):(item.x+item.w/2)-2*fontSize1,
								y:(item.y+item.h+room.y+room.h)/2-fontSize1,
								text:pi2m((room.y+room.h)-(item.y+item.h)),
								fontSize: fontSize1,
								fill:'#FF7D17'
							}">
							</v-text>
						</v-group>
					</v-group>
				</v-layer>

				<!-- 床的图层 -->
				<v-layer>
					<v-group v-for="(item, index) in regionList" :key="`region${index}`">
						<!--字得放上面不然影响拖拽 -->
						<v-text :config="{
							id:'bedName',
							x:(item.x+item.w/2),
							y:item.y+item.h/2,
							text:dict.cls[item.cls].text,
							fontSize: fontSize1,
							fill:'#3D3D3D'
						}">
						</v-text>
						<!-- <v-rect ref="bed" @click="bedDblclick" @mousemove="handleMouseMove" :config="{
						id:'item',
						x: item.x,
						y: item.y,
						width: item.w,
						height: item.h,
						fill: 'blue',
						draggable: true,
						opacity:0.2,
						offsetX: 0, 
						offsetY: 0
					}" /> -->

						<!-- 顶部线条和文字 -->
						<v-line ref="bedLineT" :config="{id:'bedLineT',
							points:[item.x+item.w/2,room.y,
									item.x+item.w/2,item.y],
							stroke: '#eeeeee',
							strokeWidth: 1,
							lineCap: 'round',
							lineJoin: 'round',
							tension: 1
						}">
						</v-line>
						<v-text ref="bedTextT" :config="{
							id:'bedTextT',
							x:(item.x+item.w/2),
							y:(item.y+roomPadding)/2,
							text:pi2m(item.y-room.y),
							fontSize: fontSize1,
							fill:'#eeeeee'
						}">
						</v-text>

						<!-- 底部线条和文字-->
						<v-line ref="bedLineB" :config="{id:'bedLineB',
							points:[item.x+item.w/2,room.y+room.h,
									item.x+item.w/2,item.y+item.h],
							stroke: '#01AF99',
							strokeWidth: 1,
							lineCap: 'round',
							lineJoin: 'round',
							tension: 1
						}">
						</v-line>
						<v-text ref="bedTextB" :config="{
							id:'bedTextB',
							x:(item.x+item.w/2),
							y:(item.y+item.h+room.y+room.h)/2-fontSize1,
							text:pi2m((room.y+room.h)-(item.y+item.h)),
							fontSize: fontSize1,
							fill:'#01AF99'
						}">
						</v-text>

						<!-- 左边线条和文字-->
						<v-line ref="bedLineL" :config="{id:'bedLineL',
							points:[room.x,item.y+item.h/2,
									item.x,item.y+item.h/2],
							stroke: '#eeeeee',
							strokeWidth: 1,
							lineCap: 'round',
							lineJoin: 'round',
							tension: 1}">
						</v-line>
						<v-text ref="bedTextL" :config="{
							id:'bedTextL',
							x:(item.x+room.x)/2,
							y:item.y+item.h/2+5,
							text:pi2m(item.x-room.x),
							fontSize: fontSize1,
							fill:'#eeeeee'
						}">
						</v-text>

						<!-- 右边线条和文字-->
						<v-line ref="bedLineR" :config="{id:'bedLineR',
							points:[room.x+room.w,item.y+item.h/2,
									item.x+item.w,item.y+item.h/2],
							stroke: '#01AF99',
							strokeWidth: 1,
							lineCap: 'round',
							lineJoin: 'round',
							tension: 1}">
						</v-line>
						<v-text ref="bedTextR" :config="{
							id:'bedTextR',
							x:(item.x+item.w+room.x+room.w)/2,
							y:item.y+item.h/2+5,
							text:pi2m((room.x+room.w)-(item.x+item.w)),
							fontSize: fontSize1,
							fill:'#01AF99'
						}">
						</v-text>
					</v-group>
				</v-layer>
			</v-stage>
			<view class="c-flex jc-c ta-c stage_descr">
				<view class="desc">以设备为参照物，拖动区域放在相应位置</view>
				<view class="unit">单位：米</view>
			</view>
		</view>

		<view class="room_data">
			<view class="c-flex">

				<view class="c-flex room_data_item">
					<view class="r-flex jc-sb">
						<view>
							<text class="name" style="margin-right: 8rpx;">房间</text>
							<u-icon name="question-circle" color="#666" size="32" @click="questionClick(3)"></u-icon>
						</view>
						<view>
							<text style="color: #01AF99;" @click="goto3d()">人工标注</text>
						</view>
					</view>
					<view v-if="!room.editing" class="r-flex jc-sb data" @click="startRoomEditing(room)">
						<view class="c-flex">
							<view class="r-flex">
								<text class="text">尺寸</text>
								<text class="value">{{room.m_x}}</text>
								<text class="x">x</text>
								<text class="value">{{room.m_y}}</text>
							</view>
						</view>
						<view class="c-flex">
							<view class="r-flex">
								<text class="text">高</text>
								<text class="value">{{room.m_z}}</text>
							</view>
						</view>
					</view>
					<view v-else class="r-flex jc-sb data">
						<view class="c-flex">
							<view class="r-flex">
								<text class="text">尺寸</text>
								<input type="digit" class="value" v-model="room.m_x" @blur="stopRoomEditing(room)"
									maxlength="4" style="background-color: #eeeeee;" auto-blur></input>
								<text class="x">x</text>
								<input type="digit" class="value" v-model="room.m_y" @blur="stopRoomEditing(room)"
									maxlength="4" style="background-color: #eeeeee;" auto-blur></input>
							</view>
						</view>
						<view class="c-flex">
							<view class="r-flex">
								<text class="text">高</text>
								<input type="digit" class="value" v-model="room.m_z" @blur="stopRoomEditing(room)"
									maxlength="4" style="background-color: #eeeeee;" auto-blur></input>
							</view>
						</view>
					</view>
				</view>

				<view class="c-flex room_data_item">
					<view class="r-flex jc-sb">
						<view>
							<text class="name">设备</text>
						</view>
					</view>
					<view v-if="!device.editing" class="r-flex jc-sb data" @click="startDeviceEditing(device)">
						<view class="c-flex">
							<view class="r-flex">
								<text class="text">距离</text>
								<text class="value">{{device.m_x}}</text>
								<text class="x">x</text>
								<text class="value">{{device.m_y}}</text>
							</view>
						</view>
						<view class="c-flex">
							<view class="r-flex">
								<text class="text">高</text>
								<text class="value">{{device.m_z}}</text>
							</view>
						</view>
					</view>
					<view v-else class="r-flex jc-sb data">
						<view class="c-flex">
							<view class="r-flex">
								<text class="text">距离</text>
								<input type="digit" class="value" v-model="device.m_x"
									@blur="stopDeviceEditing(device)" maxlength="4" style="background-color: #eeeeee;"
									auto-blur></input>
								<text class="x">x</text>
								<input type="digit" class="value" v-model="device.m_y"
									@blur="stopDeviceEditing(device)" maxlength="4" style="background-color: #eeeeee;"
									auto-blur></input>
							</view>
						</view>
						<view class="c-flex">
							<view class="r-flex">
								<text class="text">高</text>
								<input type="digit" class="value" v-model="device.m_z"
									@blur="stopDeviceEditing(device)" maxlength="4" style="background-color: #eeeeee;"
									auto-blur></input>
							</view>
						</view>
					</view>
				</view>

				<view v-for="(bed, index) in regionList" :key="`region${index}`" class="c-flex room_data_item">
					<view class="r-flex jc-sb">
						<view>
							<text class="name">{{dict.cls[bed.cls].text}}</text>
							<text class="desr">{{`(尺寸:${pi2m(bed.w)}x${pi2m(bed.h)}m)`}}</text>
						</view>
						<text class="icon iconfont icon-lajixiangshanchu delete" @click="deleteBed(index)"></text>
					</view>
					<view v-if="!bed.editing" class="r-flex jc-sb data" @click="startBedEditing(bed)">
						<view class="c-flex">
							<view class="r-flex">
								<text class="text">距离</text>
								<text class="value">{{pi2m((room.x+room.w)-(bed.x+bed.w))}}</text>
								<text class="x">x</text>
								<text class="value">{{pi2m((room.y+room.h)-(bed.y+bed.h))}}</text>
							</view>
						</view>
						<view class="c-flex">
							<view class="r-flex">
								<text class="text">高</text>
								<text class="value">{{bed.m_z}}</text>
							</view>
						</view>
					</view>
					<view v-else class="r-flex jc-sb data">
						<view class="c-flex">
							<view class="r-flex">
								<text class="text">距离</text>
								<input type="digit" class="value" v-model="bed.m_distance1"
									@blur="stopBedEditing(bed)" maxlength="4" style="background-color: #eeeeee;"
									auto-blur></input>
								<text class="x">x</text>
								<input type="digit" class="value" v-model="bed.m_distance2"
									@blur="stopBedEditing(bed)" maxlength="4" style="background-color: #eeeeee;"
									auto-blur></input>
							</view>
						</view>
						<view class="c-flex">
							<view class="r-flex">
								<text class="text">高</text>
								<input type="digit" class="value" v-model="bed.m_z" @blur="stopBedEditing(bed)"
									maxlength="4" style="background-color: #eeeeee;" auto-blur></input>
							</view>
						</view>
					</view>
				</view>


				<view v-for="(item, index) in gateList" :key="index">
					<view class="c-flex room_data_item">
						<view class="r-flex jc-sb">
							<view>
								<text class="name">{{dict.direction[item.direction].text+'门'}}</text>
							</view>
							<text class="icon iconfont icon-lajixiangshanchu delete" @click="deleteGate(index)"></text>
						</view>
						<view v-if="!item.editing" class="r-flex jc-sb data" @click="startGateEditing(item)">
							<view class="c-flex">
								<view class="r-flex">
									<text class="text">距离</text>
									<text v-if="item.direction=='1'" class="value">{{pi2m(item.x-room.x)}}</text>
									<text v-else-if="item.direction=='2'" class="value">{{pi2m(item.y-room.y)}}</text>
									<text v-else-if="item.direction=='3'"
										class="value">{{pi2m((room.x+room.w)-(item.x+item.w))}}</text>
									<text v-else-if="item.direction=='0'"
										class="value">{{pi2m((room.y+room.h)-(item.y+item.h))}}</text>
								</view>
							</view>
							<view class="c-flex">
								<view class="r-flex">
									<text class="text">门宽</text>
									<text class="value">{{item.m_width}}</text>
								</view>
							</view>
						</view>
						<view v-else class="r-flex jc-sb data">
							<view class="c-flex">
								<view class="r-flex">
									<text class="text">距离</text>
									<input type="digit" class="value" v-model="item.m_distance1"
										@blur="stopGateEditing(item)" maxlength="4" style="background-color: #eeeeee;"
										auto-blur></input>
								</view>
							</view>
							<view class="c-flex">
								<view class="r-flex">
									<text class="text">门宽</text>
									<input type="digit" class="value" v-model="item.m_width"
										@blur="stopGateEditing(item)" maxlength="4" style="background-color: #eeeeee;"
										auto-blur></input>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<view class="popup-aera">
			<!-- 修改房间尺寸 移动端点击事件无效，暂时不用-->
			<!-- <MyPopup title="房间尺寸" :visible="showEditRoom" @cancel="popupHide('showEditRoom')" @confirm="saveRoom">
				<view class="r-flex">
					<u-field v-model="roomData.x" label="尺寸(米)" type="digit" placeholder="请输入" required>
					</u-field>x
					<u-field v-model="roomData.y" type="digit" placeholder="请输入" required>
					</u-field>
				</view>
				<u-field v-model="roomData.z" label="高(米)" type="digit" placeholder="请输入" required>
				</u-field>
			</MyPopup> -->
			<!-- 修改区域尺寸 -->
			<!-- <MyPopup :title="`${editRegionTypeName}`" :visible="showEditRegion" @cancel="popupHide('showEditRegion')"
				@confirm="saveRegion">
				<view class="r-flex">
					<u-field v-model="roomData.x" label="尺寸(米)" type="digit" placeholder="请输入" required>
					</u-field>x
					<u-field v-model="roomData.y" type="digit" placeholder="请输入" required>
					</u-field>
				</view>
				<u-field v-model="roomData.z" label="高(米)" type="digit" placeholder="请输入" required>
				</u-field>
			</MyPopup> -->
			<!-- 添加床-->
			<MyPopup v-if="showPupopType=='selectBed'" title="添加床" :subtitle="`已选择:${selectBedSize}尺寸`" :visible="true"
				@cancel="popupHide('selectBed')" @confirm="saveBed">
				<view class="r-flex jc-sa grid-bed">
					<view v-for="(row, index) in dict.bedSize" :key="index" class="c-flex jc-c bed-item"
						:style="{'background-color': selectBedIndex===index ? '#EEFFFD' : '#EEEEEE',}"
						@click="bedSelect(index,1)">
						<text class="text" :style="{'color': selectBedIndex===index ? '#01B09A' : '#333333',}">
							{{row.text}}
						</text>
					</view>
				</view>
			</MyPopup>

			<!-- 自定义床尺寸 -->
			<MyPopup v-else-if="showPupopType=='addRegion'" :title="`添加${addRegionIdx&&dict.cls[addRegionIdx].text}`"
				:visible="true" @cancel="popupHide('addRegion')" @confirm="saveRegion">
				<view class="r-flex">
					<u-field v-model="customBed.x" label="尺寸(米)" type="digit" placeholder="请输入" required>
					</u-field>x
					<u-field v-model="customBed.y" type="digit" placeholder="请输入" required>
					</u-field>
				</view>
				<u-field v-model="customBed.z" label="高(米)" type="digit" placeholder="请输入" required>
				</u-field>
			</MyPopup>

			<MyPopup v-else-if="showPupopType=='addGate'" title="添加门" subtitle="为了监测效果，请勿将设备安装在门同一侧" :visible="true"
				@cancel="popupHide('addGate')" @confirm="saveGate">
				<view class="c-flex jc-c ta-c">
					<view class="r-flex jc-c">
						<view class="r-flex jc-c bed-item"
							:style="{'background-color': selectGateIndex===1 ? '#EEFFFD' : '#EEEEEE',}"
							@click="gateSelect(1)">
							<text class="text"
								:style="{'color': selectGateIndex===1 ? '#01B09A' : '#333333',}">上边门</text>
						</view>
					</view>

					<view class="r-flex jc-sa">
						<view class="r-flex jc-c bed-item"
							:style="{'background-color': selectGateIndex===0 ? '#EEFFFD' : '#EEEEEE',}"
							@click="gateSelect(0)">
							<text class="text"
								:style="{'color': selectGateIndex===0 ? '#01B09A' : '#333333',}">左边门</text>
						</view>
						<view class="r-flex jc-c bed-item"
							:style="{'background-color': selectGateIndex===2 ? '#EEFFFD' : '#EEEEEE',}"
							@click="gateSelect(2)">
							<text class="text"
								:style="{'color': selectGateIndex===2 ? '#01B09A' : '#333333',}">右边门</text>
						</view>
					</view>

					<view class="r-flex jc-c">
						<view class="r-flex jc-c bed-item"
							:style="{'background-color': selectGateIndex===3 ? '#EEFFFD' : '#EEEEEE',}"
							@click="gateSelect(3)">
							<text class="text"
								:style="{'color': selectGateIndex===3 ? '#01B09A' : '#333333',}">下边门</text>
						</view>
					</view>
				</view>
			</MyPopup>

		</view>
		<view class="footer-btns">
			<u-row gutter="16">
				<u-col span="3">
					<text class="text">安装方式</text>
				</u-col>
				<view class="r-flex jc-sb">
					<view class="r-flex jc-c tag"
						:style="{'background-color': selectSpotType==2 ? '#EEFFFD' : '#EEEEEE',}"
						@click="spotTypeSelect(2)">
						<text class="text" :style="{'color': selectSpotType==2 ? '#01B09A' : '#333333',}">侧装</text>
					</view>
					<view class="r-flex jc-c tag"
						:style="{'background-color': selectSpotType==1 ? '#EEFFFD' : '#EEEEEE','margin-left':'80rpx',}"
						@click="spotTypeSelect(1)">
						<text class="text" :style="{'color': selectSpotType==1 ? '#01B09A' : '#333333',}">顶装</text>
					</view>
				</view>
			</u-row>
			<u-row gutter="16" style="margin-top: 32rpx;">
				<u-col span="3">
					<text class="text" style="margin-right: 6rpx;">区域</text>
					<u-icon name="question-circle" color="#666" size="32" @click="questionClick(2)"></u-icon>
				</u-col>
				<view class="c-flex ta-c" @click="showPupop(999)">
					<text class="icon iconfont icon-men tubiao"></text>
					<text class="text">门</text>
				</view>
				<view class="c-flex ta-c" @click="showPupop(888)" style="margin-left: 60rpx;">
					<text class="icon iconfont icon-chuang tubiao"></text>
					<text class="text">床</text>
				</view>
				<view class="c-flex ta-c" @click="showPupop(2)" style="margin-left: 60rpx;">
					<text class="icon iconfont icon-zhuozi tubiao"></text>
					<text class="text">桌子</text>
				</view>
				<view class="c-flex ta-c" @click="showPupop(3)" style="margin-left: 60rpx;">
					<text class="icon iconfont icon-shafa tubiao"></text>
					<text class="text">沙发</text>
				</view>
			</u-row>
			<u-button shape="circle" class="next-btn diy-btn" size="medium"
				:custom-style="{ 'width': '100%', 'color': 'white', 'background-color': '#01B09A' }" hover-class="none"
				@click="submitRoomData">提交</u-button>
		</view>
	</view>
</template>
<!-- 该页面的konva无法运行于小程序,当前运行于h5中-->
<script>
	import MyPopup from './components/my-popup.vue';
	import * as util from '@/utils/util';
	import Konva from 'konva';
	/*
	//#ifdef MP-WEIXIN
	const systemInfo = uni.getSystemInfoSync();
	// 获取设备像素比
	const pixelRatio = systemInfo.pixelRatio;
	const window_w = systemInfo.screenWidth / pixelRatio;
	const window_h = systemInfo.screenWidth / pixelRatio;
	const canvas = wx.createCanvasContext('stage');
	//#endif
	*/
	const window_w = window.innerWidth;
	const innerHeight = window.innerHeight;
	const window_h = window.innerWidth;
	//const canvas = uni.createCanvasContext('stage');
	console.log("innerHeight:", innerHeight)
	console.log("window_w:", window_w)
	// 房间最大所占像素
	const roomAreaMaxValue = 310;
	const roomPadding = 40;
	const deviceWH = 20;
	export default {
		components: {
			MyPopup,
		},
		data() {
			return {
				roomPadding: roomPadding,
				fontSize1: 12,
				fontSize2: 16,
				width: 375,
				height: 375,
				staticUrl: this.$u.http.config.staticBaseUrl,
				dragItemId: null,
				dragGateIndex: null,
				dragRegionIndex: null,
				scale: 1,
				stage: {
					id: 'stage',
					x: 0,
					y: 0,
					width: window_w,
					height: window_h - 15,
					draggable: false,
					scaleX: 1,
					scaleY: 1,
					/*
					//#ifdef MP-WEIXIN
					context: canvas,
					//#endif  
					*/
				},
				room: {
					x: roomPadding,
					y: roomPadding,
					w: roomAreaMaxValue,
					h: roomAreaMaxValue,
					editing: false,
				},
				devCode: undefined,
				bedImage: null,
				device: {},
				roomData: {},
				regionList: [],
				gateList: [],
				pixel2cmRatio: undefined,
				dict: {
					spotType: [{
							code: '1',
							text: '顶装'
						},
						{
							code: '2',
							text: '侧装'
						}
					],
					direction: [{
							code: '0',
							text: '左'
						},
						{
							code: '1',
							text: '上'
						},
						{
							code: '2',
							text: '右'
						},
						{
							code: '3',
							text: '下'
						},

					],
					bedSize: [{
							code: '0.9x2',
							text: '0.9x2m',
						},
						{
							code: '1.2x2',
							text: '1.2x2m',
						},
						{
							code: '1.35x2',
							text: '1.35x2m',
						},
						{
							code: '1.5x2',
							text: '1.5x2m',
						},
						{
							code: '1.8x2',
							text: '1.8x2m',
						},
						{
							code: '1.8x2.2',
							text: '1.8x2.2m',
						},
						{
							code: '2x2',
							text: '2x2m',
						},
						{
							code: '1.2x1.9',
							text: '1.2x1.9m',
						},
						{
							code: '1.2x1.9',
							text: '1.2x1.9m',
						},
						{
							code: '0x0',
							text: '自定义',
						},
					],
					gates: [{
							code: 1,
							text: '上边门',
						},
						{
							code: 0,
							text: '左边门',
						},
						{
							code: 2,
							text: '右边门',
						},
						{
							code: 3,
							text: '下边门',
						},
					], // 0unknow,1bed,2table,3chair
					cls: [{
							code: 0,
							text: '其他',
						},
						{
							code: 1,
							text: '床',
						},
						{
							code: 2,
							text: '桌子',
						},
						{
							code: 3,
							text: '沙发',
						},
					],
				},
				showEditRoom: false,
				showEditRegion: false,

				showPupopType: undefined,
				addRegionIdx: undefined,

				selectBedIndex: undefined,
				selectBedSize: '',
				tempBedList: [],

				selectGateIndex: undefined,
				tempGateList: [],

				editRegionType: undefined,
				editRegionTypeName: '',

				//1 顶装 2 侧装
				selectSpotType: undefined,

				customBed: {},
				formBed: {},
				from: undefined,
				sourcePage: undefined,
				roomDataFrom3D:undefined,
				
				// 场景页面参数
				devScene:undefined,
				devHeight:undefined,
			}
		},

		mounted() {
			//#ifdef H5  
			// document.querySelector('.uni-page-head-hd').style.display = 'none';
			// document.querySelector('uni-page-head').style.display = 'none';
			//#endif  

			const pageHeight = document.documentElement.clientHeight;
			window.addEventListener('resize', (e) => {
				//console.log(e);
				let clientHeight = document.documentElement.clientHeight;
				console.log("pageHeight:", pageHeight);
				console.log("clientHeight:", clientHeight);
				let height = pageHeight - clientHeight;
				let footer = document.querySelector(".footer-btns");
				if (height > 0) {
					footer.style.display = "none";
					console.log("输入法弹出，隐藏提交等内容")
				} else {
					footer.style.display = "block";
					console.log("输入法收起，显示提交等内容")
				}
			});
		},
		async onLoad(options) {
			console.log("2d options:", options)
			const {token,from,devCode,sourcePage,roomDataFrom3D,devScene,spotType,devHeight} = options;
			this.from = from;
			this.devCode = devCode;
			this.sourcePage = sourcePage;
			this.roomDataFrom3D = roomDataFrom3D;
			this.devScene = devScene;
			this.selectSpotType = spotType;
			this.devHeight = devHeight;
			if (token) {
				uni.setStorageSync('token', token);
			}
			// 获取登录用户信息
			await this.getMemberDetailInfo();
			
			console.log("roomDataFrom3D:"+roomDataFrom3D)
			if (!this.devCode) {
				this.devCode = uni.getStorageSync('devCode');
			}else{
				uni.setStorageSync('devCode',this.devCode);
			}
			// #ifdef H5
			uni.hideTabBar()
			// #endif
		},
		onLaunch() {
			// #ifdef H5
			uni.hideTabBar({})
			// #endif
		},
		async onShow() {
			console.log("onShow")
			// 模拟房间数据
			const roomDetailInfo = await this.getData();
			if (!roomDetailInfo) {
				console.log("无效的devCode")
				uni.showModal({
					content: '设备无效',
					showCancel: false,
					confirmText: '关闭'
				})
			}
			// 房间尺寸
			let roomVO = roomDetailInfo?.roomVO || undefined;
			let roomSensorVO = roomDetailInfo?.roomSensorVO || undefined;
			let roomGateVOList = roomDetailInfo?.roomGateVOList || undefined;
			let roomRegionVOList = roomDetailInfo?.roomRegionVOList || undefined;
			
			// 读取3d传给2d的数据,webview中无法读取Storage的数据
			if(this.roomDataFrom3D){
				// uni.showModal({
				// 	content: '读取到3d的数据',
				// 	showCancel: false,
				// 	confirmText: '关闭'
				// })
				console.log("roomDataFrom3D:"+this.roomDataFrom3D)
				const {room:rData3d,device:devData3d} = JSON.parse(this.roomDataFrom3D);
				console.log("rData3d:",rData3d)
				console.log("devData3d:",devData3d)
				roomVO = {
					x:parseFloat(rData3d.x).toFixed(2),
					y:parseFloat(rData3d.y).toFixed(2),
					z:parseFloat(rData3d.z).toFixed(2),
				}
				roomSensorVO={
					x:parseFloat(devData3d.x).toFixed(2),
					y:parseFloat(devData3d.y).toFixed(2),
					z:parseFloat(devData3d.z).toFixed(2),
					spotType:devData3d.spotType,
				}
			}else{
				// uni.showModal({
				// 	content: '没有读取到3d的数据',
				// 	showCancel: false,
				// 	confirmText: '关闭'
				// })
			}

			if (roomVO) {
				this.roomData.x = roomVO.x;
				this.roomData.y = roomVO.y;
				this.roomData.z = roomVO.z;
			} else {
				this.roomData = {
					x: 4,
					y: 5,
					z: 2.8,
				}
			}
			this.room.w = this.m2pi(this.roomData.x);
			this.room.h = this.m2pi(this.roomData.y);
			this.room.m_x = this.roomData.x;
			this.room.m_y = this.roomData.y;
			this.room.m_z = this.roomData.z;
			this.room.editing = false;
			console.log("this.room", this.room)

			/* if (roomVO) {
				this.room.w = this.m2pi(roomVO.x);
				this.room.h = this.m2pi(roomVO.y);
				this.room.m_x = roomVO.x;
				this.room.m_y = roomVO.y;
				this.room.m_z = roomVO.z;
				this.room.editing = false;
			} else {
				let x = 4;
				let y = 4;
				let z = 2.8;
				this.room = {
					w: this.m2pi(x),
					h: this.m2pi(y),
					m_x: x,
					m_y: y,
					m_z: z,
					editing: false,
				}
			} */

			// 设备位置
			let deviceY = deviceWH / 2;
			if (roomSensorVO) {
				console.log("roomSensorVO:", roomSensorVO);
				
				// 以url参数中的为准
				if(this.devHeight){
					roomSensorVO.z = this.devHeight;
				}
				this.selectSpotType = roomSensorVO.spotType;
				
				deviceY = this.selectSpotType == 2 ? deviceWH / 2 : deviceWH;
				this.device = {
					m_x: roomSensorVO.x,
					m_y: roomSensorVO.y,
					m_z: roomSensorVO.z,
					x: this.m2pi(roomSensorVO.x) + this.room.x,
					y: this.selectSpotType == 2 ? (this.room.y + this.room.h - deviceY) : (this.room.y + this.room
						.h - this.m2pi(roomSensorVO.y) - deviceWH),
					w: deviceWH,
					h: deviceY,
					editing: false,
				}
			} else {
				this.device = {
					m_x: 3,
					m_y: 0.01,
					m_z: this.devHeight||2,
					x: this.room.x + this.room.w / 2,
					y: this.room.y + this.room.h - deviceY,
					w: deviceWH,
					h: deviceY,
					editing: false,
				};
			}
			console.log("device:", this.device);
			// 床位置数据
			// this.bedData = {
			// 	positionX: 1,
			// 	positionY: 1,
			// 	scaleX: 1.2,
			// 	scaleY: 2,
			// 	scaleZ: 0.3,
			// };
			if (roomRegionVOList && roomRegionVOList.length > 0) {
				// 床
				/* const bedList = roomRegionVOList.filter(item => item.cls === 1).map(item => item);
				if (bedList && bedList.length > 0) {
					let bedData = bedList[0];
					// m开头的表示以米计
					this.bed = {
						m_positionX: bedData.positionX,
						m_positionY: bedData.positionY,
						m_x: bedData.scaleX,
						m_y: bedData.scaleY,
						
						m_z: bedData.scaleZ,
						x: this.m2pi(bedData.positionX - bedData.scaleX / 2) + this.room.x,
						y: this.m2pi(this.pi2m(this.room.y + this.room.h) - bedData.positionY - bedData.scaleY /
							2),
						w: this.m2pi(bedData.scaleX),
						h: this.m2pi(bedData.scaleY),
						z: this.m2pi(bedData.scaleZ),
						editing: false,
					}
				} */
				this.regionList = [];
				roomRegionVOList.forEach((bedData, i) => {
					const region = {
						m_positionX: bedData.positionX,
						m_positionY: bedData.positionY,
						m_x: bedData.scaleX,
						m_y: bedData.scaleY,

						m_z: bedData.scaleZ,
						x: this.m2pi(bedData.positionX - bedData.scaleX / 2) + this.room.x,
						y: this.m2pi(this.pi2m(this.room.y + this.room.h) - bedData.positionY - bedData
							.scaleY / 2),
						w: this.m2pi(bedData.scaleX),
						h: this.m2pi(bedData.scaleY),
						z: this.m2pi(bedData.scaleZ),
						editing: false,
						cls: bedData.cls,
					};
					this.regionList.push(region);
					console.log("region:", region);
				});
			}



			// 门位置数据
			let gates = [];
			if (roomGateVOList && roomGateVOList.length > 0) {
				gates = roomGateVOList;
			}
			this.gateList = [];
			gates.forEach((item, i) => {
				const g = this.gateConfog(item);
				const gate = {
					...g,
					editing: false,
				};
				this.gateList.push(gate);
				console.log("gate:", gate);
			});



		},
		created() {
			/* console.log("created")
			const bedImage = new window.Image();
			bedImage.src = "https://konvajs.org/assets/yoda.jpg";
			bedImage.onload = () => {
				// set image only when it is loaded
				this.bedImage = bedImage;
			}; */
		},
		methods: {
			null2val: util.null2val,
			async getMemberDetailInfo(){
				let member;
				// 非小程序
				//#ifndef MP-WEIXIN
				member = await this.$u.api.fetchMemberDetailInfo({});
				uni.setStorageSync( 'member', member);
				//#endif
				
				// 小程序
				//#ifdef MP-WEIXIN
				member = uni.getStorageSync('member');
				//#endif
				
				if(!member){
					uni.showToast({
						duration: 2000,
						title: '用户信息错误',
						icon: 'none'
					})
					uni.navigateBack({ delta: 1 });
				}
			},
			async getData() {
				uni.showLoading({
					title: '加载中...',
					mask: true
				})
				try {
					let res = await this.$u.api.getRoomDetailInfo({
						devCode: this.devCode
					})
					return res;
				} catch (err) {
					console.error(err);
				} finally {
					setTimeout(() => {
						uni.hideLoading();
					}, 1000);
				}
			},
			goto3d(){
				if(!this.selectSpotType){
					uni.showModal({
						content: '请先选安装方式',
						showCancel: false,
						confirmText: '关闭'
					})
					return;
				}
				setTimeout(() => {
					console.log("跳转回小程序安装结果页:"+this.devCode)
					let extraData = {
						source: "2d",
						source2d: this.sourcePage,// 记录进2d页面时的来源，来确定提交时跳转的页面
						spotType: this.selectSpotType,
						devScene: this.devScene,
						devHeight:this.devHeight,
					}
					webUni.navigateTo({
						url: `/pages/webview/index?devCode=${this.devCode}&extraData=${JSON.stringify(extraData)}`
					});
				}, 300);
			},
			showPupop(type) {
				console.log("showPupop type:", type);
				switch (type) {
					case 888:
						this.showPupopType = "selectBed";
						break;
					case 999:
						this.showPupopType = "addGate";
						break;
					default:
						const region = this.dict.cls[type];
						this.showPupopType = "addRegion";
						this.addRegionIdx = region.code;
						console.log("regionTitle", this.regionTitle);
						break;
				}
			},
			// onSelectBedClose() {
			// 	this.selectBedIndex = undefined;
			// },
			// onAddGateClose() {
			// 	this.selectGateIndex = undefined;
			// },

			popupHide(popup) {
				this.showPupopType = undefined;
				this.addRegionIdx = undefined;
				switch (popup) {
					case 'addRegion':
						break;
					case 'selectBed':
						this.selectBedIndex = undefined;
						// 取消后需要删除临时添加的
						if (this.tempBedList.length > 0) {
							this.tempBedList.forEach(item1 => {
								const index = this.regionList.findIndex(item2 => item2.m_x === item1
									.m_x && item2.m_y === item1.m_y && item2.m_z === item1
									.m_z && item2.m_positionX === item1.m_positionX && item2.m_positionY ===
									item1.m_positionY);
								if (index !== -1) {
									this.regionList.splice(index, 1);
								}
							});
						}
						break;
					case 'addGate':
						this.selectGateIndex = undefined;
						// 取消后需要删除临时添加的
						if (this.tempGateList.length > 0) {
							this.tempGateList.forEach(item1 => {
								const index = this.gateList.findIndex(item2 => item2.direction === item1
									.direction && item2.length === item1.length && item2.width === item1
									.width && item2.height === item1.height);
								if (index !== -1) {
									this.gateList.splice(index, 1);
								}
							});
						}

						break;
				}
			},

			bedSelect(index, type) {
				this.selectBedIndex = index;

				const bedSize = this.dict.bedSize[index]
				this.selectBedSize = bedSize.text;
				if ('0x0' !== bedSize.code) {
					//this.selectBedSize = bedSize.text;
					let arr = bedSize.code.split("x");
					let bed = {
						x: 60,
						y: 60,
						w: this.m2pi(arr[0]),
						h: this.m2pi(arr[1]),
						z: this.m2pi(0.45),
						m_x: parseFloat(arr[0]),
						m_y: parseFloat(arr[1]),
						m_z: 0.45,
						editing: false,
						cls: type,
					};
					bed.m_positionX = this.pi2m(bed.x - this.room.x) + bed.m_x / 2;
					bed.m_positionY = this.pi2m((this.room.y + this.room.h) - (bed.y + bed.h)) + bed
						.m_y / 2;
					this.regionList.push(bed);
					this.tempBedList.push(bed);
					console.log("regionSelect:", bed);

				} else {
					// 自定义的显示新的弹出页
					this.addRegionIdx = 1; //cls里面的床
					this.showPupopType = "addRegion";
				}
				console.log("regionList:", this.regionList);
			},

			saveBed() {
				this.showPupopType = undefined;
				this.selectBedIndex = undefined;
				this.regionGateList = [];
			},

			saveRegion() {
				if (this.customBed.x > this.room.m_x || this.customBed.y > this.room.m_y || this.customBed.z > this.room
					.m_z) {
					uni.showToast({
						duration: 2000,
						title: '不能超出房间尺寸',
						icon: 'none',
					})
					return;
				}

				let bed = {
					x: 60,
					y: 60,
					w: this.m2pi(this.customBed.x),
					h: this.m2pi(this.customBed.y),
					z: this.m2pi(this.customBed.z),
					m_x: parseFloat(this.customBed.x),
					m_y: parseFloat(this.customBed.y),
					m_z: parseFloat(this.customBed.z),
					editing: false,
					cls: this.addRegionIdx,
				};
				this.showPupopType = undefined;
				this.customBed = {};
				bed.m_positionX = this.pi2m(bed.x - this.room.x) + bed.m_x / 2;
				bed.m_positionY = this.pi2m((this.room.y + this.room.h) - (bed.y + bed.h)) + bed.m_y / 2;
				console.log("bed:", bed);
				this.regionList.push(bed);
				this.tempBedList.push(bed);
				console.log("regionSelect:", bed);
				console.log("regionList:", this.regionList);
			},

			deleteBed(index) {
				uni.showModal({
					content: '确定删除吗？',
					showCancel: true,
					success: ({
						confirm,
						cancel
					}) => {
						if (cancel) {
							return;
						}
						this.regionList.splice(index, 1);
					}
				})
			},

			startBedEditing(item) {
				let distance1 = this.pi2m((this.room.x + this.room.w) - (item.x + item.w));
				let distance2 = this.pi2m((this.room.y + this.room.h) - (item.y + item.h));
				item.m_distance1 = parseFloat(distance1);
				item.m_distance1_old = parseFloat(distance1);
				item.m_distance2 = parseFloat(distance2);
				item.distance2_old = parseFloat(distance2);
				item.m_z_old = parseFloat(item.m_z);

				item.editing = true;
				console.log("startBedEditing:", item)

			},
			startDeviceEditing(item) {
				console.log("device:", item);
				//pi2m(device.x-room.x)
				// let distance1 = this.pi2m(item.x - this.room.x);
				//pi2m((room.y+room.h)-(device.y+device.h))
				// let distance2 = this.pi2m((this.room.y + this.room.h) - (item.y + item.h));
				item.m_x_old = parseFloat(item.m_x);
				item.m_y_old = parseFloat(item.m_y);
				item.m_z_old = parseFloat(item.m_z);

				item.editing = true;
				console.log("startDeviceEditing:", item)

			},
			startRoomEditing(item) {
				item.editing = true;
				console.log("startRoomEditing:", item)
			},
			// 计算离房间左边的距离,单位为m
			calcLeftDistanceOfRoomM(x) {
				return this.pi2m(x - this.room.x);
			},
			stopBedEditing(item) {
				console.log("item:", item)
				console.log("this.room:", this.room)
				if (this.checkRegionOutOfRoom(item)) {
					return;
				}
				if (item.m_distance1 != item.m_distance1_old) {
					item.x = this.room.x + this.room.w - item.w - this.m2pi(item.m_distance1);
					item.m_positionX = this.pi2m(item.x - this.room.x) + item.m_x / 2;

				}
				if (item.m_distance2 != item.m_distance2_old) {
					item.y = this.room.y + this.room.h - item.h - this.m2pi(item.m_distance2);
					//item.m_positionY =  1*item.m_distance2 + item.m_y / 2;
					item.m_positionY = this.pi2m((this.room.y + this.room.h) - (item.y + item.h)) + item.m_y / 2;
				}

				if (item.m_z != item.m_z_old) {
					item.z = this.m2pi(item.m_z);
				}
				item.editing = false;
				console.log("stopBedEditing:", item)
				uni.showToast({
					duration: 2000,
					title: '位置已更新',
					icon: 'none'
				})
			},
			stopDeviceEditing(item) {
				console.log("item:", item)
				console.log("this.room:", this.room)
				if (this.checkDeviceOutOfRoom(item)) {
					return;
				}
				if (item.m_x != item.m_x_old) {
					item.x = this.m2pi(item.m_x) + this.room.x;
				}
				if (item.m_y != item.m_y_old) {
					item.y = this.room.y + this.room.h - item.h - this.m2pi(item.m_y);
				}

				if (item.m_z != item.m_z_old) {
					item.z = this.m2pi(item.m_z);
				}
				item.editing = false;
				console.log("stopBedEditing:", item)
				uni.showToast({
					duration: 2000,
					title: '位置已更新',
					icon: 'none'
				})
			},
			checkRegionOutOfRoom(item) {
				if (item.m_distance1 < 0 || item.m_distance1 > this.room.m_x || item.m_distance2 < 0 || item.m_distance2 >
					this.room.m_y || item.m_z < 0 || item.m_z > this.room.m_z) {
					uni.showToast({
						duration: 2000,
						title: '不能超出房间尺寸',
						icon: 'none',
					})
					return true;
				}
				return false;
			},
			checkDeviceOutOfRoom(item) {
				// 侧装设备有可能装在墙上突出的地方
				/* if (this.selectSpotType == 2 && item.m_y > 0.2) {
					uni.showToast({
						duration: 2000,
						title: '侧装设备不能离开墙',
						icon: 'none',
					})
					return true;
				} */
				if (item.x < 0 || item.m_x > this.room.m_x || item.m_y < 0 || item.m_y >
					this.room.m_y || item.m_z < 0 || item.m_z > this.room.m_z) {
					uni.showToast({
						duration: 2000,
						title: '不能超出房间尺寸',
						icon: 'none',
					})
					return true;
				}
				return false;
			},
			stopRoomEditing(item) {
				// 需要重新修改比率，但是修改比率之后，所有的位置都需要重新调整
				// this.roomData.x = this.room.m_x;
				// this.roomData.y = this.room.m_y;
				// this.changePixel2cmRatio();
				if (this.room.m_x > 10 || this.room.m_y > 10 || this.room.m_z > 10) {
					uni.showToast({
						duration: 2000,
						title: '房间尺寸太大，设备检测范围为方圆5米',
						icon: 'none',
					})
					return;
				}
				item.w = parseFloat(this.m2pi(this.room.m_x));
				item.h = parseFloat(this.m2pi(this.room.m_y));
				item.editing = false;
				console.log("stopRoomEditing:", item)
				uni.showToast({
					duration: 2000,
					title: '房间尺寸已更新',
					icon: 'none'
				})
			},

			gateSelect(index) {
				this.selectGateIndex = index;
				let g = {
					direction: index,
					length: 1.4,
					width: 0.8,
					height: 2.0,
					editing: false,
				}
				const gate = this.gateConfog(g);
				this.gateList.push(gate);
				this.tempGateList.push(gate);
				console.log("gateSelect:", gate);
			},
			saveGate() {
				this.tempGateList = [];
				this.showPupopType = undefined;
				this.selectGateIndex = undefined;
			},

			deleteGate(index) {
				uni.showModal({
					content: '确定删除吗？',
					showCancel: true,
					success: ({
						confirm,
						cancel
					}) => {
						if (cancel) {
							return;
						}
						this.gateList.splice(index, 1);
					}
				})
			},

			spotTypeSelect(type) {
				this.selectSpotType = type;
				// 侧装
				if (this.selectSpotType == 2) {
					this.device.h = deviceWH / 2;
					this.device.y = this.room.y + this.room.h - deviceWH / 2;
					this.device.m_z = 2;
					this.device.m_y = 0.01;
				} else {
					this.device.h = deviceWH;
					this.device.y = this.device.y - deviceWH / 2;
					this.device.m_z = this.room.m_z;
				}
			},
			questionClick(type) {
				if (type == 1) {

				} else if (type == 2) {
					// uni.showModal({
					// 	content: '点击右侧图标，可向画布添加相应的区域',
					// 	showCancel: false,
					// 	confirmText: '关闭'
					// });
					uni.showToast({
						duration: 3000,
						title: '点击右侧图标，可向画布添加相应的区域',
						icon: 'none',
						position: 'bottom'
					})
				} else if (type == 3) {
					uni.showToast({
						duration: 3000,
						title: '点击卡片上的数值，可以切换成编辑模式',
						icon: 'none',
						position: 'bottom'
					})
				}
			},

			calcHateDistance(item) {
				let distance = 0;
				switch (item.direction) {
					case 0:
						distance = this.pi2m((this.room.y + this.room.h) - (item.y + item.h));
						break;
					case 1:
						distance = this.pi2m(item.x - this.room.x);
						break;
					case 2:
						distance = this.pi2m(item.y - this.room.y);
						break;
					case 3:
						distance = this.pi2m((this.room.x + this.room.w) - (item.x + item.w));
						break;
				}
				return parseFloat(distance);
			},
			startGateEditing(item) {
				let distance = this.calcHateDistance(item);
				item.m_distance1 = parseFloat(distance);
				item.m_distance1_old = parseFloat(distance);
				item.m_width_old = parseFloat(item.m_width);


				console.log("startGateEditing:", item)
				item.editing = true;
			},

			stopGateEditing(item) {
				let distance = 0;
				// 根据最新的位置和修改前的位置对比
				if (item.m_distance1 != item.m_distance1_old) {
					// 数值有变化，计算门新的位置，并移动
					switch (item.direction) {
						case 0:
							if (this.checkGateOutOfRoomY(item)) {
								return;
							}
							//distance = this.pi2m((this.room.y + this.room.h) - (item.y + item.h));
							item.y = this.room.y + this.room.h - item.h - this.m2pi(item.m_distance1);
							// 门的坐标
							break;
						case 1:
							if (this.checkGateOutOfRoomX(item)) {
								return;
							}
							//distance = this.pi2m(item.x - this.room.x);
							item.x = this.m2pi(item.m_distance1) + this.room.x;
							break;
						case 2:
							if (this.checkGateOutOfRoomY(item)) {
								return;
							}
							//distance = this.pi2m(item.y - this.room.y);
							item.y = this.m2pi(item.m_distance1) + this.room.y;
							break;
						case 3:
							if (this.checkGateOutOfRoomX(item)) {
								return;
							}
							//distance = this.pi2m((this.room.x + this.room.w) - (item.x + item.w));
							item.x = this.room.x + this.room.w - item.w - this.m2pi(item.m_distance1);
							break;
					}
				}
				if (item.m_width != item.m_width_old) {
					item.w = this.m2pi(item.m_width);
				}
				item.editing = false;
				item.m_x = parseFloat(item.m_distance1) + item.m_width / 2;
				console.log("stopGateEditing:", item)
				uni.showToast({
					duration: 2000,
					title: '位置已更新',
					icon: 'none'
				})
			},
			checkGateOutOfRoomX(item) {
				if (item.m_distance1 < 0 || item.m_distance1 > this.room.m_x || item.m_width < 0 || item.m_width > this
					.room.m_x) {
					uni.showToast({
						duration: 2000,
						title: '不能超出房间尺寸',
						icon: 'none',
					})
					return true;
				}
				return false;
			},
			checkGateOutOfRoomY(item) {
				if (item.m_distance1 < 0 || item.m_distance1 > this.room.m_y || item.m_width < 0 || item.m_width > this
					.room
					.m_y) {
					uni.showToast({
						duration: 2000,
						title: '不能超出房间尺寸',
						icon: 'none',
					})
					return true;
				}
				return false;
			},
			// 换算，长宽最多以350像素
			changePixel2cmRatio() {
				// 房间最大的边
				const roomMaxVal = this.roomData.x > this.roomData.y ? this.roomData.x : this.roomData.y;
				//const roomMaxVal = this.room.m_x > this.room.m_y ? this.room.m_x : this.room.m_y;
				// 房间尺寸和像素比
				this.pixel2cmRatio = roomAreaMaxValue / (roomMaxVal * 100);
				console.log("每厘米所占像素：" + this.pixel2cmRatio);
			},

			m2pi(x) {
				if (!x) {
					return 0;
				}
				// console.log("x:", x)
				if (!this.pixel2cmRatio) {
					this.changePixel2cmRatio();
				}
				let xx = parseFloat(x) * 100 * this.pixel2cmRatio;
				// console.log("xx:", xx)
				return parseFloat(xx);
			},
			//像素转米
			pi2m(x) {
				if (!x) {
					return 0;
				}
				// console.log("x2:", x)
				if (!this.pixel2cmRatio) {
					this.changePixel2cmRatio();
				}
				let xx = x / 100 / this.pixel2cmRatio;
				// console.log("xx2:", xx)
				return parseFloat(xx.toFixed(2));
			},
			// 计算门的位置
			gateConfog(gate) {
				const {
					direction, // 方位，0左，1前，右2，后3
					length, // 离设备所在墙的距离,站在室内面向门时离左边墙距离
					width, // 门宽度
					editing,
				} = gate;
				let x, y, w, h;
				const wPx = this.m2pi(width);
				console.log("门宽度：", wPx);
				const lenPx = this.m2pi(length);
				console.log("门离左墙距离：", lenPx);
				// 厚度
				const gateThick = 8;

				switch (direction) {
					case 0:
						x = this.room.x;
						y = this.room.y + this.room.h - lenPx - wPx / 2;
						w = gateThick;
						h = wPx;
						break;
					case 1:
						x = this.room.x + lenPx - wPx / 2;
						y = this.room.y;
						w = wPx;
						h = gateThick;
						break;
					case 2:
						x = this.room.x + this.room.w - gateThick;
						y = this.room.x + lenPx - wPx / 2;
						w = gateThick;
						h = wPx;
						break;
					case 3:
						x = this.room.x + this.room.w - lenPx - wPx / 2;
						y = this.room.y + this.room.h - gateThick;
						w = wPx;
						h = gateThick;
						break;
				}

				return {
					m_x: length,
					m_width: width,
					x: x,
					y: y,
					w: w,
					h: h,
					direction: direction,
					editing: editing,
				}
			},

			limitX(parent, pos, obj) {
				let x;
				if (pos.x < parent.x) {
					x = parent.x
				} else if (pos.x > parent.w + parent.x - obj.w) {
					x = parent.w + parent.x - obj.w;
				} else {
					x = pos.x;
				}
				return x;
			},

			limitY(parent, pos, obj) {
				let y
				if (pos.y < parent.y) {
					y = parent.y
				} else if (pos.y > parent.h + parent.y - obj.h) {
					y = parent.h + parent.y - obj.h;
				} else {
					y = pos.y;
				}
				return y;
			},

			// 边框限制:只能在几条边上进行移动
			borderLimt(parent, pos, obj) {
				console.log("parent:", parent)
				console.log("pos", pos)
				console.log("obj", obj)
				let x, y;
				switch (obj.direction) {
					case 0:
						if (pos.x != parent.x) {
							x = parent.x;
						} else {
							x = obj.x;
						}

						y = this.limitY(parent, pos, obj);
						break;
					case 1:
						x = this.limitX(parent, pos, obj);

						if (pos.y != parent.y) {
							y = parent.y;
						} else {
							y = obj.y;
						}
						break;
					case 2:
						if (pos.x != parent.x + parent.w - obj.w) {
							x = parent.x + parent.w - obj.w;
						} else {
							x = obj.x;
						}

						// 
						y = this.limitY(parent, pos, obj);
						break;
					case 3:
						x = this.limitX(parent, pos, obj);
						if (pos.y != parent.y + parent.h - obj.h) {
							y = parent.y + parent.h - obj.h;
						} else {
							y = obj.y;
						}
						break;
				}
				return {
					x: x,
					y: y,
				}
			},

			// 区域限制：只能在父级的区域进行移动
			areaLimt(parent, pos, obj) {
				return {
					x: this.limitX(parent, pos, obj),
					y: this.limitY(parent, pos, obj)
				};
			},
			handleDragstart(e) {
				console.log("handleDragstart:", e.target.id())
				this.dragItemId = e.target.id();
				if (e.target.attrs?.type == 'gate') {
					this.dragGateIndex = e.target.attrs.index;
					console.log("dragGateIndex:", this.dragGateIndex)
				} else if (e.target.attrs?.type == 'region') {
					this.dragRegionIndex = e.target.attrs.index;
					console.log("dragRegionIndex:", this.dragRegionIndex)
				}
			},
			bedDragBoundFunc(pos) {
				//console.log("bedDragBoundFunc:",pos);
				return this.areaLimt(this.room, pos, this.regionList[this.dragRegionIndex]);
			},
			deviceDragBoundFunc(pos) {
				//console.log("bedDragBoundFunc:",pos);
				if (this.selectSpotType == 2) {
					return this.borderLimt(this.room, pos, {
						...this.device,
						direction: 3
					});
				}
				return this.areaLimt(this.room, pos, this.device);
			},
			gateDragBoundFunc(pos) {
				//console.log("gateDragBoundFunc:",pos);
				return this.borderLimt(this.room, pos, this.gateList[this.dragGateIndex]);
			},
			handleDragend(e) {
				console.log("handleDragend:", e)
				console.log(e.target.attrs)
				if (e.target.id() === "room") {
					this.room.x = e.target.attrs.x;
					this.room.y = e.target.attrs.y;
				} else if (e.target.id() === "device") {
					this.device.x = e.target.attrs.x;
					this.device.y = e.target.attrs.y;
					this.device.m_x = this.pi2m(this.device.x - this.room.x)
					this.device.m_y = this.pi2m((this.room.y + this.room.h) - (this.device.y + this.device.h));
					console.log("this.device", this.device);
				}
				if (e.target.attrs?.type == 'region') {
					const idx = e.target.attrs.index;
					let item = this.regionList[idx];
					let x = e.target.attrs.x;
					let y = e.target.attrs.y;
					// 某些情况下，会出现拖出去了的情况,2个像素降低敏感度
					if (e.target.attrs.x - 2 < this.room.x) {
						console.log("超出房间左边");
						x = this.room.x;
					} else if (e.target.attrs.x > this.room.x + this.room.w - item.w + 2) {
						console.log("超出房间右边");
						x = this.room.x + this.room.w - item.w;
					}
					if (e.target.attrs.y - 2 < this.room.y) {
						console.log("超出房间上边");
						y = this.room.y;
					} else if (e.target.attrs.y > this.room.y + this.room.h - item.h + 2) {
						console.log("超出房间下边");
						y = this.room.y + this.room.h - item.h;
					}

					item.x = x;
					item.y = y
					item.m_positionX = this.pi2m(item.x - this.room.x) + item.m_x / 2;
					item.m_positionY = this.pi2m((this.room.y + this.room.h) - (item.y + item.h)) + item
						.m_y / 2;

					//e.target.getLayer().batchDraw();
					console.log("regionList[idx]", item);
				}
				if (e.target.attrs?.type == 'gate') {
					const idx = e.target.attrs.index;
					let item = this.gateList[idx];
					item.x = e.target.attrs.x;
					item.y = e.target.attrs.y;
					let distance = this.calcHateDistance(item);
					item.m_x = distance + item.m_width / 2;
					console.log("gateList[idx]", item);
				}
				this.dragItemId = null;
				this.dragGateIndex = null;
				this.dragRegionIndex = null;
			},
			bedDblclick(e) {
				console.log("bedDblclick:", e)
				console.log(e.target.attrs)
				if (e.target.attrs?.type == 'region') {
					const idx = e.target.attrs.index;
					let item = this.regionList[idx];
					let bed = e.target;
					// e.target.offsetX((e.target.w + e.target.x) / 2);
					// e.target.offsetY((e.target.h + e.target.y) / 2);
					//动画旋转
					/* var anim = new Konva.Tween({
						node: bed,
						duration: 0.2,
						rotation: bed.rotation() + 90
					});
					// item.offsetX = item.w / 2;
					// itemitem.offsetY = item.h / 2;
					// 开始动画
					anim.play();
					anim.finish(); */

					/* bed.rotate(90);
					const {
						x,
						y
					} = bed.getAbsolutePosition();
					console.log(x,y);
					item.x = x - bed.width() / 2;
					item.y = y - bed.height() / 2;
					bed.position({
						x: item.x,
						y: item.y
					});
					bed.getLayer().draw(); */

					// 长宽互换变形
					let h = item.h;
					let w = item.w;
					e.target.width(h);
					e.target.height(w);
					item.h = w;
					item.w = h;
					let mx = item.m_x;
					let my = item.m_y;
					item.m_x = my;
					item.m_y = mx;
					item.m_positionX = this.pi2m(item.x - this.room.x) + item.m_x / 2;
					item.m_positionY = this.pi2m((this.room.y + this.room.h) - (item.y + item.h)) + item
						.m_y / 2;
					e.target.getLayer().batchDraw();
					console.log("item:", item);
					// }else if( e.target.id().startsWith("bedTextR")){
					// 	this.addInput(e.target.y(),e.target.x(),'11');
				}
			},
			stageClick(e) {
				console.log(e.target.attrs);
			},
			movevToButtom(e) {
				console.log("bedDblclick:", e)
				console.log(e.target.attrs)
				if (e.target.attrs?.type == 'region' || e.target.attrs?.type == 'gate') {
					e.target.moveToBottom();
					e.target.getLayer().batchDraw();
				}
			},
			async submitRoomData() {
				if(!this.devCode){
					console.log("无效的devCode")
					uni.showModal({
						content: '设备无效',
						showCancel: false,
						confirmText: '关闭'
					})
				}
				let params = {
					roomVO: {
						roomId: this.devCode,
						x: parseFloat(this.room.m_x),
						y: parseFloat(this.room.m_y),
						z: parseFloat(this.room.m_z),
					},

					roomSensorVO: {
						roomId: this.devCode,
						x: parseFloat(this.device.m_x),
						y: parseFloat(this.device.m_y),
						z: parseFloat(this.device.m_z),
						spotType: parseInt(this.selectSpotType),
						angleEtilt: this.selectSpotType == 2 ? 30 : 90,
					},
				};
				if (this.selectSpotType == 2) {
					params.roomVO.leftX = parseFloat(this.device.m_x);
					params.roomVO.rightX = parseFloat(this.room.m_x) - parseFloat(this.device.m_x);
				}
				if (this.gateList) {
					let roomGateVOList = [];
					for (let i = 0; i < this.gateList.length; i++) {
						console.log("gateList[i]:", this.gateList[i]);
						let gate = {
							roomId: this.devCode,
							gid: i + 1,
							direction: this.gateList[i].direction,
							length: parseFloat(this.gateList[i].m_x),
							width: parseFloat(this.gateList[i].m_width),
							// 高度默认2米
							height: 2,
						};
						roomGateVOList.push(gate);
					}

					params = {
						...params,
						roomGateVOList
					}
				}
				if (this.regionList) {
					let roomRegionVOList = [];
					for (let i = 0; i < this.regionList.length; i++) {
						const item = this.regionList[i];
						console.log("regionList[i]:", item);
						let bed = {
							roomId: this.devCode,
							rid: i + 1,
							cls: parseInt(item.cls),
							positionX: parseFloat(item.m_positionX),
							positionY: parseFloat(item.m_positionY),
							scaleX: parseFloat(item.m_x),
							scaleY: parseFloat(item.m_y),
							scaleZ: parseFloat(item.m_z),
							rotation: 0,
						};
						roomRegionVOList.push(bed);
					}

					params = {
						...params,
						roomRegionVOList
					}
				}
				console.log("params:", params);
				let res = await this.$u.api.saveRoomDetailInfo(params);
				if (res) {
					//uni.showToast({ duration: 2000, title: '保存成功', icon: 'none' })
					// uni.navigateBack({ delta: 1 });
					// 正常都跳转到状态页
					//小程序中因为当前页的konvas显示不出来，所以当前页实际上是用webview嵌入的
					// 所以需要从当前也跳回小程序
					if (this.from == 'miniapp') {
						console.log("从小程序跳转进来的")
						uni.showToast({
							duration: 2000,
							title: '保存成功',
							icon: 'none'
						})
						// webUni.navigateTo({
						// 	url: `/pagesDevice/config/config-result?status=${res}`
						// })

						console.log("navigateToMiniProgram");

						// uni.navigateToMiniProgram({
						// 	appId: 'wxda02870032b8f928',
						// 	path: `/pagesDevice/config/config-result?status=${res}`,
						// 	extraData: {},
						// 	envVersion: 'develop', //开发版：develop，体验版：trial，正式版：release
						// 	success: function() {
						// 		console.log('跳转成功')
						// 	}
						// });


						// WeixinJSBridge.invoke('navigateToMiniProgram', {
						// 	appId: 'wxda02870032b8f928',
						// 	path: `/pagesDevice/config/config-result?status=${res}`,
						// 	extraData: {},
						// 	envVersion: 'develop' //开发版：develop，体验版：trial，正式版：release
						// });


						// wx.miniProgram.navigateTo({
						// 	url: `/pagesDevice/config/config-result?status=${res}`,
						// })
						// 发消息
						webUni.postMessage({
							data: {
								msg: 'h5准备跳转到小程序',
								url: `/pagesDevice/config/vali-wait`,
							}
						});
						if (this.sourcePage == "setting") {
							setTimeout(() => {
								console.log("跳转回小程序设置页")
								// webUni.navigateTo({
								// 	url: `/pagesFamily/device/notActive-v2`
								// });
								// 导致左上角变成主页按钮
								// webUni.reLaunch({
								// 	url: `/pagesFamily/device/notActive-v2`
								// });
								let num = 1;
								// 从3d返回时，提交后还在2d页面，所以跳两次
								if(this.roomDataFrom3D){
									num = 2;
								}
								webUni.navigateBack({
									delta: num
								});
							}, 300);
						} else {
							// 跳转
							setTimeout(() => {
								console.log("跳转回小程序安装结果页")
								webUni.navigateTo({
									url: `/pagesDevice/config/vali-wait`
								});
							}, 300);
						}
						//webUni.navigateBack();
						// uni.navigateBack();
					} else {
						console.log("h5内部保存，不是小程序")
						uni.showToast({
							duration: 2000,
							title: '保存成功',
							icon: 'none'
						})
						//this.$navTo(`/pagesDevice/config/config-result?status=${res}`);
						// uni.redirectTo({
						// 	url: `/pagesDevice/config/config-result?status=${res}`
						// })
					}
				} else {
					uni.showToast({
						duration: 2000,
						title: '保存失败,请重试',
						icon: 'none'
					})
				}
			},

			addInput(x, y, value, callback) {
				console.log("x,y", x, y);
				const input = document.createElement("input");
				input.value = value;
				input.style.position = "absolute";
				input.style.top = `${y}px`;
				input.style.left = `${x}px`;
				input.style.width = `40px`;
				input.style.height = `30px`;
				input.style.fontSize = `14px`;
				// input.style.color = ;
				input.style.border = "2px";
				input.style.padding = "0";
				input.style.margin = "0";
				input.style.outline = "none";
				input.style.background = "none";
				document.body.appendChild(input);
				input.focus();
				// 监听输入框的失焦事件
				input.addEventListener("blur", () => {
					input.remove();
					console.log("input.value:", input.value);
					//callback(input.value);
				});
			},
		},
	}
</script>
<style lang="scss" scoped>
	@import url("/static/css/iconfont.css");

	.main {
		background-color: #FfFFFF;

		.stage {
			box-shadow: 0rpx 24rpx 48rpx 0rpx rgba(0, 0, 0, 0.04);
			border-radius: 16rpx;

			.stage_descr {
				.desc {
					height: 36rpx;
					font-size: 26rpx;
					color: #999999;
				}

				.unit {
					height: 36rpx;
					margin-top: 12rpx;
					font-size: 22rpx;
					color: #3D3D3D;
				}
			}
		}

		.room_data {
			padding-bottom: 400rpx;

			.room_data_item {
				margin: 32rpx 32rpx 0 32rpx;
				padding: 32rpx;
				box-shadow: 0rpx 24rpx 48rpx 0rpx rgba(0, 0, 0, 0.04);
				border-radius: 16rpx;

				.name {
					color: #333333;
					font-size: 32rpx;
				}

				.desr {
					color: #999999;
					font-size: 22rpx;
					margin-left: 16rpx;
				}

				.delete {
					font-size: 52rpx;
				}

				.data {
					margin-top: 16rpx;

					.text {
						width: 70rpx;
						line-height: 64rpx;
						font-size: 26rpx;
						color: #666666;
					}

					.x {
						font-size: 40rpx;
						color: #666666;
						padding-left: 16rpx;
						padding-right: 16rpx;
					}

					.value {
						width: 120rpx;
						font-size: 40rpx;
						color: #333333;
						// padding: 16rpx;
					}
				}
			}
		}

		.popup-aera {
			.grid-bed {
				flex-wrap: wrap;
				margin: 32rpx;

			}

			.bed-item {
				width: 180rpx;
				height: 72rpx;
				background-color: #EEEEEE;
				border-radius: 16rpx;
				margin-left: 16rpx;
				margin-right: 16rpx;
				margin-top: 32rpx;
			}

			.text {
				color: #333333;
				font-size: 32rpx;
				font-weight: 500;
				text-align: center;
				white-space: nowrap;
				line-height: 36rpx;
			}
		}
	}

	.footer-btns {
		position: fixed;
		// bottom: 40rpx;
		// left: 40rpx;
		// right: 40rpx;

		// top: calc(100% - 330rpx);
		bottom: 0rpx;
		width: 100%;
		padding-left: 40rpx;
		padding-right: 40rpx;
		padding-bottom: 40rpx;
		z-index: 999;
		margin: 0 auto;
		background-color: #FfFFff;

		.next-btn {
			display: block;
			margin-top: 20rpx;
		}

		.text {
			font-size: 32rpx;
			color: #666;
		}

		.tubiao {
			font-size: 64rpx;
			color: #666;
		}

		.tag {
			width: 120rpx;
			height: 60rpx;
			background-color: #EEEEEE;
			border-radius: 16rpx;
			margin-left: 16rpx;
			margin-right: 16rpx;
			margin-top: 32rpx;
		}
	}
</style>
