<template>
	<view class="device-region-edit-page" :style="{ 'padding-bottom': rid ? '240rpx' : '140rpx' }">
		<view class="top-header-line"></view>
		
		<view>
			<view class="ta-c" style="width: 710rpx; height: 710rpx;background-size: 710rpx 710rpx"
				:style="{'background-image': `url(${staticUrl}/images/device/room-config/area-${spotType}.jpg)`}">
				<view class="cfg-btn" style="position: relative;top: 420rpx;left: 90rpx;color: #24bdf0;border:1rpx solid #24bdf0;">
					{{null2val(form.positionX, '未设置')}}
				</view>
				<view class="cfg-btn" style="position: relative;top: 260rpx;color: #257457;border:1rpx solid #257457;"
						:style="{left:`${spotType == spotTypeSideConst ? '260rpx' : '320rpx'}`}">
					{{null2val(form.positionY, '未设置')}}
				</view>
				<view class="cfg-btn" style="position: relative;top: 300rpx;left: 400rpx;color: #f29971;border:1rpx solid #f29971;">
					{{null2val(form.scaleX, '未设置')}}
				</view>
				<view class="cfg-btn" style="position: relative;top: 310rpx;left: 560rpx;color: #257457;border:1rpx solid #257457;">
					{{null2val(form.scaleY, '未设置')}}
				</view>
				<view class="cfg-btn" style="position: relative;top: 380rpx;left: 560rpx;color: #9deeda;border:1rpx solid #9deeda;">
					{{null2val(form.scaleZ, '未设置')}}
				</view>
			</view>

			<u-field
				v-model="form.rid"
				label="区域"
				placeholder="请输入"
				required
				disabled
			>
			</u-field>
			<u-field
				label="名称"
				class="direction-field"
				disabled
				required
			>
				<view slot="right" style="position: absolute; top: 104rpx; left: 46rpx; right: 46rpx;">
					<view v-for="(item, index) in dict.directions" :key="index" style="display: inline-block; text-align: left; margin-right: 50rpx;" @click="handleItemClick(item)">
						<text v-if="radioValue === item.code" class="icon iconfont icon-xuanzhong radio" style="color: #01B09A"></text>
						<text v-else class="icon iconfont icon-weixuanzhong radio"></text>
						<text style="margin-left: 14rpx; color: #888;">{{ item.name }}</text>
					</view>
				</view>
				<!-- <u-radio-group slot="right" v-model="radioValue" @change="radioGroupChange" style="position: absolute; top: 94rpx; left: 46rpx; right: 46rpx;" width="25%">
					<u-radio 
						v-for="(item, index) in dict.directions" :key="index" 
						:name="item.code"
					>
						{{item.name}}
					</u-radio>
				</u-radio-group> -->
			</u-field>
			<u-field
				v-model="form.positionX"
				label="长(米)"
				type="digit"
				placeholder="请输入"
				required
			>
			</u-field>
			<u-field
				v-model="form.positionY"
				label="宽(米)"
				type="digit"
				placeholder="请输入"
				required
			>
			</u-field>
			<u-field
				v-model="form.scaleX"
				label="床长(米)"
				type="digit"
				placeholder="请输入"
				required
			>
			</u-field>
			<u-field
				v-model="form.scaleY"
				label="床宽(米)"
				type="digit"
				placeholder="请输入"
				required
			>
			</u-field>
			<u-field
				v-model="form.scaleZ"
				label="床高(米)"
				type="digit"
				placeholder="请输入"
				required
			>
			</u-field>
			<u-field
				v-model="form.rotation"
				style="display: none;"
				label="rotation(°)"
				type="digit"
				placeholder="请输入"
				required
			>
			</u-field>
		</view>
		
		<view class="footer-btns">
			<!-- <u-button v-if="checked === true" type="primary" shape="circle" class="scan-code-btn" @click="scan">下一步</u-button> -->
			<!-- <u-button type="primary" shape="circle" class="next-btn" @click="handleSave">保存</u-button>
			<u-button v-if="rid" type="primary" shape="circle" class="next-btn" plain @click="handleOpenDeleteDialog">删除</u-button> -->
			
			<u-button shape="circle" class="next-btn diy-btn" size="medium" 
			:custom-style="{ 'width': '100%', 'color': 'white', 'background-color': '#01B09A' }"  hover-class="none"
			@click="handleSave">保存</u-button>
			<u-button v-if="rid" shape="circle" class="next-btn diy-btn" size="medium" 
			:custom-style="{ 'width': '100%', 'color': '#DE4D4A', 'background-color': '#f7f7f7' }"  hover-class="none"
			@click="handleOpenDeleteDialog">删除</u-button>
		</view>
		
		<u-modal v-model="deleteDialog" title="您确定要删除配置信息？" :title-style="{color: '#ff1f1f'}" :show-cancel-button="true" @confirm="handleConfirmDelete">
			<view class="slot-content" style="padding: 20rpx;">
			</view>
		</u-modal>
	</view>
</template>

<script>
	import * as util from '@/utils/util';
	export default {
		data() {
			return {
				staticUrl: this.$u.http.config.staticBaseUrl,
				deleteDialog: false,
				radioValue: '1',
				dict: {
					directions: [
						// {
						// 	code: '0',
						// 	name: '0'
						// },
						{
							code: '1',
							name: '床'
						},
						// {
						// 	code: '2',
						// 	name: '1'
						// },
						// {
						// 	code: '3',
						// 	name: '2'
						// }
					]
				},
				isEdit: false,
				rid: undefined,
				form: {
					rid: undefined,
					cls: undefined,
					positionX: undefined,
					positionY: undefined,
					scaleX: undefined,
					scaleY: undefined,
					scaleZ: undefined,
					rotation: 0,
				},

				spotTypeSideConst: "2",
				spotType: "1"
			}
		},
		onLoad(option) {
			if (option.spotType) {
				this.spotType = option.spotType;
			}
			this.rid = option.rid
			this.form.cls = this.radioValue;
			if (option.rid == null || option.rid == undefined) {
				let _rid = uni.getStorageSync('rid');
				if (_rid) {
					_rid = parseInt(_rid);
					this.form.rid = _rid + 1
				} else {
					this.form.rid = 1
				}
			} else {
				this.fetchRegionInfo(option.rid)
			}
		},
		methods: {
			null2val:util.null2val,
			handleItemClick(item) {
				this.radioValue = item.code
				this.form.cls = this.radioValue
			},
			// 选中任一radio时，由radio-group触发
			radioGroupChange(v) {
				this.form.cls = v
			},
			handleOpenDeleteDialog() {
				this.deleteDialog = true
			},
			fetchRegionInfo(rid) {
				uni.showLoading({
					title: '加载中...',
					mask: true
				})
				this.$u.api.fetchRoomRegionInfo({ devCode: uni.getStorageSync('devCode'), rid: rid }).then(res => {
					if (res) {
						this.form = res
						if (res.cls != null && res.cls != undefined) {
							this.radioValue = res.cls + ''
						}
					}
				}).catch(err => {
					uni.showToast({ duration: 2000, title: res.message || '发生未知错误', icon: 'none' })
				}).finally(() => {
					setTimeout(() => {
						uni.hideLoading();
					}, 1000);
				})
			},
			handleConfirmDelete() {
				
				uni.showLoading({
					title: '保存中...',
					mask: true
				})
				this.$u.api.execDelRoomRegion({ devCode: uni.getStorageSync('devCode'), rid: this.rid }).then(res => {
					uni.showToast({ duration: 2000, title: '删除区域位置成功', icon: 'none' })
					// this.deleteDialog = true
					setTimeout(() => {
						uni.navigateBack({
							delta: 1
						})
					}, 500);
				}).catch(err => {
					console.log('err', err)
				}).finally(() => {
					setTimeout(() => {
						uni.hideLoading();
					}, 1000);
				})
			},
			isNumber(n) {
				return !isNaN(parseFloat(n)) && isFinite(n);
			},
			handleSave() {
				// if (!this.form.rid || (this.form.cls == null && this.form.cls == undefined) || !this.form.positionX ||
				// 	!this.form.positionY || !this.form.scaleX || !this.form.scaleY || !this.form.scaleZ || !this.form.rotation) {
				if ((this.form.rid == null && this.form.rid == undefined) || (this.form.cls == null && this.form.cls == undefined) || 
					(this.form.positionX == null && this.form.positionX == undefined) ||
					(this.form.positionY == null && this.form.positionY == undefined) ||
					(this.form.scaleX == null && this.form.scaleX == undefined) ||
					(this.form.scaleY == null && this.form.scaleY == undefined) ||
					(this.form.scaleZ == null && this.form.scaleZ == undefined)) {
					uni.showModal({ content: '请检查“*”必填项是否填写正确', showCancel: false, confirmText: '关闭' })
					return;
				}
				
				// if (!this.isNumber(this.form.positionX) || !this.isNumber(this.form.positionY) || !this.isNumber(this.form.scaleX) ||
				// 	!this.isNumber(this.form.scaleY) || !this.isNumber(this.form.scaleZ) || !this.isNumber(this.form.rotation)) {
				if (!this.isNumber(this.form.positionX) || !this.isNumber(this.form.positionY) || !this.isNumber(this.form.scaleX) ||
					!this.isNumber(this.form.scaleY) || !this.isNumber(this.form.scaleZ)) {
					uni.showModal({ content: '请检查填写内容是否为合法数字', showCancel: false, confirmText: '关闭' })
					return;
				}
				
				if (parseFloat(this.form.positionX) < 0 || parseFloat(this.form.positionY) < 0 || parseFloat(this.form.scaleX) < 0 || 
					parseFloat(this.form.scaleY) < 0 || parseFloat(this.form.scaleZ) < 0) {
					uni.showModal({ content: '请检查填写内容是否大于0', showCancel: false, confirmText: '关闭' })
					return;
				}
				
				// if (parseFloat(this.form.rotation) < 0) {
				// 	uni.showModal({ content: '请检查填写内容(rotation)是否大于等于0', showCancel: false, confirmText: '关闭' })
				// 	return;
				// }
				
				let _params = {
					roomId: uni.getStorageSync('devCode'),
					...this.form,
				}
				uni.showLoading({
					title: '保存中...',
					mask: true
				})
				this.$u.api.execSaveRoomRegion(_params).then(res => {
					uni.setStorageSync('rid', this.form.rid)
					uni.showToast({ duration: 2000, title: '区域位置保存成功', icon: 'none' })
					setTimeout(() => {
						uni.navigateBack({
							delta: 1
						})
					}, 1000);
				}).catch(err => {
					console.error(err);
				}).finally(() => {
					setTimeout(() => {
						uni.hideLoading();
					}, 1000);
				})
			},
		}
	}
</script>

<style lang="scss">
::v-deep .u-round-circle {
	&::after {
		border: none !important;
	}
}
::v-deep input {
	text-align: right;
	color: #888;
}
</style>
<style lang="scss" scoped>
@import url("/static/css/iconfont.css");
.device-region-edit-page {
	padding-bottom: 240rpx;
	.cfg-btn {
		position: relative;
		width: 120rpx;
		height: 48rpx;
		line-height: 48rpx;
		color: #FFFFFF;
		font-size: 26rpx;
		border-radius: 2em;
	}
	.icon-reqired {
		color: red;
		display: inline-block;
		vertical-align: middle;
		font-size: 40rpx;
		position: relative;
		top: 4rpx;
		left: 6rpx;
	}
	::v-deep .u-label {
		font-size: 32rpx;
		width: 350rpx;
		display: block;
		color: #0D0D0D;
		flex: initial !important;
		&::before {
			font-size: 40rpx;
			top: 10rpx;
		}
		.u-label-text {
			padding-left: 20rpx;
			font-weight: bold;
			box-sizing: border-box;
		}
	}
	.direction-field {
		::v-deep .u-field-inner {
			height: 120rpx;
			align-items: baseline;
		}
	}
	.footer-btns {
		position: fixed;
		z-index: 100;
		bottom: 40rpx;
		left: 40rpx;
		right: 40rpx;
		margin: 0 auto;
		.next-btn {
			display: block;
			margin-top: 20rpx;
		}
	}
	// ::v-deep .uni-easyinput__content {
	// 	padding: 14rpx 6rpx;
	// }
	::v-deep .u-model__footer__button {
		height: 88rpx;
		line-height: 88rpx;
	}
	::v-deep .u-field {
		padding: 28rpx;
	}
}
</style>
