<template>
	<view class="device-room-size-page">
		<view class="top-header-line"></view>

		<view class="sketch-img"
			:style="{ 'background-image': `url(${staticUrl}/images/device/room-config/room-${form.spotType}.jpg)` }">
			<view class="label" :class="[`label-x-${form.spotType}`]">
				{{ null2val(form.x, '未设置') }}
			</view>
			<view class="label" :class="[`label-y-${form.spotType}`]">
				{{ null2val(form.y, '未设置') }}
			</view>
			<view class="label" :class="[`label-z-${form.spotType}`]">
				{{ null2val(form.z, '未设置') }}
			</view>

			<template v-if="form.spotType == spotTypeSideConst">
				<view class="label" :class="[`label-leftX`]">
					{{ null2val(form.leftX,'未设置') }}
				</view>
				<view class="label" :class="[`label-rightX`]">
					{{ null2val(form.rightX, '未设置') }}
				</view>
			</template>
		</view>

		<view>
			<view class="u-field u-border-bottom">
				<view class="r-flex u-field-inner u-label-postion-left">
					<view class="u-label" style="justify-content: flex-start; flex: 0 0 80px;">
						<text class="u-label-text">{{ '安装方式' }}</text>
					</view>
					<view class="fild-body" style="margin-left: 32rpx;">
						<view class="r-flex-1">
							<u-radio-group v-model="form.spotType" @change="handleSpotTypechange">
								<u-radio v-for="(item, index) in dict.spotType" :key="index" :name="item.code" >
									{{ item.text }}
								</u-radio>
							</u-radio-group>
						</view>
					</view>
				</view>
			</view>
			<template v-if="form.spotType === spotTypeSideConst">
				<view class="czx">
					<u-field v-model="form.x" label="长(米)" type="digit" required disabled
						style="background-color:lightgray">
				</u-field>
				</view>
				<u-field v-model="form.leftX" label="左长(米)" type="digit" placeholder="请输入" required>
				</u-field>
				<u-field v-model="form.rightX" label="右长(米)" type="digit" placeholder="请输入" required>
				</u-field>
			</template>
			<template v-else>
				<u-field v-model="form.x" label="长(米)" type="digit" placeholder="请输入" required>
				</u-field>
			</template>

			<u-field v-model="form.y" label="宽(米)" type="digit" placeholder="请输入" required>
			</u-field>
			<u-field v-model="form.z" label="高(米)" type="digit" placeholder="请输入" required>
			</u-field>
		</view>

		<view class="footer-btns">
			<u-button shape="circle" class="next-btn diy-btn" size="medium"
				:custom-style="{ 'width': '100%', 'color': 'white', 'background-color': '#01B09A' }" hover-class="none"
				@click="handleSave">{{source == "dev" ? "保存" : "下一步"}}</u-button>
		</view>

	</view>
</template>

<script>
	import * as util from '@/utils/util';
	export default {
		data() {
			return {
				staticUrl: this.$u.http.config.staticBaseUrl,
				form: {
					x: undefined,
					y: undefined,
					z: undefined,
					spotType: "2",
					angleEtilt: null,
				},
				dbSpotType:"",
				actionSheetTips: {
					text: '请选择',
					color: '#909399',
					fontSize: 24
				},
				dict: {
					spotType: [{
							code: '1',
							text: '顶装'
						},
						{
							code: '2',
							text: '侧装'
						}
					]
				},

				deviceConfigEnum: "deviceConfigEnum",
				// 侧装常量
				spotTypeSideConst: "2",
				spotTypeName: '',
				source: null,
			}
		},
		computed: {
			x() {
				if (this.form.spotType == "1"){
					return;
				}
				if (!this.form.leftX || !this.form.rightX) {
					return;
				}
				const x = Number.parseFloat(this.form.leftX) + Number.parseFloat(this.form.rightX);
				if (Number.isNaN(x)) {
					this.form.x = undefined;
				} else {
					this.form.x = x.toFixed(2);
				}
			}
		},
		onLoad(options) {
			// 这句没用，下面的请求会覆盖这个值
			//this.form.spotType = options.spotType || "1";
			this.source = options.source;
		},
		async onShow() {
			this.devCode = uni.getStorageSync('devCode');
			if (!this.devCode) {
				uni.showModal({
					content: '设备无效',
					showCancel: false,
					confirmText: '关闭'
				})
				return;
			}

			await this.fetchInfo();

			/**
			 * {
						room:{
							x:3.5,
							y:3.5,
							z:2.8
						}
						type：1,  //1顶装，2侧装
						angle:45
					}
			 */
			const roomData = getApp().globalData?.roomData;
			if (roomData) {
				const roomId = this.devCode;
				//TODO 安装角度没有保存，没看到哪个页面上有这个角度
				const {
					room: {
						x,
						y,
						z
					},
					leftX,
					rightX,
					type
				} = roomData;
				const spotType = this.form.spotType = `${type}`;
				const angleEtilt = this.form.angleEtilt;

				this.form = Object.assign(this.form, {
					roomId,
					x,
					y,
					z,
					leftX,
					rightX,
					spotType,
					angleEtilt
				});
			}

			const spotTypeDict = this.dict.spotType.find(item => item.code == this.form.spotType);
			this.spotTypeName = spotTypeDict?.text;
		},
		methods: {
			null2val: util.null2val,
			async fetchInfo() {
				uni.showLoading({
					title: '加载中...',
					mask: true
				})
				try {
					let res = await this.$u.api.fetchRoomInfo({
						devCode: this.devCode
					}) 
					if(res){
						this.form = res;
						this.dbSpotType = res.spotType;
					}
				} catch (err) {
					console.error(err);
				} finally {
					setTimeout(() => {
						uni.hideLoading();
					}, 1000);
				}
			},
			isNumber(n) {
				return !isNaN(parseFloat(n)) && isFinite(n);
			},
			async handleSave() {
				if ((this.form.x == null && this.form.x == undefined) ||
					(this.form.y == null && this.form.y == undefined) ||
					(this.form.z == null && this.form.z == undefined)) {
					uni.showModal({
						content: '请检查“*”必填项是否填写正确',
						showCancel: false,
						confirmText: '关闭'
					})
					return;
				}
				if (!this.isNumber(this.form.x) || !this.isNumber(this.form.y) || !this.isNumber(this.form.z)) {
					uni.showModal({
						content: '请检查填写内容是否为合法数字',
						showCancel: false,
						confirmText: '关闭'
					})
					return;
				}
				if (parseFloat(this.form.x) < 0 || parseFloat(this.form.y) < 0 || parseFloat(this.form.z) < 0) {
					uni.showModal({
						content: '请检查填写内容是否大于0',
						showCancel: false,
						confirmText: '关闭'
					})
					return;
				}

				if (this.form.spotType == this.spotTypeSideConst) {
					if (!(this.form.leftX ?? this.form.rightX)) {
						uni.showModal({
							content: '请检查“*”必填项是否填写正确',
							showCancel: false,
							confirmText: '关闭'
						})
						return;
					}
					if (!this.isNumber(this.form.leftX) || !this.isNumber(this.form.rightX)) {
						uni.showModal({
							content: '请检查填写内容是否为合法数字',
							showCancel: false,
							confirmText: '关闭'
						})
						return;
					}
					if (parseFloat(this.form.leftX) < 0 || parseFloat(this.form.rightX) < 0) {
						uni.showModal({
							content: '请检查填写内容是否大于0',
							showCancel: false,
							confirmText: '关闭'
						})
						return;
					}
				}
				if (this.form.spotType == "2") {
					await this.angleNotice();
				} else if (this.form.spotType == "1") {
					this.form.angleEtilt = 90;
				}
				let _params = {
					roomId: this.devCode,
					...this.form,
				}
				uni.showLoading({
					title: '保存中...',
					mask: true
				})
				this.$u.api.execSaveRoom(_params).then(res => {
					getApp().globalData = undefined;

					uni.showToast({
						duration: 2000,
						title: '房屋尺寸保存成功',
						icon: 'none'
					});
					setTimeout(() => {
						if (this.source == 'dev') {
							uni.navigateBack({
								delta: 1
							});
						} else {
							this.$navTo(`pagesDevice/config/sensor?spotType=${this.form.spotType}`);
						}
					}, 1000);
				}).catch(err => {
					console.log('err', err)
				}).finally(() => {
					setTimeout(() => {
						uni.hideLoading();
					}, 1000);
				})
			},
			handleSpotTypechange(code) {
				console.log(code);
				if (this.dbSpotType == code) {
					return;
				}
				if (this.form.spotType != this.dbSpotType) {
					uni.showModal({
						content: '设备安装方式发生变化，请确保房间信息正确',
						showCancel: false,
						confirmText: '关闭'
					});
				}
			},
			async angleNotice() {
				const val = this.form.y;
				if (!val || !this.isNumber(val)) {
					return;
				}
				let floatX = Number.parseFloat(val);
				let content;
				/*
				if (floatX >= 0 && floatX <= 3) {
					content = `根据宽度的计算，请将设备安装角度调整到45度`;
					this.form.angleEtilt = 45;
				} else if (floatX > 3 && floatX <= 6) {
					content = `根据宽度的计算，请将设备安装角度调整到30度`;
					this.form.angleEtilt = 30;
				} else {
					content = `根据宽度的计算，请将设备安装角度调整到15度`;
					this.form.angleEtilt = 15;
				}
				*/
			    // 统一按照30度
				content = `请将设备安装角度调整到30度`;
				this.form.angleEtilt = 30;
				console.log("长度为："+val+"\t角度为："+this.form.angleEtilt)
				return new Promise((resolve, reject) => {
					uni.showModal({
						content,
						showCancel: false,
						confirmText: '关闭',
						success() {
							resolve(true)
						}
					})
				})
			}
		}
	}
</script>

<style lang="scss">
	::v-deep input {
		text-align: right;
		color: #888;
	}
</style>

<style lang="scss" scoped>
	.czx{
		background-color: lightgray!important;
	}	
	.device-room-size-page {
		padding-bottom: 140rpx;

		.sketch-img {
			width: 710rpx;
			height: 710rpx;
			background-size: 710rpx 710rpx;

			.label {
				position: relative;
				border-radius: 2em;
				width: 120rpx;
				height: 48rpx;
				line-height: 48rpx;
				font-size: 26rpx;
				text-align: center;
			}

			.label-x-1 {
				top: 548rpx;
				left: 30rpx;
				color: #f29971;
				border: 1rpx solid #f29971;
			}

			.label-y-1 {
				top: 510rpx;
				left: 500rpx;
				color: #327f5b;
				border: 1rpx solid #327f5b;
			}

			.label-z-1 {
				top: 360rpx;
				left: 350rpx;
				color: #9deeda;
				border: 1rpx solid #9deeda;
			}

			.label-x-2 {
				display: none;
			}

			.label-y-2 {
				top: 596rpx;
				left: 110rpx;
				color: #327f5b;
				border: 1rpx solid #327f5b;
			}

			.label-z-2 {
				top: 450rpx;
				left: 306rpx;
				color: #9deeda;
				border: 1rpx solid #9deeda;
			}

			.label-leftX {
				top: 546rpx;
				left: 350rpx;
				color: #10b6dc;
				border: 1rpx solid #10b6dc;
			}

			.label-rightX {
				top: 410rpx;
				left: 500rpx;
				color: #e67843;
				border: 1rpx solid #e67843;
			}
		}

		.icon-reqired {
			color: red;
			display: inline-block;
			vertical-align: middle;
			font-size: 40rpx;
			position: relative;
			top: 4rpx;
			left: 6rpx;
		}

		.left-title {
			.device-name {
				color: #0D0D0D;
				font-size: 30rpx;
			}
		}

		.right-val {
			color: #888888;
			text-align: right;

			.activate-now {
				padding: 8rpx 20rpx;
				background: #01B09A;
				border-radius: 45rpx;
				color: white;
				font-size: 24rpx;
				line-height: initial;
				text-align: center;
				// position: relative;
				// top: 0rpx;
				display: inline-block;
			}

			::v-deep .u-icon {
				margin-left: 10rpx;
				position: relative;
				top: -2rpx;
			}
		}

		::v-deep .u-label {
			font-size: 32rpx;
			color: #0D0D0D;

			&::before {
				font-size: 40rpx;
				top: 10rpx;
			}

			.u-label-text {
				// margin-left: 20rpx;
				font-weight: bold;
			}
		}

		.footer-btns {
			position: fixed;
			z-index: 100;
			bottom: 40rpx;
			left: 40rpx;
			right: 40rpx;
			margin: 0 auto;

			.next-btn {
				display: block;
				margin-top: 20rpx;
			}
		}

		::v-deep .u-field {
			padding: 28rpx;
		}
	}
</style>
