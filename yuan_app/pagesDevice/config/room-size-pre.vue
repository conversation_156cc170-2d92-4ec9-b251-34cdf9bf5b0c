<template>
	<view class="page-wrap">

		<view class="content">
			温馨提示：当前只支持顶装设备的房间标定功能，如需手动输入，请点击‘手动设置’
		</view>

		<view class="footer-btns">
			<u-button shape="circle" class="scan-code-btn diy-btn" size="medium"
				:custom-style="{ 'width': '230rpx', 'color': '#01B09A', 'background-color': 'white' }" hover-class="none"
				@click="toRoomCalibration">标定房间</u-button>

			<u-button shape="circle" class="scan-code-btn diy-btn" size="medium"
				:custom-style="{ 'width': '230rpx', 'color': 'white', 'background-color': '#01B09A' }" hover-class="none"
				@click="$navTo(`pagesDevice/config/room-size`)">手动设置</u-button>
		</view>
	</view>
</template>

<script>
import { gotoRoomCalibration } from "../../utils/gotoOutside";
export default {
	data() {
		return {

		}
	},
	onLoad(option) {
	},
	methods: {
		toRoomCalibration() {
			const devCode = uni.getStorageSync('devCode');
			gotoRoomCalibration(devCode);
		},
	}
}
</script>

<style lang="scss" scoped>
@import url("/static/css/iconfont.css");

.page-wrap {
	height: 100vh;
	overflow: hidden;
	text-align: center;
	// padding-top: 120rpx;

	.title {
		text-align: left;
		font-size: 32rpx;
		// padding-left: 10rpx;
		margin: 40rpx 10rpx 20rpx 20rpx;
	}

	.content {
		text-align: left;
		font-size: 28rpx;
		margin: 100rpx 40rpx;
	}

	.footer-btns {
		position: absolute;
		bottom: 40rpx;
		left: 40rpx;
		right: 40rpx;
		margin: 0 auto;
		display: flex;
		justify-content: space-evenly;

		.scan-code-btn {
			display: block;
			margin-top: 37rpx;
		}
	}

}
</style>
