<template>
	<view class="device-door-page">
		<view class="top-header-line"></view>
		<view v-for="(gate, index) in roomGates" :key="index" class="item-block">
			<u-row gutter="16" @click="handleEditDoor(gate.gid)">
				<u-col span="4" style="color: #0D0D0D">
					<!-- gid -->
					<view class="icon-reqired" style="margin-right:10rpx;">*</view>门方位
				</u-col>
				<u-col span="4" style="color: #0D0D0D">
					<!-- {{ gate.gid }} -->
					{{dict.directions[gate.direction]}}
				</u-col>
				<u-col span="4" text-align="right">
					<view class="set-completed">编辑</view><u-icon name="arrow-right" class="icon-arrow-right"></u-icon>
				</u-col>
			</u-row>
		</view>
		
		<view class="footer-btns">
			<u-button shape="circle" class="next-btn diy-btn" size="medium" 
				:custom-style="{ 'width': '100%', 'color': '#01B09A', 'background-color': 'white' }"  hover-class="none"
				@click="handleAddDoor">+新增门方位</u-button>

			<u-button v-if="source ==`dev`" shape="circle" class="next-btn diy-btn" size="medium" 
				:custom-style="{ 'width': '100%', 'color': 'white', 'background-color': '#01B09A' }"  hover-class="none"
				@click="toBack()">完成</u-button>
			<u-button v-else shape="circle" class="next-btn diy-btn" size="medium" 
				:custom-style="{ 'width': '100%', 'color': 'white', 'background-color': '#01B09A' }"  hover-class="none"
				@click="$navTo(`pagesDevice/config/area?spotType=${spotType}`)">下一步</u-button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				roomGates: [],
				deviceConfigEnum: "deviceConfigEnum",
				source: null,
				spotType: "1",
				dict: {
					directions: {
						'0': '0',
						'1': '1',
						'2': '2',
						'3': '3',
					},
				},
			}
		},
		onLoad(options) {
			if (options.spotType) {
				this.spotType = options.spotType;
			}
			this.source = options.source;
			console.log(this.source);
		},
		onShow() {
			this.fetchRoomGateInfo();
		},
		methods: {
			fetchRoomGateInfo() {
				uni.showLoading({
					title: '加载中...',
					mask: true
				})
				this.$u.api.fetchListRoomGateInfo({ devCode: uni.getStorageSync('devCode') }).then(res => {
					this.roomGates = res
					let _gid = uni.getStorageSync('gid');
					if (res && res.length && res[res.length - 1].gid > _gid) {
						uni.setStorageSync('gid', res[res.length - 1].gid)
					}
				}).catch(err => {
					console.log('err', err)
					uni.showToast({ duration: 2000, title: res.message || '发生未知错误', icon: 'none' })
				}).finally(() => {
					setTimeout(() => {
						uni.hideLoading();
					}, 1000);
				})
			},
			handleAddDoor() {
				this.$navTo(`pagesDevice/config/door-edit?spotType=${this.spotType}`)
			},
			handleEditDoor(gid) {
				this.$navTo(`pagesDevice/config/door-edit?gid=${gid}&spotType=${this.spotType}`)
			},
			toBack() {
				uni.navigateBack({ delta: 1 });
			}
		}
	}
</script>

<style lang="scss" scoped>
.device-door-page {
	.item-block {
		box-shadow: 0px 8rpx 24rpx 0rpx rgba(0, 0, 0, 0.1);
		border-radius: 16rpx;
		padding: 20rpx 20rpx;
		margin: 30rpx;
		font-size: 28rpx;
	}
	.icon-reqired {
		color: red;
		display: inline-block;
		vertical-align: middle;
		font-size: 40rpx;
		position: relative;
		top: 4rpx;
		left: 6rpx;
	}
	.icon-reqired {
		color: red;
		display: inline-block;
		vertical-align: middle;
		font-size: 40rpx;
		position: relative;
		top: 4rpx;
		left: 6rpx;
	}
	.set-completed {
		display: inline-block;
		color: #8B8B8B;
		vertical-align: middle;
		margin-right: 6rpx;
	}
	.icon-arrow-right {
		vertical-align: middle;
		color: #8b8b8b;
		position: relative;
		top: 2rpx;
	}
	.footer-btns {
		position: absolute;
		bottom: 40rpx;
		left: 40rpx;
		right: 40rpx;
		margin: 0 auto;
		.next-btn {
			display: block;
			margin-top: 20rpx;
		}
	}
}
</style>
