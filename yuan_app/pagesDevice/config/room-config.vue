<template>
	<view class="device-room-config-page">
		<view class="top-header-line"></view>
		<view v-if="source === 'notActive'">
			<view style="font-size: 24rpx; padding-bottom: 26rpx; padding-left: 26rpx; border-bottom: 2rpx solid #f1f1f1; background: white; padding-top: 26rpx;">{{ deviceInfo.devName || deviceInfo.devCode }}</view>
			<view class="item-block">
				<u-row gutter="16" @click="$navTo('pages/address/index?source=roomConfig')">
					<u-col span="8" style="color: #0D0D0D">
						安装地址<view class="icon-reqired">*</view>
					</u-col>
					<u-col span="4" text-align="right">
						<view :class="{ 'set-completed': address, 'set-uncompleted': !address }">{{ address ? '已设置' : '未设置' }}</view><u-icon name="arrow-right" class="icon-arrow-right"></u-icon>
					</u-col>
				</u-row>
			</view>
			<view class="item-block">
				<u-row gutter="16" style="border-bottom: none;" @click="$navTo('pagesDevice/sceneConfig?source=roomConfig')">
					<u-col span="8" style="color: #0D0D0D">
						房间场景<view class="icon-reqired">*</view>
					</u-col>
					<u-col span="4" text-align="right">
						<view :class="{ 'set-completed': deviceInfo.devSceneName, 'set-uncompleted': !deviceInfo.devSceneName }">{{ deviceInfo.devSceneName || '未设置' }}</view><u-icon name="arrow-right" class="icon-arrow-right"></u-icon>
					</u-col>
				</u-row>
			</view>
		</view>
		
		<view v-if="source === 'notActive'" style="font-size: 24rpx; padding-bottom: 26rpx; padding-left: 26rpx; border-bottom: 2rpx solid #f1f1f1; margin-top: 20rpx; background: white; padding-top: 26rpx;">房间信息</view>
		<view v-else style="font-size: 24rpx; padding-bottom: 26rpx; padding-left: 26rpx; border-bottom: 2rpx solid #f1f1f1; background: white; padding-top: 26rpx;">房间信息</view>
		<view class="item-block">
			<u-row gutter="16" @click="handleSetRoomSize">
				<u-col span="8" style="color: #0D0D0D">
					房间尺寸<view class="icon-reqired">*</view>
				</u-col>
				<u-col span="4" text-align="right">
					<view :class="{ 'set-completed': statusInfo.roomVO === true, 'set-uncompleted': !statusInfo.roomVO }">{{ statusInfo.roomVO === true ? '已设置' : '未设置' }}</view><u-icon name="arrow-right" class="icon-arrow-right"></u-icon>
				</u-col>
			</u-row>
		</view>
		<view class="item-block">
			<u-row gutter="16" @click="handleSetSensor">
				<u-col span="8" style="color: #0D0D0D">
					设备方位<view class="icon-reqired">*</view>
				</u-col>
				<u-col span="4" text-align="right">
					<view :class="{ 'set-completed': statusInfo.roomSensorVO === true, 'set-uncompleted': !statusInfo.roomSensorVO }">{{ statusInfo.roomSensorVO === true ? '已设置' : '未设置' }}</view><u-icon name="arrow-right" class="icon-arrow-right"></u-icon>
				</u-col>
			</u-row>
		</view>
		<view class="item-block">
			<u-row gutter="16" @click="handleSetDoor">
				<u-col span="8" style="color: #0D0D0D">
					门方位
				</u-col>
				<u-col span="4" text-align="right">
					<view :class="{ 'set-completed': statusInfo.listRoomGateVO === true, 'set-uncompleted': !statusInfo.listRoomGateVO }">{{ statusInfo.listRoomGateVO === true ? '已设置' : '未设置' }}</view><u-icon name="arrow-right" class="icon-arrow-right"></u-icon>
				</u-col>
			</u-row>
		</view>
		<view class="item-block">
			<u-row gutter="16" style="border-bottom: none;" @click="handleSetArea">
				<u-col span="8" style="color: #0D0D0D">
					区域
				</u-col>
				<u-col span="4" text-align="right">
					<view :class="{ 'set-completed': statusInfo.listRoomRegionVO === true, 'set-uncompleted': !statusInfo.listRoomRegionVO }">{{ statusInfo.listRoomRegionVO === true ? '已设置' : '未设置' }}</view><u-icon name="arrow-right" class="icon-arrow-right"></u-icon>
				</u-col>
			</u-row>
		</view>
		
		<view class="footer-btns">
			<!-- <u-button v-if="checked === true" type="primary" shape="circle" class="scan-code-btn" @click="scan">下一步</u-button> -->
			<!-- <u-button type="primary" shape="circle" class="next-btn" @click="handleSave">提交</u-button> -->
			<u-button shape="circle" class="next-btn diy-btn" size="medium" 
			:custom-style="{ 'width': '100%', 'color': 'white', 'background-color': '#01B09A' }"  hover-class="none"
			@click="handleSave">提交</u-button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				value: '',
				deviceName: '',
				deviceNameDialog: false,
				areaActived: undefined,
				isChange: false,
				statusInfo: {
					listRoomGateVO: false,
					listRoomRegionVO: false,
					roomSensorVO: false,
					roomVO: false,
				},
				currDevId: undefined,
				deviceInfo: {},
				address: '',
				source: ''
			}
		},
		onLoad(option) {
			this.source = option.source;
			this.currDevId = option.devId
		},
		onShow() {
			this.fetchStatusInfo();
			this.fetchDeviceInfo();
		},
		methods: {
			handleSetRoomSize() {
				uni.navigateTo({
					url: `/pagesDevice/config/room-size`
				})
			},
			handleSetSensor() {
				uni.navigateTo({
					url: `/pagesDevice/config/sensor`
				})
			},
			handleSetDoor() {
				uni.navigateTo({
					url: `/pagesDevice/config/door`
				})
			},
			handleSetArea() {
				uni.navigateTo({
					url: `/pagesDevice/config/area`
				})
			},
			fetchStatusInfo() {
				uni.showLoading({
					title: '加载中...',
					mask: true
				})
				this.$u.api.fetchConfStatusInfo({ devCode: uni.getStorageSync('devCode') }).then(res => {
					this.statusInfo = res
				}).catch(err => {
					this.statusInfo = {}
				}).finally(() => {
					setTimeout(() => {
						uni.hideLoading();
					}, 1000);
				})
			},
			fetchDeviceInfo() {
				let _devId = uni.getStorageSync('devId') || this.currDevId;
				if (_devId) {
					this.$u.api.fetchDeviceInfo({ devId: uni.getStorageSync('devId') || this.currDevId }).then(res => {
						if (res) {
							this.deviceInfo = res
						}
					}).catch(err => {
						this.deviceInfo = {}
					})
					
					this.$u.api.fetchDevInstallationInfo({ devCode: uni.getStorageSync('devCode') }).then(res => {
						if (res) {
							this.address = res.houseAddr
						}
					}).catch(err => {
						this.address = ''
					})
				}
			},
			handleSave() {
				if (this.statusInfo.roomVO === false) {
					uni.showToast({ duration: 2000, title: '请填写房间尺寸信息', icon: 'none' })
					return;
				}
				if (this.statusInfo.roomSensorVO === false) {
					uni.showToast({ duration: 2000, title: '请填写设备方位信息', icon: 'none' })
					return;
				}
				uni.showLoading({
					title: '提交中...',
					mask: true
				})
				this.$u.api.execCommitRoomInfo({ devCode: uni.getStorageSync('devCode') }).then(res => {
					uni.navigateTo({
						url: `/pagesDevice/config/vali-wait`
					})
				}).catch(err => {
					console.log('err', err)
					uni.showToast({ duration: 2000, title: res.message || '发生未知错误', icon: 'none' })
				}).finally(() => {
					setTimeout(() => {
						uni.hideLoading();
					}, 1000);
				})
			}
		}
	}
</script>

<style lang="scss">
page {
	background: #f7f7f7;
}
</style>
<style lang="scss" scoped>
.device-room-config-page {
	height: 100%;
	// padding-top: 30rpx;
	.item-block {
		// box-shadow: 0px 8rpx 24rpx 0rpx rgba(0, 0, 0, 0.1);
		// border-radius: 16rpx;
		padding: 0rpx 20rpx;
		// margin: 30rpx;
		font-size: 28rpx;
		background: white;
		&:nth-child(1) {
			margin-top: 0rpx;
		}
	}
	u-row {
		display: block;
		line-height: 100rpx;
		border-bottom: 2rpx solid #f1f1f1;
		// padding: 18rpx 0;
	}
	//#ifndef MP-WEIXIN
	.u-row {
		line-height: 60px;
		border-bottom: 2rpx solid #ECECEC;
	}
	//#endif
	.icon-reqired {
		color: red;
		display: inline-block;
		vertical-align: middle;
		font-size: 40rpx;
		position: relative;
		top: 4rpx;
		left: 6rpx;
	}
	.set-completed {
		display: inline-block;
		color: #888888;
		vertical-align: middle;
		margin-right: 6rpx;
	}
	.set-uncompleted {
		display: inline-block;
		color: #C4C4C4;
		vertical-align: middle;
		margin-right: 6rpx;
	}
	.icon-arrow-right {
		vertical-align: middle;
		color: #888888;
		position: relative;
		top: 2rpx;
	}
	.title {
		font-size: 32rpx;
		color: #0D0D0D;
		// font-weight: bold;
	}
	.sub-title {
		font-size: 30rpx;
		color: #8B8B8B;
		margin-top: 10rpx;
		margin-bottom: 30rpx;
	}
	
	.footer-btns {
		position: absolute;
		bottom: 40rpx;
		left: 40rpx;
		right: 40rpx;
		margin: 0 auto;
		.next-btn {
			display: block;
			margin-top: 20rpx;
		}
	}
	
	::v-deep .uni-easyinput__content {
		padding: 14rpx 6rpx;
	}
	::v-deep .u-model__footer__button {
		height: 88rpx;
		line-height: 88rpx;
	}
}
</style>
