<template>
	<u-popup mode="bottom" v-model="visible" @close="onCancel">
		<view class="r-flex jc-sb popup-top">
			<text class="cancel" @click="onCancel">取消</text>
			<view class="c-flex jc-sb">
				<text v-if="title" class="title">{{ title }}</text>
				<text v-if="subtitle" class="selected">{{ subtitle }}</text>
			</view>
			<text class="confirm" @click="onConfirm">确定</text>
		</view>
		<view class="popup-centent">
			<slot></slot>
		</view>
	</u-popup>
</template>

<script>
	export default {
		name: "MyPopup",
		props: {
			visible: {
				type: <PERSON><PERSON>an,
				default: false
			},
			title: {
				type: String,
				default: undefined,
			},
			subtitle: {
				type: String,
				default: undefined,
			}
		},
		methods: {
			onCancel() {
				this.$emit("cancel");
			},
			onConfirm() {
				this.$emit("confirm");
			}
		}
	};
</script>

<style lang="scss" scoped>
	.popup-top {
		width: 686rpx;
		height: 88rpx;
		margin: 32rpx 0 16rpx 32rpx;

		.cancel {
			font-size: 32rpx;
			font-weight: 700;
			text-align: left;
			color: #01B09A;
		}

		.confirm {
			font-size: 32rpx;
			font-weight: 700;
			text-align: right;
			color: #01B09A;
		}

		.title {
			font-size: 36rpx;
			text-align: center;
			font-weight: 700;
			color: #515151;
		}

		.selected {
			margin-top: 16rpx;
			font-size: 26rpx;
			text-align: center;
			color: #999999;
			white-space: nowrap;
		}
	}

	.popup-centent {
		margin-bottom: 32rpx;
	}
</style>
