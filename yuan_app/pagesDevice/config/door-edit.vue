<template>
	<view class="device-door-edit-page" :style="{ 'padding-bottom': gid ? '240rpx' : '140rpx' }">
		<view class="top-header-line"></view>

		<view class="ta-c room-img" v-if="radioValue == '0'"
			:style="{'background-image': `url(${staticUrl}/images/device/room-config/door-${spotType}-${radioValue}.jpg)`}">
			<view class="cfg-btn" style="position: relative;top: 580rpx;left: 70rpx;color: #f29971;border:1rpx solid #f29971;">
				{{ null2val(form.glength, '未设置')}}
			</view>
			<view class="cfg-btn" style="position: relative;top: 250rpx;left: 120rpx;color: #257457;border:1rpx solid #257457;">
				{{ null2val(form.width, '未设置')}}
			</view>
			<view class="cfg-btn" style="position: relative;top: 370rpx;left: 300rpx;color: #9deeda;border:1rpx solid #9deeda;">
				{{ null2val(form.height, '未设置')}}
			</view>
		</view>

		<view class="ta-c room-img" v-else-if="radioValue == '1'"
			:style="{'background-image': `url(${staticUrl}/images/device/room-config/door-${spotType}-${radioValue}.jpg)`}">
			<view class="cfg-btn" style="position: relative;top: 490rpx;left: 170rpx;color: #f29971;border:1rpx solid #f29971;">
				{{ null2val(form.glength, '未设置')}}
			</view>
			<view class="cfg-btn" style="position: relative;top: 210rpx;left: 90rpx;color: #327f5b;border:1rpx solid #327f5b;">
				{{ null2val(form.width, '未设置')}}
			</view>
			<view class="cfg-btn" style="position: relative;top: 270rpx;left: 310rpx;color: #9deeda;border:1rpx solid #9deeda;">
				{{ null2val(form.height, '未设置')}}
			</view>
		</view>

		<view class="ta-c room-img" v-else-if="radioValue == '2'"
			:style="{'background-image': `url(${staticUrl}/images/device/room-config/door-${spotType}-${radioValue}.jpg)`}">
			<view class="cfg-btn" style="position: relative;color: #f29971;border:1rpx solid #f29971;"
					:style="{
						left: `${spotType == spotTypeSideConst ? '290rpx' : '370rpx;'}`,
						top: `${spotType == spotTypeSideConst ? '390rpx' : '430rpx;'}`
					}">
				{{ null2val(form.glength, '未设置')}}
			</view>
			<view class="cfg-btn" style="position: relative;top: 206rpx;left: 570rpx;color: #327f5b;border:1rpx solid #327f5b;">
				{{ null2val(form.width, '未设置')}}
			</view>
			<view class="cfg-btn" style="position: relative;top: 290rpx;left: 530rpx;color: #9deeda;border:1rpx solid #9deeda;">
				{{ null2val(form.height, '未设置')}}
			</view>
		</view>

		<view class="ta-c room-img" v-else-if="radioValue == '3'"
			:style="{'background-image': `url(${staticUrl}/images/device/room-config/door-${spotType}-${radioValue}.jpg)`}">
			<view class="cfg-btn" style="position: relative;top: 536rpx;left: 520rpx;color: #f29971;border:1rpx solid #f29971;">
				{{ null2val(form.glength, '未设置')}}
			</view>
			<view class="cfg-btn" style="position: relative;top: 290rpx;color: #327f5b;border:1rpx solid #327f5b;"
					:style="{'left': `${spotType == spotTypeSideConst ? '560rpx' : '370rpx;'}`}">
				{{ null2val(form.width, '未设置')}}
			</view>
			<view class="cfg-btn" style="position: relative;top: 306rpx;left: 616rpx;color: #9deeda;border:1rpx solid #9deeda;">
				{{ null2val(form.height, '未设置')}}
			</view>
		</view>

		<view class="ta-c" style="height: 710rpx;padding-top:200rpx;font-size: 32rpx;" v-else>
			请选择门方位
		</view>
		
		<view>
			<!-- <u-field v-model="form.gid" label="方位" placeholder="请输入" disabled></u-field> -->
			<u-field label="门方位" class="direction-field" disabled>
				<view slot="right" style="position: absolute; top: 104rpx; left: 46rpx; right: 46rpx;">
					<view v-for="(item, index) in dict.directions" :key="index" style="display: inline-block; text-align: left; margin-right: 50rpx;"
							@click="handleItemClick(item)">
						<text v-if="radioValue === item.code" class="icon iconfont icon-xuanzhong radio" style="color: #01B09A"></text>
						<text v-else class="icon iconfont icon-weixuanzhong radio"></text>
						<text style="margin-left: 14rpx; color: #888;">{{ item.name }}</text>
					</view>
				</view>
				<!-- <u-radio-group slot="right" v-model="radioValue" @change="radioGroupChange" style="position: absolute; top: 94rpx; left: 46rpx; right: 46rpx;" width="25%">
					<u-radio 
						v-for="(item, index) in dict.directions" :key="index" 
						:name="item.code"
					>
						{{item.name}}
					</u-radio>
				</u-radio-group> -->
			</u-field>
			<u-field
				v-model="form.glength"
				label="长(米)"
				type="digit"
				placeholder="请输入"
			>
			</u-field>
			<u-field
				v-model="form.width"
				label="宽(米)"
				type="digit"
				placeholder="请输入"
			>
			</u-field>
			<u-field
				v-model="form.height"
				label="高(米)"
				type="digit"
				placeholder="请输入"
			>
			</u-field>
		</view>
		
		<view class="footer-btns">
			<!-- <u-button v-if="checked === true" type="primary" shape="circle" class="scan-code-btn" @click="scan">下一步</u-button> -->
			<!-- <u-button type="primary" shape="circle" class="next-btn" @click="handleSave">保存</u-button>
			<u-button v-if="gid" type="primary" shape="circle" class="next-btn" plain @click="handleOpenDeleteDialog">删除</u-button> -->
			
			<u-button shape="circle" class="next-btn diy-btn" size="medium" 
			:custom-style="{ 'width': '100%', 'color': 'white', 'background-color': '#01B09A' }"  hover-class="none"
			@click="handleSave">保存</u-button>
			<u-button v-if="gid" shape="circle" class="next-btn diy-btn" size="medium" 
			:custom-style="{ 'width': '100%', 'color': '#DE4D4A', 'background-color': '#f7f7f7' }"  hover-class="none"
			@click="handleOpenDeleteDialog">删除</u-button>
		</view>
		
		<u-modal v-model="deleteDialog" title="您确定要删除配置信息？" :title-style="{color: '#ff1f1f'}" :show-cancel-button="true" @confirm="handleConfirmDelete">
			<view class="slot-content" style="padding: 20rpx;">
			</view>
		</u-modal>
	</view>
</template>

<script>
	import * as util from '@/utils/util';
	export default {
		data() {
			return {
				staticUrl: this.$u.http.config.staticBaseUrl,
				deleteDialog: false,
				radioValue: '0',
				dict: {
					directions: [
						{
							code: '0',
							name: '0'
						},
						{
							code: '1',
							name: '1'
						},
						{
							code: '2',
							name: '2'
						},
						{
							code: '3',
							name: '3'
						}
					]
				},
				isEdit: false,
				gid: undefined,
				form: {
					gid: undefined,
					direction: undefined,
					glength: undefined,
					width: undefined,
					height: undefined,
				},

				spotTypeSideConst: "2",
				spotType: "1",
			}
		},
		onLoad(option) {
			if (option.spotType) {
				this.spotType = option.spotType;
			}
			this.gid = option.gid
			if (option.gid == null || option.gid == undefined) {
				let _gid = uni.getStorageSync('gid');
				if (_gid) {
					_gid = parseInt(_gid);
					this.form.gid = _gid + 1
				} else {
					this.form.gid = 1
				}
			} else {
				this.fetchGateInfo(option.gid)
			}
		},
		methods: {
			null2val:util.null2val,
			handleItemClick(item) {
				this.radioValue = item.code;
				this.form.direction = this.radioValue;
			},
			// 选中任一radio时，由radio-group触发
			radioGroupChange(v) {
				this.form.direction = v
			},
			handleOpenDeleteDialog() {
				this.deleteDialog = true
			},
			fetchGateInfo(gid) {
				uni.showLoading({
					title: '加载中...',
					mask: true
				})
				this.$u.api.fetchRoomGateInfo({ devCode: uni.getStorageSync('devCode'), gid: gid }).then(res => {
					if (res) {
						this.form = res
						if (res.direction != null && res.direction != undefined) {
							this.radioValue = res.direction + ''
						}
						this.form.glength = res['length']
					}
				}).catch(err => {
					console.error(err);
				}).finally(() => {
					setTimeout(() => {
						uni.hideLoading();
					}, 1000);
				})
			},
			handleConfirmDelete() {
				uni.showLoading({
					title: '删除中...',
					mask: true
				})
				this.$u.api.execDelRoomGate({ devCode: uni.getStorageSync('devCode'), gid: this.gid }).then(res => {
					uni.showToast({ duration: 2000, title: '删除门位置成功', icon: 'none' })
					// this.deleteDialog = true
					setTimeout(() => {
						uni.navigateBack({
							delta: 1
						})
					}, 500);
				}).catch(err => {
					console.log('err', err)
					uni.showToast({ duration: 2000, title: '发生未知错误', icon: 'none' })
				}).finally(() => {
					setTimeout(() => {
						uni.hideLoading();
					}, 1000);
				})
			},
			isNumber(n) {
				return !isNaN(parseFloat(n)) && isFinite(n);
			},
			handleSave() {
				
				// if (!this.form.gid || (this.form.direction == null && this.form.direction == undefined) || 
				// 	!this.form.glength || !this.form.width || !this.form.height) {
				// 	uni.showModal({ content: '请检查“*”必填项是否填写正确', showCancel: false, confirmText: '关闭' })
				// 	return;
				// }
				
				if (!this.isNumber(this.form.glength) || !this.isNumber(this.form.width) || !this.isNumber(this.form.height)) {
					uni.showModal({ content: '请检查填写内容是否为合法数字', showCancel: false, confirmText: '关闭' })
					return;
				}
				
				if (parseFloat(this.form.glength) < 0 || parseFloat(this.form.width) < 0 || parseFloat(this.form.height) < 0) {
					uni.showModal({ content: '请检查填写内容是否大于等于0', showCancel: false, confirmText: '关闭' })
					return;
				}
				// 不选时默认值
				let direction = this.form.direction||this.radioValue;
				let _params = {
					roomId: uni.getStorageSync('devCode'),
					gid: direction,
					...this.form,
				}
				_params['length'] = this.form['glength']
				_params['direction'] = direction
				delete _params['glength']
				uni.showLoading({
					title: '保存中...',
					mask: true
				})
				this.$u.api.execSaveRoomGate(_params).then(res => {
					uni.setStorageSync('gid', this.form.gid)
					uni.showToast({ duration: 2000, title: '门位置保存成功', icon: 'none' })
					setTimeout(() => {
						uni.navigateBack({
							delta: 1
						})
					}, 1000);
				}).catch(err => {
					console.log('err', err)
					uni.showToast({ duration: 2000, title: res.message || '发生未知错误', icon: 'none' })
				}).finally(() => {
					setTimeout(() => {
						uni.hideLoading();
					}, 1000);
				})
			},
		}
	}
</script>

<style lang="scss">
::v-deep .u-round-circle {
	&::after {
		border: none !important;
	}
}
::v-deep input {
	text-align: right;
	color: #888;
}
</style>
<style lang="scss" scoped>
@import url("/static/css/iconfont.css");
.device-door-edit-page {
	padding-bottom: 240rpx;

	.room-img {
		width: 710rpx;
		height: 710rpx;
		background-size: 710rpx 710rpx;
	}
	.cfg-btn {
		position: relative;
		width: 120rpx;
		height: 48rpx;
		line-height: 48rpx;
		color: #FFFFFF;
		font-size: 26rpx;
		border-radius: 2em;
	}
	.icon-reqired {
		color: red;
		display: inline-block;
		vertical-align: middle;
		font-size: 40rpx;
		position: relative;
		top: 4rpx;
		left: 6rpx;
	}
	::v-deep .u-label {
		font-size: 32rpx;
		width: 250rpx;
		display: block;
		color: #0D0D0D;
		flex: initial !important;
		&::before {
			font-size: 40rpx;
			top: 10rpx;
		}
		.u-label-text {
			padding-left: 20rpx;
			font-weight: bold;
			box-sizing: border-box;
		}
	}
	.direction-field {
		::v-deep .u-field-inner {
			height: 120rpx;
			align-items: baseline;
		}
	}
	.footer-btns {
		position: fixed;
		z-index: 100;
		bottom: 40rpx;
		left: 40rpx;
		right: 40rpx;
		margin: 0 auto;
		.next-btn {
			display: block;
			margin-top: 20rpx;
		}
	}
	// ::v-deep .uni-easyinput__content {
	// 	padding: 14rpx 6rpx;
	// }
	::v-deep .u-model__footer__button {
		height: 88rpx;
		line-height: 88rpx;
	}
	::v-deep .u-field {
		padding: 28rpx;
	}
}
</style>
