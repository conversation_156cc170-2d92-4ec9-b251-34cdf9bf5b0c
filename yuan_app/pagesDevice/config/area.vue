<template>
	<view class="device-region-page">
		<view class="top-header-line"></view>
		<view v-for="(region, index) in roomRegions" :key="index" class="item-block">
			<u-row gutter="16" @click="handleEditRegion(region.rid)">
				<u-col span="4" style="color: #0D0D0D">
					rid<view class="icon-reqired">*</view>
				</u-col>
				<u-col span="4" style="color: #0D0D0D">
					{{ region.rid }}
				</u-col>
				<u-col span="4" text-align="right">
					<view class="set-completed">编辑</view><u-icon name="arrow-right" class="icon-arrow-right"></u-icon>
				</u-col>
			</u-row>
		</view>
		
		<view class="footer-btns">
			<!-- <u-button v-if="source == deviceConfigEnum" shape="circle" class="next-btn diy-btn" size="medium" 
				:custom-style="{ 'width': '100%', 'color': '#01B09A', 'background-color': 'white' }"  hover-class="none"
				@click="handleAddRegion">+新增区域方位</u-button>
			<u-button v-else shape="circle" class="next-btn diy-btn" size="medium" 
				:custom-style="{ 'width': '100%', 'color': 'white', 'background-color': '#01B09A' }"  hover-class="none"
				@click="submitRoomInfo">下一步</u-button> -->

			<u-button shape="circle" class="next-btn diy-btn" size="medium" 
				:custom-style="{ 'width': '100%', 'color': '#01B09A', 'background-color': 'white' }"  hover-class="none"
				@click="handleAddRegion">+新增区域方位</u-button>
<!--			<u-button shape="circle" class="next-btn diy-btn" size="medium" -->
<!--				:custom-style="{ 'width': '100%', 'color': 'white', 'background-color': '#01B09A' }"  hover-class="none"-->
<!--				@click="submitRoomInfo">提交</u-button>-->

      <u-button v-if="source ==`dev`" shape="circle" class="next-btn diy-btn" size="medium"
                :custom-style="{ 'width': '100%', 'color': 'white', 'background-color': '#01B09A' }"  hover-class="none"
                @click="toBack">完成</u-button>
      <u-button v-else shape="circle" class="next-btn diy-btn" size="medium"
                :custom-style="{ 'width': '100%', 'color': 'white', 'background-color': '#01B09A' }"  hover-class="none"
                @click="submitRoomInfo">提交</u-button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				roomRegions: [],
				deviceConfigEnum: "deviceConfigEnum",
        source: null,
				spotType: "1",
			}
		},
		onLoad(options) {
			if (options.spotType) {
				this.spotType = options.spotType;
			}
      this.source = options.source;
		},
		onShow() {
			this.fetchRoomRegionInfo();
		},
		methods: {
			fetchRoomRegionInfo() {
				uni.showLoading({
					title: '加载中...',
					mask: true
				})
				this.$u.api.fetchListRoomRegionInfo({ devCode: uni.getStorageSync('devCode') }).then(res => {
					this.roomRegions = res
					let _rid = uni.getStorageSync('rid');
					if (res && res.length && res[res.length - 1].rid > _rid) {
						uni.setStorageSync('rid', res[res.length - 1].rid)
					}
				}).catch(err => {
					console.error(err);
				}).finally(() => {
					setTimeout(() => {
						uni.hideLoading();
					}, 1000);
				})
			},
			handleAddRegion() {
				this.$navTo(`pagesDevice/config/area-edit?spotType=${this.spotType}`);
			},
			handleEditRegion(rid) {
				this.$navTo(`pagesDevice/config/area-edit?spotType=${this.spotType}&rid=${rid}`);
			},
			submitRoomInfo() {
				uni.showLoading({
					title: '提交中...',
					mask: true
				})
				this.$u.api.execCommitRoomInfo({ devCode: uni.getStorageSync('devCode') }).then(res => {
					uni.navigateTo({
						url: `/pagesDevice/config/vali-wait`
					})
				}).catch(err => {
					console.error(err);
				}).finally(() => {
					setTimeout(() => {
						uni.hideLoading();
					}, 1000);
				})
			},
      toBack() {
        uni.navigateBack({ delta: 1 });
      }
		}
	}
</script>

<style lang="scss" scoped>
.device-region-page {
	.item-block {
		box-shadow: 0px 8rpx 24rpx 0rpx rgba(0, 0, 0, 0.1);
		border-radius: 16rpx;
		padding: 20rpx 20rpx;
		margin: 30rpx;
		font-size: 28rpx;
	}
	.icon-reqired {
		color: red;
		display: inline-block;
		vertical-align: middle;
		font-size: 40rpx;
		position: relative;
		top: 4rpx;
		left: 6rpx;
	}
	.icon-reqired {
		color: red;
		display: inline-block;
		vertical-align: middle;
		font-size: 40rpx;
		position: relative;
		top: 4rpx;
		left: 6rpx;
	}
	.set-completed {
		display: inline-block;
		color: #8B8B8B;
		vertical-align: middle;
		margin-right: 6rpx;
	}
	.icon-arrow-right {
		vertical-align: middle;
		color: #8b8b8b;
		position: relative;
		top: 2rpx;
	}
	.footer-btns {
		position: absolute;
		bottom: 40rpx;
		left: 40rpx;
		right: 40rpx;
		margin: 0 auto;
		.next-btn {
			display: block;
			margin-top: 20rpx;
		}
	}
}
</style>
