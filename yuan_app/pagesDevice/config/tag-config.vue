<template>
	<view class="device-tag-config-page">
		<view class="main">
			<view class="top-header-line"></view>
			<view class="item-block">
				<view class="title">设备名称</view>
				<u-input v-model="deviceInfo.devName" :border="true"/>
			</view>
			
			<view v-if="checkDeviceType(deviceInfo,['1','2','4','5'])">
				<view class="item-block">
					<view class="title">安装场景</view>
					<view v-if="checkDeviceType(deviceInfo,['1','2','5'])">
						<view class="sub-title">床头模式可快速安装使用，全场景模式适合多种使用场景</view>
						<u-subsection :list="list" :current="curNow" active-color="#01B09A" @change="sectionChange"></u-subsection>
					</view>
					<u-row v-if="!bedSide" class="room-area-wrap">
						<u-col span="6">
							<view class="area-block" :class="{ 'actived': areaActived == '1' }" @click="handleChangeArea('1')">
								<text class="icon iconfont icon-keting" style="margin-bottom: 10rpx; font-size: 75rpx;" :style="{ 'color': areaActived == '1' ? 'white' : '#01B09A' }"></text>
								<view class="text">客厅</view>
							</view>
						</u-col>
						<u-col span="6">
							<view class="area-block" :class="{ 'actived': areaActived == '3' }" @click="handleChangeArea('3')">
								<text class="icon iconfont icon-woshi" style="margin-bottom: 10rpx; font-size: 75rpx" :style="{ 'color': areaActived == '3' ? 'white' : '#01B09A' }"></text>
								<view class="text">卧室</view>
							</view>
						</u-col>
						<u-col span="6">
							<view class="area-block" :class="{ 'actived': areaActived == '2' }" @click="handleChangeArea('2')">
								<text class="icon iconfont icon-weishengjian" style="margin-bottom: 10rpx; font-size: 75rpx;" :style="{ 'color': areaActived == '2' ? 'white' : '#01B09A' }"></text>
								<view class="text">卫生间</view>
							</view>
						</u-col>
						<u-col span="6">
							<view class="area-block" :class="{ 'actived': areaActived == '4' }" @click="handleChangeArea('4')">
								<text class="icon iconfont icon-qita" style="margin-bottom: 10rpx; font-size: 75rpx" :style="{ 'color': areaActived == '4' ? 'white' : '#01B09A' }"></text>
								<view class="text">其他</view>
							</view>
						</u-col>
					</u-row>
				</view>
				<!-- https://blog.csdn.net/qq_35921773/article/details/120799508-->
				<view v-if="!bedSide && checkDeviceType(deviceInfo,['1','2'])" class="item-block">
					<view class="title">安装方式</view>
					<view class="sub-title">错选会导致误报</view>
					<u-subsection :list="spotTypeNames" :current="curSpot" active-color="#01B09A" @change="spotTypeChange"></u-subsection>
				</view>
				
				<view v-if="!bedSide && checkDeviceType(deviceInfo,['1','2'])" class="item-block">
					<view v-if="spotType==1" class="title">安装高度(米)</view>
					<view v-else class="title">安装高度(米，建议安装高度2米)</view> 
					<u-input v-model="deviceInfo.devHeight" type="digit" :border="true" maxlength="4" placeholder="高度精确到厘米"/>
				</view>
				
				<view v-if="bedSide && checkDeviceType(deviceInfo,['1','2'])" class="item-block">
					<image :src="`${staticUrl}/images/device/bed-side.png`"></image>
				</view>
		
			</view>
		</view>
		
		<view v-if="checkDeviceType(deviceInfo,['1','2','5'])">
			<view v-if="!bedSide" class="footer-btns">
				<u-button shape="circle" class="next-btn" size="medium"
				:custom-style="{ 'width': '100%', 'color': 'white', 'background-color': '#01B09A' }" hover-class="none"
				 @click="handleSave">下一步</u-button>
			</view>
			<view v-else class="footer-btns">
				<u-button shape="circle" class="next-btn" size="medium" 
				:custom-style="{ 'width': '100%', 'color': 'white', 'background-color': '#01B09A' }" hover-class="none"
				 @click="submit">提交</u-button>
			</view>
		</view>
		<view v-else>
			<view class="footer-btns">
				<u-button shape="circle" class="next-btn" size="medium"
				:custom-style="{ 'width': '100%', 'color': 'white', 'background-color': '#01B09A' }" hover-class="none"
				 @click="handleSave">提交</u-button>
			</view>
		</view>
		
	</view>
</template>

<script>
	import * as util from '@/utils/util';
	export default {
		data() {
			return {
				staticUrl: this.$u.http.config.staticBaseUrl,
				// 默认全场景
				bedSide:false,
				curNow:1,
				token:null,
				list:[
					{
						name: '床头模式'
					}, 
					{
						name: '全场景模式'
					}
				],
				areaActived: undefined,
				isChange: false,
				deviceInfo: {
					id: undefined,
					devCode: undefined,
					devName: undefined,
					devScene: undefined,
					houseAddr: undefined,
					houseId: undefined,
					latitude: undefined,
					longitude: undefined,
					memberId: undefined,
					memberPhone: undefined,
					devHeight:undefined,
				},

				source: undefined,
				spotType:1,
				curSpot:1,
				spotTypeNames: [
						{
							name: '顶装'
						}, 
						{
							name: '侧装'
						}, 
					],
				}
		},
		onLoad(option) {
			this.source = option.source;
			this.fetchDeviceInfo();
			this.token = uni.getStorageSync('token');
		},
		methods: {
			checkDeviceType(device,typeArr){
				return typeArr.includes(device.deviceType);
			},
			sectionChange(index){
				this.curNow = index;
				if(this.curNow ===0){
					this.bedSide = true;
				}else{
					this.bedSide = false;
				}
			},
			spotTypeChange(index){
				console.log("index:"+index)
				this.curSpot = index;
				this.spotType = index+1;
				console.log("this.spotType:"+this.spotType)
				if(this.spotType == 2){
					this.deviceInfo.devHeight = 2;
				}
			},
			isNumber(n) {
				return !isNaN(parseFloat(n)) && isFinite(n);
			},
			async handleChangeArea(areaCode) {
				this.isChange = true
				this.areaActived = areaCode
				if(this.deviceInfo.devName){
					let _devName = "";
					// 1客厅，2卫生间，3卧室，4其他
					if (areaCode == '1') {
						_devName = '客厅'
					} else if (areaCode == '2') {
						_devName = '卫生间'
					} else if (areaCode == '3') {
						_devName = '卧室'
					} else if (areaCode == '4') {
						_devName = '其他'
					}
					
					let curr_family = uni.getStorageSync('curr_family')
					console.log("curr_family:",curr_family);
					const {id} = curr_family;
					const devCount = await this.$u.api.countByFamilyIdAndDevScene({ familyId: id, devScene:areaCode});
					if(devCount>0){
						_devName+=devCount;
					}
					this.deviceInfo.devName = _devName;
				}
			},
			fetchDeviceInfo() {
				uni.showLoading({
					title: '加载中...',
					mask: true
				})
				this.$u.api.fetchDevInstallationInfo({ devCode: uni.getStorageSync('devCode') }).then(res => {
					if (res) {
						console.log("devInfo:",res)
						this.deviceInfo = res
						this.areaActived = res.devScene;
						this.curNow = res.bedSide==1?0:1;
						this.bedSide = res.bedSide==1;
						this.curSpot = res.spotType==1?0:1;
						this.spotType = res.spotType;
					}
				}).catch(err => {
					this.deviceInfo = {}
				}).finally(() => {
					setTimeout(() => {
						uni.hideLoading();
					}, 1000);
				})
			},
			handleSave() {
				if (!this.deviceInfo.devName) {
					this.deviceInfo.devName = this.deviceInfo.devCode || ''
				}
				if (!this.deviceInfo.devName) {
					uni.showToast({ duration: 2000, title: '请填写名称', icon: 'none' })
					return;
				}
				if(this.deviceInfo.deviceType!='3'){
					if (!this.areaActived) {
						uni.showToast({ duration: 2000, title: '请选择设备场景', icon: 'none' })
						return;
					}
					if(this.deviceInfo.deviceType!='4'){
						if (!this.isNumber(this.deviceInfo.devHeight)) {
							uni.showToast({ duration: 2000, title: '请检查高度是否为合法数字', icon: 'none' })
							return;
						}
						if (parseFloat(this.deviceInfo.devHeight) < 0) {
							uni.showToast({ duration: 2000, title: '请检查高度是否大于0', icon: 'none' })
							return;
						}
					}
				}
				debugger;
				let _devScene = this.areaActived;
				let _currFamily = uni.getStorageSync('curr_family') || {}
				uni.showLoading({
					title: '保存中...',
					mask: true
				})
				if (uni.getStorageSync('devId')) {
					let _params = {
						id: uni.getStorageSync('devId'),
						devScene: _devScene,
						houseId: this.deviceInfo.houseId,
						devName: this.deviceInfo.devName
					}
					this.$u.api.execUpdateMyDevice(_params).then(res => {
						uni.showToast({ duration: 2000, title: '保存设备成功', icon: 'none' })
						if(this.deviceInfo.deviceType=='3' || this.deviceInfo.deviceType=='4'){
							this.bindDevice()
						}else{
							setTimeout(() => {
								this.continueBtn()
							}, 500);
						}
					}).catch(err => {
						uni.showToast({ duration: 2000, title: err.message || '发生未知错误', icon: 'none' })
					}).finally(() => {
						setTimeout(() => {
							uni.hideLoading();
						}, 500);
					})
				} else {
					let _params = {
						devCode: uni.getStorageSync('devCode'),
						devName: this.deviceInfo.devName,
						devScene: _devScene,
						memberPhone: uni.getStorageSync('phone') || undefined,
						houseId: this.deviceInfo.houseId,
						familyId: _currFamily.id
					}
					this.$u.api.execSaveDevInstallation(_params).then(res => {
						uni.showToast({ duration: 2000, title: '保存设备成功', icon: 'none' })
						console.log("设备信息保存成功、房间数据保存成功、设备激活成功。")
						if(this.deviceInfo.deviceType=='3' || this.deviceInfo.deviceType=='4'){
							this.bindDevice();
						}else{
							setTimeout(() => {
								this.continueBtn()
							}, 500);
						}
					}).catch(err => {
						uni.showToast({ duration: 2000, title: res.message || '发生未知错误', icon: 'none' })
					}).finally(() => {
						setTimeout(() => {
							uni.hideLoading();
						}, 500);
					})
				}
				
			},
			continueBtn() {
				const devCode = uni.getStorageSync('devCode');
				//#ifdef MP-WEIXIN
				this.$navTo(`pagesDevice/config/room-web?&token=${this.token}&devCode=${devCode}&devScene=${this.areaActived}&spotType=${this.spotType}&devHeight=${this.deviceInfo.devHeight}`);
				//#endif
				
				//#ifndef MP-WEIXIN
				this.$navTo(`pagesDevice/config/room?devCode=${devCode}&devScene=${this.areaActived}&spotType=${this.spotType}&devHeight=${this.deviceInfo.devHeight}`);
				//#endif
			},
			submit(){
				let _params = {
					devCode: uni.getStorageSync('devCode'),
					devName: this.deviceInfo.devName,
				}
				this.$u.api.saveBedSideDevice(_params).then(res => {
					uni.showToast({ duration: 2000, title: '保存设备成功', icon: 'none' })
					setTimeout(() => {
						uni.navigateTo({
							url: `/pagesDevice/config/vali-wait`
						})
					}, 1000);
				}).catch(err => {
					uni.showToast({ duration: 2000, title: res.message || '发生未知错误', icon: 'none' })
				}).finally(() => {
					setTimeout(() => {
						uni.hideLoading();
					}, 1000);
				})
			},
			async bindDevice(){
				console.log("绑定设备开始");
				const devCode = uni.getStorageSync('devCode');
				let _member = uni.getStorageSync('member');
				console.log("_member:",_member);
				let {phone}  = _member;
				if(!phone){
					uni.showToast({
						duration: 2000,
						title: '绑定失败',
						icon: 'none'
					})
					console.log("获取当登录人的电话号码为空")
					return;
				}
				let curr_family = uni.getStorageSync('curr_family')
				console.log("curr_family:",curr_family);
				const {id} = curr_family;
				if(!id){
					uni.showToast({
						duration: 2000,
						title: '绑定失败',
						icon: 'none'
					})
					console.log("获取当前家庭的ID为空")
					return;
				}
				await this.$u.api.execBindUser({ devCode: devCode, phone: phone, familyId: id });
				setTimeout(() => {
					uni.reLaunch({
						url: '/pages/index/index'
					})
				}, 500);
				console.log("绑定设备结束");
			},
		}
	}
</script>

<style lang="scss">
page {
	background: #f7f7f7;
}
</style>

<style lang="scss" scoped>
@import url("/static/css/iconfont.css");
.device-tag-config-page {
	.main{
		padding-bottom: 120rpx;
	}
	.item-block {
		// box-shadow: 0px 8rpx 24rpx 0rpx rgba(0, 0, 0, 0.1);
		background: white;
		border-radius: 16rpx;
		padding: 20rpx;
		margin: 30rpx;
		font-size: 32rpx;
		// #ifdef H5
		&:nth-child(1) {
			margin-top: 0rpx;
		}
		// #endif
	}
	.title {
		font-size: 32rpx;
		color: #0D0D0D;
		// font-weight: bold;
		margin-bottom: 16rpx;
	}
	.sub-title {
		font-size: 30rpx;
		color: #8B8B8B;
		margin-bottom: 30rpx;
	}
	.room-area-wrap {
		margin-top: 24rpx;
		u-col {
			&:nth-child(2n + 1) {
				::v-deep .u-col {
					padding: 0rpx 8rpx 0rpx 0rpx !important;
				}
			}
			&:nth-child(2n + 0) {
				::v-deep .u-col {
					padding: 0rpx 0rpx 0rpx 8rpx !important;
				}
			}
			&:nth-child(n + 1) {
				::v-deep .u-col {
					margin-top: 20rpx;
				}
			}
		}
		.area-block {
			width: 100%;
			padding: 32rpx 0rpx;
			padding-left: 30rpx;
			background: #F6F7F9;
			text-align: center;
			border-radius: 10rpx;
			background: white;
			//#ifdef H5
			margin-bottom: 20rpx;
			//#endif
			.icon {
				font-size: 50rpx;
				vertical-align: middle;
			}
			.img {
				display: inline-block;
				width: 76rpx;
				height: 52rpx;
			}
			.text {
				display: inline-block;
				margin-left: 30rpx;
				width: 100rpx;
				text-align: left;
			}
			&.actived {
				background: #01B09A;
				color: white;
			}
		}
	}
	.footer-btns {
		position: fixed;
		bottom: 0rpx;
		left: 0rpx;
		right: 0rpx;
		padding:20rpx 40rpx 40rpx;
		z-index: 999;
		background-color: white;
		display: flex;
		align-items: center;
		justify-content: center;
		.next-btn{
			display: block;
			flex: 1;
		}
	}
	
	::v-deep .uni-easyinput__content {
		padding: 14rpx 6rpx;
	}
	::v-deep .u-model__footer__button {
		height: 88rpx;
		line-height: 88rpx;
	}
}
</style>
