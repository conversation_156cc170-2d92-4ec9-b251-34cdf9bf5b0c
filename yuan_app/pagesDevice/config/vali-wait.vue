<template>
	<view class="verification-result-page">
		<view class="top-header-line"></view>
		
		<view v-if="submitStatus === true" class="top-wrap">
			<image class="img-center-bg" :src="'../../../static/images/common/icon-verification-passed.png'"></image>
			<view class="tip">提交成功，请耐心等待配置结果</view>
		</view>
		
<!-- 		<view class="top-wrap">
			<image class="img-center-bg" src="../../static/images/index/icon-verification-passed.png"></image>
			<view class="tip">提交失败，请重新提交</view>
		</view> -->
		
		<view class="timer-content">{{ content }}</view>
		
	</view>
</template>

<script>
	export default {
		data() {
			return {
				wait: 30,
				clockTimer: undefined,
				content: '30秒',
				submitStatus: true
			}
		},
   //      onLoad() {//默认加载
   //          // this.login();
			// wx.hideHomeButton()
   //      },
		async onLoad(option) {
			const devCode = uni.getStorageSync('devCode');
			const status = await this.$u.api.execCheckDevCode({ devCode: devCode });
			// 自己已经绑定的，跳转到首页
			if(status&&status.isSelfBind){
				uni.showToast({ duration: 2000, title: '您已添加该设备，前往首页查看', icon: 'none' })
				setTimeout(() => {
					uni.reLaunch({
						url: '/pages/index/index'
					})
				}, 1000);
			}else{
				this.processTiming()
				await this.bindDevice()
				this.fetchResultProgress()
			}
		},
		beforeDestroy() {
			if (this.clockTimer) {
				clearInterval(this.clockTimer);
				this.clockTimer = undefined
			}
		},
		methods: {
			async bindDevice(){
				console.log("绑定设备开始");
				const devCode = uni.getStorageSync('devCode');
				let _member = uni.getStorageSync('member');
				console.log("_member:",_member);
				let {phone}  = _member;
				if(!phone){
					uni.showToast({
						duration: 2000,
						title: '绑定失败',
						icon: 'none'
					})
					console.log("获取当登录人的电话号码为空")
					return;
				}
				let curr_family = uni.getStorageSync('curr_family')
				console.log("curr_family:",curr_family);
				const {id} = curr_family;
				if(!id){
					uni.showToast({
						duration: 2000,
						title: '绑定失败',
						icon: 'none'
					})
					console.log("获取当前家庭的ID为空")
					return;
				}
				await this.$u.api.execBindUser({ devCode: devCode, phone: phone, familyId: id });
				console.log("绑定设备结束");
			},
			processTiming(){
				this.content = this.wait + '秒' //这里解决60秒不见了的问题
				this.clockTimer = setInterval(() => {
					this.wait--
					this.content = this.wait + '秒'
					if (this.wait < 0) { //当倒计时小于0时清除定时器
						if (this.clockTimer) {
							clearInterval(this.clockTimer);
							this.clockTimer = undefined
						}
						this.content = '等待中...'
					}
				}, 1000)
			},
			fetchResultProgress() {
				this.$u.api.fetchCommitRoomInfoResult({ devCode: uni.getStorageSync('devCode') }).then(res => {
					if (res == '1') {
						setTimeout(() => {
							this.next(true)
						}, 1000)
					} else if (res == '2') {
						setTimeout(() => {
							this.next(false)
						}, 1000)
					} else if (res == '3') {
						uni.showToast({ duration: 2000, title: '状态反馈超时', icon: 'none' })
						setTimeout(() => {
							this.next(false)
						}, 1000)
					} else {
						setTimeout(() => {
							this.fetchResultProgress()
						}, 1500)
					}
				}).catch(err => {
					console.log('err', err)
					if (res.message === '配置失败') {
						this.next(false)
					} else {
						setTimeout(() => {
							this.fetchResultProgress()
						}, 1500)
					}
				})
				// .finally((res) => {
				// 	console.log('res', res)
				// 	if (!res || res.message !== '配置失败') {
				// 		setTimeout(() => {
				// 			this.fetchResultProgress()
				// 		}, 1500)
				// 	}
				// })
			},
			next(status) {
				if (this.clockTimer) {
					clearInterval(this.clockTimer)
					this.clockTimer = undefined
				}
				uni.redirectTo({
					url: `/pagesDevice/config/config-result?status=${status}`
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
.verification-result-page {
	height: 100vh;
	position: relative;
	// padding: 30upx;
	box-sizing: border-box;
	text-align: center;
	.device-info {
		padding: 40rpx 0rpx 0rpx 40rpx;
		font-size: 30rpx;
		color: #5E5D5D;
		text-align: left;
	}
	.top-wrap {
		padding-top: 128rpx;
		.img-center-bg {
			width: 128rpx;
			height: 128rpx;
			margin: 0 auto;
		}
		.tip {
			font-size: 30upx;
			color: #5E5D5D;
			font-size: 30rpx;
			font-weight: 500;
			margin-top: 55rpx;
			font-weight: bold;
		}
	}
	.timer-content {
		color: #47DF9B;
		font-size: 32rpx;
		margin-top: 75rpx;
		text-align: center;
	}
	.footer-btns {
		position: absolute;
		bottom: 40rpx;
		left: 60rpx;
		right: 60rpx;
		margin: 0 auto;
		.next-btn {
			display: block;
			margin-top: 20rpx;
		}
	}
}
</style>
