<template>
	<view class="page-wrap">
		<view class="title">
			一、网络设置
		</view>
		<view class="content">
			1、当前设备安装<text style="color:#01B09A">只支持2.4GWIFI</text>网络，请勿使用5G网络
		</view>
		<view class="content">
			2、为了<text style="color:#01B09A">设备安全</text>，请为<text style="color:#01B09A">WIFI设置密码</text>
		</view>

		<view class="title">
			二、什么情况下需要配网
		</view>
		<view class="content">
			1、<text style="color:#01B09A">Mini设备从未配网</text>或<text style="color:#01B09A">Pro设备未插卡或无法访问运营商4G网络时</text>
		</view>
		<view class="content">
			2、<text style="color:#01B09A">WIFI名称或密码发生变化</text>
		</view>
		<view class="content">
			3、如果已完成配网，请点击‘设备配置’按钮
		</view>
		<view class="title">
			三、如何配网
		</view>
		<view class="content">
			1、请确保手机<text style="color:#01B09A">开启蓝牙和GPS</text>，然后点击‘去配网’按钮
		</view>
		<view v-if="!isFollowedMp" style="width:100%;">
			<official-account></official-account>
		</view>

		<view class="footer-btns">
			<view style="text-align:left">
				<view style="color:#0000FF;display:inline;font-size: 32rpx;"
					@click="$navTo(`pagesDevice/config/tag-config`)">设备配置</view>
			</view>

			<u-button shape="circle" class="scan-code-btn diy-btn" size="medium"
				:custom-style="{ 'width': '100%', 'color': 'white', 'background-color': '#01B09A' }" hover-class="none"
				@click="toConfigWifi">去配网</u-button>
		</view>
	</view>
</template>

<script>
import { gotoConfigWifi } from "../../utils/gotoOutside";
export default {
	data() {
		return {
			devCode:undefined,
			isFollowedMp:false,
		}
	},
	onLoad(option) {
		this.devCode = uni.getStorageSync('devCode');
		
		const {followedMp} = uni.getStorageSync('member')
		console.log("followedMp："+followedMp);
		this.isFollowedMp = followedMp;
	},
	methods: {
		toConfigWifi() {
			gotoConfigWifi(this.devCode);
		}
	}
}
</script>

<style lang="scss" scoped>
@import url("/static/css/iconfont.css");

.page-wrap {
	height: 100vh;
	overflow: hidden;
	text-align: center;
	// padding-top: 120rpx;

	.title {
		text-align: left;
		font-size: 32rpx;
		// padding-left: 10rpx;
		margin: 40rpx 10rpx 20rpx 20rpx;
	}

	.content {
		text-align: left;
		font-size: 28rpx;
		margin: 0 20rpx 20rpx 80rpx;
	}

	.footer-btns {
		position: absolute;
		bottom: 40rpx;
		left: 40rpx;
		right: 40rpx;
		margin: 0 auto;

		.scan-code-btn {
			display: block;
			margin-top: 37rpx;
		}
	}

}
</style>
