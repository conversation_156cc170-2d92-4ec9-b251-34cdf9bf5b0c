<template>
	<view class="device-step-page">
		<view class="top-header-line"></view>
		<view v-if="isHejia">
			<u-row gutter="16" class="block-row">
				<u-col span="3" style="color: #0D0D0D">
					<!-- <image class="left-img" :src="'../../../static/images/device/icon-room-2.png'"></image> -->
					<text class="icon iconfont icon-lanyawifi" style="font-size: 100rpx; position: relative; left: 14rpx; color: #01B09A;"></text>
				</u-col>
				<u-col span="9" class="right-wrap">
					<view class="title">安装信息配置</view>
					<view class="step-item">1、标签信息配置</view>
					<view class="step-item">2、房屋信息配置</view>
				</u-col>
			</u-row>
		</view>
		<view v-else>
			<u-row gutter="16" class="block-row">
				<u-col span="3" style="color: #0D0D0D">
					<!-- <image class="left-img" :src="'../../../static/images/device/icon-wifi-1.png'"></image> -->
					<text class="icon iconfont icon-lanyawifi" style="font-size: 100rpx; color: #01B09A;"></text>
				</u-col>
				<u-col span="9" class="right-wrap">
					<view class="title">第一步</view>
					<view class="desc" style="margin-bottom: 16rpx;">配置蓝牙、wifi</view>
					<view class="desc">(暂不支持5G)</view>
				</u-col>
			</u-row>
			<u-row gutter="16" class="block-row">
				<u-col span="3" style="color: #0D0D0D">
					<!-- <image class="left-img" :src="'../../../static/images/device/icon-room-2.png'"></image> -->
					<text class="icon iconfont icon-ic_anzhuangshebei" style="font-size: 68rpx; position: relative; left: 14rpx; color: #01B09A;"></text>
				</u-col>
				<u-col span="9" class="right-wrap">
					<view class="title">第二步</view>
					<view class="desc">安装信息配置</view>
					<view class="step-item">1、标签信息配置</view>
					<view class="step-item">2、房屋信息配置</view>
				</u-col>
			</u-row>
		</view>
		<view class="footer-btns">
			<!-- <u-button v-if="checked === true" type="primary" shape="circle" class="scan-code-btn" @click="scan">下一步</u-button> -->
			<!-- <u-button type="primary" shape="circle" class="next-btn" @click="next">开始安装</u-button> -->
			<u-button shape="circle" class="next-btn diy-btn" size="medium" 
			:custom-style="{ 'width': '100%', 'color': 'white', 'background-color': '#01B09A' }"  hover-class="none"
			@click="next">开始安装</u-button>
		</view>
	</view>
</template>

<script>
	export default {
		onLoad() {
			// 根据是否有手机号来确定是否在和家亲APP内部
			const phoneNum = uni.getStorageSync('phoneNum');
			if(phoneNum){
				this.isHejia = true;
			}
		},
		data() {
			return {
				isHejia: false,
			}
		},
		methods: {
			next() {
				if (this.isHejia) {
					uni.redirectTo({
						url: '/pagesDevice/config/tag-config'
					})
				} else {
					this.$navTo('pagesFamily/device/wifi/index')
				}
			},
		}
	}
</script>

<style lang="scss" scoped>
@import url("/static/css/iconfont.css");
	.device-step-page {
		padding: 90px 60px;
		box-sizing: border-box;
		height: 100vh;
		.block-row {
			display: block;
			// #ifdef H5
			display: flex;
			// #endif
			font-size: 32rpx;
			.left-img {
				width: 100rpx;
				height: 100rpx;
			}
			.right-wrap {
				text-align: left;
				.title {
					color: #0D0D0D;
					font-weight: bold;
				}
				.desc {
					margin-top: 12rpx;
					color: #5E5D5D;
					margin-bottom: 30rpx;
				}
				.step-item {
					font-size: 28rpx;
					color: #5E5D5D;
					&:nth-child(n + 2) {
						margin-top: 10rpx;
					}
				}
			}
			&:nth-child(n + 2) {
				margin-top: 90rpx;
			}
		}
		.footer-btns {
			position: absolute;
			bottom: 40rpx;
			left: 40rpx;
			right: 40rpx;
			margin: 0 auto;
			.next-btn {
				display: block;
				margin-top: 20rpx;
			}
		}
	}
</style>