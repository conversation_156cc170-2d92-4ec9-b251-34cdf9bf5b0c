<template>
	<view class="verification-result-page">
		<view class="top-header-line"></view>
		
		<!-- <template v-if="configStatus === true">
			<rich-text-modal
				:show="serverAgreement.agreeStep == 1"
				:content="serverAgreement.simpleAgreement"
				@confirm="agreeAgreement(true)"/>
			<member-agreement-popup
				:show="serverAgreement.agreeStep == 2"
				:content="serverAgreement.agreement"
				@confirm="agreeAgreement(true)"
				@cancel="agreeAgreement(false)"/>
		</template> -->
		
		<view v-if="configStatus === true" class="top-wrap">
			<image class="img-center-bg" :src="'../../static/images/common/icon-verification-passed.png'"></image>
			<view class="tip">配置成功，设备将重新启动...</view>
			<view class="tip">请等待设备上线，此过程大概需要2分钟</view>
			<view class="timer-content">安装成功</view>
		</view>
		<view v-else class="top-wrap">
			<!--#ifdef MP-WEIXIN -->
			<view v-if="deviceType=='1'">
				<image class="img-center-bg" :src="'../../static/images/common/icon-verification-fail.png'"></image>
				<view class="tip">配置失败</view>
				
				<view style="font-size: 28rpx;text-align:left;margin:100rpx 50rpx 0 50rpx;">
					<view style="margin-bottom: 20rpx;">
						1、请检查电源连接线是否正常</text>
					</view>
					<view>
						2、请<text style="color:#01B09A;">重新开启蓝牙，确保WIFI为2.4G网络</text>
					</view>
					<view>
						3、请<text style="color:#01B09A;">长按设备的重置按钮10秒</text>
					</view>
					<view>4、请点击 <text style="color:#01B09A">返回配网</text> 按钮，按步骤重新配网</view>
				</view>
			</view>
			<view v-else>
				<image class="img-center-bg" :src="'../../static/images/common/icon-verification-fail.png'"></image>
				<view class="tip">连接超时</view>
				
				<view style="font-size: 28rpx;text-align:left;margin:100rpx 50rpx 0 50rpx;">
					<view style="margin-bottom: 20rpx;">
						1、请检查电源连接线是否正常</text>
					</view>
					<view>2、请点击 <text style="color:#01B09A">返回</text> 按钮，重新提交</view>
				</view>
			</view>
			<!--#endif -->
			<!--#ifdef (H5 || H5-HEJIA) -->
			<image class="img-center-bg" :src="'../../static/images/common/icon-verification-fail.png'"></image>
			<view class="tip">连接超时</view>
			
			<view style="font-size: 28rpx;text-align:left;margin:100rpx 50rpx 0 50rpx;">
				<view style="margin-bottom: 20rpx;">
					1、请检查电源连接线是否正常</text>
				</view>
				<view>2、请点击 <text style="color:#01B09A">返回</text> 按钮，重新提交</view>
			</view>
			<!--#endif -->
		</view>
		
		<view class="footer-btns">
			<u-button v-if="configStatus === true" shape="circle" class="next-btn diy-btn" size="medium" 
				:custom-style="{ 'width': '100%', 'color': 'white', 'background-color': '#01B09A' }"  hover-class="none"
				@click="$navTo(`pages/index/index`)">完成安装</u-button>
			<!--#ifdef MP-WEIXIN -->
			<u-button v-else-if="configStatus === false && deviceType=='1'" shape="circle" class="next-btn diy-btn" size="medium" 
				:custom-style="{ 'width': '100%', 'color': 'white', 'background-color': '#01B09A' }"  hover-class="none"
				@click="next">返回配网</u-button>
			<u-button v-else-if="configStatus === false && deviceType=='2'"  shape="circle" class="next-btn diy-btn" size="medium"
				:custom-style="{ 'width': '100%', 'color': 'white', 'background-color': '#01B09A' }"  hover-class="none"
				@click="next">返回</u-button>
			<!--#endif -->
			<!--#ifdef (H5 || H5-HEJIA) -->
			<u-button v-else shape="circle" class="next-btn diy-btn" size="medium"
				:custom-style="{ 'width': '100%', 'color': 'white', 'background-color': '#01B09A' }"  hover-class="none"
				@click="next">返回</u-button>
			<!--#endif -->
		</view>
		
	</view>
</template>

<script>
	export default {
		data() {
			return {
				configStatus: true,
				devCode: undefined,
				phone: undefined,
				deviceType:undefined,
				
				/* serverAgreement: {
					agreeStep: 0,
					serverIds:[],
					simpleAgreement: null,
					agreement: null,
				}, */
			}
		},
		onLoad(option) {
			const devCode = this.devCode = option.devCode || uni.getStorageSync("devCode");
			if (option.status) {
				if (option.status === 'true') {
					this.configStatus = true
				} else if (option.status === 'false') {
					this.configStatus = false
				}
			}
			this.phone = uni.getStorageSync('phone')
			
			this.$u.api.fetchDeviceSimpleInfo({ devCode: devCode }).then(res => {
				console.log(res);
				if(res){
					this.deviceType = res.deviceType;
				}
			}).catch(err => {
				console.log('err', err)
			})
			
			if (!this.configStatus) {
				return;
			}
			/* this.$u.api.listByDevCodeAgreement(devCode)
				.then(res => {
					this.serverAgreement = res;
					if (res.serverIds.length) {
						this.serverAgreement.agreeStep = 1;
					}
				}).catch(err => {
					console.log('err', err)
				}); */
		},
		methods: {
			next() {
				if (this.configStatus === true) {
					let _isHejia = false;
					//#ifdef H5-HEJIA
					_isHejia = true;
					// uni.navigateBack({
					// 	delta: 4
					// })
					//#endif
					//#ifdef H5
					if (_isHejia === true) {
						uni.redirectTo({
							url: '/pages/cmcc/index?deviceType=' + uni.getStorageSync('cmcc_deviceType') + '&deviceId=' + uni.getStorageSync('cmcc_deviceId')
						})
					} else {
						uni.switchTab({
							url: '/pages/index/index'
						})
					}
					//#endif
					//#ifdef MP-WEIXIN
					uni.switchTab({
						url: '/pages/index/index'
					})
					//#endif
				} else {
					//#ifdef MP-WEIXIN
					//this.$navTo(`pagesDevice/config/wifi-notice`, {}, "redirectTo");
					if(this.deviceType=='1'){
						uni.reLaunch({url:`/pagesDevice/config/wifi-notice`});
					}else{
						uni.reLaunch({url:`/pages/index/index`});
					}
					//#endif
					//#ifdef (H5||H5-HEJIA)
					uni.navigateBack({
						delta: 1
					})
					//#endif
				}
			},
			testDev() {
				if (!uni.getStorageSync('devId')) {
					let _isHejia = false;
					//#ifdef H5-HEJIA
					_isHejia = true;
					//#endif
					//#ifdef H5
					if (_isHejia === true) {
						uni.setStorageSync('testDeviceId', uni.getStorageSync('cmcc_deviceId'))
					} else {
						this.$u.api.fetchDevInstallationInfo({ devCode: this.devCode || uni.getStorageSync('devCode') }).then(res => {
							if (res) {
								uni.setStorageSync('testDeviceId', res.id)
								this.$navTo(`pagesFamily/device/config/test-dev`)
							}
						})
					}
					//#endif
					//#ifdef MP-WEIXIN
						this.$u.api.fetchDevInstallationInfo({ devCode: this.devCode || uni.getStorageSync('devCode') }).then(res => {
							if (res) {
								uni.setStorageSync('testDeviceId', res.id)
								this.$navTo(`pagesFamily/device/config/test-dev`)
							}
						})
					//#endif
				} else {
					this.$navTo(`pagesFamily/device/config/test-dev`)
				}
			},
			/* async agreeAgreement(agree) {
				if (agree === false) {
					this.serverAgreement.agreeStep = 0;
					return;
				}
				this.serverAgreement.agreeStep++;
				if (this.serverAgreement.agreeStep == 3) {
					this.serverAgreement.agreeStep = 0;
					try {
						await this.$u.api.agreeByServer({
							devCode: this.devCode,
							serverIds: this.serverAgreement.serverIds
						});
					} catch (err) {
						console.error(err);
					}
				}
			}, */
		}
	}
</script>

<style lang="scss" scoped>
.verification-result-page {
	height: 100vh;
	position: relative;
	// padding: 30upx;
	box-sizing: border-box;
	text-align: center;
	.device-info {
		padding: 40rpx 0rpx 0rpx 40rpx;
		font-size: 30rpx;
		color: #5E5D5D;
		text-align: left;
	}
	.top-wrap {
		padding-top: 128rpx;
		.img-center-bg {
			width: 128rpx;
			height: 128rpx;
			margin: 0 auto;
		}
		.tip {
			font-size: 30upx;
			color: #5E5D5D;
			font-size: 30rpx;
			font-weight: 500;
			margin-top: 55rpx;
			font-weight: bold;
		}
	}
	.timer-content {
		color: #47DF9B;
		font-size: 32rpx;
		margin-top: 75rpx;
		text-align: center;
	}
	.footer-btns {
		position: absolute;
		bottom: 40rpx;
		left: 60rpx;
		right: 60rpx;
		margin: 0 auto;

		.next-btn {
			display: block;
			margin-top: 20rpx;
		}
	}
}
</style>
