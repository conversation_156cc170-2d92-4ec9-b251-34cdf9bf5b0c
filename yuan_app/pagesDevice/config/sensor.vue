<template>
	<view class="device-sensor-page">
		<view class="top-header-line"></view>
		<view class="sketch-img" :style="{ 'background-image': `url(${staticUrl}/images/device/room-config/device-${spotType}.jpg)` }">
			<view class="label" :class="[`label-x-${spotType}`]">
				{{ null2val(form.x, '未设置') }}
			</view>
			<view v-if="spotType != spotTypeSideConst" class="label" :class="[`label-y-${spotType}`]">
				{{ spotType == spotTypeSideConst ? 0.01 : null2val(form.y, '未设置') }}
			</view>
			<view class="label" :class="[`label-z-${spotType}`]">
				{{ null2val(form.z, '未设置') }}
			</view>
		</view>

		<view>
			<u-field v-if="spotType == spotTypeSideConst" v-model="form.x" label="左长(米)" type="digit" placeholder="请输入" required disabled>
			</u-field>
			<u-field v-else v-model="form.x" label="长(米)" type="digit" placeholder="请输入" required>
			</u-field>
			<u-field v-model="form.y" label="宽(米)" type="digit" placeholder="请输入" required :disabled="spotType == spotTypeSideConst">
			</u-field>
<!--      <u-field v-if="spotType == spotTypeSideConst" v-model="0.1" label="宽(米)" type="digit" placeholder="请输入" required disabled>-->
<!--      </u-field>-->
<!--      <u-field v-else v-model="form.y" label="宽(米)" type="digit" placeholder="请输入" required>-->
<!--      </u-field>-->
			<u-field v-model="form.z" label="高(米)" type="digit" placeholder="请输入" required>
			</u-field>
		</view>

		<view class="footer-btns">
			<!-- <u-button v-if="checked === true" type="primary" shape="circle" class="scan-code-btn" @click="scan">下一步</u-button> -->
			<!-- <u-button type="primary" shape="circle" class="next-btn" @click="handleSave">保存</u-button> -->
			<u-button shape="circle" class="next-btn diy-btn" size="medium"
				:custom-style="{ 'width': '100%', 'color': 'white', 'background-color': '#01B09A' }" hover-class="none"
				@click="handleSave">{{source == "dev" ? "保存" : "下一步"}}</u-button>
		</view>
	</view>
</template>

<script>
	import * as util from '@/utils/util';
export default {
	data() {
		return {
			staticUrl: this.$u.http.config.staticBaseUrl,
			form: {
				x: undefined,
				y: undefined,
				z: undefined,
			},

			spotTypeSideConst: "2",
			spotType: "1",
			source: null,
		}
	},
	onLoad(options) {
		if (options.spotType) {
			this.spotType = options.spotType;
		}
		this.source = options.source;
		this.fetchInfo();
	},
	methods: {
		null2val:util.null2val,
		fetchInfo() {
			uni.showLoading({
				title: '加载中...',
				mask: true
			})
			this.$u.api.fetchRoomSensorInfo({ devCode: uni.getStorageSync('devCode') }).then(res => {
				this.form = res || {};
				if (this.spotType == this.spotTypeSideConst) {
					this.fetchSideDefault();
				}
			}).catch(err => {
				console.error(err);
			}).finally(() => {
				setTimeout(() => {
					uni.hideLoading();
				}, 1000);
			})
		},
		async fetchSideDefault() {
			const { form } = this;
			if (!form.x) {
				const { leftX } = await this.$u.api.fetchRoomInfo({ devCode: uni.getStorageSync('devCode') });
				form.x = leftX;
			}
			if (this.spotType == this.spotTypeSideConst) {
				this.form.y = 0.01;
			}
			if (!form.z) {
				form.z = 2.2;
			}
			this.form = {...form};
		},
		isNumber(n) {
			return !isNaN(parseFloat(n)) && isFinite(n);
		},
		handleSave() {
			if ((this.form.x == null && this.form.x == undefined) ||
						(this.form.y == null && this.form.y == undefined) ||
						(this.form.z == null && this.form.z == undefined)) {
				uni.showModal({ content: '请检查“*”必填项是否填写正确', showCancel: false, confirmText: '关闭' })
				return;
			}
			if (!this.isNumber(this.form.x) || !this.isNumber(this.form.y) || !this.isNumber(this.form.z)) {
				uni.showModal({ content: '请检查填写内容是否为合法数字', showCancel: false, confirmText: '关闭' })
				return;
			}
			if (parseFloat(this.form.x) < 0 || parseFloat(this.form.y) < 0 || parseFloat(this.form.z) < 0) {
				uni.showModal({ content: '请检查填写内容是否大于0', showCancel: false, confirmText: '关闭' })
				return;
			}

			let _params = {
				roomId: uni.getStorageSync('devCode'),
				...this.form,
			}
			uni.showLoading({
				title: '保存中...',
				mask: true
			})
			this.$u.api.execSaveRoomSensor(_params).then(res => {
				uni.showToast({ duration: 2000, title: '设备方位保存成功', icon: 'none' })
				setTimeout(() => {
					if (this.source == 'dev') {
						uni.navigateBack({ delta: 1 });
					} else {
						//#ifdef H5  
						if(this.spotType == '1'){
							this.$navTo(`pagesDevice/config/door?spotType=${this.spotType}`);
						}else{
							this.$navTo(`pagesDevice/config/room?devCode=${uni.getStorageSync('devCode')}`);
						}
						//#endif
						//#ifdef MP-WEIXIN
						if(this.spotType == '1'){
							this.$navTo(`pagesDevice/config/door?spotType=${this.spotType}`);
						}else{
							// this.$navTo(`pagesDevice/config/door?spotType=${this.spotType}`);
							//this.$navTo(`pagesDevice/config/room?devCode=${uni.getStorageSync('devCode')}`);
							// 到web端的页面
							console.log("跳转到webview")
							this.$navTo(`pagesDevice/config/room-web?devCode=${uni.getStorageSync('devCode')}`);
						}
						//#endif
					}
				}, 1000);
			}).catch(err => {
				console.error( err)
			}).finally(() => {
				setTimeout(() => {
					uni.hideLoading();
				}, 1000);
			})
		},
	}
}
</script>

<style lang="scss">
::v-deep input {
	text-align: right;
	color: #888;
}
</style>
<style lang="scss" scoped>
.device-sensor-page {
	padding-bottom: 140rpx;

	.sketch-img {
		width: 710rpx;
		height: 710rpx;
		background-size: 710rpx 710rpx;
		.label {
			position: relative;
			border-radius: 2em;
			width: 120rpx;
			height: 48rpx;
			line-height: 48rpx;
			font-size: 26rpx;
			text-align: center;
		}
		.label-x-1 {
			top: 120rpx;
			left: 250rpx;
			color: #f29971;
			border:1rpx solid #f29971;
		}
		.label-y-1{
			top: 200rpx;
			left: 310rpx;
			color: #67ad93;
			border:1rpx solid #67ad93;
		}
		.label-z-1 {
			top: 360rpx;
			left: 346rpx;
			color: #9deeda;
			border:1rpx solid #9deeda;
		}

		.label-x-2 {
			top: 640rpx;
			left: 350rpx;
			color: #10b6dc;
			border: 1rpx solid #10b6dc;
		}
		.label-y-2 {
			top: 500rpx;
			left: 80rpx;
			color: #327f5b;
			border: 1rpx solid #226c51;
		}
		.label-z-2 {
			top: 390rpx;
			left: 510rpx;
			color: #9deeda;
			border: 1rpx solid #9deeda;
		}
	}

	.icon-reqired {
		color: red;
		display: inline-block;
		vertical-align: middle;
		font-size: 40rpx;
		position: relative;
		top: 4rpx;
		left: 6rpx;
	}

	::v-deep .u-label {
		font-size: 32rpx;
		color: #0D0D0D;

		&::before {
			font-size: 40rpx;
			top: 10rpx;
		}

		.u-label-text {
			// margin-left: 20rpx;
			font-weight: bold;
		}
	}

	.footer-btns {
		position: fixed;
		z-index: 100;
		bottom: 40rpx;
		left: 40rpx;
		right: 40rpx;
		margin: 0 auto;

		.next-btn {
			display: block;
			margin-top: 20rpx;
		}
	}

	::v-deep .u-field {
		padding: 28rpx;
	}
}
</style>
