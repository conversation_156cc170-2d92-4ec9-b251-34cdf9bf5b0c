<template>
	<view>
		<web-view v-if="roomUrl" :webview-styles="webviewStyles" :src="roomUrl" @message="reciveMessage"></web-view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				cmccUrl: this.$u.http.config.cmccUrl,
				roomUrl: undefined,
				webviewStyles: {
					progress: {
						color: '#FF3333'
					}
				}
			}
		},
		onLoad: function(option) {
			const {token,devCode,sourcePage,roomDataFrom3D,devScene,spotType,devHeight} = option;
			let url = this.cmccUrl + '/pagesDevice/config/room?devCode=' + devCode + '&from=miniapp&token='+token;
			if (sourcePage) {
				url += '&sourcePage=' + sourcePage;
			} 
			if(roomDataFrom3D){
				url += '&roomDataFrom3D=' + roomDataFrom3D;
			}
			if(devScene){
				url += '&devScene=' + devScene;
			}
			if(spotType){
				url += '&spotType=' + spotType;
			}
			if(devHeight){
				url += '&devHeight=' + devHeight;
			}
			this.roomUrl = url;
			console.log("进入webview", this.roomUrl)
		},
		methods: {
			reciveMessage(data) {
				// uni.showToast({
				// 	title: "reciveMessage接收到消息：" + JSON.stringify(data.detail),
				// 	duration: 2000,
				// 	icon: 'none'
				// });
				console.log("接收到消息：" + JSON.stringify(data.detail));
			}
		}
	}
</script>

<style>
</style>
