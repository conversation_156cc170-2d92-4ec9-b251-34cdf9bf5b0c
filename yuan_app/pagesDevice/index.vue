<template>
	<view class="divece-index-page">
		<view class="top-title">设备管理</view>
		
		<view class="wrap">
			<view class="item u-border-bottom" v-for="(row, index) in rows" :key="index">
				<u-row @click="nextBtn(row.id, row.devCode)">
					<u-col span="8">
						<view class="left-info">
							<view class="device-name">{{ (row.devName || row.devCode) || '' }}</view>
							<view class="device-tag">{{ row.devSceneName || '暂无标签' }}</view>
						</view>
					</u-col>
					<u-col span="4">
						<view class="right-status">
							{{ row.activeStatusName }}<u-icon name="play-right-fill" color="#b9b9b9" size="22"></u-icon>
						</view>
					</u-col>
				</u-row>
			</view>
			<u-loadmore v-if="rows && rows.length >= 10" :status="status" class="loadmore" />
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				status: 'loadmore',
				list: 15,
				page: 0,
				rows: []
			}
		},
		onShow() {
			this.fetchDatas();
		},
		methods: {
			nextBtn(devId, devCode) {
				uni.setStorageSync('devId', devId)
				uni.setStorageSync('devCode', devCode)
				uni.navigateTo({
					url: `/pagesFamily/device/notActive-v2`
				})
			},
			fetchDatas() {
				this.$u.api.fetchMyDeviceList({ }).then(res => {
					if (res) {
						this.rows = res
					} else {
						this.rows = []
					}
				})
			},
		},
		onReachBottom() {
			if(this.page >= 3) return ;
			this.status = 'loading';
			this.page = ++ this.page;
			setTimeout(() => {
				this.list += 10;
				if(this.page >= 3) this.status = 'nomore';
				else this.status = 'loading';
			}, 2000)
		}
	}
</script>

<style lang="scss" scoped>
.divece-index-page {
	padding: 29rpx;
	.top-title {
		font-size: 36rpx;
		color: #333;
	}
	
	.wrap {
		// padding: 24rpx;
		margin-top: 30rpx;
	}
	
	.item {
		padding: 24rpx 30rpx;
		color: $u-content-color;
		font-size: 28rpx;
		box-shadow: 0rpx 8rpx 24rpx 0rpx rgba(0, 0, 0, 0.1);
		border-radius: 16rpx;
		
		.left-info {
			.device-name {
				color: #0D0D0D;
				font-size: 32rpx;
			}
			.device-tag {
				color: #000000;
				font-size: 26rpx;
				margin-top: 14rpx;
				display: block;
			}
		}
		.right-status {
			color: #b9b9b9;
			text-align: right;
			// #ifndef H5
			margin-top: 28rpx;
			// #endif
			::v-deep .u-icon {
				margin-left: 10rpx;
				position: relative;
				top: -2rpx;
			}
		}
		&:nth-child(n + 2) {
			margin-top: 20rpx;
		}
	}
	
	.loadmore {
		display: block;
		margin-top: 20rpx;
	}
}
</style>
