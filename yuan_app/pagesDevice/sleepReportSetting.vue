<template>
	<view class="device-connector-edit-page">
		<u-form :model="form" ref="uForm">
			<TimePicker v-model="form.sleepBegin" @change="handleTimeChange1" :hourMin='16' :hourMax='23' ref="timePicker1">
				<u-field @click="openTimePicker1" v-model="form.sleepBegin" required :disabled="true"
					label="检测开始时间" placeholder="请选择" right-icon="arrow-down-fill"></u-field>
			</TimePicker>

			<TimePicker v-model="form.sleepEnd" @change="handleTimeChange2" :hourMin='4' :hourMax='10'
				ref="timePicker2">
				<u-field @click="openTimePicker2" v-model="form.sleepEnd" required :disabled="true" label="检测结束时间"
					placeholder="请选择" right-icon="arrow-down-fill"></u-field>
			</TimePicker>

			<!-- <u-field v-model="form.sleepOutBedTimeout" label="离床超时(分钟)" placeholder="请输入" required maxlength="10">
			</u-field> -->
		</u-form>

		<view class="footer-btns">
			<u-button shape="circle" class="next-btn diy-btn" size="medium"
				:custom-style="{ 'width': '100%', 'color': 'white', 'background-color': '#01B09A' }" hover-class="none"
				@click="handleSave">保存</u-button>
		</view>

	</view>
</template>

<script>
	import TimePicker from './components/TimePicker.vue';
	import {limitTime} from '../utils/util.js';
	
	export default {
		components: {
			TimePicker
		},
		data() {
			return {
				form: {
					id: undefined,
					sleepBegin: '18:00',
					sleepEnd: '10:00',
					sleepOutBedTimeout: 3,
				},
			}
		},
		onLoad(option) {
			console.log(option)
			this.form.id = option.id;
			if (option.id != null && option.id != undefined) {
				this.fetchDeviceInfo()
			}
		},
		mounted() {
			this.$nextTick(() => {
				console.log('timePicker1:', this.$refs.timePicker1);
				console.log('timePicker2:', this.$refs.timePicker2);
				// this.$refs.timePicker1 = this.$children.find(child => child.$options.name === 'TimePicker');
				// this.$refs.timePicker2 = this.$children.find(child => child.$options.name === 'TimePicker');
			});
		},
		methods: {
			openTimePicker() {
				showTimePicker(this, (time) => {
					console.log('Selected Time:', this.selectedTime);
				});
			},
			openTimePicker1() {
				this.$refs.timePicker1.openTimePicker();
			},
			openTimePicker2() {
				this.$refs.timePicker2.openTimePicker();
			},
			handleTimeChange1(time) {
				console.log('检测开始时间:', time);
			},
			handleTimeChange2(time) {
				console.log('检测结束时间:', time);
				this.form.sleepEnd = limitTime(time,"10:00",true);
			},
			handleshowBeginView() {
				this.showBeginView = true;
			},
			fetchDeviceInfo() {
				uni.showLoading({
					title: '加载中...',
					mask: true
				})
				this.$u.api.fetchMyDeviceInfo({
					devId: this.form.id
				}).then(res => {
					if (res) {
						this.form = {
							id: res.id,
							sleepBegin: res.sleepBegin || '18:00',
							sleepEnd: res.sleepEnd || '10:00',
							sleepOutBedTimeout: res.sleepOutBedTimeout || 3,
						}
						console.log("devInfo:", this.form);
					}
				}).catch(err => {
					this.form = {}
				}).finally(() => {
					setTimeout(() => {
						uni.hideLoading();
					}, 1000)
				})
			},
			isNumber(n) {
				return !isNaN(parseFloat(n)) && isFinite(n);
			},
			isInteger(value) {
				return /^(\-|\+)?\d+$/.test(value);
			},
			handleSave() {
				if (!this.isInteger(this.form.sleepOutBedTimeout)) {
					uni.showModal({
						content: '请输入整数',
						showCancel: false,
						confirmText: '关闭'
					})
					return;
				}
				if (parseInt(this.form.sleepOutBedTimeout) > 30 || parseInt(this.form.sleepOutBedTimeout) < 1) {
					uni.showModal({
						content: '离床超时需小于等于30分钟',
						showCancel: false,
						confirmText: '关闭'
					})
					return;
				}
				let _params = {
					id: this.form.id,
					...this.form,
				}
				console.log(_params)
				uni.showLoading({
					title: '保存中...',
					mask: true
				})
				this.$u.api.saveSleepReportSetting(_params).then(res => {
					uni.hideLoading();
					uni.showToast({
						duration: 2000,
						title: '设置成功',
						icon: 'none'
					})
					setTimeout(() => {
						uni.navigateBack({
							delta: 1
						})
					}, 1000);
				}).catch(err => {
					uni.hideLoading();
					console.log('err', err)
					uni.showToast({
						duration: 2000,
						title: err.message || '发生未知错误',
						icon: 'none'
					})
				}).finally(() => {})
			},
		}
	}
</script>

<style lang="scss">
	page {
		background-color: #f7f7f7;
		// #ifdef H5
		height: 100%;
		// #endif
	}
</style>
<style lang="scss" scoped>
	.device-connector-edit-page {
		background-color: white;

		::v-deep .u-label {
			font-size: 32rpx;
			width: 240rpx;
			display: block;
			color: #0D0D0D;
			flex: initial !important;

			&::before {
				font-size: 40rpx;
				top: 10rpx;
			}

			.u-label-text {
				padding-left: 20rpx;
				font-weight: bold;
				box-sizing: border-box;
			}
		}

		.direction-field {
			::v-deep .u-field-inner {
				height: 120rpx;
				align-items: baseline;
			}
		}

		.footer-btns {
			position: absolute;
			bottom: 40rpx;
			left: 40rpx;
			right: 40rpx;
			margin: 0 auto;

			.next-btn {
				display: block;
				margin-top: 20rpx;
			}
		}

		::v-deep .u-form-item--left {
			width: 300rpx !important;
			flex: 0 0 300rpx !important;
		}

		::v-deep .u-form-item--left__content__label {
			font-size: 32rpx;
			display: block;
			color: #0D0D0D;
			flex: initial !important;
			padding-left: 20rpx;
			font-weight: bold;
			box-sizing: border-box;

			&::before {
				font-size: 40rpx;
				top: 10rpx;
			}

		}

		::v-deep .u-model__footer__button {
			height: 88rpx;
			line-height: 88rpx;
		}

		::v-deep .u-field {
			padding: 28rpx;
		}
	}
</style>