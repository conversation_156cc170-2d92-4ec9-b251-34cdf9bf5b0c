<template>
	<view class="device-index-page">
		<view class="top-header-line"></view>

		<view style="text-align: left; margin: 120rpx 60rpx; 0 60rpx">
			<!-- <image class="img-center-bg" :src="`${staticUrl}/images/device/add-device-pic1.png`"></image> -->
			<view style="font-size:32rpx;color:#000000;">如何添加设备</view>
			<view style="font-size:24rpx;color:#000000">
				<view style="padding: 30rpx 0;">
					1、请确保设备<text style="color:#01B09A">已插电</text>或<text style="color:#01B09A">电量充足</text>
				</view>
				<view>
					2、请扫描<text style="color:#01B09A">设备背面</text>或<text style="color:#01B09A">设备包装盒上</text>的二维码/条形码进行设备添加
				</view>
			</view>
			<!-- <u-checkbox-group style="position: relative; top: 40rpx; left: 30rpx;">
				<u-checkbox v-model="checked" shape="circle" style="font-size: 22rpx;">已完成上述操作</u-checkbox>
			</u-checkbox-group> -->
		</view>

		<!-- #ifdef MP-WEIXIN -->
		<view class="footer-btns">
			<u-button shape="circle" class="scan-code-btn diy-btn" size="medium"
				:custom-style="{ 'width': '100%', 'color': 'white', 'background-color': '#01B09A' }" hover-class="none"
				@click="scan">扫一扫</u-button>
			<!-- <u-button shape="circle" class="scan-code-btn diy-btn" size="medium" 
			:custom-style="{ 'width': '100%', 'color': 'white', 'background-color': '#01B09A' }"  hover-class="none"
			@click="scan">确认</u-button> -->
		</view>
		<!-- #endif -->

	</view>
</template>

<script>
	import {isUrl,getUrlParam} from '../../utils/util'
	export default {
		data() {
			return {
				staticUrl: this.$u.http.config.staticBaseUrl,
				checked: false
			}
		},
		onLoad(option) {//默认加载
			// this.login();
			//#ifdef MP-WEIXIN
			wx.hideHomeButton()
			//#endif
			if (option.devCode) {
				this.vali(option.devCode)
			}
		},
		onShow() {
			// this.fetchFuwuCount();
		},
		methods: {
			handleCheckedClick() {
				this.checked = this.checked === true ? false : true
			},
			gotoUrl(url) {
				if (!url) return;
				uni.navigateTo({
					url: url
				})
			},
			scan() {
				// 允许从相机和相册扫码
				uni.scanCode({
					success: (res) => {
						console.log('条码类型：' + res.scanType);
						console.log('条码内容：' + res.result);
						if (!res.result) {
							uni.showLoading({
								title: '解析二维码错误...',
								mask: true
							})
							return
						}
						let _devCode;
						const isurl = isUrl(res.result+"");
						//const isurl = res.result.startsWith("http");
						if(isurl){
							console.log("二维码为url")
							_devCode = getUrlParam(res.result,"id");
							if(!_devCode){
								_devCode = getUrlParam(res.result,"mdid");
							}
							if(!_devCode){
								_devCode = getUrlParam(res.result,"devCode");
							}
						}else{
							console.log("二维码为设备id")
							_devCode = res.result;
						}
						console.log('设备编码：' + _devCode);
						uni.setStorageSync('devCode', _devCode)
						uni.removeStorageSync('devId');
						const url = `/pagesDevice/add/result?devCode=${_devCode}`;
						console.log(url);
						uni.redirectTo({
							url
						})
					},
					fail: (err) => {
						console.log(err);
						uni.showToast({
							duration: 1000,
							title: '二维码无法识别',
							icon: 'none'
						})
					}
				});
			}
		}
	}
</script>

<style lang="scss">
page {
	// #ifdef H5
	height: 100%;
	// #endif
}
</style>

<style lang="scss" scoped>
@import url("/static/css/iconfont.css");

.device-index-page {
	height: 100vh;
	// #ifdef H5
	height: 100%;
	// #endif
	position: relative;
	overflow: hidden;
	box-sizing: border-box;
	text-align: center;

	.img-center-bg {
		width: 690rpx;
		height: 450rpx;
		margin: 0 auto;
	}

	.tip {
		display: block;
		margin-top: 30rpx;
		margin-left: 28rpx;
		font-size: 30rpx;
		color: #000;
		font-size: 30rpx;
		font-weight: 500;
	}

	.footer-btns {
		position: absolute;
		bottom: 40rpx;
		left: 40rpx;
		right: 40rpx;
		margin: 0 auto;

		.scan-code-btn {
			display: block;
			margin-top: 37rpx;
		}
	}

}
</style>
