<template>
	<view class="verification-result-page">
		<!-- aladding 没有该情况，不知道如何
		<view>
			<view style="margin: 80rpx 0 10rpx 0;">
				<u-icon size="160" color="#d9001b" name="close-circle-fill"></u-icon>
			</view>
			<view>
				设备 {{devCode}}
			</view>
			<view style="padding:30rpx 10rpx 80rpx 10rpx;">
				验证不通过
			</view>
			<view style="text-align:left;padding: 0 120rpx;">
				<view style="padding-bottom:10rpx;">
					1、请先确认设备是否<text style="color:#01B09A">正常通电</text>
				</view>
				<view style="">
					2、请尝试<text style="color:#01B09A">重新扫描</text>设备条形码或二维码
				</view>
			</view>
		</view>
		-->
		<template v-if="isAllowInstall === 1">
			<rich-text-modal
				:show="serverAgreement.agreeStep == 1"
				:content="serverAgreement.simpleAgreement"
				@confirm="agreeAgreement(true)"/>
			<member-agreement-popup
				:show="serverAgreement.agreeStep == 2"
				:content="serverAgreement.agreement"
				@confirm="agreeAgreement(true)"
				@cancel="agreeAgreement(false)"/>
		</template>
		<view v-if="isAllowInstall===1">
			<view style="margin: 80rpx 0 10rpx 0;">
				<u-icon size="160" color="#49de9c" name="checkmark-circle-fill"></u-icon>
			</view>
			<view>
				设备 {{devCode}}
			</view>
			<view style="padding:30rpx 10rpx 80rpx 10rpx;">
				验证成功，设备{{ status.isActive ? `已` : `未`}}激活
			</view>
			<template v-if="!status.isOnline">
				<!-- 这里不能因为离线就禁止下一步，因为未激活的设备本身就会离线，这么处理的话会影响设备激活流程 -->
				<view style="padding:30rpx 10rpx 80rpx 10rpx;">
					该设备当前离线
				</view>
			</template>
			<template v-if="status.isSelfBind">
				<view>
					您<text style="color:#01B09A">已经添加过</text>该设备,可在首页查看
				</view>
			</template>
			<template v-if="status.isOtherBind">
				<view>
					用户&nbsp;&nbsp;<text style="color:#01B09A">{{phone}}</text>&nbsp;&nbsp;正在添加设备
				</view>
			</template>
		</view>
		<view v-else-if="isAllowInstall===2">
			<view style="margin: 80rpx 0 10rpx 0;">
				<u-icon size="160" color="#d9001b" name="close-circle-fill"></u-icon>
			</view>
			<view>
				设备 {{devCode}}
			</view>
			<template v-if="!status.isExist">
				<view style="padding:30rpx 10rpx 80rpx 10rpx;">
					条形码/二维码无法识别，请尝试重新扫描
				</view>
			</template>
			<template v-if="!status.hasModel">
				<view style="padding:30rpx 10rpx 80rpx 10rpx;">
					该设备还没有产品型号
				</view>
			</template>
			<template v-if="!status.isOnline">
				<view style="padding:30rpx 10rpx 80rpx 10rpx;">
					该设备当前离线
				</view>
			</template>
			<template v-if="status.isOtherBind">
				<view style="padding:30rpx 10rpx 80rpx 10rpx;">
					<!-- {{status.isSelfBind  ? `您已经添加过此设备` : `已经被用户 ${devOwner.phone} 添加`}} -->
					已经被用户 {{phone || ''}} 添加
				</view>
				<view style="text-align:left;padding: 0 60rpx;">
					<view style="padding-bottom:10rpx;">
						1、若变更账号，可用原账号<text style="color:#01B09A">删除设备</text>后使用新账号重新添加设备
					</view>
					<view style="">
						2、若不是您的设备，可以让设备主人 <text style="color:#01B09A">分享设备</text>或者<text style="color:#01B09A">分享家庭</text>
					</view>
				</view>
			</template>
		</view>
		<view v-if="agree === true" class="footer-btns">
			<u-button v-if="isAllowInstall===1" shape="circle" class="next-btn diy-btn"
				size="medium" :custom-style="{ 'width': '100%', 'color': 'white', 'background-color': '#01B09A' }"
				hover-class="none" @click="configInstall">下一步</u-button>

			<u-button v-else shape="circle" class="next-btn diy-btn" size="medium"
				:custom-style="{ 'width': '100%', 'color': '#01B09A', 'background-color': 'white' }" hover-class="none"
				@click="gotoHome">返回首页</u-button>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			status: {},
			devCode: undefined,
			isAllowInstall: 0,
			agree:false,
			phone:"",
			serverAgreement: {
				agreeStep: 0,
				serverIds:[],
				simpleAgreement: null,
				agreement: null,
			},
		}
	},
	async onLoad(option) { //option为object类型，会序列化上个页面传递的参数
		console.log(option)
		this.devCode = option.devCode || '';
		// 此处解开可以用于不扫码直接根据需要安装的设备ID进入安装流程的页面调试，只需在页面后加：
		// ?devCode=mdid即可
		uni.setStorageSync('devCode', this.devCode)
		// uni.removeStorageSync('devId');
		await this.fetchDeviceInfo(option.devCode)
	},
	computed: {
		// 设备存在、有型号、有服务站、没有被别人绑定的才可以继续安装
		// isAllowInstall() {
		// 	return this.status.isExist === true && this.status.hasModel === true
		// 						&& this.status.hasStation === true && this.status.isOtherBind !== true;
		// },
	},
	methods: {
		maskedPhone(val) {
			if(!val){return}
			return `${val.substring(0, 3)}****${val.substring(7)}`;
		},
		gotoHome() {
			uni.switchTab({
				url: '/pages/index/index'
			})
		},
		configInstall() {
			if (!this.isAllowInstall) {
				uni.showToast({
					duration: 2000,
					title: '安装错误',
					icon: 'none'
				})
				return;
			}
			if(this.status.deviceType=='1' || this.status.deviceType=='2' || this.status.deviceType=='5'){
				
				// 微信环境下可以配网
				//#ifdef MP-WEIXIN
				// 不在线则配网
				if(!this.status.isOnline){
					this.$navTo(`pagesDevice/config/wifi-notice`);
					return;
				}
				//#endif 
			}
			
			// 不然直接配置房间信息
			this.$navTo(`pagesDevice/config/tag-config`);
		},
		async fetchDeviceInfo(devCode) {
			uni.showLoading({
				title: '加载中...',
				mask: true
			})
			this.status = await this.$u.api.execCheckDevCode({ devCode: devCode });
			console.log("fetchDevCheckDevCode:",this.status);
			setTimeout(() => {
				uni.hideLoading();
			}, 1000)
			if(this.status){
				let flag = this.status.isExist === true && this.status.hasModel === true
								&& this.status.hasStation === true && this.status.isOtherBind !== true;
				this.isAllowInstall = flag?1:2;
				this.phone = this.maskedPhone(this.status.memberPhone);
			}
			if(this.isAllowInstall===1){
				// 自己已经绑定的，跳转到首页
				// if(this.status.isSelfBind){
				// 	uni.showToast({ duration: 2000, title: '您已添加该设备，前往首页查看', icon: 'none' })
				// 	setTimeout(() => {
				// 		uni.reLaunch({
				// 			url: '/pages/index/index'
				// 		})
				// 	}, 1000);
				// }
				this.$u.api.listByDevCodeAgreement(this.devCode)
					.then(res => {
						// 此处返回的结果在api里面被处理过，并不是接口的结果
						if(res.agreeStep===0){
							this.agree = true;
						}
						this.serverAgreement = res;
						if (res.serverIds.length) {
							this.serverAgreement.agreeStep = 1;
						}
					}).catch(err => {
						console.log('err', err)
					});
			}
		},
		async agreeAgreement(agree) {
			if (agree === false) {
				this.serverAgreement.agreeStep = 0;
				this.agree = false;
				return;
			}
			this.agree = true;
			this.serverAgreement.agreeStep++;
			if (this.serverAgreement.agreeStep == 3) {
				this.serverAgreement.agreeStep = 0;
				try {
					await this.$u.api.agreeByDevCodeAgreement({
						devCode: this.devCode
					});
				} catch (err) {
					console.error(err);
				}
			}
		},
	}
}
</script>

<style lang="scss" scoped>
.verification-result-page {
	height: 80vh;
	box-sizing: border-box;
	text-align: center;
	background-color: #FFFFFF;

	.top-tip {
		display: inline-block;
		background: #ff8989;
		color: white;
		border-radius: 10rpx;
		padding: 2rpx 10rpx;
		margin-left: 20rpx;
		font-size: 24rpx;
		position: relative;
		top: -2rpx;
	}

	.device-info {
		padding: 40rpx 0rpx 0rpx 40rpx;
		font-size: 30rpx;
		color: #5E5D5D;
		text-align: left;
	}

	.top-wrap {
		padding-top: 128rpx;

		.img-center-bg {
			width: 268rpx;
			height: 268rpx;
			margin: 0 auto;
		}

		.tip {
			font-size: 30rpx;
			color: #5E5D5D;
			font-size: 30rpx;
			font-weight: 500;
			margin-top: 55rpx;
			font-weight: bold;
		}
	}

	.footer-btns {
		position: absolute;
		bottom: 40rpx;
		left: 60rpx;
		right: 60rpx;
		margin: 0 auto;

		.next-btn {
			display: block;
			margin-top: 20rpx;
		}
	}
}
</style>
