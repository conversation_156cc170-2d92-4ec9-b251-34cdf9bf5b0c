// 这里的vm，就是我们在vue文件里面的this，所以我们能在这里获取vuex的变量，比如存放在里面的token
// 同时，我们也可以在此使用getApp().globalData，如果你把token放在getApp().globalData的话，也是可以使用的
const install = (Vue, vm) => {
	Vue.prototype.$u.http.setConfig({
		
		// 测试环境
		// baseUrl: 'http://localhost:8090',
		// baseUrl: 'https://rfcare.106tec.com/api',
		// wsBaseUrl: 'ws://*************:8090',
		// staticBaseUrl: 'https://rfcare.106tec.com/rfcare-static',
		// staticBaseUrl: 'http://*************:10010',
		
		// 开发环境--真机模拟，配置电脑局域网地址
		// baseUrl: 'http://**************:8090',
		// wsBaseUrl: 'ws://**************:8090',
		// staticBaseUrl: 'https://test.rfcare.cn',
		
		// 开发环境--真机模拟,配置电脑局域网地址,本地h5
		// baseUrl: 'http://**************:8090',
		// wsBaseUrl: 'ws://**************:8090',
		// staticBaseUrl: 'https://test.rfcare.cn',
		// cmccUrl: 'http://**************',
		
		// 开发环境
		// baseUrl: 'http://*************:8090',
		// wsBaseUrl: 'ws://*************:8090',
		// staticBaseUrl: 'https://test.rfcare.cn',
		// cmccUrl: 'http://cmcctest.rfcare.cn',
		
		
		// 测试环境--小程序
		// baseUrl: 'https://test.rfcare.cn/api',
		// wsBaseUrl: 'wss://test.rfcare.cn',
		// staticBaseUrl: 'https://test.rfcare.cn',
		// cmccUrl: 'https://cmcctest.rfcare.cn',
		
		
		// 正式环境--小程序
		// baseUrl: 'https://www.rfcare.cn/api',
		// wsBaseUrl: 'wss://www.rfcare.cn',
		// staticBaseUrl: 'https://www.rfcare.cn',
		// cmccUrl: 'https://cmcc.rfcare.cn',
		
		// 生产/测试环境--H5部署
		baseUrl: '/api',
		wsBaseUrl: '',
		staticBaseUrl: '',
		
		
		// 如果将此值设置为true，拦截回调中将会返回服务端返回的所有数据response，而不是response.data
		// 设置为true后，就需要在this.$u.http.interceptor.response进行多一次的判断，请打印查看具体值
		// originalData: true, 
		// 设置自定义头部content-type
		// header: {
		// 	'content-type': 'xxx'
		// }
	});
	// 请求拦截，配置Token等参数
	Vue.prototype.$u.http.interceptor.request = (config) => {
		// config.header.Token = 'xxxxxx';
		
		// 方式一，存放在vuex的token，假设使用了uView封装的vuex方式，见：https://uviewui.com/components/globalVariable.html
		// config.header.token = vm.token;
		
		// 方式二，如果没有使用uView封装的vuex方法，那么需要使用$store.state获取
		// config.header.token = vm.$store.state.token;
		
		// 方式三，如果token放在了globalData，通过getApp().globalData获取
		// config.header.token = getApp().globalData.username;
		
		// 方式四，如果token放在了Storage本地存储中，拦截是每次请求都执行的，所以哪怕您重新登录修改了Storage，下一次的请求将会是最新值
		const token = uni.getStorageSync('token');
		config.header.token = token;
		return config; 
	}
	// 响应拦截，判断状态码是否通过
	Vue.prototype.$u.http.interceptor.response = (res) => {
		// 如果把originalData设置为了true，这里得到将会是服务器返回的所有的原始数据
		// 判断可能变成了res.statueCode，或者res.data.code之类的，请打印查看结果
		let _serverResp = res;

		let routes = getCurrentPages();
		let r = routes[0];
		let curRoute = r?r.route:'' //获取当前页面路由
		//console.log("res:",_serverResp);
		if (_serverResp.status == '00000') {
			// 如果把originalData设置为了true，这里return回什么，this.$u.post的then回调中就会得到什么
			return _serverResp.data;
		} else {
			if (res.message) {
				if (res.message.indexOf('TOKEN WAS WRONG') != -1) {
					if (curRoute.indexOf('supplement') !== -1) {
					} else if (curRoute.indexOf('auth/login') === -1) {
						// 清除无效的token和用户信息
						uni.removeStorageSync('token');
						uni.removeStorageSync('member');

						// 跳转到登录页面
						// #ifdef H5
						// 在H5环境下，检查当前URL是否有token参数，如果有则保留
						let urlParams = new URLSearchParams(window.location.search);
						let tokenParam = urlParams.get('token');

						if (tokenParam) {
							// 如果URL中有token参数，跳转到登录页面并传递参数
							uni.reLaunch({
								url: `/pages/auth/login/index?${urlParams}`
							})
						} else {
							uni.reLaunch({
								url: '/pages/auth/login/index'
							})
						}
						// #endif

						// #ifndef H5
						uni.reLaunch({
							url: '/pages/auth/login/index'
						})
						// #endif
					}
				} else {
					if (res.needTip === undefined || res.needTip === true) {
						uni.showToast({
							duration: 2000,
							title: res.message,
							icon: 'none'
						})
					}
				}
				// this.$refs.uToast.show({
				// 	title: res.message,
				// 	position: 'top',
				// 	type: 'error',
				// 	icon: false
				// });
			}
			return false;
		}
	}
}

export default {
	install
}