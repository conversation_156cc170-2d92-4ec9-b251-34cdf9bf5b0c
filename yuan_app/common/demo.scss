/* #ifndef APP-NVUE */
view,
text {
	box-sizing: border-box;
}
/* #endif */
page{
	height: 100%;
}
$g-clr-green:#33bfae;
$g-clr-green80:#33BFAE;
$g-clr-green60:#66CFC2;
$g-clr-green40:#99DFD6;
$g-clr-green20:#CCEFEB;
$g-clr-green10:#E5F7F5;

$g-clr-green-2:#20CD8A;

$g-clr-orange:#FCC14F;
$g-clr-orange-2:#FC9B4F;

$g-clr-red:#FA6400;
$g-clr-blue:#45B3FF;

$g-clr-gray-3:#333333;
$g-clr-gray-6:#666666;
$g-clr-gray-9:#999999;
$g-clr-gray-c:#cccccc;
$g-clr-gray-e:#eeeeee;

.top-header-line {
	position: fixed;
	top: 0rpx;
	left: 0rpx;
	right: 0rpx;
	height: 2rpx;
	background: #C7C7C7;
	z-index: 1;
}

.r-flex {
	display: flex;
	flex-direction: row;
	align-items: center;
	&-1 {
		flex: 1;
	}
}
.c-flex {
	display: flex;
	flex-direction: column;
	justify-items: center;
	&-1 {
		flex: 1;
	}
}

.vc-flex {
	display: flex;
	align-items: center;
}
.vr-flex {
	display: flex;
	justify-content: center;
}
.vcr-flex {
	display: flex;
	align-items: center;
	justify-content: center;
}

.ta-c {
	text-align: center;
}
.ta-r {
	text-align: right;
}
.jc-sb{
	justify-content: space-between;
}
.jc-sa{
	justify-content: space-around;
}
.jc-fe{
	justify-content: flex-end
}
.jc-c{
	justify-content: center
}
.leve-tag-orange {
	background: #F66C3E !important;
}
.leve-tag-green {
	background: #81D88A !important;
}
.leve-tag-red {
	background: #ED2E1C !important;
}
.leve-tag-grey {
	background: #484848 !important;
}

.diy-btn {
	::v-deep button {
		height: 86rpx !important;
		font-size: 30rpx !important;
	}
}
.expand-hotspot{
	position: relative;
}
.expand-hotspot::after {
    content: '';
    position: absolute;
    top: -16rpx;
    right: -10rpx;
    bottom: -16rpx;
    left: -100rpx;
    border: 2rpx;
	 // border: 2rpx solid olivedrab;
 }
 
 // 自动生成代码的样式
 .flex-row {
   display: flex;
   flex-direction: row;
 }
 
 .flex-col {
   display: flex;
   flex-direction: column;
 }
 
.justify-start {
  justify-content: flex-start;
}

.justify-end {
  justify-content: flex-end;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-around {
  justify-content: space-around;
}

.justify-evenly {
  justify-content: space-evenly;
}

.items-start {
  align-items: flex-start;
}

.items-end {
  align-items: flex-end;
}

.items-center {
  align-items: center;
}

.items-baseline {
  align-items: baseline;
}

.items-stretch {
  align-items: stretch;
}

.self-start {
  align-self: flex-start;
}

.self-end {
  align-self: flex-end;
}

.self-center {
  align-self: center;
}

.self-baseline {
  align-self: baseline;
}

.self-stretch {
  align-self: stretch;
}

.flex-1 {
  flex: 1 1 0%;
}

.flex-auto {
  flex: 1 1 auto;
}

.grow {
  flex-grow: 1;
}

.grow-0 {
  flex-grow: 0;
}

.shrink {
  flex-shrink: 1;
}

.shrink-0 {
  flex-shrink: 0;
}

.relative {
  position: relative;
}