// 如果没有通过拦截器配置域名的话，可以在这里写上完整的URL(加上域名部分)
let hotSearchUrl = '/ebapi/store_api/hot_search';
let indexUrl = '/ebapi/public_api/index';
let loginUrl = '/pinanbao/login';

let houseEventHasDoingUrl = '/pinanbao/event/hasDoing';
let houseEventInfoUrl = '/pinanbao/event/getEventInfo';
let houseDeviceListUrl = '/pinanbao/house/device/list';
let memberDetailInfoUrl = '/pinanbao/member/detail/info';
let houseSosBakUrl = '/pinanbao/house/sos-bak';

let getRealTimeMonitorUrl = '/pinanbao/house/getRealTimeMonitorUrl';

// 此处第二个参数vm，就是我们在页面使用的this，你可以通过vm获取vuex等操作，更多内容详见uView对拦截器的介绍部分：
// https://uviewui.com/js/http.html#%E4%BD%95%E8%B0%93%E8%AF%B7%E6%B1%82%E6%8B%A6%E6%88%AA%EF%BC%9F
const install = (Vue, vm) => {
	// 此处没有使用传入的params参数
	let getSearch = (params = {}) => vm.$u.get(hotSearchUrl, {
		id: 2
	});
	
	// 此处使用了传入的params参数，一切自定义即可
	let getInfo = (params = {}) => vm.$u.post(indexUrl, params);
	
	// 此处使用了传入的params参数，一切自定义即可
	let login = (params = {}) => vm.$u.post(loginUrl, params);
	
	let fetchHouseEventHasDoing = (params = {}) => vm.$u.get(houseEventHasDoingUrl, params, {}, false);
	let fetchHouseEventInfo = (params = {}) => vm.$u.get(houseEventInfoUrl, params);
	// 此处使用了传入的params参数，一切自定义即可
	let fetchHouseDeviceList = (params = {}) => vm.$u.get(houseDeviceListUrl, params);
	let fetchMemberDetailInfo = (params = {}) => vm.$u.post(memberDetailInfoUrl, params);
	
	let execHouseSosBak = (params = {}) => vm.$u.post(houseSosBakUrl, params);
	
	let fetchRealTimeMonitor = (params = {}) => vm.$u.get(getRealTimeMonitorUrl, params);

	// 解析手机号码
	let execDecodePhoneNumber = (params = {}) => vm.$u.post(`/pinanbao/decodePhoneNumber?code=${params.code}&encryptedData=${params.encryptedData}&iv=${params.iv}`, params);

	// 获取验证码
	let fetchRegVCode = (params = {}) => vm.$u.get(`/pinanbao/getVCode`, params);
	// 新用户注册
	let execRegisterPhone = (params = {}) => vm.$u.get(`/pinanbao/registerPhone`, params);
	// 设置新密码
	let execChangePwd = (params = {}) => vm.$u.post(`/pinanbao/changePwd`, params);
	// 校验验证码
	let execCheckVCode = (params = {}) => vm.$u.get(`/pinanbao/checkVCode?phone=${params.phone}&vcode=${params.vcode}`, params);
	// 校验token
	let execCheckToken = (params = {}) => vm.$u.post(`/pinanbao/checkToken`, params);

	// 获取设备安装信息（地址（经纬度）、设备标签、场景）
	let fetchDevInstallationInfo = (params = {}) => vm.$u.get(`/installation/api/getDevInstallationInfo`, params);
	// 获取设备安装信息（地址（经纬度）、设备标签、场景）
	let fetchMyDeviceInfo = (params = {}) => vm.$u.get(`/pinanbao/device/my/getMyDeviceInfo?devId=${params.devId}`, params);
	
	// 我的设备列表
	let fetchMyDeviceList = (params = {}) => vm.$u.get(`/pinanbao/device/my/myDeviceList`, params);
	// 
	let  updateFirmwareVersion = (params = {}) => vm.$u.get(`/pinanbao/device/updateFirmwareVersion`, params);
	let  checkUpgradeableVersion = (params = {}) => vm.$u.get(`/pinanbao/device/checkUpgradeableVersion`, params);
	let  countByFamilyIdAndDevScene = (params = {}) => vm.$u.get(`/pinanbao/device/countByFamilyIdAndDevScene`, params);
	// 不再使用
	// aladding 设备所有人
	let fetchDeviceOwner = (params = {}) => vm.$u.get(`/pinanbao/device/getOwner`, params);
	
	// 验证设备
	let execCheckDevCode = (params = {}) => vm.$u.post(`/pinanbao/devAdd/checkDevCode?devCode=${params.devCode}`, params);
	// 添加设备-绑定用户
	let execBindUser = (params = {}) => vm.$u.post(`/pinanbao/devAdd/bindUser?devCode=${params.devCode}&phone=${params.phone}&familyId=${params.familyId}`, params);
	
	// 被监护人列表
	let fetchOlderMyList = (params = {}) => vm.$u.get(`/pinanbao/older/my/list`, params);
	// 家庭的被监护人列表
	let queryOlderByFamilyId = (params = {}) => vm.$u.get(`/pinanbao/older/queryOlderByFamilyId`, params);
	// 被监护人新增
	let execOlderMyAdd = (params = {}) => vm.$u.post(`/pinanbao/older/my/add`, params);
	// 被监护人修改
	let execOlderMyUpdate = (params = {}) => vm.$u.post(`/pinanbao/older/my/update`, params);
	// 老人是否有守护服务订单
	let olderHasOrder= (params = {}) => vm.$u.get(`/pinanbao/older/hasOrder`, params);
	// 查询我的设备拥有的被监护人列表
	let fetchMyOlderListByDeviceId = (params = {}) => vm.$u.get(`/pinanbao/device/my/queryMyOlderListByDeviceId?devId=${params.devId}`, params);
	// 被监护人详情
	let fetchMyOlderInfo = (params = {}) => vm.$u.get(`/pinanbao/older/my/getMyOlderInfo?olderId=${params.olderId}`, params);
	// 删除被监护人
	let execOlderMyDelete = (params = {}) => vm.$u.delete(`/pinanbao/older/my/delete/${params.olderId}`, params);
	// aladding 获取销售产品信息明细
	let fetchProductPackageDetail = (params = {}) => vm.$u.get(`/pinanbao/device/getProductPackageDetail`, params);
	
	
	// 查询我的设备拥有的紧急联系人列表
	let fetchMyContactListByDeviceId = (params = {}) => vm.$u.get(`/pinanbao/device/my/queryMyContactListByDeviceId?devId=${params.devId}`, params);
	// 紧急联系人列表
	let fetchContactMyList = (params = {}) => vm.$u.get(`/pinanbao/contact/my/list`, params);
	// 紧急联系人列表
	let queryContactByFamilyId = (params = {}) => vm.$u.get(`/pinanbao/contact/queryByFamilyId`, params);
	// 紧急联系人详情
	let fetchMContactInfo = (params = {}) => vm.$u.get(`/pinanbao/contact/my/getMyContactInfo?contactId=${params.contactId}`, params);

	// 选择我的紧急人列表中某个保存绑定
	// 220916不再绑定到设备
	let execDeviceBindHouseContact = (params = {}) => vm.$u.post(`/pinanbao/device/my/bindHouseContact?devId=${params.devId}&contactId=${params.contactId}`, params);
	// 移除被紧急联系人（解除绑定）
	let execDeviceunBindHouseContact = (params = {}) => vm.$u.post(`/pinanbao/device/my/unHouseContact?devId=${params.devId}&contactId=${params.contactId}`, params);
	// 删除紧急联系人
	let execContactMyDelete = (params = {}) => vm.$u.delete(`/pinanbao/contact/my/delete/${params.contactId}`, params);
	
	// 紧急联系人新增
	let execContactMyAdd = (params = {}) => vm.$u.post(`/pinanbao/contact/my/add`, params);
	// 紧急联系人修改
	let execContactMyUpdate = (params = {}) => vm.$u.post(`/pinanbao/contact/my/update`, params);
	// 紧急联系人新增/修改
	let execContactMyAddOrUpdate = (params = {}) => vm.$u.post(`/pinanbao/contact/my/addOrUpdate`, params);
	// 紧急联系人查询
	let fetchMyContact = (params = {}) => vm.$u.post(`/pinanbao/contact/my/getMyContact`, params);
	
	// 设备修改(设备名，场景、响应时间)
	let execUpdateMyDevice = (params = {}) => vm.$u.post(`/pinanbao/device/my/updateMyDevice`, params);
	// aladding 修改设备安装方式
	let updateDeviceSpotType = (params = {}) => vm.$u.post(`/pinanbao/device/my/updateSpotType`, params);
	// aladding 只修改设备名
	let execUpdateDeviceName = (params = {}) => vm.$u.post(`/pinanbao/device/my/updateName`, params);
	
	// 服务站简介
	let fetchMyDevStationInfo = (params = {}) => vm.$u.get(`/pinanbao/device/my/getMyDevStationInfo?stationId=${params.stationId}`, params);
	// 在设备列表中移除设备（解除绑定）
	let execUnBindMyDevice = (params = {}) => vm.$u.post(`/pinanbao/device/my/unBindMyDevice?devId=${params.devId}`, params);
	
	// 事件 分页
	let fetchEventList = (params = {}) => vm.$u.get('/pinanbao/event/allList', params);
	
	// 某个事件的详细处理记录
	let fetchEventInfo = (params = {}) => vm.$u.get(`/pinanbao/event/getEventInfo?orderId=${params.orderId}`, params);
	
	// 修改个人信息（姓名、地址、头像、昵称、性别等）
	let execUpdateMyInfo = (params = {}) => vm.$u.post(`/pinanbao/member/update/updateMyInfo`, params);
	// 查询授权列表
	let selectAuthorizeList = (params = {}) => vm.$u.get(`/pinanbao/member/selectAuthorizeList`, params);
	// 查询未授权数量
	let getUnauthorizedCount = (params = {}) => vm.$u.get(`/pinanbao/member/getUnauthorizedCount`, params);
	// 第三方授权/拒绝授权,1授权，2拒绝
	let authorizeThirdParty = (params = {}) => vm.$u.get(`/pinanbao/member/authorizeThirdParty`, params);
	let getVoIPPwd = (params = {}) => vm.$u.get(`/pinanbao/member/getVoIPPwd`, params);
	
	// 卧室在床时间统计图表
	let fetchInbedTimeChartList = (params = {}) => vm.$u.get(`/pinanbao/house/device/getInbedTimeChartList`, params, {}, false);
	
	// 卧室在床时间统计图表
	let fetchToiletInoutChartList = (params = {}) => vm.$u.get(`/pinanbao/house/device/getToiletInoutChartList`, params);
	
	// 生成紧急联系人邀请二维码
	let execWxQrcodeGen = (params = {}) => vm.$u.get(`/pinanbao/wx/qrcode/gen`, params);
	// aladding 生成紧急联系人邀请二维码(家庭)
	let execWxQrcodeGenFamily = (params = {}) => vm.$u.get(`/pinanbao/wx/qrcode/invite/family/contact`, params);
	
	// 
	let fetchZengzhiServerList = (params = {}) => vm.$u.post(`/pinanbao/device/zengzhiServerList?devId=${params.devId}`);
	// aladding 列出设备的可购买增值服务
	let listAddServerDevice = (params = {}) => vm.$u.post(`/pinanbao/device/listAddServer?devId=${params.devId}`);
	
	// 获取订单列表
	let fetchOrderList = (params = {}) => vm.$u.get(`/pinanbao/server-order/listOrder`, params);
	
	// 获取订单详情
	let fetchOrderDetail = (params = {}) => vm.$u.get(`/pinanbao/server-order/getOrderInfo/${params.id}`);

	// 验证密码获取会员卡信息
	let expiresWithinOneMonth = (params = {}) => vm.$u.get(`/pinanbao/server-order/expiresWithinOneMonth`, params);

	// 验证密码获取会员卡信息
	let getCardByPassword = (params = {}) => vm.$u.get(`/pinanbao/card/getCardByPassword`, params);
	let getCardById = (params = {}) => vm.$u.get(`/pinanbao/card/getCardById`, params);
	// 根据会员卡id获取会员卡绑定的设备
	let getCardDevicesByCardId = (params = {}) => vm.$u.get(`/pinanbao/card/getCardDevicesByCardId`, params);
	// 设备扫码记录
	let cardScan = (params = {}) => vm.$u.post(`/pinanbao/card/cardScan?cardNo=${params.cardNo}`, params);
	// 设备延期
	let insertCardDevices = (params = {}) => vm.$u.post(`/pinanbao/server-order/insertCardDevices`, params);
	// 会员卡提货
	let takeDeliveryGoods = (params = {}) => vm.$u.post(`/pinanbao/delivery/takeDeliveryGoods`, params);
	// 会员卡提货分页列表
	let selectDeliveryByPage = (params = {}) => vm.$u.post(`/pinanbao/delivery/selectByPage`, params);
	// 会员卡统计数
	let queryCardCount = (params = {}) => vm.$u.get(`/pinanbao/delivery/queryCardCount`, params);
	// 获取个人的会员卡激活列表
	let getCardsByMemberId = (params = {}) => vm.$u.get(`/pinanbao/card/getCardsByMemberId`, params);

	// 新增（会员卡）服务订单
	let createMemberServerByCard = (params = {}) => vm.$u.post(`/pinanbao/server-order/createMemberServerByCard`, params);

	// 新增订单
	let execSaveOrder = (params = {}) => vm.$u.post(`/pinanbao/server-order/createOrder`, params);

	// aladding 新增产品订单
	let addProductOrder = (params = {}) => vm.$u.post(`/pinanbao/server-order/createProductOrder`, params);

	// aladding 新增（无忧）服务订单
	let createMemberServerOrder = (params = {}) => vm.$u.post(`/pinanbao/server-order/createMemberServer`, params);
	
	// aladding 编辑（无忧）服务订单
	let editServerOrder = (params = {}) => vm.$u.post(`/pinanbao/server-order/edit`, params);
	
	// 预下单接口
	let execPrepay = (params = {}) => vm.$u.post(`/pinanbao/pay/prepay`, params);
	
	// 预下单接口
	let execH5Prepay = (params = {}) => vm.$u.post(`/pinanbao/payH5/prepay`, params);
	
	// 查询支付状态
	let fetchPayStatus = (params = {}) => vm.$u.get(`/pinanbao/pay/queryPayStatus/${params.id}`, { needTip: false }, {}, false);
	
	// 获取套餐信息
	let fetchServerPackageInfo = (params = {}) => vm.$u.post(`/pinanbao/device/getServerPackageInfo?devId=${params.devId}&serverId=${params.serverId}`);
	
	// 取消订单
	let execCancelOrder = (params = {}) => vm.$u.get(`/pinanbao/server-order/cancelOrder/${params.id}`);
	
	
	// 根据设备mac地址获取mdid
	let getDevCodeByMac = (params = {}) => vm.$u.post(`/installation/api/getDevCodeByMac?mac=${params.mac}`, params);
	// 查询配置房间信息的结果
	let fetchCommitRoomInfoResult = (params = {}) => vm.$u.get(`/installation/api/getCommitRoomInfoResult`, params);
	// 保存设备安装信息（地址（经纬度）、设备标签、设备场景，注意：devCode必须传
	let execSaveDevInstallation = (params = {}) => vm.$u.post(`/installation/api/saveDevInstallation`, params);
	// 获取设备安装信息（地址（经纬度）、设备标签、场景）
	// let fetchDevInstallationInfo = (params = {}) => vm.$u.get(`/installation/api/getDevInstallationInfo`, params);
	// 保存传感器信息，注意：roomId（取值devCode）必须传；id空是新增，id有是修改
	let execSaveRoomSensor = (params = {}) => vm.$u.post(`/installation/api/saveRoomSensor`, params);
	// 获取传感器信息详情
	let fetchRoomSensorInfo = (params = {}) => vm.$u.get(`/installation/api/getRoomSensorInfo`, params);
	// 获取房间信息详情
	let fetchRoomInfo = (params = {}) => vm.$u.get(`/installation/api/getRoomInfo`, params);
	// 保存房间信息，注意：roomId（取值devCode）必须传；id空是新增，id有是修改
	let execSaveRoom = (params = {}) => vm.$u.post(`/installation/api/saveRoom`, params);
	// 获取是否配置的状态接口
	let fetchConfStatusInfo = (params = {}) => vm.$u.get(`/installation/api/getConfStatusInfo`, params);
	// 提交提交房间信息
	let execCommitRoomInfo = (params = {}) => vm.$u.post(`/installation/api/commitRoomInfo?devCode=${params.devCode}`, params);
	// 获取门信息详情
	let fetchRoomGateInfo = (params = {}) => vm.$u.get(`/installation/api/getRoomGateInfo`, params);
	// 保存门信息，注意：roomId（取值devCode）必须传；id空是新增，id有是修改
	let execSaveRoomGate = (params = {}) => vm.$u.post(`/installation/api/saveRoomGate`, params);
	// 删除门信息
	let execDelRoomGate = (params = {}) => vm.$u.delete(`/installation/api/delRoomGate?devCode=${params.devCode}&gid=${params.gid}`, params);
	// 获取门信息列表
	let fetchListRoomGateInfo = (params = {}) => vm.$u.get(`/installation/api/listRoomGateInfo`, params);
	// 获取门区域信息详情
	let fetchRoomRegionInfo = (params = {}) => vm.$u.get(`/installation/api/getRoomRegionInfo`, params);
	// 删除区域信息
	let execDelRoomRegion = (params = {}) => vm.$u.delete(`/installation/api/delRoomRegion?devCode=${params.devCode}&rid=${params.rid}`, params);
	// 保存区域信息，注意：roomId（取值devCode）必须传；id空是新增，id有是修改
	let execSaveRoomRegion = (params = {}) => vm.$u.post(`/installation/api/saveRoomRegion`, params);
	// 获取区域信息列表
	let fetchListRoomRegionInfo = (params = {}) => vm.$u.get(`/installation/api/listRoomRegionInfo`, params);
	
	
	
	// 查询收货地址列表
	let fetchAddressList = (params = {}) => vm.$u.get(`/pinanbao/address/getMyAddress`, params);
	// 保存收货地址
	let saveAddress = (params = {}) => vm.$u.post(`/pinanbao/address/save`, params);
	// 删除收货地址
	let deleteAddress = (params = {}) => vm.$u.get(`/pinanbao/address/del`, params);
	// 获取收货地址
	let getAddressById = (params = {}) => vm.$u.get(`/pinanbao/address/getById`, params);
	// 获取收货地址
	let getDefaultAddress = (params = {}) => vm.$u.get(`/pinanbao/address/getDefault`, params);
	
	// 查询家庭列表
	let fetchFamilyList = (params = {}) => vm.$u.get(`/pinanbao/family/queryFamilyList`, params);
	// 新增家庭
	let execCreateFamily = (params = {}) => vm.$u.post(`/pinanbao/family/createFamily`, params);
	// 删除家庭
	let deleteFamily = (params = {}) => vm.$u.get(`/pinanbao/family/delete`, params);
	
	// 设备管理 及 查询家庭设备列表
	let fetchFamilyDevList = (params = {}) => vm.$u.get(`/pinanbao/family/queryFamilyDevList`, params);
	// 告警提醒
	let fetchTodayEventList = (params = {}) => vm.$u.get(`/pinanbao/family/getTodayEventList`, params);
	// 查事件,familyId 必须传，date (yyyy-MM-dd格式)
	let fetchHisEventList = (params = {}) => vm.$u.get(`/pinanbao/family/getHisEventList`, params);
	// 查事件,familyId 必须传，date (yyyy-MM-dd格式)，与fetchHisEventList接口相同，只是把原来的指定日期修改为日期区间查询
	let fetchFamilyHisEventList = (params = {}) => vm.$u.get(`/pinanbao/family/listHisEvent`, params);
	// 信息一览
	let fetchTodayStatInfo = (params = {}) => vm.$u.get(`/pinanbao/family/getTodayStatInfo`, params);
	// 查询家庭详情
	let fetchFamilyDetail = (params = {}) => vm.$u.get(`/pinanbao/family/getFamilyDetail`, params);
	// 查询家庭成员列表
	let fetchFamilyMemberList = (params = {}) => vm.$u.get(`/pinanbao/family/queryFamilyMemberList`, params);
	// 添加家庭成员（接受邀请）,传参：familyId，code，encryptedData，iv
	let execAddFamilyMember = (params = {}) => vm.$u.post(`/pinanbao/family/addFamilyMember`, params);
	// 删除家庭成员
	let execRemoveFamilyMember = (params = {}) => vm.$u.post(`/pinanbao/family/removeFamilyMember?familyId=${params.familyId}&memberId=${params.memberId}`, params);
	// 家庭修改(名称，地址、门牌号、经纬度)
	let execUpdateFamily = (params = {}) => vm.$u.post(`/pinanbao/family/updateFamily`, params);
	
	// 联系人绑定到家庭
	let bindContact2Family = (params = {}) => vm.$u.get(`/pinanbao/contact/bindContact2Family`, params);
	// 被监护人绑定到家庭
	let bindOlder2Family = (params = {}) => vm.$u.get(`/pinanbao/older/bindOlder2Family`, params);
	// 联系人绑定到家庭
	let bindOlder2Family4Devive = (params = {}) => vm.$u.get(`/pinanbao/older/bindOlder2Family4Devive`, params);
	// 被监护人绑定到家庭
	let bindContact2Family4Device = (params = {}) => vm.$u.get(`/pinanbao/contact/bindContact2Family4Device`, params);
	
	// 查询家庭成员列表
	let fetchDevMemberList = (params = {}) => vm.$u.get(`/pinanbao/device/queryDevMemberList`, params);
	// 添加设备成员（接受邀请）,传参：devId，code，encryptedData，iv -- 六期新增
	let execAddDevMember = (params = {}) => vm.$u.post(`/pinanbao/device/addDevMember`, params);
	// 删除设备成员
	let execRemoveDevMember = (params = {}) => vm.$u.post(`/pinanbao/device/removeDevMember?devId=${params.devId}&memberId=${params.memberId}`, params);
	
	// 转移家庭设备
	let execTransferFamilyDev = (params = {}) => vm.$u.post(`/pinanbao/family/transferFamilyDev?fromFamilyId=${params.fromFamilyId}&toFamilyId=${params.toFamilyId}&devId=${params.devId}`, params);
	
	// 查询设备信息
	let fetchDeviceInfo = (params = {}) => vm.$u.get(`/pinanbao/devInfoSta/getDeviceInfo`, params);
	// 查询设备信息:设备表
	let fetchDeviceSimpleInfo = (params = {}) => vm.$u.get(`/pinanbao/device/my/getDeviceByCode`, params);
	// 获取实时监控URL
	let fetchRealTimeMonitorUrl = (params = {}) => vm.$u.get(`/pinanbao/devInfoSta/getRealTimeMonitorUrl`, params);
	// 获取实时监控URL,通过devCode
	let fetchRealTimeMonitorUrlByCode = (params = {}) => vm.$u.get(`/pinanbao/devInfoSta/getRealTimeMonitorUrlByCode`, params);
	// 人员实时动态
	let fetchPersonRealtimeStatusList = (params = {}) => vm.$u.get(`/pinanbao/devInfoSta/getPersonRealtimeStatusList`, params);
	// 今日心率呼吸动态列表（曲线）
	let fetchHeartBreathList = (params = {}) => vm.$u.get(`/pinanbao/devInfoSta/getHeartBreathList`, params);
	// 心率呼吸异常记录列表
	let fetchHeartBreathExceptionList = (params = {}) => vm.$u.get(`/pinanbao/devInfoSta/getHeartBreathExceptionList`, params);
	// 心率呼吸异常记录详情
	let fetchHeartBreathExceptionDetail = (params = {}) => vm.$u.get(`/pinanbao/devInfoSta/getHeartBreathExceptionDetail`, params);
	// 卧室人员在床离床实时动态
	let fetchBedroomPersonInbedRealtimeStatusList = (params = {}) => vm.$u.get(`/pinanbao/devInfoSta/getBedroomPersonInbedRealtimeStatusList`, params);
	// 卧室人员人员实时动态和在床实时动态
	let fetchBedroomPersonRealtimeStatusAndPersonInbedRealtimeStatusList = (params = {}) => vm.$u.get(`/pinanbao/devInfoSta/getBedroomPersonRealtimeStatusAndPersonInbedRealtimeStatusList`, params);
	// 卫生间停留时间统计
	let fetchToiletStayDurationList = (params = {}) => vm.$u.get(`/pinanbao/devInfoSta/getToiletStayDurationList`, params);
	// 事件列表
	let fetchDevEventList = (params = {}) => vm.$u.get(`/pinanbao/devInfoSta/queryEventList`, params);
	
	
	
	// 查看报告（dateStr格式为yyyy-MM-dd,devId 设备id，二个参数必选）
	let fetchReport = (params = {}) => vm.$u.get(`/pinanbao/report/getReport`, params);
	// 查看最近有报告的日期
	let fetchRecentlyDateHaveReport = (params = {}) => vm.$u.get(`/pinanbao/report/getRecentlyDateHaveReport`, params);
	
	
	// 查询反馈类型列表
	let fetchFeedbackTypeList = (params = {}) => vm.$u.get(`/pinanbao/my/queryFeedbackTypeList`, params);
	// 查询问题类型列表
	let fetchProblemTypeList = (params = {}) => vm.$u.get(`/pinanbao/my/queryProblemTypeList`, params);
	// 提交问题反馈
	let execCommitProblemFeedback = (params = {}) => vm.$u.post(`/pinanbao/my/commitProblemFeedback`, params);
	// 查询未读系统消息数量
	let fetchNoReadSysMsgCount = (params = {}) => vm.$u.get(`/pinanbao/my/queryNoReadSysMsgCount`, params);
	// 查询未读系统消息列表
	let fetchNoReadSysMsgList = (params = {}) => vm.$u.get(`/pinanbao/my/queryNoReadSysMsgList`, params);
	// 查询系统消息列表
	let fetchSysMsgList = (params = {}) => vm.$u.get(`/pinanbao/my/querySysMsgList`, params);
	// 标记消息已读
	let execSetMsgHadRead = (params = {}) => vm.$u.post(`/pinanbao/my/setMsgHadRead`, params);
	// 查询机构是否推广增值服务，0不推广，1推广
	let fetchOrgSpreadServer = (params = {}) => vm.$u.get(`/pinanbao/my/getOrgSpreadServer`, params);
	
	
	// 查询服务列表（增值服务）
	let fetchServerList = (params = {}) => vm.$u.get(`/pinanbao/my/queryServerList`, params);
	// 获取服务介绍内容
	let fetchMyServerPackageInfo = (params = {}) => vm.$u.get(`/pinanbao/my/getServerPackageInfo`, params);
	// 查询要开通服务的设备列表
	let fetchOpenServerDevList = (params = {}) => vm.$u.get(`/pinanbao/my/queryOpenServerDevList`, params);
	// 验证设备 --六期修改
	let fetchDevCheckDevCode = (params = {}) => vm.$u.post(`/installation/api/checkDevCode?devCode=${params.devCode}`, params);
	
	// 轮训是否生成了测试久滞工单(0：进行中，1：生成，2：失败 -- 六期新增
	let fetchQueryTestOrder = (params = {}) => vm.$u.get(`/installation/api/queryTestOrder?devCode=${params.devCode}`, params);
	// 开始测试 -- 六期新增
	let execStartTestDev = (params = {}) => vm.$u.get(`/installation/api/startTestDev?devCode=${params.devCode}`, params);
	
	
	// 修改设备模式，设备模式：0日常模式，1测试模式 -- 六期新增
	let execModifyDevModel = (params = {}) => vm.$u.post(`/pinanbao/device/my/modifyDevModel?devId=${params.devId}&devModel=${params.devModel}`, params);
	// 修改设备场景：1客厅，2卫生间，3卧室，4其他
	let execModifyDevSence = (params = {}) => vm.$u.post(`/pinanbao/device/my/modifyDevSence?devId=${params.devId}&devSence=${params.devSence}`, params);
	
	// 查询设备房间信息详情
	let getRoomDetailInfo = (params = {}) => vm.$u.get(`/pinanbao/device/getRoomDetailInfo?devCode=${params.devCode}`, params);
	// 保存房间详细信息
	let saveRoomDetailInfo = (params) => vm.$u.post(`/pinanbao/device/saveRoomDetailInfo`, params);
	
	// 保存床旁设备信息
	let saveBedSideDevice = (params) => vm.$u.post(`/pinanbao/device/saveBedSideDevice`, params);
	//保存离床预警信息
	let saveBedOutAlarm = (params) => vm.$u.post(`/pinanbao/device/saveBedOutAlarm`, params);
	
	//保存睡眠报告配置
	let saveSleepReportSetting = (params) => vm.$u.post(`/pinanbao/device/saveSleepReportSetting`, params);
	
	// 合家欢邀请 - 生成 scheme
	let fetchGenScheme = (params = {}) => vm.$u.post(`/pinanbao/wx/scheme/gen?path=${params.path}&query=${query}`, params);
	
	// aladding 查询我的紧联系人
	const listMyContact = (familyId) => vm.$u.get(`/pinanbao/family/contact/listMyContact`, {familyId});
	// aladding 查询家庭紧联系人
	const listFamilyContact = (familyId) => vm.$u.get(`/pinanbao/family/contact/listFamilyContact`, {familyId});
	// aladding 绑定家庭与联系人
	let bindFamilyContact = (params) => vm.$u.post(`/pinanbao/family/contact/bind`, params);
	// aladding 解除绑定家庭与联系人
	const unbindFamilyContact = (params) => vm.$u.post(`/pinanbao/family/contact/unbind`, params);
	
	// 联通固话--获取验证码
	const getVerificationCode = (params) => vm.$u.get(`/pinanbao/careMemberTelephone/getVerificationCode`, params);
	// 联通固话--获取固话号码列表
	const getLandlineNumbers = (params) => vm.$u.get(`/pinanbao/careMemberTelephone/getLandlineNumbers`, params);
	// 联通固话--绑定固话
	const bindTelephone = (params) => vm.$u.get(`/pinanbao/careMemberTelephone/bind`, params);
	// 联通固话--解绑固话
	const unbindTelephone = (params) => vm.$u.get(`/pinanbao/careMemberTelephone/unbind`, params);
	
	
	
	// aladding 处理返回的服务协议
	const populateAgreement = async (data) => {
		let serverIds = [], agreements = [], simpleAgreements = [], agreement = null, simpleAgreement = null;
		if (data.length === 0) {
			return { serverIds, simpleAgreement, agreement, agreeStep: 0 };
		}
		for (let item of data) {
			serverIds.push(item.id);
			if (item.simpleAgreement) {
				simpleAgreements.push(item.simpleAgreement);
			}
			if (item.agreement) {
				agreements.push(item.agreement);
			}
		}
		const agreementSeparator = `<div style="height:1rpx;background-color:#a0cfff ;margin:16px 0px;"></div>`;
		simpleAgreement = simpleAgreements.join(agreementSeparator);
		agreement = agreements.join(agreementSeparator);
		return {serverIds, simpleAgreement, agreement, agreeStep: 0}
	}
	// aladding 根据设备编号查询
	const listByDevCodeAgreement = async (devCode) => {
		try {
			const res = await vm.$u.get(`/pinanbao/member/agreement/listByDevCode?devCode=${devCode}`);
			return populateAgreement(res);
		} catch (err) {
			console.error(err);
			return populateAgreement([]);
		}
	}
	// aladding 根据设备产品模型查询
	const listByDevModelAgreement = async (devId) => {
		try {
			const res = await vm.$u.get(`/pinanbao/member/agreement/listByDevModel?devId=${devId}`);
			return populateAgreement(res);
		} catch (err) {
			console.error(err);
			return populateAgreement([]);
		}
	}
	// aladding 根据服务ID查询
	const listByServerIdAgreement = async (serverId) => {
		try {
			const res = await vm.$u.get(`/pinanbao/member/agreement/listByServerId?serverId=${serverId}`);
			return populateAgreement(res ? [res] : [])
		} catch (err) {
			console.error(err);
			return populateAgreement([]);
		}
	}
	// 根据服务类型查询
	const listByServerType = async (serverType) => {
		try {
			const res = await vm.$u.get(`/pinanbao/member/agreement/listByServerType?serverType=${serverType}`);
			return populateAgreement(res ? [res] : [])
		} catch (err) {
			console.error(err);
			return populateAgreement([]);
		}
	}
	// aladding 查询服务是否需要激活
	const listNotAcceptByFamilyIdAgreement = async (familyId) => {
		try {
			const res = await vm.$u.get(`/pinanbao/member/agreement/listNotAcceptByFamilyId?familyId=${familyId}`);
			return populateAgreement(res);
		} catch (err) {
			console.error(err);
			return populateAgreement([]);
		}
	}
	const listNotAgreeAgreement = async () => {
		try {
			const res = await vm.$u.get(`/pinanbao/member/agreement/listNotAgreeAgreement`);
			return populateAgreement(res);
		} catch (err) {
			console.error(err);
			return populateAgreement([]);
		}
	}
	
	// aladding 根据服务id列表查询未接受的服务
	/*
	const listNotAcceptByServerIdsAgreement = async (params) => {
		try {
			const res = await vm.$u.get(`/pinanbao/member/agreement/listNotAcceptByServerIds?serverIds=${params.serverIds}`);
			return populateAgreement(res);
		} catch (err) {
			console.error(err);
			return populateAgreement([]);
		}
	}
	*/
    // 接收当前用户所有设备的服务
    const agreeByMember = (params = {}) => vm.$u.get(`/pinanbao/member/agreement/agreeByMember`);
    // 接收家庭下所有设备的服务
    const agreeByFamilyIdAgreement = (params = {}) => vm.$u.get(`/pinanbao/member/agreement/agreeByFamilyId`, params);
	// aladding 接受服务协议
	const agreeByServerAgreement = (params = {}, devId) => vm.$u.post(`/pinanbao/member/agreement/agreeByServer${devId ? "?devId=" + devId : ""}`, params);
	// aladding 接受设备服务协议
	const agreeByDevCodeAgreement = (params = {}) => vm.$u.post(`/pinanbao/member/agreement/agreeByDevCode?devCode=${params.devCode}`, params);
	// aladding 接受设备服务协议
	const agreeByDevIdAgreement = (params = {}) => vm.$u.post(`/pinanbao/member/agreement/agreeByDevId?devId=${params.devId}`, params);

	// aladding 获取服务信息及价格
	const getServerPrices = (params = {}) => vm.$u.get(`/pinanbao/member-server/detailPrices?serverId=${params.serverId}`);
	// aladding 获取无忧服务详细信息
	const getMemberServerDetail = (params = {}) => vm.$u.get(`/pinanbao/member-server/getServerDetail`, params);
	
	// aladding 查询用户订单数量
	const countUnpaidOrder = (params = {}) => vm.$u.get(`/pinanbao/server-order/countUnpaid?serverId=${params.serverId}`);
	
	// aladding 查询服务名
	const getServerByType = (serverType = "") => vm.$u.get(`/pinanbao/worryfree/getServerByType?serverType=${serverType}`);
	// aladding 备案会员
	const addAccountByOrderWorryfree = (params = {}) => vm.$u.post(`/pinanbao/worryfree/addAccountByOrder?orderId=${params.orderId}`, params);

	// 已不使用该接口
	// aladding 查询我的未在保护中的被监护人
	const listMyNotGuardOlderWorryfree = (params) => vm.$u.get(`/pinanbao/worryfree/listMyNotGuardOlder`, params);
	// 已不使用该接口
	// aladding 查询设备是否在保护中
	const isGuardDeviceWorryfree = async (params = {}) =>"true" === await vm.$u.get(`/pinanbao/worryfree/isGuardDevice`, params);
	// 已不使用该接口
	// aladding 查询被监护人是否在保护中
	const isGuardOlderWorryfree = async (params = {}) => "true" === await vm.$u.get(`/pinanbao/worryfree/isGuardOlder`, params);

	// aladding 查询我的有效的无忧服务账号
	const pageMyServerRecordWorryfree = (params = {}) => vm.$u.get(`/pinanbao/worryfree/pageMyServerRecord`, params);
	// aladding 查询我的有效的无忧服务账号
	const detailServerRecordWorryfree = (params = {}) => vm.$u.get(`/pinanbao/worryfree/detailServerRecord/${params.id}`, params);
	// aladding 查询我的有效的无忧服务订单中被保护人信息
	const listMyOrderItemOlderWorryfree = (params = {}) => vm.$u.get(`/pinanbao/worryfree/listMyOrderItemOlder`, params);
	// aladding 查询被监护人的服务记录数
	const countOlderServerRecordWorryfree = (params = {}) => vm.$u.get(`/pinanbao/worryfree/countOlderServerRecord`, params);

	// aladding 查询服务当前列表
	const listMemberServerCurrent = (params = {}) => vm.$u.get(`/pinanbao/worryfree/listMyCurrent`, params);
	// aladding 查询服务历史列表
	const listMemberServerHistory = (params = {}) => vm.$u.get(`/pinanbao/worryfree/listMyHistory`, params);
	// aladding 查询服务当前数量
	const countMemberServerCurrent = (params = {}) => vm.$u.get(`/pinanbao/worryfree/countMyCurrent`, params);
	// aladding 查询服务历史数量
	const countMemberServerHistory = (params = {}) => vm.$u.get(`/pinanbao/worryfree/countMyHistory`, params);
	// aladding 获取无忧服务详细信息
	const listServerRecordByMonth = (params = {}) => vm.$u.get(`/pinanbao/worryfree/listServerRecord/month`, params);
	// aladding 获取无忧服务详细信息
	const listServerRecordByDate = (params = {}) => vm.$u.get(`/pinanbao/worryfree/listServerRecord/date`, params);
	
	// aladding 批量查询数据字典
	const listDcitMapByTypes = (params = {}) => vm.$u.get(`/pinanbao/dict/listMapByTypes?typeCodes=${params.typeCodes}`);
	// aladding 批量查询数据字典
	const listDcitByType = (params = {}) => vm.$u.get(`/pinanbao/dict/listByType?typeCode=${params.typeCode}`);
	// aladding 查询我的所有设备未读消息数量
	const countDevMsgMyUnread = (params = {}) => vm.$u.get(`/pinanbao/device-msg/countMyUnread`, params);
	// aladding 分布查询我的设备消息
	const pageDevMsgMy = (params = {}) => vm.$u.get(`/pinanbao/device-msg/pageMy`, params);
	// aladding 标记为已读
	const updateDevMsgRead = (params = {}) => vm.$u.get(`/pinanbao/device-msg/updateReadById`, params);
	// aladding 查看消息明细
	const getDetailDevMsg = (params = {}) => vm.$u.get(`/pinanbao/device-msg/detail/${params.id}`);
	// 将各个定义的接口名称，统一放进对象挂载到vm.$u.api(因为vm就是this，也即this.$u.api)下
	vm.$u.api = {
		getSearch, getInfo, login, fetchHouseEventHasDoing, fetchHouseEventInfo,
		fetchHouseDeviceList, fetchMemberDetailInfo, execHouseSosBak, fetchRealTimeMonitor, 
		
		execDecodePhoneNumber,
		fetchRegVCode,
		execRegisterPhone,
		execChangePwd,
		execCheckVCode,
		execCheckToken,
		
		fetchDevInstallationInfo,
		fetchMyDeviceInfo,
		
		fetchMyDeviceList,
		updateFirmwareVersion,
		checkUpgradeableVersion,
		countByFamilyIdAndDevScene,
		execCheckDevCode,
		execBindUser,
		
		fetchOlderMyList,
		queryOlderByFamilyId,
		execOlderMyAdd,
		execOlderMyUpdate,
		olderHasOrder,
		fetchMyOlderListByDeviceId,
		fetchMyOlderInfo,
		execOlderMyDelete,

		addAccountByOrderWorryfree,
		listMyNotGuardOlderWorryfree,
		isGuardDeviceWorryfree,
		isGuardOlderWorryfree,
		pageMyServerRecordWorryfree,
		detailServerRecordWorryfree,
		listMyOrderItemOlderWorryfree,
		countOlderServerRecordWorryfree,
		
		fetchProductPackageDetail,
		
		fetchMyContactListByDeviceId,
		fetchContactMyList,
		queryContactByFamilyId,
		fetchMContactInfo,
		execDeviceBindHouseContact,
		execDeviceunBindHouseContact,
		execContactMyAdd,
		execContactMyUpdate,
		execContactMyAddOrUpdate,
		fetchMyContact,
		
		execUpdateMyDevice,
		updateDeviceSpotType,
		execUpdateDeviceName,
		fetchMyDevStationInfo,
		execUnBindMyDevice,
		
		fetchEventList,
		fetchEventInfo,
		execContactMyDelete,
		
		execUpdateMyInfo,
		selectAuthorizeList,
		getUnauthorizedCount,
		authorizeThirdParty,
		getVoIPPwd,
		
		fetchInbedTimeChartList,
		fetchToiletInoutChartList,
		
		execWxQrcodeGen,
		execWxQrcodeGenFamily,
		
		fetchZengzhiServerList,
		listAddServerDevice,

		expiresWithinOneMonth,
		getCardByPassword,
		getCardById,
		getCardDevicesByCardId,
		cardScan,
		insertCardDevices,
		takeDeliveryGoods,
		selectDeliveryByPage,
		queryCardCount,
		getCardsByMemberId,
		createMemberServerByCard,

		fetchOrderList,
		fetchOrderDetail,
		execSaveOrder,
		addProductOrder,
		execPrepay,
		execH5Prepay,
		fetchPayStatus,
		fetchServerPackageInfo,
		
		execCancelOrder,
		
		
		getDevCodeByMac,
		fetchCommitRoomInfoResult,
		execSaveDevInstallation,
		// fetchDevInstallationInfo,
		execSaveRoomSensor,
		fetchRoomSensorInfo,
		fetchRoomInfo,
		execSaveRoom,
		fetchConfStatusInfo,
		execCommitRoomInfo,
		fetchRoomGateInfo,
		execSaveRoomGate,
		execDelRoomGate,
		fetchListRoomGateInfo,
		fetchRoomRegionInfo,
		execDelRoomRegion,
		execSaveRoomRegion,
		fetchListRoomRegionInfo,
		
		fetchAddressList,
		saveAddress,
		deleteAddress,
		getAddressById,
		getDefaultAddress,
		
		fetchFamilyList,
		execCreateFamily,
		deleteFamily,
		fetchFamilyDevList,
		fetchTodayEventList,
		fetchHisEventList,
		fetchFamilyHisEventList,
		fetchTodayStatInfo,
		fetchFamilyDetail,
		fetchFamilyMemberList,
		execAddFamilyMember,
		execRemoveFamilyMember,
		execUpdateFamily,
		
		bindOlder2Family,
		bindContact2Family,
		bindOlder2Family4Devive,
		bindContact2Family4Device,
		
		fetchDevMemberList,
		execAddDevMember,
		execRemoveDevMember,
		execTransferFamilyDev,
		
		fetchDeviceInfo,
		fetchDeviceSimpleInfo,
		fetchRealTimeMonitorUrl,
		fetchRealTimeMonitorUrlByCode,
		fetchPersonRealtimeStatusList,
		fetchHeartBreathList,
		fetchHeartBreathExceptionList,
		fetchHeartBreathExceptionDetail,
		fetchBedroomPersonInbedRealtimeStatusList,
		fetchBedroomPersonRealtimeStatusAndPersonInbedRealtimeStatusList,
		fetchToiletStayDurationList,
		fetchDevEventList,
		
		fetchReport,
		fetchRecentlyDateHaveReport,
		
		fetchFeedbackTypeList,
		fetchProblemTypeList,
		fetchProblemTypeList,
		execCommitProblemFeedback,
		fetchNoReadSysMsgCount,
		fetchNoReadSysMsgList,
		fetchSysMsgList,
		execSetMsgHadRead,
		fetchOrgSpreadServer,
		
		fetchServerList,
		fetchMyServerPackageInfo,
		fetchOpenServerDevList,
		
		fetchDevCheckDevCode,
		
		fetchQueryTestOrder,
		execStartTestDev,
		
		execModifyDevModel,
		execModifyDevSence,
		getRoomDetailInfo,
		saveRoomDetailInfo,
		saveBedSideDevice,
		saveBedOutAlarm,
		saveSleepReportSetting,
		
		fetchGenScheme,
		
		listMyContact,
		listFamilyContact,
		bindFamilyContact,
		unbindFamilyContact,
		
		listByDevCodeAgreement,
		listByDevModelAgreement,
		listNotAcceptByFamilyIdAgreement,
		listNotAgreeAgreement,
		listByServerIdAgreement,
		listByServerType,
		//listNotAcceptByServerIdsAgreement,
		
		agreeByMember,
		agreeByFamilyIdAgreement,
		agreeByServerAgreement,
		agreeByDevCodeAgreement,
		agreeByDevIdAgreement,
		getServerByType,
		getServerPrices,
		countUnpaidOrder,
		listDcitMapByTypes,
		listDcitByType,
		createMemberServerOrder,
		editServerOrder,
		countMemberServerCurrent,
		countMemberServerHistory,
		listMemberServerCurrent,
		listMemberServerHistory,
		getMemberServerDetail,
		listServerRecordByMonth,
		listServerRecordByDate,
		countDevMsgMyUnread,
		pageDevMsgMy,
		updateDevMsgRead,
		getDetailDevMsg,
		fetchDeviceOwner,
		
		getVerificationCode,
		getLandlineNumbers,
		bindTelephone,
		unbindTelephone,
	};
}

export default {
	install
}