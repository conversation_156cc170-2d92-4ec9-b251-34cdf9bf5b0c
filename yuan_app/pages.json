{
	"easycom": {
		"^u-(.*)": "@/uview-ui/components/u-$1/u-$1.vue"
	},
	// "condition": { //模式配置，仅开发期间生效
	// 	"current": 0, //当前激活的模式(list 的索引项)
	// 	"list": [{
	// 		"name": "test", //模式名称
	// 		"path": "pages/componentsC/test/index", //启动页面，必选
	// 		"query": "uuid=c4bba940-f69e-11ea-a419-6bafda9d095e&__id__=1" //启动参数，在页面的onLoad函数里面得到
	// 	}]
	// },
	"pages": [
		// #ifdef MP-WEIXIN
		{
			"path": "pages/index/index",
			"style": {
				"backgroundColor": "#f7f7f7",
				"navigationBarBackgroundColor": "#f7f7f7",
				"navigationBarTitleText": "与安宝",
				"navigationBarTextStyle": "black",
				"enablePullDownRefresh": true,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/cmcc/index",
			"style": {
				"backgroundColor": "#4166F5",
				"navigationBarBackgroundColor": "#4166F5",
				"navigationBarTextStyle": "white",
				"navigationBarTitleText": "与安宝",
				"enablePullDownRefresh": false
			}
		},
		// #endif
		// #ifndef MP-WEIXIN
		{
			"path": "pages/index/index",
			"style": {
				"backgroundColor": "#f7f7f7",
				"navigationBarBackgroundColor": "#f7f7f7",
				"navigationBarTitleText": "与安宝",
				"navigationBarTextStyle": "black",
				"enablePullDownRefresh": true,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/cmcc/index",
			"style": {
				"backgroundColor": "#4166F5",
				"navigationBarBackgroundColor": "#4166F5",
				"navigationBarTextStyle": "white",
				"navigationBarTitleText": "与安宝",
				"enablePullDownRefresh": false
			}
		},
		// #endif
		{
			"path": "pages/invitation/index",
			"style": {
				"navigationBarTitleText": "",
				"backgroundColor": "#f7f7f7",
				"navigationBarBackgroundColor": "#f7f7f7",
				"navigationBarTextStyle": "black",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/invitation/device",
			"style": {
				"navigationBarTitleText": "",
				"backgroundColor": "#f7f7f7",
				"navigationBarBackgroundColor": "#f7f7f7",
				"navigationBarTextStyle": "black",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/auth/login/index",
			"style": {
				"navigationBarBackgroundColor": "#4166F5",
				"navigationBarTextStyle": "white",
				"navigationBarTitleText": "登录",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/auth/app-auth/index",
			"style": {
				"navigationBarBackgroundColor": "#667eea",
				"navigationBarTextStyle": "white",
				"navigationBarTitleText": "验证中",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		
		// {
		//     "path" : "index",
		//     "style" :                                                                                    
		//     {
		// 		"backgroundColor":"#4166F5",
		// 		"navigationBarBackgroundColor":"#4166F5",
		//               "navigationBarTitleText": "我的家",
		// 		"navigationBarTextStyle": "white",
		//               "enablePullDownRefresh": false
		//     }
		// },
		{
			"path": "pages/auth/forget/index",
			"style": {
				"navigationBarBackgroundColor": "#fff",
				"navigationBarTitleText": "找回密码",
				"navigationBarTextStyle": "black",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/auth/reg/index",
			"style": {
				"navigationBarBackgroundColor": "#4166F5",
				"navigationBarTextStyle": "white",
				"navigationBarTitleText": "登录",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/report/index",
			"style": {
				"backgroundColor": "#f7f7f7",
				"navigationBarBackgroundColor": "#f7f7f7",
				"navigationBarTitleText": "日报",
				"navigationBarTextStyle": "black",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/my/index",
			"style": {
				"navigationBarBackgroundColor": "#f7f7f7",
				"navigationBarTextStyle": "black",
				"navigationBarTitleText": "我的",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/auth/forget/newpwd",
			"style": {
				"navigationBarBackgroundColor": "#fff",
				"navigationBarTextStyle": "black",
				"navigationBarTitleText": "设置密码",
				"enablePullDownRefresh": false
			}
		}, {
			"path": "pages/auth/forget/reset-completed",
			"style": {
				"navigationBarBackgroundColor": "#fff",
				"navigationBarTextStyle": "black",
				"navigationBarTitleText": "重置完成",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/supplement/index",
			"style": {
				"backgroundColor": "#f7f7f7",
				"navigationBarBackgroundColor": "#f7f7f7",
				"navigationBarTitleText": "保存紧急联系人",
				"navigationBarTextStyle": "black",
				"enablePullDownRefresh": false
			}
		}, {
			"path": "pages/auth/modifyPassword/index",
			"style": {
				"backgroundColor": "#fff",
				"navigationBarBackgroundColor": "#fff",
				"navigationBarTitleText": "修改密码",
				"navigationBarTextStyle": "black",
				"enablePullDownRefresh": false
			}
		}, {
			"path": "pages/my/setting",
			"style": {
				"backgroundColor": "#fff",
				"navigationBarBackgroundColor": "#fff",
				"navigationBarTitleText": "我的",
				"navigationBarTextStyle": "black",
				"enablePullDownRefresh": false
			}
		}, {
			"path": "pages/auth/modifyPassword/step1",
			"style": {
				"backgroundColor": "#fff",
				"navigationBarBackgroundColor": "#fff",
				"navigationBarTitleText": "修改密码",
				"navigationBarTextStyle": "black",
				"enablePullDownRefresh": false
			}
		}, {
			"path": "pages/auth/modifyPassword/step2",
			"style": {
				"backgroundColor": "#fff",
				"navigationBarBackgroundColor": "#fff",
				"navigationBarTitleText": "提交结果",
				"navigationBarTextStyle": "black",
				"enablePullDownRefresh": false
			}
		}, {
			"path": "pages/my/userCenter",
			"style": {
				"backgroundColor": "#fff",
				"navigationBarBackgroundColor": "#fff",
				"navigationBarTitleText": "用户中心",
				"navigationBarTextStyle": "black",
				"enablePullDownRefresh": false
			}
		}, {
			"path": "pages/webview/index",
			"style": {
				"navigationBarTitleText": "实时查看",
				"enablePullDownRefresh": false
			}
		},

		{
			"path": "pages/address/index",
			"style": {
				"backgroundColor": "#fff",
				"navigationBarBackgroundColor": "#fff",
				"navigationBarTitleText": "安装地址",
				"navigationBarTextStyle": "black",
				"enablePullDownRefresh": false
			}
		}, {
			"path": "pages/report/components/bedroom",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}
		}
	],
	"subPackages": [{
		"root": "pagesDevice",
		"pages": [{
			 "path": "responseSetting",
				"style": {
					"backgroundColor": "#fff",
					"navigationBarBackgroundColor": "#fff",
					"navigationBarTitleText": "设备响应时间",
					"navigationBarTextStyle": "black",
					"enablePullDownRefresh": false
				}
			}, {
				"path": "stationInfo",
				"style": {
					"backgroundColor": "#fff",
					"navigationBarBackgroundColor": "#fff",
					"navigationBarTitleText": "服务站详情",
					"navigationBarTextStyle": "black",
					"enablePullDownRefresh": false
				}
			}, {
				"path": "sceneConfig",
				"style": {
					"backgroundColor": "#fff",
					"navigationBarBackgroundColor": "#fff",
					"navigationBarTextStyle": "black",
					"navigationBarTitleText": "标签选择",
					"enablePullDownRefresh": false
				}
			},
			{
				"path": "add/index",
				"style": {
					"backgroundColor": "#fff",
					"navigationBarBackgroundColor": "#fff",
					"navigationBarTitleText": "扫描验证",
					"navigationBarTextStyle": "black",
					"enablePullDownRefresh": false
				}
			},
			{
				"path": "add/result",
				"style": {
					"backgroundColor": "#fff",
					"navigationBarBackgroundColor": "#fff",
					"navigationBarTitleText": "扫描验证",
					"navigationBarTextStyle": "black",
					"enablePullDownRefresh": false
				}
			},
			{
				"path": "call/call",
				"style": {
					"navigationStyle": "custom",
					"navigationBarTitleText": "呼叫设备"
				}
			},
			{
				"path": "call/webview-call",
				"style": {
					"navigationBarTitleText": "打电话",
					"enablePullDownRefresh": false
				}
			},
			{
				"path": "config/sensor",
				"style": {
					"backgroundColor": "#fff",
					"navigationBarBackgroundColor": "#fff",
					"navigationBarTitleText": "设备方位",
					"navigationBarTextStyle": "black",
					"enablePullDownRefresh": false
				}
			},
			{
				"path": "config/room-config",
				"style": {
					"backgroundColor": "#f7f7f7",
					"navigationBarBackgroundColor": "#fff",
					"navigationBarTitleText": "房间信息配置",
					"navigationBarTextStyle": "black",
					"enablePullDownRefresh": false
				}
			},
			{
				"path": "config/step",
				"style": {
					"backgroundColor": "#fff",
					"navigationBarBackgroundColor": "#fff",
					"navigationBarTitleText": "安装步骤",
					"navigationBarTextStyle": "black",
					"enablePullDownRefresh": false
				}
			},
			{
				"path": "config/config-result",
				"style": {
					"backgroundColor": "#fff",
					"navigationBarBackgroundColor": "#fff",
					"navigationBarTitleText": "配置结果",
					"navigationBarTextStyle": "black",
					"enablePullDownRefresh": false
				}
			},
			{
				"path": "config/tag-config",
				"style": {
					"backgroundColor": "#fff",
					"navigationBarBackgroundColor": "#fff",
					"navigationBarTitleText": "设备场景",
					"navigationBarTextStyle": "black",
					"enablePullDownRefresh": false
				}
			},
			{
				"path": "config/vali-wait",
				"style": {
					"backgroundColor": "#fff",
					"navigationBarBackgroundColor": "#fff",
					"navigationBarTitleText": "提交结果",
					"navigationBarTextStyle": "black",
					"enablePullDownRefresh": false
				}
			},
			{
				"path": "config/area-edit",
				"style": {
					"backgroundColor": "#fff",
					"navigationBarBackgroundColor": "#fff",
					"navigationBarTitleText": "区域",
					"navigationBarTextStyle": "black",
					"enablePullDownRefresh": false
				}
			},
			{
				"path": "config/area",
				"style": {
					"backgroundColor": "#fff",
					"navigationBarBackgroundColor": "#fff",
					"navigationBarTitleText": "区域",
					"navigationBarTextStyle": "black",
					"enablePullDownRefresh": false
				}
			},
			{
				"path": "config/door-edit",
				"style": {
					"backgroundColor": "#fff",
					"navigationBarBackgroundColor": "#fff",
					"navigationBarTitleText": "门方位",
					"navigationBarTextStyle": "black",
					"enablePullDownRefresh": false
				}
			},
			{
				"path": "config/door",
				"style": {
					"backgroundColor": "#fff",
					"navigationBarBackgroundColor": "#fff",
					"navigationBarTitleText": "门方位",
					"navigationBarTextStyle": "black",
					"enablePullDownRefresh": false
				}
			},
			{
				"path": "config/room-size-pre",
				"style": {
					"backgroundColor": "#fff",
					"navigationBarBackgroundColor": "#fff",
					"navigationBarTitleText": "房间信息配置",
					"navigationBarTextStyle": "black",
					"enablePullDownRefresh": false
				}
			},
			{
				"path": "config/room-size",
				"style": {
					"backgroundColor": "#fff",
					"navigationBarBackgroundColor": "#fff",
					"navigationBarTitleText": "房间尺寸",
					"navigationBarTextStyle": "black",
					"enablePullDownRefresh": false
				}
			},
			{
				"path": "config/room",
				"style": {
					"backgroundColor": "#fff",
					"navigationBarBackgroundColor": "#fff",
					"navigationBarTitleText": "房间配置",
					"navigationBarTextStyle": "black",
					"enablePullDownRefresh": false,
					"navigationStyle":"custom"
				}
			},
			{
				"path": "config/room-web",
				"style": {
					"navigationBarTitleText": "房间配置",
					"enablePullDownRefresh": false
				}
			},
			// {
			// 	"path": "config/demo",
			// 	"style": {
			// 		"backgroundColor": "#fff",
			// 		"navigationBarBackgroundColor": "#fff",
			// 		"navigationBarTitleText": "demo",
			// 		"navigationBarTextStyle": "black",
			// 		"enablePullDownRefresh": false
			// 	}
			// },
			{
				"path": "config/wifi-notice",
				"style": {
					"backgroundColor": "#fff",
					"navigationBarBackgroundColor": "#fff",
					"navigationBarTitleText": "配网提醒",
					"navigationBarTextStyle": "black",
					"enablePullDownRefresh": false
				}
			},
			{
				"path" : "bedOutAlarmSetting",
				"style" : 
				{
					"backgroundColor": "#fff",
					"navigationBarBackgroundColor": "#fff",
					"navigationBarTitleText" : "预警配置",
					"navigationBarTextStyle": "black",
					"enablePullDownRefresh": false
				}
			},
			{
				"path" : "sleepReportSetting",
				"style" : 
				{
					"backgroundColor": "#fff",
					"navigationBarBackgroundColor": "#fff",
					"navigationBarTitleText" : "睡眠统计配置",
					"navigationBarTextStyle": "black",
					"enablePullDownRefresh": false
				}
			}
		]
	}, {
		"root": "pagesHome",
		"pages": [{
			"path": "parlour/index",
			"style": {
				"navigationBarTitleText": "客厅",
				"backgroundColor": "#f7f7f7",
				"navigationBarBackgroundColor": "#fff",
				"navigationBarTextStyle": "black",
				"enablePullDownRefresh": true
			}
		}, {
			"path": "toilet/index",
			"style": {
				"navigationBarTitleText": "卫生间",
				"backgroundColor": "#f7f7f7",
				"navigationBarBackgroundColor": "#fff",
				"navigationBarTextStyle": "black",
				"enablePullDownRefresh": true
			}
		}, {
			"path": "bedroom/index",
			"style": {
				"navigationBarTitleText": "卧室",
				"backgroundColor": "#f7f7f7",
				"navigationBarBackgroundColor": "#fff",
				"navigationBarTextStyle": "black",
				"enablePullDownRefresh": true
			}
		}
		// {
		// 	"path" : "ssb/index",
		// 	"style" : 
		// 	{
		// 		"navigationBarTitleText" : "随身宝",
		// 		"backgroundColor": "#f7f7f7",
		// 		"navigationBarBackgroundColor": "#fff",
		// 		"navigationBarTextStyle": "black",
		// 		"enablePullDownRefresh": true
		// 	}
		// },
		// {
		// 	"path" : "ssb/map",
		// 	"style" : 
		// 	{
		// 		"navigationBarTitleText" : "随身宝",
		// 		"backgroundColor": "#f7f7f7",
		// 		"navigationBarBackgroundColor": "#fff",
		// 		"navigationBarTextStyle": "black",
		// 		"enablePullDownRefresh": true
		// 	}
		// },
		]
	}, {
		"root": "pagesMy",
		"pages": [{
			"path": "event/index",
			"style": {
				"navigationBarTitleText": "历史事件列表",
				"backgroundColor": "#f7f7f7",
				"navigationBarBackgroundColor": "#fff",
				"navigationBarTextStyle": "black",
				"enablePullDownRefresh": false
			}
		}, {
			"path": "device/guardian/index",
			"style": {
				"backgroundColor": "#f7f7f7",
				"navigationBarBackgroundColor": "#fff",
				"navigationBarTitleText": "被监护人",
				"navigationBarTextStyle": "black",
				"enablePullDownRefresh": false
			}
		}, {
			"path": "device/guardian/selector",
			"style": {
				"backgroundColor": "#f7f7f7",
				"navigationBarBackgroundColor": "#fff",
				"navigationBarTitleText": "被监护人",
				"navigationBarTextStyle": "black",
				"enablePullDownRefresh": false
			}
		}, {
			"path": "device/guardian/edit",
			"style": {
				"backgroundColor": "#f7f7f7",
				"navigationBarBackgroundColor": "#fff",
				"navigationBarTextStyle": "black",
				"navigationBarTitleText": "新增被监护人",
				"enablePullDownRefresh": false
			}
		}, {
			"path": "device/connector/index",
			"style": {
				"backgroundColor": "#f7f7f7",
				"navigationBarBackgroundColor": "#fff",
				"navigationBarTitleText": "紧急联系人",
				"navigationBarTextStyle": "black",
				"enablePullDownRefresh": false
			}
		}, {
			"path": "device/connector/selector",
			"style": {
				"backgroundColor": "#f7f7f7",
				"navigationBarBackgroundColor": "#fff",
				"navigationBarTitleText": "紧急联系人",
				"navigationBarTextStyle": "black",
				"enablePullDownRefresh": false
			}
		}, {
			"path": "device/connector/edit",
			"style": {
				"backgroundColor": "#f7f7f7",
				"navigationBarBackgroundColor": "#fff",
				"navigationBarTextStyle": "black",
				"navigationBarTitleText": "紧急联系人",
				"enablePullDownRefresh": false,
				"app-plus": {
					"softinputMode": "adjustResize"
				}
			}
		}, {
			"path": "order/index",
			"style": {
				"backgroundColor": "#f7f7f7",
				"navigationBarBackgroundColor": "#fff",
				"navigationBarTitleText": "订单管理",
				"navigationBarTextStyle": "black",
				"enablePullDownRefresh": false
			}
		}, {
			"path": "order/info",
			"style": {
				"backgroundColor": "#f7f7f7",
				"navigationBarBackgroundColor": "#fff",
				"navigationBarTitleText": "订单详情",
				"navigationBarTextStyle": "black",
				"enablePullDownRefresh": false
			}
		}, {
			"path": "article/index",
			"style": {
				"backgroundColor": "#f7f7f7",
				"navigationBarBackgroundColor": "#fff",
				"navigationBarTitleText": "",
				"navigationBarTextStyle": "black",
				"enablePullDownRefresh": false
			}
		}, {
			"path": "feedback/index",
			"style": {
				"backgroundColor": "#f7f7f7",
				"navigationBarBackgroundColor": "#fff",
				"navigationBarTitleText": "问题反馈",
				"navigationBarTextStyle": "black",
				"enablePullDownRefresh": false
			}
		}, {
			"path": "feedback/submit",
			"style": {
				"backgroundColor": "#fff",
				"navigationBarBackgroundColor": "#fff",
				"navigationBarTitleText": "问题",
				"navigationBarTextStyle": "black",
				"enablePullDownRefresh": false
			}
		}, {
			"path": "message/index",
			"style": {
				"backgroundColor": "#f7f7f7",
				"navigationBarBackgroundColor": "#fff",
				"navigationBarTitleText": "系统消息",
				"navigationBarTextStyle": "black",
				"enablePullDownRefresh": false
			}
		}, {
			"path": "help/index",
			"style": {
				"backgroundColor": "#fff",
				"navigationBarBackgroundColor": "#fff",
				"navigationBarTitleText": "帮助中心",
				"navigationBarTextStyle": "black",
				"enablePullDownRefresh": false
			}
		}, {
			"path": "serverType/index",
			"style": {
				"backgroundColor": "#f7f7f7",
				"navigationBarBackgroundColor": "#fff",
				"navigationBarTitleText": "服务",
				"navigationBarTextStyle": "black",
				"enablePullDownRefresh": false
			}
		}, {
			"path": "services/index",
			"style": {
				"backgroundColor": "#f7f7f7",
				"navigationBarBackgroundColor": "#fff",
				"navigationBarTitleText": "增值服务",
				"navigationBarTextStyle": "black",
				"enablePullDownRefresh": false
			}
		}, {
			"path": "address/index",
			"style": {
				"backgroundColor": "#f7f7f7",
				"navigationBarBackgroundColor": "#fff",
				"navigationBarTitleText": "地址管理",
				"navigationBarTextStyle": "black",
				"enablePullDownRefresh": false
			}
		}, {
			"path": "address/edit",
			"style": {
				"backgroundColor": "#f7f7f7",
				"navigationBarBackgroundColor": "#fff",
				"navigationBarTitleText": " 收货地址",
				"navigationBarTextStyle": "black",
				"enablePullDownRefresh": false
			}
		}, {
			"path": "authorize/index",
			"style": {
				"backgroundColor": "#f7f7f7",
				"navigationBarBackgroundColor": "#fff",
				"navigationBarTitleText": "授权管理",
				"navigationBarTextStyle": "black",
				"enablePullDownRefresh": false
			}
		},
		{
			"path" : "telephone/telephone",
			"style" : 
			{
				"navigationBarTitleText" : "智能固话绑定/解绑",
				"enablePullDownRefresh": false
			}
		},
		{
			"path" : "telephone/captcha",
			"style" : 
			{
				"navigationBarTitleText" : "验证码验证",
				"enablePullDownRefresh": false
			}
		},
		{
			"path" : "telephone/list",
			"style" : 
			{
				"navigationBarTitleText" : "固话列表",
				"enablePullDownRefresh": true
			}
		}]
	}, {
		"root": "pagesFamily",
		"pages": [{
				"path": "familys/index",
				"style": {
					"navigationBarTitleText": "家庭管理",
					"backgroundColor": "#f7f7f7",
					"navigationBarBackgroundColor": "#fff",
					"navigationBarTextStyle": "black",
					"enablePullDownRefresh": false
				}
			}, {
				"path": "familys/add",
				"style": {
					"navigationBarTitleText": "创建新增",
					"backgroundColor": "#fff",
					"navigationBarBackgroundColor": "#fff",
					"navigationBarTextStyle": "black",
					"enablePullDownRefresh": false
				}
			}, {
				"path": "detail/index",
				"style": {
					"navigationBarTitleText": "家庭详情",
					"backgroundColor": "#f7f7f7",
					"navigationBarBackgroundColor": "#fff",
					"navigationBarTextStyle": "black",
					"enablePullDownRefresh": false
				}
			}, {
				"path": "member/index",
				"style": {
					"navigationBarTitleText": "家庭成员",
					"backgroundColor": "#f7f7f7",
					"navigationBarBackgroundColor": "#fff",
					"navigationBarTextStyle": "black",
					"enablePullDownRefresh": false
				}
			}, {
				"path": "member/remove",
				"style": {
					"navigationBarTitleText": "成员列表",
					"backgroundColor": "#fff",
					"navigationBarBackgroundColor": "#fff",
					"navigationBarTextStyle": "black",
					"enablePullDownRefresh": false
				}
			}, {
				"path": "devMember/index",
				"style": {
					"navigationBarTitleText": "设备成员",
					"backgroundColor": "#f7f7f7",
					"navigationBarBackgroundColor": "#fff",
					"navigationBarTextStyle": "black",
					"enablePullDownRefresh": false
				}
			}, {
				"path": "devMember/remove",
				"style": {
					"navigationBarTitleText": "成员列表",
					"backgroundColor": "#fff",
					"navigationBarBackgroundColor": "#fff",
					"navigationBarTextStyle": "black",
					"enablePullDownRefresh": false
				}
			}, {
				"path": "device/index",
				"style": {
					"navigationBarTitleText": "设备列表",
					"backgroundColor": "#f7f7f7",
					"navigationBarBackgroundColor": "#fff",
					"navigationBarTextStyle": "black",
					"enablePullDownRefresh": false
				}
			}, {
				"path": "device/move",
				"style": {
					"navigationBarTitleText": "设备转移",
					"backgroundColor": "#f7f7f7",
					"navigationBarBackgroundColor": "#fff",
					"navigationBarTextStyle": "black",
					"enablePullDownRefresh": false
				}
			}, {
				"path": "device/choice",
				"style": {
					"navigationBarTitleText": "选择转入的设备",
					"backgroundColor": "#f7f7f7",
					"navigationBarBackgroundColor": "#fff",
					"navigationBarTextStyle": "black",
					"enablePullDownRefresh": false
				}
			}, {
				"path": "warning/index",
				"style": {
					"navigationBarTitleText": "告警提醒",
					"backgroundColor": "#f7f7f7",
					"navigationBarBackgroundColor": "#fff",
					"navigationBarTextStyle": "black",
					"enablePullDownRefresh": false
				}
			}, {
				"path": "warning/index-v2",
				"style": {
					"navigationBarTitleText": "提醒",
					"backgroundColor": "#f7f7f7",
					"navigationBarBackgroundColor": "#fff",
					"navigationBarTextStyle": "black",
					"enablePullDownRefresh": false
				}
			}, {
				"path": "warning/dev-msg/index",
				"style": {
					"navigationBarTitleText": "设备通知",
					"backgroundColor": "#f7f7f7",
					"navigationBarBackgroundColor": "#fff",
					"navigationBarTextStyle": "black",
					"enablePullDownRefresh": false
				}
			}, {
				"path": "warning/dev-msg/detail",
				"style": {
					"navigationBarTitleText": "设备通知详情",
					"backgroundColor": "#f7f7f7",
					"navigationBarBackgroundColor": "#fff",
					"navigationBarTextStyle": "black",
					"enablePullDownRefresh": false
				}
			}, {
				"path": "timeline/index",
				"style": {
					"navigationBarTitleText": "事件进展",
					"backgroundColor": "#f7f7f7",
					"navigationBarBackgroundColor": "#fff",
					"navigationBarTextStyle": "black",
					"enablePullDownRefresh": false
				}
			}, {
				"path": "exception/index",
				"style": {
					"navigationBarTitleText": "异常记录",
					"backgroundColor": "#f7f7f7",
					"navigationBarBackgroundColor": "#fff",
					"navigationBarTextStyle": "black",
					"enablePullDownRefresh": false
				}
			}, {
				"path": "exception/info",
				"style": {
					"navigationBarTitleText": "异常详情",
					"backgroundColor": "#f7f7f7",
					"navigationBarBackgroundColor": "#fff",
					"navigationBarTextStyle": "black",
					"enablePullDownRefresh": false
				}
			}, {
				"path": "address/index",
				"style": {
					"navigationBarTitleText": "家庭地址",
					"backgroundColor": "#f7f7f7",
					"navigationBarBackgroundColor": "#fff",
					"navigationBarTextStyle": "black",
					"enablePullDownRefresh": false
				}
			}, {
				"path": "device/wifi/index",
				"style": {
					"backgroundColor": "#fff",
					"navigationBarBackgroundColor": "#fff",
					"navigationBarTitleText": "配置蓝牙/WIFI",
					"navigationBarTextStyle": "black",
					"enablePullDownRefresh": false
				}
			}, {
				"path": "device/agreement",
				"style": {
					"backgroundColor": "#fff",
					"navigationBarBackgroundColor": "#fff",
					"navigationBarTitleText": "熵行科技服务协议",
					"navigationBarTextStyle": "black",
					"enablePullDownRefresh": false
				}
			}, {
				"path": "device/notActive-v2",
				"style": {
					"backgroundColor": "#f7f7f7",
					"navigationBarBackgroundColor": "#fff",
					"navigationBarTitleText": "设备详情",
					"navigationBarTextStyle": "black",
					"enablePullDownRefresh": false
				}
			}, {
				"path": "device/order/index",
				"style": {
					"backgroundColor": "#f7f7f7",
					"navigationBarBackgroundColor": "#fff",
					"navigationBarTextStyle": "black",
					"enablePullDownRefresh": false,
					"navigationBarTitleText": "产品服务"
				}
			}, {
				"path": "device/spec/buy",
				"style": {
					"backgroundColor": "#f7f7f7",
					"navigationBarBackgroundColor": "#fff",
					"navigationBarTextStyle": "black",
					"enablePullDownRefresh": false,
					"navigationBarTitleText": "套餐选择"
				}
			}, {
				"path": "device/spec/index",
				"style": {
					"backgroundColor": "#fff",
					"navigationBarBackgroundColor": "#fff",
					"navigationBarTextStyle": "black",
					"enablePullDownRefresh": false,
					"navigationBarTitleText": ""
				}
			}, {
				"path": "device/config/test-dev",
				"style": {
					"backgroundColor": "#fff",
					"navigationBarBackgroundColor": "#fff",
					"navigationBarTitleText": "测试设备",
					"navigationBarTextStyle": "black",
					"enablePullDownRefresh": false
				}
			}, {
				"path": "device/config/test-doing",
				"style": {
					"backgroundColor": "#fff",
					"navigationBarBackgroundColor": "#fff",
					"navigationBarTitleText": "测试设备",
					"navigationBarTextStyle": "black",
					"enablePullDownRefresh": false
				}
			}, {
				"path": "device/config/test-error",
				"style": {
					"backgroundColor": "#fff",
					"navigationBarBackgroundColor": "#fff",
					"navigationBarTitleText": "测试设备",
					"navigationBarTextStyle": "black",
					"enablePullDownRefresh": false
				}
			}, {
				"path": "contact/index",
				"style": {
					"backgroundColor": "#f7f7f7",
					"navigationBarBackgroundColor": "#fff",
					"navigationBarTitleText": "家庭-紧急联系人",
					"navigationBarTextStyle": "black",
					"enablePullDownRefresh": false
				}
			}, {
				"path": "contact/selector",
				"style": {
					"backgroundColor": "#f7f7f7",
					"navigationBarBackgroundColor": "#fff",
					"navigationBarTitleText": "选择家庭紧急联系人",
					"navigationBarTextStyle": "black",
					"enablePullDownRefresh": false
				}
			}, {
				"path": "member-server/order-form",
				"style": {
					"backgroundColor": "#f7f7f7",
					"navigationBarBackgroundColor": "#fff",
					"navigationBarTextStyle": "black",
					"enablePullDownRefresh": false,
					"navigationBarTitleText": "服务信息"
				}
			}, {
				"path": "member-server/pay-finish",
				"style": {
					"backgroundColor": "#f7f7f7",
					"navigationBarBackgroundColor": "#fff",
					"navigationBarTextStyle": "black",
					"enablePullDownRefresh": false,
					"navigationBarTitleText": "支付完成"
				}
			}, {
				"path": "member-server/my-server",
				"style": {
					"navigationBarTitleText": "我的服务",
					"backgroundColor": "#f7f7f7",
					"navigationBarBackgroundColor": "#fff",
					"navigationBarTextStyle": "black",
					"enablePullDownRefresh": false
				}
			}, {
				"path": "member-server/server-record",
				"style": {
					"navigationBarTitleText": "服务记录",
					"backgroundColor": "#f7f7f7",
					"navigationBarBackgroundColor": "#fff",
					"navigationBarTextStyle": "black",
					"enablePullDownRefresh": false
				}
			}, {
				"path": "member-server/order-detail",
				"style": {
					"navigationBarTitleText": "服务详情",
					"backgroundColor": "#f7f7f7",
					"navigationBarBackgroundColor": "#fff",
					"navigationBarTextStyle": "black",
					"enablePullDownRefresh": false
				}
			}, {
				"path": "member-server/record-event-timeline",
				"style": {
					"navigationBarTitleText": "服务跟踪记录",
					"backgroundColor": "#f7f7f7",
					"navigationBarBackgroundColor": "#fff",
					"navigationBarTextStyle": "black",
					"enablePullDownRefresh": false
				}
			}, {
				"path": "member-card/delay-device",
				"style": {
					"navigationBarTitleText": "延期设备",
					"backgroundColor": "#f7f7f7",
					"navigationBarBackgroundColor": "#fff",
					"navigationBarTextStyle": "black",
					"enablePullDownRefresh": false
				}
			}, {
				"path": "member-card/activate-card",
				"style": {
					"navigationBarTitleText": "激活会员卡",
					"navigationBarTextStyle": "white",
					"backgroundColor": "#000",
					"navigationBarBackgroundColor": "#000",
					"enablePullDownRefresh": false
				}
			}, {
				"path": "member-card/card-info",
				"style": {
					"navigationBarTitleText": "会员卡",
					"navigationBarTextStyle": "black",
					"backgroundColor": "#f7f7f7",
					"navigationBarBackgroundColor": "#fff",
					"enablePullDownRefresh": false
				}
			}, {
				"path": "member-card/activate-list",
				"style": {
					"navigationBarTitleText": "激活记录",
					"navigationBarTextStyle": "black",
					"backgroundColor": "#f7f7f7",
					"navigationBarBackgroundColor": "#fff",
					"enablePullDownRefresh": false
				}
			}, {
				"path": "member-card/take-delivery-goods",
				"style": {
					"navigationBarTitleText": "设备提货",
					"backgroundColor": "#f7f7f7",
					"navigationBarBackgroundColor": "#fff",
					"navigationBarTextStyle": "black",
					"enablePullDownRefresh": false
				}
			}, {
				"path": "member-card/take-delivery-goods-list",
				"style": {
					"navigationBarTitleText": "提货管理",
					"backgroundColor": "#f7f7f7",
					"navigationBarBackgroundColor": "#fff",
					"navigationBarTextStyle": "black",
					"enablePullDownRefresh": false
				}
			}

		]
	}],
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "uView",
		"navigationBarBackgroundColor": "#FFFFFF",
		"backgroundColor": "#FFFFFF"
	},
	"tabBar": {
		"color": "#979797",
		"selectedColor": "#000000",
		"backgroundColor": "#FFFFFF",
		"borderStyle": "black",
		"list": [{
				"text": "首页",
				"pagePath": "pages/index/index",
				"iconPath": "static/images/bottom/icon-home-normal.png",
				"selectedIconPath": "static/images/bottom/icon-home-actived.png"
			},
			{
				"text": "报告(体验版)",
				"pagePath": "pages/report/index",
				"iconPath": "static/images/bottom/icon-report-normal.png",
				"selectedIconPath": "static/images/bottom/icon-report-actived.png"
			},
			{
				"text": "我的",
				"pagePath": "pages/my/index",
				"iconPath": "static/images/bottom/icon-my-normal.png",
				"selectedIconPath": "static/images/bottom/icon-my-actived.png"
			}
		]
	}
}
