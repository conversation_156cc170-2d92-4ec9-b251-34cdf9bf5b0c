<template>
	<view>
		<template v-if="olders.length">
			<view v-if="!hideTitle">所在家庭被监护人</view>

			<scroll-view scroll-x="true">
				<view class="avatar_view">
					<view v-for="(older, index) in olders" :key="index" class="_avatar">
						<view class="_circle _normal"
							:class="{
								_active: index === activeIndex,
								_warn: older.guard === false,
							}"
							@click="clickOlder(older, index)">
							{{ older.name }}
						</view>
						<view>{{ older.alias || older.name }}</view>
					</view>
				</view>
			</scroll-view>
		</template>

		
		<view v-else-if="!hideTips" class="tips">请进入家庭管理下的详情界面，设置被监护人信息</view>
	</view>
</template>

<script>
const events = {
	clickOlder: "clickOlder",
}
export default {
	name: "older-avatar-view",
	props: {
		olders: {
			type: Array,
			default: []
		},
		//是否响应click和高亮当前人
		mute: false,
		hideTitle: false,
		hideTips: false,
	},
	data() {
		return {
			activeIndex: 100,
		};
	},
	created() {
		this.activeIndex = this.mute ? -1 : 0;
	},
	methods: {
		clickOlder(older, index) {
			if (this.mute)
				return;
			this.activeIndex = index;
			this.$emit(events.clickOlder, older, index)
		}
	}
}
</script>

<style lang="scss" scoped>
.avatar_view {
	display: flex;
	flex-wrap: nowrap;
	justify-content: flex-start;
	._avatar {
		flex: 0 0 25%;
		display: flex;
		flex-direction: column;
		align-items: center;
		margin: 10rpx 0;
		text-align: center;
		._circle {
			white-space: nowrap;
			text-overflow: ellipsis;
			overflow: hidden;
			border-radius: 50%;
			width: 120rpx;
			height: 120rpx;
			line-height: 120rpx;
			text-align: center;
		}
		._normal {
			border: 1px solid #01B09A;
			color: #01B09A;
		}
		._warn {
			border: 0;
			background-color: #e57f8c;
			color: white;
		}
		._active {
			box-shadow: 0 0 4rpx 4rpx #c8c9cc;
			font-weight: 700;
		}
	}
}
.tips {
	font-size: 13px;
	color: $u-type-warning;
}


</style>