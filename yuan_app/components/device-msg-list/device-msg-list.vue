<template>
	<view class="page-wrap">
		<view class="top-header-line"></view>
		<view class="r-flex" style="padding: 26rpx; padding-bottom: 0rpx;">
			<view class="r-flex-1">
				<u-input v-model="devNameOrCode" :border="true" placeholder="请输入设备名称或编号" placeholder-style="color: #bbbbbb" />
			</view>
			<view class="ta-r" style="width: 100rpx;">
				<u-icon name="calendar" color="5fb6bd" size="70" @click="showDateSelector">
				</u-icon>
			</view>
		</view>

		<view class="radio-btns">
			<view @click="switchFindMode(0)" :class="[findMode === 0 ? 'active-btn' : 'rest-btn']">按天</view>
			<view @click="switchFindMode(1)" :class="[findMode === 1 ? 'active-btn' : 'rest-btn']">按月</view>
		</view>

		<view class="item-wrap">
			<view style="margin: 16rpx 10rpx 10rpx;">{{ dateTitle }}</view>
			<view v-for="(data, index) in datas" :key="index" @click="toRead(data)" class="item">
				<view class="item-title">
					{{data.createTime}}
				</view>
				<view class="item-content">
					<view>
						<image class="left-img" :src="`${staticUrl}/images/device/device-1-1.png`"></image>
						<view v-if="data.readStatus == unreadConst" class="red-dot"></view>
					</view>
					<view class="r-flex-1">
						<view class="off-line r-flex">
							<div class="r-flex-1">
								{{data.msgTypeDescr}}
							</div>
							<u-icon name="arrow-right" size="30rpx"></u-icon>
						</view>
						<view class="content">
							{{data.devName+""+(data.devCode&&"("+data.devCode+")")}}
						</view>
						<view class="content">{{ `来自 ${data.title}-${data.content}` }}</view>
					</view>
				</view>
			</view>

			<u-empty v-if="!datas.length" text="暂无数据" mode="list"></u-empty>
			<u-divider v-else-if="alreadyFetchAll">没有更多了</u-divider>
			<u-loadmore v-else :status="loadStatus" class="loadmore" />

		</view>

		<!-- 选择日期 -->
		<u-calendar v-model="showCalendar" @change="selectedDate" mode="date"></u-calendar>
		<u-select v-model="showYMSelector" mode="mutil-column-auto" :list="yearMonthList"
			@confirm="selectedYearMonth">
		</u-select>
	</view>
</template>

<script>
export default {
	data() {
		return {
			devNameOrCode: '',
			todayYmd: '',

			staticUrl: this.$u.http.config.staticBaseUrl,
			findMode: 0,
			yearMonthList: [],

			showCalendar: false,
			showYMSelector: false,

			findDate: null,
			findYearMonth: null,

			loadStatus: 'loadmore',
			page: 0,
			datas: [],
			alreadyFetchAll: false,

			unreadConst: "0",
		}
	},
	computed: {
		dateTitle() {
			if (this.findMode == 0) {
				if (this.findDate) {
					return this.todayYmd == this.findDate ? "今天" : this.findDate;
				}
				return "";
			}
			return this.findYearMonth ? this.findYearMonth : "";
		},
	},
	watch: {
		'devNameOrCode': function (val) {
			this.$u.debounce(this.handleSearch, 700);
		}
	},
	created() {
		this.initData();
	},
	mounted() {
		// this.fetchDatas();
	},
	methods: {
		initData() {
			const now = new Date();
			this.todayYmd = this.$u.timeFormat(now, 'yyyy-mm-dd');
			// this.findDate = this.todayYmd;
			// this.findYearMonth = this.$u.timeFormat(now, 'yyyy-mm');

			const months = [];
			for (let i = 1; i <= 12; i++) {
				months.push({
					label: `${i}`,
					value: i < 10 ? `0${i}` : i,
				})
			}

			let yearMonthList = [];
			let year = now.getFullYear();
			for (let i = 0; i < 10; i++, year--) {
				yearMonthList.push({
					label: `${year}`,
					value: year,
					children: months
				});
			}
			this.yearMonthList = yearMonthList;
		},
		showDateSelector() {
			const { findMode } = this;
			this.showCalendar = findMode == 0;
			this.showYMSelector = findMode == 1;
		},
		switchFindMode(findMode) {
			if (this.findMode === findMode) {
				return;
			}
			this.findMode = findMode;
			this.fetchDatas()
		},
		selectedDate({ result }) {
			this.showCalendar = false;
			this.findDate = result;
			this.fetchDatas()
		},
		selectedYearMonth([year, month]) {
			this.showYMSelector = false;
			this.findYearMonth = `${year.value}-${month.value}`;
			this.fetchDatas()
		},
		handleSearch() {
			this.fetchDatas();
		},
		//组件外部调用方法
		fetchNextPage(reset = false) {
			// this.$u.debounce(() => { this.fetchDatas(false) }, 1000);
			this.fetchDatas(reset);
		},
		async fetchDatas(reset = true) {
			if (reset) {
				this.page = 0;
				this.datas = [];
				this.alreadyFetchAll = false;
			}
			const { alreadyFetchAll, findMode, findDate, findYearMonth, datas, devNameOrCode } = this;
			if (alreadyFetchAll) {
				return;
			}
			this.page = this.page + 1;
			
			let startDate = "", endDate = "";
			if (findMode === 0 && findDate) {
				startDate = new Date(findDate);
				endDate = new Date(findDate);
				endDate.setDate(startDate.getDate() + 1);
				startDate = this.$u.timeFormat(startDate, "yyyy-mm-dd");
				endDate = this.$u.timeFormat(endDate, "yyyy-mm-dd");
			} else if (findYearMonth) {
				startDate = new Date(findYearMonth + "-01");
				endDate = new Date(findYearMonth + "-01");
				endDate.setMonth(startDate.getMonth() + 1);
				startDate = this.$u.timeFormat(startDate, "yyyy-mm-dd");
				endDate = this.$u.timeFormat(endDate, "yyyy-mm-dd");
			}

			try {
				this.loadStatus = 'loading';
				const res = await this.$u.api.pageDevMsgMy({ startDate, endDate, current: this.page, devNameOrCode });
				//10为分页大小
				this.alreadyFetchAll = res.length < 10;
				this.datas = datas.concat(res);
				this.loadStatus = 'loadmore';
			} catch (err) {
				console.error(err);
			}
		},
		toRead({ id }) {
			this.$navTo(`pagesFamily/warning/dev-msg/detail?id=${id}`);
		},
	}
}
</script>

<style lang="scss" scoped>
.page-wrap {
	.radio-btns {
		padding: 20rpx 10rpx;
		display: flex;
		text-align: center;
		.btn {
			width: 180rpx;
			height: 60rpx;
			line-height: 60rpx;
		}
		.rest-btn {
			@extend .btn;
			color: #aaaaaa;
			border: 1rpx solid #aaaaaa;
			border-right: 0;
		}
		.rest-btn:nth-last-child(1) {
			border-right: 1rpx solid #aaaaaa;
		}
		.active-btn {
			@extend .btn;
			color: #01B09A;
			border: 1rpx solid #01B09A;
		}
		.active-btn + .rest-btn {
			border-left: 0;
		}
	}

	.item-wrap {
		margin-top: 20rpx;
		padding: 10rpx 0;
		background-color: #F2F2F2;
		.item {
			margin-bottom: 20rpx;
			background-color: #FFFFFF;
			.item-title {
				height: 60rpx;
				line-height: 60rpx;
				padding: 0 10rpx;
				border-bottom: 1rpx solid #d7d7d7;
				font-size: 24rpx;
			}
			.item-content {
				padding: 20rpx 10rpx 0 10rpx;
				display: flex;
				font-size: 26rpx;
				.left-img {
					width: 70rpx;
					height: 70rpx;
					margin-right: 30rpx;
					vertical-align: middle;
				}

				.off-line {
					font-size: 30rpx;
					margin-bottom: 10rpx;
				}
				.content {
					margin-bottom: 10rpx;
				}
			}
		}
		
	}

	.red-dot {
		width: 20rpx;
		height: 20rpx;
		border-radius: 50%;
		background-color: #d00010;
		position: relative;
		left: 76rpx;
		top: 16rpx;
	}

}
</style>
