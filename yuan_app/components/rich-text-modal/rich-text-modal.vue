<template>
	<u-modal v-model="showModal"
		:title="title"
		:width="700"
		:confirm-style="{color: counter_ ? '$u-light-color' : '$u-main-color'}"
	>
		<view class="slot-content">
			<rich-text :nodes="content"></rich-text>
		</view>
		<template #confirm-button>
			<view class="footer-btns">
				<u-button
					class="agree-btn diy-btn"
					shape="circle"
					@click="confirm"
					:disabled="counter_ > 0"
					:custom-style="{ 'width': '100%', 'color': counter_ > 0 ? '' : '#01B09A', 'background-color': 'transparent'}"
					hover-class="none"
				>
					<!-- :class="{confirmBtnEnabled: counter_ == 0}" -->
					{{confirmText_}}
				</u-button>
			</view>
		</template>
	</u-modal>
</template>

<script>
const events = {
	confirm: "confirm"
}
const props = {
	show: {
		type: Boolean,
	},
	title: {
		type: String,
		default: "服务协议契约条款",
	},
	content: {
		type: String,
	},
	confirmText: {
		type: String,
		default: "知道了",
	},
	counter: {
		type: Number,
		default: 5,
	},
}
// 增加禁用样式
// 点确认按钮不关闭对话框
export default {
	name:"RichTextModal",
	props,
	data() {
		return {
			showModal: {
				type: Boolean,
				default: false,
			},
			interval: 1000,
			confirmText_: '',
			autoClose: false,
			counter_: 0,
		};
	},
	
	watch: {
		show: {
			handler: function(nVal) {
				this.showModal = nVal;
				if (!nVal) {
					return;
				}
				this.startCounter()
			},
			immediate: true
		}
	},
	
	methods: {
		startCounter() {
			let {counter, confirmText} = this;
			this.counter_ = counter;
			if (counter) {
				this.confirmText_ = `${confirmText} (${counter}秒)`;
				let countTimer = setInterval(
					() => {
						counter--;
						this.confirmText_ = `${confirmText} ${counter ? '(' + counter + '秒)': '' }`;
						if (counter == 0) {
							clearInterval(countTimer);
							countTimer = undefined;
							this.counter_ = counter;
							if (this.autoClose) {
								this.showModal = false;
							}
						}
					},
					this.interval
				);
			} else {
				this.confirmText_ = confirmText;
			}
		},
		confirm() {
			this.$emit(events.confirm);
		}
	}
}
</script>

<style lang="scss" scoped>
.slot-content {
	font-size: 28rpx;
	color: $u-content-color;
	padding: 30rpx;
	min-height: 750rpx;
	margin-top: 40rpx;
}

.confirmBtn {
	::v-deep button {
		background-color: transparent !important;
		width: 500rpx;
	}
}


.footer-btns {
	padding: 10rpx 60rpx;
	text-align: center;
}
</style>