<template>
	<view>
		<!-- #ifdef MP-WEIXIN -->
		<uni-ec-canvas class="uni-ec-canvas" ref="rectCanvas" canvas-id="rect-lazy-load-chart" :ec="ec"></uni-ec-canvas>
		<!-- #endif -->
		<!-- #ifndef MP-WEIXIN -->
		<view :id="optionId" class="uni-ec-canvas"></view>
		<!-- #endif -->
	</view>
</template>

<script>
	import uniEcCanvas from '@/components/uni-ec-canvas/uni-ec-canvas.vue';
	import * as echarts from "@/components/uni-ec-canvas/echarts";
	import * as util from '@/utils/util';
	
	let RectChat = null
	const colorList = [
		'#45B3FF',
		'#FF7D17',
		'#20CD8A',
		'#01B09A',
	];
	export default {
		name: 'RectChat',
		components: {
			uniEcCanvas
		},
		props: {
			datas: {
				type: Array,
				default: function() {
					return []
				}
			},
		},
		data() {
			return {
				optionId: undefined,
				ec: {
					lazyLoad: true
				},
				option: {
					title: {
					      show: false, // 无数据时展示 title
					      textStyle: {
					        color: 'gray',
					        fontSize: 13
					      },
					      text: '暂无数据',
					      left: 'center',
					      top: 'center'
					},
					tooltip: {
						show:true,
						formatter:'{b}',
					},
					xAxis: {
						show: true,
						// axisLine:{
						// 	show:false,
						// }
						axisTick:{
							show:false,
						},
						splitLine:{
							show:false,
						},
						axisLabel: {  
							show: false,  
							// formatter: '{value} xx',
							// formatter: function (value, index) {
							// 	if (index === 0 || index === this.data.length - 1) {  
							// 		return value; // 显示第一个和最后一个标签  
							// 	} else {  
							// 		return ''; // 中间标签不显示  
							// 	}
							// },
						},
					},
					yAxis: {
						show: true,
						axisLine:{
							show:false,
						},
						axisTick:{
							show:false,
						},
						axisLabel:{
							show:false,
						},
					},
					grid:{
						top:10,
						bottom:2,
					},
					series: [{
						type: 'custom',
						renderItem: function(params, api) {
							var yValue = api.value(2);
							var start = api.coord([api.value(0), yValue]);
							var size = api.size([api.value(1) - api.value(0), yValue]);
							var style = api.style();
							return {
								type: 'rect',
								shape: {
									x: start[0],
									y: start[1],
									width: size[0],
									height: size[1]
								},
								style: style
							};
						},
						// label: {
						// 	show: true,
						// 	position: 'top'
						// },
						dimensions: ['from', 'to', 'profit'],
						encode: {
							x: [0, 1],
							y: 2,
							tooltip: [0, 1, 2],
							itemName: 3
						},
						data:[],
					}]
				}
			}
		},
		mounted() {
			// 设置随机数id
			let t = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
			let len = t.length
			let id = ''
			for (let i = 0; i < 32; i++) {
				id += t.charAt(Math.floor(Math.random() * len))
			}
			this.optionId = id
			const numArr = [
					[10, 16, 1],
					[16, 18, 3],
					[18, 26, 2],
					[26, 32, 3],
					[32, 56, 2],
					[56, 62, 4]
				];
				
			const dateArr = [
					{startX:'2023-12-12 00:00:00', endX:'2023-12-13 00:00:00', height:1},
					{startX:'2023-12-13 00:00:00', endX:'2023-12-15 00:00:00', height:3},
					{startX:'2023-12-15 00:00:00', endX:'2023-12-16 00:00:00', height:2},
					{startX:'2023-12-16 00:00:00', endX:'2023-12-19 00:00:00', height:3},
					{startX:'2023-12-19 00:00:00', endX:'2023-12-20 00:00:00', height:1},
					{startX:'2023-12-20 00:00:00', endX:'2023-12-23 00:00:00', height:2},
					];
			console.log("this.datas:",this.datas)
			//const dateArr2 = this.convertTimeToValues(this.datas,new Date(this.timeMin),new Date(this.timeMax));
			const dateArr2 = this.datas;
			console.log("dateArr2:",dateArr2)
			if(!dateArr2 || dateArr2.length<1){
				this.option.title.show = true;
			}else{
				this.option.series[0].data = dateArr2.map(function(item, index) {
					// console.log("item:",item)
					// console.log("color:",colorList[item[2]-1])
					// if(index==dateArr2.length-1){
					// 	return;
					// }
					return {
						value: [item.startX,item.endX,item.height+1,item.createDate],
						itemStyle: {
							color: colorList[item.height]
						}
					};
				});
				console.log("this.option.series[0].data:",this.option.series[0].data)
			}
			this.$nextTick(() => {
				// #ifdef MP-WEIXIN
				this.$refs.rectCanvas.init(this.initChart)
				// #endif
				// #ifndef MP-WEIXIN
				let el = document.getElementById(this.optionId);
				let myChart = echarts.init(el);
				myChart.setOption(this.option);
				// #endif
			})
		},
		beforeDestroy() {
			// #ifdef MP-WEIXIN
			RectChat.dispose();
			// #endif
			console.log('RectChat.dispose');
		},
		methods: {
			convertTimeToValues:util.convertTimeToValues,
			initChart(canvas, width, height, canvasDpr) {
				RectChat = echarts.init(canvas, null, {
					width: width,
					height: height,
					devicePixelRatio: canvasDpr
				})
				canvas.setChart(RectChat)
				RectChat.setOption(this.option)
				return RectChat
			}
		}
	}
</script>

<style>
	.uni-ec-canvas {
		width: 100%;
		height: 200rpx;
		display: block;
	}
</style>
