<template>
	<view class="family-warning-page">
		<view class="top-header-line"></view>
		<view class="r-flex" style="padding: 26rpx; padding-bottom: 0rpx;">
			<view class="r-flex-1">
				<u-input v-model="devNameOrCode" :border="true" placeholder="请输入设备名称或编号" placeholder-style="color: #bbbbbb" />
			</view>
			<view class="ta-r" style="width: 100rpx;">
				<!-- <u-icon name="calendar" :color="date ? '#01B09A' : '#bbb'" size="70" @click="dialog.calendar.show = true">
				</u-icon> -->
				<u-icon name="calendar" color="5fb6bd" size="70" @click="showDateSelector">
				</u-icon>
			</view>
		</view>

		<view class="radio-btns">
			<view @click="switchFindMode(0)" :class="[findMode === 0 ? 'active-btn' : 'rest-btn']">按天</view>
			<view @click="switchFindMode(1)" :class="[findMode === 1 ? 'active-btn' : 'rest-btn']">按月</view>
		</view>

		<view class="content">
			<view style="margin: 16rpx 10rpx 10rpx;">{{ dateTitle }}</view>
			<view class="warning-item" v-for="(warning, index) in datas" :key="index"
				@click="$navTo(`pagesFamily/timeline/index?id=${warning.id}`)">
				<view class="info">
					<view style="position: relative;">
						{{ warning.createTime }}
						<text v-if="warning.tag"
							style="color: white; background: #01B09A; font-size: 20rpx; padding: 4rpx 10rpx; margin-left: 10rpx;">{{
									warning.tag
							}}</text>
						<text style="position: absolute; top: 0rpx; right: 0rpx;" :style="{ color: dict.statusColor[warning.status] }">{{ warning.statusName }}</text>
					</view>
					<view @longtap="longtapCopy(warning.devName || warning.devCode)">{{ `设备：${warning.devName}(${warning.devCode})` }}</view>
					<view style="font-size: 23rpx;" @longtap="longtapCopy(warning.houseName)">{{ warning.houseName }}</view>
					<!-- <view class="address">
					{{ warning.title }}
					</view> -->
					<view class="red" style="text-align: right;">发生{{ warning.orderTypeName }}</view>
				</view>
			</view>
		</view>

		<u-empty v-if="!datas.length" text="暂无数据" mode="list"></u-empty>
		<u-divider v-else-if="alreadyFetchAll">没有更多了</u-divider>
		<u-loadmore v-else :status="loadStatus" class="loadmore" />

		<!-- <u-calendar v-model="dialog.calendar.show" mode="date" btn-type="success" active-bg-color="#01B09A"
			@change="handleDateChange"></u-calendar> -->

		<!-- 选择日期 -->
		<u-calendar v-model="showCalendar" @change="selectedDate" mode="date"></u-calendar>
		<u-select v-model="showYMSelector" mode="mutil-column-auto" :list="yearMonthList" @confirm="selectedYearMonth">
		</u-select>
	</view>
</template>

<script>
import {longtapCopy} from '../../utils/util'
export default {
	data() {
		return {
			// dialog: {
			// 	calendar: {
			// 		show: false
			// 	}
			// },
			dict: {
				statusColor: {
					'0': '#ff3609',
					'1': '#999',
					'2': '#25c525',
				}
			},
			loadStatus: 'loadmore',
			page: 0,
			datas: [],
			currFamilyId: undefined,
			devNameOrCode: '',
			firstIsNull: true,


			todayYmd: '',

			findMode: 0,
			yearMonthList: [],

			showCalendar: false,
			showYMSelector: false,

			findDate: null,
			findYearMonth: null,

			alreadyFetchAll: false,
		}
	},
	computed: {
		dateTitle() {
			if (this.findMode == 0) {
				if (this.findDate) {
					return this.todayYmd == this.findDate ? "今天" : this.findDate;
				}
				return "";
			}
			return this.findYearMonth ? this.findYearMonth : "";
		},
	},
	watch: {
		'devNameOrCode': function (val) {
			this.$u.debounce(this.handleSearch, 700)
		}
	},
	created() {
		const family = uni.getStorageSync('curr_family');
		this.currFamilyId = family?.id;
		this.initData();
		// this.fetchDatas(true);
	},
	methods: {
		longtapCopy,
		initData() {
			const now = new Date();
			this.todayYmd = this.$u.timeFormat(now, 'yyyy-mm-dd');
			// this.findDate = this.todayYmd;
			// this.findYearMonth = this.$u.timeFormat(now, 'yyyy-mm');

			const months = [];
			for (let i = 1; i <= 12; i++) {
				months.push({
					label: `${i}`,
					value: i < 10 ? `0${i}` : i,
				})
			}

			let yearMonthList = [];
			let year = now.getFullYear();
			for (let i = 0; i < 10; i++, year--) {
				yearMonthList.push({
					label: `${year}`,
					value: year,
					children: months
				});
			}
			this.yearMonthList = yearMonthList;
		},
		showDateSelector() {
			const { findMode } = this;
			this.showCalendar = findMode == 0;
			this.showYMSelector = findMode == 1;
		},
		switchFindMode(findMode) {
			if (this.findMode === findMode) {
				return;
			}
			this.findMode = findMode;
			this.fetchDatas()
		},
		selectedDate({ result }) {
			this.showCalendar = false;
			this.findDate = result;
			this.fetchDatas()
		},
		selectedYearMonth([year, month]) {
			this.showYMSelector = false;
			this.findYearMonth = `${year.value}-${month.value}`;
			this.fetchDatas()
		},

		handleDateChange(day) {
			this.date = day.result;
			this.handleSearch();
		},
		handleSearch() {
			this.page = 1;
			this.days = [];
			this.dayMapping = {};
			this.fetchDatas();
		},
		//组件外部调用方法
		fetchNextPage() {
			// this.$u.debounce(() => { this.fetchDatas(false) }, 1000);
			this.fetchDatas(false);
		},
		async fetchDatas(reset = true) {
			if (reset) {
				this.page = 0;
				this.datas = [];
				this.alreadyFetchAll = false;
			}
			const { alreadyFetchAll, findMode, findDate, findYearMonth, datas, devNameOrCode } = this;
			if (alreadyFetchAll) {
				return;
			}
			this.page = this.page + 1;

			let startDate = "", endDate = "";
			if (findMode === 0 && findDate) {
				startDate = new Date(findDate);
				endDate = new Date(findDate);
				endDate.setDate(startDate.getDate() + 1);
				startDate = this.$u.timeFormat(startDate, "yyyy-mm-dd");
				endDate = this.$u.timeFormat(endDate, "yyyy-mm-dd");
			} else if (findYearMonth) {
				startDate = new Date(findYearMonth + "-01");
				endDate = new Date(findYearMonth + "-01");
				endDate.setMonth(startDate.getMonth() + 1);
				startDate = this.$u.timeFormat(startDate, "yyyy-mm-dd");
				endDate = this.$u.timeFormat(endDate, "yyyy-mm-dd");
			}

			try {
				this.loadStatus = 'loading';
				const { records } = await this.$u.api.fetchFamilyHisEventList({
					familyId: this.currFamilyId,
					current: this.page,
					devNameOrCode,
					startDate,
					endDate,
				});
				//10为分页大小
				this.alreadyFetchAll = records.length < 10;
				this.datas = datas.concat(records);
				this.loadStatus = 'loadmore';
			} catch (err) {
				console.error(err);
			}
		},
	}
}
</script>

<style lang="scss">
page {
	background-color: #f7f7f7;
}
</style>
<style lang="scss" scoped>
.family-warning-page {
	height: 100vh;
	//#ifdef H5
	height: 100%;
	//#endif

	.radio-btns {
		padding: 20rpx 10rpx;
		display: flex;
		text-align: center;
		.btn {
			width: 180rpx;
			height: 60rpx;
			line-height: 60rpx;
		}
		.rest-btn {
			@extend .btn;
			color: #aaaaaa;
			border: 1rpx solid #aaaaaa;
			border-right: 0;
		}
		.rest-btn:nth-last-child(1) {
			border-right: 1rpx solid #aaaaaa;
		}
		.active-btn {
			@extend .btn;
			color: #01B09A;
			border: 1rpx solid #01B09A;
		}
		.active-btn + .rest-btn {
			border-left: 0;
		}
	}

	.content {
		// margin-top: 20upx;
		padding: 20upx 26upx;
		box-sizing: border-box;
		margin-bottom: 30upx;
		padding-top: 0rpx;

		.day-title {
			font-size: 24rpx;
			color: #666;
			margin-bottom: 30rpx;
			margin-top: 30upx;
		}

		.warning-item {
			background: #FFFFFF;
			box-shadow: 0px 8upx 24upx 0px rgba(0, 0, 0, 0.1);
			border-radius: 16upx;
			// padding: 40upx;
			color: #333;
			display: flex;
			flex-direction: row;
			align-items: center;
			align-content: center;
			font-size: 30upx;

			view {
				&:nth-child(n + 2) {
					margin-top: 16upx;
				}
			}

			.info {
				padding: 20upx;
				font-size: 28upx;
				color: #000;
				width: 100%;

				.red {
					color: #ff2a2a;
				}
				.address {
					flex: 1;
					color: #5E5D5D;
				}
			}

			.arrow {
				width: 80rpx;
				text-align: center;

				.icon-arrow-right {
					vertical-align: middle;
					color: #6B6B6B;
					position: relative;
					top: 2rpx;
				}
			}

			&:nth-child(n + 2) {
				margin-top: 20upx;
			}
		}
	}

}

::v-deep .u-input {
	border-radius: 50rpx !important;
	border-color: #bbbbbb !important;
}

::v-deep .u-load-more-wrap {
	background: transparent !important;

	.u-more-text {
		background: transparent !important;
	}
}

::v-deep .u-load-more-wrap {
	margin-top: 30upx !important;
}

::v-deep .u-calendar__bottom {
	margin-top: 40rpx;
}

::v-deep .u-calendar__bottom__choose {
	display: none;
}

::v-deep .u-btn--success {
	border-color: #01B09A !important;
	background-color: #01B09A !important;
}

::v-deep .u-success-hover {
	border-color: #00ada2 !important;
	background-color: #00ada2 !important;
}
</style>
