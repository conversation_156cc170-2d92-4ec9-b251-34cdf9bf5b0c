<template>
	<view>
		<view class="compos">
			<view class="base-btn" @tap="show = !show" :style="btnStyle">
				<slot></slot>
			</view>

			<view class="modal" 
				:style="{ 
					height: show ? (btnList.length * 9 + 1) + 'vw' : 0,
					overflow: show ? '' : 'hidden', 
					width: modalWidth,
					top: modalTopPos,
					left: modalLeftPos,
					opacity: modalOpacity
				}"
				style="position:absolute; z-index: 999"
			>
				<view class="modal-ang" v-if="dotShow && btnList.length > 0" :style="direction !== 'left' ? 'left: 10px': 'right: 10px'"></view>
				<view class="modal-item" v-for="(item, index) in btnList" :index="index" :key="index" @tap="handleItemTap(index,item)" 
					:style="{ 'border-bottom': index === btnList.length - 1 ? 'none' : '2rpx solid #f1f1f1',
						'padding-top': index === 0 ? '3rpx' : '0rpx'}">
					{{ item.selfCreate === false ? `(分享)` : '' }}
					{{ item.name }}
					<text v-if="item.worryFree" class="icon iconfont icon-anquan1" style="margin-left: 10rpx; position: relative; top: 4rpx; font-size: 32rpx; color: #01B09A;"></text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	/**
	 * :btnList				按钮列表
	 * :btnStyle			外部按钮样式
	 * :modalWidth			弹出层宽度
	 * :modalLeftPos		弹出层左定位
	 * :modalTopPos			弹出层顶定位
	 * :modalOpacity		弹出层透明度
	 * :direction			弹出层箭头位置 left right
	 * :active				默认激活状态
	 * @select				选中列表触发事件
	 * */
	
	
export default {
	data() {
		return {
			show: this.active,
			dotShow: this.active
		};
	},
	watch: {
		show() {
			setTimeout(() => {
				this.dotShow = this.show;
				this.$emit('on-show-change', this.show);
			}, 50);
		},
		active() {
			this.show = this.active
		}
	},
	props: {
		btnList: {
			type: Array,
			default: () => {
				return [];
			}
		},
		btnStyle: {
			type: Object,
			default: () => {
				return {};
			}
		},
		modalWidth: {
			type: String,
			default: '15vw'
		},
		modalLeftPos: {
			type: String,
			default: '5vw'
		},
		modalTopPos: {
			type: String,
			default: '6vw'
		},
		modalOpacity: {
			type: String,
			default: '0.8'
		},
		direction: {
			type: String,
			default: 'left'
		},
		active: {
			type: Boolean,
			default: false
		}
	},
	methods: {
		handleItemTap(e,item) {
			this.$emit('on-select', e,item);
			this.show = false;
			this.dotShow = false;
		},
		handleClose(e) {
			this.show = false;
			this.dotShow = false;
			this.$emit('on-close');
		}
	}
};
</script>

<style lang="scss">
@import url("/static/css/iconfont.css");
.compos {
	position: relative;
	
	.modal {
		background-color: #ffffff;
		position: absolute;
		border-radius: 1vw;
		transition: height ease-out 100ms;
		z-index: 999;
		transform:translateZ(1px);
		font-size: 25rpx;
		// box-shadow: 0rpx 10rpx 10rpx #eee;
		padding: 0rpx 20rpx;
		// padding-top: 6rpx;

		.modal-item {
			z-index: 99;
			line-height: 9vw;
			color: #333;
			text-align: center;
			border-bottom: 2rpx solid #f1f1f1;
			margin-left: 4rpx;
			margin-right: rpx;
			// padding: 8rpx 0rpx;
			/* 一行显示*/
			-webkit-line-clamp: 1;
			/* 将对象作为弹性伸缩盒子模型显示 */
			display: -webkit-box;
			/*子元素的排列方式 */
			-webkit-box-orient: vertical;
			text-overflow:ellipsis;
			overflow: hidden;
			word-break:break-all;
		}

		.modal-item:last-child {
			border-bottom: none;
		}

		.modal-ang {
			background-color: white;
			position: absolute;
			width: 18rpx;
			height: 18rpx;
			transform: rotate(45deg);
			top: -6rpx;
			border-radius: 6rpx;
			// left: 0px;
			// right: 0px;
			// margin: 0 auto;
		}
	}
}

.base-btn {
	position: relative;
	border: 0upx;
	display: inline-flex;
	align-items: center;
	justify-content: center;
	box-sizing: border-box;
}
</style>
