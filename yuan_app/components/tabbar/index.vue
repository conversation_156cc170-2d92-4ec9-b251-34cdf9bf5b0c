<template>
	<view class="footer-assembly">
		<image class="bg" :src="'@/static/images/tabbar-bottom-bg.png'" mode="widthFix"></image>
		<image class="sos-img" :src="'@/static/images/tabbar-bottom-sos.png'" mode="widthFix" @click="gotoSos"></image>
		
		<view class="home-icon-btn" @click="gotoUrl('/pages/index/index')">
			<image :src="'@/static/images/icon-tabbar-home-' + (!index || index === 0 ? 'actived' : 'normal') + '.png'" mode="widthFix"></image>
		</view>
		<view class="my-icon-btn" @click="gotoUrl('/pages/my/index')">
			<image :src="'@/static/images/icon-tabbar-my-' + (index === 1 ? 'actived' : 'normal') + '.png'" mode="widthFix"></image>
		</view>		
	</view>
</template>

<script>
	export default {
		props: {
			index: {
				type: Number,
				default: 0
			},
		},
		data() {
			return {
				
			}
		},
		methods: {
			gotoSos() {
				uni.navigateTo({
					url: '/pages/sos/index'
				})
			},
			gotoUrl(url){
				if (!url) return;
				uni.redirectTo({
					url: url
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
.footer-assembly {
	position: fixed;
	left: 0upx;
	right: 0upx;
	bottom: -4upx;
	.bg {
		width:750upx;
	}
	.sos-img {
		width: 134upx;
		height: 134upx;
		margin: 0 auto;
		position: absolute;
		bottom: 40upx;
		left: 0upx;
		right: 0upx;
	}
	.home-icon-btn {
		display: inline-block;
		position: absolute;
		bottom: 20upx;
		left: 120upx;
		padding: 20upx;
		image {
			width: 44upx;
			height: 44upx;
		}
	}
	.my-icon-btn {
		display: inline-block;
		position: absolute;
		bottom: 20upx;
		right: 120upx;
		padding: 20upx;
		image {
			width: 44upx;
			height: 44upx;
		}
	}
}
</style>
