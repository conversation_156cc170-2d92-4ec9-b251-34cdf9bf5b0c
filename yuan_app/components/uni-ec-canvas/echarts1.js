!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e(t.echarts={})}(this,function(t){"use strict";function e(t){var e={},i={},n=t.match(/Firefox\/([\d.]+)/),r=t.match(/MSIE\s([\d.]+)/)||t.match(/Trident\/.+?rv:(([\d.]+))/),a=t.match(/Edge\/([\d.]+)/),o=/micromessenger/i.test(t);return n&&(i.firefox=!0,i.version=n[1]),r&&(i.ie=!0,i.version=r[1]),a&&(i.edge=!0,i.version=a[1]),o&&(i.weChat=!0),{browser:i,os:e,node:!1,canvasSupported:!!document.createElement("canvas").getContext,svgSupported:"undefined"!=typeof SVGRect,touchEventsSupported:"ontouchstart"in window&&!i.ie&&!i.edge,pointerEventsSupported:"onpointerdown"in window&&(i.edge||i.ie&&i.version>=11),domSupported:"undefined"!=typeof document}}function i(t,e){"createCanvas"===t&&(Rg=null),zg[t]=e}function n(t){if(null==t||"object"!=typeof t)return t;var e=t,i=Cg.call(t);if("[object Array]"===i){if(!B(t)){e=[];for(var r=0,a=t.length;a>r;r++)e[r]=n(t[r])}}else if(Tg[i]){if(!B(t)){var o=t.constructor;if(t.constructor.from)e=o.from(t);else{e=new o(t.length);for(var r=0,a=t.length;a>r;r++)e[r]=n(t[r])}}}else if(!Ig[i]&&!B(t)&&!I(t)){e={};for(var s in t)t.hasOwnProperty(s)&&(e[s]=n(t[s]))}return e}function r(t,e,i){if(!M(e)||!M(t))return i?n(e):t;for(var a in e)if(e.hasOwnProperty(a)){var o=t[a],s=e[a];!M(s)||!M(o)||_(s)||_(o)||I(s)||I(o)||S(s)||S(o)||B(s)||B(o)?!i&&a in t||(t[a]=n(e[a],!0)):r(o,s,i)}return t}function a(t,e){for(var i=t[0],n=1,a=t.length;a>n;n++)i=r(i,t[n],e);return i}function o(t,e){for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i]);return t}function s(t,e,i){for(var n in e)e.hasOwnProperty(n)&&(i?null!=e[n]:null==t[n])&&(t[n]=e[n]);return t}function l(){return Rg||(Rg=Bg().getContext("2d")),Rg}function h(t,e){if(t){if(t.indexOf)return t.indexOf(e);for(var i=0,n=t.length;n>i;i++)if(t[i]===e)return i}return-1}function u(t,e){function i(){}var n=t.prototype;i.prototype=e.prototype,t.prototype=new i;for(var r in n)n.hasOwnProperty(r)&&(t.prototype[r]=n[r]);t.prototype.constructor=t,t.superClass=e}function c(t,e,i){t="prototype"in t?t.prototype:t,e="prototype"in e?e.prototype:e,s(t,e,i)}function d(t){return t?"string"==typeof t?!1:"number"==typeof t.length:void 0}function f(t,e,i){if(t&&e)if(t.forEach&&t.forEach===kg)t.forEach(e,i);else if(t.length===+t.length)for(var n=0,r=t.length;r>n;n++)e.call(i,t[n],n,t);else for(var a in t)t.hasOwnProperty(a)&&e.call(i,t[a],a,t)}function p(t,e,i){if(t&&e){if(t.map&&t.map===Og)return t.map(e,i);for(var n=[],r=0,a=t.length;a>r;r++)n.push(e.call(i,t[r],r,t));return n}}function g(t,e,i,n){if(t&&e){if(t.reduce&&t.reduce===Eg)return t.reduce(e,i,n);for(var r=0,a=t.length;a>r;r++)i=e.call(n,i,t[r],r,t);return i}}function v(t,e,i){if(t&&e){if(t.filter&&t.filter===Pg)return t.filter(e,i);for(var n=[],r=0,a=t.length;a>r;r++)e.call(i,t[r],r,t)&&n.push(t[r]);return n}}function m(t,e,i){if(t&&e)for(var n=0,r=t.length;r>n;n++)if(e.call(i,t[n],n,t))return t[n]}function y(t,e){var i=Lg.call(arguments,2);return function(){return t.apply(e,i.concat(Lg.call(arguments)))}}function x(t){var e=Lg.call(arguments,1);return function(){return t.apply(this,e.concat(Lg.call(arguments)))}}function _(t){return"[object Array]"===Cg.call(t)}function w(t){return"function"==typeof t}function b(t){return"[object String]"===Cg.call(t)}function M(t){var e=typeof t;return"function"===e||!!t&&"object"===e}function S(t){return!!Ig[Cg.call(t)]}function A(t){return!!Tg[Cg.call(t)]}function I(t){return"object"==typeof t&&"number"==typeof t.nodeType&&"object"==typeof t.ownerDocument}function T(t){return t!==t}function C(){for(var t=0,e=arguments.length;e>t;t++)if(null!=arguments[t])return arguments[t]}function D(t,e){return null!=t?t:e}function k(t,e,i){return null!=t?t:null!=e?e:i}function P(){return Function.call.apply(Lg,arguments)}function L(t){if("number"==typeof t)return[t,t,t,t];var e=t.length;return 2===e?[t[0],t[1],t[0],t[1]]:3===e?[t[0],t[1],t[2],t[1]]:t}function O(t,e){if(!t)throw new Error(e)}function E(t){return null==t?null:"function"==typeof t.trim?t.trim():t.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")}function z(t){t[Ng]=!0}function B(t){return t[Ng]}function R(t){function e(t,e){i?n.set(t,e):n.set(e,t)}var i=_(t);this.data={};var n=this;t instanceof R?t.each(e):t&&f(t,e)}function N(t){return new R(t)}function F(t,e){for(var i=new t.constructor(t.length+e.length),n=0;n<t.length;n++)i[n]=t[n];var r=t.length;for(n=0;n<e.length;n++)i[n+r]=e[n];return i}function V(){}function W(t,e){var i=new Vg(2);return null==t&&(t=0),null==e&&(e=0),i[0]=t,i[1]=e,i}function H(t,e){return t[0]=e[0],t[1]=e[1],t}function G(t){var e=new Vg(2);return e[0]=t[0],e[1]=t[1],e}function Z(t,e,i){return t[0]=e,t[1]=i,t}function X(t,e,i){return t[0]=e[0]+i[0],t[1]=e[1]+i[1],t}function Y(t,e,i,n){return t[0]=e[0]+i[0]*n,t[1]=e[1]+i[1]*n,t}function U(t,e,i){return t[0]=e[0]-i[0],t[1]=e[1]-i[1],t}function q(t){return Math.sqrt(j(t))}function j(t){return t[0]*t[0]+t[1]*t[1]}function K(t,e,i){return t[0]=e[0]*i[0],t[1]=e[1]*i[1],t}function $(t,e,i){return t[0]=e[0]/i[0],t[1]=e[1]/i[1],t}function Q(t,e){return t[0]*e[0]+t[1]*e[1]}function J(t,e,i){return t[0]=e[0]*i,t[1]=e[1]*i,t}function te(t,e){var i=q(e);return 0===i?(t[0]=0,t[1]=0):(t[0]=e[0]/i,t[1]=e[1]/i),t}function ee(t,e){return Math.sqrt((t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1]))}function ie(t,e){return(t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1])}function ne(t,e){return t[0]=-e[0],t[1]=-e[1],t}function re(t,e,i,n){return t[0]=e[0]+n*(i[0]-e[0]),t[1]=e[1]+n*(i[1]-e[1]),t}function ae(t,e,i){var n=e[0],r=e[1];return t[0]=i[0]*n+i[2]*r+i[4],t[1]=i[1]*n+i[3]*r+i[5],t}function oe(t,e,i){return t[0]=Math.min(e[0],i[0]),t[1]=Math.min(e[1],i[1]),t}function se(t,e,i){return t[0]=Math.max(e[0],i[0]),t[1]=Math.max(e[1],i[1]),t}function le(){this.on("mousedown",this._dragStart,this),this.on("mousemove",this._drag,this),this.on("mouseup",this._dragEnd,this)}function he(t,e){return{target:t,topTarget:e&&e.topTarget}}function ue(t,e){var i=t._$eventProcessor;return null!=e&&i&&i.normalizeQuery&&(e=i.normalizeQuery(e)),e}function ce(t,e,i,n,r,a){var o=t._$handlers;if("function"==typeof i&&(r=n,n=i,i=null),!n||!e)return t;i=ue(t,i),o[e]||(o[e]=[]);for(var s=0;s<o[e].length;s++)if(o[e][s].h===n)return t;var l={h:n,one:a,query:i,ctx:r||t,callAtLast:n.zrEventfulCallAtLast},h=o[e].length-1,u=o[e][h];return u&&u.callAtLast?o[e].splice(h,0,l):o[e].push(l),t}function de(t,e,i,n,r,a){var o=n+"-"+r,s=t.length;if(a.hasOwnProperty(o))return a[o];if(1===e){var l=Math.round(Math.log((1<<s)-1&~r)/qg);return t[i][l]}for(var h=n|1<<i,u=i+1;n&1<<u;)u++;for(var c=0,d=0,f=0;s>d;d++){var p=1<<d;p&r||(c+=(f%2?-1:1)*t[i][d]*de(t,e-1,u,h,r|p,a),f++)}return a[o]=c,c}function fe(t,e){var i=[[t[0],t[1],1,0,0,0,-e[0]*t[0],-e[0]*t[1]],[0,0,0,t[0],t[1],1,-e[1]*t[0],-e[1]*t[1]],[t[2],t[3],1,0,0,0,-e[2]*t[2],-e[2]*t[3]],[0,0,0,t[2],t[3],1,-e[3]*t[2],-e[3]*t[3]],[t[4],t[5],1,0,0,0,-e[4]*t[4],-e[4]*t[5]],[0,0,0,t[4],t[5],1,-e[5]*t[4],-e[5]*t[5]],[t[6],t[7],1,0,0,0,-e[6]*t[6],-e[6]*t[7]],[0,0,0,t[6],t[7],1,-e[7]*t[6],-e[7]*t[7]]],n={},r=de(i,8,0,0,0,n);if(0!==r){for(var a=[],o=0;8>o;o++)for(var s=0;8>s;s++)null==a[s]&&(a[s]=0),a[s]+=((o+s)%2?-1:1)*de(i,7,0===o?1:0,1<<o,1<<s,n)/r*e[o];return function(t,e,i){var n=e*a[6]+i*a[7]+1;t[0]=(e*a[0]+i*a[1]+a[2])/n,t[1]=(e*a[3]+i*a[4]+a[5])/n}}}function pe(t,e,i,n,r){return ge(Kg,e,n,r,!0)&&ge(t,i,Kg[0],Kg[1])}function ge(t,e,i,n,r){if(e.getBoundingClientRect&&Ag.domSupported&&!ye(e)){var a=e[jg]||(e[jg]={}),o=ve(e,a),s=me(o,a,r);if(s)return s(t,i,n),!0}return!1}function ve(t,e){var i=e.markers;if(i)return i;i=e.markers=[];for(var n=["left","right"],r=["top","bottom"],a=0;4>a;a++){var o=document.createElement("div"),s=o.style,l=a%2,h=(a>>1)%2;s.cssText=["position: absolute","visibility: hidden","padding: 0","margin: 0","border-width: 0","user-select: none","width:0","height:0",n[l]+":0",r[h]+":0",n[1-l]+":auto",r[1-h]+":auto",""].join("!important;"),t.appendChild(o),i.push(o)}return i}function me(t,e,i){for(var n=i?"invTrans":"trans",r=e[n],a=e.srcCoords,o=!0,s=[],l=[],h=0;4>h;h++){var u=t[h].getBoundingClientRect(),c=2*h,d=u.left,f=u.top;s.push(d,f),o=o&&a&&d===a[c]&&f===a[c+1],l.push(t[h].offsetLeft,t[h].offsetTop)}return o&&r?r:(e.srcCoords=s,e[n]=i?fe(l,s):fe(s,l))}function ye(t){return"CANVAS"===t.nodeName.toUpperCase()}function xe(t,e,i,n){return i=i||{},n||!Ag.canvasSupported?_e(t,e,i):Ag.browser.firefox&&null!=e.layerX&&e.layerX!==e.offsetX?(i.zrX=e.layerX,i.zrY=e.layerY):null!=e.offsetX?(i.zrX=e.offsetX,i.zrY=e.offsetY):_e(t,e,i),i}function _e(t,e,i){if(Ag.domSupported&&t.getBoundingClientRect){var n=e.clientX,r=e.clientY;if(ye(t)){var a=t.getBoundingClientRect();return i.zrX=n-a.left,void(i.zrY=r-a.top)}if(ge(Jg,t,n,r))return i.zrX=Jg[0],void(i.zrY=Jg[1])}i.zrX=i.zrY=0}function we(t){return t||window.event}function be(t,e,i){if(e=we(e),null!=e.zrX)return e;var n=e.type,r=n&&n.indexOf("touch")>=0;if(r){var a="touchend"!==n?e.targetTouches[0]:e.changedTouches[0];a&&xe(t,a,e,i)}else xe(t,e,e,i),e.zrDelta=e.wheelDelta?e.wheelDelta/120:-(e.detail||0)/3;var o=e.button;return null==e.which&&void 0!==o&&Qg.test(e.type)&&(e.which=1&o?1:2&o?3:4&o?2:0),e}function Me(t,e,i,n){$g?t.addEventListener(e,i,n):t.attachEvent("on"+e,i)}function Se(t,e,i,n){$g?t.removeEventListener(e,i,n):t.detachEvent("on"+e,i)}function Ae(t){return 2===t.which||3===t.which}function Ie(t){var e=t[1][0]-t[0][0],i=t[1][1]-t[0][1];return Math.sqrt(e*e+i*i)}function Te(t){return[(t[0][0]+t[1][0])/2,(t[0][1]+t[1][1])/2]}function Ce(t,e,i){return{type:t,event:i,target:e.target,topTarget:e.topTarget,cancelBubble:!1,offsetX:i.zrX,offsetY:i.zrY,gestureEvent:i.gestureEvent,pinchX:i.pinchX,pinchY:i.pinchY,pinchScale:i.pinchScale,wheelDelta:i.zrDelta,zrByTouch:i.zrByTouch,which:i.which,stop:De}}function De(){tv(this.event)}function ke(){}function Pe(t,e,i){if(t[t.rectHover?"rectContain":"contain"](e,i)){for(var n,r=t;r;){if(r.clipPath&&!r.clipPath.contain(e,i))return!1;r.silent&&(n=!0),r=r.parent}return n?nv:!0}return!1}function Le(t,e,i){var n=t.painter;return 0>e||e>n.getWidth()||0>i||i>n.getHeight()}function Oe(){var t=new ov(6);return Ee(t),t}function Ee(t){return t[0]=1,t[1]=0,t[2]=0,t[3]=1,t[4]=0,t[5]=0,t}function ze(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4],t[5]=e[5],t}function Be(t,e,i){var n=e[0]*i[0]+e[2]*i[1],r=e[1]*i[0]+e[3]*i[1],a=e[0]*i[2]+e[2]*i[3],o=e[1]*i[2]+e[3]*i[3],s=e[0]*i[4]+e[2]*i[5]+e[4],l=e[1]*i[4]+e[3]*i[5]+e[5];return t[0]=n,t[1]=r,t[2]=a,t[3]=o,t[4]=s,t[5]=l,t}function Re(t,e,i){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4]+i[0],t[5]=e[5]+i[1],t}function Ne(t,e,i){var n=e[0],r=e[2],a=e[4],o=e[1],s=e[3],l=e[5],h=Math.sin(i),u=Math.cos(i);return t[0]=n*u+o*h,t[1]=-n*h+o*u,t[2]=r*u+s*h,t[3]=-r*h+u*s,t[4]=u*a+h*l,t[5]=u*l-h*a,t}function Fe(t,e,i){var n=i[0],r=i[1];return t[0]=e[0]*n,t[1]=e[1]*r,t[2]=e[2]*n,t[3]=e[3]*r,t[4]=e[4]*n,t[5]=e[5]*r,t}function Ve(t,e){var i=e[0],n=e[2],r=e[4],a=e[1],o=e[3],s=e[5],l=i*o-a*n;return l?(l=1/l,t[0]=o*l,t[1]=-a*l,t[2]=-n*l,t[3]=i*l,t[4]=(n*s-o*r)*l,t[5]=(a*r-i*s)*l,t):null}function We(t){var e=Oe();return ze(e,t),e}function He(t){return t>hv||-hv>t}function Ge(t){this._target=t.target,this._life=t.life||1e3,this._delay=t.delay||0,this._initialized=!1,this.loop=null==t.loop?!1:t.loop,this.gap=t.gap||0,this.easing=t.easing||"Linear",this.onframe=t.onframe,this.ondestroy=t.ondestroy,this.onrestart=t.onrestart,this._pausedTime=0,this._paused=!1}function Ze(t){return t=Math.round(t),0>t?0:t>255?255:t}function Xe(t){return t=Math.round(t),0>t?0:t>360?360:t}function Ye(t){return 0>t?0:t>1?1:t}function Ue(t){return Ze(t.length&&"%"===t.charAt(t.length-1)?parseFloat(t)/100*255:parseInt(t,10))}function qe(t){return Ye(t.length&&"%"===t.charAt(t.length-1)?parseFloat(t)/100:parseFloat(t))}function je(t,e,i){return 0>i?i+=1:i>1&&(i-=1),1>6*i?t+(e-t)*i*6:1>2*i?e:2>3*i?t+(e-t)*(2/3-i)*6:t}function Ke(t,e,i){return t+(e-t)*i}function $e(t,e,i,n,r){return t[0]=e,t[1]=i,t[2]=n,t[3]=r,t}function Qe(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t}function Je(t,e){Mv&&Qe(Mv,e),Mv=bv.put(t,Mv||e.slice())}function ti(t,e){if(t){e=e||[];var i=bv.get(t);if(i)return Qe(e,i);t+="";var n=t.replace(/ /g,"").toLowerCase();if(n in wv)return Qe(e,wv[n]),Je(t,e),e;if("#"!==n.charAt(0)){var r=n.indexOf("("),a=n.indexOf(")");if(-1!==r&&a+1===n.length){var o=n.substr(0,r),s=n.substr(r+1,a-(r+1)).split(","),l=1;switch(o){case"rgba":if(4!==s.length)return void $e(e,0,0,0,1);l=qe(s.pop());case"rgb":return 3!==s.length?void $e(e,0,0,0,1):($e(e,Ue(s[0]),Ue(s[1]),Ue(s[2]),l),Je(t,e),e);case"hsla":return 4!==s.length?void $e(e,0,0,0,1):(s[3]=qe(s[3]),ei(s,e),Je(t,e),e);case"hsl":return 3!==s.length?void $e(e,0,0,0,1):(ei(s,e),Je(t,e),e);default:return}}$e(e,0,0,0,1)}else{if(4===n.length){var h=parseInt(n.substr(1),16);return h>=0&&4095>=h?($e(e,(3840&h)>>4|(3840&h)>>8,240&h|(240&h)>>4,15&h|(15&h)<<4,1),Je(t,e),e):void $e(e,0,0,0,1)}if(7===n.length){var h=parseInt(n.substr(1),16);return h>=0&&16777215>=h?($e(e,(16711680&h)>>16,(65280&h)>>8,255&h,1),Je(t,e),e):void $e(e,0,0,0,1)}}}}function ei(t,e){var i=(parseFloat(t[0])%360+360)%360/360,n=qe(t[1]),r=qe(t[2]),a=.5>=r?r*(n+1):r+n-r*n,o=2*r-a;return e=e||[],$e(e,Ze(255*je(o,a,i+1/3)),Ze(255*je(o,a,i)),Ze(255*je(o,a,i-1/3)),1),4===t.length&&(e[3]=t[3]),e}function ii(t){if(t){var e,i,n=t[0]/255,r=t[1]/255,a=t[2]/255,o=Math.min(n,r,a),s=Math.max(n,r,a),l=s-o,h=(s+o)/2;if(0===l)e=0,i=0;else{i=.5>h?l/(s+o):l/(2-s-o);var u=((s-n)/6+l/2)/l,c=((s-r)/6+l/2)/l,d=((s-a)/6+l/2)/l;n===s?e=d-c:r===s?e=1/3+u-d:a===s&&(e=2/3+c-u),0>e&&(e+=1),e>1&&(e-=1)}var f=[360*e,i,h];return null!=t[3]&&f.push(t[3]),f}}function ni(t,e){var i=ti(t);if(i){for(var n=0;3>n;n++)i[n]=0>e?i[n]*(1-e)|0:(255-i[n])*e+i[n]|0,i[n]>255?i[n]=255:t[n]<0&&(i[n]=0);return hi(i,4===i.length?"rgba":"rgb")}}function ri(t){var e=ti(t);return e?((1<<24)+(e[0]<<16)+(e[1]<<8)+ +e[2]).toString(16).slice(1):void 0}function ai(t,e,i){if(e&&e.length&&t>=0&&1>=t){i=i||[];var n=t*(e.length-1),r=Math.floor(n),a=Math.ceil(n),o=e[r],s=e[a],l=n-r;return i[0]=Ze(Ke(o[0],s[0],l)),i[1]=Ze(Ke(o[1],s[1],l)),i[2]=Ze(Ke(o[2],s[2],l)),i[3]=Ye(Ke(o[3],s[3],l)),i}}function oi(t,e,i){if(e&&e.length&&t>=0&&1>=t){var n=t*(e.length-1),r=Math.floor(n),a=Math.ceil(n),o=ti(e[r]),s=ti(e[a]),l=n-r,h=hi([Ze(Ke(o[0],s[0],l)),Ze(Ke(o[1],s[1],l)),Ze(Ke(o[2],s[2],l)),Ye(Ke(o[3],s[3],l))],"rgba");return i?{color:h,leftIndex:r,rightIndex:a,value:n}:h}}function si(t,e,i,n){return t=ti(t),t?(t=ii(t),null!=e&&(t[0]=Xe(e)),null!=i&&(t[1]=qe(i)),null!=n&&(t[2]=qe(n)),hi(ei(t),"rgba")):void 0}function li(t,e){return t=ti(t),t&&null!=e?(t[3]=Ye(e),hi(t,"rgba")):void 0}function hi(t,e){if(t&&t.length){var i=t[0]+","+t[1]+","+t[2];return("rgba"===e||"hsva"===e||"hsla"===e)&&(i+=","+t[3]),e+"("+i+")"}}function ui(t,e){return t[e]}function ci(t,e,i){t[e]=i}function di(t,e,i){return(e-t)*i+t}function fi(t,e,i){return i>.5?e:t}function pi(t,e,i,n,r){var a=t.length;if(1===r)for(var o=0;a>o;o++)n[o]=di(t[o],e[o],i);else for(var s=a&&t[0].length,o=0;a>o;o++)for(var l=0;s>l;l++)n[o][l]=di(t[o][l],e[o][l],i)}function gi(t,e,i){var n=t.length,r=e.length;if(n!==r){var a=n>r;if(a)t.length=r;else for(var o=n;r>o;o++)t.push(1===i?e[o]:Tv.call(e[o]))}for(var s=t[0]&&t[0].length,o=0;o<t.length;o++)if(1===i)isNaN(t[o])&&(t[o]=e[o]);else for(var l=0;s>l;l++)isNaN(t[o][l])&&(t[o][l]=e[o][l])}function vi(t,e,i){if(t===e)return!0;var n=t.length;if(n!==e.length)return!1;if(1===i){for(var r=0;n>r;r++)if(t[r]!==e[r])return!1}else for(var a=t[0].length,r=0;n>r;r++)for(var o=0;a>o;o++)if(t[r][o]!==e[r][o])return!1;return!0}function mi(t,e,i,n,r,a,o,s,l){var h=t.length;if(1===l)for(var u=0;h>u;u++)s[u]=yi(t[u],e[u],i[u],n[u],r,a,o);else for(var c=t[0].length,u=0;h>u;u++)for(var d=0;c>d;d++)s[u][d]=yi(t[u][d],e[u][d],i[u][d],n[u][d],r,a,o)}function yi(t,e,i,n,r,a,o){var s=.5*(i-t),l=.5*(n-e);return(2*(e-i)+s+l)*o+(-3*(e-i)-2*s-l)*a+s*r+e}function xi(t){if(d(t)){var e=t.length;if(d(t[0])){for(var i=[],n=0;e>n;n++)i.push(Tv.call(t[n]));return i}return Tv.call(t)}return t}function _i(t){return t[0]=Math.floor(t[0]),t[1]=Math.floor(t[1]),t[2]=Math.floor(t[2]),"rgba("+t.join(",")+")"}function wi(t){var e=t[t.length-1].value;return d(e&&e[0])?2:1}function bi(t,e,i,n,r,a){var o=t._getter,s=t._setter,l="spline"===e,h=n.length;if(h){var u,c=n[0].value,f=d(c),p=!1,g=!1,v=f?wi(n):0;n.sort(function(t,e){return t.time-e.time}),u=n[h-1].time;for(var m=[],y=[],x=n[0].value,_=!0,w=0;h>w;w++){m.push(n[w].time/u);var b=n[w].value;if(f&&vi(b,x,v)||!f&&b===x||(_=!1),x=b,"string"==typeof b){var M=ti(b);M?(b=M,p=!0):g=!0}y.push(b)}if(a||!_){for(var S=y[h-1],w=0;h-1>w;w++)f?gi(y[w],S,v):!isNaN(y[w])||isNaN(S)||g||p||(y[w]=S);f&&gi(o(t._target,r),S,v);var A,I,T,C,D,k,P=0,L=0;if(p)var O=[0,0,0,0];var E=function(t,e){var i;if(0>e)i=0;else if(L>e){for(A=Math.min(P+1,h-1),i=A;i>=0&&!(m[i]<=e);i--);i=Math.min(i,h-2)}else{for(i=P;h>i&&!(m[i]>e);i++);i=Math.min(i-1,h-2)}P=i,L=e;var n=m[i+1]-m[i];if(0!==n)if(I=(e-m[i])/n,l)if(C=y[i],T=y[0===i?i:i-1],D=y[i>h-2?h-1:i+1],k=y[i>h-3?h-1:i+2],f)mi(T,C,D,k,I,I*I,I*I*I,o(t,r),v);else{var a;if(p)a=mi(T,C,D,k,I,I*I,I*I*I,O,1),a=_i(O);else{if(g)return fi(C,D,I);a=yi(T,C,D,k,I,I*I,I*I*I)}s(t,r,a)}else if(f)pi(y[i],y[i+1],I,o(t,r),v);else{var a;if(p)pi(y[i],y[i+1],I,O,1),a=_i(O);else{if(g)return fi(y[i],y[i+1],I);a=di(y[i],y[i+1],I)}s(t,r,a)}},z=new Ge({target:t._target,life:u,loop:t._loop,delay:t._delay,onframe:E,ondestroy:i});return e&&"spline"!==e&&(z.easing=e),z}}}function Mi(t,e,i,n,r,a,o,s){function l(){u--,u||a&&a()}b(n)?(a=r,r=n,n=0):w(r)?(a=r,r="linear",n=0):w(n)?(a=n,n=0):w(i)?(a=i,i=500):i||(i=500),t.stopAnimation(),Si(t,"",t,e,i,n,s);var h=t.animators.slice(),u=h.length;u||a&&a();for(var c=0;c<h.length;c++)h[c].done(l).start(r,o)}function Si(t,e,i,n,r,a,o){var s={},l=0;for(var h in n)n.hasOwnProperty(h)&&(null!=i[h]?M(n[h])&&!d(n[h])?Si(t,e?e+"."+h:h,i[h],n[h],r,a,o):(o?(s[h]=i[h],Ai(t,e,h,n[h])):s[h]=n[h],l++):null==n[h]||o||Ai(t,e,h,n[h]));l>0&&t.animate(e,!1).when(null==r?500:r,s).delay(a||0)}function Ai(t,e,i,n){if(e){var r={};r[e]={},r[e][i]=n,t.attr(r)}else t.attr(i,n)}function Ii(t,e,i,n){0>i&&(t+=i,i=-i),0>n&&(e+=n,n=-n),this.x=t,this.y=e,this.width=i,this.height=n}function Ti(t){for(var e=0;t>=Vv;)e|=1&t,t>>=1;return t+e}function Ci(t,e,i,n){var r=e+1;if(r===i)return 1;if(n(t[r++],t[e])<0){for(;i>r&&n(t[r],t[r-1])<0;)r++;Di(t,e,r)}else for(;i>r&&n(t[r],t[r-1])>=0;)r++;return r-e}function Di(t,e,i){for(i--;i>e;){var n=t[e];t[e++]=t[i],t[i--]=n}}function ki(t,e,i,n,r){for(n===e&&n++;i>n;n++){for(var a,o=t[n],s=e,l=n;l>s;)a=s+l>>>1,r(o,t[a])<0?l=a:s=a+1;var h=n-s;switch(h){case 3:t[s+3]=t[s+2];case 2:t[s+2]=t[s+1];case 1:t[s+1]=t[s];break;default:for(;h>0;)t[s+h]=t[s+h-1],h--}t[s]=o}}function Pi(t,e,i,n,r,a){var o=0,s=0,l=1;if(a(t,e[i+r])>0){for(s=n-r;s>l&&a(t,e[i+r+l])>0;)o=l,l=(l<<1)+1,0>=l&&(l=s);l>s&&(l=s),o+=r,l+=r}else{for(s=r+1;s>l&&a(t,e[i+r-l])<=0;)o=l,l=(l<<1)+1,0>=l&&(l=s);l>s&&(l=s);var h=o;o=r-l,l=r-h}for(o++;l>o;){var u=o+(l-o>>>1);a(t,e[i+u])>0?o=u+1:l=u}return l}function Li(t,e,i,n,r,a){var o=0,s=0,l=1;if(a(t,e[i+r])<0){for(s=r+1;s>l&&a(t,e[i+r-l])<0;)o=l,l=(l<<1)+1,0>=l&&(l=s);l>s&&(l=s);var h=o;o=r-l,l=r-h}else{for(s=n-r;s>l&&a(t,e[i+r+l])>=0;)o=l,l=(l<<1)+1,0>=l&&(l=s);l>s&&(l=s),o+=r,l+=r}for(o++;l>o;){var u=o+(l-o>>>1);a(t,e[i+u])<0?l=u:o=u+1}return l}function Oi(t,e){function i(t,e){l[c]=t,h[c]=e,c+=1}function n(){for(;c>1;){var t=c-2;if(t>=1&&h[t-1]<=h[t]+h[t+1]||t>=2&&h[t-2]<=h[t]+h[t-1])h[t-1]<h[t+1]&&t--;else if(h[t]>h[t+1])break;a(t)}}function r(){for(;c>1;){var t=c-2;t>0&&h[t-1]<h[t+1]&&t--,a(t)}}function a(i){var n=l[i],r=h[i],a=l[i+1],u=h[i+1];h[i]=r+u,i===c-3&&(l[i+1]=l[i+2],h[i+1]=h[i+2]),c--;var d=Li(t[a],t,n,r,0,e);n+=d,r-=d,0!==r&&(u=Pi(t[n+r-1],t,a,u,u-1,e),0!==u&&(u>=r?o(n,r,a,u):s(n,r,a,u)))}function o(i,n,r,a){var o=0;for(o=0;n>o;o++)d[o]=t[i+o];var s=0,l=r,h=i;if(t[h++]=t[l++],0!==--a){if(1===n){for(o=0;a>o;o++)t[h+o]=t[l+o];return void(t[h+a]=d[s])}for(var c,f,p,g=u;;){c=0,f=0,p=!1;do if(e(t[l],d[s])<0){if(t[h++]=t[l++],f++,c=0,0===--a){p=!0;break}}else if(t[h++]=d[s++],c++,f=0,1===--n){p=!0;break}while(g>(c|f));if(p)break;do{if(c=Li(t[l],d,s,n,0,e),0!==c){for(o=0;c>o;o++)t[h+o]=d[s+o];if(h+=c,s+=c,n-=c,1>=n){p=!0;break}}if(t[h++]=t[l++],0===--a){p=!0;break}if(f=Pi(d[s],t,l,a,0,e),0!==f){for(o=0;f>o;o++)t[h+o]=t[l+o];if(h+=f,l+=f,a-=f,0===a){p=!0;break}}if(t[h++]=d[s++],1===--n){p=!0;break}g--}while(c>=Wv||f>=Wv);if(p)break;0>g&&(g=0),g+=2}if(u=g,1>u&&(u=1),1===n){for(o=0;a>o;o++)t[h+o]=t[l+o];t[h+a]=d[s]}else{if(0===n)throw new Error;for(o=0;n>o;o++)t[h+o]=d[s+o]}}else for(o=0;n>o;o++)t[h+o]=d[s+o]}function s(i,n,r,a){var o=0;for(o=0;a>o;o++)d[o]=t[r+o];var s=i+n-1,l=a-1,h=r+a-1,c=0,f=0;if(t[h--]=t[s--],0!==--n){if(1===a){for(h-=n,s-=n,f=h+1,c=s+1,o=n-1;o>=0;o--)t[f+o]=t[c+o];return void(t[h]=d[l])}for(var p=u;;){var g=0,v=0,m=!1;do if(e(d[l],t[s])<0){if(t[h--]=t[s--],g++,v=0,0===--n){m=!0;break}}else if(t[h--]=d[l--],v++,g=0,1===--a){m=!0;break}while(p>(g|v));if(m)break;do{if(g=n-Li(d[l],t,i,n,n-1,e),0!==g){for(h-=g,s-=g,n-=g,f=h+1,c=s+1,o=g-1;o>=0;o--)t[f+o]=t[c+o];if(0===n){m=!0;break}}if(t[h--]=d[l--],1===--a){m=!0;break}if(v=a-Pi(t[s],d,0,a,a-1,e),0!==v){for(h-=v,l-=v,a-=v,f=h+1,c=l+1,o=0;v>o;o++)t[f+o]=d[c+o];if(1>=a){m=!0;break}}if(t[h--]=t[s--],0===--n){m=!0;break}p--}while(g>=Wv||v>=Wv);if(m)break;0>p&&(p=0),p+=2}if(u=p,1>u&&(u=1),1===a){for(h-=n,s-=n,f=h+1,c=s+1,o=n-1;o>=0;o--)t[f+o]=t[c+o];t[h]=d[l]}else{if(0===a)throw new Error;for(c=h-(a-1),o=0;a>o;o++)t[c+o]=d[o]}}else for(c=h-(a-1),o=0;a>o;o++)t[c+o]=d[o]}var l,h,u=Wv,c=0,d=[];l=[],h=[],this.mergeRuns=n,this.forceMergeRuns=r,this.pushRun=i}function Ei(t,e,i,n){i||(i=0),n||(n=t.length);var r=n-i;if(!(2>r)){var a=0;if(Vv>r)return a=Ci(t,i,n,e),void ki(t,i,n,i+a,e);var o=new Oi(t,e),s=Ti(r);do{if(a=Ci(t,i,n,e),s>a){var l=r;l>s&&(l=s),ki(t,i,i+l,i+a,e),a=l}o.pushRun(i,a),o.mergeRuns(),r-=a,i+=a}while(0!==r);o.forceMergeRuns()}}function zi(t,e){return t.zlevel===e.zlevel?t.z===e.z?t.z2-e.z2:t.z-e.z:t.zlevel-e.zlevel}function Bi(t,e,i){var n=null==e.x?0:e.x,r=null==e.x2?1:e.x2,a=null==e.y?0:e.y,o=null==e.y2?0:e.y2;e.global||(n=n*i.width+i.x,r=r*i.width+i.x,a=a*i.height+i.y,o=o*i.height+i.y),n=isNaN(n)?0:n,r=isNaN(r)?1:r,a=isNaN(a)?0:a,o=isNaN(o)?0:o;var s=t.createLinearGradient(n,a,r,o);return s}function Ri(t,e,i){var n=i.width,r=i.height,a=Math.min(n,r),o=null==e.x?.5:e.x,s=null==e.y?.5:e.y,l=null==e.r?.5:e.r;e.global||(o=o*n+i.x,s=s*r+i.y,l*=a);var h=t.createRadialGradient(o,s,0,o,s,l);return h}function Ni(){return!1}function Fi(t,e,i){var n=Bg(),r=e.getWidth(),a=e.getHeight(),o=n.style;return o&&(o.position="absolute",o.left=0,o.top=0,o.width=r+"px",o.height=a+"px",n.setAttribute("data-zr-dom-id",t)),n.width=r*i,n.height=a*i,n}function Vi(t){if("string"==typeof t){var e=em.get(t);return e&&e.image}return t}function Wi(t,e,i,n,r){if(t){if("string"==typeof t){if(e&&e.__zrImageSrc===t||!i)return e;var a=em.get(t),o={hostEl:i,cb:n,cbPayload:r};return a?(e=a.image,!Gi(e)&&a.pending.push(o)):(e=new Image,e.onload=e.onerror=Hi,em.put(t,e.__cachedImgObj={image:e,pending:[o]}),e.src=e.__zrImageSrc=t),e}return t}return e}function Hi(){var t=this.__cachedImgObj;this.onload=this.onerror=this.__cachedImgObj=null;for(var e=0;e<t.pending.length;e++){var i=t.pending[e],n=i.cb;n&&n(this,i.cbPayload),i.hostEl.dirty()}t.pending.length=0}function Gi(t){return t&&t.width&&t.height}function Zi(t,e){e=e||om;var i=t+":"+e;if(im[i])return im[i];for(var n=(t+"").split("\n"),r=0,a=0,o=n.length;o>a;a++)r=Math.max(nn(n[a],e).width,r);return nm>rm&&(nm=0,im={}),nm++,im[i]=r,r}function Xi(t,e,i,n,r,a,o,s){return o?Ui(t,e,i,n,r,a,o,s):Yi(t,e,i,n,r,a,s)}function Yi(t,e,i,n,r,a,o){var s=rn(t,e,r,a,o),l=Zi(t,e);r&&(l+=r[1]+r[3]);var h=s.outerHeight,u=qi(0,l,i),c=ji(0,h,n),d=new Ii(u,c,l,h);return d.lineHeight=s.lineHeight,d}function Ui(t,e,i,n,r,a,o,s){var l=an(t,{rich:o,truncate:s,font:e,textAlign:i,textPadding:r,textLineHeight:a}),h=l.outerWidth,u=l.outerHeight,c=qi(0,h,i),d=ji(0,u,n);return new Ii(c,d,h,u)}function qi(t,e,i){return"right"===i?t-=e:"center"===i&&(t-=e/2),t}function ji(t,e,i){return"middle"===i?t-=e/2:"bottom"===i&&(t-=e),t}function Ki(t,e,i){var n=e.textPosition,r=e.textDistance,a=i.x,o=i.y;r=r||0;var s=i.height,l=i.width,h=s/2,u="left",c="top";switch(n){case"left":a-=r,o+=h,u="right",c="middle";break;case"right":a+=r+l,o+=h,c="middle";break;case"top":a+=l/2,o-=r,u="center",c="bottom";break;case"bottom":a+=l/2,o+=s+r,u="center";break;case"inside":a+=l/2,o+=h,u="center",c="middle";break;case"insideLeft":a+=r,o+=h,c="middle";break;case"insideRight":a+=l-r,o+=h,u="right",c="middle";break;case"insideTop":a+=l/2,o+=r,u="center";break;case"insideBottom":a+=l/2,o+=s-r,u="center",c="bottom";break;case"insideTopLeft":a+=r,o+=r;break;case"insideTopRight":a+=l-r,o+=r,u="right";break;case"insideBottomLeft":a+=r,o+=s-r,c="bottom";break;case"insideBottomRight":a+=l-r,o+=s-r,u="right",c="bottom"}return t=t||{},t.x=a,t.y=o,t.textAlign=u,t.textVerticalAlign=c,t}function $i(t,e,i,n,r){if(!e)return"";var a=(t+"").split("\n");r=Qi(e,i,n,r);for(var o=0,s=a.length;s>o;o++)a[o]=Ji(a[o],r);return a.join("\n")}function Qi(t,e,i,n){n=o({},n),n.font=e;var i=D(i,"...");n.maxIterations=D(n.maxIterations,2);var r=n.minChar=D(n.minChar,0);n.cnCharWidth=Zi("国",e);var a=n.ascCharWidth=Zi("a",e);n.placeholder=D(n.placeholder,"");for(var s=t=Math.max(0,t-1),l=0;r>l&&s>=a;l++)s-=a;var h=Zi(i,e);return h>s&&(i="",h=0),s=t-h,n.ellipsis=i,n.ellipsisWidth=h,n.contentWidth=s,n.containerWidth=t,n}function Ji(t,e){var i=e.containerWidth,n=e.font,r=e.contentWidth;if(!i)return"";var a=Zi(t,n);if(i>=a)return t;for(var o=0;;o++){if(r>=a||o>=e.maxIterations){t+=e.ellipsis;break}var s=0===o?tn(t,r,e.ascCharWidth,e.cnCharWidth):a>0?Math.floor(t.length*r/a):0;t=t.substr(0,s),a=Zi(t,n)}return""===t&&(t=e.placeholder),t}function tn(t,e,i,n){for(var r=0,a=0,o=t.length;o>a&&e>r;a++){var s=t.charCodeAt(a);r+=s>=0&&127>=s?i:n}return a}function en(t){return Zi("国",t)}function nn(t,e){return sm.measureText(t,e)}function rn(t,e,i,n,r){null!=t&&(t+="");var a=D(n,en(e)),o=t?t.split("\n"):[],s=o.length*a,l=s,h=!0;if(i&&(l+=i[0]+i[2]),t&&r){h=!1;var u=r.outerHeight,c=r.outerWidth;if(null!=u&&l>u)t="",o=[];else if(null!=c)for(var d=Qi(c-(i?i[1]+i[3]:0),e,r.ellipsis,{minChar:r.minChar,placeholder:r.placeholder}),f=0,p=o.length;p>f;f++)o[f]=Ji(o[f],d)}return{lines:o,height:s,outerHeight:l,lineHeight:a,canCacheByTextString:h}}function an(t,e){var i={lines:[],width:0,height:0};if(null!=t&&(t+=""),!t)return i;for(var n,r=am.lastIndex=0;null!=(n=am.exec(t));){var a=n.index;a>r&&on(i,t.substring(r,a)),on(i,n[2],n[1]),r=am.lastIndex}r<t.length&&on(i,t.substring(r,t.length));var o=i.lines,s=0,l=0,h=[],u=e.textPadding,c=e.truncate,d=c&&c.outerWidth,f=c&&c.outerHeight;u&&(null!=d&&(d-=u[1]+u[3]),null!=f&&(f-=u[0]+u[2]));for(var p=0;p<o.length;p++){for(var g=o[p],v=0,m=0,y=0;y<g.tokens.length;y++){var x=g.tokens[y],_=x.styleName&&e.rich[x.styleName]||{},w=x.textPadding=_.textPadding,b=x.font=_.font||e.font,M=x.textHeight=D(_.textHeight,en(b));if(w&&(M+=w[0]+w[2]),x.height=M,x.lineHeight=k(_.textLineHeight,e.textLineHeight,M),x.textAlign=_&&_.textAlign||e.textAlign,x.textVerticalAlign=_&&_.textVerticalAlign||"middle",null!=f&&s+x.lineHeight>f)return{lines:[],width:0,height:0};x.textWidth=Zi(x.text,b);var S=_.textWidth,A=null==S||"auto"===S;if("string"==typeof S&&"%"===S.charAt(S.length-1))x.percentWidth=S,h.push(x),S=0;else{if(A){S=x.textWidth;var I=_.textBackgroundColor,T=I&&I.image;T&&(T=Vi(T),Gi(T)&&(S=Math.max(S,T.width*M/T.height)))}var C=w?w[1]+w[3]:0;S+=C;var P=null!=d?d-m:null;null!=P&&S>P&&(!A||C>P?(x.text="",x.textWidth=S=0):(x.text=$i(x.text,P-C,b,c.ellipsis,{minChar:c.minChar}),x.textWidth=Zi(x.text,b),S=x.textWidth+C))}m+=x.width=S,_&&(v=Math.max(v,x.lineHeight))}g.width=m,g.lineHeight=v,s+=v,l=Math.max(l,m)}i.outerWidth=i.width=D(e.textWidth,l),i.outerHeight=i.height=D(e.textHeight,s),u&&(i.outerWidth+=u[1]+u[3],i.outerHeight+=u[0]+u[2]);for(var p=0;p<h.length;p++){var x=h[p],L=x.percentWidth;x.width=parseInt(L,10)/100*l}return i}function on(t,e,i){for(var n=""===e,r=e.split("\n"),a=t.lines,o=0;o<r.length;o++){var s=r[o],l={styleName:i,text:s,isLineHolder:!s&&!n};if(o)a.push({tokens:[l]});else{var h=(a[a.length-1]||(a[0]={tokens:[]})).tokens,u=h.length;1===u&&h[0].isLineHolder?h[0]=l:(s||!u||n)&&h.push(l)}}}function sn(t){var e=(t.fontSize||t.fontFamily)&&[t.fontStyle,t.fontWeight,(t.fontSize||12)+"px",t.fontFamily||"sans-serif"].join(" ");return e&&E(e)||t.textFont||t.font}function ln(t,e){var i,n,r,a,o=e.x,s=e.y,l=e.width,h=e.height,u=e.r;0>l&&(o+=l,l=-l),0>h&&(s+=h,h=-h),"number"==typeof u?i=n=r=a=u:u instanceof Array?1===u.length?i=n=r=a=u[0]:2===u.length?(i=r=u[0],n=a=u[1]):3===u.length?(i=u[0],n=a=u[1],r=u[2]):(i=u[0],n=u[1],r=u[2],a=u[3]):i=n=r=a=0;var c;i+n>l&&(c=i+n,i*=l/c,n*=l/c),r+a>l&&(c=r+a,r*=l/c,a*=l/c),n+r>h&&(c=n+r,n*=h/c,r*=h/c),i+a>h&&(c=i+a,i*=h/c,a*=h/c),t.moveTo(o+i,s),t.lineTo(o+l-n,s),0!==n&&t.arc(o+l-n,s+n,n,-Math.PI/2,0),t.lineTo(o+l,s+h-r),0!==r&&t.arc(o+l-r,s+h-r,r,0,Math.PI/2),t.lineTo(o+a,s+h),0!==a&&t.arc(o+a,s+h-a,a,Math.PI/2,Math.PI),t.lineTo(o,s+i),0!==i&&t.arc(o+i,s+i,i,Math.PI,1.5*Math.PI)}function hn(t){return un(t),f(t.rich,un),t}function un(t){if(t){t.font=sn(t);var e=t.textAlign;"middle"===e&&(e="center"),t.textAlign=null==e||hm[e]?e:"left";var i=t.textVerticalAlign||t.textBaseline;"center"===i&&(i="middle"),t.textVerticalAlign=null==i||um[i]?i:"top";var n=t.textPadding;n&&(t.textPadding=L(t.textPadding))}}function cn(t,e,i,n,r,a){n.rich?fn(t,e,i,n,r,a):dn(t,e,i,n,r,a)}function dn(t,e,i,n,r,a){var o,s=mn(n),l=!1,h=e.__attrCachedBy===Xv.PLAIN_TEXT;a!==Yv?(a&&(o=a.style,l=!s&&h&&o),e.__attrCachedBy=s?Xv.NONE:Xv.PLAIN_TEXT):h&&(e.__attrCachedBy=Xv.NONE);var u=n.font||lm;l&&u===(o.font||lm)||(e.font=u);var c=t.__computedFont;t.__styleFont!==u&&(t.__styleFont=u,c=t.__computedFont=e.font);var d=n.textPadding,f=n.textLineHeight,p=t.__textCotentBlock;(!p||t.__dirtyText)&&(p=t.__textCotentBlock=rn(i,c,d,f,n.truncate));var g=p.outerHeight,v=p.lines,m=p.lineHeight,y=_n(fm,t,n,r),x=y.baseX,_=y.baseY,w=y.textAlign||"left",b=y.textVerticalAlign;gn(e,n,r,x,_);var M=ji(_,g,b),S=x,A=M;if(s||d){var I=Zi(i,c),T=I;d&&(T+=d[1]+d[3]);var C=qi(x,T,w);s&&yn(t,e,n,C,M,T,g),d&&(S=An(x,w,d),A+=d[0])}e.textAlign=w,e.textBaseline="middle",e.globalAlpha=n.opacity||1;for(var D=0;D<cm.length;D++){var k=cm[D],P=k[0],L=k[1],O=n[P];l&&O===o[P]||(e[L]=Zv(e,L,O||k[2]))}A+=m/2;var E=n.textStrokeWidth,z=l?o.textStrokeWidth:null,B=!l||E!==z,R=!l||B||n.textStroke!==o.textStroke,N=bn(n.textStroke,E),F=Mn(n.textFill);if(N&&(B&&(e.lineWidth=E),R&&(e.strokeStyle=N)),F&&(l&&n.textFill===o.textFill||(e.fillStyle=F)),1===v.length)N&&e.strokeText(v[0],S,A),F&&e.fillText(v[0],S,A);else for(var D=0;D<v.length;D++)N&&e.strokeText(v[D],S,A),F&&e.fillText(v[D],S,A),A+=m}function fn(t,e,i,n,r,a){a!==Yv&&(e.__attrCachedBy=Xv.NONE);var o=t.__textCotentBlock;(!o||t.__dirtyText)&&(o=t.__textCotentBlock=an(i,n)),pn(t,e,o,n,r)}function pn(t,e,i,n,r){var a=i.width,o=i.outerWidth,s=i.outerHeight,l=n.textPadding,h=_n(fm,t,n,r),u=h.baseX,c=h.baseY,d=h.textAlign,f=h.textVerticalAlign;gn(e,n,r,u,c);var p=qi(u,o,d),g=ji(c,s,f),v=p,m=g;l&&(v+=l[3],m+=l[0]);var y=v+a;mn(n)&&yn(t,e,n,p,g,o,s);for(var x=0;x<i.lines.length;x++){for(var _,w=i.lines[x],b=w.tokens,M=b.length,S=w.lineHeight,A=w.width,I=0,T=v,C=y,D=M-1;M>I&&(_=b[I],!_.textAlign||"left"===_.textAlign);)vn(t,e,_,n,S,m,T,"left"),A-=_.width,T+=_.width,I++;for(;D>=0&&(_=b[D],"right"===_.textAlign);)vn(t,e,_,n,S,m,C,"right"),A-=_.width,C-=_.width,D--;for(T+=(a-(T-v)-(y-C)-A)/2;D>=I;)_=b[I],vn(t,e,_,n,S,m,T+_.width/2,"center"),T+=_.width,I++;m+=S}}function gn(t,e,i,n,r){if(i&&e.textRotation){var a=e.textOrigin;"center"===a?(n=i.width/2+i.x,r=i.height/2+i.y):a&&(n=a[0]+i.x,r=a[1]+i.y),t.translate(n,r),t.rotate(-e.textRotation),t.translate(-n,-r)}}function vn(t,e,i,n,r,a,o,s){var l=n.rich[i.styleName]||{};
l.text=i.text;var h=i.textVerticalAlign,u=a+r/2;"top"===h?u=a+i.height/2:"bottom"===h&&(u=a+r-i.height/2),!i.isLineHolder&&mn(l)&&yn(t,e,l,"right"===s?o-i.width:"center"===s?o-i.width/2:o,u-i.height/2,i.width,i.height);var c=i.textPadding;c&&(o=An(o,s,c),u-=i.height/2-c[2]-i.textHeight/2),wn(e,"shadowBlur",k(l.textShadowBlur,n.textShadowBlur,0)),wn(e,"shadowColor",l.textShadowColor||n.textShadowColor||"transparent"),wn(e,"shadowOffsetX",k(l.textShadowOffsetX,n.textShadowOffsetX,0)),wn(e,"shadowOffsetY",k(l.textShadowOffsetY,n.textShadowOffsetY,0)),wn(e,"textAlign",s),wn(e,"textBaseline","middle"),wn(e,"font",i.font||lm);var d=bn(l.textStroke||n.textStroke,p),f=Mn(l.textFill||n.textFill),p=D(l.textStrokeWidth,n.textStrokeWidth);d&&(wn(e,"lineWidth",p),wn(e,"strokeStyle",d),e.strokeText(i.text,o,u)),f&&(wn(e,"fillStyle",f),e.fillText(i.text,o,u))}function mn(t){return!!(t.textBackgroundColor||t.textBorderWidth&&t.textBorderColor)}function yn(t,e,i,n,r,a,o){var s=i.textBackgroundColor,l=i.textBorderWidth,h=i.textBorderColor,u=b(s);if(wn(e,"shadowBlur",i.textBoxShadowBlur||0),wn(e,"shadowColor",i.textBoxShadowColor||"transparent"),wn(e,"shadowOffsetX",i.textBoxShadowOffsetX||0),wn(e,"shadowOffsetY",i.textBoxShadowOffsetY||0),u||l&&h){e.beginPath();var c=i.textBorderRadius;c?ln(e,{x:n,y:r,width:a,height:o,r:c}):e.rect(n,r,a,o),e.closePath()}if(u)if(wn(e,"fillStyle",s),null!=i.fillOpacity){var d=e.globalAlpha;e.globalAlpha=i.fillOpacity*i.opacity,e.fill(),e.globalAlpha=d}else e.fill();else if(M(s)){var f=s.image;f=Wi(f,null,t,xn,s),f&&Gi(f)&&e.drawImage(f,n,r,a,o)}if(l&&h)if(wn(e,"lineWidth",l),wn(e,"strokeStyle",h),null!=i.strokeOpacity){var d=e.globalAlpha;e.globalAlpha=i.strokeOpacity*i.opacity,e.stroke(),e.globalAlpha=d}else e.stroke()}function xn(t,e){e.image=t}function _n(t,e,i,n){var r=i.x||0,a=i.y||0,o=i.textAlign,s=i.textVerticalAlign;if(n){var l=i.textPosition;if(l instanceof Array)r=n.x+Sn(l[0],n.width),a=n.y+Sn(l[1],n.height);else{var h=e&&e.calculateTextPosition?e.calculateTextPosition(dm,i,n):Ki(dm,i,n);r=h.x,a=h.y,o=o||h.textAlign,s=s||h.textVerticalAlign}var u=i.textOffset;u&&(r+=u[0],a+=u[1])}return t=t||{},t.baseX=r,t.baseY=a,t.textAlign=o,t.textVerticalAlign=s,t}function wn(t,e,i){return t[e]=Zv(t,e,i),t[e]}function bn(t,e){return null==t||0>=e||"transparent"===t||"none"===t?null:t.image||t.colorStops?"#000":t}function Mn(t){return null==t||"none"===t?null:t.image||t.colorStops?"#000":t}function Sn(t,e){return"string"==typeof t?t.lastIndexOf("%")>=0?parseFloat(t)/100*e:parseFloat(t):t}function An(t,e,i){return"right"===e?t-i[1]:"center"===e?t+i[3]/2-i[1]/2:t+i[3]}function In(t,e){return null!=t&&(t||e.textBackgroundColor||e.textBorderWidth&&e.textBorderColor||e.textPadding)}function Tn(t){t=t||{},zv.call(this,t);for(var e in t)t.hasOwnProperty(e)&&"style"!==e&&(this[e]=t[e]);this.style=new qv(t.style,this),this._rect=null,this.__clipPaths=null}function Cn(t){Tn.call(this,t)}function Dn(t){return parseInt(t,10)}function kn(t){return t?t.__builtin__?!0:"function"!=typeof t.resize||"function"!=typeof t.refresh?!1:!0:!1}function Pn(t,e,i){return _m.copy(t.getBoundingRect()),t.transform&&_m.applyTransform(t.transform),wm.width=e,wm.height=i,!_m.intersect(wm)}function Ln(t,e){if(t===e)return!1;if(!t||!e||t.length!==e.length)return!0;for(var i=0;i<t.length;i++)if(t[i]!==e[i])return!0;return!1}function On(t,e){for(var i=0;i<t.length;i++){var n=t[i];n.setTransform(e),e.beginPath(),n.buildPath(e,n.shape),e.clip(),n.restoreTransform(e)}}function En(t,e){var i=document.createElement("div");return i.style.cssText=["position:relative","width:"+t+"px","height:"+e+"px","padding:0","margin:0","border-width:0"].join(";")+";",i}function zn(t){return"mousewheel"===t&&Ag.browser.firefox?"DOMMouseScroll":t}function Bn(t){var e=t.pointerType;return"pen"===e||"touch"===e}function Rn(t){t.touching=!0,null!=t.touchTimer&&(clearTimeout(t.touchTimer),t.touchTimer=null),t.touchTimer=setTimeout(function(){t.touching=!1,t.touchTimer=null},700)}function Nn(t){t&&(t.zrByTouch=!0)}function Fn(t,e){return be(t.dom,new Wn(t,e),!0)}function Vn(t,e){for(var i=e,n=!1;i&&9!==i.nodeType&&!(n=i.domBelongToZr||i!==e&&i===t.painterRoot);)i=i.parentNode;return n}function Wn(t,e){this.type=e.type,this.target=this.currentTarget=t.dom,this.pointerType=e.pointerType,this.clientX=e.clientX,this.clientY=e.clientY}function Hn(t,e){var i=e.domHandlers;Ag.pointerEventsSupported?f(Im.pointer,function(n){Zn(e,n,function(e){i[n].call(t,e)})}):(Ag.touchEventsSupported&&f(Im.touch,function(n){Zn(e,n,function(r){i[n].call(t,r),Rn(e)})}),f(Im.mouse,function(n){Zn(e,n,function(r){r=we(r),e.touching||i[n].call(t,r)})}))}function Gn(t,e){function i(i){function n(n){n=we(n),Vn(t,n.target)||(n=Fn(t,n),e.domHandlers[i].call(t,n))}Zn(e,i,n,{capture:!0})}Ag.pointerEventsSupported?f(Tm.pointer,i):Ag.touchEventsSupported||f(Tm.mouse,i)}function Zn(t,e,i,n){t.mounted[e]=i,t.listenerOpts[e]=n,Me(t.domTarget,zn(e),i,n)}function Xn(t){var e=t.mounted;for(var i in e)e.hasOwnProperty(i)&&Se(t.domTarget,zn(i),e[i],t.listenerOpts[i]);t.mounted={}}function Yn(t,e){if(t._mayPointerCapture=null,Am&&t._pointerCapturing^e){t._pointerCapturing=e;var i=t._globalHandlerScope;e?Gn(t,i):Xn(i)}}function Un(t,e){this.domTarget=t,this.domHandlers=e,this.mounted={},this.listenerOpts={},this.touchTimer=null,this.touching=!1}function qn(t,e){Ug.call(this),this.dom=t,this.painterRoot=e,this._localHandlerScope=new Un(t,Dm),Am&&(this._globalHandlerScope=new Un(document,km)),this._pointerCapturing=!1,this._mayPointerCapture=null,Hn(this,this._localHandlerScope)}function jn(t,e){var i=new Bm(Mg(),t,e);return Em[i.id]=i,i}function Kn(t){if(t)t.dispose();else{for(var e in Em)Em.hasOwnProperty(e)&&Em[e].dispose();Em={}}return this}function $n(t){return Em[t]}function Qn(t,e){Om[t]=e}function Jn(t){delete Em[t]}function tr(t){return t instanceof Array?t:null==t?[]:[t]}function er(t,e,i){if(t){t[e]=t[e]||{},t.emphasis=t.emphasis||{},t.emphasis[e]=t.emphasis[e]||{};for(var n=0,r=i.length;r>n;n++){var a=i[n];!t.emphasis[e].hasOwnProperty(a)&&t[e].hasOwnProperty(a)&&(t.emphasis[e][a]=t[e][a])}}}function ir(t){return!Fm(t)||Vm(t)||t instanceof Date?t:t.value}function nr(t){return Fm(t)&&!(t instanceof Array)}function rr(t,e){e=(e||[]).slice();var i=p(t||[],function(t){return{exist:t}});return Nm(e,function(t,n){if(Fm(t)){for(var r=0;r<i.length;r++)if(!i[r].option&&null!=t.id&&i[r].exist.id===t.id+"")return i[r].option=t,void(e[n]=null);for(var r=0;r<i.length;r++){var a=i[r].exist;if(!(i[r].option||null!=a.id&&null!=t.id||null==t.name||sr(t)||sr(a)||a.name!==t.name+""))return i[r].option=t,void(e[n]=null)}}}),Nm(e,function(t){if(Fm(t)){for(var e=0;e<i.length;e++){var n=i[e].exist;if(!i[e].option&&!sr(n)&&null==t.id){i[e].option=t;break}}e>=i.length&&i.push({option:t})}}),i}function ar(t){var e=N();Nm(t,function(t){var i=t.exist;i&&e.set(i.id,t)}),Nm(t,function(t){var i=t.option;O(!i||null==i.id||!e.get(i.id)||e.get(i.id)===t,"id duplicates: "+(i&&i.id)),i&&null!=i.id&&e.set(i.id,t),!t.keyInfo&&(t.keyInfo={})}),Nm(t,function(t,i){var n=t.exist,r=t.option,a=t.keyInfo;if(Fm(r)){if(a.name=null!=r.name?r.name+"":n?n.name:Wm+i,n)a.id=n.id;else if(null!=r.id)a.id=r.id+"";else{var o=0;do a.id="\x00"+a.name+"\x00"+o++;while(e.get(a.id))}e.set(a.id,t)}})}function or(t){var e=t.name;return!(!e||!e.indexOf(Wm))}function sr(t){return Fm(t)&&t.id&&0===(t.id+"").indexOf("\x00_ec_\x00")}function lr(t,e){return null!=e.dataIndexInside?e.dataIndexInside:null!=e.dataIndex?_(e.dataIndex)?p(e.dataIndex,function(e){return t.indexOfRawIndex(e)}):t.indexOfRawIndex(e.dataIndex):null!=e.name?_(e.name)?p(e.name,function(e){return t.indexOfName(e)}):t.indexOfName(e.name):void 0}function hr(){var t="__\x00ec_inner_"+Gm++ +"_"+Math.random().toFixed(5);return function(e){return e[t]||(e[t]={})}}function ur(t,e,i){if(b(e)){var n={};n[e+"Index"]=0,e=n}var r=i&&i.defaultMainType;!r||cr(e,r+"Index")||cr(e,r+"Id")||cr(e,r+"Name")||(e[r+"Index"]=0);var a={};return Nm(e,function(n,r){var n=e[r];if("dataIndex"===r||"dataIndexInside"===r)return void(a[r]=n);var o=r.match(/^(\w+)(Index|Id|Name)$/)||[],s=o[1],l=(o[2]||"").toLowerCase();if(!(!s||!l||null==n||"index"===l&&"none"===n||i&&i.includeMainTypes&&h(i.includeMainTypes,s)<0)){var u={mainType:s};("index"!==l||"all"!==n)&&(u[l]=n);var c=t.queryComponents(u);a[s+"Models"]=c,a[s+"Model"]=c[0]}}),a}function cr(t,e){return t&&t.hasOwnProperty(e)}function dr(t,e,i){t.setAttribute?t.setAttribute(e,i):t[e]=i}function fr(t,e){return t.getAttribute?t.getAttribute(e):t[e]}function pr(t){return"auto"===t?Ag.domSupported?"html":"richText":t||"html"}function gr(t){var e={main:"",sub:""};return t&&(t=t.split(Zm),e.main=t[0]||"",e.sub=t[1]||""),e}function vr(t){O(/^[a-zA-Z0-9_]+([.][a-zA-Z0-9_]+)?$/.test(t),'componentType "'+t+'" illegal')}function mr(t,e){t.$constructor=t,t.extend=function(t){wg&&f(e,function(e){t[e]||console.warn("Method `"+e+"` should be implemented"+(t.type?" in "+t.type:"")+".")});var i=this,n=function(){t.$constructor?t.$constructor.apply(this,arguments):i.apply(this,arguments)};return o(n.prototype,t),n.extend=this.extend,n.superCall=xr,n.superApply=_r,u(n,this),n.superClass=i,n}}function yr(t){var e=["__\x00is_clz",Ym++,Math.random().toFixed(3)].join("_");t.prototype[e]=!0,wg&&O(!t.isInstance,'The method "is" can not be defined.'),t.isInstance=function(t){return!(!t||!t[e])}}function xr(t,e){var i=P(arguments,2);return this.superClass.prototype[e].apply(t,i)}function _r(t,e,i){return this.superClass.prototype[e].apply(t,i)}function wr(t,e){function i(t){var e=n[t.main];return e&&e[Xm]||(e=n[t.main]={},e[Xm]=!0),e}e=e||{};var n={};if(t.registerClass=function(t,e){if(e)if(vr(e),e=gr(e),e.sub){if(e.sub!==Xm){var r=i(e);r[e.sub]=t}}else wg&&n[e.main]&&console.warn(e.main+" exists."),n[e.main]=t;return t},t.getClass=function(t,e,i){var r=n[t];if(r&&r[Xm]&&(r=e?r[e]:null),i&&!r)throw new Error(e?"Component "+t+"."+(e||"")+" not exists. Load it first.":t+".type should be specified.");return r},t.getClassesByMainType=function(t){t=gr(t);var e=[],i=n[t.main];return i&&i[Xm]?f(i,function(t,i){i!==Xm&&e.push(t)}):e.push(i),e},t.hasClass=function(t){return t=gr(t),!!n[t.main]},t.getAllClassMainTypes=function(){var t=[];return f(n,function(e,i){t.push(i)}),t},t.hasSubTypes=function(t){t=gr(t);var e=n[t.main];return e&&e[Xm]},t.parseClassType=gr,e.registerWhenExtend){var r=t.extend;r&&(t.extend=function(e){var i=r.call(this,e);return t.registerClass(i,e.type)})}return t}function br(t){return t>-ty&&ty>t}function Mr(t){return t>ty||-ty>t}function Sr(t,e,i,n,r){var a=1-r;return a*a*(a*t+3*r*e)+r*r*(r*n+3*a*i)}function Ar(t,e,i,n,r){var a=1-r;return 3*(((e-t)*a+2*(i-e)*r)*a+(n-i)*r*r)}function Ir(t,e,i,n,r,a){var o=n+3*(e-i)-t,s=3*(i-2*e+t),l=3*(e-t),h=t-r,u=s*s-3*o*l,c=s*l-9*o*h,d=l*l-3*s*h,f=0;if(br(u)&&br(c))if(br(s))a[0]=0;else{var p=-l/s;p>=0&&1>=p&&(a[f++]=p)}else{var g=c*c-4*u*d;if(br(g)){var v=c/u,p=-s/o+v,m=-v/2;p>=0&&1>=p&&(a[f++]=p),m>=0&&1>=m&&(a[f++]=m)}else if(g>0){var y=Jm(g),x=u*s+1.5*o*(-c+y),_=u*s+1.5*o*(-c-y);x=0>x?-Qm(-x,ny):Qm(x,ny),_=0>_?-Qm(-_,ny):Qm(_,ny);var p=(-s-(x+_))/(3*o);p>=0&&1>=p&&(a[f++]=p)}else{var w=(2*u*s-3*o*c)/(2*Jm(u*u*u)),b=Math.acos(w)/3,M=Jm(u),S=Math.cos(b),p=(-s-2*M*S)/(3*o),m=(-s+M*(S+iy*Math.sin(b)))/(3*o),A=(-s+M*(S-iy*Math.sin(b)))/(3*o);p>=0&&1>=p&&(a[f++]=p),m>=0&&1>=m&&(a[f++]=m),A>=0&&1>=A&&(a[f++]=A)}}return f}function Tr(t,e,i,n,r){var a=6*i-12*e+6*t,o=9*e+3*n-3*t-9*i,s=3*e-3*t,l=0;if(br(o)){if(Mr(a)){var h=-s/a;h>=0&&1>=h&&(r[l++]=h)}}else{var u=a*a-4*o*s;if(br(u))r[0]=-a/(2*o);else if(u>0){var c=Jm(u),h=(-a+c)/(2*o),d=(-a-c)/(2*o);h>=0&&1>=h&&(r[l++]=h),d>=0&&1>=d&&(r[l++]=d)}}return l}function Cr(t,e,i,n,r,a){var o=(e-t)*r+t,s=(i-e)*r+e,l=(n-i)*r+i,h=(s-o)*r+o,u=(l-s)*r+s,c=(u-h)*r+h;a[0]=t,a[1]=o,a[2]=h,a[3]=c,a[4]=c,a[5]=u,a[6]=l,a[7]=n}function Dr(t,e,i,n,r,a,o,s,l,h,u){var c,d,f,p,g,v=.005,m=1/0;ry[0]=l,ry[1]=h;for(var y=0;1>y;y+=.05)ay[0]=Sr(t,i,r,o,y),ay[1]=Sr(e,n,a,s,y),p=Zg(ry,ay),m>p&&(c=y,m=p);m=1/0;for(var x=0;32>x&&!(ey>v);x++)d=c-v,f=c+v,ay[0]=Sr(t,i,r,o,d),ay[1]=Sr(e,n,a,s,d),p=Zg(ay,ry),d>=0&&m>p?(c=d,m=p):(oy[0]=Sr(t,i,r,o,f),oy[1]=Sr(e,n,a,s,f),g=Zg(oy,ry),1>=f&&m>g?(c=f,m=g):v*=.5);return u&&(u[0]=Sr(t,i,r,o,c),u[1]=Sr(e,n,a,s,c)),Jm(m)}function kr(t,e,i,n){var r=1-n;return r*(r*t+2*n*e)+n*n*i}function Pr(t,e,i,n){return 2*((1-n)*(e-t)+n*(i-e))}function Lr(t,e,i,n,r){var a=t-2*e+i,o=2*(e-t),s=t-n,l=0;if(br(a)){if(Mr(o)){var h=-s/o;h>=0&&1>=h&&(r[l++]=h)}}else{var u=o*o-4*a*s;if(br(u)){var h=-o/(2*a);h>=0&&1>=h&&(r[l++]=h)}else if(u>0){var c=Jm(u),h=(-o+c)/(2*a),d=(-o-c)/(2*a);h>=0&&1>=h&&(r[l++]=h),d>=0&&1>=d&&(r[l++]=d)}}return l}function Or(t,e,i){var n=t+i-2*e;return 0===n?.5:(t-e)/n}function Er(t,e,i,n,r){var a=(e-t)*n+t,o=(i-e)*n+e,s=(o-a)*n+a;r[0]=t,r[1]=a,r[2]=s,r[3]=s,r[4]=o,r[5]=i}function zr(t,e,i,n,r,a,o,s,l){var h,u=.005,c=1/0;ry[0]=o,ry[1]=s;for(var d=0;1>d;d+=.05){ay[0]=kr(t,i,r,d),ay[1]=kr(e,n,a,d);var f=Zg(ry,ay);c>f&&(h=d,c=f)}c=1/0;for(var p=0;32>p&&!(ey>u);p++){var g=h-u,v=h+u;ay[0]=kr(t,i,r,g),ay[1]=kr(e,n,a,g);var f=Zg(ay,ry);if(g>=0&&c>f)h=g,c=f;else{oy[0]=kr(t,i,r,v),oy[1]=kr(e,n,a,v);var m=Zg(oy,ry);1>=v&&c>m?(h=v,c=m):u*=.5}}return l&&(l[0]=kr(t,i,r,h),l[1]=kr(e,n,a,h)),Jm(c)}function Br(t,e,i){if(0!==t.length){var n,r=t[0],a=r[0],o=r[0],s=r[1],l=r[1];for(n=1;n<t.length;n++)r=t[n],a=sy(a,r[0]),o=ly(o,r[0]),s=sy(s,r[1]),l=ly(l,r[1]);e[0]=a,e[1]=s,i[0]=o,i[1]=l}}function Rr(t,e,i,n,r,a){r[0]=sy(t,i),r[1]=sy(e,n),a[0]=ly(t,i),a[1]=ly(e,n)}function Nr(t,e,i,n,r,a,o,s,l,h){var u,c=Tr,d=Sr,f=c(t,i,r,o,gy);for(l[0]=1/0,l[1]=1/0,h[0]=-1/0,h[1]=-1/0,u=0;f>u;u++){var p=d(t,i,r,o,gy[u]);l[0]=sy(p,l[0]),h[0]=ly(p,h[0])}for(f=c(e,n,a,s,vy),u=0;f>u;u++){var g=d(e,n,a,s,vy[u]);l[1]=sy(g,l[1]),h[1]=ly(g,h[1])}l[0]=sy(t,l[0]),h[0]=ly(t,h[0]),l[0]=sy(o,l[0]),h[0]=ly(o,h[0]),l[1]=sy(e,l[1]),h[1]=ly(e,h[1]),l[1]=sy(s,l[1]),h[1]=ly(s,h[1])}function Fr(t,e,i,n,r,a,o,s){var l=Or,h=kr,u=ly(sy(l(t,i,r),1),0),c=ly(sy(l(e,n,a),1),0),d=h(t,i,r,u),f=h(e,n,a,c);o[0]=sy(t,r,d),o[1]=sy(e,a,f),s[0]=ly(t,r,d),s[1]=ly(e,a,f)}function Vr(t,e,i,n,r,a,o,s,l){var h=oe,u=se,c=Math.abs(r-a);if(1e-4>c%cy&&c>1e-4)return s[0]=t-i,s[1]=e-n,l[0]=t+i,void(l[1]=e+n);if(dy[0]=uy(r)*i+t,dy[1]=hy(r)*n+e,fy[0]=uy(a)*i+t,fy[1]=hy(a)*n+e,h(s,dy,fy),u(l,dy,fy),r%=cy,0>r&&(r+=cy),a%=cy,0>a&&(a+=cy),r>a&&!o?a+=cy:a>r&&o&&(r+=cy),o){var d=a;a=r,r=d}for(var f=0;a>f;f+=Math.PI/2)f>r&&(py[0]=uy(f)*i+t,py[1]=hy(f)*n+e,h(s,py,s),u(l,py,l))}function Wr(t,e,i,n,r,a,o){if(0===r)return!1;var s=r,l=0,h=t;if(o>e+s&&o>n+s||e-s>o&&n-s>o||a>t+s&&a>i+s||t-s>a&&i-s>a)return!1;if(t===i)return Math.abs(a-t)<=s/2;l=(e-n)/(t-i),h=(t*n-i*e)/(t-i);var u=l*a-o+h,c=u*u/(l*l+1);return s/2*s/2>=c}function Hr(t,e,i,n,r,a,o,s,l,h,u){if(0===l)return!1;var c=l;if(u>e+c&&u>n+c&&u>a+c&&u>s+c||e-c>u&&n-c>u&&a-c>u&&s-c>u||h>t+c&&h>i+c&&h>r+c&&h>o+c||t-c>h&&i-c>h&&r-c>h&&o-c>h)return!1;var d=Dr(t,e,i,n,r,a,o,s,h,u,null);return c/2>=d}function Gr(t,e,i,n,r,a,o,s,l){if(0===o)return!1;var h=o;if(l>e+h&&l>n+h&&l>a+h||e-h>l&&n-h>l&&a-h>l||s>t+h&&s>i+h&&s>r+h||t-h>s&&i-h>s&&r-h>s)return!1;var u=zr(t,e,i,n,r,a,s,l,null);return h/2>=u}function Zr(t){return t%=ky,0>t&&(t+=ky),t}function Xr(t,e,i,n,r,a,o,s,l){if(0===o)return!1;var h=o;s-=t,l-=e;var u=Math.sqrt(s*s+l*l);if(u-h>i||i>u+h)return!1;if(Math.abs(n-r)%Py<1e-4)return!0;if(a){var c=n;n=Zr(r),r=Zr(c)}else n=Zr(n),r=Zr(r);n>r&&(r+=Py);var d=Math.atan2(l,s);return 0>d&&(d+=Py),d>=n&&r>=d||d+Py>=n&&r>=d+Py}function Yr(t,e,i,n,r,a){if(a>e&&a>n||e>a&&n>a)return 0;if(n===e)return 0;var o=e>n?1:-1,s=(a-e)/(n-e);(1===s||0===s)&&(o=e>n?.5:-.5);var l=s*(i-t)+t;return l===r?1/0:l>r?o:0}function Ur(t,e){return Math.abs(t-e)<Ey}function qr(){var t=By[0];By[0]=By[1],By[1]=t}function jr(t,e,i,n,r,a,o,s,l,h){if(h>e&&h>n&&h>a&&h>s||e>h&&n>h&&a>h&&s>h)return 0;var u=Ir(e,n,a,s,h,zy);if(0===u)return 0;for(var c,d,f=0,p=-1,g=0;u>g;g++){var v=zy[g],m=0===v||1===v?.5:1,y=Sr(t,i,r,o,v);l>y||(0>p&&(p=Tr(e,n,a,s,By),By[1]<By[0]&&p>1&&qr(),c=Sr(e,n,a,s,By[0]),p>1&&(d=Sr(e,n,a,s,By[1]))),f+=2===p?v<By[0]?e>c?m:-m:v<By[1]?c>d?m:-m:d>s?m:-m:v<By[0]?e>c?m:-m:c>s?m:-m)}return f}function Kr(t,e,i,n,r,a,o,s){if(s>e&&s>n&&s>a||e>s&&n>s&&a>s)return 0;var l=Lr(e,n,a,s,zy);if(0===l)return 0;var h=Or(e,n,a);if(h>=0&&1>=h){for(var u=0,c=kr(e,n,a,h),d=0;l>d;d++){var f=0===zy[d]||1===zy[d]?.5:1,p=kr(t,i,r,zy[d]);o>p||(u+=zy[d]<h?e>c?f:-f:c>a?f:-f)}return u}var f=0===zy[0]||1===zy[0]?.5:1,p=kr(t,i,r,zy[0]);return o>p?0:e>a?f:-f}function $r(t,e,i,n,r,a,o,s){if(s-=e,s>i||-i>s)return 0;var l=Math.sqrt(i*i-s*s);zy[0]=-l,zy[1]=l;var h=Math.abs(n-r);if(1e-4>h)return 0;if(1e-4>h%Oy){n=0,r=Oy;var u=a?1:-1;return o>=zy[0]+t&&o<=zy[1]+t?u:0}if(a){var l=n;n=Zr(r),r=Zr(l)}else n=Zr(n),r=Zr(r);n>r&&(r+=Oy);for(var c=0,d=0;2>d;d++){var f=zy[d];if(f+t>o){var p=Math.atan2(s,f),u=a?1:-1;0>p&&(p=Oy+p),(p>=n&&r>=p||p+Oy>=n&&r>=p+Oy)&&(p>Math.PI/2&&p<1.5*Math.PI&&(u=-u),c+=u)}}return c}function Qr(t,e,i,n,r){for(var a=0,o=0,s=0,l=0,h=0,u=0;u<t.length;){var c=t[u++];switch(c===Ly.M&&u>1&&(i||(a+=Yr(o,s,l,h,n,r))),1===u&&(o=t[u],s=t[u+1],l=o,h=s),c){case Ly.M:l=t[u++],h=t[u++],o=l,s=h;break;case Ly.L:if(i){if(Wr(o,s,t[u],t[u+1],e,n,r))return!0}else a+=Yr(o,s,t[u],t[u+1],n,r)||0;o=t[u++],s=t[u++];break;case Ly.C:if(i){if(Hr(o,s,t[u++],t[u++],t[u++],t[u++],t[u],t[u+1],e,n,r))return!0}else a+=jr(o,s,t[u++],t[u++],t[u++],t[u++],t[u],t[u+1],n,r)||0;o=t[u++],s=t[u++];break;case Ly.Q:if(i){if(Gr(o,s,t[u++],t[u++],t[u],t[u+1],e,n,r))return!0}else a+=Kr(o,s,t[u++],t[u++],t[u],t[u+1],n,r)||0;o=t[u++],s=t[u++];break;case Ly.A:var d=t[u++],f=t[u++],p=t[u++],g=t[u++],v=t[u++],m=t[u++];u+=1;var y=1-t[u++],x=Math.cos(v)*p+d,_=Math.sin(v)*g+f;u>1?a+=Yr(o,s,x,_,n,r):(l=x,h=_);var w=(n-d)*g/p+d;if(i){if(Xr(d,f,g,v,v+m,y,e,w,r))return!0}else a+=$r(d,f,g,v,v+m,y,w,r);o=Math.cos(v+m)*p+d,s=Math.sin(v+m)*g+f;break;case Ly.R:l=o=t[u++],h=s=t[u++];var b=t[u++],M=t[u++],x=l+b,_=h+M;if(i){if(Wr(l,h,x,h,e,n,r)||Wr(x,h,x,_,e,n,r)||Wr(x,_,l,_,e,n,r)||Wr(l,_,l,h,e,n,r))return!0}else a+=Yr(x,h,x,_,n,r),a+=Yr(l,_,l,h,n,r);break;case Ly.Z:if(i){if(Wr(o,s,l,h,e,n,r))return!0}else a+=Yr(o,s,l,h,n,r);o=l,s=h}}return i||Ur(s,h)||(a+=Yr(o,s,l,h,n,r)||0),0!==a}function Jr(t,e,i){return Qr(t,0,!1,e,i)}function ta(t,e,i,n){return Qr(t,e,!0,i,n)}function ea(t){Tn.call(this,t),this.path=null}function ia(t,e,i,n,r,a,o,s,l,h,u){var c=l*(qy/180),d=Uy(c)*(t-i)/2+Yy(c)*(e-n)/2,f=-1*Yy(c)*(t-i)/2+Uy(c)*(e-n)/2,p=d*d/(o*o)+f*f/(s*s);p>1&&(o*=Xy(p),s*=Xy(p));var g=(r===a?-1:1)*Xy((o*o*s*s-o*o*f*f-s*s*d*d)/(o*o*f*f+s*s*d*d))||0,v=g*o*f/s,m=g*-s*d/o,y=(t+i)/2+Uy(c)*v-Yy(c)*m,x=(e+n)/2+Yy(c)*v+Uy(c)*m,_=$y([1,0],[(d-v)/o,(f-m)/s]),w=[(d-v)/o,(f-m)/s],b=[(-1*d-v)/o,(-1*f-m)/s],M=$y(w,b);Ky(w,b)<=-1&&(M=qy),Ky(w,b)>=1&&(M=0),0===a&&M>0&&(M-=2*qy),1===a&&0>M&&(M+=2*qy),u.addData(h,y,x,o,s,_,M,c,a)}function na(t){if(!t)return new Dy;for(var e,i=0,n=0,r=i,a=n,o=new Dy,s=Dy.CMD,l=t.match(Qy),h=0;h<l.length;h++){for(var u,c=l[h],d=c.charAt(0),f=c.match(Jy)||[],p=f.length,g=0;p>g;g++)f[g]=parseFloat(f[g]);for(var v=0;p>v;){var m,y,x,_,w,b,M,S=i,A=n;switch(d){case"l":i+=f[v++],n+=f[v++],u=s.L,o.addData(u,i,n);break;case"L":i=f[v++],n=f[v++],u=s.L,o.addData(u,i,n);break;case"m":i+=f[v++],n+=f[v++],u=s.M,o.addData(u,i,n),r=i,a=n,d="l";break;case"M":i=f[v++],n=f[v++],u=s.M,o.addData(u,i,n),r=i,a=n,d="L";break;case"h":i+=f[v++],u=s.L,o.addData(u,i,n);break;case"H":i=f[v++],u=s.L,o.addData(u,i,n);break;case"v":n+=f[v++],u=s.L,o.addData(u,i,n);break;case"V":n=f[v++],u=s.L,o.addData(u,i,n);break;case"C":u=s.C,o.addData(u,f[v++],f[v++],f[v++],f[v++],f[v++],f[v++]),i=f[v-2],n=f[v-1];break;case"c":u=s.C,o.addData(u,f[v++]+i,f[v++]+n,f[v++]+i,f[v++]+n,f[v++]+i,f[v++]+n),i+=f[v-2],n+=f[v-1];break;case"S":m=i,y=n;var I=o.len(),T=o.data;e===s.C&&(m+=i-T[I-4],y+=n-T[I-3]),u=s.C,S=f[v++],A=f[v++],i=f[v++],n=f[v++],o.addData(u,m,y,S,A,i,n);break;case"s":m=i,y=n;var I=o.len(),T=o.data;e===s.C&&(m+=i-T[I-4],y+=n-T[I-3]),u=s.C,S=i+f[v++],A=n+f[v++],i+=f[v++],n+=f[v++],o.addData(u,m,y,S,A,i,n);break;case"Q":S=f[v++],A=f[v++],i=f[v++],n=f[v++],u=s.Q,o.addData(u,S,A,i,n);break;case"q":S=f[v++]+i,A=f[v++]+n,i+=f[v++],n+=f[v++],u=s.Q,o.addData(u,S,A,i,n);break;case"T":m=i,y=n;var I=o.len(),T=o.data;e===s.Q&&(m+=i-T[I-4],y+=n-T[I-3]),i=f[v++],n=f[v++],u=s.Q,o.addData(u,m,y,i,n);break;case"t":m=i,y=n;var I=o.len(),T=o.data;e===s.Q&&(m+=i-T[I-4],y+=n-T[I-3]),i+=f[v++],n+=f[v++],u=s.Q,o.addData(u,m,y,i,n);break;case"A":x=f[v++],_=f[v++],w=f[v++],b=f[v++],M=f[v++],S=i,A=n,i=f[v++],n=f[v++],u=s.A,ia(S,A,i,n,b,M,x,_,w,u,o);break;case"a":x=f[v++],_=f[v++],w=f[v++],b=f[v++],M=f[v++],S=i,A=n,i+=f[v++],n+=f[v++],u=s.A,ia(S,A,i,n,b,M,x,_,w,u,o)}}("z"===d||"Z"===d)&&(u=s.Z,o.addData(u),i=r,n=a),e=u}return o.toStatic(),o}function ra(t,e){var i=na(t);return e=e||{},e.buildPath=function(t){if(t.setData){t.setData(i.data);var e=t.getContext();e&&t.rebuildPath(e)}else{var e=t;i.rebuildPath(e)}},e.applyTransform=function(t){Zy(i,t),this.dirty(!0)},e}function aa(t,e){return new ea(ra(t,e))}function oa(t,e){return ea.extend(ra(t,e))}function sa(t,e){for(var i=[],n=t.length,r=0;n>r;r++){var a=t[r];a.path||a.createPathProxy(),a.__dirtyPath&&a.buildPath(a.path,a.shape,!0),i.push(a.path)}var o=new ea(e);return o.createPathProxy(),o.buildPath=function(t){t.appendPath(i);var e=t.getContext();e&&t.rebuildPath(e)},o}function la(t,e,i,n,r,a,o){var s=.5*(i-t),l=.5*(n-e);return(2*(e-i)+s+l)*o+(-3*(e-i)-2*s-l)*a+s*r+e}function ha(t,e,i){var n=e.points,r=e.smooth;if(n&&n.length>=2){if(r&&"spline"!==r){var a=sx(n,r,i,e.smoothConstraint);t.moveTo(n[0][0],n[0][1]);for(var o=n.length,s=0;(i?o:o-1)>s;s++){var l=a[2*s],h=a[2*s+1],u=n[(s+1)%o];t.bezierCurveTo(l[0],l[1],h[0],h[1],u[0],u[1])}}else{"spline"===r&&(n=ox(n,i)),t.moveTo(n[0][0],n[0][1]);for(var s=1,c=n.length;c>s;s++)t.lineTo(n[s][0],n[s][1])}i&&t.closePath()}}function ua(t,e,i){if(e){var n=e.x1,r=e.x2,a=e.y1,o=e.y2;t.x1=n,t.x2=r,t.y1=a,t.y2=o;var s=i&&i.lineWidth;s&&(ux(2*n)===ux(2*r)&&(t.x1=t.x2=da(n,s,!0)),ux(2*a)===ux(2*o)&&(t.y1=t.y2=da(a,s,!0)))}}function ca(t,e,i){if(e){var n=e.x,r=e.y,a=e.width,o=e.height;t.x=n,t.y=r,t.width=a,t.height=o;var s=i&&i.lineWidth;s&&(t.x=da(n,s,!0),t.y=da(r,s,!0),t.width=Math.max(da(n+a,s,!1)-t.x,0===a?0:1),t.height=Math.max(da(r+o,s,!1)-t.y,0===o?0:1))}}function da(t,e,i){if(!e)return t;var n=ux(2*t);return(n+ux(e))%2===0?n/2:(n+(i?1:-1))/2}function fa(t,e,i){var n=t.cpx2,r=t.cpy2;return null===n||null===r?[(i?Ar:Sr)(t.x1,t.cpx1,t.cpx2,t.x2,e),(i?Ar:Sr)(t.y1,t.cpy1,t.cpy2,t.y2,e)]:[(i?Pr:kr)(t.x1,t.cpx1,t.x2,e),(i?Pr:kr)(t.y1,t.cpy1,t.y2,e)]}function pa(t){Tn.call(this,t),this._displayables=[],this._temporaryDisplayables=[],this._cursor=0,this.notClear=!0}function ga(t){return ea.extend(t)}function va(t,e){return oa(t,e)}function ma(t,e){Ox[t]=e}function ya(t){return Ox.hasOwnProperty(t)?Ox[t]:void 0}function xa(t,e,i,n){var r=aa(t,e);return i&&("center"===n&&(i=wa(i,r.getBoundingRect())),ba(r,i)),r}function _a(t,e,i){var n=new Cn({style:{image:t,x:e.x,y:e.y,width:e.width,height:e.height},onload:function(t){if("center"===i){var r={width:t.width,height:t.height};n.setStyle(wa(e,r))}}});return n}function wa(t,e){var i,n=e.width/e.height,r=t.height*n;r<=t.width?i=t.height:(r=t.width,i=r/n);var a=t.x+t.width/2,o=t.y+t.height/2;return{x:a-r/2,y:o-i/2,width:r,height:i}}function ba(t,e){if(t.applyTransform){var i=t.getBoundingRect(),n=i.calculateTransform(e);t.applyTransform(n)}}function Ma(t){return ua(t.shape,t.shape,t.style),t}function Sa(t){return ca(t.shape,t.shape,t.style),t}function Aa(t){return null!=t&&"none"!==t}function Ia(t){if("string"!=typeof t)return t;var e=Bx.get(t);return e||(e=ni(t,-.1),1e4>Rx&&(Bx.set(t,e),Rx++)),e}function Ta(t){if(t.__hoverStlDirty){t.__hoverStlDirty=!1;var e=t.__hoverStl;if(!e)return void(t.__cachedNormalStl=t.__cachedNormalZ2=null);var i=t.__cachedNormalStl={};t.__cachedNormalZ2=t.z2;var n=t.style;for(var r in e)null!=e[r]&&(i[r]=n[r]);i.fill=n.fill,i.stroke=n.stroke}}function Ca(t){var e=t.__hoverStl;if(e&&!t.__highlighted){var i=t.__zr,n=t.useHoverLayer&&i&&"canvas"===i.painter.type;if(t.__highlighted=n?"layer":"plain",!(t.isGroup||!i&&t.useHoverLayer)){var r=t,a=t.style;n&&(r=i.addHover(t),a=r.style),$a(a),n||Ta(r),a.extendFrom(e),Da(a,e,"fill"),Da(a,e,"stroke"),Ka(a),n||(t.dirty(!1),t.z2+=Tx)}}}function Da(t,e,i){!Aa(e[i])&&Aa(t[i])&&(t[i]=Ia(t[i]))}function ka(t){var e=t.__highlighted;if(e&&(t.__highlighted=!1,!t.isGroup))if("layer"===e)t.__zr&&t.__zr.removeHover(t);else{var i=t.style,n=t.__cachedNormalStl;n&&($a(i),t.setStyle(n),Ka(i));var r=t.__cachedNormalZ2;null!=r&&t.z2-r===Tx&&(t.z2=r)}}function Pa(t,e,i){var n,r=kx,a=kx;t.__highlighted&&(r=Dx,n=!0),e(t,i),t.__highlighted&&(a=Dx,n=!0),t.isGroup&&t.traverse(function(t){!t.isGroup&&e(t,i)}),n&&t.__highDownOnUpdate&&t.__highDownOnUpdate(r,a)}function La(t,e){e=t.__hoverStl=e!==!1&&(t.hoverStyle||e||{}),t.__hoverStlDirty=!0,t.__highlighted&&(t.__cachedNormalStl=null,ka(t),Ca(t))}function Oa(t){!Ra(this,t)&&!this.__highByOuter&&Pa(this,Ca)}function Ea(t){!Ra(this,t)&&!this.__highByOuter&&Pa(this,ka)}function za(t){this.__highByOuter|=1<<(t||0),Pa(this,Ca)}function Ba(t){!(this.__highByOuter&=~(1<<(t||0)))&&Pa(this,ka)}function Ra(t,e){return t.__highDownSilentOnTouch&&e.zrByTouch}function Na(t,e){Fa(t,!0),Pa(t,La,e)}function Fa(t,e){var i=e===!1;if(t.__highDownSilentOnTouch=t.highDownSilentOnTouch,t.__highDownOnUpdate=t.highDownOnUpdate,!i||t.__highDownDispatcher){var n=i?"off":"on";t[n]("mouseover",Oa)[n]("mouseout",Ea),t[n]("emphasis",za)[n]("normal",Ba),t.__highByOuter=t.__highByOuter||0,t.__highDownDispatcher=!i}}function Va(t){return!(!t||!t.__highDownDispatcher)}function Wa(t){var e=Lx[t];return null==e&&32>=Px&&(e=Lx[t]=Px++),e}function Ha(t,e,i,n,r,a,o){r=r||Ix;var s,l=r.labelFetcher,h=r.labelDataIndex,u=r.labelDimIndex,c=r.labelProp,d=i.getShallow("show"),f=n.getShallow("show");(d||f)&&(l&&(s=l.getFormattedLabel(h,"normal",null,u,c)),null==s&&(s=w(r.defaultText)?r.defaultText(h,r):r.defaultText));var p=d?s:null,g=f?D(l?l.getFormattedLabel(h,"emphasis",null,u,c):null,s):null;(null!=p||null!=g)&&(Za(t,i,a,r),Za(e,n,o,r,!0)),t.text=p,e.text=g}function Ga(t,e,i){var n=t.style;e&&($a(n),t.setStyle(e),Ka(n)),n=t.__hoverStl,i&&n&&($a(n),o(n,i),Ka(n))}function Za(t,e,i,n,r){return Ya(t,e,n,r),i&&o(t,i),t}function Xa(t,e,i){var n,r={isRectText:!0};i===!1?n=!0:r.autoColor=i,Ya(t,e,r,n)}function Ya(t,e,i,n){if(i=i||Ix,i.isRectText){var r;i.getTextPosition?r=i.getTextPosition(e,n):(r=e.getShallow("position")||(n?null:"inside"),"outside"===r&&(r="top")),t.textPosition=r,t.textOffset=e.getShallow("offset");var a=e.getShallow("rotate");null!=a&&(a*=Math.PI/180),t.textRotation=a,t.textDistance=D(e.getShallow("distance"),n?null:5)}var o,s=e.ecModel,l=s&&s.option.textStyle,h=Ua(e);if(h){o={};for(var u in h)if(h.hasOwnProperty(u)){var c=e.getModel(["rich",u]);qa(o[u]={},c,l,i,n)}}return t.rich=o,qa(t,e,l,i,n,!0),i.forceRich&&!i.textStyle&&(i.textStyle={}),t}function Ua(t){for(var e;t&&t!==t.ecModel;){var i=(t.option||Ix).rich;if(i){e=e||{};for(var n in i)i.hasOwnProperty(n)&&(e[n]=1)}t=t.parentModel}return e}function qa(t,e,i,n,r,a){i=!r&&i||Ix,t.textFill=ja(e.getShallow("color"),n)||i.color,t.textStroke=ja(e.getShallow("textBorderColor"),n)||i.textBorderColor,t.textStrokeWidth=D(e.getShallow("textBorderWidth"),i.textBorderWidth),r||(a&&(t.insideRollbackOpt=n,Ka(t)),null==t.textFill&&(t.textFill=n.autoColor)),t.fontStyle=e.getShallow("fontStyle")||i.fontStyle,t.fontWeight=e.getShallow("fontWeight")||i.fontWeight,t.fontSize=e.getShallow("fontSize")||i.fontSize,t.fontFamily=e.getShallow("fontFamily")||i.fontFamily,t.textAlign=e.getShallow("align"),t.textVerticalAlign=e.getShallow("verticalAlign")||e.getShallow("baseline"),t.textLineHeight=e.getShallow("lineHeight"),t.textWidth=e.getShallow("width"),t.textHeight=e.getShallow("height"),t.textTag=e.getShallow("tag"),a&&n.disableBox||(t.textBackgroundColor=ja(e.getShallow("backgroundColor"),n),t.textPadding=e.getShallow("padding"),t.textBorderColor=ja(e.getShallow("borderColor"),n),t.textBorderWidth=e.getShallow("borderWidth"),t.textBorderRadius=e.getShallow("borderRadius"),t.textBoxShadowColor=e.getShallow("shadowColor"),t.textBoxShadowBlur=e.getShallow("shadowBlur"),t.textBoxShadowOffsetX=e.getShallow("shadowOffsetX"),t.textBoxShadowOffsetY=e.getShallow("shadowOffsetY")),t.textShadowColor=e.getShallow("textShadowColor")||i.textShadowColor,t.textShadowBlur=e.getShallow("textShadowBlur")||i.textShadowBlur,t.textShadowOffsetX=e.getShallow("textShadowOffsetX")||i.textShadowOffsetX,t.textShadowOffsetY=e.getShallow("textShadowOffsetY")||i.textShadowOffsetY}function ja(t,e){return"auto"!==t?t:e&&e.autoColor?e.autoColor:null}function Ka(t){var e,i=t.textPosition,n=t.insideRollbackOpt;if(n&&null==t.textFill){var r=n.autoColor,a=n.isRectText,o=n.useInsideStyle,s=o!==!1&&(o===!0||a&&i&&"string"==typeof i&&i.indexOf("inside")>=0),l=!s&&null!=r;(s||l)&&(e={textFill:t.textFill,textStroke:t.textStroke,textStrokeWidth:t.textStrokeWidth}),s&&(t.textFill="#fff",null==t.textStroke&&(t.textStroke=r,null==t.textStrokeWidth&&(t.textStrokeWidth=2))),l&&(t.textFill=r)}t.insideRollback=e}function $a(t){var e=t.insideRollback;e&&(t.textFill=e.textFill,t.textStroke=e.textStroke,t.textStrokeWidth=e.textStrokeWidth,t.insideRollback=null)}function Qa(t,e){var i=e&&e.getModel("textStyle");return E([t.fontStyle||i&&i.getShallow("fontStyle")||"",t.fontWeight||i&&i.getShallow("fontWeight")||"",(t.fontSize||i&&i.getShallow("fontSize")||12)+"px",t.fontFamily||i&&i.getShallow("fontFamily")||"sans-serif"].join(" "))}function Ja(t,e,i,n,r,a){"function"==typeof r&&(a=r,r=null);var o=n&&n.isAnimationEnabled();if(o){var s=t?"Update":"",l=n.getShallow("animationDuration"+s),h=n.getShallow("animationEasing"+s),u=n.getShallow("animationDelay"+s);"function"==typeof u&&(u=u(r,n.getAnimationDelayParams?n.getAnimationDelayParams(e,r):null)),"function"==typeof l&&(l=l(r)),l>0?e.animateTo(i,l,u||0,h,a,!!a):(e.stopAnimation(),e.attr(i),a&&a())}else e.stopAnimation(),e.attr(i),a&&a()}function to(t,e,i,n,r){Ja(!0,t,e,i,n,r)}function eo(t,e,i,n,r){Ja(!1,t,e,i,n,r)}function io(t,e){for(var i=Ee([]);t&&t!==e;)Be(i,t.getLocalTransform(),i),t=t.parent;return i}function no(t,e,i){return e&&!d(e)&&(e=uv.getLocalTransform(e)),i&&(e=Ve([],e)),ae([],t,e)}function ro(t,e,i){var n=0===e[4]||0===e[5]||0===e[0]?1:Math.abs(2*e[4]/e[0]),r=0===e[4]||0===e[5]||0===e[2]?1:Math.abs(2*e[4]/e[2]),a=["left"===t?-n:"right"===t?n:0,"top"===t?-r:"bottom"===t?r:0];return a=no(a,e,i),Math.abs(a[0])>Math.abs(a[1])?a[0]>0?"right":"left":a[1]>0?"bottom":"top"}function ao(t,e,i){function n(t){var e={};return t.traverse(function(t){!t.isGroup&&t.anid&&(e[t.anid]=t)}),e}function r(t){var e={position:G(t.position),rotation:t.rotation};return t.shape&&(e.shape=o({},t.shape)),e}if(t&&e){var a=n(t);e.traverse(function(t){if(!t.isGroup&&t.anid){var e=a[t.anid];if(e){var n=r(t);t.attr(r(e)),to(t,n,i,t.dataIndex)}}})}}function oo(t,e){return p(t,function(t){var i=t[0];i=Sx(i,e.x),i=Ax(i,e.x+e.width);var n=t[1];return n=Sx(n,e.y),n=Ax(n,e.y+e.height),[i,n]})}function so(t,e){var i=Sx(t.x,e.x),n=Ax(t.x+t.width,e.x+e.width),r=Sx(t.y,e.y),a=Ax(t.y+t.height,e.y+e.height);return n>=i&&a>=r?{x:i,y:r,width:n-i,height:a-r}:void 0}function lo(t,e,i){e=o({rectHover:!0},e);var n=e.style={strokeNoScale:!0};return i=i||{x:-1,y:-1,width:2,height:2},t?0===t.indexOf("image://")?(n.image=t.slice(8),s(n,i),new Cn(e)):xa(t.replace("path://",""),e,i,"center"):void 0}function ho(t,e,i,n,r){for(var a=0,o=r[r.length-1];a<r.length;a++){var s=r[a];if(uo(t,e,i,n,s[0],s[1],o[0],o[1]))return!0;o=s}}function uo(t,e,i,n,r,a,o,s){var l=i-t,h=n-e,u=o-r,c=s-a,d=co(u,c,l,h);if(fo(d))return!1;var f=t-r,p=e-a,g=co(f,p,l,h)/d;if(0>g||g>1)return!1;var v=co(f,p,u,c)/d;return 0>v||v>1?!1:!0}function co(t,e,i,n){return t*n-i*e}function fo(t){return 1e-6>=t&&t>=-1e-6}function po(t,e,i){this.parentModel=e,this.ecModel=i,this.option=t}function go(t,e,i){for(var n=0;n<e.length&&(!e[n]||(t=t&&"object"==typeof t?t[e[n]]:null,null!=t));n++);return null==t&&i&&(t=i.get(e)),t
}function vo(t,e){var i=Zx(t).getParent;return i?i.call(t,e):t.parentModel}function mo(t){return[t||"",Xx++,Math.random().toFixed(5)].join("_")}function yo(t){var e={};return t.registerSubTypeDefaulter=function(t,i){t=gr(t),e[t.main]=i},t.determineSubType=function(i,n){var r=n.type;if(!r){var a=gr(i).main;t.hasSubTypes(i)&&e[a]&&(r=e[a](n))}return r},t}function xo(t,e){function i(t){var i={},a=[];return f(t,function(o){var s=n(i,o),l=s.originalDeps=e(o),u=r(l,t);s.entryCount=u.length,0===s.entryCount&&a.push(o),f(u,function(t){h(s.predecessor,t)<0&&s.predecessor.push(t);var e=n(i,t);h(e.successor,t)<0&&e.successor.push(o)})}),{graph:i,noEntryList:a}}function n(t,e){return t[e]||(t[e]={predecessor:[],successor:[]}),t[e]}function r(t,e){var i=[];return f(t,function(t){h(e,t)>=0&&i.push(t)}),i}t.topologicalTravel=function(t,e,n,r){function a(t){l[t].entryCount--,0===l[t].entryCount&&h.push(t)}function o(t){u[t]=!0,a(t)}if(t.length){var s=i(e),l=s.graph,h=s.noEntryList,u={};for(f(t,function(t){u[t]=!0});h.length;){var c=h.pop(),d=l[c],p=!!u[c];p&&(n.call(r,c,d.originalDeps.slice()),delete u[c]),f(d.successor,p?o:a)}f(u,function(){throw new Error("Circle dependency may exists")})}}}function _o(t){return t.replace(/^\s+|\s+$/g,"")}function wo(t,e,i,n){var r=e[1]-e[0],a=i[1]-i[0];if(0===r)return 0===a?i[0]:(i[0]+i[1])/2;if(n)if(r>0){if(t<=e[0])return i[0];if(t>=e[1])return i[1]}else{if(t>=e[0])return i[0];if(t<=e[1])return i[1]}else{if(t===e[0])return i[0];if(t===e[1])return i[1]}return(t-e[0])/r*a+i[0]}function bo(t,e){switch(t){case"center":case"middle":t="50%";break;case"left":case"top":t="0%";break;case"right":case"bottom":t="100%"}return"string"==typeof t?_o(t).match(/%$/)?parseFloat(t)/100*e:parseFloat(t):null==t?0/0:+t}function Mo(t,e,i){return null==e&&(e=10),e=Math.min(Math.max(0,e),20),t=(+t).toFixed(e),i?t:+t}function So(t){return t.sort(function(t,e){return t-e}),t}function Ao(t){if(t=+t,isNaN(t))return 0;for(var e=1,i=0;Math.round(t*e)/e!==t;)e*=10,i++;return i}function Io(t){var e=t.toString(),i=e.indexOf("e");if(i>0){var n=+e.slice(i+1);return 0>n?-n:0}var r=e.indexOf(".");return 0>r?0:e.length-1-r}function To(t,e){var i=Math.log,n=Math.LN10,r=Math.floor(i(t[1]-t[0])/n),a=Math.round(i(Math.abs(e[1]-e[0]))/n),o=Math.min(Math.max(-r+a,0),20);return isFinite(o)?o:20}function Co(t,e,i){if(!t[e])return 0;var n=g(t,function(t,e){return t+(isNaN(e)?0:e)},0);if(0===n)return 0;for(var r=Math.pow(10,i),a=p(t,function(t){return(isNaN(t)?0:t)/n*r*100}),o=100*r,s=p(a,function(t){return Math.floor(t)}),l=g(s,function(t,e){return t+e},0),h=p(a,function(t,e){return t-s[e]});o>l;){for(var u=Number.NEGATIVE_INFINITY,c=null,d=0,f=h.length;f>d;++d)h[d]>u&&(u=h[d],c=d);++s[c],h[c]=0,++l}return s[e]/r}function Do(t){var e=2*Math.PI;return(t%e+e)%e}function ko(t){return t>-Yx&&Yx>t}function Po(t){if(t instanceof Date)return t;if("string"==typeof t){var e=qx.exec(t);if(!e)return new Date(0/0);if(e[8]){var i=+e[4]||0;return"Z"!==e[8].toUpperCase()&&(i-=e[8].slice(0,3)),new Date(Date.UTC(+e[1],+(e[2]||1)-1,+e[3]||1,i,+(e[5]||0),+e[6]||0,+e[7]||0))}return new Date(+e[1],+(e[2]||1)-1,+e[3]||1,+e[4]||0,+(e[5]||0),+e[6]||0,+e[7]||0)}return new Date(null==t?0/0:Math.round(t))}function Lo(t){return Math.pow(10,Oo(t))}function Oo(t){if(0===t)return 0;var e=Math.floor(Math.log(t)/Math.LN10);return t/Math.pow(10,e)>=10&&e++,e}function Eo(t,e){var i,n=Oo(t),r=Math.pow(10,n),a=t/r;return i=e?1.5>a?1:2.5>a?2:4>a?3:7>a?5:10:1>a?1:2>a?2:3>a?3:5>a?5:10,t=i*r,n>=-20?+t.toFixed(0>n?-n:0):t}function zo(t,e){var i=(t.length-1)*e+1,n=Math.floor(i),r=+t[n-1],a=i-n;return a?r+a*(t[n]-r):r}function Bo(t){function e(t,i,n){return t.interval[n]<i.interval[n]||t.interval[n]===i.interval[n]&&(t.close[n]-i.close[n]===(n?-1:1)||!n&&e(t,i,1))}t.sort(function(t,i){return e(t,i,0)?-1:1});for(var i=-1/0,n=1,r=0;r<t.length;){for(var a=t[r].interval,o=t[r].close,s=0;2>s;s++)a[s]<=i&&(a[s]=i,o[s]=s?1:1-n),i=a[s],n=o[s];a[0]===a[1]&&o[0]*o[1]!==1?t.splice(r,1):r++}return t}function Ro(t){return t-parseFloat(t)>=0}function No(t){return isNaN(t)?"-":(t=(t+"").split("."),t[0].replace(/(\d{1,3})(?=(?:\d{3})+(?!\d))/g,"$1,")+(t.length>1?"."+t[1]:""))}function Fo(t,e){return t=(t||"").toLowerCase().replace(/-(.)/g,function(t,e){return e.toUpperCase()}),e&&t&&(t=t.charAt(0).toUpperCase()+t.slice(1)),t}function Vo(t){return null==t?"":(t+"").replace($x,function(t,e){return Qx[e]})}function Wo(t,e,i){_(e)||(e=[e]);var n=e.length;if(!n)return"";for(var r=e[0].$vars||[],a=0;a<r.length;a++){var o=Jx[a];t=t.replace(t_(o),t_(o,0))}for(var s=0;n>s;s++)for(var l=0;l<r.length;l++){var h=e[s][r[l]];t=t.replace(t_(Jx[l],s),i?Vo(h):h)}return t}function Ho(t,e,i){return f(e,function(e,n){t=t.replace("{"+n+"}",i?Vo(e):e)}),t}function Go(t,e){t=b(t)?{color:t,extraCssText:e}:t||{};var i=t.color,n=t.type,e=t.extraCssText,r=t.renderMode||"html",a=t.markerId||"X";return i?"html"===r?"subItem"===n?'<span style="display:inline-block;vertical-align:middle;margin-right:8px;margin-left:3px;border-radius:4px;width:4px;height:4px;background-color:'+Vo(i)+";"+(e||"")+'"></span>':'<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:'+Vo(i)+";"+(e||"")+'"></span>':{renderMode:r,content:"{marker"+a+"|}  ",style:{color:i}}:""}function Zo(t,e){return t+="","0000".substr(0,e-t.length)+t}function Xo(t,e,i){("week"===t||"month"===t||"quarter"===t||"half-year"===t||"year"===t)&&(t="MM-dd\nyyyy");var n=Po(e),r=i?"UTC":"",a=n["get"+r+"FullYear"](),o=n["get"+r+"Month"]()+1,s=n["get"+r+"Date"](),l=n["get"+r+"Hours"](),h=n["get"+r+"Minutes"](),u=n["get"+r+"Seconds"](),c=n["get"+r+"Milliseconds"]();return t=t.replace("MM",Zo(o,2)).replace("M",o).replace("yyyy",a).replace("yy",a%100).replace("dd",Zo(s,2)).replace("d",s).replace("hh",Zo(l,2)).replace("h",l).replace("mm",Zo(h,2)).replace("m",h).replace("ss",Zo(u,2)).replace("s",u).replace("SSS",Zo(c,3))}function Yo(t){return t?t.charAt(0).toUpperCase()+t.substr(1):t}function Uo(t){return Xi(t.text,t.font,t.textAlign,t.textVerticalAlign,t.textPadding,t.textLineHeight,t.rich,t.truncate)}function qo(t,e,i,n,r,a,o,s){return Xi(t,e,i,n,r,s,a,o)}function jo(t,e){if("_blank"===e||"blank"===e){var i=window.open();i.opener=null,i.location=t}else window.open(t,e)}function Ko(t,e,i,n,r){var a=0,o=0;null==n&&(n=1/0),null==r&&(r=1/0);var s=0;e.eachChild(function(l,h){var u,c,d=l.position,f=l.getBoundingRect(),p=e.childAt(h+1),g=p&&p.getBoundingRect();if("horizontal"===t){var v=f.width+(g?-g.x+f.x:0);u=a+v,u>n||l.newline?(a=0,u=v,o+=s+i,s=f.height):s=Math.max(s,f.height)}else{var m=f.height+(g?-g.y+f.y:0);c=o+m,c>r||l.newline?(a+=s+i,o=0,c=m,s=f.width):s=Math.max(s,f.width)}l.newline||(d[0]=a,d[1]=o,"horizontal"===t?a=u+i:o=c+i)})}function $o(t,e,i){i=Kx(i||0);var n=e.width,r=e.height,a=bo(t.left,n),o=bo(t.top,r),s=bo(t.right,n),l=bo(t.bottom,r),h=bo(t.width,n),u=bo(t.height,r),c=i[2]+i[0],d=i[1]+i[3],f=t.aspect;switch(isNaN(h)&&(h=n-s-d-a),isNaN(u)&&(u=r-l-c-o),null!=f&&(isNaN(h)&&isNaN(u)&&(f>n/r?h=.8*n:u=.8*r),isNaN(h)&&(h=f*u),isNaN(u)&&(u=h/f)),isNaN(a)&&(a=n-s-h-d),isNaN(o)&&(o=r-l-u-c),t.left||t.right){case"center":a=n/2-h/2-i[3];break;case"right":a=n-h-d}switch(t.top||t.bottom){case"middle":case"center":o=r/2-u/2-i[0];break;case"bottom":o=r-u-c}a=a||0,o=o||0,isNaN(h)&&(h=n-d-a-(s||0)),isNaN(u)&&(u=r-c-o-(l||0));var p=new Ii(a+i[3],o+i[0],h,u);return p.margin=i,p}function Qo(t,e,i){function n(i,n){var o={},l=0,h={},u=0,c=2;if(n_(i,function(e){h[e]=t[e]}),n_(i,function(t){r(e,t)&&(o[t]=h[t]=e[t]),a(o,t)&&l++,a(h,t)&&u++}),s[n])return a(e,i[1])?h[i[2]]=null:a(e,i[2])&&(h[i[1]]=null),h;if(u!==c&&l){if(l>=c)return o;for(var d=0;d<i.length;d++){var f=i[d];if(!r(o,f)&&r(t,f)){o[f]=t[f];break}}return o}return h}function r(t,e){return t.hasOwnProperty(e)}function a(t,e){return null!=t[e]&&"auto"!==t[e]}function o(t,e,i){n_(t,function(t){e[t]=i[t]})}!M(i)&&(i={});var s=i.ignoreSize;!_(s)&&(s=[s,s]);var l=n(a_[0],0),h=n(a_[1],1);o(a_[0],t,l),o(a_[1],t,h)}function Jo(t){return ts({},t)}function ts(t,e){return e&&t&&n_(r_,function(i){e.hasOwnProperty(i)&&(t[i]=e[i])}),t}function es(t){var e=[];return f(l_.getClassesByMainType(t),function(t){e=e.concat(t.prototype.dependencies||[])}),e=p(e,function(t){return gr(t).main}),"dataset"!==t&&h(e,"dataset")<=0&&e.unshift("dataset"),e}function is(t,e){for(var i=t.length,n=0;i>n;n++)if(t[n].length>e)return t[n];return t[i-1]}function ns(t){this.fromDataset=t.fromDataset,this.data=t.data||(t.sourceFormat===v_?{}:[]),this.sourceFormat=t.sourceFormat||m_,this.seriesLayoutBy=t.seriesLayoutBy||x_,this.dimensionsDefine=t.dimensionsDefine,this.encodeDefine=t.encodeDefine&&N(t.encodeDefine),this.startIndex=t.startIndex||0,this.dimensionsDetectCount=t.dimensionsDetectCount}function rs(t){var e=t.option.source,i=m_;if(A(e))i=y_;else if(_(e)){0===e.length&&(i=p_);for(var n=0,r=e.length;r>n;n++){var a=e[n];if(null!=a){if(_(a)){i=p_;break}if(M(a)){i=g_;break}}}}else if(M(e)){for(var o in e)if(e.hasOwnProperty(o)&&d(e[o])){i=v_;break}}else if(null!=e)throw new Error("Invalid data");b_(t).sourceFormat=i}function as(t){return b_(t).source}function os(t){b_(t).datasetMap=N()}function ss(t){var e=t.option,i=e.data,n=A(i)?y_:f_,r=!1,a=e.seriesLayoutBy,o=e.sourceHeader,s=e.dimensions,l=ps(t);if(l){var h=l.option;i=h.source,n=b_(l).sourceFormat,r=!0,a=a||h.seriesLayoutBy,null==o&&(o=h.sourceHeader),s=s||h.dimensions}var u=ls(i,n,a,o,s);b_(t).source=new ns({data:i,fromDataset:r,seriesLayoutBy:a,sourceFormat:n,dimensionsDefine:u.dimensionsDefine,startIndex:u.startIndex,dimensionsDetectCount:u.dimensionsDetectCount,encodeDefine:e.encode})}function ls(t,e,i,n,r){if(!t)return{dimensionsDefine:hs(r)};var a,o;if(e===p_)"auto"===n||null==n?us(function(t){null!=t&&"-"!==t&&(b(t)?null==o&&(o=1):o=0)},i,t,10):o=n?1:0,r||1!==o||(r=[],us(function(t,e){r[e]=null!=t?t:""},i,t)),a=r?r.length:i===__?t.length:t[0]?t[0].length:null;else if(e===g_)r||(r=cs(t));else if(e===v_)r||(r=[],f(t,function(t,e){r.push(e)}));else if(e===f_){var s=ir(t[0]);a=_(s)&&s.length||1}else e===y_&&wg&&O(!!r,"dimensions must be given if data is TypedArray.");return{startIndex:o,dimensionsDefine:hs(r),dimensionsDetectCount:a}}function hs(t){if(t){var e=N();return p(t,function(t){if(t=o({},M(t)?t:{name:t}),null==t.name)return t;t.name+="",null==t.displayName&&(t.displayName=t.name);var i=e.get(t.name);return i?t.name+="-"+i.count++:e.set(t.name,{count:1}),t})}}function us(t,e,i,n){if(null==n&&(n=1/0),e===__)for(var r=0;r<i.length&&n>r;r++)t(i[r]?i[r][0]:null,r);else for(var a=i[0]||[],r=0;r<a.length&&n>r;r++)t(a[r],r)}function cs(t){for(var e,i=0;i<t.length&&!(e=t[i++]););if(e){var n=[];return f(e,function(t,e){n.push(e)}),n}}function ds(t,e,i){function n(t,e,i){for(var n=0;i>n;n++)t.push(e+n)}function r(t){var e=t.dimsDef;return e?e.length:1}var a={},o=ps(e);if(!o||!t)return a;var s,l,h=[],u=[],c=e.ecModel,d=b_(c).datasetMap,p=o.uid+"_"+i.seriesLayoutBy;t=t.slice(),f(t,function(e,i){!M(e)&&(t[i]={name:e}),"ordinal"===e.type&&null==s&&(s=i,l=r(t[i])),a[e.name]=[]});var g=d.get(p)||d.set(p,{categoryWayDim:l,valueWayDim:0});return f(t,function(t,e){var i=t.name,o=r(t);if(null==s){var l=g.valueWayDim;n(a[i],l,o),n(u,l,o),g.valueWayDim+=o}else if(s===e)n(a[i],0,o),n(h,0,o);else{var l=g.categoryWayDim;n(a[i],l,o),n(u,l,o),g.categoryWayDim+=o}}),h.length&&(a.itemName=h),u.length&&(a.seriesName=u),a}function fs(t,e,i){var n={},r=ps(t);if(!r)return n;var a,o=e.sourceFormat,s=e.dimensionsDefine;(o===g_||o===v_)&&f(s,function(t,e){"name"===(M(t)?t.name:t)&&(a=e)});var l=function(){function t(t){return null!=t.v&&null!=t.n}for(var n={},r={},l=[],h=0,u=Math.min(5,i);u>h;h++){var c=vs(e.data,o,e.seriesLayoutBy,s,e.startIndex,h);l.push(c);var d=c===w_.Not;if(d&&null==n.v&&h!==a&&(n.v=h),(null==n.n||n.n===n.v||!d&&l[n.n]===w_.Not)&&(n.n=h),t(n)&&l[n.n]!==w_.Not)return n;d||(c===w_.Might&&null==r.v&&h!==a&&(r.v=h),(null==r.n||r.n===r.v)&&(r.n=h))}return t(n)?n:t(r)?r:null}();if(l){n.value=l.v;var h=null!=a?a:l.n;n.itemName=[h],n.seriesName=[h]}return n}function ps(t){var e=t.option,i=e.data;return i?void 0:t.ecModel.getComponent("dataset",e.datasetIndex||0)}function gs(t,e){return vs(t.data,t.sourceFormat,t.seriesLayoutBy,t.dimensionsDefine,t.startIndex,e)}function vs(t,e,i,n,r,a){function o(t){var e=b(t);return null!=t&&isFinite(t)&&""!==t?e?w_.Might:w_.Not:e&&"-"!==t?w_.Must:void 0}var s,l=5;if(A(t))return w_.Not;var h,u;if(n){var c=n[a];M(c)?(h=c.name,u=c.type):b(c)&&(h=c)}if(null!=u)return"ordinal"===u?w_.Must:w_.Not;if(e===p_)if(i===__){for(var d=t[a],f=0;f<(d||[]).length&&l>f;f++)if(null!=(s=o(d[r+f])))return s}else for(var f=0;f<t.length&&l>f;f++){var p=t[r+f];if(p&&null!=(s=o(p[a])))return s}else if(e===g_){if(!h)return w_.Not;for(var f=0;f<t.length&&l>f;f++){var g=t[f];if(g&&null!=(s=o(g[h])))return s}}else if(e===v_){if(!h)return w_.Not;var d=t[h];if(!d||A(d))return w_.Not;for(var f=0;f<d.length&&l>f;f++)if(null!=(s=o(d[f])))return s}else if(e===f_)for(var f=0;f<t.length&&l>f;f++){var g=t[f],v=ir(g);if(!_(v))return w_.Not;if(null!=(s=o(v[a])))return s}return w_.Not}function ms(t,e){if(e){var i=e.seiresIndex,n=e.seriesId,r=e.seriesName;return null!=i&&t.componentIndex!==i||null!=n&&t.id!==n||null!=r&&t.name!==r}}function ys(t,e){var i=t.color&&!t.colorLayer;f(e,function(e,a){"colorLayer"===a&&i||l_.hasClass(a)||("object"==typeof e?t[a]=t[a]?r(t[a],e,!1):n(e):null==t[a]&&(t[a]=e))})}function xs(t){t=t,this.option={},this.option[M_]=1,this._componentsMap=N({series:[]}),this._seriesIndices,this._seriesIndicesMap,ys(t,this._theme.option),r(t,u_,!1),this.mergeOption(t)}function _s(t,e){_(e)||(e=e?[e]:[]);var i={};return f(e,function(e){i[e]=(t.get(e)||[]).slice()}),i}function ws(t,e,i){var n=e.type?e.type:i?i.subType:l_.determineSubType(t,e);return n}function bs(t,e){t._seriesIndicesMap=N(t._seriesIndices=p(e,function(t){return t.componentIndex})||[])}function Ms(t,e){return e.hasOwnProperty("subType")?v(t,function(t){return t.subType===e.subType}):t}function Ss(t){if(wg&&!t._seriesIndices)throw new Error("Option should contains series.")}function As(t){f(A_,function(e){this[e]=y(t[e],t)},this)}function Is(){this._coordinateSystems=[]}function Ts(t){this._api=t,this._timelineOptions=[],this._mediaList=[],this._mediaDefault,this._currentMediaIndices=[],this._optionBackup,this._newBaseOption}function Cs(t,e,i){var n,r,a=[],o=[],s=t.timeline;if(t.baseOption&&(r=t.baseOption),(s||t.options)&&(r=r||{},a=(t.options||[]).slice()),t.media){r=r||{};var l=t.media;T_(l,function(t){t&&t.option&&(t.query?o.push(t):n||(n=t))})}return r||(r=t),r.timeline||(r.timeline=s),T_([r].concat(a).concat(p(o,function(t){return t.option})),function(t){T_(e,function(e){e(t,i)})}),{baseOption:r,timelineOptions:a,mediaDefault:n,mediaList:o}}function Ds(t,e,i){var n={width:e,height:i,aspectratio:e/i},r=!0;return f(t,function(t,e){var i=e.match(P_);if(i&&i[1]&&i[2]){var a=i[1],o=i[2].toLowerCase();ks(n[o],t,a)||(r=!1)}}),r}function ks(t,e,i){return"min"===i?t>=e:"max"===i?e>=t:t===e}function Ps(t,e){return t.join(",")===e.join(",")}function Ls(t,e){e=e||{},T_(e,function(e,i){if(null!=e){var n=t[i];if(l_.hasClass(i)){e=tr(e),n=tr(n);var r=rr(n,e);t[i]=D_(r,function(t){return t.option&&t.exist?k_(t.exist,t.option,!0):t.exist||t.option})}else t[i]=k_(n,e,!0)}})}function Os(t){var e=t&&t.itemStyle;if(e)for(var i=0,n=E_.length;n>i;i++){var a=E_[i],o=e.normal,s=e.emphasis;o&&o[a]&&(t[a]=t[a]||{},t[a].normal?r(t[a].normal,o[a]):t[a].normal=o[a],o[a]=null),s&&s[a]&&(t[a]=t[a]||{},t[a].emphasis?r(t[a].emphasis,s[a]):t[a].emphasis=s[a],s[a]=null)}}function Es(t,e,i){if(t&&t[e]&&(t[e].normal||t[e].emphasis)){var n=t[e].normal,r=t[e].emphasis;n&&(i?(t[e].normal=t[e].emphasis=null,s(t[e],n)):t[e]=n),r&&(t.emphasis=t.emphasis||{},t.emphasis[e]=r)}}function zs(t){Es(t,"itemStyle"),Es(t,"lineStyle"),Es(t,"areaStyle"),Es(t,"label"),Es(t,"labelLine"),Es(t,"upperLabel"),Es(t,"edgeLabel")}function Bs(t,e){var i=O_(t)&&t[e],n=O_(i)&&i.textStyle;if(n)for(var r=0,a=Hm.length;a>r;r++){var e=Hm[r];n.hasOwnProperty(e)&&(i[e]=n[e])}}function Rs(t){t&&(zs(t),Bs(t,"label"),t.emphasis&&Bs(t.emphasis,"label"))}function Ns(t){if(O_(t)){Os(t),zs(t),Bs(t,"label"),Bs(t,"upperLabel"),Bs(t,"edgeLabel"),t.emphasis&&(Bs(t.emphasis,"label"),Bs(t.emphasis,"upperLabel"),Bs(t.emphasis,"edgeLabel"));var e=t.markPoint;e&&(Os(e),Rs(e));var i=t.markLine;i&&(Os(i),Rs(i));var n=t.markArea;n&&Rs(n);var r=t.data;if("graph"===t.type){r=r||t.nodes;var a=t.links||t.edges;if(a&&!A(a))for(var o=0;o<a.length;o++)Rs(a[o]);f(t.categories,function(t){zs(t)})}if(r&&!A(r))for(var o=0;o<r.length;o++)Rs(r[o]);var e=t.markPoint;if(e&&e.data)for(var s=e.data,o=0;o<s.length;o++)Rs(s[o]);var i=t.markLine;if(i&&i.data)for(var l=i.data,o=0;o<l.length;o++)_(l[o])?(Rs(l[o][0]),Rs(l[o][1])):Rs(l[o]);"gauge"===t.type?(Bs(t,"axisLabel"),Bs(t,"title"),Bs(t,"detail")):"treemap"===t.type?(Es(t.breadcrumb,"itemStyle"),f(t.levels,function(t){zs(t)})):"tree"===t.type&&zs(t.leaves)}}function Fs(t){return _(t)?t:t?[t]:[]}function Vs(t){return(_(t)?t[0]:t)||{}}function Ws(t,e){e=e.split(",");for(var i=t,n=0;n<e.length&&(i=i&&i[e[n]],null!=i);n++);return i}function Hs(t,e,i,n){e=e.split(",");for(var r,a=t,o=0;o<e.length-1;o++)r=e[o],null==a[r]&&(a[r]={}),a=a[r];(n||null==a[e[o]])&&(a[e[o]]=i)}function Gs(t){f(B_,function(e){e[0]in t&&!(e[1]in t)&&(t[e[1]]=t[e[0]])})}function Zs(t){f(t,function(e,i){var n=[],r=[0/0,0/0],a=[e.stackResultDimension,e.stackedOverDimension],o=e.data,s=e.isStackedByIndex,l=o.map(a,function(a,l,h){var u=o.get(e.stackedDimension,h);if(isNaN(u))return r;var c,d;s?d=o.getRawIndex(h):c=o.get(e.stackedByDimension,h);for(var f=0/0,p=i-1;p>=0;p--){var g=t[p];if(s||(d=g.data.rawIndexOf(g.stackedByDimension,c)),d>=0){var v=g.data.getByRawIndex(g.stackResultDimension,d);if(u>=0&&v>0||0>=u&&0>v){u+=v,f=v;break}}}return n[0]=u,n[1]=f,n});o.hostModel.setData(l),e.data=l})}function Xs(t,e){ns.isInstance(t)||(t=ns.seriesDataToSource(t)),this._source=t;var i=this._data=t.data,n=t.sourceFormat;if(n===y_){if(wg&&null==e)throw new Error("Typed array data must specify dimension size");this._offset=0,this._dimSize=e,this._data=i}var r=W_[n===p_?n+"_"+t.seriesLayoutBy:n];wg&&O(r,"Invalide sourceFormat: "+n),o(this,r)}function Ys(){return this._data.length}function Us(t){return this._data[t]}function qs(t){for(var e=0;e<t.length;e++)this._data.push(t[e])}function js(t,e,i){return null!=i?t[i]:t}function Ks(t,e,i,n){return $s(t[n],this._dimensionInfos[e])}function $s(t,e){var i=e&&e.type;if("ordinal"===i){var n=e&&e.ordinalMeta;return n?n.parseAndCollect(t):t}return"time"===i&&"number"!=typeof t&&null!=t&&"-"!==t&&(t=+Po(t)),null==t||""===t?0/0:+t}function Qs(t,e,i){if(t){var n=t.getRawDataItem(e);if(null!=n){var r,a,o=t.getProvider().getSource().sourceFormat,s=t.getDimensionInfo(i);return s&&(r=s.name,a=s.index),H_[o](n,e,a,r)}}}function Js(t,e,i){if(t){var n=t.getProvider().getSource().sourceFormat;if(n===f_||n===g_){var r=t.getRawDataItem(e);return n!==f_||M(r)||(r=null),r?r[i]:void 0}}}function tl(t){return new el(t)}function el(t){t=t||{},this._reset=t.reset,this._plan=t.plan,this._count=t.count,this._onDirty=t.onDirty,this._dirty=!0,this.context}function il(t,e,i,n,r,a){U_.reset(i,n,r,a),t._callingProgress=e,t._callingProgress({start:i,end:n,count:n-i,next:U_.next},t.context)}function nl(t,e){t._dueIndex=t._outputDueEnd=t._dueEnd=0,t._settedOutputEnd=null;var i,n;!e&&t._reset&&(i=t._reset(t.context),i&&i.progress&&(n=i.forceFirstProgress,i=i.progress),_(i)&&!i.length&&(i=null)),t._progress=i,t._modBy=t._modDataCount=null;var r=t._downstream;return r&&r.dirty(),n}function rl(t){var e=t.name;or(t)||(t.name=al(t)||e)}function al(t){var e=t.getRawData(),i=e.mapDimension("seriesName",!0),n=[];return f(i,function(t){var i=e.getDimensionInfo(t);i.displayName&&n.push(i.displayName)}),n.join(" ")}function ol(t){return t.model.getRawData().count()}function sl(t){var e=t.model;return e.setData(e.getRawData().cloneShallow()),ll}function ll(t,e){e.outputData&&t.end>e.outputData.count()&&e.model.getRawData().cloneShallow(e.outputData)}function hl(t,e){f(t.CHANGABLE_METHODS,function(i){t.wrapMethod(i,x(ul,e))})}function ul(t){var e=cl(t);e&&e.setOutputEnd(this.count())}function cl(t){var e=(t.ecModel||{}).scheduler,i=e&&e.getPipeline(t.uid);if(i){var n=i.currentTask;if(n){var r=n.agentStubMap;r&&(n=r.get(t.uid))}return n}}function dl(){this.group=new Fv,this.uid=mo("viewChart"),this.renderTask=tl({plan:gl,reset:vl}),this.renderTask.context={view:this}}function fl(t,e,i){if(t&&(t.trigger(e,i),t.isGroup&&!Va(t)))for(var n=0,r=t.childCount();r>n;n++)fl(t.childAt(n),e,i)}function pl(t,e,i){var n=lr(t,e),r=e&&null!=e.highlightKey?Wa(e.highlightKey):null;null!=n?f(tr(n),function(e){fl(t.getItemGraphicEl(e),i,r)}):t.eachItemGraphicEl(function(t){fl(t,i,r)})}function gl(t){return tw(t.model)}function vl(t){var e=t.model,i=t.ecModel,n=t.api,r=t.payload,a=e.pipelineContext.progressiveRender,o=t.view,s=r&&J_(r).updateMethod,l=a?"incrementalPrepareRender":s&&o[s]?s:"render";return"render"!==l&&o[l](e,i,n,r),iw[l]}function ml(t,e,i){function n(){u=(new Date).getTime(),c=null,t.apply(o,s||[])}var r,a,o,s,l,h=0,u=0,c=null;e=e||0;var d=function(){r=(new Date).getTime(),o=this,s=arguments;var t=l||e,d=l||i;l=null,a=r-(d?h:u)-t,clearTimeout(c),d?c=setTimeout(n,t):a>=0?n():c=setTimeout(n,-a),h=r};return d.clear=function(){c&&(clearTimeout(c),c=null)},d.debounceNextCall=function(t){l=t},d}function yl(t,e,i,n){var r=t[e];if(r){var a=r[nw]||r,o=r[aw],s=r[rw];if(s!==i||o!==n){if(null==i||!n)return t[e]=a;r=t[e]=ml(a,i,"debounce"===n),r[nw]=a,r[aw]=n,r[rw]=i}return r}}function xl(t,e){var i=t[e];i&&i[nw]&&(t[e]=i[nw])}function _l(t,e,i,n){this.ecInstance=t,this.api=e,this.unfinished;var i=this._dataProcessorHandlers=i.slice(),n=this._visualHandlers=n.slice();this._allHandlers=i.concat(n),this._stageTaskMap=N()}function wl(t,e,i,n,r){function a(t,e){return t.setDirty&&(!t.dirtyMap||t.dirtyMap.get(e.__pipeline.id))}r=r||{};var o;f(e,function(e){if(!r.visualType||r.visualType===e.visualType){var s=t._stageTaskMap.get(e.uid),l=s.seriesTaskMap,h=s.overallTask;if(h){var u,c=h.agentStubMap;c.each(function(t){a(r,t)&&(t.dirty(),u=!0)}),u&&h.dirty(),dw(h,n);var d=t.getPerformArgs(h,r.block);c.each(function(t){t.perform(d)}),o|=h.perform(d)}else l&&l.each(function(s){a(r,s)&&s.dirty();var l=t.getPerformArgs(s,r.block);l.skip=!e.performRawSeries&&i.isSeriesFiltered(s.context.model),dw(s,n),o|=s.perform(l)})}}),t.unfinished|=o}function bl(t,e,i,n,r){function a(i){var a=i.uid,s=o.get(a)||o.set(a,tl({plan:Cl,reset:Dl,count:Pl}));s.context={model:i,ecModel:n,api:r,useClearVisual:e.isVisual&&!e.isLayout,plan:e.plan,reset:e.reset,scheduler:t},Ll(t,i,s)}var o=i.seriesTaskMap||(i.seriesTaskMap=N()),s=e.seriesType,l=e.getTargetSeries;e.createOnAllSeries?n.eachRawSeries(a):s?n.eachRawSeriesByType(s,a):l&&l(n,r).each(a);var h=t._pipelineMap;o.each(function(t,e){h.get(e)||(t.dispose(),o.removeKey(e))})}function Ml(t,e,i,n,r){function a(e){var i=e.uid,n=s.get(i);n||(n=s.set(i,tl({reset:Al,onDirty:Tl})),o.dirty()),n.context={model:e,overallProgress:u,modifyOutputEnd:c},n.agent=o,n.__block=u,Ll(t,e,n)}var o=i.overallTask=i.overallTask||tl({reset:Sl});o.context={ecModel:n,api:r,overallReset:e.overallReset,scheduler:t};var s=o.agentStubMap=o.agentStubMap||N(),l=e.seriesType,h=e.getTargetSeries,u=!0,c=e.modifyOutputEnd;l?n.eachRawSeriesByType(l,a):h?h(n,r).each(a):(u=!1,f(n.getSeries(),a));var d=t._pipelineMap;s.each(function(t,e){d.get(e)||(t.dispose(),o.dirty(),s.removeKey(e))})}function Sl(t){t.overallReset(t.ecModel,t.api,t.payload)}function Al(t){return t.overallProgress&&Il}function Il(){this.agent.dirty(),this.getDownstream().dirty()}function Tl(){this.agent&&this.agent.dirty()}function Cl(t){return t.plan&&t.plan(t.model,t.ecModel,t.api,t.payload)}function Dl(t){t.useClearVisual&&t.data.clearAllVisual();var e=t.resetDefines=tr(t.reset(t.model,t.ecModel,t.api,t.payload));return e.length>1?p(e,function(t,e){return kl(e)}):fw}function kl(t){return function(e,i){var n=i.data,r=i.resetDefines[t];if(r&&r.dataEach)for(var a=e.start;a<e.end;a++)r.dataEach(n,a);else r&&r.progress&&r.progress(e,n)}}function Pl(t){return t.data.count()}function Ll(t,e,i){var n=e.uid,r=t._pipelineMap.get(n);!r.head&&(r.head=i),r.tail&&r.tail.pipe(i),r.tail=i,i.__idxInPipeline=r.count++,i.__pipeline=r}function Ol(t){pw=null;try{t(gw,vw)}catch(e){}return pw}function El(t,e){for(var i in e.prototype)t[i]=V}function zl(t){if(b(t)){var e=new DOMParser;t=e.parseFromString(t,"text/xml")}for(9===t.nodeType&&(t=t.firstChild);"svg"!==t.nodeName.toLowerCase()||1!==t.nodeType;)t=t.nextSibling;return t}function Bl(){this._defs={},this._root=null,this._isDefine=!1,this._isText=!1}function Rl(t,e){for(var i=t.firstChild;i;){if(1===i.nodeType){var n=i.getAttribute("offset");n=n.indexOf("%")>0?parseInt(n,10)/100:n?parseFloat(n):0;var r=i.getAttribute("stop-color")||"#000000";e.addColorStop(n,r)}i=i.nextSibling}}function Nl(t,e){t&&t.__inheritedStyle&&(e.__inheritedStyle||(e.__inheritedStyle={}),s(e.__inheritedStyle,t.__inheritedStyle))}function Fl(t){for(var e=E(t).split(Sw),i=[],n=0;n<e.length;n+=2){var r=parseFloat(e[n]),a=parseFloat(e[n+1]);i.push([r,a])}return i}function Vl(t,e,i,n){var r=e.__inheritedStyle||{},a="text"===e.type;if(1===t.nodeType&&(Hl(t,e),o(r,Gl(t)),!n))for(var s in Tw)if(Tw.hasOwnProperty(s)){var l=t.getAttribute(s);null!=l&&(r[Tw[s]]=l)}var h=a?"textFill":"fill",u=a?"textStroke":"stroke";e.style=e.style||new qv;var c=e.style;null!=r.fill&&c.set(h,Wl(r.fill,i)),null!=r.stroke&&c.set(u,Wl(r.stroke,i)),f(["lineWidth","opacity","fillOpacity","strokeOpacity","miterLimit","fontSize"],function(t){var e="lineWidth"===t&&a?"textStrokeWidth":t;null!=r[t]&&c.set(e,parseFloat(r[t]))}),r.textBaseline&&"auto"!==r.textBaseline||(r.textBaseline="alphabetic"),"alphabetic"===r.textBaseline&&(r.textBaseline="bottom"),"start"===r.textAlign&&(r.textAlign="left"),"end"===r.textAlign&&(r.textAlign="right"),f(["lineDashOffset","lineCap","lineJoin","fontWeight","fontFamily","fontStyle","textAlign","textBaseline"],function(t){null!=r[t]&&c.set(t,r[t])}),r.lineDash&&(e.style.lineDash=E(r.lineDash).split(Sw)),c[u]&&"none"!==c[u]&&(e[u]=!0),e.__inheritedStyle=r}function Wl(t,e){var i=e&&t&&t.match(Cw);if(i){var n=E(i[1]),r=e[n];return r}return t}function Hl(t,e){var i=t.getAttribute("transform");if(i){i=i.replace(/,/g," ");var n=null,r=[];i.replace(Dw,function(t,e,i){r.push(e,i)});for(var a=r.length-1;a>0;a-=2){var o=r[a],s=r[a-1];switch(n=n||Oe(),s){case"translate":o=E(o).split(Sw),Re(n,n,[parseFloat(o[0]),parseFloat(o[1]||0)]);break;case"scale":o=E(o).split(Sw),Fe(n,n,[parseFloat(o[0]),parseFloat(o[1]||o[0])]);break;case"rotate":o=E(o).split(Sw),Ne(n,n,parseFloat(o[0]));break;case"skew":o=E(o).split(Sw),console.warn("Skew transform is not supported yet");break;case"matrix":var o=E(o).split(Sw);n[0]=parseFloat(o[0]),n[1]=parseFloat(o[1]),n[2]=parseFloat(o[2]),n[3]=parseFloat(o[3]),n[4]=parseFloat(o[4]),n[5]=parseFloat(o[5])}}e.setLocalTransform(n)}}function Gl(t){var e=t.getAttribute("style"),i={};if(!e)return i;var n={};kw.lastIndex=0;for(var r;null!=(r=kw.exec(e));)n[r[1]]=r[2];for(var a in Tw)Tw.hasOwnProperty(a)&&null!=n[a]&&(i[Tw[a]]=n[a]);return i}function Zl(t,e,i){var n=e/t.width,r=i/t.height,a=Math.min(n,r),o=[a,a],s=[-(t.x+t.width/2)*a+e/2,-(t.y+t.height/2)*a+i/2];return{scale:o,position:s}}function Xl(t,e){return function(i,n,r){return!e&&this._disposed?void lh(this.id):(i=i&&i.toLowerCase(),void Ug.prototype[t].call(this,i,n,r))}}function Yl(){Ug.call(this)}function Ul(t,e,i){function r(t,e){return t.__prio-e.__prio}i=i||{},"string"==typeof e&&(e=db[e]),this.id,this.group,this._dom=t;var a="canvas";wg&&(a=("undefined"==typeof window?global:window).__ECHARTS__DEFAULT__RENDERER__||a);var o=this._zr=jn(t,{renderer:i.renderer||a,devicePixelRatio:i.devicePixelRatio,width:i.width,height:i.height});this._throttledZrFlush=ml(y(o.flush,o),17);var e=n(e);e&&N_(e,!0),this._theme=e,this._chartsViews=[],this._chartsMap={},this._componentsViews=[],this._componentsMap={},this._coordSysMgr=new Is;var s=this._api=dh(this);Ei(cb,r),Ei(lb,r),this._scheduler=new _l(this,s,lb,cb),Ug.call(this,this._ecEventProcessor=new fh),this._messageCenter=new Yl,this._initEvents(),this.resize=y(this.resize,this),this._pendingActions=[],o.animation.on("frame",this._onframe,this),eh(o,this),z(this)}function ql(t,e,i){if(this._disposed)return void lh(this.id);var n,r=this._model,a=this._coordSysMgr.getCoordinateSystems();e=ur(r,e);for(var o=0;o<a.length;o++){var s=a[o];if(s[t]&&null!=(n=s[t](r,e,i)))return n}wg&&console.warn("No coordinate system that supports "+t+" found by the given finder.")}function jl(t){var e=t._model,i=t._scheduler;i.restorePipelines(e),i.prepareStageTasks(),ih(t,"component",e,i),ih(t,"chart",e,i),i.plan()}function Kl(t,e,i,n,r){function a(n){n&&n.__alive&&n[e]&&n[e](n.__model,o,t._api,i)}var o=t._model;if(!n)return void zw(t._componentsViews.concat(t._chartsViews),a);var s={};s[n+"Id"]=i[n+"Id"],s[n+"Index"]=i[n+"Index"],s[n+"Name"]=i[n+"Name"];var l={mainType:n,query:s};r&&(l.subType=r);var h=i.excludeSeriesId;null!=h&&(h=N(tr(h))),o&&o.eachComponent(l,function(e){h&&null!=h.get(e.id)||a(t["series"===n?"_chartsMap":"_componentsMap"][e.__viewId])},t)}function $l(t,e){var i=t._chartsMap,n=t._scheduler;e.eachSeries(function(t){n.updateStreamModes(t,i[t.__viewId])})}function Ql(t,e){var i=t.type,n=t.escapeConnect,r=ob[i],a=r.actionInfo,l=(a.update||"update").split(":"),h=l.pop();l=null!=l[0]&&Nw(l[0]),this[tb]=!0;var u=[t],c=!1;t.batch&&(c=!0,u=p(t.batch,function(e){return e=s(o({},e),t),e.batch=null,e}));var d,f=[],g="highlight"===i||"downplay"===i;zw(u,function(t){d=r.action(t,this._model,this._api),d=d||o({},t),d.type=a.event||d.type,f.push(d),g?Kl(this,h,t,"series"):l&&Kl(this,h,t,l.main,l.sub)},this),"none"===h||g||l||(this[eb]?(jl(this),rb.update.call(this,t),this[eb]=!1):rb[h].call(this,t)),d=c?{type:a.event||i,escapeConnect:n,batch:f}:f[0],this[tb]=!1,!e&&this._messageCenter.trigger(d.type,d)}function Jl(t){for(var e=this._pendingActions;e.length;){var i=e.shift();Ql.call(this,i,t)}}function th(t){!t&&this.trigger("updated")}function eh(t,e){t.on("rendered",function(){e.trigger("rendered"),!t.animation.isFinished()||e[eb]||e._scheduler.unfinished||e._pendingActions.length||e.trigger("finished")})}function ih(t,e,i,n){function r(t){var e="_ec_"+t.id+"_"+t.type,r=s[e];if(!r){var u=Nw(t.type),c=a?K_.getClass(u.main,u.sub):dl.getClass(u.sub);wg&&Ew(c,u.sub+" does not exist."),r=new c,r.init(i,h),s[e]=r,o.push(r),l.add(r.group)}t.__viewId=r.__id=e,r.__alive=!0,r.__model=t,r.group.__ecComponentInfo={mainType:t.mainType,index:t.componentIndex},!a&&n.prepareView(r,t,i,h)}for(var a="component"===e,o=a?t._componentsViews:t._chartsViews,s=a?t._componentsMap:t._chartsMap,l=t._zr,h=t._api,u=0;u<o.length;u++)o[u].__alive=!1;a?i.eachComponent(function(t,e){"series"!==t&&r(e)}):i.eachSeries(r);for(var u=0;u<o.length;){var c=o[u];c.__alive?u++:(!a&&c.renderTask.dispose(),l.remove(c.group),c.dispose(i,h),o.splice(u,1),delete s[c.__id],c.__id=c.group.__ecComponentInfo=null)}}function nh(t){t.clearColorPalette(),t.eachSeries(function(t){t.clearColorPalette()})}function rh(t,e,i,n){ah(t,e,i,n),zw(t._chartsViews,function(t){t.__alive=!1}),oh(t,e,i,n),zw(t._chartsViews,function(t){t.__alive||t.remove(e,i)})}function ah(t,e,i,n,r){zw(r||t._componentsViews,function(t){var r=t.__model;t.render(r,e,i,n),ch(r,t)})}function oh(t,e,i,n,r){var a,o=t._scheduler;e.eachSeries(function(e){var i=t._chartsMap[e.__viewId];i.__alive=!0;var s=i.renderTask;o.updatePayload(s,n),r&&r.get(e.uid)&&s.dirty(),a|=s.perform(o.getPerformArgs(s)),i.group.silent=!!e.get("silent"),ch(e,i),uh(e,i)}),o.unfinished|=a,hh(t,e),lw(t._zr.dom,e)}function sh(t,e){zw(ub,function(i){i(t,e)})}function lh(t){wg&&console.warn("Instance "+t+" has been disposed")}function hh(t,e){var i=t._zr,n=i.storage,r=0;n.traverse(function(){r++}),r>e.get("hoverLayerThreshold")&&!Ag.node&&e.eachSeries(function(e){if(!e.preventUsingHoverLayer){var i=t._chartsMap[e.__viewId];
i.__alive&&i.group.traverse(function(t){t.useHoverLayer=!0})}})}function uh(t,e){var i=t.get("blendMode")||null;wg&&!Ag.canvasSupported&&i&&"source-over"!==i&&console.warn("Only canvas support blendMode"),e.group.traverse(function(t){t.isGroup||t.style.blend!==i&&t.setStyle("blend",i),t.eachPendingDisplayable&&t.eachPendingDisplayable(function(t){t.setStyle("blend",i)})})}function ch(t,e){var i=t.get("z"),n=t.get("zlevel");e.group.traverse(function(t){"group"!==t.type&&(null!=i&&(t.z=i),null!=n&&(t.zlevel=n))})}function dh(t){var e=t._coordSysMgr;return o(new As(t),{getCoordinateSystems:y(e.getCoordinateSystems,e),getComponentByElement:function(e){for(;e;){var i=e.__ecComponentInfo;if(null!=i)return t._model.getComponent(i.mainType,i.index);e=e.parent}}})}function fh(){this.eventInfo}function ph(t){function e(t,e){for(var i=0;i<t.length;i++){var n=t[i];n[a]=e}}var i=0,n=1,r=2,a="__connectUpdateStatus";zw(sb,function(o,s){t._messageCenter.on(s,function(o){if(gb[t.group]&&t[a]!==i){if(o&&o.escapeConnect)return;var s=t.makeActionFromEvent(o),l=[];zw(pb,function(e){e!==t&&e.group===t.group&&l.push(e)}),e(l,i),zw(l,function(t){t[a]!==n&&t.dispatchAction(s)}),e(l,r)}})})}function gh(t,e,i){if(wg){if(zm.replace(".","")-0<Vw.zrender.replace(".","")-0)throw new Error("zrender/src "+zm+" is too old for ECharts "+Fw+". Current version need ZRender "+Vw.zrender+"+");if(!t)throw new Error("Initialize failed: invalid dom.")}var n=xh(t);if(n)return wg&&console.warn("There is a chart instance already initialized on the dom."),n;wg&&(!I(t)||"CANVAS"===t.nodeName.toUpperCase()||(t.clientWidth||i&&null!=i.width)&&(t.clientHeight||i&&null!=i.height)||console.warn("Can't get DOM width or height. Please check dom.clientWidth and dom.clientHeight. They should not be 0.For example, you may need to call this in the callback of window.onload."));var r=new Ul(t,e,i);return r.id="ec_"+vb++,pb[r.id]=r,dr(t,yb,r.id),ph(r),r}function vh(t){if(_(t)){var e=t;t=null,zw(e,function(e){null!=e.group&&(t=e.group)}),t=t||"g_"+mb++,zw(e,function(e){e.group=t})}return gb[t]=!0,t}function mh(t){gb[t]=!1}function yh(t){"string"==typeof t?t=pb[t]:t instanceof Ul||(t=xh(t)),t instanceof Ul&&!t.isDisposed()&&t.dispose()}function xh(t){return pb[fr(t,yb)]}function _h(t){return pb[t]}function wh(t,e){db[t]=e}function bh(t){hb.push(t)}function Mh(t,e){kh(lb,t,e,Hw)}function Sh(t){ub.push(t)}function Ah(t,e,i){"function"==typeof e&&(i=e,e="");var n=Rw(t)?t.type:[t,t={event:e}][0];t.event=(t.event||n).toLowerCase(),e=t.event,Ew(ib.test(n)&&ib.test(e)),ob[n]||(ob[n]={action:i,actionInfo:t}),sb[e]=n}function Ih(t,e){Is.register(t,e)}function Th(t){var e=Is.get(t);return e?e.getDimensionsInfo?e.getDimensionsInfo():e.dimensions.slice():void 0}function Ch(t,e){kh(cb,t,e,Yw,"layout")}function Dh(t,e){kh(cb,t,e,jw,"visual")}function kh(t,e,i,n,r){if((Bw(e)||Rw(e))&&(i=e,e=n),wg){if(isNaN(e)||null==e)throw new Error("Illegal priority");zw(t,function(t){Ew(t.__raw!==i)})}var a=_l.wrapStageHandler(i,r);return a.__prio=e,a.__raw=i,t.push(a),a}function Ph(t,e){fb[t]=e}function Lh(t){return l_.extend(t)}function Oh(t){return K_.extend(t)}function Eh(t){return j_.extend(t)}function zh(t){return dl.extend(t)}function Bh(t){i("createCanvas",t)}function Rh(t,e,i){Lw.registerMap(t,e,i)}function Nh(t){var e=Lw.retrieveMap(t);return e&&e[0]&&{geoJson:e[0].geoJSON,specialAreas:e[0].specialAreas}}function Fh(t){return t}function Vh(t,e,i,n,r){this._old=t,this._new=e,this._oldKeyGetter=i||Fh,this._newKeyGetter=n||Fh,this.context=r}function Wh(t,e,i,n,r){for(var a=0;a<t.length;a++){var o="_ec_"+r[n](t[a],a),s=e[o];null==s?(i.push(o),e[o]=a):(s.length||(e[o]=s=[s]),s.push(a))}}function Hh(t){var e={},i=e.encode={},n=N(),r=[],a=[],o=e.userOutput={dimensionNames:t.dimensions.slice(),encode:{}};f(t.dimensions,function(e){var s=t.getDimensionInfo(e),l=s.coordDim;if(l){wg&&O(null==wb.get(l));var h=s.coordDimIndex;Gh(i,l)[h]=e,s.isExtraCoord||(n.set(l,1),Xh(s.type)&&(r[0]=e),Gh(o.encode,l)[h]=s.index),s.defaultTooltip&&a.push(e)}wb.each(function(t,e){var n=Gh(i,e),r=s.otherDims[e];null!=r&&r!==!1&&(n[r]=s.name)})});var s=[],l={};n.each(function(t,e){var n=i[e];l[e]=n[0],s=s.concat(n)}),e.dataDimsOnCoord=s,e.encodeFirstDimNotExtra=l;var h=i.label;h&&h.length&&(r=h.slice());var u=i.tooltip;return u&&u.length?a=u.slice():a.length||(a=r.slice()),i.defaultedLabel=r,i.defaultedTooltip=a,e}function Gh(t,e){return t.hasOwnProperty(e)||(t[e]=[]),t[e]}function Zh(t){return"category"===t?"ordinal":"time"===t?"time":"float"}function Xh(t){return!("ordinal"===t||"time"===t)}function Yh(t){null!=t&&o(this,t),this.otherDims={}}function Uh(t){return t._rawCount>65535?Tb:Db}function qh(t){var e=t.constructor;return e===Array?t.slice():new e(t)}function jh(t,e){f(kb.concat(e.__wrappedMethods||[]),function(i){e.hasOwnProperty(i)&&(t[i]=e[i])}),t.__wrappedMethods=e.__wrappedMethods,f(Pb,function(i){t[i]=n(e[i])}),t._calculationInfo=o(e._calculationInfo)}function Kh(t,e,i,n,r){var a=Ib[e.type],o=n-1,s=e.name,l=t[s][o];if(l&&l.length<i){for(var h=new a(Math.min(r-o*i,i)),u=0;u<l.length;u++)h[u]=l[u];t[s][o]=h}for(var c=n*i;r>c;c+=i)t[s].push(new a(Math.min(r-c,i)))}function $h(t){var e=t._invertedIndicesMap;f(e,function(i,n){var r=t._dimensionInfos[n],a=r.ordinalMeta;if(a){i=e[n]=new Cb(a.categories.length);for(var o=0;o<i.length;o++)i[o]=Sb;for(var o=0;o<t._count;o++)i[t.get(n,o)]=o}})}function Qh(t,e,i){var n;if(null!=e){var r=t._chunkSize,a=Math.floor(i/r),o=i%r,s=t.dimensions[e],l=t._storage[s][a];if(l){n=l[o];var h=t._dimensionInfos[s].ordinalMeta;h&&h.categories.length&&(n=h.categories[n])}}return n}function Jh(t){return t}function tu(t){return t<this._count&&t>=0?this._indices[t]:-1}function eu(t,e){var i=t._idList[e];return null==i&&(i=Qh(t,t._idDimIdx,e)),null==i&&(i=Ab+e),i}function iu(t){return _(t)||(t=[t]),t}function nu(t,e){for(var i=0;i<e.length;i++)t._dimensionInfos[e[i]]||console.error("Unkown dimension "+e[i])}function ru(t,e){var i=t.dimensions,n=new Lb(p(i,t.getDimensionInfo,t),t.hostModel);jh(n,t);for(var r=n._storage={},a=t._storage,o=0;o<i.length;o++){var s=i[o];a[s]&&(h(e,s)>=0?(r[s]=au(a[s]),n._rawExtent[s]=ou(),n._extent[s]=null):r[s]=a[s])}return n}function au(t){for(var e=new Array(t.length),i=0;i<t.length;i++)e[i]=qh(t[i]);return e}function ou(){return[1/0,-1/0]}function su(t,e,i){function r(t,e,i){null!=wb.get(e)?t.otherDims[e]=i:(t.coordDim=e,t.coordDimIndex=i,h.set(e,!0))}ns.isInstance(e)||(e=ns.seriesDataToSource(e)),i=i||{},t=(t||[]).slice();for(var a=(i.dimsDef||[]).slice(),l=N(),h=N(),u=[],c=lu(e,t,a,i.dimCount),d=0;c>d;d++){var p=a[d]=o({},M(a[d])?a[d]:{name:a[d]}),g=p.name,v=u[d]=new Yh;null!=g&&null==l.get(g)&&(v.name=v.displayName=g,l.set(g,d)),null!=p.type&&(v.type=p.type),null!=p.displayName&&(v.displayName=p.displayName)}var m=i.encodeDef;!m&&i.encodeDefaulter&&(m=i.encodeDefaulter(e,c)),m=N(m),m.each(function(t,e){if(t=tr(t).slice(),1===t.length&&!b(t[0])&&t[0]<0)return void m.set(e,!1);var i=m.set(e,[]);f(t,function(t,n){b(t)&&(t=l.get(t)),null!=t&&c>t&&(i[n]=t,r(u[t],e,n))})});var y=0;f(t,function(t){var e,t,i,a;if(b(t))e=t,t={};else{e=t.name;var o=t.ordinalMeta;t.ordinalMeta=null,t=n(t),t.ordinalMeta=o,i=t.dimsDef,a=t.otherDims,t.name=t.coordDim=t.coordDimIndex=t.dimsDef=t.otherDims=null}var l=m.get(e);if(l!==!1){var l=tr(l);if(!l.length)for(var h=0;h<(i&&i.length||1);h++){for(;y<u.length&&null!=u[y].coordDim;)y++;y<u.length&&l.push(y++)}f(l,function(n,o){var l=u[n];if(r(s(l,t),e,o),null==l.name&&i){var h=i[o];!M(h)&&(h={name:h}),l.name=l.displayName=h.name,l.defaultTooltip=h.defaultTooltip}a&&s(l.otherDims,a)})}});var x=i.generateCoord,_=i.generateCoordCount,w=null!=_;_=x?_||1:0;for(var S=x||"value",A=0;c>A;A++){var v=u[A]=u[A]||new Yh,I=v.coordDim;null==I&&(v.coordDim=hu(S,h,w),v.coordDimIndex=0,(!x||0>=_)&&(v.isExtraCoord=!0),_--),null==v.name&&(v.name=hu(v.coordDim,l)),null!=v.type||gs(e,A,v.name)!==w_.Must&&(!v.isExtraCoord||null==v.otherDims.itemName&&null==v.otherDims.seriesName)||(v.type="ordinal")}return u}function lu(t,e,i,n){var r=Math.max(t.dimensionsDetectCount||1,e.length,i.length,n||0);return f(e,function(t){var e=t.dimsDef;e&&(r=Math.max(r,e.length))}),r}function hu(t,e,i){if(i||null!=e.get(t)){for(var n=0;null!=e.get(t+n);)n++;t+=n}return e.set(t,!0),t}function uu(t){this.coordSysName=t,this.coordSysDims=[],this.axisMap=N(),this.categoryAxisMap=N(),this.firstCategoryDimIndex=null}function cu(t){var e=t.get("coordinateSystem"),i=new uu(e),n=Bb[e];return n?(n(t,i,i.axisMap,i.categoryAxisMap),i):void 0}function du(t){return"category"===t.get("type")}function fu(t,e,i){i=i||{};var n,r,a,o,s=i.byIndex,l=i.stackedCoordDimension,h=!(!t||!t.get("stack"));if(f(e,function(t,i){b(t)&&(e[i]=t={name:t}),h&&!t.isExtraCoord&&(s||n||!t.ordinalMeta||(n=t),r||"ordinal"===t.type||"time"===t.type||l&&l!==t.coordDim||(r=t))}),!r||s||n||(s=!0),r){a="__\x00ecstackresult",o="__\x00ecstackedover",n&&(n.createInvertedIndices=!0);var u=r.coordDim,c=r.type,d=0;f(e,function(t){t.coordDim===u&&d++}),e.push({name:a,coordDim:u,coordDimIndex:d,type:c,isExtraCoord:!0,isCalculationCoord:!0}),d++,e.push({name:o,coordDim:o,coordDimIndex:d,type:c,isExtraCoord:!0,isCalculationCoord:!0})}return{stackedDimension:r&&r.name,stackedByDimension:n&&n.name,isStackedByIndex:s,stackedOverDimension:o,stackResultDimension:a}}function pu(t,e){return!!e&&e===t.getCalculationInfo("stackedDimension")}function gu(t,e){return pu(t,e)?t.getCalculationInfo("stackResultDimension"):e}function vu(t,e,i){i=i||{},ns.isInstance(t)||(t=ns.seriesDataToSource(t));var n,r=e.get("coordinateSystem"),a=Is.get(r),o=cu(e);o&&(n=p(o.coordSysDims,function(t){var e={name:t},i=o.axisMap.get(t);if(i){var n=i.get("type");e.type=Zh(n)}return e})),n||(n=a&&(a.getDimensionsInfo?a.getDimensionsInfo():a.dimensions.slice())||["x","y"]);var s,l,h=zb(t,{coordDimensions:n,generateCoord:i.generateCoord,encodeDefaulter:i.useEncodeDefaulter?x(ds,n,e):null});o&&f(h,function(t,e){var i=t.coordDim,n=o.categoryAxisMap.get(i);n&&(null==s&&(s=e),t.ordinalMeta=n.getOrdinalMeta()),null!=t.otherDims.itemName&&(l=!0)}),l||null==s||(h[s].otherDims.itemName=0);var u=fu(e,h),c=new Lb(h,e);c.setCalculationInfo(u);var d=null!=s&&mu(t)?function(t,e,i,n){return n===s?i:this.defaultDimValueGetter(t,e,i,n)}:null;return c.hasItemOption=!1,c.initData(t,null,d),c}function mu(t){if(t.sourceFormat===f_){var e=yu(t.data||[]);return null!=e&&!_(ir(e))}}function yu(t){for(var e=0;e<t.length&&null==t[e];)e++;return t[e]}function xu(t){this._setting=t||{},this._extent=[1/0,-1/0],this._interval=0,this.init&&this.init.apply(this,arguments)}function _u(t){this.categories=t.categories||[],this._needCollect=t.needCollect,this._deduplication=t.deduplication,this._map}function wu(t){return t._map||(t._map=N(t.categories))}function bu(t){return M(t)&&null!=t.value?t.value:t+""}function Mu(t,e,i,n){var r={},a=t[1]-t[0],o=r.interval=Eo(a/e,!0);null!=i&&i>o&&(o=r.interval=i),null!=n&&o>n&&(o=r.interval=n);var s=r.intervalPrecision=Su(o),l=r.niceTickExtent=[Vb(Math.ceil(t[0]/o)*o,s),Vb(Math.floor(t[1]/o)*o,s)];return Iu(l,t),r}function Su(t){return Io(t)+2}function Au(t,e,i){t[e]=Math.max(Math.min(t[e],i[1]),i[0])}function Iu(t,e){!isFinite(t[0])&&(t[0]=e[0]),!isFinite(t[1])&&(t[1]=e[1]),Au(t,0,e),Au(t,1,e),t[0]>t[1]&&(t[0]=t[1])}function Tu(t){return t.get("stack")||Gb+t.seriesIndex}function Cu(t){return t.dim+t.index}function Du(t,e){var i=[];return e.eachSeriesByType(t,function(t){zu(t)&&!Bu(t)&&i.push(t)}),i}function ku(t){var e={};f(t,function(t){var i=t.coordinateSystem,n=i.getBaseAxis();if("time"===n.type||"value"===n.type)for(var r=t.getData(),a=n.dim+"_"+n.index,o=r.mapDimension(n.dim),s=0,l=r.count();l>s;++s){var h=r.get(o,s);e[a]?e[a].push(h):e[a]=[h]}});var i=[];for(var n in e)if(e.hasOwnProperty(n)){var r=e[n];if(r){r.sort(function(t,e){return t-e});for(var a=null,o=1;o<r.length;++o){var s=r[o]-r[o-1];s>0&&(a=null===a?s:Math.min(a,s))}i[n]=a}}return i}function Pu(t){var e=ku(t),i=[];return f(t,function(t){var n,r=t.coordinateSystem,a=r.getBaseAxis(),o=a.getExtent();if("category"===a.type)n=a.getBandWidth();else if("value"===a.type||"time"===a.type){var s=a.dim+"_"+a.index,l=e[s],h=Math.abs(o[1]-o[0]),u=a.scale.getExtent(),c=Math.abs(u[1]-u[0]);n=l?h/c*l:h}else{var d=t.getData();n=Math.abs(o[1]-o[0])/d.count()}var f=bo(t.get("barWidth"),n),p=bo(t.get("barMaxWidth"),n),g=bo(t.get("barMinWidth")||1,n),v=t.get("barGap"),m=t.get("barCategoryGap");i.push({bandWidth:n,barWidth:f,barMaxWidth:p,barMinWidth:g,barGap:v,barCategoryGap:m,axisKey:Cu(a),stackId:Tu(t)})}),Lu(i)}function Lu(t){var e={};f(t,function(t){var i=t.axisKey,n=t.bandWidth,r=e[i]||{bandWidth:n,remainedWidth:n,autoWidthCount:0,categoryGap:"20%",gap:"30%",stacks:{}},a=r.stacks;e[i]=r;var o=t.stackId;a[o]||r.autoWidthCount++,a[o]=a[o]||{width:0,maxWidth:0};var s=t.barWidth;s&&!a[o].width&&(a[o].width=s,s=Math.min(r.remainedWidth,s),r.remainedWidth-=s);var l=t.barMaxWidth;l&&(a[o].maxWidth=l);var h=t.barMinWidth;h&&(a[o].minWidth=h);var u=t.barGap;null!=u&&(r.gap=u);var c=t.barCategoryGap;null!=c&&(r.categoryGap=c)});var i={};return f(e,function(t,e){i[e]={};var n=t.stacks,r=t.bandWidth,a=bo(t.categoryGap,r),o=bo(t.gap,1),s=t.remainedWidth,l=t.autoWidthCount,h=(s-a)/(l+(l-1)*o);h=Math.max(h,0),f(n,function(t){var e=t.maxWidth,i=t.minWidth;if(t.width){var n=t.width;e&&(n=Math.min(n,e)),i&&(n=Math.max(n,i)),t.width=n,s-=n+o*n,l--}else{var n=h;e&&n>e&&(n=Math.min(e,s)),i&&i>n&&(n=i),n!==h&&(t.width=n,s-=n+o*n,l--)}}),h=(s-a)/(l+(l-1)*o),h=Math.max(h,0);var u,c=0;f(n,function(t){t.width||(t.width=h),u=t,c+=t.width*(1+o)}),u&&(c-=u.width*o);var d=-c/2;f(n,function(t,n){i[e][n]=i[e][n]||{bandWidth:r,offset:d,width:t.width},d+=t.width*(1+o)})}),i}function Ou(t,e,i){if(t&&e){var n=t[Cu(e)];return null!=n&&null!=i&&(n=n[Tu(i)]),n}}function Eu(t,e){var i=Du(t,e),n=Pu(i),r={};f(i,function(t){var e=t.getData(),i=t.coordinateSystem,a=i.getBaseAxis(),o=Tu(t),s=n[Cu(a)][o],l=s.offset,h=s.width,u=i.getOtherAxis(a),c=t.get("barMinHeight")||0;r[o]=r[o]||[],e.setLayout({bandWidth:s.bandWidth,offset:l,size:h});for(var d=e.mapDimension(u.dim),f=e.mapDimension(a.dim),p=pu(e,d),g=u.isHorizontal(),v=Ru(a,u,p),m=0,y=e.count();y>m;m++){var x=e.get(d,m),_=e.get(f,m),w=x>=0?"p":"n",b=v;p&&(r[o][_]||(r[o][_]={p:v,n:v}),b=r[o][_][w]);var M,S,A,I;if(g){var T=i.dataToPoint([x,_]);M=b,S=T[1]+l,A=T[0]-v,I=h,Math.abs(A)<c&&(A=(0>A?-1:1)*c),isNaN(A)||p&&(r[o][_][w]+=A)}else{var T=i.dataToPoint([_,x]);M=T[0]+l,S=b,A=h,I=T[1]-v,Math.abs(I)<c&&(I=(0>=I?-1:1)*c),isNaN(I)||p&&(r[o][_][w]+=I)}e.setItemLayout(m,{x:M,y:S,width:A,height:I})}},this)}function zu(t){return t.coordinateSystem&&"cartesian2d"===t.coordinateSystem.type}function Bu(t){return t.pipelineContext&&t.pipelineContext.large}function Ru(t,e){return e.toGlobalCoord(e.dataToCoord("log"===e.type?1:0))}function Nu(t,e){return oM(t,aM(e))}function Fu(t,e){var i,n,r,a=t.type,o=e.getMin(),s=e.getMax(),l=t.getExtent();"ordinal"===a?i=e.getCategories().length:(n=e.get("boundaryGap"),_(n)||(n=[n||0,n||0]),"boolean"==typeof n[0]&&(wg&&console.warn('Boolean type for boundaryGap is only allowed for ordinal axis. Please use string in percentage instead, e.g., "20%". Currently, boundaryGap is set to be 0.'),n=[0,0]),n[0]=bo(n[0],1),n[1]=bo(n[1],1),r=l[1]-l[0]||Math.abs(l[0])),"dataMin"===o?o=l[0]:"function"==typeof o&&(o=o({min:l[0],max:l[1]})),"dataMax"===s?s=l[1]:"function"==typeof s&&(s=s({min:l[0],max:l[1]}));var h=null!=o,u=null!=s;null==o&&(o="ordinal"===a?i?0:0/0:l[0]-n[0]*r),null==s&&(s="ordinal"===a?i?i-1:0/0:l[1]+n[1]*r),(null==o||!isFinite(o))&&(o=0/0),(null==s||!isFinite(s))&&(s=0/0),t.setBlank(T(o)||T(s)||"ordinal"===a&&!t.getOrdinalMeta().categories.length),e.getNeedCrossZero()&&(o>0&&s>0&&!h&&(o=0),0>o&&0>s&&!u&&(s=0));var c=e.ecModel;if(c&&"time"===a){var d,p=Du("bar",c);if(f(p,function(t){d|=t.getBaseAxis()===e.axis}),d){var g=Pu(p),v=Vu(o,s,e,g);o=v.min,s=v.max}}return{extent:[o,s],fixMin:h,fixMax:u}}function Vu(t,e,i,n){var r=i.axis.getExtent(),a=r[1]-r[0],o=Ou(n,i.axis);if(void 0===o)return{min:t,max:e};var s=1/0;f(o,function(t){s=Math.min(t.offset,s)});var l=-1/0;f(o,function(t){l=Math.max(t.offset+t.width,l)}),s=Math.abs(s),l=Math.abs(l);var h=s+l,u=e-t,c=1-(s+l)/a,d=u/c-u;return e+=d*(l/h),t-=d*(s/h),{min:t,max:e}}function Wu(t,e){var i=Fu(t,e),n=i.extent,r=e.get("splitNumber");"log"===t.type&&(t.base=e.get("logBase"));var a=t.type;t.setExtent(n[0],n[1]),t.niceExtent({splitNumber:r,fixMin:i.fixMin,fixMax:i.fixMax,minInterval:"interval"===a||"time"===a?e.get("minInterval"):null,maxInterval:"interval"===a||"time"===a?e.get("maxInterval"):null});var o=e.get("interval");null!=o&&t.setInterval&&t.setInterval(o)}function Hu(t,e){if(e=e||t.get("type"))switch(e){case"category":return new Fb(t.getOrdinalMeta?t.getOrdinalMeta():t.getCategories(),[1/0,-1/0]);case"value":return new Hb;default:return(xu.getClass(e)||Hb).create(t)}}function Gu(t){var e=t.scale.getExtent(),i=e[0],n=e[1];return!(i>0&&n>0||0>i&&0>n)}function Zu(t){var e=t.getLabelModel().get("formatter"),i="category"===t.type?t.scale.getExtent()[0]:null;return"string"==typeof e?e=function(e){return function(i){return i=t.scale.getLabel(i),e.replace("{value}",null!=i?i:"")}}(e):"function"==typeof e?function(n,r){return null!=i&&(r=n-i),e(Xu(t,n),r)}:function(e){return t.scale.getLabel(e)}}function Xu(t,e){return"category"===t.type?t.scale.getLabel(e):e}function Yu(t){var e=t.model,i=t.scale;if(e.get("axisLabel.show")&&!i.isBlank()){var n,r,a="category"===t.type,o=i.getExtent();a?r=i.count():(n=i.getTicks(),r=n.length);var s,l=t.getLabelModel(),h=Zu(t),u=1;r>40&&(u=Math.ceil(r/40));for(var c=0;r>c;c+=u){var d=n?n[c]:o[0]+c,f=h(d),p=l.getTextRect(f),g=Uu(p,l.get("rotate")||0);s?s.union(g):s=g}return s}}function Uu(t,e){var i=e*Math.PI/180,n=t.plain(),r=n.width,a=n.height,o=r*Math.abs(Math.cos(i))+Math.abs(a*Math.sin(i)),s=r*Math.abs(Math.sin(i))+Math.abs(a*Math.cos(i)),l=new Ii(n.x,n.y,o,s);return l}function qu(t){var e=t.get("interval");return null==e?"auto":e}function ju(t){return"category"===t.type&&0===qu(t.getLabelModel())}function Ku(t,e){if("image"!==this.type){var i=this.style,n=this.shape;n&&"line"===n.symbolType?i.stroke=t:this.__isEmptyBrush?(i.stroke=t,i.fill=e||"#fff"):(i.fill&&(i.fill=t),i.stroke&&(i.stroke=t)),this.dirty(!1)}}function $u(t,e,i,n,r,a,o){var s=0===t.indexOf("empty");s&&(t=t.substr(5,1).toLowerCase()+t.substr(6));var l;return l=0===t.indexOf("image://")?_a(t.slice(8),new Ii(e,i,n,r),o?"center":"cover"):0===t.indexOf("path://")?xa(t.slice(7),{},new Ii(e,i,n,r),o?"center":"cover"):new _M({shape:{symbolType:t,x:e,y:i,width:n,height:r}}),l.__isEmptyBrush=s,l.setColor=Ku,l.setColor(a),l}function Qu(t){return vu(t.getSource(),t)}function Ju(t,e){var i=e;po.isInstance(e)||(i=new po(e),c(i,dM));var n=Hu(i);return n.setExtent(t[0],t[1]),Wu(n,i),n}function tc(t){c(t,dM)}function ec(t,e){return Math.abs(t-e)<MM}function ic(t,e,i){var n=0,r=t[0];if(!r)return!1;for(var a=1;a<t.length;a++){var o=t[a];n+=Yr(r[0],r[1],o[0],o[1],e,i),r=o}var s=t[0];return ec(r[0],s[0])&&ec(r[1],s[1])||(n+=Yr(r[0],r[1],s[0],s[1],e,i)),0!==n}function nc(t,e,i){if(this.name=t,this.geometries=e,i)i=[i[0],i[1]];else{var n=this.getBoundingRect();i=[n.x+n.width/2,n.y+n.height/2]}this.center=i}function rc(t){if(!t.UTF8Encoding)return t;var e=t.UTF8Scale;null==e&&(e=1024);for(var i=t.features,n=0;n<i.length;n++)for(var r=i[n],a=r.geometry,o=a.coordinates,s=a.encodeOffsets,l=0;l<o.length;l++){var h=o[l];if("Polygon"===a.type)o[l]=ac(h,s[l],e);else if("MultiPolygon"===a.type)for(var u=0;u<h.length;u++){var c=h[u];h[u]=ac(c,s[l][u],e)}}return t.UTF8Encoding=!1,t}function ac(t,e,i){for(var n=[],r=e[0],a=e[1],o=0;o<t.length;o+=2){var s=t.charCodeAt(o)-64,l=t.charCodeAt(o+1)-64;s=s>>1^-(1&s),l=l>>1^-(1&l),s+=r,l+=a,r=s,a=l,n.push([s/i,l/i])}return n}function oc(t){return"category"===t.type?lc(t):cc(t)}function sc(t,e){return"category"===t.type?uc(t,e):{ticks:t.scale.getTicks()}}function lc(t){var e=t.getLabelModel(),i=hc(t,e);return!e.get("show")||t.scale.isBlank()?{labels:[],labelCategoryInterval:i.labelCategoryInterval}:i}function hc(t,e){var i=dc(t,"labels"),n=qu(e),r=fc(i,n);if(r)return r;var a,o;return w(n)?a=xc(t,n):(o="auto"===n?gc(t):n,a=yc(t,o)),pc(i,n,{labels:a,labelCategoryInterval:o})}function uc(t,e){var i=dc(t,"ticks"),n=qu(e),r=fc(i,n);if(r)return r;var a,o;if((!e.get("show")||t.scale.isBlank())&&(a=[]),w(n))a=xc(t,n,!0);else if("auto"===n){var s=hc(t,t.getLabelModel());o=s.labelCategoryInterval,a=p(s.labels,function(t){return t.tickValue})}else o=n,a=yc(t,o,!0);return pc(i,n,{ticks:a,tickCategoryInterval:o})}function cc(t){var e=t.scale.getTicks(),i=Zu(t);return{labels:p(e,function(e,n){return{formattedLabel:i(e,n),rawLabel:t.scale.getLabel(e),tickValue:e}})}}function dc(t,e){return AM(t)[e]||(AM(t)[e]=[])}function fc(t,e){for(var i=0;i<t.length;i++)if(t[i].key===e)return t[i].value}function pc(t,e,i){return t.push({key:e,value:i}),i}function gc(t){var e=AM(t).autoInterval;return null!=e?e:AM(t).autoInterval=t.calculateCategoryInterval()}function vc(t){var e=mc(t),i=Zu(t),n=(e.axisRotate-e.labelRotate)/180*Math.PI,r=t.scale,a=r.getExtent(),o=r.count();if(a[1]-a[0]<1)return 0;var s=1;o>40&&(s=Math.max(1,Math.floor(o/40)));for(var l=a[0],h=t.dataToCoord(l+1)-t.dataToCoord(l),u=Math.abs(h*Math.cos(n)),c=Math.abs(h*Math.sin(n)),d=0,f=0;l<=a[1];l+=s){var p=0,g=0,v=Xi(i(l),e.font,"center","top");p=1.3*v.width,g=1.3*v.height,d=Math.max(d,p,7),f=Math.max(f,g,7)}var m=d/u,y=f/c;isNaN(m)&&(m=1/0),isNaN(y)&&(y=1/0);var x=Math.max(0,Math.floor(Math.min(m,y))),_=AM(t.model),w=t.getExtent(),b=_.lastAutoInterval,M=_.lastTickCount;return null!=b&&null!=M&&Math.abs(b-x)<=1&&Math.abs(M-o)<=1&&b>x&&_.axisExtend0===w[0]&&_.axisExtend1===w[1]?x=b:(_.lastTickCount=o,_.lastAutoInterval=x,_.axisExtend0=w[0],_.axisExtend1=w[1]),x}function mc(t){var e=t.getLabelModel();return{axisRotate:t.getRotate?t.getRotate():t.isHorizontal&&!t.isHorizontal()?90:0,labelRotate:e.get("rotate")||0,font:e.getFont()}}function yc(t,e,i){function n(t){l.push(i?t:{formattedLabel:r(t),rawLabel:a.getLabel(t),tickValue:t})}var r=Zu(t),a=t.scale,o=a.getExtent(),s=t.getLabelModel(),l=[],h=Math.max((e||0)+1,1),u=o[0],c=a.count();0!==u&&h>1&&c/h>2&&(u=Math.round(Math.ceil(u/h)*h));var d=ju(t),f=s.get("showMinLabel")||d,p=s.get("showMaxLabel")||d;f&&u!==o[0]&&n(o[0]);for(var g=u;g<=o[1];g+=h)n(g);return p&&g-h!==o[1]&&n(o[1]),l}function xc(t,e,i){var n=t.scale,r=Zu(t),a=[];return f(n.getTicks(),function(t){var o=n.getLabel(t);e(t,o)&&a.push(i?t:{formattedLabel:r(t),rawLabel:o,tickValue:t})}),a}function _c(t,e){var i=t[1]-t[0],n=e,r=i/n/2;t[0]+=r,t[1]-=r}function wc(t,e,i,n){function r(t,e){return t=Mo(t),e=Mo(e),d?t>e:e>t}var a=e.length;if(t.onBand&&!i&&a){var o,s,l=t.getExtent();if(1===a)e[0].coord=l[0],o=e[1]={coord:l[0]};else{var h=e[a-1].tickValue-e[0].tickValue,u=(e[a-1].coord-e[0].coord)/h;f(e,function(t){t.coord-=u/2});var c=t.scale.getExtent();s=1+c[1]-e[a-1].tickValue,o={coord:e[a-1].coord+u*s},e.push(o)}var d=l[0]>l[1];r(e[0].coord,l[0])&&(n?e[0].coord=l[0]:e.shift()),n&&r(l[0],e[0].coord)&&e.unshift({coord:l[0]}),r(l[1],o.coord)&&(n?o.coord=l[1]:e.pop()),n&&r(o.coord,l[1])&&e.push({coord:l[1]})}}function bc(t){return this._axes[t]}function Mc(t){PM.call(this,t)}function Sc(t,e){return e.type||(e.data?"category":"value")}function Ac(t,e){return t.getCoordSysModel()===e}function Ic(t,e,i){this._coordsMap={},this._coordsList=[],this._axesMap={},this._axesList=[],this._initCartesian(t,e,i),this.model=t}function Tc(t,e,i,n){function r(t){return t.dim+"_"+t.index}i.getAxesOnZeroOf=function(){return a?[a]:[]};var a,o=t[e],s=i.model,l=s.get("axisLine.onZero"),h=s.get("axisLine.onZeroAxisIndex");if(l){if(null!=h)Cc(o[h])&&(a=o[h]);else for(var u in o)if(o.hasOwnProperty(u)&&Cc(o[u])&&!n[r(o[u])]){a=o[u];break}a&&(n[r(a)]=!0)}}function Cc(t){return t&&"category"!==t.type&&"time"!==t.type&&Gu(t)}function Dc(t,e){var i=t.getExtent(),n=i[0]+i[1];t.toGlobalCoord="x"===t.dim?function(t){return t+e}:function(t){return n-t+e},t.toLocalCoord="x"===t.dim?function(t){return t-e}:function(t){return n-t+e}}function kc(t){return p(VM,function(e){var i=t.getReferringComponents(e)[0];if(wg&&!i)throw new Error(e+' "'+C(t.get(e+"Index"),t.get(e+"Id"),0)+'" not found');return i})}function Pc(t){return"cartesian2d"===t.get("coordinateSystem")}function Lc(t,e){var i=t.mapDimension("defaultedLabel",!0),n=i.length;if(1===n)return Qs(t,e,i[0]);if(n){for(var r=[],a=0;a<i.length;a++){var o=Qs(t,e,i[a]);r.push(o)}return r.join(" ")}}function Oc(t,e,i,n,r,a){var o=i.getModel("label"),s=i.getModel("emphasis.label");Ha(t,e,o,s,{labelFetcher:r,labelDataIndex:a,defaultText:Lc(r.getData(),a),isRectText:!0,autoColor:n}),Ec(t),Ec(e)}function Ec(t,e){"outside"===t.textPosition&&(t.textPosition=e)}function zc(t,e,i){var n=t.getArea(),r=t.getBaseAxis().isHorizontal(),a=n.x,o=n.y,s=n.width,l=n.height,h=i.get("lineStyle.width")||2;a-=h/2,o-=h/2,s+=h,l+=h,a=Math.floor(a),s=Math.round(s);var u=new dx({shape:{x:a,y:o,width:s,height:l}});return e&&(u.shape[r?"width":"height"]=0,eo(u,{shape:{width:s,height:l}},i)),u}function Bc(t,e,i){var n=t.getArea(),r=new rx({shape:{cx:Mo(t.cx,1),cy:Mo(t.cy,1),r0:Mo(n.r0,1),r:Mo(n.r,1),startAngle:n.startAngle,endAngle:n.endAngle,clockwise:n.clockwise}});return e&&(r.shape.endAngle=n.startAngle,eo(r,{shape:{endAngle:n.endAngle}},i)),r}function Rc(t,e,i){return t?"polar"===t.type?Bc(t,e,i):"cartesian2d"===t.type?zc(t,e,i):null:null}function Nc(t,e){var i=t.getArea&&t.getArea();if("cartesian2d"===t.type){var n=t.getBaseAxis();if("category"!==n.type||!n.onBand){var r=e.getLayout("bandWidth");n.isHorizontal()?(i.x-=r,i.width+=2*r):(i.y-=r,i.height+=2*r)}}return i}function Fc(t,e,i){i.style.text=null,to(i,{shape:{width:0}},e,t,function(){i.parent&&i.parent.remove(i)})}function Vc(t,e,i){i.style.text=null,to(i,{shape:{r:i.shape.r0}},e,t,function(){i.parent&&i.parent.remove(i)})}function Wc(t){return null!=t.startAngle&&null!=t.endAngle&&t.startAngle===t.endAngle}function Hc(t,e,i,n,r,a,o,l){var h=e.getItemVisual(i,"color"),u=e.getItemVisual(i,"opacity"),c=e.getVisual("borderColor"),d=n.getModel("itemStyle"),f=n.getModel("emphasis.itemStyle").getBarItemStyle();l||t.setShape("r",d.get("barBorderRadius")||0),t.useStyle(s({stroke:Wc(r)?"none":c,fill:Wc(r)?"none":h,opacity:u},d.getBarItemStyle()));var p=n.getShallow("cursor");p&&t.attr("cursor",p);var g=o?r.height>0?"bottom":"top":r.width>0?"left":"right";l||Oc(t.style,f,n,h,a,i,g),Wc(r)&&(f.fill=f.stroke="none"),Na(t,f)}function Gc(t,e){var i=t.get(XM)||0,n=isNaN(e.width)?Number.MAX_VALUE:Math.abs(e.width),r=isNaN(e.height)?Number.MAX_VALUE:Math.abs(e.height);return Math.min(i,n,r)}function Zc(t,e,i){var n=t.getData(),r=[],a=n.getLayout("valueAxisHorizontal")?1:0;r[1-a]=n.getLayout("valueAxisStart");var o=n.getLayout("largeDataIndices"),s=n.getLayout("barWidth"),l=t.getModel("backgroundStyle"),h=t.get("showBackground",!0);if(h){var u=n.getLayout("largeBackgroundPoints"),c=[];c[1-a]=n.getLayout("backgroundStart");var d=new QM({shape:{points:u},incremental:!!i,__startPoint:c,__baseDimIdx:a,__largeDataIndices:o,__barWidth:s,silent:!0,z2:0});Uc(d,l,n),e.add(d)}var f=new QM({shape:{points:n.getLayout("largePoints")},incremental:!!i,__startPoint:r,__baseDimIdx:a,__largeDataIndices:o,__barWidth:s});e.add(f),Yc(f,t,n),f.seriesIndex=t.seriesIndex,t.get("silent")||(f.on("mousedown",JM),f.on("mousemove",JM))}function Xc(t,e,i){var n=t.__baseDimIdx,r=1-n,a=t.shape.points,o=t.__largeDataIndices,s=Math.abs(t.__barWidth/2),l=t.__startPoint[r];YM[0]=e,YM[1]=i;for(var h=YM[n],u=YM[1-n],c=h-s,d=h+s,f=0,p=a.length/2;p>f;f++){var g=2*f,v=a[g+n],m=a[g+r];if(v>=c&&d>=v&&(m>=l?u>=l&&m>=u:u>=m&&l>=u))return o[f]}return-1}function Yc(t,e,i){var n=i.getVisual("borderColor")||i.getVisual("color"),r=e.getModel("itemStyle").getItemStyle(["color","borderColor"]);t.useStyle(r),t.style.fill=null,t.style.stroke=n,t.style.lineWidth=i.getLayout("barWidth")}function Uc(t,e,i){var n=e.get("borderColor")||e.get("color"),r=e.getItemStyle(["color","borderColor"]);t.useStyle(r),t.style.fill=null,t.style.stroke=n,t.style.lineWidth=i.getLayout("barWidth")}function qc(t,e,i){var n,r="polar"===i.type;return n=r?i.getArea():i.grid.getRect(),r?{cx:n.cx,cy:n.cy,r0:t?n.r0:e.r0,r:t?n.r:e.r,startAngle:t?e.startAngle:0,endAngle:t?e.endAngle:2*Math.PI}:{x:t?e.x:n.x,y:t?n.y:e.y,width:t?e.width:n.width,height:t?n.height:e.height}}function jc(t,e,i){var n="polar"===t.type?rx:dx;return new n({shape:qc(e,i,t),silent:!0,z2:0})}function Kc(t,e,i,n){var r,a,o=Do(i-t.rotation),s=n[0]>n[1],l="start"===e&&!s||"start"!==e&&s;return ko(o-tS/2)?(a=l?"bottom":"top",r="center"):ko(o-1.5*tS)?(a=l?"top":"bottom",r="center"):(a="middle",r=1.5*tS>o&&o>tS/2?l?"left":"right":l?"right":"left"),{rotation:o,textAlign:r,textVerticalAlign:a}}function $c(t,e,i){if(!ju(t.axis)){var n=t.get("axisLabel.showMinLabel"),r=t.get("axisLabel.showMaxLabel");e=e||[],i=i||[];var a=e[0],o=e[1],s=e[e.length-1],l=e[e.length-2],h=i[0],u=i[1],c=i[i.length-1],d=i[i.length-2];n===!1?(Qc(a),Qc(h)):Jc(a,o)&&(n?(Qc(o),Qc(u)):(Qc(a),Qc(h))),r===!1?(Qc(s),Qc(c)):Jc(l,s)&&(r?(Qc(l),Qc(d)):(Qc(s),Qc(c)))}}function Qc(t){t&&(t.ignore=!0)}function Jc(t,e){var i=t&&t.getBoundingRect().clone(),n=e&&e.getBoundingRect().clone();if(i&&n){var r=Ee([]);return Ne(r,r,-t.rotation),i.applyTransform(Be([],r,t.getLocalTransform())),n.applyTransform(Be([],r,e.getLocalTransform())),i.intersect(n)}}function td(t){return"middle"===t||"center"===t}function ed(t,e,i,n,r){for(var a=[],o=[],s=[],l=0;l<t.length;l++){var h=t[l].coord;o[0]=h,o[1]=0,s[0]=h,s[1]=i,e&&(ae(o,o,e),ae(s,s,e));var u=new px({anid:r+"_"+t[l].tickValue,subPixelOptimize:!0,shape:{x1:o[0],y1:o[1],x2:s[0],y2:s[1]},style:n,z2:2,silent:!0});a.push(u)}return a}function id(t,e,i){var n=e.axis,r=e.getModel("axisTick");if(r.get("show")&&!n.scale.isBlank()){for(var a=r.getModel("lineStyle"),o=i.tickDirection*r.get("length"),l=n.getTicksCoords(),h=ed(l,t._transform,o,s(a.getLineStyle(),{stroke:e.get("axisLine.lineStyle.color")}),"ticks"),u=0;u<h.length;u++)t.group.add(h[u]);return h}}function nd(t,e,i){var n=e.axis,r=e.getModel("minorTick");if(r.get("show")&&!n.scale.isBlank()){var a=n.getMinorTicksCoords();if(a.length)for(var o=r.getModel("lineStyle"),l=i.tickDirection*r.get("length"),h=s(o.getLineStyle(),s(e.getModel("axisTick").getLineStyle(),{stroke:e.get("axisLine.lineStyle.color")})),u=0;u<a.length;u++)for(var c=ed(a[u],t._transform,l,h,"minorticks_"+u),d=0;d<c.length;d++)t.group.add(c[d])}}function rd(t,e,i){var n=e.axis,r=C(i.axisLabelShow,e.get("axisLabel.show"));if(r&&!n.scale.isBlank()){var a=e.getModel("axisLabel"),o=a.get("margin"),s=n.getViewLabels(),l=(C(i.labelRotate,a.get("rotate"))||0)*tS/180,h=rS(i.rotation,l,i.labelDirection),u=e.getCategories&&e.getCategories(!0),c=[],d=aS(e),p=e.get("triggerEvent");return f(s,function(r,s){var l=r.tickValue,f=r.formattedLabel,g=r.rawLabel,v=a;u&&u[l]&&u[l].textStyle&&(v=new po(u[l].textStyle,a,e.ecModel));var m=v.getTextColor()||e.get("axisLine.lineStyle.color"),y=n.dataToCoord(l),x=[y,i.labelOffset+i.labelDirection*o],_=new tx({anid:"label_"+l,position:x,rotation:h.rotation,silent:d,z2:10});Za(_.style,v,{text:f,textAlign:v.getShallow("align",!0)||h.textAlign,textVerticalAlign:v.getShallow("verticalAlign",!0)||v.getShallow("baseline",!0)||h.textVerticalAlign,textFill:"function"==typeof m?m("category"===n.type?g:"value"===n.type?l+"":l,s):m}),p&&(_.eventData=nS(e),_.eventData.targetType="axisLabel",_.eventData.value=g),t._dumbGroup.add(_),_.updateTransform(),c.push(_),t.group.add(_),_.decomposeTransform()}),c}}function ad(t,e){var i={axesInfo:{},seriesInvolved:!1,coordSysAxesInfo:{},coordSysMap:{}};return od(i,t,e),i.seriesInvolved&&ld(i,t),i}function od(t,e,i){var n=e.getComponent("tooltip"),r=e.getComponent("axisPointer"),a=r.get("link",!0)||[],o=[];oS(i.getCoordinateSystems(),function(i){function s(n,s,l){var u=l.model.getModel("axisPointer",r),d=u.get("show");if(d&&("auto"!==d||n||pd(u))){null==s&&(s=u.get("triggerTooltip")),u=n?sd(l,c,r,e,n,s):u;
var f=u.get("snap"),p=gd(l.model),g=s||f||"category"===l.type,v=t.axesInfo[p]={key:p,axis:l,coordSys:i,axisPointerModel:u,triggerTooltip:s,involveSeries:g,snap:f,useHandle:pd(u),seriesModels:[]};h[p]=v,t.seriesInvolved|=g;var m=hd(a,l);if(null!=m){var y=o[m]||(o[m]={axesInfo:{}});y.axesInfo[p]=v,y.mapper=a[m].mapper,v.linkGroup=y}}}if(i.axisPointerEnabled){var l=gd(i.model),h=t.coordSysAxesInfo[l]={};t.coordSysMap[l]=i;var u=i.model,c=u.getModel("tooltip",n);if(oS(i.getAxes(),sS(s,!1,null)),i.getTooltipAxes&&n&&c.get("show")){var d="axis"===c.get("trigger"),f="cross"===c.get("axisPointer.type"),p=i.getTooltipAxes(c.get("axisPointer.axis"));(d||f)&&oS(p.baseAxes,sS(s,f?"cross":!0,d)),f&&oS(p.otherAxes,sS(s,"cross",!1))}}})}function sd(t,e,i,r,a,o){var l=e.getModel("axisPointer"),h={};oS(["type","snap","lineStyle","shadowStyle","label","animation","animationDurationUpdate","animationEasingUpdate","z"],function(t){h[t]=n(l.get(t))}),h.snap="category"!==t.type&&!!o,"cross"===l.get("type")&&(h.type="line");var u=h.label||(h.label={});if(null==u.show&&(u.show=!1),"cross"===a){var c=l.get("label.show");if(u.show=null!=c?c:!0,!o){var d=h.lineStyle=l.get("crossStyle");d&&s(u,d.textStyle)}}return t.model.getModel("axisPointer",new po(h,i,r))}function ld(t,e){e.eachSeries(function(e){var i=e.coordinateSystem,n=e.get("tooltip.trigger",!0),r=e.get("tooltip.show",!0);i&&"none"!==n&&n!==!1&&"item"!==n&&r!==!1&&e.get("axisPointer.show",!0)!==!1&&oS(t.coordSysAxesInfo[gd(i.model)],function(t){var n=t.axis;i.getAxis(n.dim)===n&&(t.seriesModels.push(e),null==t.seriesDataCount&&(t.seriesDataCount=0),t.seriesDataCount+=e.getData().count())})},this)}function hd(t,e){for(var i=e.model,n=e.dim,r=0;r<t.length;r++){var a=t[r]||{};if(ud(a[n+"AxisId"],i.id)||ud(a[n+"AxisIndex"],i.componentIndex)||ud(a[n+"AxisName"],i.name))return r}}function ud(t,e){return"all"===t||_(t)&&h(t,e)>=0||t===e}function cd(t){var e=dd(t);if(e){var i=e.axisPointerModel,n=e.axis.scale,r=i.option,a=i.get("status"),o=i.get("value");null!=o&&(o=n.parse(o));var s=pd(i);null==a&&(r.status=s?"show":"hide");var l=n.getExtent().slice();l[0]>l[1]&&l.reverse(),(null==o||o>l[1])&&(o=l[1]),o<l[0]&&(o=l[0]),r.value=o,s&&(r.status=e.axis.scale.isBlank()?"hide":"show")}}function dd(t){var e=(t.ecModel.getComponent("axisPointer")||{}).coordSysAxesInfo;return e&&e.axesInfo[gd(t)]}function fd(t){var e=dd(t);return e&&e.axisPointerModel}function pd(t){return!!t.get("handle.show")}function gd(t){return t.type+"||"+t.id}function vd(t,e,i,n,r,a){var o=lS.getAxisPointerClass(t.axisPointerClass);if(o){var s=fd(e);s?(t._axisPointer||(t._axisPointer=new o)).render(e,s,n,a):md(t,n)}}function md(t,e,i){var n=t._axisPointer;n&&n.dispose(e,i),t._axisPointer=null}function yd(t,e,i){i=i||{};var n=t.coordinateSystem,r=e.axis,a={},o=r.getAxesOnZeroOf()[0],s=r.position,l=o?"onZero":s,h=r.dim,u=n.getRect(),c=[u.x,u.x+u.width,u.y,u.y+u.height],d={left:0,right:1,top:0,bottom:1,onZero:2},f=e.get("offset")||0,p="x"===h?[c[2]-f,c[3]+f]:[c[0]-f,c[1]+f];if(o){var g=o.toGlobalCoord(o.dataToCoord(0));p[d.onZero]=Math.max(Math.min(g,p[1]),p[0])}a.position=["y"===h?p[d[l]]:c[0],"x"===h?p[d[l]]:c[3]],a.rotation=Math.PI/2*("x"===h?0:1);var v={top:-1,bottom:1,left:-1,right:1};a.labelDirection=a.tickDirection=a.nameDirection=v[s],a.labelOffset=o?p[d[s]]-p[d.onZero]:0,e.get("axisTick.inside")&&(a.tickDirection=-a.tickDirection),C(i.labelInside,e.get("axisLabel.inside"))&&(a.labelDirection=-a.labelDirection);var m=e.get("axisLabel.rotate");return a.labelRotate="top"===l?-m:m,a.z2=1,a}function xd(t,e,i,n){var r=i.axis;if(!r.scale.isBlank()){var a=i.getModel("splitArea"),o=a.getModel("areaStyle"),l=o.get("color"),h=n.coordinateSystem.getRect(),u=r.getTicksCoords({tickModel:a,clamp:!0});if(u.length){var c=l.length,d=t.__splitAreaColors,f=N(),p=0;if(d)for(var g=0;g<u.length;g++){var v=d.get(u[g].tickValue);if(null!=v){p=(v+(c-1)*g)%c;break}}var m=r.toGlobalCoord(u[0].coord),y=o.getAreaStyle();l=_(l)?l:[l];for(var g=1;g<u.length;g++){var x,w,b,M,S=r.toGlobalCoord(u[g].coord);r.isHorizontal()?(x=m,w=h.y,b=S-x,M=h.height,m=x+b):(x=h.x,w=m,b=h.width,M=S-w,m=w+M);var A=u[g-1].tickValue;null!=A&&f.set(A,p),e.add(new dx({anid:null!=A?"area_"+A:null,shape:{x:x,y:w,width:b,height:M},style:s({fill:l[p]},y),silent:!0})),p=(p+1)%c}t.__splitAreaColors=f}}}function _d(t){t.__splitAreaColors=null}function wd(t,e,i){Fv.call(this),this.updateData(t,e,i)}function bd(t){return[t[0]/2,t[1]/2]}function Md(t,e){this.parent.drift(t,e)}function Sd(t,e){if(!this.incremental&&!this.useHoverLayer)if("emphasis"===e){var i=this.__symbolOriginalScale,n=i[1]/i[0],r={scale:[Math.max(1.1*i[0],i[0]+3),Math.max(1.1*i[1],i[1]+3*n)]};this.animateTo(r,400,"elasticOut")}else"normal"===e&&this.animateTo({scale:this.__symbolOriginalScale},400,"elasticOut")}function Ad(t){this.group=new Fv,this._symbolCtor=t||wd}function Id(t,e,i,n){return!(!e||isNaN(e[0])||isNaN(e[1])||n.isIgnore&&n.isIgnore(i)||n.clipShape&&!n.clipShape.contain(e[0],e[1])||"none"===t.getItemVisual(i,"symbol"))}function Td(t){return null==t||M(t)||(t={isIgnore:t}),t||{}}function Cd(t){var e=t.hostModel;return{itemStyle:e.getModel("itemStyle").getItemStyle(["color"]),hoverItemStyle:e.getModel("emphasis.itemStyle").getItemStyle(),symbolRotate:e.get("symbolRotate"),symbolOffset:e.get("symbolOffset"),hoverAnimation:e.get("hoverAnimation"),labelModel:e.getModel("label"),hoverLabelModel:e.getModel("emphasis.label"),cursorStyle:e.get("cursor")}}function Dd(t,e,i){var n,r=t.getBaseAxis(),a=t.getOtherAxis(r),o=kd(a,i),s=r.dim,l=a.dim,h=e.mapDimension(l),u=e.mapDimension(s),c="x"===l||"radius"===l?1:0,d=p(t.dimensions,function(t){return e.mapDimension(t)}),f=e.getCalculationInfo("stackResultDimension");return(n|=pu(e,d[0]))&&(d[0]=f),(n|=pu(e,d[1]))&&(d[1]=f),{dataDimsForPoint:d,valueStart:o,valueAxisDim:l,baseAxisDim:s,stacked:!!n,valueDim:h,baseDim:u,baseDataOffset:c,stackedOverDimension:e.getCalculationInfo("stackedOverDimension")}}function kd(t,e){var i=0,n=t.scale.getExtent();return"start"===e?i=n[0]:"end"===e?i=n[1]:n[0]>0?i=n[0]:n[1]<0&&(i=n[1]),i}function Pd(t,e,i,n){var r=0/0;t.stacked&&(r=i.get(i.getCalculationInfo("stackedOverDimension"),n)),isNaN(r)&&(r=t.valueStart);var a=t.baseDataOffset,o=[];return o[a]=i.get(t.baseDim,n),o[1-a]=r,e.dataToPoint(o)}function Ld(t,e){var i=[];return e.diff(t).add(function(t){i.push({cmd:"+",idx:t})}).update(function(t,e){i.push({cmd:"=",idx:e,idx1:t})}).remove(function(t){i.push({cmd:"-",idx:t})}).execute(),i}function Od(t){return isNaN(t[0])||isNaN(t[1])}function Ed(t,e,i,n,r,a,o,s,l,h){return"none"!==h&&h?zd.apply(this,arguments):Bd.apply(this,arguments)}function zd(t,e,i,n,r,a,o,s,l,h,u){for(var c=0,d=i,f=0;n>f;f++){var p=e[d];if(d>=r||0>d)break;if(Od(p)){if(u){d+=a;continue}break}if(d===i)t[a>0?"moveTo":"lineTo"](p[0],p[1]);else if(l>0){var g=e[c],v="y"===h?1:0,m=(p[v]-g[v])*l;SS(IS,g),IS[v]=g[v]+m,SS(TS,p),TS[v]=p[v]-m,t.bezierCurveTo(IS[0],IS[1],TS[0],TS[1],p[0],p[1])}else t.lineTo(p[0],p[1]);c=d,d+=a}return f}function Bd(t,e,i,n,r,a,o,s,l,h,u){for(var c=0,d=i,f=0;n>f;f++){var p=e[d];if(d>=r||0>d)break;if(Od(p)){if(u){d+=a;continue}break}if(d===i)t[a>0?"moveTo":"lineTo"](p[0],p[1]),SS(IS,p);else if(l>0){var g=d+a,v=e[g];if(u)for(;v&&Od(e[g]);)g+=a,v=e[g];var m=.5,y=e[c],v=e[g];if(!v||Od(v))SS(TS,p);else{Od(v)&&!u&&(v=p),U(AS,v,y);var x,_;if("x"===h||"y"===h){var w="x"===h?0:1;x=Math.abs(p[w]-y[w]),_=Math.abs(p[w]-v[w])}else x=Gg(p,y),_=Gg(p,v);m=_/(_+x),MS(TS,p,AS,-l*(1-m))}wS(IS,IS,s),bS(IS,IS,o),wS(TS,TS,s),bS(TS,TS,o),t.bezierCurveTo(IS[0],IS[1],TS[0],TS[1],p[0],p[1]),MS(IS,p,AS,l*m)}else t.lineTo(p[0],p[1]);c=d,d+=a}return f}function Rd(t,e){var i=[1/0,1/0],n=[-1/0,-1/0];if(e)for(var r=0;r<t.length;r++){var a=t[r];a[0]<i[0]&&(i[0]=a[0]),a[1]<i[1]&&(i[1]=a[1]),a[0]>n[0]&&(n[0]=a[0]),a[1]>n[1]&&(n[1]=a[1])}return{min:e?i:n,max:e?n:i}}function Nd(t,e){if(t.length===e.length){for(var i=0;i<t.length;i++){var n=t[i],r=e[i];if(n[0]!==r[0]||n[1]!==r[1])return}return!0}}function Fd(t,e){var i=[],n=[],r=[],a=[];return Br(t,i,n),Br(e,r,a),Math.max(Math.abs(i[0]-r[0]),Math.abs(i[1]-r[1]),Math.abs(n[0]-a[0]),Math.abs(n[1]-a[1]))}function Vd(t){return"number"==typeof t?t:t?.5:0}function Wd(t,e,i){if(!i.valueDim)return[];for(var n=[],r=0,a=e.count();a>r;r++)n.push(Pd(i,t,e,r));return n}function Hd(t,e,i){for(var n=e.getBaseAxis(),r="x"===n.dim||"radius"===n.dim?0:1,a=[],o=0;o<t.length-1;o++){var s=t[o+1],l=t[o];a.push(l);var h=[];switch(i){case"end":h[r]=s[r],h[1-r]=l[1-r],a.push(h);break;case"middle":var u=(l[r]+s[r])/2,c=[];h[r]=c[r]=u,h[1-r]=l[1-r],c[1-r]=s[1-r],a.push(h),a.push(c);break;default:h[r]=l[r],h[1-r]=s[1-r],a.push(h)}}return t[o]&&a.push(t[o]),a}function Gd(t,e){var i=t.getVisual("visualMeta");if(i&&i.length&&t.count()){if("cartesian2d"!==e.type)return void(wg&&console.warn("Visual map on line style is only supported on cartesian2d."));for(var n,r,a=i.length-1;a>=0;a--){var o=i[a].dimension,s=t.dimensions[o],l=t.getDimensionInfo(s);if(n=l&&l.coordDim,"x"===n||"y"===n){r=i[a];break}}if(!r)return void(wg&&console.warn("Visual map on line style only support x or y dimension."));var h=e.getAxis(n),u=p(r.stops,function(t){return{coord:h.toGlobalCoord(h.dataToCoord(t.value)),color:t.color}}),c=u.length,d=r.outerColors.slice();c&&u[0].coord>u[c-1].coord&&(u.reverse(),d.reverse());var g=10,v=u[0].coord-g,m=u[c-1].coord+g,y=m-v;if(.001>y)return"transparent";f(u,function(t){t.offset=(t.coord-v)/y}),u.push({offset:c?u[c-1].offset:.5,color:d[1]||"transparent"}),u.unshift({offset:c?u[0].offset:.5,color:d[0]||"transparent"});var x=new _x(0,0,0,0,u,!0);return x[n]=v,x[n+"2"]=m,x}}function Zd(t,e,i){var n=t.get("showAllSymbol"),r="auto"===n;if(!n||r){var a=i.getAxesByScale("ordinal")[0];if(a&&(!r||!Xd(a,e))){var o=e.mapDimension(a.dim),s={};return f(a.getViewLabels(),function(t){s[t.tickValue]=1}),function(t){return!s.hasOwnProperty(e.get(o,t))}}}}function Xd(t,e){var i=t.getExtent(),n=Math.abs(i[1]-i[0])/t.scale.count();isNaN(n)&&(n=0);for(var r=e.count(),a=Math.max(1,Math.round(r/5)),o=0;r>o;o+=a)if(1.5*wd.getSymbolSize(e,o)[t.isHorizontal()?1:0]>n)return!1;return!0}function Yd(t,e,i){if("cartesian2d"===t.type){var n=t.getBaseAxis().isHorizontal(),r=zc(t,e,i);if(!i.get("clip",!0)){var a=r.shape,o=Math.max(a.width,a.height);n?(a.y-=o,a.height+=2*o):(a.x-=o,a.width+=2*o)}return r}return Bc(t,e,i)}function Ud(t,e){this.getAllNames=function(){var t=e();return t.mapArray(t.getName)},this.containName=function(t){var i=e();return i.indexOfName(t)>=0},this.indexOfName=function(e){var i=t();return i.indexOfName(e)},this.getItemVisual=function(e,i){var n=t();return n.getItemVisual(e,i)}}function qd(t,e,i,n){var r=e.getData(),a=this.dataIndex,o=r.getName(a),s=e.get("selectedOffset");n.dispatchAction({type:"pieToggleSelect",from:t,name:o,seriesId:e.id}),r.each(function(t){jd(r.getItemGraphicEl(t),r.getItemLayout(t),e.isSelected(r.getName(t)),s,i)})}function jd(t,e,i,n,r){var a=(e.startAngle+e.endAngle)/2,o=Math.cos(a),s=Math.sin(a),l=i?n:0,h=[o*l,s*l];r?t.animate().when(200,{position:h}).start("bounceOut"):t.attr("position",h)}function Kd(t,e){Fv.call(this);var i=new rx({z2:2}),n=new hx,r=new tx;this.add(i),this.add(n),this.add(r),this.updateData(t,e,!0)}function $d(t,e,i,n,r,a,o,s,l,h){function u(e,i,n){for(var r=e;i>r&&!(t[r].y+n>l+o);r++)if(t[r].y+=n,r>e&&i>r+1&&t[r+1].y>t[r].y+t[r].height)return void c(r,n/2);c(i-1,n/2)}function c(e,i){for(var n=e;n>=0&&!(t[n].y-i<l)&&(t[n].y-=i,!(n>0&&t[n].y>t[n-1].y+t[n-1].height));n--);}function d(t,e,i,n,r,a){for(var o=a>0?e?Number.MAX_VALUE:0:e?Number.MAX_VALUE:0,s=0,l=t.length;l>s;s++)if("none"===t[s].labelAlignTo){var h=Math.abs(t[s].y-n),u=t[s].len,c=t[s].len2,d=r+u>h?Math.sqrt((r+u+c)*(r+u+c)-h*h):Math.abs(t[s].x-i);e&&d>=o&&(d=o-10),!e&&o>=d&&(d=o+10),t[s].x=i+d*a,o=d}}t.sort(function(t,e){return t.y-e.y});for(var f,p=0,g=t.length,v=[],m=[],y=0;g>y;y++){if("outer"===t[y].position&&"labelLine"===t[y].labelAlignTo){var x=t[y].x-h;t[y].linePoints[1][0]+=x,t[y].x=h}f=t[y].y-p,0>f&&u(y,g,-f,r),p=t[y].y+t[y].height}0>o-p&&c(g-1,p-o);for(var y=0;g>y;y++)t[y].y>=i?m.push(t[y]):v.push(t[y]);d(v,!1,e,i,n,r),d(m,!0,e,i,n,r)}function Qd(t,e,i,n,r,a,o,s){for(var l=[],h=[],u=Number.MAX_VALUE,c=-Number.MAX_VALUE,d=0;d<t.length;d++)Jd(t[d])||(t[d].x<e?(u=Math.min(u,t[d].x),l.push(t[d])):(c=Math.max(c,t[d].x),h.push(t[d])));$d(h,e,i,n,1,r,a,o,s,c),$d(l,e,i,n,-1,r,a,o,s,u);for(var d=0;d<t.length;d++){var f=t[d];if(!Jd(f)){var p=f.linePoints;if(p){var g,v="edge"===f.labelAlignTo,m=f.textRect.width;g=v?f.x<e?p[2][0]-f.labelDistance-o-f.labelMargin:o+r-f.labelMargin-p[2][0]-f.labelDistance:f.x<e?f.x-o-f.bleedMargin:o+r-f.x-f.bleedMargin,g<f.textRect.width&&(f.text=$i(f.text,g,f.font),"edge"===f.labelAlignTo&&(m=Zi(f.text,f.font)));var y=p[1][0]-p[2][0];v?p[2][0]=f.x<e?o+f.labelMargin+m+f.labelDistance:o+r-f.labelMargin-m-f.labelDistance:(p[2][0]=f.x<e?f.x+f.labelDistance:f.x-f.labelDistance,p[1][0]=p[2][0]+y),p[1][1]=p[2][1]=f.y}}}}function Jd(t){return"center"===t.position}function tf(t,e){return $o(t.getBoxLayoutParams(),{width:e.getWidth(),height:e.getHeight()})}function ef(t,e){var i=t.get("center"),n=e.getWidth(),r=e.getHeight(),a=Math.min(n,r),o=bo(i[0],e.getWidth()),s=bo(i[1],e.getHeight()),l=bo(t.get("radius"),a/2);return{cx:o,cy:s,r:l}}function nf(t,e){return e&&("string"==typeof e?t=e.replace("{value}",null!=t?t:""):"function"==typeof e&&(t=e(t))),t}function rf(t){return t.get("stack")||"__ec_stack_"+t.seriesIndex}function af(t,e){return e.dim+t.model.componentIndex}function of(t,e){var i={},n=sf(v(e.getSeriesByType(t),function(t){return!e.isSeriesFiltered(t)&&t.coordinateSystem&&"polar"===t.coordinateSystem.type}));e.eachSeriesByType(t,function(t){if("polar"===t.coordinateSystem.type){var e=t.getData(),r=t.coordinateSystem,a=r.getBaseAxis(),o=af(r,a),s=rf(t),l=n[o][s],h=l.offset,u=l.width,c=r.getOtherAxis(a),d=t.coordinateSystem.cx,f=t.coordinateSystem.cy,p=t.get("barMinHeight")||0,g=t.get("barMinAngle")||0;i[s]=i[s]||[];for(var v=e.mapDimension(c.dim),m=e.mapDimension(a.dim),y=pu(e,v),x="radius"!==a.dim||!t.get("roundCap",!0),_="radius"===c.dim?c.dataToRadius(0):c.dataToAngle(0),w=0,b=e.count();b>w;w++){var M=e.get(v,w),S=e.get(m,w),A=M>=0?"p":"n",I=_;y&&(i[s][S]||(i[s][S]={p:_,n:_}),I=i[s][S][A]);var T,C,D,k;if("radius"===c.dim){var P=c.dataToRadius(M)-_,L=a.dataToAngle(S);Math.abs(P)<p&&(P=(0>P?-1:1)*p),T=I,C=I+P,D=L-h,k=D-u,y&&(i[s][S][A]=C)}else{var O=c.dataToAngle(M,x)-_,E=a.dataToRadius(S);Math.abs(O)<g&&(O=(0>O?-1:1)*g),T=E+h,C=T+u,D=I,k=I+O,y&&(i[s][S][A]=k)}e.setItemLayout(w,{cx:d,cy:f,r0:T,r:C,startAngle:-D*Math.PI/180,endAngle:-k*Math.PI/180})}}},this)}function sf(t){var e={};f(t,function(t){var i=t.getData(),n=t.coordinateSystem,r=n.getBaseAxis(),a=af(n,r),o=r.getExtent(),s="category"===r.type?r.getBandWidth():Math.abs(o[1]-o[0])/i.count(),l=e[a]||{bandWidth:s,remainedWidth:s,autoWidthCount:0,categoryGap:"20%",gap:"30%",stacks:{}},h=l.stacks;e[a]=l;var u=rf(t);h[u]||l.autoWidthCount++,h[u]=h[u]||{width:0,maxWidth:0};var c=bo(t.get("barWidth"),s),d=bo(t.get("barMaxWidth"),s),f=t.get("barGap"),p=t.get("barCategoryGap");c&&!h[u].width&&(c=Math.min(l.remainedWidth,c),h[u].width=c,l.remainedWidth-=c),d&&(h[u].maxWidth=d),null!=f&&(l.gap=f),null!=p&&(l.categoryGap=p)});var i={};return f(e,function(t,e){i[e]={};var n=t.stacks,r=t.bandWidth,a=bo(t.categoryGap,r),o=bo(t.gap,1),s=t.remainedWidth,l=t.autoWidthCount,h=(s-a)/(l+(l-1)*o);h=Math.max(h,0),f(n,function(t){var e=t.maxWidth;e&&h>e&&(e=Math.min(e,s),t.width&&(e=Math.min(e,t.width)),s-=e,t.width=e,l--)}),h=(s-a)/(l+(l-1)*o),h=Math.max(h,0);var u,c=0;f(n,function(t){t.width||(t.width=h),u=t,c+=t.width*(1+o)}),u&&(c-=u.width*o);var d=-c/2;f(n,function(t,n){i[e][n]=i[e][n]||{offset:d,width:t.width},d+=t.width*(1+o)})}),i}function lf(t,e){TM.call(this,"radius",t,e),this.type="category"}function hf(t,e){e=e||[0,360],TM.call(this,"angle",t,e),this.type="category"}function uf(t,e){return e.type||(e.data?"category":"value")}function cf(t,e,i){var n=e.get("center"),r=i.getWidth(),a=i.getHeight();t.cx=bo(n[0],r),t.cy=bo(n[1],a);var o=t.getRadiusAxis(),s=Math.min(r,a)/2,l=e.get("radius");null==l?l=[0,"100%"]:_(l)||(l=[0,l]),l=[bo(l[0],s),bo(l[1],s)],o.inverse?o.setExtent(l[1],l[0]):o.setExtent(l[0],l[1])}function df(t){var e=this,i=e.getAngleAxis(),n=e.getRadiusAxis();if(i.scale.setExtent(1/0,-1/0),n.scale.setExtent(1/0,-1/0),t.eachSeries(function(t){if(t.coordinateSystem===e){var r=t.getData();f(r.mapDimension("radius",!0),function(t){n.scale.unionExtentFromData(r,gu(r,t))}),f(r.mapDimension("angle",!0),function(t){i.scale.unionExtentFromData(r,gu(r,t))})}}),Wu(i.scale,i.model),Wu(n.scale,n.model),"category"===i.type&&!i.onBand){var r=i.getExtent(),a=360/i.scale.count();i.inverse?r[1]+=a:r[1]-=a,i.setExtent(r[0],r[1])}}function ff(t,e){if(t.type=e.get("type"),t.scale=Hu(e),t.onBand=e.get("boundaryGap")&&"category"===t.type,t.inverse=e.get("inverse"),"angleAxis"===e.mainType){t.inverse^=e.get("clockwise");var i=e.get("startAngle");t.setExtent(i,i+(t.inverse?-360:360))}e.axis=t,t.model=e}function pf(t,e,i){e[1]>e[0]&&(e=e.slice().reverse());var n=t.coordToPoint([e[0],i]),r=t.coordToPoint([e[1],i]);return{x1:n[0],y1:n[1],x2:r[0],y2:r[1]}}function gf(t){var e=t.getRadiusAxis();return e.inverse?0:1}function vf(t){var e=t[0],i=t[t.length-1];e&&i&&Math.abs(Math.abs(e.coord-i.coord)-360)<1e-4&&t.pop()}function mf(t,e,i){return{position:[t.cx,t.cy],rotation:i/180*Math.PI,labelDirection:-1,tickDirection:-1,nameDirection:1,labelRotate:e.getModel("axisLabel").get("rotate"),z2:1}}function yf(t,e,i,n,r){var a=t.axis;if(!a.scale.isBlank()&&a.containData(e)){if(!t.involveSeries)return void i.showPointer(t,e);var s=xf(e,t),l=s.payloadBatch,h=s.snapToValue;l[0]&&null==r.seriesIndex&&o(r,l[0]),!n&&t.snap&&a.containData(h)&&null!=h&&(e=h),i.showPointer(t,e,l,r),i.showTooltip(t,s,h)}}function xf(t,e){var i=e.axis,n=i.dim,r=t,a=[],o=Number.MAX_VALUE,s=-1;return rA(e.seriesModels,function(e){var l,h,u=e.getData().mapDimension(n,!0);if(e.getAxisTooltipData){var c=e.getAxisTooltipData(u,t,i);h=c.dataIndices,l=c.nestestValue}else{if(h=e.getData().indicesOfNearest(u[0],t,"category"===i.type?.5:null),!h.length)return;l=e.getData().get(u[0],h[0])}if(null!=l&&isFinite(l)){var d=t-l,f=Math.abs(d);o>=f&&((o>f||d>=0&&0>s)&&(o=f,s=d,r=l,a.length=0),rA(h,function(t){a.push({seriesIndex:e.seriesIndex,dataIndexInside:t,dataIndex:e.getData().getRawIndex(t)})}))}}),{payloadBatch:a,snapToValue:r}}function _f(t,e,i,n){t[e.key]={value:i,payloadBatch:n}}function wf(t,e,i,n){var r=i.payloadBatch,a=e.axis,o=a.model,s=e.axisPointerModel;if(e.triggerTooltip&&r.length){var l=e.coordSys.model,h=gd(l),u=t.map[h];u||(u=t.map[h]={coordSysId:l.id,coordSysIndex:l.componentIndex,coordSysType:l.type,coordSysMainType:l.mainType,dataByAxis:[]},t.list.push(u)),u.dataByAxis.push({axisDim:a.dim,axisIndex:o.componentIndex,axisType:o.type,axisId:o.id,value:n,valueLabelOpt:{precision:s.get("label.precision"),formatter:s.get("label.formatter")},seriesDataIndices:r.slice()})}}function bf(t,e,i){var n=i.axesInfo=[];rA(e,function(e,i){var r=e.axisPointerModel.option,a=t[i];a?(!e.useHandle&&(r.status="show"),r.value=a.value,r.seriesDataIndices=(a.payloadBatch||[]).slice()):!e.useHandle&&(r.status="hide"),"show"===r.status&&n.push({axisDim:e.axis.dim,axisIndex:e.axis.model.componentIndex,value:r.value})})}function Mf(t,e,i,n){if(Tf(e)||!t.list.length)return void n({type:"hideTip"});var r=((t.list[0].dataByAxis[0]||{}).seriesDataIndices||[])[0]||{};n({type:"showTip",escapeConnect:!0,x:e[0],y:e[1],tooltipOption:i.tooltipOption,position:i.position,dataIndexInside:r.dataIndexInside,dataIndex:r.dataIndex,seriesIndex:r.seriesIndex,dataByCoordSys:t.list})}function Sf(t,e,i){var n=i.getZr(),r="axisPointerLastHighlights",a=oA(n)[r]||{},o=oA(n)[r]={};rA(t,function(t){var e=t.axisPointerModel.option;"show"===e.status&&rA(e.seriesDataIndices,function(t){var e=t.seriesIndex+" | "+t.dataIndex;o[e]=t})});var s=[],l=[];f(a,function(t,e){!o[e]&&l.push(t)}),f(o,function(t,e){!a[e]&&s.push(t)}),l.length&&i.dispatchAction({type:"downplay",escapeConnect:!0,batch:l}),s.length&&i.dispatchAction({type:"highlight",escapeConnect:!0,batch:s})}function Af(t,e){for(var i=0;i<(t||[]).length;i++){var n=t[i];if(e.axis.dim===n.axisDim&&e.axis.model.componentIndex===n.axisIndex)return n}}function If(t){var e=t.axis.model,i={},n=i.axisDim=t.axis.dim;return i.axisIndex=i[n+"AxisIndex"]=e.componentIndex,i.axisName=i[n+"AxisName"]=e.name,i.axisId=i[n+"AxisId"]=e.id,i}function Tf(t){return!t||null==t[0]||isNaN(t[0])||null==t[1]||isNaN(t[1])}function Cf(t,e,i){if(!Ag.node){var n=e.getZr();lA(n).records||(lA(n).records={}),Df(n,e);var r=lA(n).records[t]||(lA(n).records[t]={});r.handler=i}}function Df(t,e){function i(i,n){t.on(i,function(i){var r=Of(e);hA(lA(t).records,function(t){t&&n(t,i,r.dispatchAction)}),kf(r.pendings,e)})}lA(t).initialized||(lA(t).initialized=!0,i("click",x(Lf,"click")),i("mousemove",x(Lf,"mousemove")),i("globalout",Pf))}function kf(t,e){var i,n=t.showTip.length,r=t.hideTip.length;n?i=t.showTip[n-1]:r&&(i=t.hideTip[r-1]),i&&(i.dispatchAction=null,e.dispatchAction(i))}function Pf(t,e,i){t.handler("leave",null,i)}function Lf(t,e,i,n){e.handler(t,i,n)}function Of(t){var e={showTip:[],hideTip:[]},i=function(n){var r=e[n.type];r?r.push(n):(n.dispatchAction=i,t.dispatchAction(n))};return{dispatchAction:i,pendings:e}}function Ef(t,e){if(!Ag.node){var i=e.getZr(),n=(lA(i).records||{})[t];n&&(lA(i).records[t]=null)}}function zf(){}function Bf(t,e,i,n){Rf(cA(i).lastProp,n)||(cA(i).lastProp=n,e?to(i,n,t):(i.stopAnimation(),i.attr(n)))}function Rf(t,e){if(M(t)&&M(e)){var i=!0;return f(e,function(e,n){i=i&&Rf(t[n],e)}),!!i}return t===e}function Nf(t,e){t[e.get("label.show")?"show":"hide"]()}function Ff(t){return{position:t.position.slice(),rotation:t.rotation||0}}function Vf(t,e,i){var n=e.get("z"),r=e.get("zlevel");t&&t.traverse(function(t){"group"!==t.type&&(null!=n&&(t.z=n),null!=r&&(t.zlevel=r),t.silent=i)})}function Wf(t){var e,i=t.get("type"),n=t.getModel(i+"Style");return"line"===i?(e=n.getLineStyle(),e.fill=null):"shadow"===i&&(e=n.getAreaStyle(),e.stroke=null),e}function Hf(t,e,i,n,r){var a=i.get("value"),o=Zf(a,e.axis,e.ecModel,i.get("seriesDataIndices"),{precision:i.get("label.precision"),formatter:i.get("label.formatter")}),s=i.getModel("label"),l=Kx(s.get("padding")||0),h=s.getFont(),u=Xi(o,h),c=r.position,d=u.width+l[1]+l[3],f=u.height+l[0]+l[2],p=r.align;"right"===p&&(c[0]-=d),"center"===p&&(c[0]-=d/2);var g=r.verticalAlign;"bottom"===g&&(c[1]-=f),"middle"===g&&(c[1]-=f/2),Gf(c,d,f,n);var v=s.get("backgroundColor");v&&"auto"!==v||(v=e.get("axisLine.lineStyle.color")),t.label={shape:{x:0,y:0,width:d,height:f,r:s.get("borderRadius")},position:c.slice(),style:{text:o,textFont:h,textFill:s.getTextColor(),textPosition:"inside",textPadding:l,fill:v,stroke:s.get("borderColor")||"transparent",lineWidth:s.get("borderWidth")||0,shadowBlur:s.get("shadowBlur"),shadowColor:s.get("shadowColor"),shadowOffsetX:s.get("shadowOffsetX"),shadowOffsetY:s.get("shadowOffsetY")},z2:10}}function Gf(t,e,i,n){var r=n.getWidth(),a=n.getHeight();t[0]=Math.min(t[0]+e,r)-e,t[1]=Math.min(t[1]+i,a)-i,t[0]=Math.max(t[0],0),t[1]=Math.max(t[1],0)}function Zf(t,e,i,n,r){t=e.scale.parse(t);var a=e.scale.getLabel(t,{precision:r.precision}),o=r.formatter;if(o){var s={value:Xu(e,t),axisDimension:e.dim,axisIndex:e.index,seriesData:[]};f(n,function(t){var e=i.getSeriesByIndex(t.seriesIndex),n=t.dataIndexInside,r=e&&e.getDataParams(n);r&&s.seriesData.push(r)}),b(o)?a=o.replace("{value}",a):w(o)&&(a=o(s))}return a}function Xf(t,e,i){var n=Oe();return Ne(n,n,i.rotation),Re(n,n,i.position),no([t.dataToCoord(e),(i.labelOffset||0)+(i.labelDirection||1)*(i.labelMargin||0)],n)}function Yf(t,e,i,n,r,a){var o=eS.innerTextLayout(i.rotation,0,i.labelDirection);i.labelMargin=r.get("label.margin"),Hf(e,n,r,a,{position:Xf(n.axis,t,i),align:o.textAlign,verticalAlign:o.textVerticalAlign})}function Uf(t,e,i){return i=i||0,{x1:t[i],y1:t[1-i],x2:e[i],y2:e[1-i]}}function qf(t,e,i){return i=i||0,{x:t[i],y:t[1-i],width:e[i],height:e[1-i]}}function jf(t,e,i,n,r,a){return{cx:t,cy:e,r0:i,r:n,startAngle:r,endAngle:a,clockwise:!0}}function Kf(t,e){var i={};return i[e.dim+"AxisIndex"]=e.index,t.getCartesian(i)}function $f(t){return"x"===t.dim?0:1}function Qf(t,e,i,n,r){var a=e.axis,o=a.dataToCoord(t),s=n.getAngleAxis().getExtent()[0];s=s/180*Math.PI;var l,h,u,c=n.getRadiusAxis().getExtent();if("radius"===a.dim){var d=Oe();Ne(d,d,s),Re(d,d,[n.cx,n.cy]),l=no([o,-r],d);var f=e.getModel("axisLabel").get("rotate")||0,p=eS.innerTextLayout(s,f*Math.PI/180,-1);h=p.textAlign,u=p.textVerticalAlign}else{var g=c[1];l=n.coordToPoint([g+r,o]);var v=n.cx,m=n.cy;h=Math.abs(l[0]-v)/g<.3?"center":l[0]>v?"left":"right",u=Math.abs(l[1]-m)/g<.3?"middle":l[1]>m?"top":"bottom"}return{position:l,align:h,verticalAlign:u}}function Jf(t){var e="cubic-bezier(0.23, 1, 0.32, 1)",i="left "+t+"s "+e+",top "+t+"s "+e;return p(_A,function(t){return t+"transition:"+i}).join(";")}function tp(t){var e=[],i=t.get("fontSize"),n=t.getTextColor();n&&e.push("color:"+n),e.push("font:"+t.getFont());var r=t.get("lineHeight");null==r&&(r=Math.round(3*i/2)),i&&e.push("line-height:"+r+"px");var a=t.get("textShadowColor"),o=t.get("textShadowBlur")||0,s=t.get("textShadowOffsetX")||0,l=t.get("textShadowOffsetY")||0;return o&&e.push("text-shadow:"+s+"px "+l+"px "+o+"px "+a),yA(["decoration","align"],function(i){var n=t.get(i);n&&e.push("text-"+i+":"+n)}),e.join(";")}function ep(t){var e=[],i=t.get("transitionDuration"),n=t.get("backgroundColor"),r=t.getModel("textStyle"),a=t.get("padding");return i&&e.push(Jf(i)),n&&(Ag.canvasSupported?e.push("background-Color:"+n):(e.push("background-Color:#"+ri(n)),e.push("filter:alpha(opacity=70)"))),yA(["width","color","radius"],function(i){var n="border-"+i,r=xA(n),a=t.get(r);null!=a&&e.push(n+":"+a+("color"===i?"":"px"))}),e.push(tp(r)),null!=a&&e.push("padding:"+Kx(a).join("px ")+"px"),e.join(";")+";"}function ip(t,e,i,n,r){var a=e&&e.painter;if(i){var o=a&&a.getViewportRoot();o&&pe(t,o,document.body,n,r)}else{t[0]=n,t[1]=r;var s=a&&a.getViewportRootOffset();s&&(t[0]+=s.offsetLeft,t[1]+=s.offsetTop)}t[2]=t[0]/e.getWidth(),t[3]=t[1]/e.getHeight()}function np(t,e,i){if(Ag.wxa)return null;var n=document.createElement("div");n.domBelongToZr=!0,this.el=n;var r=this._zr=e.getZr(),a=this._appendToBody=i&&i.appendToBody;this._styleCoord=[0,0,0,0],ip(this._styleCoord,r,a,e.getWidth()/2,e.getHeight()/2),a?document.body.appendChild(n):t.appendChild(n),this._container=t,this._show=!1,this._hideTimeout;var o=this;n.onmouseenter=function(){o._enterable&&(clearTimeout(o._hideTimeout),o._show=!0),o._inContent=!0},n.onmousemove=function(t){if(t=t||window.event,!o._enterable){var e=r.handler,i=r.painter.getViewportRoot();be(i,t,!0),e.dispatch("mousemove",t)}},n.onmouseleave=function(){o._enterable&&o._show&&o.hideLater(o._hideDelay),o._inContent=!1}}function rp(t,e,i,n){t[0]=i,t[1]=n,t[2]=t[0]/e.getWidth(),t[3]=t[1]/e.getHeight()}function ap(t){var e=this._zr=t.getZr();this._styleCoord=[0,0,0,0],rp(this._styleCoord,e,t.getWidth()/2,t.getHeight()/2),this._show=!1,this._hideTimeout}function op(t){for(var e=t.pop();t.length;){var i=t.pop();i&&(po.isInstance(i)&&(i=i.get("tooltip",!0)),"string"==typeof i&&(i={formatter:i}),e=new po(i,e,e.ecModel))}return e}function sp(t,e){return t.dispatchAction||y(e.dispatchAction,e)}function lp(t,e,i,n,r,a,o){var s=i.getOuterSize(),l=s.width,h=s.height;return null!=a&&(t+l+a>n?t-=l+a:t+=a),null!=o&&(e+h+o>r?e-=h+o:e+=o),[t,e]}function hp(t,e,i,n,r){var a=i.getOuterSize(),o=a.width,s=a.height;return t=Math.min(t+o,n)-o,e=Math.min(e+s,r)-s,t=Math.max(t,0),e=Math.max(e,0),[t,e]}function up(t,e,i){var n=i[0],r=i[1],a=5,o=0,s=0,l=e.width,h=e.height;switch(t){case"inside":o=e.x+l/2-n/2,s=e.y+h/2-r/2;break;case"top":o=e.x+l/2-n/2,s=e.y-r-a;break;case"bottom":o=e.x+l/2-n/2,s=e.y+h+a;break;case"left":o=e.x-n-a,s=e.y+h/2-r/2;break;case"right":o=e.x+l+a,s=e.y+h/2-r/2}return[o,s]}function cp(t){return"center"===t||"middle"===t}function dp(t){er(t,"label",["show"])}function fp(t){return!(isNaN(parseFloat(t.x))&&isNaN(parseFloat(t.y)))}function pp(t){return!isNaN(parseFloat(t.x))&&!isNaN(parseFloat(t.y))}function gp(t,e,i,n,r,a){var o=[],s=pu(e,n),l=s?e.getCalculationInfo("stackResultDimension"):n,h=wp(e,l,t),u=e.indicesOfNearest(l,h)[0];o[r]=e.get(i,u),o[a]=e.get(l,u);var c=e.get(n,u),d=Ao(e.get(n,u));return d=Math.min(d,20),d>=0&&(o[a]=+o[a].toFixed(d)),[o,c]}function vp(t,e){var i=t.getData(),r=t.coordinateSystem;if(e&&!pp(e)&&!_(e.coord)&&r){var a=r.dimensions,o=mp(e,i,r,t);if(e=n(e),e.type&&PA[e.type]&&o.baseAxis&&o.valueAxis){var s=DA(a,o.baseAxis.dim),l=DA(a,o.valueAxis.dim),h=PA[e.type](i,o.baseDataDim,o.valueDataDim,s,l);e.coord=h[0],e.value=h[1]}else{for(var u=[null!=e.xAxis?e.xAxis:e.radiusAxis,null!=e.yAxis?e.yAxis:e.angleAxis],c=0;2>c;c++)PA[u[c]]&&(u[c]=wp(i,i.mapDimension(a[c]),u[c]));e.coord=u}}return e}function mp(t,e,i,n){var r={};return null!=t.valueIndex||null!=t.valueDim?(r.valueDataDim=null!=t.valueIndex?e.getDimension(t.valueIndex):t.valueDim,r.valueAxis=i.getAxis(yp(n,r.valueDataDim)),r.baseAxis=i.getOtherAxis(r.valueAxis),r.baseDataDim=e.mapDimension(r.baseAxis.dim)):(r.baseAxis=n.getBaseAxis(),r.valueAxis=i.getOtherAxis(r.baseAxis),r.baseDataDim=e.mapDimension(r.baseAxis.dim),r.valueDataDim=e.mapDimension(r.valueAxis.dim)),r}function yp(t,e){var i=t.getData(),n=i.dimensions;e=i.getDimension(e);for(var r=0;r<n.length;r++){var a=i.getDimensionInfo(n[r]);if(a.name===e)return a.coordDim}}function xp(t,e){return t&&t.containData&&e.coord&&!fp(e)?t.containData(e.coord):!0}function _p(t,e,i,n){return 2>n?t.coord&&t.coord[n]:t.value}function wp(t,e,i){if("average"===i){var n=0,r=0;return t.each(e,function(t){isNaN(t)||(n+=t,r++)}),n/r}return"median"===i?t.getMedian(e):t.getDataExtent(e,!0)["max"===i?1:0]}function bp(t){return isNaN(+t.cpx1)||isNaN(+t.cpy1)}function Mp(t){return"_"+t+"Type"}function Sp(t,e,i){var n=e.getItemVisual(i,t);if(n&&"none"!==n){var r=e.getItemVisual(i,"color"),a=e.getItemVisual(i,t+"Size"),o=e.getItemVisual(i,t+"Rotate");_(a)||(a=[a,a]);var s=$u(n,-a[0]/2,-a[1]/2,a[0],a[1],r);return s.__specifiedRotation=null==o||isNaN(o)?void 0:+o*Math.PI/180||0,s.name=t,s}}function Ap(t){var e=new EA({name:"line",subPixelOptimize:!0});return Ip(e.shape,t),e}function Ip(t,e){t.x1=e[0][0],t.y1=e[0][1],t.x2=e[1][0],t.y2=e[1][1],t.percent=1;var i=e[2];i?(t.cpx1=i[0],t.cpy1=i[1]):(t.cpx1=0/0,t.cpy1=0/0)}function Tp(){var t=this,e=t.childOfName("fromSymbol"),i=t.childOfName("toSymbol"),n=t.childOfName("label");if(e||i||!n.ignore){for(var r=1,a=this.parent;a;)a.scale&&(r/=a.scale[0]),a=a.parent;var o=t.childOfName("line");if(this.__dirty||o.__dirty){var s=o.shape.percent,l=o.pointAt(0),h=o.pointAt(s),u=U([],h,l);if(te(u,u),e){e.attr("position",l);var c=e.__specifiedRotation;if(null==c){var d=o.tangentAt(0);e.attr("rotation",Math.PI/2-Math.atan2(d[1],d[0]))}else e.attr("rotation",c);e.attr("scale",[r*s,r*s])}if(i){i.attr("position",h);var c=i.__specifiedRotation;if(null==c){var d=o.tangentAt(1);i.attr("rotation",-Math.PI/2-Math.atan2(d[1],d[0]))}else i.attr("rotation",c);i.attr("scale",[r*s,r*s])}if(!n.ignore){n.attr("position",h);var f,p,g,v,m=n.__labelDistance,y=m[0]*r,x=m[1]*r,_=s/2,d=o.tangentAt(_),w=[d[1],-d[0]],b=o.pointAt(_);w[1]>0&&(w[0]=-w[0],w[1]=-w[1]);var M=d[0]<0?-1:1;if("start"!==n.__position&&"end"!==n.__position){var S=-Math.atan2(d[1],d[0]);h[0]<l[0]&&(S=Math.PI+S),n.attr("rotation",S)}var A;switch(n.__position){case"insideStartTop":case"insideMiddleTop":case"insideEndTop":case"middle":A=-x,g="bottom";break;case"insideStartBottom":case"insideMiddleBottom":case"insideEndBottom":A=x,g="top";break;default:A=0,g="middle"}switch(n.__position){case"end":f=[u[0]*y+h[0],u[1]*x+h[1]],p=u[0]>.8?"left":u[0]<-.8?"right":"center",g=u[1]>.8?"top":u[1]<-.8?"bottom":"middle";break;case"start":f=[-u[0]*y+l[0],-u[1]*x+l[1]],p=u[0]>.8?"right":u[0]<-.8?"left":"center",g=u[1]>.8?"bottom":u[1]<-.8?"top":"middle";
break;case"insideStartTop":case"insideStart":case"insideStartBottom":f=[y*M+l[0],l[1]+A],p=d[0]<0?"right":"left",v=[-y*M,-A];break;case"insideMiddleTop":case"insideMiddle":case"insideMiddleBottom":case"middle":f=[b[0],b[1]+A],p="center",v=[0,-A];break;case"insideEndTop":case"insideEnd":case"insideEndBottom":f=[-y*M+h[0],h[1]+A],p=d[0]>=0?"right":"left",v=[y*M,-A]}n.attr({style:{textVerticalAlign:n.__verticalAlign||g,textAlign:n.__textAlign||p},position:f,scale:[r,r],origin:v})}}}}function Cp(t,e,i){Fv.call(this),this._createLine(t,e,i)}function Dp(t){this._ctor=t||Cp,this.group=new Fv}function kp(t,e,i,n){var r=e.getItemLayout(i);if(zp(r)){var a=new t._ctor(e,i,n);e.setItemGraphicEl(i,a),t.group.add(a)}}function Pp(t,e,i,n,r,a){var o=e.getItemGraphicEl(n);return zp(i.getItemLayout(r))?(o?o.updateData(i,r,a):o=new t._ctor(i,r,a),i.setItemGraphicEl(r,o),void t.group.add(o)):void t.group.remove(o)}function Lp(t){return t.animators&&t.animators.length>0}function Op(t){var e=t.hostModel;return{lineStyle:e.getModel("lineStyle").getLineStyle(),hoverLineStyle:e.getModel("emphasis.lineStyle").getLineStyle(),labelModel:e.getModel("label"),hoverLabelModel:e.getModel("emphasis.label")}}function Ep(t){return isNaN(t[0])||isNaN(t[1])}function zp(t){return!Ep(t[0])&&!Ep(t[1])}function Bp(t){return!isNaN(t)&&!isFinite(t)}function Rp(t,e,i,n){var r=1-t,a=n.dimensions[t];return Bp(e[r])&&Bp(i[r])&&e[t]===i[t]&&n.getAxis(a).containData(e[t])}function Np(t,e){if("cartesian2d"===t.type){var i=e[0].coord,n=e[1].coord;if(i&&n&&(Rp(1,i,n,t)||Rp(0,i,n,t)))return!0}return xp(t,e[0])&&xp(t,e[1])}function Fp(t,e,i,n,r){var a,o=n.coordinateSystem,s=t.getItemModel(e),l=bo(s.get("x"),r.getWidth()),h=bo(s.get("y"),r.getHeight());if(isNaN(l)||isNaN(h)){if(n.getMarkerPosition)a=n.getMarkerPosition(t.getValues(t.dimensions,e));else{var u=o.dimensions,c=t.get(u[0],e),d=t.get(u[1],e);a=o.dataToPoint([c,d])}if("cartesian2d"===o.type){var f=o.getAxis("x"),p=o.getAxis("y"),u=o.dimensions;Bp(t.get(u[0],e))?a[0]=f.toGlobalCoord(f.getExtent()[i?0:1]):Bp(t.get(u[1],e))&&(a[1]=p.toGlobalCoord(p.getExtent()[i?0:1]))}isNaN(l)||(a[0]=l),isNaN(h)||(a[1]=h)}else a=[l,h];t.setItemLayout(e,a)}function Vp(t,e,i){var n;n=t?p(t&&t.dimensions,function(t){var i=e.getData().getDimensionInfo(e.getData().mapDimension(t))||{};return s({name:t},i)}):[{name:"value",type:"float"}];var r=new Lb(n,i),a=new Lb(n,i),o=new Lb([],i),l=p(i.get("data"),x(FA,e,t,i));t&&(l=v(l,x(Np,t)));var h=t?_p:function(t){return t.value};return r.initData(p(l,function(t){return t[0]}),null,h),a.initData(p(l,function(t){return t[1]}),null,h),o.initData(p(l,function(t){return t[2]})),o.hasItemOption=!0,{from:r,to:a,line:o}}function Wp(t){return h(WA,t)>=0}function Hp(t,e){t=t.slice();var i=p(t,Yo);e=(e||[]).slice();var n=p(e,Yo);return function(r,a){f(t,function(t,o){for(var s={name:t,capital:i[o]},l=0;l<e.length;l++)s[e[l]]=t+n[l];r.call(a,s)})}}function Gp(t,e,i){function n(t,e){return h(e.nodes,t)>=0}function r(t,n){var r=!1;return e(function(e){f(i(t,e)||[],function(t){n.records[e.name][t]&&(r=!0)})}),r}function a(t,n){n.nodes.push(t),e(function(e){f(i(t,e)||[],function(t){n.records[e.name][t]=!0})})}return function(i){function o(t){!n(t,s)&&r(t,s)&&(a(t,s),l=!0)}var s={nodes:[],records:{}};if(e(function(t){s.records[t.name]={}}),!i)return s;a(i,s);var l;do l=!1,t(o);while(l);return s}}function Zp(t,e){var i=t[e]-t[1-e];return{span:Math.abs(i),sign:i>0?-1:0>i?1:e?-1:1}}function Xp(t,e){return Math.min(null!=e[1]?e[1]:1/0,Math.max(null!=e[0]?e[0]:-1/0,t))}function Yp(t,e,i){var n=[1/0,-1/0];return ZA(i,function(t){var i=t.getData();i&&ZA(i.mapDimension(e,!0),function(t){var e=i.getApproximateExtent(t);e[0]<n[0]&&(n[0]=e[0]),e[1]>n[1]&&(n[1]=e[1])})}),n[1]<n[0]&&(n=[0/0,0/0]),Up(t,n),n}function Up(t,e){var i=t.getAxisModel(),n=i.getMin(!0),r="category"===i.get("type"),a=r&&i.getCategories().length;null!=n&&"dataMin"!==n&&"function"!=typeof n?e[0]=n:r&&(e[0]=a>0?0:0/0);var o=i.getMax(!0);return null!=o&&"dataMax"!==o&&"function"!=typeof o?e[1]=o:r&&(e[1]=a>0?a-1:0/0),i.get("scale",!0)||(e[0]>0&&(e[0]=0),e[1]<0&&(e[1]=0)),e}function qp(t,e){var i=t.getAxisModel(),n=t._percentWindow,r=t._valueWindow;if(n){var a=To(r,[0,500]);a=Math.min(a,20);var o=e||0===n[0]&&100===n[1];i.setRange(o?null:+r[0].toFixed(a),o?null:+r[1].toFixed(a))}}function jp(t){var e=t._minMaxSpan={},i=t._dataZoomModel,n=t._dataExtent;ZA(["min","max"],function(r){var a=i.get(r+"Span"),o=i.get(r+"ValueSpan");null!=o&&(o=t.getAxisModel().axis.scale.parse(o)),null!=o?a=wo(n[0]+o,n,[0,100],!0):null!=a&&(o=wo(a,[0,100],n,!0)-n[0]),e[r+"Span"]=a,e[r+"ValueSpan"]=o})}function Kp(t){var e={};return UA(["start","end","startValue","endValue","throttle"],function(i){t.hasOwnProperty(i)&&(e[i]=t[i])}),e}function $p(t,e){var i=t._rangePropMode,n=t.get("rangeMode");UA([["start","startValue"],["end","endValue"]],function(t,r){var a=null!=e[t[0]],o=null!=e[t[1]];a&&!o?i[r]="percent":!a&&o?i[r]="value":n?i[r]=n[r]:a&&(i[r]="percent")})}function Qp(t){var e={x:"y",y:"x",radius:"angle",angle:"radius"};return e[t]}function Jp(t){return"vertical"===t?"ns-resize":"ew-resize"}function tg(t,e){return!!eg(t)[e]}function eg(t){return t[uI]||(t[uI]={})}function ig(t){this.pointerChecker,this._zr=t,this._opt={};var e=y,i=e(ng,this),r=e(rg,this),a=e(ag,this),o=e(og,this),l=e(sg,this);Ug.call(this),this.setPointerChecker=function(t){this.pointerChecker=t},this.enable=function(e,h){this.disable(),this._opt=s(n(h)||{},{zoomOnMouseWheel:!0,moveOnMouseMove:!0,moveOnMouseWheel:!1,preventDefaultMouseMove:!0}),null==e&&(e=!0),(e===!0||"move"===e||"pan"===e)&&(t.on("mousedown",i),t.on("mousemove",r),t.on("mouseup",a)),(e===!0||"scale"===e||"zoom"===e)&&(t.on("mousewheel",o),t.on("pinch",l))},this.disable=function(){t.off("mousedown",i),t.off("mousemove",r),t.off("mouseup",a),t.off("mousewheel",o),t.off("pinch",l)},this.dispose=this.disable,this.isDragging=function(){return this._dragging},this.isPinching=function(){return this._pinching}}function ng(t){if(!(Ae(t)||t.target&&t.target.draggable)){var e=t.offsetX,i=t.offsetY;this.pointerChecker&&this.pointerChecker(t,e,i)&&(this._x=e,this._y=i,this._dragging=!0)}}function rg(t){if(this._dragging&&ug("moveOnMouseMove",t,this._opt)&&"pinch"!==t.gestureEvent&&!tg(this._zr,"globalPan")){var e=t.offsetX,i=t.offsetY,n=this._x,r=this._y,a=e-n,o=i-r;this._x=e,this._y=i,this._opt.preventDefaultMouseMove&&tv(t.event),hg(this,"pan","moveOnMouseMove",t,{dx:a,dy:o,oldX:n,oldY:r,newX:e,newY:i})}}function ag(t){Ae(t)||(this._dragging=!1)}function og(t){var e=ug("zoomOnMouseWheel",t,this._opt),i=ug("moveOnMouseWheel",t,this._opt),n=t.wheelDelta,r=Math.abs(n),a=t.offsetX,o=t.offsetY;if(0!==n&&(e||i)){if(e){var s=r>3?1.4:r>1?1.2:1.1,l=n>0?s:1/s;lg(this,"zoom","zoomOnMouseWheel",t,{scale:l,originX:a,originY:o})}if(i){var h=Math.abs(n),u=(n>0?1:-1)*(h>3?.4:h>1?.15:.05);lg(this,"scrollMove","moveOnMouseWheel",t,{scrollDelta:u,originX:a,originY:o})}}}function sg(t){if(!tg(this._zr,"globalPan")){var e=t.pinchScale>1?1.1:1/1.1;lg(this,"zoom",null,t,{scale:e,originX:t.pinchX,originY:t.pinchY})}}function lg(t,e,i,n,r){t.pointerChecker&&t.pointerChecker(n,r.originX,r.originY)&&(tv(n.event),hg(t,e,i,n,r))}function hg(t,e,i,n,r){r.isAvailableBehavior=y(ug,null,i,n),t.trigger(e,r)}function ug(t,e,i){var n=i[t];return!t||n&&(!b(n)||e.event[n+"Key"])}function cg(t,e){var i=pg(t),n=e.dataZoomId,r=e.coordId;f(i,function(t){var i=t.dataZoomInfos;i[n]&&h(e.allCoordIds,r)<0&&(delete i[n],t.count--)}),vg(i);var a=i[r];a||(a=i[r]={coordId:r,dataZoomInfos:{},count:0},a.controller=gg(t,a),a.dispatchAction=x(mg,t)),!a.dataZoomInfos[n]&&a.count++,a.dataZoomInfos[n]=e;var o=yg(a.dataZoomInfos);a.controller.enable(o.controlType,o.opt),a.controller.setPointerChecker(e.containsPoint),yl(a,"dispatchAction",e.dataZoomModel.get("throttle",!0),"fixRate")}function dg(t,e){var i=pg(t);f(i,function(t){t.controller.dispose();var i=t.dataZoomInfos;i[e]&&(delete i[e],t.count--)}),vg(i)}function fg(t){return t.type+"\x00_"+t.id}function pg(t){var e=t.getZr();return e[cI]||(e[cI]={})}function gg(t,e){var i=new ig(t.getZr());return f(["pan","zoom","scrollMove"],function(t){i.on(t,function(i){var n=[];f(e.dataZoomInfos,function(r){if(i.isAvailableBehavior(r.dataZoomModel.option)){var a=(r.getRange||{})[t],o=a&&a(e.controller,i);!r.dataZoomModel.get("disabled",!0)&&o&&n.push({dataZoomId:r.dataZoomId,start:o[0],end:o[1]})}}),n.length&&e.dispatchAction(n)})}),i}function vg(t){f(t,function(e,i){e.count||(e.controller.dispose(),delete t[i])})}function mg(t,e){t.dispatchAction({type:"dataZoom",batch:e})}function yg(t){var e,i="type_",n={type_true:2,type_move:1,type_false:0,type_undefined:-1},r=!0;return f(t,function(t){var a=t.dataZoomModel,o=a.get("disabled",!0)?!1:a.get("zoomLock",!0)?"move":!0;n[i+o]>n[i+e]&&(e=o),r&=a.get("preventDefaultMouseMove",!0)}),{controlType:e,opt:{zoomOnMouseWheel:!0,moveOnMouseMove:!0,moveOnMouseWheel:!0,preventDefaultMouseMove:!!r}}}function xg(t){return function(e,i,n,r){var a=this._range,o=a.slice(),s=e.axisModels[0];if(s){var l=t(o,s,e,i,n,r);return GA(l,o,[0,100],"all"),this._range=o,a[0]!==o[0]||a[1]!==o[1]?o:void 0}}}var _g;"undefined"!=typeof window?_g=window.__DEV__:"undefined"!=typeof global&&(_g=global.__DEV__),"undefined"==typeof _g&&(_g=!0);var wg=_g,bg=2311,Mg=function(){return bg++},Sg={};Sg="object"==typeof wx&&"function"==typeof wx.getSystemInfoSync?{browser:{},os:{},node:!1,wxa:!0,canvasSupported:!0,svgSupported:!1,touchEventsSupported:!0,domSupported:!1}:"undefined"==typeof document&&"undefined"!=typeof self?{browser:{},os:{},node:!1,worker:!0,canvasSupported:!0,domSupported:!1}:"undefined"==typeof navigator?{browser:{},os:{},node:!0,worker:!1,canvasSupported:!0,svgSupported:!0,domSupported:!1}:e(navigator.userAgent);var Ag=Sg,Ig={"[object Function]":1,"[object RegExp]":1,"[object Date]":1,"[object Error]":1,"[object CanvasGradient]":1,"[object CanvasPattern]":1,"[object Image]":1,"[object Canvas]":1},Tg={"[object Int8Array]":1,"[object Uint8Array]":1,"[object Uint8ClampedArray]":1,"[object Int16Array]":1,"[object Uint16Array]":1,"[object Int32Array]":1,"[object Uint32Array]":1,"[object Float32Array]":1,"[object Float64Array]":1},Cg=Object.prototype.toString,Dg=Array.prototype,kg=Dg.forEach,Pg=Dg.filter,Lg=Dg.slice,Og=Dg.map,Eg=Dg.reduce,zg={},Bg=function(){return zg.createCanvas()};zg.createCanvas=function(){return document.createElement("canvas")};var Rg,Ng="__ec_primitive__";R.prototype={constructor:R,get:function(t){return this.data.hasOwnProperty(t)?this.data[t]:null},set:function(t,e){return this.data[t]=e},each:function(t,e){void 0!==e&&(t=y(t,e));for(var i in this.data)this.data.hasOwnProperty(i)&&t(this.data[i],i)},removeKey:function(t){delete this.data[t]}};var Fg=(Object.freeze||Object)({$override:i,clone:n,merge:r,mergeAll:a,extend:o,defaults:s,createCanvas:Bg,getContext:l,indexOf:h,inherits:u,mixin:c,isArrayLike:d,each:f,map:p,reduce:g,filter:v,find:m,bind:y,curry:x,isArray:_,isFunction:w,isString:b,isObject:M,isBuiltInObject:S,isTypedArray:A,isDom:I,eqNaN:T,retrieve:C,retrieve2:D,retrieve3:k,slice:P,normalizeCssArray:L,assert:O,trim:E,setAsPrimitive:z,isPrimitive:B,createHashMap:N,concatArray:F,noop:V}),Vg="undefined"==typeof Float32Array?Array:Float32Array,Wg=q,Hg=j,Gg=ee,Zg=ie,Xg=(Object.freeze||Object)({create:W,copy:H,clone:G,set:Z,add:X,scaleAndAdd:Y,sub:U,len:q,length:Wg,lenSquare:j,lengthSquare:Hg,mul:K,div:$,dot:Q,scale:J,normalize:te,distance:ee,dist:Gg,distanceSquare:ie,distSquare:Zg,negate:ne,lerp:re,applyTransform:ae,min:oe,max:se});le.prototype={constructor:le,_dragStart:function(t){for(var e=t.target;e&&!e.draggable;)e=e.parent;e&&(this._draggingTarget=e,e.dragging=!0,this._x=t.offsetX,this._y=t.offsetY,this.dispatchToElement(he(e,t),"dragstart",t.event))},_drag:function(t){var e=this._draggingTarget;if(e){var i=t.offsetX,n=t.offsetY,r=i-this._x,a=n-this._y;this._x=i,this._y=n,e.drift(r,a,t),this.dispatchToElement(he(e,t),"drag",t.event);var o=this.findHover(i,n,e).target,s=this._dropTarget;this._dropTarget=o,e!==o&&(s&&o!==s&&this.dispatchToElement(he(s,t),"dragleave",t.event),o&&o!==s&&this.dispatchToElement(he(o,t),"dragenter",t.event))}},_dragEnd:function(t){var e=this._draggingTarget;e&&(e.dragging=!1),this.dispatchToElement(he(e,t),"dragend",t.event),this._dropTarget&&this.dispatchToElement(he(this._dropTarget,t),"drop",t.event),this._draggingTarget=null,this._dropTarget=null}};var Yg=Array.prototype.slice,Ug=function(t){this._$handlers={},this._$eventProcessor=t};Ug.prototype={constructor:Ug,one:function(t,e,i,n){return ce(this,t,e,i,n,!0)},on:function(t,e,i,n){return ce(this,t,e,i,n,!1)},isSilent:function(t){var e=this._$handlers;return!e[t]||!e[t].length},off:function(t,e){var i=this._$handlers;if(!t)return this._$handlers={},this;if(e){if(i[t]){for(var n=[],r=0,a=i[t].length;a>r;r++)i[t][r].h!==e&&n.push(i[t][r]);i[t]=n}i[t]&&0===i[t].length&&delete i[t]}else delete i[t];return this},trigger:function(t){var e=this._$handlers[t],i=this._$eventProcessor;if(e){var n=arguments,r=n.length;r>3&&(n=Yg.call(n,1));for(var a=e.length,o=0;a>o;){var s=e[o];if(i&&i.filter&&null!=s.query&&!i.filter(t,s.query))o++;else{switch(r){case 1:s.h.call(s.ctx);break;case 2:s.h.call(s.ctx,n[1]);break;case 3:s.h.call(s.ctx,n[1],n[2]);break;default:s.h.apply(s.ctx,n)}s.one?(e.splice(o,1),a--):o++}}}return i&&i.afterTrigger&&i.afterTrigger(t),this},triggerWithContext:function(t){var e=this._$handlers[t],i=this._$eventProcessor;if(e){var n=arguments,r=n.length;r>4&&(n=Yg.call(n,1,n.length-1));for(var a=n[n.length-1],o=e.length,s=0;o>s;){var l=e[s];if(i&&i.filter&&null!=l.query&&!i.filter(t,l.query))s++;else{switch(r){case 1:l.h.call(a);break;case 2:l.h.call(a,n[1]);break;case 3:l.h.call(a,n[1],n[2]);break;default:l.h.apply(a,n)}l.one?(e.splice(s,1),o--):s++}}}return i&&i.afterTrigger&&i.afterTrigger(t),this}};var qg=Math.log(2),jg="___zrEVENTSAVED",Kg=[],$g="undefined"!=typeof window&&!!window.addEventListener,Qg=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,Jg=[],tv=$g?function(t){t.preventDefault(),t.stopPropagation(),t.cancelBubble=!0}:function(t){t.returnValue=!1,t.cancelBubble=!0},ev=function(){this._track=[]};ev.prototype={constructor:ev,recognize:function(t,e,i){return this._doTrack(t,e,i),this._recognize(t)},clear:function(){return this._track.length=0,this},_doTrack:function(t,e,i){var n=t.touches;if(n){for(var r={points:[],touches:[],target:e,event:t},a=0,o=n.length;o>a;a++){var s=n[a],l=xe(i,s,{});r.points.push([l.zrX,l.zrY]),r.touches.push(s)}this._track.push(r)}},_recognize:function(t){for(var e in iv)if(iv.hasOwnProperty(e)){var i=iv[e](this._track,t);if(i)return i}}};var iv={pinch:function(t,e){var i=t.length;if(i){var n=(t[i-1]||{}).points,r=(t[i-2]||{}).points||n;if(r&&r.length>1&&n&&n.length>1){var a=Ie(n)/Ie(r);!isFinite(a)&&(a=1),e.pinchScale=a;var o=Te(n);return e.pinchX=o[0],e.pinchY=o[1],{type:"pinch",target:t[0].target,event:e}}}}},nv="silent";ke.prototype.dispose=function(){};var rv=["click","dblclick","mousewheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],av=function(t,e,i,n){Ug.call(this),this.storage=t,this.painter=e,this.painterRoot=n,i=i||new ke,this.proxy=null,this._hovered={},this._lastTouchMoment,this._lastX,this._lastY,this._gestureMgr,le.call(this),this.setHandlerProxy(i)};av.prototype={constructor:av,setHandlerProxy:function(t){this.proxy&&this.proxy.dispose(),t&&(f(rv,function(e){t.on&&t.on(e,this[e],this)},this),t.handler=this),this.proxy=t},mousemove:function(t){var e=t.zrX,i=t.zrY,n=Le(this,e,i),r=this._hovered,a=r.target;a&&!a.__zr&&(r=this.findHover(r.x,r.y),a=r.target);var o=this._hovered=n?{x:e,y:i}:this.findHover(e,i),s=o.target,l=this.proxy;l.setCursor&&l.setCursor(s?s.cursor:"default"),a&&s!==a&&this.dispatchToElement(r,"mouseout",t),this.dispatchToElement(o,"mousemove",t),s&&s!==a&&this.dispatchToElement(o,"mouseover",t)},mouseout:function(t){var e=t.zrEventControl,i=t.zrIsToLocalDOM;"only_globalout"!==e&&this.dispatchToElement(this._hovered,"mouseout",t),"no_globalout"!==e&&!i&&this.trigger("globalout",{type:"globalout",event:t})},resize:function(){this._hovered={}},dispatch:function(t,e){var i=this[t];i&&i.call(this,e)},dispose:function(){this.proxy.dispose(),this.storage=this.proxy=this.painter=null},setCursorStyle:function(t){var e=this.proxy;e.setCursor&&e.setCursor(t)},dispatchToElement:function(t,e,i){t=t||{};var n=t.target;if(!n||!n.silent){for(var r="on"+e,a=Ce(e,t,i);n&&(n[r]&&(a.cancelBubble=n[r].call(n,a)),n.trigger(e,a),n=n.parent,!a.cancelBubble););a.cancelBubble||(this.trigger(e,a),this.painter&&this.painter.eachOtherLayer(function(t){"function"==typeof t[r]&&t[r].call(t,a),t.trigger&&t.trigger(e,a)}))}},findHover:function(t,e,i){for(var n=this.storage.getDisplayList(),r={x:t,y:e},a=n.length-1;a>=0;a--){var o;if(n[a]!==i&&!n[a].ignore&&(o=Pe(n[a],t,e))&&(!r.topTarget&&(r.topTarget=n[a]),o!==nv)){r.target=n[a];break}}return r},processGesture:function(t,e){this._gestureMgr||(this._gestureMgr=new ev);var i=this._gestureMgr;"start"===e&&i.clear();var n=i.recognize(t,this.findHover(t.zrX,t.zrY,null).target,this.proxy.dom);if("end"===e&&i.clear(),n){var r=n.type;t.gestureEvent=r,this.dispatchToElement({target:n.target},r,n.event)}}},f(["click","mousedown","mouseup","mousewheel","dblclick","contextmenu"],function(t){av.prototype[t]=function(e){var i,n,r=e.zrX,a=e.zrY,o=Le(this,r,a);if("mouseup"===t&&o||(i=this.findHover(r,a),n=i.target),"mousedown"===t)this._downEl=n,this._downPoint=[e.zrX,e.zrY],this._upEl=n;else if("mouseup"===t)this._upEl=n;else if("click"===t){if(this._downEl!==this._upEl||!this._downPoint||Gg(this._downPoint,[e.zrX,e.zrY])>4)return;this._downPoint=null}this.dispatchToElement(i,t,e)}}),c(av,Ug),c(av,le);var ov="undefined"==typeof Float32Array?Array:Float32Array,sv=(Object.freeze||Object)({create:Oe,identity:Ee,copy:ze,mul:Be,translate:Re,rotate:Ne,scale:Fe,invert:Ve,clone:We}),lv=Ee,hv=5e-5,uv=function(t){t=t||{},t.position||(this.position=[0,0]),null==t.rotation&&(this.rotation=0),t.scale||(this.scale=[1,1]),this.origin=this.origin||null},cv=uv.prototype;cv.transform=null,cv.needLocalTransform=function(){return He(this.rotation)||He(this.position[0])||He(this.position[1])||He(this.scale[0]-1)||He(this.scale[1]-1)};var dv=[];cv.updateTransform=function(){var t=this.parent,e=t&&t.transform,i=this.needLocalTransform(),n=this.transform;if(!i&&!e)return void(n&&lv(n));n=n||Oe(),i?this.getLocalTransform(n):lv(n),e&&(i?Be(n,t.transform,n):ze(n,t.transform)),this.transform=n;var r=this.globalScaleRatio;if(null!=r&&1!==r){this.getGlobalScale(dv);var a=dv[0]<0?-1:1,o=dv[1]<0?-1:1,s=((dv[0]-a)*r+a)/dv[0]||0,l=((dv[1]-o)*r+o)/dv[1]||0;n[0]*=s,n[1]*=s,n[2]*=l,n[3]*=l}this.invTransform=this.invTransform||Oe(),Ve(this.invTransform,n)},cv.getLocalTransform=function(t){return uv.getLocalTransform(this,t)},cv.setTransform=function(t){var e=this.transform,i=t.dpr||1;e?t.setTransform(i*e[0],i*e[1],i*e[2],i*e[3],i*e[4],i*e[5]):t.setTransform(i,0,0,i,0,0)},cv.restoreTransform=function(t){var e=t.dpr||1;t.setTransform(e,0,0,e,0,0)};var fv=[],pv=Oe();cv.setLocalTransform=function(t){if(t){var e=t[0]*t[0]+t[1]*t[1],i=t[2]*t[2]+t[3]*t[3],n=this.position,r=this.scale;He(e-1)&&(e=Math.sqrt(e)),He(i-1)&&(i=Math.sqrt(i)),t[0]<0&&(e=-e),t[3]<0&&(i=-i),n[0]=t[4],n[1]=t[5],r[0]=e,r[1]=i,this.rotation=Math.atan2(-t[1]/i,t[0]/e)}},cv.decomposeTransform=function(){if(this.transform){var t=this.parent,e=this.transform;t&&t.transform&&(Be(fv,t.invTransform,e),e=fv);var i=this.origin;i&&(i[0]||i[1])&&(pv[4]=i[0],pv[5]=i[1],Be(fv,e,pv),fv[4]-=i[0],fv[5]-=i[1],e=fv),this.setLocalTransform(e)}},cv.getGlobalScale=function(t){var e=this.transform;return t=t||[],e?(t[0]=Math.sqrt(e[0]*e[0]+e[1]*e[1]),t[1]=Math.sqrt(e[2]*e[2]+e[3]*e[3]),e[0]<0&&(t[0]=-t[0]),e[3]<0&&(t[1]=-t[1]),t):(t[0]=1,t[1]=1,t)},cv.transformCoordToLocal=function(t,e){var i=[t,e],n=this.invTransform;return n&&ae(i,i,n),i},cv.transformCoordToGlobal=function(t,e){var i=[t,e],n=this.transform;return n&&ae(i,i,n),i},uv.getLocalTransform=function(t,e){e=e||[],lv(e);var i=t.origin,n=t.scale||[1,1],r=t.rotation||0,a=t.position||[0,0];return i&&(e[4]-=i[0],e[5]-=i[1]),Fe(e,e,n),r&&Ne(e,e,r),i&&(e[4]+=i[0],e[5]+=i[1]),e[4]+=a[0],e[5]+=a[1],e};var gv={linear:function(t){return t},quadraticIn:function(t){return t*t},quadraticOut:function(t){return t*(2-t)},quadraticInOut:function(t){return(t*=2)<1?.5*t*t:-.5*(--t*(t-2)-1)},cubicIn:function(t){return t*t*t},cubicOut:function(t){return--t*t*t+1},cubicInOut:function(t){return(t*=2)<1?.5*t*t*t:.5*((t-=2)*t*t+2)},quarticIn:function(t){return t*t*t*t},quarticOut:function(t){return 1- --t*t*t*t},quarticInOut:function(t){return(t*=2)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2)},quinticIn:function(t){return t*t*t*t*t},quinticOut:function(t){return--t*t*t*t*t+1},quinticInOut:function(t){return(t*=2)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2)},sinusoidalIn:function(t){return 1-Math.cos(t*Math.PI/2)},sinusoidalOut:function(t){return Math.sin(t*Math.PI/2)},sinusoidalInOut:function(t){return.5*(1-Math.cos(Math.PI*t))},exponentialIn:function(t){return 0===t?0:Math.pow(1024,t-1)},exponentialOut:function(t){return 1===t?1:1-Math.pow(2,-10*t)},exponentialInOut:function(t){return 0===t?0:1===t?1:(t*=2)<1?.5*Math.pow(1024,t-1):.5*(-Math.pow(2,-10*(t-1))+2)},circularIn:function(t){return 1-Math.sqrt(1-t*t)},circularOut:function(t){return Math.sqrt(1- --t*t)},circularInOut:function(t){return(t*=2)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1)},elasticIn:function(t){var e,i=.1,n=.4;return 0===t?0:1===t?1:(!i||1>i?(i=1,e=n/4):e=n*Math.asin(1/i)/(2*Math.PI),-(i*Math.pow(2,10*(t-=1))*Math.sin(2*(t-e)*Math.PI/n)))},elasticOut:function(t){var e,i=.1,n=.4;return 0===t?0:1===t?1:(!i||1>i?(i=1,e=n/4):e=n*Math.asin(1/i)/(2*Math.PI),i*Math.pow(2,-10*t)*Math.sin(2*(t-e)*Math.PI/n)+1)},elasticInOut:function(t){var e,i=.1,n=.4;return 0===t?0:1===t?1:(!i||1>i?(i=1,e=n/4):e=n*Math.asin(1/i)/(2*Math.PI),(t*=2)<1?-.5*i*Math.pow(2,10*(t-=1))*Math.sin(2*(t-e)*Math.PI/n):i*Math.pow(2,-10*(t-=1))*Math.sin(2*(t-e)*Math.PI/n)*.5+1)},backIn:function(t){var e=1.70158;return t*t*((e+1)*t-e)},backOut:function(t){var e=1.70158;return--t*t*((e+1)*t+e)+1},backInOut:function(t){var e=2.5949095;return(t*=2)<1?.5*t*t*((e+1)*t-e):.5*((t-=2)*t*((e+1)*t+e)+2)},bounceIn:function(t){return 1-gv.bounceOut(1-t)},bounceOut:function(t){return 1/2.75>t?7.5625*t*t:2/2.75>t?7.5625*(t-=1.5/2.75)*t+.75:2.5/2.75>t?7.5625*(t-=2.25/2.75)*t+.9375:7.5625*(t-=2.625/2.75)*t+.984375},bounceInOut:function(t){return.5>t?.5*gv.bounceIn(2*t):.5*gv.bounceOut(2*t-1)+.5}};Ge.prototype={constructor:Ge,step:function(t,e){if(this._initialized||(this._startTime=t+this._delay,this._initialized=!0),this._paused)return void(this._pausedTime+=e);var i=(t-this._startTime-this._pausedTime)/this._life;if(!(0>i)){i=Math.min(i,1);var n=this.easing,r="string"==typeof n?gv[n]:n,a="function"==typeof r?r(i):i;return this.fire("frame",a),1===i?this.loop?(this.restart(t),"restart"):(this._needsRemove=!0,"destroy"):null}},restart:function(t){var e=(t-this._startTime-this._pausedTime)%this._life;this._startTime=t-e+this.gap,this._pausedTime=0,this._needsRemove=!1},fire:function(t,e){t="on"+t,this[t]&&this[t](this._target,e)},pause:function(){this._paused=!0},resume:function(){this._paused=!1}};var vv=function(){this.head=null,this.tail=null,this._len=0},mv=vv.prototype;mv.insert=function(t){var e=new yv(t);return this.insertEntry(e),e},mv.insertEntry=function(t){this.head?(this.tail.next=t,t.prev=this.tail,t.next=null,this.tail=t):this.head=this.tail=t,this._len++},mv.remove=function(t){var e=t.prev,i=t.next;e?e.next=i:this.head=i,i?i.prev=e:this.tail=e,t.next=t.prev=null,this._len--},mv.len=function(){return this._len},mv.clear=function(){this.head=this.tail=null,this._len=0};var yv=function(t){this.value=t,this.next,this.prev},xv=function(t){this._list=new vv,this._map={},this._maxSize=t||10,this._lastRemovedEntry=null},_v=xv.prototype;_v.put=function(t,e){var i=this._list,n=this._map,r=null;if(null==n[t]){var a=i.len(),o=this._lastRemovedEntry;if(a>=this._maxSize&&a>0){var s=i.head;i.remove(s),delete n[s.key],r=s.value,this._lastRemovedEntry=s}o?o.value=e:o=new yv(e),o.key=t,i.insertEntry(o),n[t]=o}return r},_v.get=function(t){var e=this._map[t],i=this._list;return null!=e?(e!==i.tail&&(i.remove(e),i.insertEntry(e)),e.value):void 0},_v.clear=function(){this._list.clear(),this._map={}};var wv={transparent:[0,0,0,0],aliceblue:[240,248,255,1],antiquewhite:[250,235,215,1],aqua:[0,255,255,1],aquamarine:[127,255,212,1],azure:[240,255,255,1],beige:[245,245,220,1],bisque:[255,228,196,1],black:[0,0,0,1],blanchedalmond:[255,235,205,1],blue:[0,0,255,1],blueviolet:[138,43,226,1],brown:[165,42,42,1],burlywood:[222,184,135,1],cadetblue:[95,158,160,1],chartreuse:[127,255,0,1],chocolate:[210,105,30,1],coral:[255,127,80,1],cornflowerblue:[100,149,237,1],cornsilk:[255,248,220,1],crimson:[220,20,60,1],cyan:[0,255,255,1],darkblue:[0,0,139,1],darkcyan:[0,139,139,1],darkgoldenrod:[184,134,11,1],darkgray:[169,169,169,1],darkgreen:[0,100,0,1],darkgrey:[169,169,169,1],darkkhaki:[189,183,107,1],darkmagenta:[139,0,139,1],darkolivegreen:[85,107,47,1],darkorange:[255,140,0,1],darkorchid:[153,50,204,1],darkred:[139,0,0,1],darksalmon:[233,150,122,1],darkseagreen:[143,188,143,1],darkslateblue:[72,61,139,1],darkslategray:[47,79,79,1],darkslategrey:[47,79,79,1],darkturquoise:[0,206,209,1],darkviolet:[148,0,211,1],deeppink:[255,20,147,1],deepskyblue:[0,191,255,1],dimgray:[105,105,105,1],dimgrey:[105,105,105,1],dodgerblue:[30,144,255,1],firebrick:[178,34,34,1],floralwhite:[255,250,240,1],forestgreen:[34,139,34,1],fuchsia:[255,0,255,1],gainsboro:[220,220,220,1],ghostwhite:[248,248,255,1],gold:[255,215,0,1],goldenrod:[218,165,32,1],gray:[128,128,128,1],green:[0,128,0,1],greenyellow:[173,255,47,1],grey:[128,128,128,1],honeydew:[240,255,240,1],hotpink:[255,105,180,1],indianred:[205,92,92,1],indigo:[75,0,130,1],ivory:[255,255,240,1],khaki:[240,230,140,1],lavender:[230,230,250,1],lavenderblush:[255,240,245,1],lawngreen:[124,252,0,1],lemonchiffon:[255,250,205,1],lightblue:[173,216,230,1],lightcoral:[240,128,128,1],lightcyan:[224,255,255,1],lightgoldenrodyellow:[250,250,210,1],lightgray:[211,211,211,1],lightgreen:[144,238,144,1],lightgrey:[211,211,211,1],lightpink:[255,182,193,1],lightsalmon:[255,160,122,1],lightseagreen:[32,178,170,1],lightskyblue:[135,206,250,1],lightslategray:[119,136,153,1],lightslategrey:[119,136,153,1],lightsteelblue:[176,196,222,1],lightyellow:[255,255,224,1],lime:[0,255,0,1],limegreen:[50,205,50,1],linen:[250,240,230,1],magenta:[255,0,255,1],maroon:[128,0,0,1],mediumaquamarine:[102,205,170,1],mediumblue:[0,0,205,1],mediumorchid:[186,85,211,1],mediumpurple:[147,112,219,1],mediumseagreen:[60,179,113,1],mediumslateblue:[123,104,238,1],mediumspringgreen:[0,250,154,1],mediumturquoise:[72,209,204,1],mediumvioletred:[199,21,133,1],midnightblue:[25,25,112,1],mintcream:[245,255,250,1],mistyrose:[255,228,225,1],moccasin:[255,228,181,1],navajowhite:[255,222,173,1],navy:[0,0,128,1],oldlace:[253,245,230,1],olive:[128,128,0,1],olivedrab:[107,142,35,1],orange:[255,165,0,1],orangered:[255,69,0,1],orchid:[218,112,214,1],palegoldenrod:[238,232,170,1],palegreen:[152,251,152,1],paleturquoise:[175,238,238,1],palevioletred:[219,112,147,1],papayawhip:[255,239,213,1],peachpuff:[255,218,185,1],peru:[205,133,63,1],pink:[255,192,203,1],plum:[221,160,221,1],powderblue:[176,224,230,1],purple:[128,0,128,1],red:[255,0,0,1],rosybrown:[188,143,143,1],royalblue:[65,105,225,1],saddlebrown:[139,69,19,1],salmon:[250,128,114,1],sandybrown:[244,164,96,1],seagreen:[46,139,87,1],seashell:[255,245,238,1],sienna:[160,82,45,1],silver:[192,192,192,1],skyblue:[135,206,235,1],slateblue:[106,90,205,1],slategray:[112,128,144,1],slategrey:[112,128,144,1],snow:[255,250,250,1],springgreen:[0,255,127,1],steelblue:[70,130,180,1],tan:[210,180,140,1],teal:[0,128,128,1],thistle:[216,191,216,1],tomato:[255,99,71,1],turquoise:[64,224,208,1],violet:[238,130,238,1],wheat:[245,222,179,1],white:[255,255,255,1],whitesmoke:[245,245,245,1],yellow:[255,255,0,1],yellowgreen:[154,205,50,1]},bv=new xv(20),Mv=null,Sv=ai,Av=oi,Iv=(Object.freeze||Object)({parse:ti,lift:ni,toHex:ri,fastLerp:ai,fastMapToColor:Sv,lerp:oi,mapToColor:Av,modifyHSL:si,modifyAlpha:li,stringify:hi}),Tv=Array.prototype.slice,Cv=function(t,e,i,n){this._tracks={},this._target=t,this._loop=e||!1,this._getter=i||ui,this._setter=n||ci,this._clipCount=0,this._delay=0,this._doneList=[],this._onframeList=[],this._clipList=[]};Cv.prototype={when:function(t,e){var i=this._tracks;for(var n in e)if(e.hasOwnProperty(n)){if(!i[n]){i[n]=[];var r=this._getter(this._target,n);if(null==r)continue;0!==t&&i[n].push({time:0,value:xi(r)})}i[n].push({time:t,value:e[n]})}return this},during:function(t){return this._onframeList.push(t),this},pause:function(){for(var t=0;t<this._clipList.length;t++)this._clipList[t].pause();this._paused=!0},resume:function(){for(var t=0;t<this._clipList.length;t++)this._clipList[t].resume();this._paused=!1},isPaused:function(){return!!this._paused},_doneCallback:function(){this._tracks={},this._clipList.length=0;for(var t=this._doneList,e=t.length,i=0;e>i;i++)t[i].call(this)},start:function(t,e){var i,n=this,r=0,a=function(){r--,r||n._doneCallback()};for(var o in this._tracks)if(this._tracks.hasOwnProperty(o)){var s=bi(this,t,a,this._tracks[o],o,e);s&&(this._clipList.push(s),r++,this.animation&&this.animation.addClip(s),i=s)}if(i){var l=i.onframe;i.onframe=function(t,e){l(t,e);for(var i=0;i<n._onframeList.length;i++)n._onframeList[i](t,e)}}return r||this._doneCallback(),this},stop:function(t){for(var e=this._clipList,i=this.animation,n=0;n<e.length;n++){var r=e[n];t&&r.onframe(this._target,1),i&&i.removeClip(r)}e.length=0},delay:function(t){return this._delay=t,this},done:function(t){return t&&this._doneList.push(t),this},getClips:function(){return this._clipList}};var Dv=1;"undefined"!=typeof window&&(Dv=Math.max(window.devicePixelRatio||1,1));var kv=0,Pv=Dv,Lv=function(){};1===kv&&(Lv=console.error);var Ov=Lv,Ev=function(){this.animators=[]};Ev.prototype={constructor:Ev,animate:function(t,e){var i,n=!1,r=this,a=this.__zr;if(t){var o=t.split("."),s=r;n="shape"===o[0];for(var l=0,u=o.length;u>l;l++)s&&(s=s[o[l]]);s&&(i=s)}else i=r;if(!i)return void Ov('Property "'+t+'" is not existed in element '+r.id);var c=r.animators,d=new Cv(i,e);return d.during(function(){r.dirty(n)}).done(function(){c.splice(h(c,d),1)}),c.push(d),a&&a.animation.addAnimator(d),d},stopAnimation:function(t){for(var e=this.animators,i=e.length,n=0;i>n;n++)e[n].stop(t);return e.length=0,this},animateTo:function(t,e,i,n,r,a){Mi(this,t,e,i,n,r,a)},animateFrom:function(t,e,i,n,r,a){Mi(this,t,e,i,n,r,a,!0)}};var zv=function(t){uv.call(this,t),Ug.call(this,t),Ev.call(this,t),this.id=t.id||Mg()};zv.prototype={type:"element",name:"",__zr:null,ignore:!1,clipPath:null,isGroup:!1,drift:function(t,e){switch(this.draggable){case"horizontal":e=0;break;case"vertical":t=0}var i=this.transform;i||(i=this.transform=[1,0,0,1,0,0]),i[4]+=t,i[5]+=e,this.decomposeTransform(),this.dirty(!1)},beforeUpdate:function(){},afterUpdate:function(){},update:function(){this.updateTransform()},traverse:function(){},attrKV:function(t,e){if("position"===t||"scale"===t||"origin"===t){if(e){var i=this[t];i||(i=this[t]=[]),i[0]=e[0],i[1]=e[1]}}else this[t]=e},hide:function(){this.ignore=!0,this.__zr&&this.__zr.refresh()},show:function(){this.ignore=!1,this.__zr&&this.__zr.refresh()},attr:function(t,e){if("string"==typeof t)this.attrKV(t,e);else if(M(t))for(var i in t)t.hasOwnProperty(i)&&this.attrKV(i,t[i]);return this.dirty(!1),this},setClipPath:function(t){var e=this.__zr;e&&t.addSelfToZr(e),this.clipPath&&this.clipPath!==t&&this.removeClipPath(),this.clipPath=t,t.__zr=e,t.__clipTarget=this,this.dirty(!1)},removeClipPath:function(){var t=this.clipPath;t&&(t.__zr&&t.removeSelfFromZr(t.__zr),t.__zr=null,t.__clipTarget=null,this.clipPath=null,this.dirty(!1))},addSelfToZr:function(t){this.__zr=t;var e=this.animators;if(e)for(var i=0;i<e.length;i++)t.animation.addAnimator(e[i]);
this.clipPath&&this.clipPath.addSelfToZr(t)},removeSelfFromZr:function(t){this.__zr=null;var e=this.animators;if(e)for(var i=0;i<e.length;i++)t.animation.removeAnimator(e[i]);this.clipPath&&this.clipPath.removeSelfFromZr(t)}},c(zv,Ev),c(zv,uv),c(zv,Ug);var Bv=ae,Rv=Math.min,Nv=Math.max;Ii.prototype={constructor:Ii,union:function(t){var e=Rv(t.x,this.x),i=Rv(t.y,this.y);this.width=Nv(t.x+t.width,this.x+this.width)-e,this.height=Nv(t.y+t.height,this.y+this.height)-i,this.x=e,this.y=i},applyTransform:function(){var t=[],e=[],i=[],n=[];return function(r){if(r){t[0]=i[0]=this.x,t[1]=n[1]=this.y,e[0]=n[0]=this.x+this.width,e[1]=i[1]=this.y+this.height,Bv(t,t,r),Bv(e,e,r),Bv(i,i,r),Bv(n,n,r),this.x=Rv(t[0],e[0],i[0],n[0]),this.y=Rv(t[1],e[1],i[1],n[1]);var a=Nv(t[0],e[0],i[0],n[0]),o=Nv(t[1],e[1],i[1],n[1]);this.width=a-this.x,this.height=o-this.y}}}(),calculateTransform:function(t){var e=this,i=t.width/e.width,n=t.height/e.height,r=Oe();return Re(r,r,[-e.x,-e.y]),Fe(r,r,[i,n]),Re(r,r,[t.x,t.y]),r},intersect:function(t){if(!t)return!1;t instanceof Ii||(t=Ii.create(t));var e=this,i=e.x,n=e.x+e.width,r=e.y,a=e.y+e.height,o=t.x,s=t.x+t.width,l=t.y,h=t.y+t.height;return!(o>n||i>s||l>a||r>h)},contain:function(t,e){var i=this;return t>=i.x&&t<=i.x+i.width&&e>=i.y&&e<=i.y+i.height},clone:function(){return new Ii(this.x,this.y,this.width,this.height)},copy:function(t){this.x=t.x,this.y=t.y,this.width=t.width,this.height=t.height},plain:function(){return{x:this.x,y:this.y,width:this.width,height:this.height}}},Ii.create=function(t){return new Ii(t.x,t.y,t.width,t.height)};var Fv=function(t){t=t||{},zv.call(this,t);for(var e in t)t.hasOwnProperty(e)&&(this[e]=t[e]);this._children=[],this.__storage=null,this.__dirty=!0};Fv.prototype={constructor:Fv,isGroup:!0,type:"group",silent:!1,children:function(){return this._children.slice()},childAt:function(t){return this._children[t]},childOfName:function(t){for(var e=this._children,i=0;i<e.length;i++)if(e[i].name===t)return e[i]},childCount:function(){return this._children.length},add:function(t){return t&&t!==this&&t.parent!==this&&(this._children.push(t),this._doAdd(t)),this},addBefore:function(t,e){if(t&&t!==this&&t.parent!==this&&e&&e.parent===this){var i=this._children,n=i.indexOf(e);n>=0&&(i.splice(n,0,t),this._doAdd(t))}return this},_doAdd:function(t){t.parent&&t.parent.remove(t),t.parent=this;var e=this.__storage,i=this.__zr;e&&e!==t.__storage&&(e.addToStorage(t),t instanceof Fv&&t.addChildrenToStorage(e)),i&&i.refresh()},remove:function(t){var e=this.__zr,i=this.__storage,n=this._children,r=h(n,t);return 0>r?this:(n.splice(r,1),t.parent=null,i&&(i.delFromStorage(t),t instanceof Fv&&t.delChildrenFromStorage(i)),e&&e.refresh(),this)},removeAll:function(){var t,e,i=this._children,n=this.__storage;for(e=0;e<i.length;e++)t=i[e],n&&(n.delFromStorage(t),t instanceof Fv&&t.delChildrenFromStorage(n)),t.parent=null;return i.length=0,this},eachChild:function(t,e){for(var i=this._children,n=0;n<i.length;n++){var r=i[n];t.call(e,r,n)}return this},traverse:function(t,e){for(var i=0;i<this._children.length;i++){var n=this._children[i];t.call(e,n),"group"===n.type&&n.traverse(t,e)}return this},addChildrenToStorage:function(t){for(var e=0;e<this._children.length;e++){var i=this._children[e];t.addToStorage(i),i instanceof Fv&&i.addChildrenToStorage(t)}},delChildrenFromStorage:function(t){for(var e=0;e<this._children.length;e++){var i=this._children[e];t.delFromStorage(i),i instanceof Fv&&i.delChildrenFromStorage(t)}},dirty:function(){return this.__dirty=!0,this.__zr&&this.__zr.refresh(),this},getBoundingRect:function(t){for(var e=null,i=new Ii(0,0,0,0),n=t||this._children,r=[],a=0;a<n.length;a++){var o=n[a];if(!o.ignore&&!o.invisible){var s=o.getBoundingRect(),l=o.getLocalTransform(r);l?(i.copy(s),i.applyTransform(l),e=e||i.clone(),e.union(i)):(e=e||s.clone(),e.union(s))}}return e||i}},u(Fv,zv);var Vv=32,Wv=7,Hv=function(){this._roots=[],this._displayList=[],this._displayListLen=0};Hv.prototype={constructor:Hv,traverse:function(t,e){for(var i=0;i<this._roots.length;i++)this._roots[i].traverse(t,e)},getDisplayList:function(t,e){return e=e||!1,t&&this.updateDisplayList(e),this._displayList},updateDisplayList:function(t){this._displayListLen=0;for(var e=this._roots,i=this._displayList,n=0,r=e.length;r>n;n++)this._updateAndAddDisplayable(e[n],null,t);i.length=this._displayListLen,Ag.canvasSupported&&Ei(i,zi)},_updateAndAddDisplayable:function(t,e,i){if(!t.ignore||i){t.beforeUpdate(),t.__dirty&&t.update(),t.afterUpdate();var n=t.clipPath;if(n){e=e?e.slice():[];for(var r=n,a=t;r;)r.parent=a,r.updateTransform(),e.push(r),a=r,r=r.clipPath}if(t.isGroup){for(var o=t._children,s=0;s<o.length;s++){var l=o[s];t.__dirty&&(l.__dirty=!0),this._updateAndAddDisplayable(l,e,i)}t.__dirty=!1}else t.__clipPaths=e,this._displayList[this._displayListLen++]=t}},addRoot:function(t){t.__storage!==this&&(t instanceof Fv&&t.addChildrenToStorage(this),this.addToStorage(t),this._roots.push(t))},delRoot:function(t){if(null==t){for(var e=0;e<this._roots.length;e++){var i=this._roots[e];i instanceof Fv&&i.delChildrenFromStorage(this)}return this._roots=[],this._displayList=[],void(this._displayListLen=0)}if(t instanceof Array)for(var e=0,n=t.length;n>e;e++)this.delRoot(t[e]);else{var r=h(this._roots,t);r>=0&&(this.delFromStorage(t),this._roots.splice(r,1),t instanceof Fv&&t.delChildrenFromStorage(this))}},addToStorage:function(t){return t&&(t.__storage=this,t.dirty(!1)),this},delFromStorage:function(t){return t&&(t.__storage=null),this},dispose:function(){this._renderList=this._roots=null},displayableSortFunc:zi};var Gv={shadowBlur:1,shadowOffsetX:1,shadowOffsetY:1,textShadowBlur:1,textShadowOffsetX:1,textShadowOffsetY:1,textBoxShadowBlur:1,textBoxShadowOffsetX:1,textBoxShadowOffsetY:1},Zv=function(t,e,i){return Gv.hasOwnProperty(e)?i*=t.dpr:i},Xv={NONE:0,STYLE_BIND:1,PLAIN_TEXT:2},Yv=9,Uv=[["shadowBlur",0],["shadowOffsetX",0],["shadowOffsetY",0],["shadowColor","#000"],["lineCap","butt"],["lineJoin","miter"],["miterLimit",10]],qv=function(t){this.extendFrom(t,!1)};qv.prototype={constructor:qv,fill:"#000",stroke:null,opacity:1,fillOpacity:null,strokeOpacity:null,lineDash:null,lineDashOffset:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,lineWidth:1,strokeNoScale:!1,text:null,font:null,textFont:null,fontStyle:null,fontWeight:null,fontSize:null,fontFamily:null,textTag:null,textFill:"#000",textStroke:null,textWidth:null,textHeight:null,textStrokeWidth:0,textLineHeight:null,textPosition:"inside",textRect:null,textOffset:null,textAlign:null,textVerticalAlign:null,textDistance:5,textShadowColor:"transparent",textShadowBlur:0,textShadowOffsetX:0,textShadowOffsetY:0,textBoxShadowColor:"transparent",textBoxShadowBlur:0,textBoxShadowOffsetX:0,textBoxShadowOffsetY:0,transformText:!1,textRotation:0,textOrigin:null,textBackgroundColor:null,textBorderColor:null,textBorderWidth:0,textBorderRadius:0,textPadding:null,rich:null,truncate:null,blend:null,bind:function(t,e,i){var n=this,r=i&&i.style,a=!r||t.__attrCachedBy!==Xv.STYLE_BIND;t.__attrCachedBy=Xv.STYLE_BIND;for(var o=0;o<Uv.length;o++){var s=Uv[o],l=s[0];(a||n[l]!==r[l])&&(t[l]=Zv(t,l,n[l]||s[1]))}if((a||n.fill!==r.fill)&&(t.fillStyle=n.fill),(a||n.stroke!==r.stroke)&&(t.strokeStyle=n.stroke),(a||n.opacity!==r.opacity)&&(t.globalAlpha=null==n.opacity?1:n.opacity),(a||n.blend!==r.blend)&&(t.globalCompositeOperation=n.blend||"source-over"),this.hasStroke()){var h=n.lineWidth;t.lineWidth=h/(this.strokeNoScale&&e&&e.getLineScale?e.getLineScale():1)}},hasFill:function(){var t=this.fill;return null!=t&&"none"!==t},hasStroke:function(){var t=this.stroke;return null!=t&&"none"!==t&&this.lineWidth>0},extendFrom:function(t,e){if(t)for(var i in t)!t.hasOwnProperty(i)||e!==!0&&(e===!1?this.hasOwnProperty(i):null==t[i])||(this[i]=t[i])},set:function(t,e){"string"==typeof t?this[t]=e:this.extendFrom(t,!0)},clone:function(){var t=new this.constructor;return t.extendFrom(this,!0),t},getGradient:function(t,e,i){for(var n="radial"===e.type?Ri:Bi,r=n(t,e,i),a=e.colorStops,o=0;o<a.length;o++)r.addColorStop(a[o].offset,a[o].color);return r}};for(var jv=qv.prototype,Kv=0;Kv<Uv.length;Kv++){var $v=Uv[Kv];$v[0]in jv||(jv[$v[0]]=$v[1])}qv.getGradient=jv.getGradient;var Qv=function(t,e){this.image=t,this.repeat=e,this.type="pattern"};Qv.prototype.getCanvasPattern=function(t){return t.createPattern(this.image,this.repeat||"repeat")};var Jv=function(t,e,i){var n;i=i||Pv,"string"==typeof t?n=Fi(t,e,i):M(t)&&(n=t,t=n.id),this.id=t,this.dom=n;var r=n.style;r&&(n.onselectstart=Ni,r["-webkit-user-select"]="none",r["user-select"]="none",r["-webkit-touch-callout"]="none",r["-webkit-tap-highlight-color"]="rgba(0,0,0,0)",r.padding=0,r.margin=0,r["border-width"]=0),this.domBack=null,this.ctxBack=null,this.painter=e,this.config=null,this.clearColor=0,this.motionBlur=!1,this.lastFrameAlpha=.7,this.dpr=i};Jv.prototype={constructor:Jv,__dirty:!0,__used:!1,__drawIndex:0,__startIndex:0,__endIndex:0,incremental:!1,getElementCount:function(){return this.__endIndex-this.__startIndex},initContext:function(){this.ctx=this.dom.getContext("2d"),this.ctx.dpr=this.dpr},createBackBuffer:function(){var t=this.dpr;this.domBack=Fi("back-"+this.id,this.painter,t),this.ctxBack=this.domBack.getContext("2d"),1!==t&&this.ctxBack.scale(t,t)},resize:function(t,e){var i=this.dpr,n=this.dom,r=n.style,a=this.domBack;r&&(r.width=t+"px",r.height=e+"px"),n.width=t*i,n.height=e*i,a&&(a.width=t*i,a.height=e*i,1!==i&&this.ctxBack.scale(i,i))},clear:function(t,e){var i=this.dom,n=this.ctx,r=i.width,a=i.height,e=e||this.clearColor,o=this.motionBlur&&!t,s=this.lastFrameAlpha,l=this.dpr;if(o&&(this.domBack||this.createBackBuffer(),this.ctxBack.globalCompositeOperation="copy",this.ctxBack.drawImage(i,0,0,r/l,a/l)),n.clearRect(0,0,r,a),e&&"transparent"!==e){var h;e.colorStops?(h=e.__canvasGradient||qv.getGradient(n,e,{x:0,y:0,width:r,height:a}),e.__canvasGradient=h):e.image&&(h=Qv.prototype.getCanvasPattern.call(e,n)),n.save(),n.fillStyle=h||e,n.fillRect(0,0,r,a),n.restore()}if(o){var u=this.domBack;n.save(),n.globalAlpha=s,n.drawImage(u,0,0,r,a),n.restore()}}};var tm="undefined"!=typeof window&&(window.requestAnimationFrame&&window.requestAnimationFrame.bind(window)||window.msRequestAnimationFrame&&window.msRequestAnimationFrame.bind(window)||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame)||function(t){setTimeout(t,16)},em=new xv(50),im={},nm=0,rm=5e3,am=/\{([a-zA-Z0-9_]+)\|([^}]*)\}/g,om="12px sans-serif",sm={};sm.measureText=function(t,e){var i=l();return i.font=e||om,i.measureText(t)};var lm=om,hm={left:1,right:1,center:1},um={top:1,bottom:1,middle:1},cm=[["textShadowBlur","shadowBlur",0],["textShadowOffsetX","shadowOffsetX",0],["textShadowOffsetY","shadowOffsetY",0],["textShadowColor","shadowColor","transparent"]],dm={},fm={},pm=new Ii,gm=function(){};gm.prototype={constructor:gm,drawRectText:function(t,e){var i=this.style;e=i.textRect||e,this.__dirty&&hn(i,!0);var n=i.text;if(null!=n&&(n+=""),In(n,i)){t.save();var r=this.transform;i.transformText?this.setTransform(t):r&&(pm.copy(e),pm.applyTransform(r),e=pm),cn(this,t,n,i,e,Yv),t.restore()}}},Tn.prototype={constructor:Tn,type:"displayable",__dirty:!0,invisible:!1,z:0,z2:0,zlevel:0,draggable:!1,dragging:!1,silent:!1,culling:!1,cursor:"pointer",rectHover:!1,progressive:!1,incremental:!1,globalScaleRatio:1,beforeBrush:function(){},afterBrush:function(){},brush:function(){},getBoundingRect:function(){},contain:function(t,e){return this.rectContain(t,e)},traverse:function(t,e){t.call(e,this)},rectContain:function(t,e){var i=this.transformCoordToLocal(t,e),n=this.getBoundingRect();return n.contain(i[0],i[1])},dirty:function(){this.__dirty=this.__dirtyText=!0,this._rect=null,this.__zr&&this.__zr.refresh()},animateStyle:function(t){return this.animate("style",t)},attrKV:function(t,e){"style"!==t?zv.prototype.attrKV.call(this,t,e):this.style.set(e)},setStyle:function(t,e){return this.style.set(t,e),this.dirty(!1),this},useStyle:function(t){return this.style=new qv(t,this),this.dirty(!1),this},calculateTextPosition:null},u(Tn,zv),c(Tn,gm),Cn.prototype={constructor:Cn,type:"image",brush:function(t,e){var i=this.style,n=i.image;i.bind(t,this,e);var r=this._image=Wi(n,this._image,this,this.onload);if(r&&Gi(r)){var a=i.x||0,o=i.y||0,s=i.width,l=i.height,h=r.width/r.height;if(null==s&&null!=l?s=l*h:null==l&&null!=s?l=s/h:null==s&&null==l&&(s=r.width,l=r.height),this.setTransform(t),i.sWidth&&i.sHeight){var u=i.sx||0,c=i.sy||0;t.drawImage(r,u,c,i.sWidth,i.sHeight,a,o,s,l)}else if(i.sx&&i.sy){var u=i.sx,c=i.sy,d=s-u,f=l-c;t.drawImage(r,u,c,d,f,a,o,s,l)}else t.drawImage(r,a,o,s,l);null!=i.text&&(this.restoreTransform(t),this.drawRectText(t,this.getBoundingRect()))}},getBoundingRect:function(){var t=this.style;return this._rect||(this._rect=new Ii(t.x||0,t.y||0,t.width||0,t.height||0)),this._rect}},u(Cn,Tn);var vm=1e5,mm=314159,ym=.01,xm=.001,_m=new Ii(0,0,0,0),wm=new Ii(0,0,0,0),bm=function(t,e,i){this.type="canvas";var n=!t.nodeName||"CANVAS"===t.nodeName.toUpperCase();this._opts=i=o({},i||{}),this.dpr=i.devicePixelRatio||Pv,this._singleCanvas=n,this.root=t;var r=t.style;r&&(r["-webkit-tap-highlight-color"]="transparent",r["-webkit-user-select"]=r["user-select"]=r["-webkit-touch-callout"]="none",t.innerHTML=""),this.storage=e;var a=this._zlevelList=[],s=this._layers={};if(this._layerConfig={},this._needsManuallyCompositing=!1,n){var l=t.width,h=t.height;null!=i.width&&(l=i.width),null!=i.height&&(h=i.height),this.dpr=i.devicePixelRatio||1,t.width=l*this.dpr,t.height=h*this.dpr,this._width=l,this._height=h;var u=new Jv(t,this,this.dpr);u.__builtin__=!0,u.initContext(),s[mm]=u,u.zlevel=mm,a.push(mm),this._domRoot=t}else{this._width=this._getSize(0),this._height=this._getSize(1);var c=this._domRoot=En(this._width,this._height);t.appendChild(c)}this._hoverlayer=null,this._hoverElements=[]};bm.prototype={constructor:bm,getType:function(){return"canvas"},isSingleCanvas:function(){return this._singleCanvas},getViewportRoot:function(){return this._domRoot},getViewportRootOffset:function(){var t=this.getViewportRoot();return t?{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}:void 0},refresh:function(t){var e=this.storage.getDisplayList(!0),i=this._zlevelList;this._redrawId=Math.random(),this._paintList(e,t,this._redrawId);for(var n=0;n<i.length;n++){var r=i[n],a=this._layers[r];if(!a.__builtin__&&a.refresh){var o=0===n?this._backgroundColor:null;a.refresh(o)}}return this.refreshHover(),this},addHover:function(t,e){if(!t.__hoverMir){var i=new t.constructor({style:t.style,shape:t.shape,z:t.z,z2:t.z2,silent:t.silent});return i.__from=t,t.__hoverMir=i,e&&i.setStyle(e),this._hoverElements.push(i),i}},removeHover:function(t){var e=t.__hoverMir,i=this._hoverElements,n=h(i,e);n>=0&&i.splice(n,1),t.__hoverMir=null},clearHover:function(){for(var t=this._hoverElements,e=0;e<t.length;e++){var i=t[e].__from;i&&(i.__hoverMir=null)}t.length=0},refreshHover:function(){var t=this._hoverElements,e=t.length,i=this._hoverlayer;if(i&&i.clear(),e){Ei(t,this.storage.displayableSortFunc),i||(i=this._hoverlayer=this.getLayer(vm));var n={};i.ctx.save();for(var r=0;e>r;){var a=t[r],o=a.__from;o&&o.__zr?(r++,o.invisible||(a.transform=o.transform,a.invTransform=o.invTransform,a.__clipPaths=o.__clipPaths,this._doPaintEl(a,i,!0,n))):(t.splice(r,1),o.__hoverMir=null,e--)}i.ctx.restore()}},getHoverLayer:function(){return this.getLayer(vm)},_paintList:function(t,e,i){if(this._redrawId===i){e=e||!1,this._updateLayerStatus(t);var n=this._doPaintList(t,e);if(this._needsManuallyCompositing&&this._compositeManually(),!n){var r=this;tm(function(){r._paintList(t,e,i)})}}},_compositeManually:function(){var t=this.getLayer(mm).ctx,e=this._domRoot.width,i=this._domRoot.height;t.clearRect(0,0,e,i),this.eachBuiltinLayer(function(n){n.virtual&&t.drawImage(n.dom,0,0,e,i)})},_doPaintList:function(t,e){for(var i=[],n=0;n<this._zlevelList.length;n++){var r=this._zlevelList[n],a=this._layers[r];a.__builtin__&&a!==this._hoverlayer&&(a.__dirty||e)&&i.push(a)}for(var o=!0,s=0;s<i.length;s++){var a=i[s],l=a.ctx,h={};l.save();var u=e?a.__startIndex:a.__drawIndex,c=!e&&a.incremental&&Date.now,d=c&&Date.now(),p=a.zlevel===this._zlevelList[0]?this._backgroundColor:null;if(a.__startIndex===a.__endIndex)a.clear(!1,p);else if(u===a.__startIndex){var g=t[u];g.incremental&&g.notClear&&!e||a.clear(!1,p)}-1===u&&(console.error("For some unknown reason. drawIndex is -1"),u=a.__startIndex);for(var v=u;v<a.__endIndex;v++){var m=t[v];if(this._doPaintEl(m,a,e,h),m.__dirty=m.__dirtyText=!1,c){var y=Date.now()-d;if(y>15)break}}a.__drawIndex=v,a.__drawIndex<a.__endIndex&&(o=!1),h.prevElClipPaths&&l.restore(),l.restore()}return Ag.wxa&&f(this._layers,function(t){t&&t.ctx&&t.ctx.draw&&t.ctx.draw()}),o},_doPaintEl:function(t,e,i,n){var r=e.ctx,a=t.transform;if(!(!e.__dirty&&!i||t.invisible||0===t.style.opacity||a&&!a[0]&&!a[3]||t.culling&&Pn(t,this._width,this._height))){var o=t.__clipPaths,s=n.prevElClipPaths;(!s||Ln(o,s))&&(s&&(r.restore(),n.prevElClipPaths=null,n.prevEl=null),o&&(r.save(),On(o,r),n.prevElClipPaths=o)),t.beforeBrush&&t.beforeBrush(r),t.brush(r,n.prevEl||null),n.prevEl=t,t.afterBrush&&t.afterBrush(r)}},getLayer:function(t,e){this._singleCanvas&&!this._needsManuallyCompositing&&(t=mm);var i=this._layers[t];return i||(i=new Jv("zr_"+t,this,this.dpr),i.zlevel=t,i.__builtin__=!0,this._layerConfig[t]?r(i,this._layerConfig[t],!0):this._layerConfig[t-ym]&&r(i,this._layerConfig[t-ym],!0),e&&(i.virtual=e),this.insertLayer(t,i),i.initContext()),i},insertLayer:function(t,e){var i=this._layers,n=this._zlevelList,r=n.length,a=null,o=-1,s=this._domRoot;if(i[t])return void Ov("ZLevel "+t+" has been used already");if(!kn(e))return void Ov("Layer of zlevel "+t+" is not valid");if(r>0&&t>n[0]){for(o=0;r-1>o&&!(n[o]<t&&n[o+1]>t);o++);a=i[n[o]]}if(n.splice(o+1,0,t),i[t]=e,!e.virtual)if(a){var l=a.dom;l.nextSibling?s.insertBefore(e.dom,l.nextSibling):s.appendChild(e.dom)}else s.firstChild?s.insertBefore(e.dom,s.firstChild):s.appendChild(e.dom)},eachLayer:function(t,e){var i,n,r=this._zlevelList;for(n=0;n<r.length;n++)i=r[n],t.call(e,this._layers[i],i)},eachBuiltinLayer:function(t,e){var i,n,r,a=this._zlevelList;for(r=0;r<a.length;r++)n=a[r],i=this._layers[n],i.__builtin__&&t.call(e,i,n)},eachOtherLayer:function(t,e){var i,n,r,a=this._zlevelList;for(r=0;r<a.length;r++)n=a[r],i=this._layers[n],i.__builtin__||t.call(e,i,n)},getLayers:function(){return this._layers},_updateLayerStatus:function(t){function e(t){a&&(a.__endIndex!==t&&(a.__dirty=!0),a.__endIndex=t)}if(this.eachBuiltinLayer(function(t){t.__dirty=t.__used=!1}),this._singleCanvas)for(var i=1;i<t.length;i++){var n=t[i];if(n.zlevel!==t[i-1].zlevel||n.incremental){this._needsManuallyCompositing=!0;break}}for(var r,a=null,o=0,i=0;i<t.length;i++){var s,n=t[i],l=n.zlevel;r!==l&&(r=l,o=0),n.incremental?(s=this.getLayer(l+xm,this._needsManuallyCompositing),s.incremental=!0,o=1):s=this.getLayer(l+(o>0?ym:0),this._needsManuallyCompositing),s.__builtin__||Ov("ZLevel "+l+" has been used by unkown layer "+s.id),s!==a&&(s.__used=!0,s.__startIndex!==i&&(s.__dirty=!0),s.__startIndex=i,s.__drawIndex=s.incremental?-1:i,e(i),a=s),n.__dirty&&(s.__dirty=!0,s.incremental&&s.__drawIndex<0&&(s.__drawIndex=i))}e(i),this.eachBuiltinLayer(function(t){!t.__used&&t.getElementCount()>0&&(t.__dirty=!0,t.__startIndex=t.__endIndex=t.__drawIndex=0),t.__dirty&&t.__drawIndex<0&&(t.__drawIndex=t.__startIndex)})},clear:function(){return this.eachBuiltinLayer(this._clearLayer),this},_clearLayer:function(t){t.clear()},setBackgroundColor:function(t){this._backgroundColor=t},configLayer:function(t,e){if(e){var i=this._layerConfig;i[t]?r(i[t],e,!0):i[t]=e;for(var n=0;n<this._zlevelList.length;n++){var a=this._zlevelList[n];if(a===t||a===t+ym){var o=this._layers[a];r(o,i[t],!0)}}}},delLayer:function(t){var e=this._layers,i=this._zlevelList,n=e[t];n&&(n.dom.parentNode.removeChild(n.dom),delete e[t],i.splice(h(i,t),1))},resize:function(t,e){if(this._domRoot.style){var i=this._domRoot;i.style.display="none";var n=this._opts;if(null!=t&&(n.width=t),null!=e&&(n.height=e),t=this._getSize(0),e=this._getSize(1),i.style.display="",this._width!==t||e!==this._height){i.style.width=t+"px",i.style.height=e+"px";for(var r in this._layers)this._layers.hasOwnProperty(r)&&this._layers[r].resize(t,e);f(this._progressiveLayers,function(i){i.resize(t,e)}),this.refresh(!0)}this._width=t,this._height=e}else{if(null==t||null==e)return;this._width=t,this._height=e,this.getLayer(mm).resize(t,e)}return this},clearLayer:function(t){var e=this._layers[t];e&&e.clear()},dispose:function(){this.root.innerHTML="",this.root=this.storage=this._domRoot=this._layers=null},getRenderedCanvas:function(t){if(t=t||{},this._singleCanvas&&!this._compositeManually)return this._layers[mm].dom;var e=new Jv("image",this,t.pixelRatio||this.dpr);if(e.initContext(),e.clear(!1,t.backgroundColor||this._backgroundColor),t.pixelRatio<=this.dpr){this.refresh();var i=e.dom.width,n=e.dom.height,r=e.ctx;this.eachLayer(function(t){t.__builtin__?r.drawImage(t.dom,0,0,i,n):t.renderToCanvas&&(e.ctx.save(),t.renderToCanvas(e.ctx),e.ctx.restore())})}else for(var a={},o=this.storage.getDisplayList(!0),s=0;s<o.length;s++){var l=o[s];this._doPaintEl(l,e,!0,a)}return e.dom},getWidth:function(){return this._width},getHeight:function(){return this._height},_getSize:function(t){var e=this._opts,i=["width","height"][t],n=["clientWidth","clientHeight"][t],r=["paddingLeft","paddingTop"][t],a=["paddingRight","paddingBottom"][t];if(null!=e[i]&&"auto"!==e[i])return parseFloat(e[i]);var o=this.root,s=document.defaultView.getComputedStyle(o);return(o[n]||Dn(s[i])||Dn(o.style[i]))-(Dn(s[r])||0)-(Dn(s[a])||0)|0},pathToImage:function(t,e){e=e||this.dpr;var i=document.createElement("canvas"),n=i.getContext("2d"),r=t.getBoundingRect(),a=t.style,o=a.shadowBlur*e,s=a.shadowOffsetX*e,l=a.shadowOffsetY*e,h=a.hasStroke()?a.lineWidth:0,u=Math.max(h/2,-s+o),c=Math.max(h/2,s+o),d=Math.max(h/2,-l+o),f=Math.max(h/2,l+o),p=r.width+u+c,g=r.height+d+f;i.width=p*e,i.height=g*e,n.scale(e,e),n.clearRect(0,0,p,g),n.dpr=e;var v={position:t.position,rotation:t.rotation,scale:t.scale};t.position=[u-r.x,d-r.y],t.rotation=0,t.scale=[1,1],t.updateTransform(),t&&t.brush(n);var m=Cn,y=new m({style:{x:0,y:0,image:i}});return null!=v.position&&(y.position=t.position=v.position),null!=v.rotation&&(y.rotation=t.rotation=v.rotation),null!=v.scale&&(y.scale=t.scale=v.scale),y}};var Mm=function(t){t=t||{},this.stage=t.stage||{},this.onframe=t.onframe||function(){},this._clips=[],this._running=!1,this._time,this._pausedTime,this._pauseStart,this._paused=!1,Ug.call(this)};Mm.prototype={constructor:Mm,addClip:function(t){this._clips.push(t)},addAnimator:function(t){t.animation=this;for(var e=t.getClips(),i=0;i<e.length;i++)this.addClip(e[i])},removeClip:function(t){var e=h(this._clips,t);e>=0&&this._clips.splice(e,1)},removeAnimator:function(t){for(var e=t.getClips(),i=0;i<e.length;i++)this.removeClip(e[i]);t.animation=null},_update:function(){for(var t=(new Date).getTime()-this._pausedTime,e=t-this._time,i=this._clips,n=i.length,r=[],a=[],o=0;n>o;o++){var s=i[o],l=s.step(t,e);l&&(r.push(l),a.push(s))}for(var o=0;n>o;)i[o]._needsRemove?(i[o]=i[n-1],i.pop(),n--):o++;n=r.length;for(var o=0;n>o;o++)a[o].fire(r[o]);this._time=t,this.onframe(e),this.trigger("frame",e),this.stage.update&&this.stage.update()},_startLoop:function(){function t(){e._running&&(tm(t),!e._paused&&e._update())}var e=this;this._running=!0,tm(t)},start:function(){this._time=(new Date).getTime(),this._pausedTime=0,this._startLoop()},stop:function(){this._running=!1},pause:function(){this._paused||(this._pauseStart=(new Date).getTime(),this._paused=!0)},resume:function(){this._paused&&(this._pausedTime+=(new Date).getTime()-this._pauseStart,this._paused=!1)},clear:function(){this._clips=[]},isFinished:function(){return!this._clips.length},animate:function(t,e){e=e||{};var i=new Cv(t,e.loop,e.getter,e.setter);return this.addAnimator(i),i}},c(Mm,Ug);var Sm=300,Am=Ag.domSupported,Im=function(){var t=["click","dblclick","mousewheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],e=["touchstart","touchend","touchmove"],i={pointerdown:1,pointerup:1,pointermove:1,pointerout:1},n=p(t,function(t){var e=t.replace("mouse","pointer");return i.hasOwnProperty(e)?e:t});return{mouse:t,touch:e,pointer:n}}(),Tm={mouse:["mousemove","mouseup"],pointer:["pointermove","pointerup"]},Cm=Wn.prototype;Cm.stopPropagation=Cm.stopImmediatePropagation=Cm.preventDefault=V;var Dm={mousedown:function(t){t=be(this.dom,t),this._mayPointerCapture=[t.zrX,t.zrY],this.trigger("mousedown",t)},mousemove:function(t){t=be(this.dom,t);var e=this._mayPointerCapture;!e||t.zrX===e[0]&&t.zrY===e[1]||Yn(this,!0),this.trigger("mousemove",t)},mouseup:function(t){t=be(this.dom,t),Yn(this,!1),this.trigger("mouseup",t)},mouseout:function(t){t=be(this.dom,t),this._pointerCapturing&&(t.zrEventControl="no_globalout");var e=t.toElement||t.relatedTarget;t.zrIsToLocalDOM=Vn(this,e),this.trigger("mouseout",t)},touchstart:function(t){t=be(this.dom,t),Nn(t),this._lastTouchMoment=new Date,this.handler.processGesture(t,"start"),Dm.mousemove.call(this,t),Dm.mousedown.call(this,t)},touchmove:function(t){t=be(this.dom,t),Nn(t),this.handler.processGesture(t,"change"),Dm.mousemove.call(this,t)},touchend:function(t){t=be(this.dom,t),Nn(t),this.handler.processGesture(t,"end"),Dm.mouseup.call(this,t),+new Date-this._lastTouchMoment<Sm&&Dm.click.call(this,t)},pointerdown:function(t){Dm.mousedown.call(this,t)},pointermove:function(t){Bn(t)||Dm.mousemove.call(this,t)},pointerup:function(t){Dm.mouseup.call(this,t)},pointerout:function(t){Bn(t)||Dm.mouseout.call(this,t)}};f(["click","mousewheel","dblclick","contextmenu"],function(t){Dm[t]=function(e){e=be(this.dom,e),this.trigger(t,e)}});var km={pointermove:function(t){Bn(t)||km.mousemove.call(this,t)},pointerup:function(t){km.mouseup.call(this,t)},mousemove:function(t){this.trigger("mousemove",t)},mouseup:function(t){var e=this._pointerCapturing;Yn(this,!1),this.trigger("mouseup",t),e&&(t.zrEventControl="only_globalout",this.trigger("mouseout",t))}},Pm=qn.prototype;Pm.dispose=function(){Xn(this._localHandlerScope),Am&&Xn(this._globalHandlerScope)},Pm.setCursor=function(t){this.dom.style&&(this.dom.style.cursor=t||"default")},c(qn,Ug);var Lm=!Ag.canvasSupported,Om={canvas:bm},Em={},zm="4.3.2",Bm=function(t,e,i){i=i||{},this.dom=e,this.id=t;var n=this,r=new Hv,a=i.renderer;if(Lm){if(!Om.vml)throw new Error("You need to require 'zrender/vml/vml' to support IE8");a="vml"}else a&&Om[a]||(a="canvas");var o=new Om[a](e,r,i,t);this.storage=r,this.painter=o;var s=Ag.node||Ag.worker?null:new qn(o.getViewportRoot(),o.root);this.handler=new av(r,o,s,o.root),this.animation=new Mm({stage:{update:y(this.flush,this)}}),this.animation.start(),this._needsRefresh;var l=r.delFromStorage,h=r.addToStorage;r.delFromStorage=function(t){l.call(r,t),t&&t.removeSelfFromZr(n)},r.addToStorage=function(t){h.call(r,t),t.addSelfToZr(n)}};Bm.prototype={constructor:Bm,getId:function(){return this.id},add:function(t){this.storage.addRoot(t),this._needsRefresh=!0},remove:function(t){this.storage.delRoot(t),this._needsRefresh=!0},configLayer:function(t,e){this.painter.configLayer&&this.painter.configLayer(t,e),this._needsRefresh=!0},setBackgroundColor:function(t){this.painter.setBackgroundColor&&this.painter.setBackgroundColor(t),this._needsRefresh=!0},refreshImmediately:function(){this._needsRefresh=this._needsRefreshHover=!1,this.painter.refresh(),this._needsRefresh=this._needsRefreshHover=!1},refresh:function(){this._needsRefresh=!0},flush:function(){var t;this._needsRefresh&&(t=!0,this.refreshImmediately()),this._needsRefreshHover&&(t=!0,this.refreshHoverImmediately()),t&&this.trigger("rendered")},addHover:function(t,e){if(this.painter.addHover){var i=this.painter.addHover(t,e);return this.refreshHover(),i}},removeHover:function(t){this.painter.removeHover&&(this.painter.removeHover(t),this.refreshHover())},clearHover:function(){this.painter.clearHover&&(this.painter.clearHover(),this.refreshHover())},refreshHover:function(){this._needsRefreshHover=!0},refreshHoverImmediately:function(){this._needsRefreshHover=!1,this.painter.refreshHover&&this.painter.refreshHover()},resize:function(t){t=t||{},this.painter.resize(t.width,t.height),this.handler.resize()},clearAnimation:function(){this.animation.clear()},getWidth:function(){return this.painter.getWidth()},getHeight:function(){return this.painter.getHeight()},pathToImage:function(t,e){return this.painter.pathToImage(t,e)},setCursorStyle:function(t){this.handler.setCursorStyle(t)},findHover:function(t,e){return this.handler.findHover(t,e)},on:function(t,e,i){this.handler.on(t,e,i)},off:function(t,e){this.handler.off(t,e)},trigger:function(t,e){this.handler.trigger(t,e)},clear:function(){this.storage.delRoot(),this.painter.clear()},dispose:function(){this.animation.stop(),this.clear(),this.storage.dispose(),this.painter.dispose(),this.handler.dispose(),this.animation=this.storage=this.painter=this.handler=null,Jn(this.id)}};var Rm=(Object.freeze||Object)({version:zm,init:jn,dispose:Kn,getInstance:$n,registerPainter:Qn}),Nm=f,Fm=M,Vm=_,Wm="series\x00",Hm=["fontStyle","fontWeight","fontSize","fontFamily","rich","tag","color","textBorderColor","textBorderWidth","width","height","lineHeight","align","verticalAlign","baseline","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY","textShadowColor","textShadowBlur","textShadowOffsetX","textShadowOffsetY","backgroundColor","borderColor","borderWidth","borderRadius","padding"],Gm=0,Zm=".",Xm="___EC__COMPONENT__CONTAINER___",Ym=0,Um=function(t){for(var e=0;e<t.length;e++)t[e][1]||(t[e][1]=t[e][0]);return function(e,i,n){for(var r={},a=0;a<t.length;a++){var o=t[a][1];if(!(i&&h(i,o)>=0||n&&h(n,o)<0)){var s=e.getShallow(o);null!=s&&(r[t[a][0]]=s)}}return r}},qm=Um([["lineWidth","width"],["stroke","color"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"]]),jm={getLineStyle:function(t){var e=qm(this,t);return e.lineDash=this.getLineDash(e.lineWidth),e},getLineDash:function(t){null==t&&(t=1);var e=this.get("type"),i=Math.max(t,2),n=4*t;return"solid"===e||null==e?!1:"dashed"===e?[n,n]:[i,i]}},Km=Um([["fill","color"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["opacity"],["shadowColor"]]),$m={getAreaStyle:function(t,e){return Km(this,t,e)}},Qm=Math.pow,Jm=Math.sqrt,ty=1e-8,ey=1e-4,iy=Jm(3),ny=1/3,ry=W(),ay=W(),oy=W(),sy=Math.min,ly=Math.max,hy=Math.sin,uy=Math.cos,cy=2*Math.PI,dy=W(),fy=W(),py=W(),gy=[],vy=[],my={M:1,L:2,C:3,Q:4,A:5,Z:6,R:7},yy=[],xy=[],_y=[],wy=[],by=Math.min,My=Math.max,Sy=Math.cos,Ay=Math.sin,Iy=Math.sqrt,Ty=Math.abs,Cy="undefined"!=typeof Float32Array,Dy=function(t){this._saveData=!t,this._saveData&&(this.data=[]),this._ctx=null};Dy.prototype={constructor:Dy,_xi:0,_yi:0,_x0:0,_y0:0,_ux:0,_uy:0,_len:0,_lineDash:null,_dashOffset:0,_dashIdx:0,_dashSum:0,setScale:function(t,e,i){i=i||0,this._ux=Ty(i/Pv/t)||0,this._uy=Ty(i/Pv/e)||0},getContext:function(){return this._ctx},beginPath:function(t){return this._ctx=t,t&&t.beginPath(),t&&(this.dpr=t.dpr),this._saveData&&(this._len=0),this._lineDash&&(this._lineDash=null,this._dashOffset=0),this},moveTo:function(t,e){return this.addData(my.M,t,e),this._ctx&&this._ctx.moveTo(t,e),this._x0=t,this._y0=e,this._xi=t,this._yi=e,this},lineTo:function(t,e){var i=Ty(t-this._xi)>this._ux||Ty(e-this._yi)>this._uy||this._len<5;return this.addData(my.L,t,e),this._ctx&&i&&(this._needsDash()?this._dashedLineTo(t,e):this._ctx.lineTo(t,e)),i&&(this._xi=t,this._yi=e),this},bezierCurveTo:function(t,e,i,n,r,a){return this.addData(my.C,t,e,i,n,r,a),this._ctx&&(this._needsDash()?this._dashedBezierTo(t,e,i,n,r,a):this._ctx.bezierCurveTo(t,e,i,n,r,a)),this._xi=r,this._yi=a,this},quadraticCurveTo:function(t,e,i,n){return this.addData(my.Q,t,e,i,n),this._ctx&&(this._needsDash()?this._dashedQuadraticTo(t,e,i,n):this._ctx.quadraticCurveTo(t,e,i,n)),this._xi=i,this._yi=n,this},arc:function(t,e,i,n,r,a){return this.addData(my.A,t,e,i,i,n,r-n,0,a?0:1),this._ctx&&this._ctx.arc(t,e,i,n,r,a),this._xi=Sy(r)*i+t,this._yi=Ay(r)*i+e,this
},arcTo:function(t,e,i,n,r){return this._ctx&&this._ctx.arcTo(t,e,i,n,r),this},rect:function(t,e,i,n){return this._ctx&&this._ctx.rect(t,e,i,n),this.addData(my.R,t,e,i,n),this},closePath:function(){this.addData(my.Z);var t=this._ctx,e=this._x0,i=this._y0;return t&&(this._needsDash()&&this._dashedLineTo(e,i),t.closePath()),this._xi=e,this._yi=i,this},fill:function(t){t&&t.fill(),this.toStatic()},stroke:function(t){t&&t.stroke(),this.toStatic()},setLineDash:function(t){if(t instanceof Array){this._lineDash=t,this._dashIdx=0;for(var e=0,i=0;i<t.length;i++)e+=t[i];this._dashSum=e}return this},setLineDashOffset:function(t){return this._dashOffset=t,this},len:function(){return this._len},setData:function(t){var e=t.length;this.data&&this.data.length===e||!Cy||(this.data=new Float32Array(e));for(var i=0;e>i;i++)this.data[i]=t[i];this._len=e},appendPath:function(t){t instanceof Array||(t=[t]);for(var e=t.length,i=0,n=this._len,r=0;e>r;r++)i+=t[r].len();Cy&&this.data instanceof Float32Array&&(this.data=new Float32Array(n+i));for(var r=0;e>r;r++)for(var a=t[r].data,o=0;o<a.length;o++)this.data[n++]=a[o];this._len=n},addData:function(t){if(this._saveData){var e=this.data;this._len+arguments.length>e.length&&(this._expandData(),e=this.data);for(var i=0;i<arguments.length;i++)e[this._len++]=arguments[i];this._prevCmd=t}},_expandData:function(){if(!(this.data instanceof Array)){for(var t=[],e=0;e<this._len;e++)t[e]=this.data[e];this.data=t}},_needsDash:function(){return this._lineDash},_dashedLineTo:function(t,e){var i,n,r=this._dashSum,a=this._dashOffset,o=this._lineDash,s=this._ctx,l=this._xi,h=this._yi,u=t-l,c=e-h,d=Iy(u*u+c*c),f=l,p=h,g=o.length;for(u/=d,c/=d,0>a&&(a=r+a),a%=r,f-=a*u,p-=a*c;u>0&&t>=f||0>u&&f>=t||0===u&&(c>0&&e>=p||0>c&&p>=e);)n=this._dashIdx,i=o[n],f+=u*i,p+=c*i,this._dashIdx=(n+1)%g,u>0&&l>f||0>u&&f>l||c>0&&h>p||0>c&&p>h||s[n%2?"moveTo":"lineTo"](u>=0?by(f,t):My(f,t),c>=0?by(p,e):My(p,e));u=f-t,c=p-e,this._dashOffset=-Iy(u*u+c*c)},_dashedBezierTo:function(t,e,i,n,r,a){var o,s,l,h,u,c=this._dashSum,d=this._dashOffset,f=this._lineDash,p=this._ctx,g=this._xi,v=this._yi,m=Sr,y=0,x=this._dashIdx,_=f.length,w=0;for(0>d&&(d=c+d),d%=c,o=0;1>o;o+=.1)s=m(g,t,i,r,o+.1)-m(g,t,i,r,o),l=m(v,e,n,a,o+.1)-m(v,e,n,a,o),y+=Iy(s*s+l*l);for(;_>x&&(w+=f[x],!(w>d));x++);for(o=(w-d)/y;1>=o;)h=m(g,t,i,r,o),u=m(v,e,n,a,o),x%2?p.moveTo(h,u):p.lineTo(h,u),o+=f[x]/y,x=(x+1)%_;x%2!==0&&p.lineTo(r,a),s=r-h,l=a-u,this._dashOffset=-Iy(s*s+l*l)},_dashedQuadraticTo:function(t,e,i,n){var r=i,a=n;i=(i+2*t)/3,n=(n+2*e)/3,t=(this._xi+2*t)/3,e=(this._yi+2*e)/3,this._dashedBezierTo(t,e,i,n,r,a)},toStatic:function(){var t=this.data;t instanceof Array&&(t.length=this._len,Cy&&(this.data=new Float32Array(t)))},getBoundingRect:function(){yy[0]=yy[1]=_y[0]=_y[1]=Number.MAX_VALUE,xy[0]=xy[1]=wy[0]=wy[1]=-Number.MAX_VALUE;for(var t=this.data,e=0,i=0,n=0,r=0,a=0;a<t.length;){var o=t[a++];switch(1===a&&(e=t[a],i=t[a+1],n=e,r=i),o){case my.M:n=t[a++],r=t[a++],e=n,i=r,_y[0]=n,_y[1]=r,wy[0]=n,wy[1]=r;break;case my.L:Rr(e,i,t[a],t[a+1],_y,wy),e=t[a++],i=t[a++];break;case my.C:Nr(e,i,t[a++],t[a++],t[a++],t[a++],t[a],t[a+1],_y,wy),e=t[a++],i=t[a++];break;case my.Q:Fr(e,i,t[a++],t[a++],t[a],t[a+1],_y,wy),e=t[a++],i=t[a++];break;case my.A:var s=t[a++],l=t[a++],h=t[a++],u=t[a++],c=t[a++],d=t[a++]+c;a+=1;var f=1-t[a++];1===a&&(n=Sy(c)*h+s,r=Ay(c)*u+l),Vr(s,l,h,u,c,d,f,_y,wy),e=Sy(d)*h+s,i=Ay(d)*u+l;break;case my.R:n=e=t[a++],r=i=t[a++];var p=t[a++],g=t[a++];Rr(n,r,n+p,r+g,_y,wy);break;case my.Z:e=n,i=r}oe(yy,yy,_y),se(xy,xy,wy)}return 0===a&&(yy[0]=yy[1]=xy[0]=xy[1]=0),new Ii(yy[0],yy[1],xy[0]-yy[0],xy[1]-yy[1])},rebuildPath:function(t){for(var e,i,n,r,a,o,s=this.data,l=this._ux,h=this._uy,u=this._len,c=0;u>c;){var d=s[c++];switch(1===c&&(n=s[c],r=s[c+1],e=n,i=r),d){case my.M:e=n=s[c++],i=r=s[c++],t.moveTo(n,r);break;case my.L:a=s[c++],o=s[c++],(Ty(a-n)>l||Ty(o-r)>h||c===u-1)&&(t.lineTo(a,o),n=a,r=o);break;case my.C:t.bezierCurveTo(s[c++],s[c++],s[c++],s[c++],s[c++],s[c++]),n=s[c-2],r=s[c-1];break;case my.Q:t.quadraticCurveTo(s[c++],s[c++],s[c++],s[c++]),n=s[c-2],r=s[c-1];break;case my.A:var f=s[c++],p=s[c++],g=s[c++],v=s[c++],m=s[c++],y=s[c++],x=s[c++],_=s[c++],w=g>v?g:v,b=g>v?1:g/v,M=g>v?v/g:1,S=Math.abs(g-v)>.001,A=m+y;S?(t.translate(f,p),t.rotate(x),t.scale(b,M),t.arc(0,0,w,m,A,1-_),t.scale(1/b,1/M),t.rotate(-x),t.translate(-f,-p)):t.arc(f,p,w,m,A,1-_),1===c&&(e=Sy(m)*g+f,i=Ay(m)*v+p),n=Sy(A)*g+f,r=Ay(A)*v+p;break;case my.R:e=n=s[c],i=r=s[c+1],t.rect(s[c++],s[c++],s[c++],s[c++]);break;case my.Z:t.closePath(),n=e,r=i}}}},Dy.CMD=my;var ky=2*Math.PI,Py=2*Math.PI,Ly=Dy.CMD,Oy=2*Math.PI,Ey=1e-4,zy=[-1,-1,-1],By=[-1,-1],Ry=Qv.prototype.getCanvasPattern,Ny=Math.abs,Fy=new Dy(!0);ea.prototype={constructor:ea,type:"path",__dirtyPath:!0,strokeContainThreshold:5,segmentIgnoreThreshold:0,subPixelOptimize:!1,brush:function(t,e){var i=this.style,n=this.path||Fy,r=i.hasStroke(),a=i.hasFill(),o=i.fill,s=i.stroke,l=a&&!!o.colorStops,h=r&&!!s.colorStops,u=a&&!!o.image,c=r&&!!s.image;if(i.bind(t,this,e),this.setTransform(t),this.__dirty){var d;l&&(d=d||this.getBoundingRect(),this._fillGradient=i.getGradient(t,o,d)),h&&(d=d||this.getBoundingRect(),this._strokeGradient=i.getGradient(t,s,d))}l?t.fillStyle=this._fillGradient:u&&(t.fillStyle=Ry.call(o,t)),h?t.strokeStyle=this._strokeGradient:c&&(t.strokeStyle=Ry.call(s,t));var f=i.lineDash,p=i.lineDashOffset,g=!!t.setLineDash,v=this.getGlobalScale();if(n.setScale(v[0],v[1],this.segmentIgnoreThreshold),this.__dirtyPath||f&&!g&&r?(n.beginPath(t),f&&!g&&(n.setLineDash(f),n.setLineDashOffset(p)),this.buildPath(n,this.shape,!1),this.path&&(this.__dirtyPath=!1)):(t.beginPath(),this.path.rebuildPath(t)),a)if(null!=i.fillOpacity){var m=t.globalAlpha;t.globalAlpha=i.fillOpacity*i.opacity,n.fill(t),t.globalAlpha=m}else n.fill(t);if(f&&g&&(t.setLineDash(f),t.lineDashOffset=p),r)if(null!=i.strokeOpacity){var m=t.globalAlpha;t.globalAlpha=i.strokeOpacity*i.opacity,n.stroke(t),t.globalAlpha=m}else n.stroke(t);f&&g&&t.setLineDash([]),null!=i.text&&(this.restoreTransform(t),this.drawRectText(t,this.getBoundingRect()))},buildPath:function(){},createPathProxy:function(){this.path=new Dy},getBoundingRect:function(){var t=this._rect,e=this.style,i=!t;if(i){var n=this.path;n||(n=this.path=new Dy),this.__dirtyPath&&(n.beginPath(),this.buildPath(n,this.shape,!1)),t=n.getBoundingRect()}if(this._rect=t,e.hasStroke()){var r=this._rectWithStroke||(this._rectWithStroke=t.clone());if(this.__dirty||i){r.copy(t);var a=e.lineWidth,o=e.strokeNoScale?this.getLineScale():1;e.hasFill()||(a=Math.max(a,this.strokeContainThreshold||4)),o>1e-10&&(r.width+=a/o,r.height+=a/o,r.x-=a/o/2,r.y-=a/o/2)}return r}return t},contain:function(t,e){var i=this.transformCoordToLocal(t,e),n=this.getBoundingRect(),r=this.style;if(t=i[0],e=i[1],n.contain(t,e)){var a=this.path.data;if(r.hasStroke()){var o=r.lineWidth,s=r.strokeNoScale?this.getLineScale():1;if(s>1e-10&&(r.hasFill()||(o=Math.max(o,this.strokeContainThreshold)),ta(a,o/s,t,e)))return!0}if(r.hasFill())return Jr(a,t,e)}return!1},dirty:function(t){null==t&&(t=!0),t&&(this.__dirtyPath=t,this._rect=null),this.__dirty=this.__dirtyText=!0,this.__zr&&this.__zr.refresh(),this.__clipTarget&&this.__clipTarget.dirty()},animateShape:function(t){return this.animate("shape",t)},attrKV:function(t,e){"shape"===t?(this.setShape(e),this.__dirtyPath=!0,this._rect=null):Tn.prototype.attrKV.call(this,t,e)},setShape:function(t,e){var i=this.shape;if(i){if(M(t))for(var n in t)t.hasOwnProperty(n)&&(i[n]=t[n]);else i[t]=e;this.dirty(!0)}return this},getLineScale:function(){var t=this.transform;return t&&Ny(t[0]-1)>1e-10&&Ny(t[3]-1)>1e-10?Math.sqrt(Ny(t[0]*t[3]-t[2]*t[1])):1}},ea.extend=function(t){var e=function(e){ea.call(this,e),t.style&&this.style.extendFrom(t.style,!1);var i=t.shape;if(i){this.shape=this.shape||{};var n=this.shape;for(var r in i)!n.hasOwnProperty(r)&&i.hasOwnProperty(r)&&(n[r]=i[r])}t.init&&t.init.call(this,e)};u(e,ea);for(var i in t)"style"!==i&&"shape"!==i&&(e.prototype[i]=t[i]);return e},u(ea,Tn);var Vy=Dy.CMD,Wy=[[],[],[]],Hy=Math.sqrt,Gy=Math.atan2,Zy=function(t,e){var i,n,r,a,o,s,l=t.data,h=Vy.M,u=Vy.C,c=Vy.L,d=Vy.R,f=Vy.A,p=Vy.Q;for(r=0,a=0;r<l.length;){switch(i=l[r++],a=r,n=0,i){case h:n=1;break;case c:n=1;break;case u:n=3;break;case p:n=2;break;case f:var g=e[4],v=e[5],m=Hy(e[0]*e[0]+e[1]*e[1]),y=Hy(e[2]*e[2]+e[3]*e[3]),x=Gy(-e[1]/y,e[0]/m);l[r]*=m,l[r++]+=g,l[r]*=y,l[r++]+=v,l[r++]*=m,l[r++]*=y,l[r++]+=x,l[r++]+=x,r+=2,a=r;break;case d:s[0]=l[r++],s[1]=l[r++],ae(s,s,e),l[a++]=s[0],l[a++]=s[1],s[0]+=l[r++],s[1]+=l[r++],ae(s,s,e),l[a++]=s[0],l[a++]=s[1]}for(o=0;n>o;o++){var s=Wy[o];s[0]=l[r++],s[1]=l[r++],ae(s,s,e),l[a++]=s[0],l[a++]=s[1]}}},Xy=Math.sqrt,Yy=Math.sin,Uy=Math.cos,qy=Math.PI,jy=function(t){return Math.sqrt(t[0]*t[0]+t[1]*t[1])},Ky=function(t,e){return(t[0]*e[0]+t[1]*e[1])/(jy(t)*jy(e))},$y=function(t,e){return(t[0]*e[1]<t[1]*e[0]?-1:1)*Math.acos(Ky(t,e))},Qy=/([mlvhzcqtsa])([^mlvhzcqtsa]*)/gi,Jy=/-?([0-9]*\.)?[0-9]+([eE]-?[0-9]+)?/g,tx=function(t){Tn.call(this,t)};tx.prototype={constructor:tx,type:"text",brush:function(t,e){var i=this.style;this.__dirty&&hn(i,!0),i.fill=i.stroke=i.shadowBlur=i.shadowColor=i.shadowOffsetX=i.shadowOffsetY=null;var n=i.text;return null!=n&&(n+=""),In(n,i)?(this.setTransform(t),cn(this,t,n,i,null,e),void this.restoreTransform(t)):void(t.__attrCachedBy=Xv.NONE)},getBoundingRect:function(){var t=this.style;if(this.__dirty&&hn(t,!0),!this._rect){var e=t.text;null!=e?e+="":e="";var i=Xi(t.text+"",t.font,t.textAlign,t.textVerticalAlign,t.textPadding,t.textLineHeight,t.rich);if(i.x+=t.x||0,i.y+=t.y||0,bn(t.textStroke,t.textStrokeWidth)){var n=t.textStrokeWidth;i.x-=n/2,i.y-=n/2,i.width+=n,i.height+=n}this._rect=i}return this._rect}},u(tx,Tn);var ex=ea.extend({type:"circle",shape:{cx:0,cy:0,r:0},buildPath:function(t,e,i){i&&t.moveTo(e.cx+e.r,e.cy),t.arc(e.cx,e.cy,e.r,0,2*Math.PI,!0)}}),ix=[["shadowBlur",0],["shadowColor","#000"],["shadowOffsetX",0],["shadowOffsetY",0]],nx=function(t){return Ag.browser.ie&&Ag.browser.version>=11?function(){var e,i=this.__clipPaths,n=this.style;if(i)for(var r=0;r<i.length;r++){var a=i[r],o=a&&a.shape,s=a&&a.type;if(o&&("sector"===s&&o.startAngle===o.endAngle||"rect"===s&&(!o.width||!o.height))){for(var l=0;l<ix.length;l++)ix[l][2]=n[ix[l][0]],n[ix[l][0]]=ix[l][1];e=!0;break}}if(t.apply(this,arguments),e)for(var l=0;l<ix.length;l++)n[ix[l][0]]=ix[l][2]}:t},rx=ea.extend({type:"sector",shape:{cx:0,cy:0,r0:0,r:0,startAngle:0,endAngle:2*Math.PI,clockwise:!0},brush:nx(ea.prototype.brush),buildPath:function(t,e){var i=e.cx,n=e.cy,r=Math.max(e.r0||0,0),a=Math.max(e.r,0),o=e.startAngle,s=e.endAngle,l=e.clockwise,h=Math.cos(o),u=Math.sin(o);t.moveTo(h*r+i,u*r+n),t.lineTo(h*a+i,u*a+n),t.arc(i,n,a,o,s,!l),t.lineTo(Math.cos(s)*r+i,Math.sin(s)*r+n),0!==r&&t.arc(i,n,r,s,o,l),t.closePath()}}),ax=ea.extend({type:"ring",shape:{cx:0,cy:0,r:0,r0:0},buildPath:function(t,e){var i=e.cx,n=e.cy,r=2*Math.PI;t.moveTo(i+e.r,n),t.arc(i,n,e.r,0,r,!1),t.moveTo(i+e.r0,n),t.arc(i,n,e.r0,0,r,!0)}}),ox=function(t,e){for(var i=t.length,n=[],r=0,a=1;i>a;a++)r+=ee(t[a-1],t[a]);var o=r/2;o=i>o?i:o;for(var a=0;o>a;a++){var s,l,h,u=a/(o-1)*(e?i:i-1),c=Math.floor(u),d=u-c,f=t[c%i];e?(s=t[(c-1+i)%i],l=t[(c+1)%i],h=t[(c+2)%i]):(s=t[0===c?c:c-1],l=t[c>i-2?i-1:c+1],h=t[c>i-3?i-1:c+2]);var p=d*d,g=d*p;n.push([la(s[0],f[0],l[0],h[0],d,p,g),la(s[1],f[1],l[1],h[1],d,p,g)])}return n},sx=function(t,e,i,n){var r,a,o,s,l=[],h=[],u=[],c=[];if(n){o=[1/0,1/0],s=[-1/0,-1/0];for(var d=0,f=t.length;f>d;d++)oe(o,o,t[d]),se(s,s,t[d]);oe(o,o,n[0]),se(s,s,n[1])}for(var d=0,f=t.length;f>d;d++){var p=t[d];if(i)r=t[d?d-1:f-1],a=t[(d+1)%f];else{if(0===d||d===f-1){l.push(G(t[d]));continue}r=t[d-1],a=t[d+1]}U(h,a,r),J(h,h,e);var g=ee(p,r),v=ee(p,a),m=g+v;0!==m&&(g/=m,v/=m),J(u,h,-g),J(c,h,v);var y=X([],p,u),x=X([],p,c);n&&(se(y,y,o),oe(y,y,s),se(x,x,o),oe(x,x,s)),l.push(y),l.push(x)}return i&&l.push(l.shift()),l},lx=ea.extend({type:"polygon",shape:{points:null,smooth:!1,smoothConstraint:null},buildPath:function(t,e){ha(t,e,!0)}}),hx=ea.extend({type:"polyline",shape:{points:null,smooth:!1,smoothConstraint:null},style:{stroke:"#000",fill:null},buildPath:function(t,e){ha(t,e,!1)}}),ux=Math.round,cx={},dx=ea.extend({type:"rect",shape:{r:0,x:0,y:0,width:0,height:0},buildPath:function(t,e){var i,n,r,a;this.subPixelOptimize?(ca(cx,e,this.style),i=cx.x,n=cx.y,r=cx.width,a=cx.height,cx.r=e.r,e=cx):(i=e.x,n=e.y,r=e.width,a=e.height),e.r?ln(t,e):t.rect(i,n,r,a),t.closePath()}}),fx={},px=ea.extend({type:"line",shape:{x1:0,y1:0,x2:0,y2:0,percent:1},style:{stroke:"#000",fill:null},buildPath:function(t,e){var i,n,r,a;this.subPixelOptimize?(ua(fx,e,this.style),i=fx.x1,n=fx.y1,r=fx.x2,a=fx.y2):(i=e.x1,n=e.y1,r=e.x2,a=e.y2);var o=e.percent;0!==o&&(t.moveTo(i,n),1>o&&(r=i*(1-o)+r*o,a=n*(1-o)+a*o),t.lineTo(r,a))},pointAt:function(t){var e=this.shape;return[e.x1*(1-t)+e.x2*t,e.y1*(1-t)+e.y2*t]}}),gx=[],vx=ea.extend({type:"bezier-curve",shape:{x1:0,y1:0,x2:0,y2:0,cpx1:0,cpy1:0,percent:1},style:{stroke:"#000",fill:null},buildPath:function(t,e){var i=e.x1,n=e.y1,r=e.x2,a=e.y2,o=e.cpx1,s=e.cpy1,l=e.cpx2,h=e.cpy2,u=e.percent;0!==u&&(t.moveTo(i,n),null==l||null==h?(1>u&&(Er(i,o,r,u,gx),o=gx[1],r=gx[2],Er(n,s,a,u,gx),s=gx[1],a=gx[2]),t.quadraticCurveTo(o,s,r,a)):(1>u&&(Cr(i,o,l,r,u,gx),o=gx[1],l=gx[2],r=gx[3],Cr(n,s,h,a,u,gx),s=gx[1],h=gx[2],a=gx[3]),t.bezierCurveTo(o,s,l,h,r,a)))},pointAt:function(t){return fa(this.shape,t,!1)},tangentAt:function(t){var e=fa(this.shape,t,!0);return te(e,e)}}),mx=ea.extend({type:"arc",shape:{cx:0,cy:0,r:0,startAngle:0,endAngle:2*Math.PI,clockwise:!0},style:{stroke:"#000",fill:null},buildPath:function(t,e){var i=e.cx,n=e.cy,r=Math.max(e.r,0),a=e.startAngle,o=e.endAngle,s=e.clockwise,l=Math.cos(a),h=Math.sin(a);t.moveTo(l*r+i,h*r+n),t.arc(i,n,r,a,o,!s)}}),yx=ea.extend({type:"compound",shape:{paths:null},_updatePathDirty:function(){for(var t=this.__dirtyPath,e=this.shape.paths,i=0;i<e.length;i++)t=t||e[i].__dirtyPath;this.__dirtyPath=t,this.__dirty=this.__dirty||t},beforeBrush:function(){this._updatePathDirty();for(var t=this.shape.paths||[],e=this.getGlobalScale(),i=0;i<t.length;i++)t[i].path||t[i].createPathProxy(),t[i].path.setScale(e[0],e[1],t[i].segmentIgnoreThreshold)},buildPath:function(t,e){for(var i=e.paths||[],n=0;n<i.length;n++)i[n].buildPath(t,i[n].shape,!0)},afterBrush:function(){for(var t=this.shape.paths||[],e=0;e<t.length;e++)t[e].__dirtyPath=!1},getBoundingRect:function(){return this._updatePathDirty(),ea.prototype.getBoundingRect.call(this)}}),xx=function(t){this.colorStops=t||[]};xx.prototype={constructor:xx,addColorStop:function(t,e){this.colorStops.push({offset:t,color:e})}};var _x=function(t,e,i,n,r,a){this.x=null==t?0:t,this.y=null==e?0:e,this.x2=null==i?1:i,this.y2=null==n?0:n,this.type="linear",this.global=a||!1,xx.call(this,r)};_x.prototype={constructor:_x},u(_x,xx);var bx=function(t,e,i,n,r){this.x=null==t?.5:t,this.y=null==e?.5:e,this.r=null==i?.5:i,this.type="radial",this.global=r||!1,xx.call(this,n)};bx.prototype={constructor:bx},u(bx,xx),pa.prototype.incremental=!0,pa.prototype.clearDisplaybles=function(){this._displayables=[],this._temporaryDisplayables=[],this._cursor=0,this.dirty(),this.notClear=!1},pa.prototype.addDisplayable=function(t,e){e?this._temporaryDisplayables.push(t):this._displayables.push(t),this.dirty()},pa.prototype.addDisplayables=function(t,e){e=e||!1;for(var i=0;i<t.length;i++)this.addDisplayable(t[i],e)},pa.prototype.eachPendingDisplayable=function(t){for(var e=this._cursor;e<this._displayables.length;e++)t&&t(this._displayables[e]);for(var e=0;e<this._temporaryDisplayables.length;e++)t&&t(this._temporaryDisplayables[e])},pa.prototype.update=function(){this.updateTransform();for(var t=this._cursor;t<this._displayables.length;t++){var e=this._displayables[t];e.parent=this,e.update(),e.parent=null}for(var t=0;t<this._temporaryDisplayables.length;t++){var e=this._temporaryDisplayables[t];e.parent=this,e.update(),e.parent=null}},pa.prototype.brush=function(t){for(var e=this._cursor;e<this._displayables.length;e++){var i=this._displayables[e];i.beforeBrush&&i.beforeBrush(t),i.brush(t,e===this._cursor?null:this._displayables[e-1]),i.afterBrush&&i.afterBrush(t)}this._cursor=e;for(var e=0;e<this._temporaryDisplayables.length;e++){var i=this._temporaryDisplayables[e];i.beforeBrush&&i.beforeBrush(t),i.brush(t,0===e?null:this._temporaryDisplayables[e-1]),i.afterBrush&&i.afterBrush(t)}this._temporaryDisplayables=[],this.notClear=!0};var Mx=[];pa.prototype.getBoundingRect=function(){if(!this._rect){for(var t=new Ii(1/0,1/0,-1/0,-1/0),e=0;e<this._displayables.length;e++){var i=this._displayables[e],n=i.getBoundingRect().clone();i.needLocalTransform()&&n.applyTransform(i.getLocalTransform(Mx)),t.union(n)}this._rect=t}return this._rect},pa.prototype.contain=function(t,e){var i=this.transformCoordToLocal(t,e),n=this.getBoundingRect();if(n.contain(i[0],i[1]))for(var r=0;r<this._displayables.length;r++){var a=this._displayables[r];if(a.contain(t,e))return!0}return!1},u(pa,Tn);var Sx=Math.max,Ax=Math.min,Ix={},Tx=1,Cx={color:"textFill",textBorderColor:"textStroke",textBorderWidth:"textStrokeWidth"},Dx="emphasis",kx="normal",Px=1,Lx={},Ox={},Ex=sa,zx=da,Bx=N(),Rx=0;ma("circle",ex),ma("sector",rx),ma("ring",ax),ma("polygon",lx),ma("polyline",hx),ma("rect",dx),ma("line",px),ma("bezierCurve",vx),ma("arc",mx);var Nx=(Object.freeze||Object)({Z2_EMPHASIS_LIFT:Tx,CACHED_LABEL_STYLE_PROPERTIES:Cx,extendShape:ga,extendPath:va,registerShape:ma,getShapeClass:ya,makePath:xa,makeImage:_a,mergePath:Ex,resizePath:ba,subPixelOptimizeLine:Ma,subPixelOptimizeRect:Sa,subPixelOptimize:zx,setElementHoverStyle:La,setHoverStyle:Na,setAsHighDownDispatcher:Fa,isHighDownDispatcher:Va,getHighlightDigit:Wa,setLabelStyle:Ha,modifyLabelStyle:Ga,setTextStyle:Za,setText:Xa,getFont:Qa,updateProps:to,initProps:eo,getTransform:io,applyTransform:no,transformDirection:ro,groupTransition:ao,clipPointsByRect:oo,clipRectByRect:so,createIcon:lo,linePolygonIntersect:ho,lineLineIntersect:uo,Group:Fv,Image:Cn,Text:tx,Circle:ex,Sector:rx,Ring:ax,Polygon:lx,Polyline:hx,Rect:dx,Line:px,BezierCurve:vx,Arc:mx,IncrementalDisplayable:pa,CompoundPath:yx,LinearGradient:_x,RadialGradient:bx,BoundingRect:Ii}),Fx=["textStyle","color"],Vx={getTextColor:function(t){var e=this.ecModel;return this.getShallow("color")||(!t&&e?e.get(Fx):null)},getFont:function(){return Qa({fontStyle:this.getShallow("fontStyle"),fontWeight:this.getShallow("fontWeight"),fontSize:this.getShallow("fontSize"),fontFamily:this.getShallow("fontFamily")},this.ecModel)},getTextRect:function(t){return Xi(t,this.getFont(),this.getShallow("align"),this.getShallow("verticalAlign")||this.getShallow("baseline"),this.getShallow("padding"),this.getShallow("lineHeight"),this.getShallow("rich"),this.getShallow("truncateText"))}},Wx=Um([["fill","color"],["stroke","borderColor"],["lineWidth","borderWidth"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"],["textPosition"],["textAlign"]]),Hx={getItemStyle:function(t,e){var i=Wx(this,t,e),n=this.getBorderLineDash();return n&&(i.lineDash=n),i},getBorderLineDash:function(){var t=this.get("borderType");return"solid"===t||null==t?null:"dashed"===t?[5,5]:[1,1]}},Gx=c,Zx=hr();po.prototype={constructor:po,init:null,mergeOption:function(t){r(this.option,t,!0)},get:function(t,e){return null==t?this.option:go(this.option,this.parsePath(t),!e&&vo(this,t))},getShallow:function(t,e){var i=this.option,n=null==i?i:i[t],r=!e&&vo(this,t);return null==n&&r&&(n=r.getShallow(t)),n},getModel:function(t,e){var i,n=null==t?this.option:go(this.option,t=this.parsePath(t));return e=e||(i=vo(this,t))&&i.getModel(t),new po(n,e,this.ecModel)},isEmpty:function(){return null==this.option},restoreData:function(){},clone:function(){var t=this.constructor;return new t(n(this.option))},setReadOnly:function(){},parsePath:function(t){return"string"==typeof t&&(t=t.split(".")),t},customizeGetParent:function(t){Zx(this).getParent=t},isAnimationEnabled:function(){if(!Ag.node){if(null!=this.option.animation)return!!this.option.animation;if(this.parentModel)return this.parentModel.isAnimationEnabled()}}},mr(po),yr(po),Gx(po,jm),Gx(po,$m),Gx(po,Vx),Gx(po,Hx);var Xx=0,Yx=1e-4,Ux=9007199254740991,qx=/^(?:(\d{4})(?:[-\/](\d{1,2})(?:[-\/](\d{1,2})(?:[T ](\d{1,2})(?::(\d\d)(?::(\d\d)(?:[.,](\d+))?)?)?(Z|[\+\-]\d\d:?\d\d)?)?)?)?)?$/,jx=(Object.freeze||Object)({linearMap:wo,parsePercent:bo,round:Mo,asc:So,getPrecision:Ao,getPrecisionSafe:Io,getPixelPrecision:To,getPercentWithPrecision:Co,MAX_SAFE_INTEGER:Ux,remRadian:Do,isRadianAroundZero:ko,parseDate:Po,quantity:Lo,quantityExponent:Oo,nice:Eo,quantile:zo,reformIntervals:Bo,isNumeric:Ro}),Kx=L,$x=/([&<>"'])/g,Qx={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},Jx=["a","b","c","d","e","f","g"],t_=function(t,e){return"{"+t+(null==e?"":e)+"}"},e_=$i,i_=(Object.freeze||Object)({addCommas:No,toCamelCase:Fo,normalizeCssArray:Kx,encodeHTML:Vo,formatTpl:Wo,formatTplSimple:Ho,getTooltipMarker:Go,formatTime:Xo,capitalFirst:Yo,truncateText:e_,getTextBoundingRect:Uo,getTextRect:qo,windowOpen:jo}),n_=f,r_=["left","right","top","bottom","width","height"],a_=[["width","left","right"],["height","top","bottom"]],o_=(x(Ko,"vertical"),x(Ko,"horizontal"),{getBoxLayoutParams:function(){return{left:this.get("left"),top:this.get("top"),right:this.get("right"),bottom:this.get("bottom"),width:this.get("width"),height:this.get("height")}}}),s_=hr(),l_=po.extend({type:"component",id:"",name:"",mainType:"",subType:"",componentIndex:0,defaultOption:null,ecModel:null,dependentModels:[],uid:null,layoutMode:null,$constructor:function(t,e,i,n){po.call(this,t,e,i,n),this.uid=mo("ec_cpt_model")},init:function(t,e,i){this.mergeDefaultAndTheme(t,i)},mergeDefaultAndTheme:function(t,e){var i=this.layoutMode,n=i?Jo(t):{},a=e.getTheme();r(t,a.get(this.mainType)),r(t,this.getDefaultOption()),i&&Qo(t,n,i)},mergeOption:function(t){r(this.option,t,!0);var e=this.layoutMode;e&&Qo(this.option,t,e)},optionUpdated:function(){},getDefaultOption:function(){var t=s_(this);if(!t.defaultOption){for(var e=[],i=this.constructor;i;){var n=i.prototype.defaultOption;n&&e.push(n),i=i.superClass}for(var a={},o=e.length-1;o>=0;o--)a=r(a,e[o],!0);t.defaultOption=a}return t.defaultOption},getReferringComponents:function(t){return this.ecModel.queryComponents({mainType:t,index:this.get(t+"Index",!0),id:this.get(t+"Id",!0)})}});wr(l_,{registerWhenExtend:!0}),yo(l_),xo(l_,es),c(l_,o_);var h_="";"undefined"!=typeof navigator&&(h_=navigator.platform||"");var u_={color:["#c23531","#2f4554","#61a0a8","#d48265","#91c7ae","#749f83","#ca8622","#bda29a","#6e7074","#546570","#c4ccd3"],gradientColor:["#f6efa6","#d88273","#bf444c"],textStyle:{fontFamily:h_.match(/^Win/)?"Microsoft YaHei":"sans-serif",fontSize:12,fontStyle:"normal",fontWeight:"normal"},blendMode:null,animation:"auto",animationDuration:1e3,animationDurationUpdate:300,animationEasing:"exponentialOut",animationEasingUpdate:"cubicOut",animationThreshold:2e3,progressiveThreshold:3e3,progressive:400,hoverLayerThreshold:3e3,useUTC:!1},c_=hr(),d_={clearColorPalette:function(){c_(this).colorIdx=0,c_(this).colorNameMap={}},getColorFromPalette:function(t,e,i){e=e||this;var n=c_(e),r=n.colorIdx||0,a=n.colorNameMap=n.colorNameMap||{};if(a.hasOwnProperty(t))return a[t];var o=tr(this.get("color",!0)),s=this.get("colorLayer",!0),l=null!=i&&s?is(s,i):o;if(l=l||o,l&&l.length){var h=l[r];return t&&(a[t]=h),n.colorIdx=(r+1)%l.length,h}}},f_="original",p_="arrayRows",g_="objectRows",v_="keyedColumns",m_="unknown",y_="typedArray",x_="column",__="row";ns.seriesDataToSource=function(t){return new ns({data:t,sourceFormat:A(t)?y_:f_,fromDataset:!1})},yr(ns);var w_={Must:1,Might:2,Not:3},b_=hr(),M_="\x00_ec_inner",S_=po.extend({init:function(t,e,i,n){i=i||{},this.option=null,this._theme=new po(i),this._optionManager=n},setOption:function(t,e){O(!(M_ in t),"please use chart.getOption()"),this._optionManager.setOption(t,e),this.resetOption(null)},resetOption:function(t){var e=!1,i=this._optionManager;if(!t||"recreate"===t){var n=i.mountOption("recreate"===t);this.option&&"recreate"!==t?(this.restoreData(),this.mergeOption(n)):xs.call(this,n),e=!0}if(("timeline"===t||"media"===t)&&this.restoreData(),!t||"recreate"===t||"timeline"===t){var r=i.getTimelineOption(this);r&&(this.mergeOption(r),e=!0)}if(!t||"recreate"===t||"media"===t){var a=i.getMediaOption(this,this._api);a.length&&f(a,function(t){this.mergeOption(t,e=!0)},this)}return e},mergeOption:function(t){function e(e,n){var r=tr(t[e]),s=rr(a.get(e),r);ar(s),f(s,function(t){var i=t.option;M(i)&&(t.keyInfo.mainType=e,t.keyInfo.subType=ws(e,i,t.exist))});var l=_s(a,n);i[e]=[],a.set(e,[]),f(s,function(t,n){var r=t.exist,s=t.option;if(O(M(s)||r,"Empty component definition"),s){var h=l_.getClass(e,t.keyInfo.subType,!0);if(r&&r.constructor===h)r.name=t.keyInfo.name,r.mergeOption(s,this),r.optionUpdated(s,!1);else{var u=o({dependentModels:l,componentIndex:n},t.keyInfo);r=new h(s,this,this,u),o(r,u),r.init(s,this,this,u),r.optionUpdated(null,!0)}}else r.mergeOption({},this),r.optionUpdated({},!1);a.get(e)[n]=r,i[e][n]=r.option},this),"series"===e&&bs(this,a.get("series"))}var i=this.option,a=this._componentsMap,s=[];os(this),f(t,function(t,e){null!=t&&(l_.hasClass(e)?e&&s.push(e):i[e]=null==i[e]?n(t):r(i[e],t,!0))}),l_.topologicalTravel(s,l_.getAllClassMainTypes(),e,this),this._seriesIndicesMap=N(this._seriesIndices=this._seriesIndices||[])},getOption:function(){var t=n(this.option);return f(t,function(e,i){if(l_.hasClass(i)){for(var e=tr(e),n=e.length-1;n>=0;n--)sr(e[n])&&e.splice(n,1);t[i]=e}}),delete t[M_],t},getTheme:function(){return this._theme},getComponent:function(t,e){var i=this._componentsMap.get(t);return i?i[e||0]:void 0},queryComponents:function(t){var e=t.mainType;if(!e)return[];var i=t.index,n=t.id,r=t.name,a=this._componentsMap.get(e);if(!a||!a.length)return[];var o;if(null!=i)_(i)||(i=[i]),o=v(p(i,function(t){return a[t]}),function(t){return!!t});else if(null!=n){var s=_(n);o=v(a,function(t){return s&&h(n,t.id)>=0||!s&&t.id===n})}else if(null!=r){var l=_(r);o=v(a,function(t){return l&&h(r,t.name)>=0||!l&&t.name===r})}else o=a.slice();return Ms(o,t)},findComponents:function(t){function e(t){var e=r+"Index",i=r+"Id",n=r+"Name";return!t||null==t[e]&&null==t[i]&&null==t[n]?null:{mainType:r,index:t[e],id:t[i],name:t[n]}}function i(e){return t.filter?v(e,t.filter):e}var n=t.query,r=t.mainType,a=e(n),o=a?this.queryComponents(a):this._componentsMap.get(r);return i(Ms(o,t))},eachComponent:function(t,e,i){var n=this._componentsMap;if("function"==typeof t)i=e,e=t,n.each(function(t,n){f(t,function(t,r){e.call(i,n,t,r)})});else if(b(t))f(n.get(t),e,i);else if(M(t)){var r=this.findComponents(t);f(r,e,i)}},getSeriesByName:function(t){var e=this._componentsMap.get("series");return v(e,function(e){return e.name===t})},getSeriesByIndex:function(t){return this._componentsMap.get("series")[t]},getSeriesByType:function(t){var e=this._componentsMap.get("series");return v(e,function(e){return e.subType===t})},getSeries:function(){return this._componentsMap.get("series").slice()},getSeriesCount:function(){return this._componentsMap.get("series").length},eachSeries:function(t,e){Ss(this),f(this._seriesIndices,function(i){var n=this._componentsMap.get("series")[i];t.call(e,n,i)},this)},eachRawSeries:function(t,e){f(this._componentsMap.get("series"),t,e)},eachSeriesByType:function(t,e,i){Ss(this),f(this._seriesIndices,function(n){var r=this._componentsMap.get("series")[n];r.subType===t&&e.call(i,r,n)},this)},eachRawSeriesByType:function(t,e,i){return f(this.getSeriesByType(t),e,i)},isSeriesFiltered:function(t){return Ss(this),null==this._seriesIndicesMap.get(t.componentIndex)},getCurrentSeriesIndices:function(){return(this._seriesIndices||[]).slice()},filterSeries:function(t,e){Ss(this);var i=v(this._componentsMap.get("series"),t,e);bs(this,i)},restoreData:function(t){var e=this._componentsMap;bs(this,e.get("series"));var i=[];e.each(function(t,e){i.push(e)}),l_.topologicalTravel(i,l_.getAllClassMainTypes(),function(i){f(e.get(i),function(e){("series"!==i||!ms(e,t))&&e.restoreData()})})}});c(S_,d_);var A_=["getDom","getZr","getWidth","getHeight","getDevicePixelRatio","dispatchAction","isDisposed","on","off","getDataURL","getConnectedDataURL","getModel","getOption","getViewOfComponentModel","getViewOfSeriesModel"],I_={};Is.prototype={constructor:Is,create:function(t,e){var i=[];f(I_,function(n){var r=n.create(t,e);i=i.concat(r||[])}),this._coordinateSystems=i},update:function(t,e){f(this._coordinateSystems,function(i){i.update&&i.update(t,e)})},getCoordinateSystems:function(){return this._coordinateSystems.slice()}},Is.register=function(t,e){I_[t]=e},Is.get=function(t){return I_[t]};var T_=f,C_=n,D_=p,k_=r,P_=/^(min|max)?(.+)$/;Ts.prototype={constructor:Ts,setOption:function(t,e){t&&f(tr(t.series),function(t){t&&t.data&&A(t.data)&&z(t.data)}),t=C_(t);var i=this._optionBackup,n=Cs.call(this,t,e,!i);this._newBaseOption=n.baseOption,i?(Ls(i.baseOption,n.baseOption),n.timelineOptions.length&&(i.timelineOptions=n.timelineOptions),n.mediaList.length&&(i.mediaList=n.mediaList),n.mediaDefault&&(i.mediaDefault=n.mediaDefault)):this._optionBackup=n},mountOption:function(t){var e=this._optionBackup;return this._timelineOptions=D_(e.timelineOptions,C_),this._mediaList=D_(e.mediaList,C_),this._mediaDefault=C_(e.mediaDefault),this._currentMediaIndices=[],C_(t?e.baseOption:this._newBaseOption)},getTimelineOption:function(t){var e,i=this._timelineOptions;if(i.length){var n=t.getComponent("timeline");n&&(e=C_(i[n.getCurrentIndex()],!0))}return e},getMediaOption:function(){var t=this._api.getWidth(),e=this._api.getHeight(),i=this._mediaList,n=this._mediaDefault,r=[],a=[];if(!i.length&&!n)return a;for(var o=0,s=i.length;s>o;o++)Ds(i[o].query,t,e)&&r.push(o);return!r.length&&n&&(r=[-1]),r.length&&!Ps(r,this._currentMediaIndices)&&(a=D_(r,function(t){return C_(-1===t?n.option:i[t].option)})),this._currentMediaIndices=r,a}};var L_=f,O_=M,E_=["areaStyle","lineStyle","nodeStyle","linkStyle","chordStyle","label","labelLine"],z_=function(t,e){L_(Fs(t.series),function(t){O_(t)&&Ns(t)});var i=["xAxis","yAxis","radiusAxis","angleAxis","singleAxis","parallelAxis","radar"];e&&i.push("valueAxis","categoryAxis","logAxis","timeAxis"),L_(i,function(e){L_(Fs(t[e]),function(t){t&&(Bs(t,"axisLabel"),Bs(t.axisPointer,"label"))})}),L_(Fs(t.parallel),function(t){var e=t&&t.parallelAxisDefault;Bs(e,"axisLabel"),Bs(e&&e.axisPointer,"label")}),L_(Fs(t.calendar),function(t){Es(t,"itemStyle"),Bs(t,"dayLabel"),Bs(t,"monthLabel"),Bs(t,"yearLabel")}),L_(Fs(t.radar),function(t){Bs(t,"name")}),L_(Fs(t.geo),function(t){O_(t)&&(Rs(t),L_(Fs(t.regions),function(t){Rs(t)}))}),L_(Fs(t.timeline),function(t){Rs(t),Es(t,"label"),Es(t,"itemStyle"),Es(t,"controlStyle",!0);var e=t.data;_(e)&&f(e,function(t){M(t)&&(Es(t,"label"),Es(t,"itemStyle"))})}),L_(Fs(t.toolbox),function(t){Es(t,"iconStyle"),L_(t.feature,function(t){Es(t,"iconStyle")})}),Bs(Vs(t.axisPointer),"label"),Bs(Vs(t.tooltip).axisPointer,"label")},B_=[["x","left"],["y","top"],["x2","right"],["y2","bottom"]],R_=["grid","geo","parallel","legend","toolbox","title","visualMap","dataZoom","timeline"],N_=function(t,e){z_(t,e),t.series=tr(t.series),f(t.series,function(t){if(M(t)){var e=t.type;if("line"===e)null!=t.clipOverflow&&(t.clip=t.clipOverflow);else if("pie"===e||"gauge"===e)null!=t.clockWise&&(t.clockwise=t.clockWise);else if("gauge"===e){var i=Ws(t,"pointer.color");null!=i&&Hs(t,"itemStyle.color",i)}Gs(t)}}),t.dataRange&&(t.visualMap=t.dataRange),f(R_,function(e){var i=t[e];i&&(_(i)||(i=[i]),f(i,function(t){Gs(t)}))})},F_=function(t){var e=N();t.eachSeries(function(t){var i=t.get("stack");if(i){var n=e.get(i)||e.set(i,[]),r=t.getData(),a={stackResultDimension:r.getCalculationInfo("stackResultDimension"),stackedOverDimension:r.getCalculationInfo("stackedOverDimension"),stackedDimension:r.getCalculationInfo("stackedDimension"),stackedByDimension:r.getCalculationInfo("stackedByDimension"),isStackedByIndex:r.getCalculationInfo("isStackedByIndex"),data:r,seriesModel:t};
if(!a.stackedDimension||!a.isStackedByIndex&&!a.stackedByDimension)return;n.length&&r.setCalculationInfo("stackedOnSeries",n[n.length-1].seriesModel),n.push(a)}}),e.each(Zs)},V_=Xs.prototype;V_.pure=!1,V_.persistent=!0,V_.getSource=function(){return this._source};var W_={arrayRows_column:{pure:!0,count:function(){return Math.max(0,this._data.length-this._source.startIndex)},getItem:function(t){return this._data[t+this._source.startIndex]},appendData:qs},arrayRows_row:{pure:!0,count:function(){var t=this._data[0];return t?Math.max(0,t.length-this._source.startIndex):0},getItem:function(t){t+=this._source.startIndex;for(var e=[],i=this._data,n=0;n<i.length;n++){var r=i[n];e.push(r?r[t]:null)}return e},appendData:function(){throw new Error('Do not support appendData when set seriesLayoutBy: "row".')}},objectRows:{pure:!0,count:Ys,getItem:Us,appendData:qs},keyedColumns:{pure:!0,count:function(){var t=this._source.dimensionsDefine[0].name,e=this._data[t];return e?e.length:0},getItem:function(t){for(var e=[],i=this._source.dimensionsDefine,n=0;n<i.length;n++){var r=this._data[i[n].name];e.push(r?r[t]:null)}return e},appendData:function(t){var e=this._data;f(t,function(t,i){for(var n=e[i]||(e[i]=[]),r=0;r<(t||[]).length;r++)n.push(t[r])})}},original:{count:Ys,getItem:Us,appendData:qs},typedArray:{persistent:!1,pure:!0,count:function(){return this._data?this._data.length/this._dimSize:0},getItem:function(t,e){t-=this._offset,e=e||[];for(var i=this._dimSize*t,n=0;n<this._dimSize;n++)e[n]=this._data[i+n];return e},appendData:function(t){wg&&O(A(t),"Added data must be TypedArray if data in initialization is TypedArray"),this._data=t},clean:function(){this._offset+=this.count(),this._data=null}}},H_={arrayRows:js,objectRows:function(t,e,i,n){return null!=i?t[n]:t},keyedColumns:js,original:function(t,e,i){var n=ir(t);return null!=i&&n instanceof Array?n[i]:n},typedArray:js},G_={arrayRows:Ks,objectRows:function(t,e){return $s(t[e],this._dimensionInfos[e])},keyedColumns:Ks,original:function(t,e,i,n){var r=t&&(null==t.value?t:t.value);return!this._rawData.pure&&nr(t)&&(this.hasItemOption=!0),$s(r instanceof Array?r[n]:r,this._dimensionInfos[e])},typedArray:function(t,e,i,n){return t[n]}},Z_=/\{@(.+?)\}/g,X_={getDataParams:function(t,e){var i=this.getData(e),n=this.getRawValue(t,e),r=i.getRawIndex(t),a=i.getName(t),o=i.getRawDataItem(t),s=i.getItemVisual(t,"color"),l=i.getItemVisual(t,"borderColor"),h=this.ecModel.getComponent("tooltip"),u=h&&h.get("renderMode"),c=pr(u),d=this.mainType,f="series"===d,p=i.userOutput;return{componentType:d,componentSubType:this.subType,componentIndex:this.componentIndex,seriesType:f?this.subType:null,seriesIndex:this.seriesIndex,seriesId:f?this.id:null,seriesName:f?this.name:null,name:a,dataIndex:r,data:o,dataType:e,value:n,color:s,borderColor:l,dimensionNames:p?p.dimensionNames:null,encode:p?p.encode:null,marker:Go({color:s,renderMode:c}),$vars:["seriesName","name","value"]}},getFormattedLabel:function(t,e,i,n,r){e=e||"normal";var a=this.getData(i),o=a.getItemModel(t),s=this.getDataParams(t,i);null!=n&&s.value instanceof Array&&(s.value=s.value[n]);var l=o.get("normal"===e?[r||"label","formatter"]:[e,r||"label","formatter"]);if("function"==typeof l)return s.status=e,s.dimensionIndex=n,l(s);if("string"==typeof l){var h=Wo(l,s);return h.replace(Z_,function(e,i){var n=i.length;return"["===i.charAt(0)&&"]"===i.charAt(n-1)&&(i=+i.slice(1,n-1)),Qs(a,t,i)})}},getRawValue:function(t,e){return Qs(this.getData(e),t)},formatTooltip:function(){}},Y_=el.prototype;Y_.perform=function(t){function e(t){return!(t>=1)&&(t=1),t}var i=this._upstream,n=t&&t.skip;if(this._dirty&&i){var r=this.context;r.data=r.outputData=i.context.outputData}this.__pipeline&&(this.__pipeline.currentTask=this);var a;this._plan&&!n&&(a=this._plan(this.context));var o=e(this._modBy),s=this._modDataCount||0,l=e(t&&t.modBy),h=t&&t.modDataCount||0;(o!==l||s!==h)&&(a="reset");var u;(this._dirty||"reset"===a)&&(this._dirty=!1,u=nl(this,n)),this._modBy=l,this._modDataCount=h;var c=t&&t.step;if(i?(wg&&O(null!=i._outputDueEnd),this._dueEnd=i._outputDueEnd):(wg&&O(!this._progress||this._count),this._dueEnd=this._count?this._count(this.context):1/0),this._progress){var d=this._dueIndex,f=Math.min(null!=c?this._dueIndex+c:1/0,this._dueEnd);if(!n&&(u||f>d)){var p=this._progress;if(_(p))for(var g=0;g<p.length;g++)il(this,p[g],d,f,l,h);else il(this,p,d,f,l,h)}this._dueIndex=f;var v=null!=this._settedOutputEnd?this._settedOutputEnd:f;wg&&O(v>=this._outputDueEnd),this._outputDueEnd=v}else this._dueIndex=this._outputDueEnd=null!=this._settedOutputEnd?this._settedOutputEnd:this._dueEnd;return this.unfinished()};var U_=function(){function t(){return i>n?n++:null}function e(){var t=n%o*r+Math.ceil(n/o),e=n>=i?null:a>t?t:n;return n++,e}var i,n,r,a,o,s={reset:function(l,h,u,c){n=l,i=h,r=u,a=c,o=Math.ceil(a/r),s.next=r>1&&a>0?e:t}};return s}();Y_.dirty=function(){this._dirty=!0,this._onDirty&&this._onDirty(this.context)},Y_.unfinished=function(){return this._progress&&this._dueIndex<this._dueEnd},Y_.pipe=function(t){wg&&O(t&&!t._disposed&&t!==this),(this._downstream!==t||this._dirty)&&(this._downstream=t,t._upstream=this,t.dirty())},Y_.dispose=function(){this._disposed||(this._upstream&&(this._upstream._downstream=null),this._downstream&&(this._downstream._upstream=null),this._dirty=!1,this._disposed=!0)},Y_.getUpstream=function(){return this._upstream},Y_.getDownstream=function(){return this._downstream},Y_.setOutputEnd=function(t){this._outputDueEnd=this._settedOutputEnd=t};var q_=hr(),j_=l_.extend({type:"series.__base__",seriesIndex:0,coordinateSystem:null,defaultOption:null,legendVisualProvider:null,visualColorAccessPath:"itemStyle.color",visualBorderColorAccessPath:"itemStyle.borderColor",layoutMode:null,init:function(t,e,i){this.seriesIndex=this.componentIndex,this.dataTask=tl({count:ol,reset:sl}),this.dataTask.context={model:this},this.mergeDefaultAndTheme(t,i),ss(this);var n=this.getInitialData(t,i);hl(n,this),this.dataTask.context.data=n,wg&&O(n,"getInitialData returned invalid data."),q_(this).dataBeforeProcessed=n,rl(this)},mergeDefaultAndTheme:function(t,e){var i=this.layoutMode,n=i?Jo(t):{},a=this.subType;l_.hasClass(a)&&(a+="Series"),r(t,e.getTheme().get(this.subType)),r(t,this.getDefaultOption()),er(t,"label",["show"]),this.fillDataTextStyle(t.data),i&&Qo(t,n,i)},mergeOption:function(t,e){t=r(this.option,t,!0),this.fillDataTextStyle(t.data);var i=this.layoutMode;i&&Qo(this.option,t,i),ss(this);var n=this.getInitialData(t,e);hl(n,this),this.dataTask.dirty(),this.dataTask.context.data=n,q_(this).dataBeforeProcessed=n,rl(this)},fillDataTextStyle:function(t){if(t&&!A(t))for(var e=["show"],i=0;i<t.length;i++)t[i]&&t[i].label&&er(t[i],"label",e)},getInitialData:function(){},appendData:function(t){var e=this.getRawData();e.appendData(t.data)},getData:function(t){var e=cl(this);if(e){var i=e.context.data;return null==t?i:i.getLinkedData(t)}return q_(this).data},setData:function(t){var e=cl(this);if(e){var i=e.context;i.data!==t&&e.modifyOutputEnd&&e.setOutputEnd(t.count()),i.outputData=t,e!==this.dataTask&&(i.data=t)}q_(this).data=t},getSource:function(){return as(this)},getRawData:function(){return q_(this).dataBeforeProcessed},getBaseAxis:function(){var t=this.coordinateSystem;return t&&t.getBaseAxis&&t.getBaseAxis()},formatTooltip:function(t,e,i,n){function r(i){function r(t,i){var r=c.getDimensionInfo(i);if(r&&r.otherDims.tooltip!==!1){var d=r.type,f="sub"+o.seriesIndex+"at"+u,p=Go({color:y,type:"subItem",renderMode:n,markerId:f}),g="string"==typeof p?p:p.content,v=(a?g+Vo(r.displayName||"-")+": ":"")+Vo("ordinal"===d?t+"":"time"===d?e?"":Xo("yyyy/MM/dd hh:mm:ss",t):No(t));v&&s.push(v),l&&(h[f]=y,++u)}}var a=g(i,function(t,e,i){var n=c.getDimensionInfo(i);return t|=n&&n.tooltip!==!1&&null!=n.displayName},0),s=[];d.length?f(d,function(e){r(Qs(c,t,e),e)}):f(i,r);var p=a?l?"\n":"<br/>":"",v=p+s.join(p||", ");return{renderMode:n,content:v,style:h}}function a(t){return{renderMode:n,content:Vo(No(t)),style:h}}var o=this;n=n||"html";var s="html"===n?"<br/>":"\n",l="richText"===n,h={},u=0,c=this.getData(),d=c.mapDimension("defaultedTooltip",!0),p=d.length,v=this.getRawValue(t),m=_(v),y=c.getItemVisual(t,"color");M(y)&&y.colorStops&&(y=(y.colorStops[0]||{}).color),y=y||"transparent";var x=p>1||m&&!p?r(v):a(p?Qs(c,t,d[0]):m?v[0]:v),w=x.content,b=o.seriesIndex+"at"+u,S=Go({color:y,type:"item",renderMode:n,markerId:b});h[b]=y,++u;var A=c.getName(t),I=this.name;or(this)||(I=""),I=I?Vo(I)+(e?": ":s):"";var T="string"==typeof S?S:S.content,C=e?T+I+w:I+T+(A?Vo(A)+": "+w:w);return{html:C,markers:h}},isAnimationEnabled:function(){if(Ag.node)return!1;var t=this.getShallow("animation");return t&&this.getData().count()>this.getShallow("animationThreshold")&&(t=!1),t},restoreData:function(){this.dataTask.dirty()},getColorFromPalette:function(t,e,i){var n=this.ecModel,r=d_.getColorFromPalette.call(this,t,e,i);return r||(r=n.getColorFromPalette(t,e,i)),r},coordDimToDataDim:function(t){return this.getRawData().mapDimension(t,!0)},getProgressive:function(){return this.get("progressive")},getProgressiveThreshold:function(){return this.get("progressiveThreshold")},getAxisTooltipData:null,getTooltipPosition:null,pipeTask:null,preventIncremental:null,pipelineContext:null});c(j_,X_),c(j_,d_);var K_=function(){this.group=new Fv,this.uid=mo("viewComponent")};K_.prototype={constructor:K_,init:function(){},render:function(){},dispose:function(){},filterForExposedEvent:null};var $_=K_.prototype;$_.updateView=$_.updateLayout=$_.updateVisual=function(){},mr(K_),wr(K_,{registerWhenExtend:!0});var Q_=function(){var t=hr();return function(e){var i=t(e),n=e.pipelineContext,r=i.large,a=i.progressiveRender,o=i.large=n&&n.large,s=i.progressiveRender=n&&n.progressiveRender;return!!(r^o||a^s)&&"reset"}},J_=hr(),tw=Q_();dl.prototype={type:"chart",init:function(){},render:function(){},highlight:function(t,e,i,n){pl(t.getData(),n,"emphasis")},downplay:function(t,e,i,n){pl(t.getData(),n,"normal")},remove:function(){this.group.removeAll()},dispose:function(){},incrementalPrepareRender:null,incrementalRender:null,updateTransform:null,filterForExposedEvent:null};var ew=dl.prototype;ew.updateView=ew.updateLayout=ew.updateVisual=function(t,e,i,n){this.render(t,e,i,n)},mr(dl,["dispose"]),wr(dl,{registerWhenExtend:!0}),dl.markUpdateMethod=function(t,e){J_(t).updateMethod=e};var iw={incrementalPrepareRender:{progress:function(t,e){e.view.incrementalRender(t,e.model,e.ecModel,e.api,e.payload)}},render:{forceFirstProgress:!0,progress:function(t,e){e.view.render(e.model,e.ecModel,e.api,e.payload)}}},nw="\x00__throttleOriginMethod",rw="\x00__throttleRate",aw="\x00__throttleType",ow={createOnAllSeries:!0,performRawSeries:!0,reset:function(t,e){var i=t.getData(),n=(t.visualColorAccessPath||"itemStyle.color").split("."),r=t.get(n),a=!w(r)||r instanceof xx?null:r;(!r||a)&&(r=t.getColorFromPalette(t.name,null,e.getSeriesCount())),i.setVisual("color",r);var o=(t.visualBorderColorAccessPath||"itemStyle.borderColor").split("."),s=t.get(o);if(i.setVisual("borderColor",s),!e.isSeriesFiltered(t)){a&&i.each(function(e){i.setItemVisual(e,"color",a(t.getDataParams(e)))});var l=function(t,e){var i=t.getItemModel(e),r=i.get(n,!0),a=i.get(o,!0);null!=r&&t.setItemVisual(e,"color",r),null!=a&&t.setItemVisual(e,"borderColor",a)};return{dataEach:i.hasItemOption?l:null}}}},sw={legend:{selector:{all:"全选",inverse:"反选"}},toolbox:{brush:{title:{rect:"矩形选择",polygon:"圈选",lineX:"横向选择",lineY:"纵向选择",keep:"保持选择",clear:"清除选择"}},dataView:{title:"数据视图",lang:["数据视图","关闭","刷新"]},dataZoom:{title:{zoom:"区域缩放",back:"区域缩放还原"}},magicType:{title:{line:"切换为折线图",bar:"切换为柱状图",stack:"切换为堆叠",tiled:"切换为平铺"}},restore:{title:"还原"},saveAsImage:{title:"保存为图片",lang:["右键另存为图片"]}},series:{typeNames:{pie:"饼图",bar:"柱状图",line:"折线图",scatter:"散点图",effectScatter:"涟漪散点图",radar:"雷达图",tree:"树图",treemap:"矩形树图",boxplot:"箱型图",candlestick:"K线图",k:"K线图",heatmap:"热力图",map:"地图",parallel:"平行坐标图",lines:"线图",graph:"关系图",sankey:"桑基图",funnel:"漏斗图",gauge:"仪表盘图",pictorialBar:"象形柱图",themeRiver:"主题河流图",sunburst:"旭日图"}},aria:{general:{withTitle:"这是一个关于“{title}”的图表。",withoutTitle:"这是一个图表，"},series:{single:{prefix:"",withName:"图表类型是{seriesType}，表示{seriesName}。",withoutName:"图表类型是{seriesType}。"},multiple:{prefix:"它由{seriesCount}个图表系列组成。",withName:"第{seriesId}个系列是一个表示{seriesName}的{seriesType}，",withoutName:"第{seriesId}个系列是一个{seriesType}，",separator:{middle:"；",end:"。"}}},data:{allData:"其数据是——",partialData:"其中，前{displayCnt}项是——",withName:"{name}的数据是{value}",withoutName:"{value}",separator:{middle:"，",end:""}}}},lw=function(t,e){function i(t,e){if("string"!=typeof t)return t;var i=t;return f(e,function(t,e){i=i.replace(new RegExp("\\{\\s*"+e+"\\s*\\}","g"),t)}),i}function n(t){var e=o.get(t);if(null==e){for(var i=t.split("."),n=sw.aria,r=0;r<i.length;++r)n=n[i[r]];return n}return e}function r(){var t=e.getModel("title").option;return t&&t.length&&(t=t[0]),t&&t.text}function a(t){return sw.series.typeNames[t]||"自定义图"}var o=e.getModel("aria");if(o.get("show")){if(o.get("description"))return void t.setAttribute("aria-label",o.get("description"));var s=0;e.eachSeries(function(){++s},this);var l,h=o.get("data.maxCount")||10,u=o.get("series.maxCount")||10,c=Math.min(s,u);if(!(1>s)){var d=r();l=d?i(n("general.withTitle"),{title:d}):n("general.withoutTitle");var p=[],g=s>1?"series.multiple.prefix":"series.single.prefix";l+=i(n(g),{seriesCount:s}),e.eachSeries(function(t,e){if(c>e){var r,o=t.get("name"),l="series."+(s>1?"multiple":"single")+".";r=n(o?l+"withName":l+"withoutName"),r=i(r,{seriesId:t.seriesIndex,seriesName:t.get("name"),seriesType:a(t.subType)});var u=t.getData();window.data=u,r+=u.count()>h?i(n("data.partialData"),{displayCnt:h}):n("data.allData");for(var d=[],f=0;f<u.count();f++)if(h>f){var g=u.getName(f),v=Qs(u,f);d.push(i(n(g?"data.withName":"data.withoutName"),{name:g,value:v}))}r+=d.join(n("data.separator.middle"))+n("data.separator.end"),p.push(r)}}),l+=p.join(n("series.multiple.separator.middle"))+n("series.multiple.separator.end"),t.setAttribute("aria-label",l)}}},hw=Math.PI,uw=function(t,e){e=e||{},s(e,{text:"loading",textColor:"#000",fontSize:"12px",maskColor:"rgba(255, 255, 255, 0.8)",showSpinner:!0,color:"#c23531",spinnerRadius:10,lineWidth:5,zlevel:0});var i=new Fv,n=new dx({style:{fill:e.maskColor},zlevel:e.zlevel,z:1e4});i.add(n);var r=e.fontSize+" sans-serif",a=new dx({style:{fill:"none",text:e.text,font:r,textPosition:"right",textDistance:10,textFill:e.textColor},zlevel:e.zlevel,z:10001});if(i.add(a),e.showSpinner){var o=new mx({shape:{startAngle:-hw/2,endAngle:-hw/2+.1,r:e.spinnerRadius},style:{stroke:e.color,lineCap:"round",lineWidth:e.lineWidth},zlevel:e.zlevel,z:10001});o.animateShape(!0).when(1e3,{endAngle:3*hw/2}).start("circularInOut"),o.animateShape(!0).when(1e3,{startAngle:3*hw/2}).delay(300).start("circularInOut"),i.add(o)}return i.resize=function(){var i=Zi(e.text,r),s=e.showSpinner?e.spinnerRadius:0,l=(t.getWidth()-2*s-(e.showSpinner&&i?10:0)-i)/2-(e.showSpinner?0:i/2),h=t.getHeight()/2;e.showSpinner&&o.setShape({cx:l,cy:h}),a.setShape({x:l-s,y:h-s,width:2*s,height:2*s}),n.setShape({x:0,y:0,width:t.getWidth(),height:t.getHeight()})},i.resize(),i},cw=_l.prototype;cw.restoreData=function(t,e){t.restoreData(e),this._stageTaskMap.each(function(t){var e=t.overallTask;e&&e.dirty()})},cw.getPerformArgs=function(t,e){if(t.__pipeline){var i=this._pipelineMap.get(t.__pipeline.id),n=i.context,r=!e&&i.progressiveEnabled&&(!n||n.progressiveRender)&&t.__idxInPipeline>i.blockIndex,a=r?i.step:null,o=n&&n.modDataCount,s=null!=o?Math.ceil(o/a):null;return{step:a,modBy:s,modDataCount:o}}},cw.getPipeline=function(t){return this._pipelineMap.get(t)},cw.updateStreamModes=function(t,e){var i=this._pipelineMap.get(t.uid),n=t.getData(),r=n.count(),a=i.progressiveEnabled&&e.incrementalPrepareRender&&r>=i.threshold,o=t.get("large")&&r>=t.get("largeThreshold"),s="mod"===t.get("progressiveChunkMode")?r:null;t.pipelineContext=i.context={progressiveRender:a,modDataCount:s,large:o}},cw.restorePipelines=function(t){var e=this,i=e._pipelineMap=N();t.eachSeries(function(t){var n=t.getProgressive(),r=t.uid;i.set(r,{id:r,head:null,tail:null,threshold:t.getProgressiveThreshold(),progressiveEnabled:n&&!(t.preventIncremental&&t.preventIncremental()),blockIndex:-1,step:Math.round(n||700),count:0}),Ll(e,t,t.dataTask)})},cw.prepareStageTasks=function(){var t=this._stageTaskMap,e=this.ecInstance.getModel(),i=this.api;f(this._allHandlers,function(n){var r=t.get(n.uid)||t.set(n.uid,[]);n.reset&&bl(this,n,r,e,i),n.overallReset&&Ml(this,n,r,e,i)},this)},cw.prepareView=function(t,e,i,n){var r=t.renderTask,a=r.context;a.model=e,a.ecModel=i,a.api=n,r.__block=!t.incrementalPrepareRender,Ll(this,e,r)},cw.performDataProcessorTasks=function(t,e){wl(this,this._dataProcessorHandlers,t,e,{block:!0})},cw.performVisualTasks=function(t,e,i){wl(this,this._visualHandlers,t,e,i)},cw.performSeriesTasks=function(t){var e;t.eachSeries(function(t){e|=t.dataTask.perform()}),this.unfinished|=e},cw.plan=function(){this._pipelineMap.each(function(t){var e=t.tail;do{if(e.__block){t.blockIndex=e.__idxInPipeline;break}e=e.getUpstream()}while(e)})};var dw=cw.updatePayload=function(t,e){"remain"!==e&&(t.context.payload=e)},fw=kl(0);_l.wrapStageHandler=function(t,e){return w(t)&&(t={overallReset:t,seriesType:Ol(t)}),t.uid=mo("stageHandler"),e&&(t.visualType=e),t};var pw,gw={},vw={};El(gw,S_),El(vw,As),gw.eachSeriesByType=gw.eachRawSeriesByType=function(t){pw=t},gw.eachComponent=function(t){"series"===t.mainType&&t.subType&&(pw=t.subType)};var mw=["#37A2DA","#32C5E9","#67E0E3","#9FE6B8","#FFDB5C","#ff9f7f","#fb7293","#E062AE","#E690D1","#e7bcf3","#9d96f5","#8378EA","#96BFFF"],yw={color:mw,colorLayer:[["#37A2DA","#ffd85c","#fd7b5f"],["#37A2DA","#67E0E3","#FFDB5C","#ff9f7f","#E062AE","#9d96f5"],["#37A2DA","#32C5E9","#9FE6B8","#FFDB5C","#ff9f7f","#fb7293","#e7bcf3","#8378EA","#96BFFF"],mw]},xw="#eee",_w=function(){return{axisLine:{lineStyle:{color:xw}},axisTick:{lineStyle:{color:xw}},axisLabel:{textStyle:{color:xw}},splitLine:{lineStyle:{type:"dashed",color:"#aaa"}},splitArea:{areaStyle:{color:xw}}}},ww=["#dd6b66","#759aa0","#e69d87","#8dc1a9","#ea7e53","#eedd78","#73a373","#73b9bc","#7289ab","#91ca8c","#f49f42"],bw={color:ww,backgroundColor:"#333",tooltip:{axisPointer:{lineStyle:{color:xw},crossStyle:{color:xw},label:{color:"#000"}}},legend:{textStyle:{color:xw}},textStyle:{color:xw},title:{textStyle:{color:xw}},toolbox:{iconStyle:{normal:{borderColor:xw}}},dataZoom:{textStyle:{color:xw}},visualMap:{textStyle:{color:xw}},timeline:{lineStyle:{color:xw},itemStyle:{normal:{color:ww[1]}},label:{normal:{textStyle:{color:xw}}},controlStyle:{normal:{color:xw,borderColor:xw}}},timeAxis:_w(),logAxis:_w(),valueAxis:_w(),categoryAxis:_w(),line:{symbol:"circle"},graph:{color:ww},gauge:{title:{textStyle:{color:xw}}},candlestick:{itemStyle:{normal:{color:"#FD1050",color0:"#0CF49B",borderColor:"#FD1050",borderColor0:"#0CF49B"}}}};bw.categoryAxis.splitLine.show=!1,l_.extend({type:"dataset",defaultOption:{seriesLayoutBy:x_,sourceHeader:null,dimensions:null,source:null},optionUpdated:function(){rs(this)}}),K_.extend({type:"dataset"});var Mw=ea.extend({type:"ellipse",shape:{cx:0,cy:0,rx:0,ry:0},buildPath:function(t,e){var i=.5522848,n=e.cx,r=e.cy,a=e.rx,o=e.ry,s=a*i,l=o*i;t.moveTo(n-a,r),t.bezierCurveTo(n-a,r-l,n-s,r-o,n,r-o),t.bezierCurveTo(n+s,r-o,n+a,r-l,n+a,r),t.bezierCurveTo(n+a,r+l,n+s,r+o,n,r+o),t.bezierCurveTo(n-s,r+o,n-a,r+l,n-a,r),t.closePath()}}),Sw=/[\s,]+/;Bl.prototype.parse=function(t,e){e=e||{};var i=zl(t);if(!i)throw new Error("Illegal svg");var n=new Fv;this._root=n;var r=i.getAttribute("viewBox")||"",a=parseFloat(i.getAttribute("width")||e.width),o=parseFloat(i.getAttribute("height")||e.height);isNaN(a)&&(a=null),isNaN(o)&&(o=null),Vl(i,n,null,!0);for(var s=i.firstChild;s;)this._parseNode(s,n),s=s.nextSibling;var l,h;if(r){var u=E(r).split(Sw);u.length>=4&&(l={x:parseFloat(u[0]||0),y:parseFloat(u[1]||0),width:parseFloat(u[2]),height:parseFloat(u[3])})}if(l&&null!=a&&null!=o&&(h=Zl(l,a,o),!e.ignoreViewBox)){var c=n;n=new Fv,n.add(c),c.scale=h.scale.slice(),c.position=h.position.slice()}return e.ignoreRootClip||null==a||null==o||n.setClipPath(new dx({shape:{x:0,y:0,width:a,height:o}})),{root:n,width:a,height:o,viewBoxRect:l,viewBoxTransform:h}},Bl.prototype._parseNode=function(t,e){var i=t.nodeName.toLowerCase();"defs"===i?this._isDefine=!0:"text"===i&&(this._isText=!0);var n;if(this._isDefine){var r=Iw[i];if(r){var a=r.call(this,t),o=t.getAttribute("id");o&&(this._defs[o]=a)}}else{var r=Aw[i];r&&(n=r.call(this,t,e),e.add(n))}for(var s=t.firstChild;s;)1===s.nodeType&&this._parseNode(s,n),3===s.nodeType&&this._isText&&this._parseText(s,n),s=s.nextSibling;"defs"===i?this._isDefine=!1:"text"===i&&(this._isText=!1)},Bl.prototype._parseText=function(t,e){if(1===t.nodeType){var i=t.getAttribute("dx")||0,n=t.getAttribute("dy")||0;this._textX+=parseFloat(i),this._textY+=parseFloat(n)}var r=new tx({style:{text:t.textContent,transformText:!0},position:[this._textX||0,this._textY||0]});Nl(e,r),Vl(t,r,this._defs);var a=r.style.fontSize;a&&9>a&&(r.style.fontSize=9,r.scale=r.scale||[1,1],r.scale[0]*=a/9,r.scale[1]*=a/9);var o=r.getBoundingRect();return this._textX+=o.width,e.add(r),r};var Aw={g:function(t,e){var i=new Fv;return Nl(e,i),Vl(t,i,this._defs),i},rect:function(t,e){var i=new dx;return Nl(e,i),Vl(t,i,this._defs),i.setShape({x:parseFloat(t.getAttribute("x")||0),y:parseFloat(t.getAttribute("y")||0),width:parseFloat(t.getAttribute("width")||0),height:parseFloat(t.getAttribute("height")||0)}),i},circle:function(t,e){var i=new ex;return Nl(e,i),Vl(t,i,this._defs),i.setShape({cx:parseFloat(t.getAttribute("cx")||0),cy:parseFloat(t.getAttribute("cy")||0),r:parseFloat(t.getAttribute("r")||0)}),i},line:function(t,e){var i=new px;return Nl(e,i),Vl(t,i,this._defs),i.setShape({x1:parseFloat(t.getAttribute("x1")||0),y1:parseFloat(t.getAttribute("y1")||0),x2:parseFloat(t.getAttribute("x2")||0),y2:parseFloat(t.getAttribute("y2")||0)}),i},ellipse:function(t,e){var i=new Mw;return Nl(e,i),Vl(t,i,this._defs),i.setShape({cx:parseFloat(t.getAttribute("cx")||0),cy:parseFloat(t.getAttribute("cy")||0),rx:parseFloat(t.getAttribute("rx")||0),ry:parseFloat(t.getAttribute("ry")||0)}),i},polygon:function(t,e){var i=t.getAttribute("points");i&&(i=Fl(i));var n=new lx({shape:{points:i||[]}});return Nl(e,n),Vl(t,n,this._defs),n},polyline:function(t,e){var i=new ea;Nl(e,i),Vl(t,i,this._defs);var n=t.getAttribute("points");n&&(n=Fl(n));var r=new hx({shape:{points:n||[]}});return r},image:function(t,e){var i=new Cn;return Nl(e,i),Vl(t,i,this._defs),i.setStyle({image:t.getAttribute("xlink:href"),x:t.getAttribute("x"),y:t.getAttribute("y"),width:t.getAttribute("width"),height:t.getAttribute("height")}),i},text:function(t,e){var i=t.getAttribute("x")||0,n=t.getAttribute("y")||0,r=t.getAttribute("dx")||0,a=t.getAttribute("dy")||0;this._textX=parseFloat(i)+parseFloat(r),this._textY=parseFloat(n)+parseFloat(a);var o=new Fv;return Nl(e,o),Vl(t,o,this._defs),o},tspan:function(t,e){var i=t.getAttribute("x"),n=t.getAttribute("y");null!=i&&(this._textX=parseFloat(i)),null!=n&&(this._textY=parseFloat(n));var r=t.getAttribute("dx")||0,a=t.getAttribute("dy")||0,o=new Fv;return Nl(e,o),Vl(t,o,this._defs),this._textX+=r,this._textY+=a,o},path:function(t,e){var i=t.getAttribute("d")||"",n=aa(i);return Nl(e,n),Vl(t,n,this._defs),n}},Iw={lineargradient:function(t){var e=parseInt(t.getAttribute("x1")||0,10),i=parseInt(t.getAttribute("y1")||0,10),n=parseInt(t.getAttribute("x2")||10,10),r=parseInt(t.getAttribute("y2")||0,10),a=new _x(e,i,n,r);return Rl(t,a),a},radialgradient:function(){}},Tw={fill:"fill",stroke:"stroke","stroke-width":"lineWidth",opacity:"opacity","fill-opacity":"fillOpacity","stroke-opacity":"strokeOpacity","stroke-dasharray":"lineDash","stroke-dashoffset":"lineDashOffset","stroke-linecap":"lineCap","stroke-linejoin":"lineJoin","stroke-miterlimit":"miterLimit","font-family":"fontFamily","font-size":"fontSize","font-style":"fontStyle","font-weight":"fontWeight","text-align":"textAlign","alignment-baseline":"textBaseline"},Cw=/url\(\s*#(.*?)\)/,Dw=/(translate|scale|rotate|skewX|skewY|matrix)\(([\-\s0-9\.e,]*)\)/g,kw=/([^\s:;]+)\s*:\s*([^:;]+)/g,Pw=N(),Lw={registerMap:function(t,e,i){var n;return _(e)?n=e:e.svg?n=[{type:"svg",source:e.svg,specialAreas:e.specialAreas}]:(e.geoJson&&!e.features&&(i=e.specialAreas,e=e.geoJson),n=[{type:"geoJSON",source:e,specialAreas:i}]),f(n,function(t){var e=t.type;"geoJson"===e&&(e=t.type="geoJSON");var i=Ow[e];wg&&O(i,"Illegal map type: "+e),i(t)}),Pw.set(t,n)},retrieveMap:function(t){return Pw.get(t)}},Ow={geoJSON:function(t){var e=t.source;t.geoJSON=b(e)?"undefined"!=typeof JSON&&JSON.parse?JSON.parse(e):new Function("return ("+e+");")():e},svg:function(t){t.svgXML=zl(t.source)}},Ew=O,zw=f,Bw=w,Rw=M,Nw=l_.parseClassType,Fw="4.9.0",Vw={zrender:"4.3.2"},Ww=1,Hw=1e3,Gw=800,Zw=900,Xw=5e3,Yw=1e3,Uw=1100,qw=2e3,jw=3e3,Kw=3500,$w=4e3,Qw=5e3,Jw={PROCESSOR:{FILTER:Hw,SERIES_FILTER:Gw,STATISTIC:Xw},VISUAL:{LAYOUT:Yw,PROGRESSIVE_LAYOUT:Uw,GLOBAL:qw,CHART:jw,POST_CHART_LAYOUT:Kw,COMPONENT:$w,BRUSH:Qw}},tb="__flagInMainProcess",eb="__optionUpdated",ib=/^[a-zA-Z0-9_]+$/;Yl.prototype.on=Xl("on",!0),Yl.prototype.off=Xl("off",!0),Yl.prototype.one=Xl("one",!0),c(Yl,Ug);var nb=Ul.prototype;nb._onframe=function(){if(!this._disposed){var t=this._scheduler;if(this[eb]){var e=this[eb].silent;this[tb]=!0,jl(this),rb.update.call(this),this[tb]=!1,this[eb]=!1,Jl.call(this,e),th.call(this,e)}else if(t.unfinished){var i=Ww,n=this._model,r=this._api;t.unfinished=!1;do{var a=+new Date;t.performSeriesTasks(n),t.performDataProcessorTasks(n),$l(this,n),t.performVisualTasks(n),oh(this,this._model,r,"remain"),i-=+new Date-a}while(i>0&&t.unfinished);t.unfinished||this._zr.flush()}}},nb.getDom=function(){return this._dom},nb.getZr=function(){return this._zr},nb.setOption=function(t,e,i){if(wg&&Ew(!this[tb],"`setOption` should not be called during main process."),this._disposed)return void lh(this.id);var n;if(Rw(e)&&(i=e.lazyUpdate,n=e.silent,e=e.notMerge),this[tb]=!0,!this._model||e){var r=new Ts(this._api),a=this._theme,o=this._model=new S_;o.scheduler=this._scheduler,o.init(null,null,a,r)}this._model.setOption(t,hb),i?(this[eb]={silent:n},this[tb]=!1):(jl(this),rb.update.call(this),this._zr.flush(),this[eb]=!1,this[tb]=!1,Jl.call(this,n),th.call(this,n))},nb.setTheme=function(){console.error("ECharts#setTheme() is DEPRECATED in ECharts 3.0")},nb.getModel=function(){return this._model},nb.getOption=function(){return this._model&&this._model.getOption()},nb.getWidth=function(){return this._zr.getWidth()},nb.getHeight=function(){return this._zr.getHeight()},nb.getDevicePixelRatio=function(){return this._zr.painter.dpr||window.devicePixelRatio||1},nb.getRenderedCanvas=function(t){if(Ag.canvasSupported){t=t||{},t.pixelRatio=t.pixelRatio||1,t.backgroundColor=t.backgroundColor||this._model.get("backgroundColor");var e=this._zr;return e.painter.getRenderedCanvas(t)}},nb.getSvgDataURL=function(){if(Ag.svgSupported){var t=this._zr,e=t.storage.getDisplayList();return f(e,function(t){t.stopAnimation(!0)}),t.painter.toDataURL()}},nb.getDataURL=function(t){if(this._disposed)return void lh(this.id);t=t||{};var e=t.excludeComponents,i=this._model,n=[],r=this;zw(e,function(t){i.eachComponent({mainType:t},function(t){var e=r._componentsMap[t.__viewId];e.group.ignore||(n.push(e),e.group.ignore=!0)})});var a="svg"===this._zr.painter.getType()?this.getSvgDataURL():this.getRenderedCanvas(t).toDataURL("image/"+(t&&t.type||"png"));return zw(n,function(t){t.group.ignore=!1}),a},nb.getConnectedDataURL=function(t){if(this._disposed)return void lh(this.id);if(Ag.canvasSupported){var e="svg"===t.type,i=this.group,r=Math.min,a=Math.max,o=1/0;if(gb[i]){var s=o,l=o,h=-o,u=-o,c=[],d=t&&t.pixelRatio||1;f(pb,function(o){if(o.group===i){var d=e?o.getZr().painter.getSvgDom().innerHTML:o.getRenderedCanvas(n(t)),f=o.getDom().getBoundingClientRect();s=r(f.left,s),l=r(f.top,l),h=a(f.right,h),u=a(f.bottom,u),c.push({dom:d,left:f.left,top:f.top})}}),s*=d,l*=d,h*=d,u*=d;var p=h-s,g=u-l,v=Bg(),m=jn(v,{renderer:e?"svg":"canvas"});if(m.resize({width:p,height:g}),e){var y="";return zw(c,function(t){var e=t.left-s,i=t.top-l;y+='<g transform="translate('+e+","+i+')">'+t.dom+"</g>"}),m.painter.getSvgRoot().innerHTML=y,t.connectedBackgroundColor&&m.painter.setBackgroundColor(t.connectedBackgroundColor),m.refreshImmediately(),m.painter.toDataURL()}return t.connectedBackgroundColor&&m.add(new dx({shape:{x:0,y:0,width:p,height:g},style:{fill:t.connectedBackgroundColor}})),zw(c,function(t){var e=new Cn({style:{x:t.left*d-s,y:t.top*d-l,image:t.dom}});m.add(e)}),m.refreshImmediately(),v.toDataURL("image/"+(t&&t.type||"png"))}return this.getDataURL(t)}},nb.convertToPixel=x(ql,"convertToPixel"),nb.convertFromPixel=x(ql,"convertFromPixel"),nb.containPixel=function(t,e){if(this._disposed)return void lh(this.id);var i,n=this._model;return t=ur(n,t),f(t,function(t,n){n.indexOf("Models")>=0&&f(t,function(t){var r=t.coordinateSystem;if(r&&r.containPoint)i|=!!r.containPoint(e);else if("seriesModels"===n){var a=this._chartsMap[t.__viewId];a&&a.containPoint?i|=a.containPoint(e,t):wg&&console.warn(n+": "+(a?"The found component do not support containPoint.":"No view mapping to the found component."))}else wg&&console.warn(n+": containPoint is not supported")},this)},this),!!i},nb.getVisual=function(t,e){var i=this._model;t=ur(i,t,{defaultMainType:"series"});var n=t.seriesModel;wg&&(n||console.warn("There is no specified seires model"));var r=n.getData(),a=t.hasOwnProperty("dataIndexInside")?t.dataIndexInside:t.hasOwnProperty("dataIndex")?r.indexOfRawIndex(t.dataIndex):null;return null!=a?r.getItemVisual(a,e):r.getVisual(e)},nb.getViewOfComponentModel=function(t){return this._componentsMap[t.__viewId]},nb.getViewOfSeriesModel=function(t){return this._chartsMap[t.__viewId]};var rb={prepareAndUpdate:function(t){jl(this),rb.update.call(this,t)},update:function(t){var e=this._model,i=this._api,n=this._zr,r=this._coordSysMgr,a=this._scheduler;if(e){a.restoreData(e,t),a.performSeriesTasks(e),r.create(e,i),a.performDataProcessorTasks(e,t),$l(this,e),r.update(e,i),nh(e),a.performVisualTasks(e,t),rh(this,e,i,t);var o=e.get("backgroundColor")||"transparent";if(Ag.canvasSupported)n.setBackgroundColor(o);else{var s=ti(o);o=hi(s,"rgb"),0===s[3]&&(o="transparent")}sh(e,i)}},updateTransform:function(t){var e=this._model,i=this,n=this._api;if(e){var r=[];e.eachComponent(function(a,o){var s=i.getViewOfComponentModel(o);if(s&&s.__alive)if(s.updateTransform){var l=s.updateTransform(o,e,n,t);l&&l.update&&r.push(s)}else r.push(s)});var a=N();e.eachSeries(function(r){var o=i._chartsMap[r.__viewId];if(o.updateTransform){var s=o.updateTransform(r,e,n,t);s&&s.update&&a.set(r.uid,1)}else a.set(r.uid,1)}),nh(e),this._scheduler.performVisualTasks(e,t,{setDirty:!0,dirtyMap:a}),oh(i,e,n,t,a),sh(e,this._api)}},updateView:function(t){var e=this._model;e&&(dl.markUpdateMethod(t,"updateView"),nh(e),this._scheduler.performVisualTasks(e,t,{setDirty:!0}),rh(this,this._model,this._api,t),sh(e,this._api))},updateVisual:function(t){rb.update.call(this,t)},updateLayout:function(t){rb.update.call(this,t)}};nb.resize=function(t){if(wg&&Ew(!this[tb],"`resize` should not be called during main process."),this._disposed)return void lh(this.id);this._zr.resize(t);var e=this._model;if(this._loadingFX&&this._loadingFX.resize(),e){var i=e.resetOption("media"),n=t&&t.silent;this[tb]=!0,i&&jl(this),rb.update.call(this),this[tb]=!1,Jl.call(this,n),th.call(this,n)}},nb.showLoading=function(t,e){if(this._disposed)return void lh(this.id);if(Rw(t)&&(e=t,t=""),t=t||"default",this.hideLoading(),!fb[t])return void(wg&&console.warn("Loading effects "+t+" not exists."));var i=fb[t](this._api,e),n=this._zr;this._loadingFX=i,n.add(i)},nb.hideLoading=function(){return this._disposed?void lh(this.id):(this._loadingFX&&this._zr.remove(this._loadingFX),void(this._loadingFX=null))},nb.makeActionFromEvent=function(t){var e=o({},t);
return e.type=sb[t.type],e},nb.dispatchAction=function(t,e){if(this._disposed)return void lh(this.id);if(Rw(e)||(e={silent:!!e}),ob[t.type]&&this._model){if(this[tb])return void this._pendingActions.push(t);Ql.call(this,t,e.silent),e.flush?this._zr.flush(!0):e.flush!==!1&&Ag.browser.weChat&&this._throttledZrFlush(),Jl.call(this,e.silent),th.call(this,e.silent)}},nb.appendData=function(t){if(this._disposed)return void lh(this.id);var e=t.seriesIndex,i=this.getModel(),n=i.getSeriesByIndex(e);wg&&Ew(t.data&&n),n.appendData(t),this._scheduler.unfinished=!0},nb.on=Xl("on",!1),nb.off=Xl("off",!1),nb.one=Xl("one",!1);var ab=["click","dblclick","mouseover","mouseout","mousemove","mousedown","mouseup","globalout","contextmenu"];nb._initEvents=function(){zw(ab,function(t){var e=function(e){var i,n=this.getModel(),r=e.target,a="globalout"===t;if(a)i={};else if(r&&null!=r.dataIndex){var s=r.dataModel||n.getSeriesByIndex(r.seriesIndex);i=s&&s.getDataParams(r.dataIndex,r.dataType,r)||{}}else r&&r.eventData&&(i=o({},r.eventData));if(i){var l=i.componentType,h=i.componentIndex;("markLine"===l||"markPoint"===l||"markArea"===l)&&(l="series",h=i.seriesIndex);var u=l&&null!=h&&n.getComponent(l,h),c=u&&this["series"===u.mainType?"_chartsMap":"_componentsMap"][u.__viewId];wg&&(a||u&&c||console.warn("model or view can not be found by params")),i.event=e,i.type=t,this._ecEventProcessor.eventInfo={targetEl:r,packedEvent:i,model:u,view:c},this.trigger(t,i)}};e.zrEventfulCallAtLast=!0,this._zr.on(t,e,this)},this),zw(sb,function(t,e){this._messageCenter.on(e,function(t){this.trigger(e,t)},this)},this)},nb.isDisposed=function(){return this._disposed},nb.clear=function(){return this._disposed?void lh(this.id):void this.setOption({series:[]},!0)},nb.dispose=function(){if(this._disposed)return void lh(this.id);this._disposed=!0,dr(this.getDom(),yb,"");var t=this._api,e=this._model;zw(this._componentsViews,function(i){i.dispose(e,t)}),zw(this._chartsViews,function(i){i.dispose(e,t)}),this._zr.dispose(),delete pb[this.id]},c(Ul,Ug),fh.prototype={constructor:fh,normalizeQuery:function(t){var e={},i={},n={};if(b(t)){var r=Nw(t);e.mainType=r.main||null,e.subType=r.sub||null}else{var a=["Index","Name","Id"],o={name:1,dataIndex:1,dataType:1};f(t,function(t,r){for(var s=!1,l=0;l<a.length;l++){var h=a[l],u=r.lastIndexOf(h);if(u>0&&u===r.length-h.length){var c=r.slice(0,u);"data"!==c&&(e.mainType=c,e[h.toLowerCase()]=t,s=!0)}}o.hasOwnProperty(r)&&(i[r]=t,s=!0),s||(n[r]=t)})}return{cptQuery:e,dataQuery:i,otherQuery:n}},filter:function(t,e){function i(t,e,i,n){return null==t[i]||e[n||i]===t[i]}var n=this.eventInfo;if(!n)return!0;var r=n.targetEl,a=n.packedEvent,o=n.model,s=n.view;if(!o||!s)return!0;var l=e.cptQuery,h=e.dataQuery;return i(l,o,"mainType")&&i(l,o,"subType")&&i(l,o,"index","componentIndex")&&i(l,o,"name")&&i(l,o,"id")&&i(h,a,"name")&&i(h,a,"dataIndex")&&i(h,a,"dataType")&&(!s.filterForExposedEvent||s.filterForExposedEvent(t,e.otherQuery,r,a))},afterTrigger:function(){this.eventInfo=null}};var ob={},sb={},lb=[],hb=[],ub=[],cb=[],db={},fb={},pb={},gb={},vb=new Date-0,mb=new Date-0,yb="_echarts_instance_",xb=mh;Dh(qw,ow),bh(N_),Mh(Zw,F_),Ph("default",uw),Ah({type:"highlight",event:"highlight",update:"highlight"},V),Ah({type:"downplay",event:"downplay",update:"downplay"},V),wh("light",yw),wh("dark",bw);var _b={};Vh.prototype={constructor:Vh,add:function(t){return this._add=t,this},update:function(t){return this._update=t,this},remove:function(t){return this._remove=t,this},execute:function(){var t,e=this._old,i=this._new,n={},r={},a=[],o=[];for(Wh(e,n,a,"_oldKeyGetter",this),Wh(i,r,o,"_newKeyGetter",this),t=0;t<e.length;t++){var s=a[t],l=r[s];if(null!=l){var h=l.length;h?(1===h&&(r[s]=null),l=l.shift()):r[s]=null,this._update&&this._update(l,t)}else this._remove&&this._remove(t)}for(var t=0;t<o.length;t++){var s=o[t];if(r.hasOwnProperty(s)){var l=r[s];if(null==l)continue;if(l.length)for(var u=0,h=l.length;h>u;u++)this._add&&this._add(l[u]);else this._add&&this._add(l)}}}};var wb=N(["tooltip","label","itemName","itemId","seriesName"]),bb=M,Mb="undefined",Sb=-1,Ab="e\x00\x00",Ib={"float":typeof Float64Array===Mb?Array:Float64Array,"int":typeof Int32Array===Mb?Array:Int32Array,ordinal:Array,number:Array,time:Array},Tb=typeof Uint32Array===Mb?Array:Uint32Array,Cb=typeof Int32Array===Mb?Array:Int32Array,Db=typeof Uint16Array===Mb?Array:Uint16Array,kb=["hasItemOption","_nameList","_idList","_invertedIndicesMap","_rawData","_chunkSize","_chunkCount","_dimValueGetter","_count","_rawCount","_nameDimIdx","_idDimIdx"],Pb=["_extent","_approximateExtent","_rawExtent"],Lb=function(t,e){t=t||["x","y"];for(var i={},n=[],r={},a=0;a<t.length;a++){var o=t[a];b(o)?o=new Yh({name:o}):o instanceof Yh||(o=new Yh(o));var s=o.name;o.type=o.type||"float",o.coordDim||(o.coordDim=s,o.coordDimIndex=0),o.otherDims=o.otherDims||{},n.push(s),i[s]=o,o.index=a,o.createInvertedIndices&&(r[s]=[])}this.dimensions=n,this._dimensionInfos=i,this.hostModel=e,this.dataType,this._indices=null,this._count=0,this._rawCount=0,this._storage={},this._nameList=[],this._idList=[],this._optionModels=[],this._visual={},this._layout={},this._itemVisuals=[],this.hasItemVisual={},this._itemLayouts=[],this._graphicEls=[],this._chunkSize=1e5,this._chunkCount=0,this._rawData,this._rawExtent={},this._extent={},this._approximateExtent={},this._dimensionsSummary=Hh(this),this._invertedIndicesMap=r,this._calculationInfo={},this.userOutput=this._dimensionsSummary.userOutput},Ob=Lb.prototype;Ob.type="list",Ob.hasItemOption=!0,Ob.getDimension=function(t){return("number"==typeof t||!isNaN(t)&&!this._dimensionInfos.hasOwnProperty(t))&&(t=this.dimensions[t]),t},Ob.getDimensionInfo=function(t){return this._dimensionInfos[this.getDimension(t)]},Ob.getDimensionsOnCoord=function(){return this._dimensionsSummary.dataDimsOnCoord.slice()},Ob.mapDimension=function(t,e){var i=this._dimensionsSummary;if(null==e)return i.encodeFirstDimNotExtra[t];var n=i.encode[t];return e===!0?(n||[]).slice():n&&n[e]},Ob.initData=function(t,e,i){var n=ns.isInstance(t)||d(t);if(n&&(t=new Xs(t,this.dimensions.length)),wg&&!n&&("function"!=typeof t.getItem||"function"!=typeof t.count))throw new Error("Inavlid data provider.");this._rawData=t,this._storage={},this._indices=null,this._nameList=e||[],this._idList=[],this._nameRepeatCount={},i||(this.hasItemOption=!1),this.defaultDimValueGetter=G_[this._rawData.getSource().sourceFormat],this._dimValueGetter=i=i||this.defaultDimValueGetter,this._dimValueGetterArrayRows=G_.arrayRows,this._rawExtent={},this._initDataFromProvider(0,t.count()),t.pure&&(this.hasItemOption=!1)},Ob.getProvider=function(){return this._rawData},Ob.appendData=function(t){wg&&O(!this._indices,"appendData can only be called on raw data.");var e=this._rawData,i=this.count();e.appendData(t);var n=e.count();e.persistent||(n+=i),this._initDataFromProvider(i,n)},Ob.appendValues=function(t,e){for(var i=this._chunkSize,n=this._storage,r=this.dimensions,a=r.length,o=this._rawExtent,s=this.count(),l=s+Math.max(t.length,e?e.length:0),h=this._chunkCount,u=0;a>u;u++){var c=r[u];o[c]||(o[c]=ou()),n[c]||(n[c]=[]),Kh(n,this._dimensionInfos[c],i,h,l),this._chunkCount=n[c].length}for(var d=new Array(a),f=s;l>f;f++){for(var p=f-s,g=Math.floor(f/i),v=f%i,m=0;a>m;m++){var c=r[m],y=this._dimValueGetterArrayRows(t[p]||d,c,p,m);n[c][g][v]=y;var x=o[c];y<x[0]&&(x[0]=y),y>x[1]&&(x[1]=y)}e&&(this._nameList[f]=e[p])}this._rawCount=this._count=l,this._extent={},$h(this)},Ob._initDataFromProvider=function(t,e){if(!(t>=e)){for(var i,n=this._chunkSize,r=this._rawData,a=this._storage,o=this.dimensions,s=o.length,l=this._dimensionInfos,h=this._nameList,u=this._idList,c=this._rawExtent,d=this._nameRepeatCount={},f=this._chunkCount,p=0;s>p;p++){var g=o[p];c[g]||(c[g]=ou());var v=l[g];0===v.otherDims.itemName&&(i=this._nameDimIdx=p),0===v.otherDims.itemId&&(this._idDimIdx=p),a[g]||(a[g]=[]),Kh(a,v,n,f,e),this._chunkCount=a[g].length}for(var m=new Array(s),y=t;e>y;y++){m=r.getItem(y,m);for(var x=Math.floor(y/n),_=y%n,w=0;s>w;w++){var g=o[w],b=a[g][x],M=this._dimValueGetter(m,g,y,w);b[_]=M;var S=c[g];M<S[0]&&(S[0]=M),M>S[1]&&(S[1]=M)}if(!r.pure){var A=h[y];if(m&&null==A)if(null!=m.name)h[y]=A=m.name;else if(null!=i){var I=o[i],T=a[I][x];if(T){A=T[_];var C=l[I].ordinalMeta;C&&C.categories.length&&(A=C.categories[A])}}var D=null==m?null:m.id;null==D&&null!=A&&(d[A]=d[A]||0,D=A,d[A]>0&&(D+="__ec__"+d[A]),d[A]++),null!=D&&(u[y]=D)}}!r.persistent&&r.clean&&r.clean(),this._rawCount=this._count=e,this._extent={},$h(this)}},Ob.count=function(){return this._count},Ob.getIndices=function(){var t,e=this._indices;if(e){var i=e.constructor,n=this._count;if(i===Array){t=new i(n);for(var r=0;n>r;r++)t[r]=e[r]}else t=new i(e.buffer,0,n)}else for(var i=Uh(this),t=new i(this.count()),r=0;r<t.length;r++)t[r]=r;return t},Ob.get=function(t,e){if(!(e>=0&&e<this._count))return 0/0;var i=this._storage;if(!i[t])return 0/0;e=this.getRawIndex(e);var n=Math.floor(e/this._chunkSize),r=e%this._chunkSize,a=i[t][n],o=a[r];return o},Ob.getByRawIndex=function(t,e){if(!(e>=0&&e<this._rawCount))return 0/0;var i=this._storage[t];if(!i)return 0/0;var n=Math.floor(e/this._chunkSize),r=e%this._chunkSize,a=i[n];return a[r]},Ob._getFast=function(t,e){var i=Math.floor(e/this._chunkSize),n=e%this._chunkSize,r=this._storage[t][i];return r[n]},Ob.getValues=function(t,e){var i=[];_(t)||(e=t,t=this.dimensions);for(var n=0,r=t.length;r>n;n++)i.push(this.get(t[n],e));return i},Ob.hasValue=function(t){for(var e=this._dimensionsSummary.dataDimsOnCoord,i=0,n=e.length;n>i;i++)if(isNaN(this.get(e[i],t)))return!1;return!0},Ob.getDataExtent=function(t){t=this.getDimension(t);var e=this._storage[t],i=ou();if(!e)return i;var n,r=this.count(),a=!this._indices;if(a)return this._rawExtent[t].slice();if(n=this._extent[t])return n.slice();n=i;for(var o=n[0],s=n[1],l=0;r>l;l++){var h=this._getFast(t,this.getRawIndex(l));o>h&&(o=h),h>s&&(s=h)}return n=[o,s],this._extent[t]=n,n},Ob.getApproximateExtent=function(t){return t=this.getDimension(t),this._approximateExtent[t]||this.getDataExtent(t)},Ob.setApproximateExtent=function(t,e){e=this.getDimension(e),this._approximateExtent[e]=t.slice()},Ob.getCalculationInfo=function(t){return this._calculationInfo[t]},Ob.setCalculationInfo=function(t,e){bb(t)?o(this._calculationInfo,t):this._calculationInfo[t]=e},Ob.getSum=function(t){var e=this._storage[t],i=0;if(e)for(var n=0,r=this.count();r>n;n++){var a=this.get(t,n);isNaN(a)||(i+=a)}return i},Ob.getMedian=function(t){var e=[];this.each(t,function(t){isNaN(t)||e.push(t)});var i=[].concat(e).sort(function(t,e){return t-e}),n=this.count();return 0===n?0:n%2===1?i[(n-1)/2]:(i[n/2]+i[n/2-1])/2},Ob.rawIndexOf=function(t,e){var i=t&&this._invertedIndicesMap[t];if(wg&&!i)throw new Error("Do not supported yet");var n=i[e];return null==n||isNaN(n)?Sb:n},Ob.indexOfName=function(t){for(var e=0,i=this.count();i>e;e++)if(this.getName(e)===t)return e;return-1},Ob.indexOfRawIndex=function(t){if(t>=this._rawCount||0>t)return-1;if(!this._indices)return t;var e=this._indices,i=e[t];if(null!=i&&i<this._count&&i===t)return t;for(var n=0,r=this._count-1;r>=n;){var a=(n+r)/2|0;if(e[a]<t)n=a+1;else{if(!(e[a]>t))return a;r=a-1}}return-1},Ob.indicesOfNearest=function(t,e,i){var n=this._storage,r=n[t],a=[];if(!r)return a;null==i&&(i=1/0);for(var o=1/0,s=-1,l=0,h=0,u=this.count();u>h;h++){var c=e-this.get(t,h),d=Math.abs(c);i>=d&&((o>d||d===o&&c>=0&&0>s)&&(o=d,s=c,l=0),c===s&&(a[l++]=h))}return a.length=l,a},Ob.getRawIndex=Jh,Ob.getRawDataItem=function(t){if(this._rawData.persistent)return this._rawData.getItem(this.getRawIndex(t));for(var e=[],i=0;i<this.dimensions.length;i++){var n=this.dimensions[i];e.push(this.get(n,t))}return e},Ob.getName=function(t){var e=this.getRawIndex(t);return this._nameList[e]||Qh(this,this._nameDimIdx,e)||""},Ob.getId=function(t){return eu(this,this.getRawIndex(t))},Ob.each=function(t,e,i,n){if(this._count){"function"==typeof t&&(n=i,i=e,e=t,t=[]),i=i||n||this,t=p(iu(t),this.getDimension,this),wg&&nu(this,t);for(var r=t.length,a=0;a<this.count();a++)switch(r){case 0:e.call(i,a);break;case 1:e.call(i,this.get(t[0],a),a);break;case 2:e.call(i,this.get(t[0],a),this.get(t[1],a),a);break;default:for(var o=0,s=[];r>o;o++)s[o]=this.get(t[o],a);s[o]=a,e.apply(i,s)}}},Ob.filterSelf=function(t,e,i,n){if(this._count){"function"==typeof t&&(n=i,i=e,e=t,t=[]),i=i||n||this,t=p(iu(t),this.getDimension,this),wg&&nu(this,t);for(var r=this.count(),a=Uh(this),o=new a(r),s=[],l=t.length,h=0,u=t[0],c=0;r>c;c++){var d,f=this.getRawIndex(c);if(0===l)d=e.call(i,c);else if(1===l){var g=this._getFast(u,f);d=e.call(i,g,c)}else{for(var v=0;l>v;v++)s[v]=this._getFast(u,f);s[v]=c,d=e.apply(i,s)}d&&(o[h++]=f)}return r>h&&(this._indices=o),this._count=h,this._extent={},this.getRawIndex=this._indices?tu:Jh,this}},Ob.selectRange=function(t){if(this._count){var e=[];for(var i in t)t.hasOwnProperty(i)&&e.push(i);wg&&nu(this,e);var n=e.length;if(n){var r=this.count(),a=Uh(this),o=new a(r),s=0,l=e[0],h=t[l][0],u=t[l][1],c=!1;if(!this._indices){var d=0;if(1===n){for(var f=this._storage[e[0]],p=0;p<this._chunkCount;p++)for(var g=f[p],v=Math.min(this._count-p*this._chunkSize,this._chunkSize),m=0;v>m;m++){var y=g[m];(y>=h&&u>=y||isNaN(y))&&(o[s++]=d),d++}c=!0}else if(2===n){for(var f=this._storage[l],x=this._storage[e[1]],_=t[e[1]][0],w=t[e[1]][1],p=0;p<this._chunkCount;p++)for(var g=f[p],b=x[p],v=Math.min(this._count-p*this._chunkSize,this._chunkSize),m=0;v>m;m++){var y=g[m],M=b[m];(y>=h&&u>=y||isNaN(y))&&(M>=_&&w>=M||isNaN(M))&&(o[s++]=d),d++}c=!0}}if(!c)if(1===n)for(var m=0;r>m;m++){var S=this.getRawIndex(m),y=this._getFast(l,S);(y>=h&&u>=y||isNaN(y))&&(o[s++]=S)}else for(var m=0;r>m;m++){for(var A=!0,S=this.getRawIndex(m),p=0;n>p;p++){var I=e[p],y=this._getFast(i,S);(y<t[I][0]||y>t[I][1])&&(A=!1)}A&&(o[s++]=this.getRawIndex(m))}return r>s&&(this._indices=o),this._count=s,this._extent={},this.getRawIndex=this._indices?tu:Jh,this}}},Ob.mapArray=function(t,e,i,n){"function"==typeof t&&(n=i,i=e,e=t,t=[]),i=i||n||this;var r=[];return this.each(t,function(){r.push(e&&e.apply(this,arguments))},i),r},Ob.map=function(t,e,i,n){i=i||n||this,t=p(iu(t),this.getDimension,this),wg&&nu(this,t);var r=ru(this,t);r._indices=this._indices,r.getRawIndex=r._indices?tu:Jh;for(var a=r._storage,o=[],s=this._chunkSize,l=t.length,h=this.count(),u=[],c=r._rawExtent,d=0;h>d;d++){for(var f=0;l>f;f++)u[f]=this.get(t[f],d);u[l]=d;var g=e&&e.apply(i,u);if(null!=g){"object"!=typeof g&&(o[0]=g,g=o);for(var v=this.getRawIndex(d),m=Math.floor(v/s),y=v%s,x=0;x<g.length;x++){var _=t[x],w=g[x],b=c[_],M=a[_];M&&(M[m][y]=w),w<b[0]&&(b[0]=w),w>b[1]&&(b[1]=w)}}}return r},Ob.downSample=function(t,e,i,n){for(var r=ru(this,[t]),a=r._storage,o=[],s=Math.floor(1/e),l=a[t],h=this.count(),u=this._chunkSize,c=r._rawExtent[t],d=new(Uh(this))(h),f=0,p=0;h>p;p+=s){s>h-p&&(s=h-p,o.length=s);for(var g=0;s>g;g++){var v=this.getRawIndex(p+g),m=Math.floor(v/u),y=v%u;o[g]=l[m][y]}var x=i(o),_=this.getRawIndex(Math.min(p+n(o,x)||0,h-1)),w=Math.floor(_/u),b=_%u;l[w][b]=x,x<c[0]&&(c[0]=x),x>c[1]&&(c[1]=x),d[f++]=_}return r._count=f,r._indices=d,r.getRawIndex=tu,r},Ob.getItemModel=function(t){var e=this.hostModel;return new po(this.getRawDataItem(t),e,e&&e.ecModel)},Ob.diff=function(t){var e=this;return new Vh(t?t.getIndices():[],this.getIndices(),function(e){return eu(t,e)},function(t){return eu(e,t)})},Ob.getVisual=function(t){var e=this._visual;return e&&e[t]},Ob.setVisual=function(t,e){if(bb(t))for(var i in t)t.hasOwnProperty(i)&&this.setVisual(i,t[i]);else this._visual=this._visual||{},this._visual[t]=e},Ob.setLayout=function(t,e){if(bb(t))for(var i in t)t.hasOwnProperty(i)&&this.setLayout(i,t[i]);else this._layout[t]=e},Ob.getLayout=function(t){return this._layout[t]},Ob.getItemLayout=function(t){return this._itemLayouts[t]},Ob.setItemLayout=function(t,e,i){this._itemLayouts[t]=i?o(this._itemLayouts[t]||{},e):e},Ob.clearItemLayouts=function(){this._itemLayouts.length=0},Ob.getItemVisual=function(t,e,i){var n=this._itemVisuals[t],r=n&&n[e];return null!=r||i?r:this.getVisual(e)},Ob.setItemVisual=function(t,e,i){var n=this._itemVisuals[t]||{},r=this.hasItemVisual;if(this._itemVisuals[t]=n,bb(e))for(var a in e)e.hasOwnProperty(a)&&(n[a]=e[a],r[a]=!0);else n[e]=i,r[e]=!0},Ob.clearAllVisual=function(){this._visual={},this._itemVisuals=[],this.hasItemVisual={}};var Eb=function(t){t.seriesIndex=this.seriesIndex,t.dataIndex=this.dataIndex,t.dataType=this.dataType};Ob.setItemGraphicEl=function(t,e){var i=this.hostModel;e&&(e.dataIndex=t,e.dataType=this.dataType,e.seriesIndex=i&&i.seriesIndex,"group"===e.type&&e.traverse(Eb,e)),this._graphicEls[t]=e},Ob.getItemGraphicEl=function(t){return this._graphicEls[t]},Ob.eachItemGraphicEl=function(t,e){f(this._graphicEls,function(i,n){i&&t&&t.call(e,i,n)})},Ob.cloneShallow=function(t){if(!t){var e=p(this.dimensions,this.getDimensionInfo,this);t=new Lb(e,this.hostModel)}if(t._storage=this._storage,jh(t,this),this._indices){var i=this._indices.constructor;t._indices=new i(this._indices)}else t._indices=null;return t.getRawIndex=t._indices?tu:Jh,t},Ob.wrapMethod=function(t,e){var i=this[t];"function"==typeof i&&(this.__wrappedMethods=this.__wrappedMethods||[],this.__wrappedMethods.push(t),this[t]=function(){var t=i.apply(this,arguments);return e.apply(this,[t].concat(P(arguments)))})},Ob.TRANSFERABLE_METHODS=["cloneShallow","downSample","map"],Ob.CHANGABLE_METHODS=["filterSelf","selectRange"];var zb=function(t,e){return e=e||{},su(e.coordDimensions||[],t,{dimsDef:e.dimensionsDefine||t.dimensionsDefine,encodeDef:e.encodeDefine||t.encodeDefine,dimCount:e.dimensionsCount,encodeDefaulter:e.encodeDefaulter,generateCoord:e.generateCoord,generateCoordCount:e.generateCoordCount})},Bb={cartesian2d:function(t,e,i,n){var r=t.getReferringComponents("xAxis")[0],a=t.getReferringComponents("yAxis")[0];if(wg){if(!r)throw new Error('xAxis "'+C(t.get("xAxisIndex"),t.get("xAxisId"),0)+'" not found');if(!a)throw new Error('yAxis "'+C(t.get("xAxisIndex"),t.get("yAxisId"),0)+'" not found')}e.coordSysDims=["x","y"],i.set("x",r),i.set("y",a),du(r)&&(n.set("x",r),e.firstCategoryDimIndex=0),du(a)&&(n.set("y",a),null==e.firstCategoryDimIndex&(e.firstCategoryDimIndex=1))},singleAxis:function(t,e,i,n){var r=t.getReferringComponents("singleAxis")[0];if(wg&&!r)throw new Error("singleAxis should be specified.");e.coordSysDims=["single"],i.set("single",r),du(r)&&(n.set("single",r),e.firstCategoryDimIndex=0)},polar:function(t,e,i,n){var r=t.getReferringComponents("polar")[0],a=r.findAxisModel("radiusAxis"),o=r.findAxisModel("angleAxis");if(wg){if(!o)throw new Error("angleAxis option not found");if(!a)throw new Error("radiusAxis option not found")}e.coordSysDims=["radius","angle"],i.set("radius",a),i.set("angle",o),du(a)&&(n.set("radius",a),e.firstCategoryDimIndex=0),du(o)&&(n.set("angle",o),null==e.firstCategoryDimIndex&&(e.firstCategoryDimIndex=1))},geo:function(t,e){e.coordSysDims=["lng","lat"]},parallel:function(t,e,i,n){var r=t.ecModel,a=r.getComponent("parallel",t.get("parallelIndex")),o=e.coordSysDims=a.dimensions.slice();f(a.parallelAxisIndex,function(t,a){var s=r.getComponent("parallelAxis",t),l=o[a];i.set(l,s),du(s)&&null==e.firstCategoryDimIndex&&(n.set(l,s),e.firstCategoryDimIndex=a)})}};xu.prototype.parse=function(t){return t},xu.prototype.getSetting=function(t){return this._setting[t]},xu.prototype.contain=function(t){var e=this._extent;return t>=e[0]&&t<=e[1]},xu.prototype.normalize=function(t){var e=this._extent;return e[1]===e[0]?.5:(t-e[0])/(e[1]-e[0])},xu.prototype.scale=function(t){var e=this._extent;return t*(e[1]-e[0])+e[0]},xu.prototype.unionExtent=function(t){var e=this._extent;t[0]<e[0]&&(e[0]=t[0]),t[1]>e[1]&&(e[1]=t[1])},xu.prototype.unionExtentFromData=function(t,e){this.unionExtent(t.getApproximateExtent(e))},xu.prototype.getExtent=function(){return this._extent.slice()},xu.prototype.setExtent=function(t,e){var i=this._extent;isNaN(t)||(i[0]=t),isNaN(e)||(i[1]=e)},xu.prototype.isBlank=function(){return this._isBlank},xu.prototype.setBlank=function(t){this._isBlank=t},xu.prototype.getLabel=null,mr(xu),wr(xu,{registerWhenExtend:!0}),_u.createByAxisModel=function(t){var e=t.option,i=e.data,n=i&&p(i,bu);return new _u({categories:n,needCollect:!n,deduplication:e.dedplication!==!1})};var Rb=_u.prototype;Rb.getOrdinal=function(t){return wu(this).get(t)},Rb.parseAndCollect=function(t){var e,i=this._needCollect;if("string"!=typeof t&&!i)return t;if(i&&!this._deduplication)return e=this.categories.length,this.categories[e]=t,e;var n=wu(this);return e=n.get(t),null==e&&(i?(e=this.categories.length,this.categories[e]=t,n.set(t,e)):e=0/0),e};var Nb=xu.prototype,Fb=xu.extend({type:"ordinal",init:function(t,e){(!t||_(t))&&(t=new _u({categories:t})),this._ordinalMeta=t,this._extent=e||[0,t.categories.length-1]},parse:function(t){return"string"==typeof t?this._ordinalMeta.getOrdinal(t):Math.round(t)},contain:function(t){return t=this.parse(t),Nb.contain.call(this,t)&&null!=this._ordinalMeta.categories[t]},normalize:function(t){return Nb.normalize.call(this,this.parse(t))},scale:function(t){return Math.round(Nb.scale.call(this,t))},getTicks:function(){for(var t=[],e=this._extent,i=e[0];i<=e[1];)t.push(i),i++;return t},getLabel:function(t){return this.isBlank()?void 0:this._ordinalMeta.categories[t]},count:function(){return this._extent[1]-this._extent[0]+1},unionExtentFromData:function(t,e){this.unionExtent(t.getApproximateExtent(e))},getOrdinalMeta:function(){return this._ordinalMeta},niceTicks:V,niceExtent:V});Fb.create=function(){return new Fb};var Vb=Mo,Wb=Mo,Hb=xu.extend({type:"interval",_interval:0,_intervalPrecision:2,setExtent:function(t,e){var i=this._extent;isNaN(t)||(i[0]=parseFloat(t)),isNaN(e)||(i[1]=parseFloat(e))},unionExtent:function(t){var e=this._extent;t[0]<e[0]&&(e[0]=t[0]),t[1]>e[1]&&(e[1]=t[1]),Hb.prototype.setExtent.call(this,e[0],e[1])},getInterval:function(){return this._interval},setInterval:function(t){this._interval=t,this._niceExtent=this._extent.slice(),this._intervalPrecision=Su(t)},getTicks:function(t){var e=this._interval,i=this._extent,n=this._niceExtent,r=this._intervalPrecision,a=[];if(!e)return a;var o=1e4;i[0]<n[0]&&a.push(t?Wb(n[0]-e,r):i[0]);for(var s=n[0];s<=n[1]&&(a.push(s),s=Wb(s+e,r),s!==a[a.length-1]);)if(a.length>o)return[];var l=a.length?a[a.length-1]:n[1];return i[1]>l&&a.push(t?Wb(l+e,r):i[1]),a},getMinorTicks:function(t){for(var e=this.getTicks(!0),i=[],n=this.getExtent(),r=1;r<e.length;r++){for(var a=e[r],o=e[r-1],s=0,l=[],h=a-o,u=h/t;t-1>s;){var c=Mo(o+(s+1)*u);c>n[0]&&c<n[1]&&l.push(c),s++}i.push(l)}return i},getLabel:function(t,e){if(null==t)return"";var i=e&&e.precision;return null==i?i=Io(t)||0:"auto"===i&&(i=this._intervalPrecision),t=Wb(t,i,!0),No(t)},niceTicks:function(t,e,i){t=t||5;var n=this._extent,r=n[1]-n[0];if(isFinite(r)){0>r&&(r=-r,n.reverse());var a=Mu(n,t,e,i);this._intervalPrecision=a.intervalPrecision,this._interval=a.interval,this._niceExtent=a.niceTickExtent}},niceExtent:function(t){var e=this._extent;if(e[0]===e[1])if(0!==e[0]){var i=e[0];t.fixMax?e[0]-=i/2:(e[1]+=i/2,e[0]-=i/2)}else e[1]=1;var n=e[1]-e[0];isFinite(n)||(e[0]=0,e[1]=1),this.niceTicks(t.splitNumber,t.minInterval,t.maxInterval);var r=this._interval;t.fixMin||(e[0]=Wb(Math.floor(e[0]/r)*r)),t.fixMax||(e[1]=Wb(Math.ceil(e[1]/r)*r))}});Hb.create=function(){return new Hb};var Gb="__ec_stack_",Zb=.5,Xb="undefined"!=typeof Float32Array?Float32Array:Array,Yb={seriesType:"bar",plan:Q_(),reset:function(t){function e(t,e){for(var i,d=t.count,f=new Xb(2*d),p=new Xb(2*d),g=new Xb(d),v=[],m=[],y=0,x=0;null!=(i=t.next());)m[u]=e.get(s,i),m[1-u]=e.get(l,i),v=n.dataToPoint(m,null,v),p[y]=h?r.x+r.width:v[0],f[y++]=v[0],p[y]=h?v[1]:r.y+r.height,f[y++]=v[1],g[x++]=i;e.setLayout({largePoints:f,largeDataIndices:g,largeBackgroundPoints:p,barWidth:c,valueAxisStart:Ru(a,o,!1),backgroundStart:h?r.x:r.y,valueAxisHorizontal:h})}if(zu(t)&&Bu(t)){var i=t.getData(),n=t.coordinateSystem,r=n.grid.getRect(),a=n.getBaseAxis(),o=n.getOtherAxis(a),s=i.mapDimension(o.dim),l=i.mapDimension(a.dim),h=o.isHorizontal(),u=h?0:1,c=Ou(Pu([t]),a,t).width;return c>Zb||(c=Zb),{progress:e}}}},Ub=Hb.prototype,qb=Math.ceil,jb=Math.floor,Kb=1e3,$b=60*Kb,Qb=60*$b,Jb=24*Qb,tM=function(t,e,i,n){for(;n>i;){var r=i+n>>>1;t[r][1]<e?i=r+1:n=r}return i},eM=Hb.extend({type:"time",getLabel:function(t){var e=this._stepLvl,i=new Date(t);return Xo(e[0],i,this.getSetting("useUTC"))},niceExtent:function(t){var e=this._extent;if(e[0]===e[1]&&(e[0]-=Jb,e[1]+=Jb),e[1]===-1/0&&1/0===e[0]){var i=new Date;e[1]=+new Date(i.getFullYear(),i.getMonth(),i.getDate()),e[0]=e[1]-Jb}this.niceTicks(t.splitNumber,t.minInterval,t.maxInterval);var n=this._interval;t.fixMin||(e[0]=Mo(jb(e[0]/n)*n)),t.fixMax||(e[1]=Mo(qb(e[1]/n)*n))},niceTicks:function(t,e,i){t=t||10;var n=this._extent,r=n[1]-n[0],a=r/t;null!=e&&e>a&&(a=e),null!=i&&a>i&&(a=i);var o=iM.length,s=tM(iM,a,0,o),l=iM[Math.min(s,o-1)],h=l[1];if("year"===l[0]){var u=r/h,c=Eo(u/t,!0);h*=c}var d=this.getSetting("useUTC")?0:60*new Date(+n[0]||+n[1]).getTimezoneOffset()*1e3,f=[Math.round(qb((n[0]-d)/h)*h+d),Math.round(jb((n[1]-d)/h)*h+d)];Iu(f,n),this._stepLvl=l,this._interval=h,this._niceExtent=f},parse:function(t){return+Po(t)}});f(["contain","normalize"],function(t){eM.prototype[t]=function(e){return Ub[t].call(this,this.parse(e))}});var iM=[["hh:mm:ss",Kb],["hh:mm:ss",5*Kb],["hh:mm:ss",10*Kb],["hh:mm:ss",15*Kb],["hh:mm:ss",30*Kb],["hh:mm\nMM-dd",$b],["hh:mm\nMM-dd",5*$b],["hh:mm\nMM-dd",10*$b],["hh:mm\nMM-dd",15*$b],["hh:mm\nMM-dd",30*$b],["hh:mm\nMM-dd",Qb],["hh:mm\nMM-dd",2*Qb],["hh:mm\nMM-dd",6*Qb],["hh:mm\nMM-dd",12*Qb],["MM-dd\nyyyy",Jb],["MM-dd\nyyyy",2*Jb],["MM-dd\nyyyy",3*Jb],["MM-dd\nyyyy",4*Jb],["MM-dd\nyyyy",5*Jb],["MM-dd\nyyyy",6*Jb],["week",7*Jb],["MM-dd\nyyyy",10*Jb],["week",14*Jb],["week",21*Jb],["month",31*Jb],["week",42*Jb],["month",62*Jb],["week",70*Jb],["quarter",95*Jb],["month",31*Jb*4],["month",31*Jb*5],["half-year",380*Jb/2],["month",31*Jb*8],["month",31*Jb*10],["year",380*Jb]];eM.create=function(t){return new eM({useUTC:t.ecModel.get("useUTC")})};var nM=xu.prototype,rM=Hb.prototype,aM=Io,oM=Mo,sM=Math.floor,lM=Math.ceil,hM=Math.pow,uM=Math.log,cM=xu.extend({type:"log",base:10,$constructor:function(){xu.apply(this,arguments),this._originalScale=new Hb},getTicks:function(t){var e=this._originalScale,i=this._extent,n=e.getExtent();return p(rM.getTicks.call(this,t),function(t){var r=Mo(hM(this.base,t));return r=t===i[0]&&e.__fixMin?Nu(r,n[0]):r,r=t===i[1]&&e.__fixMax?Nu(r,n[1]):r},this)},getMinorTicks:rM.getMinorTicks,getLabel:rM.getLabel,scale:function(t){return t=nM.scale.call(this,t),hM(this.base,t)},setExtent:function(t,e){var i=this.base;t=uM(t)/uM(i),e=uM(e)/uM(i),rM.setExtent.call(this,t,e)},getExtent:function(){var t=this.base,e=nM.getExtent.call(this);e[0]=hM(t,e[0]),e[1]=hM(t,e[1]);var i=this._originalScale,n=i.getExtent();return i.__fixMin&&(e[0]=Nu(e[0],n[0])),i.__fixMax&&(e[1]=Nu(e[1],n[1])),e},unionExtent:function(t){this._originalScale.unionExtent(t);var e=this.base;t[0]=uM(t[0])/uM(e),t[1]=uM(t[1])/uM(e),nM.unionExtent.call(this,t)},unionExtentFromData:function(t,e){this.unionExtent(t.getApproximateExtent(e))},niceTicks:function(t){t=t||10;var e=this._extent,i=e[1]-e[0];if(!(1/0===i||0>=i)){var n=Lo(i),r=t/i*n;for(.5>=r&&(n*=10);!isNaN(n)&&Math.abs(n)<1&&Math.abs(n)>0;)n*=10;var a=[Mo(lM(e[0]/n)*n),Mo(sM(e[1]/n)*n)];this._interval=n,this._niceExtent=a}},niceExtent:function(t){rM.niceExtent.call(this,t);var e=this._originalScale;e.__fixMin=t.fixMin,e.__fixMax=t.fixMax}});f(["contain","normalize"],function(t){cM.prototype[t]=function(e){return e=uM(e)/uM(this.base),nM[t].call(this,e)}}),cM.create=function(){return new cM};var dM={getMin:function(t){var e=this.option,i=t||null==e.rangeStart?e.min:e.rangeStart;return this.axis&&null!=i&&"dataMin"!==i&&"function"!=typeof i&&!T(i)&&(i=this.axis.scale.parse(i)),i},getMax:function(t){var e=this.option,i=t||null==e.rangeEnd?e.max:e.rangeEnd;return this.axis&&null!=i&&"dataMax"!==i&&"function"!=typeof i&&!T(i)&&(i=this.axis.scale.parse(i)),i},getNeedCrossZero:function(){var t=this.option;return null!=t.rangeStart||null!=t.rangeEnd?!1:!t.scale},getCoordSysModel:V,setRange:function(t,e){this.option.rangeStart=t,this.option.rangeEnd=e},resetRange:function(){this.option.rangeStart=this.option.rangeEnd=null}},fM=ga({type:"triangle",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(t,e){var i=e.cx,n=e.cy,r=e.width/2,a=e.height/2;t.moveTo(i,n-a),t.lineTo(i+r,n+a),t.lineTo(i-r,n+a),t.closePath()}}),pM=ga({type:"diamond",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(t,e){var i=e.cx,n=e.cy,r=e.width/2,a=e.height/2;t.moveTo(i,n-a),t.lineTo(i+r,n),t.lineTo(i,n+a),t.lineTo(i-r,n),t.closePath()}}),gM=ga({type:"pin",shape:{x:0,y:0,width:0,height:0},buildPath:function(t,e){var i=e.x,n=e.y,r=e.width/5*3,a=Math.max(r,e.height),o=r/2,s=o*o/(a-o),l=n-a+o+s,h=Math.asin(s/o),u=Math.cos(h)*o,c=Math.sin(h),d=Math.cos(h),f=.6*o,p=.7*o;t.moveTo(i-u,l+s),t.arc(i,l,o,Math.PI-h,2*Math.PI+h),t.bezierCurveTo(i+u-c*f,l+s+d*f,i,n-p,i,n),t.bezierCurveTo(i,n-p,i-u+c*f,l+s+d*f,i-u,l+s),t.closePath()}}),vM=ga({type:"arrow",shape:{x:0,y:0,width:0,height:0},buildPath:function(t,e){var i=e.height,n=e.width,r=e.x,a=e.y,o=n/3*2;t.moveTo(r,a),t.lineTo(r+o,a+i),t.lineTo(r,a+i/4*3),t.lineTo(r-o,a+i),t.lineTo(r,a),t.closePath()}}),mM={line:px,rect:dx,roundRect:dx,square:dx,circle:ex,diamond:pM,pin:gM,arrow:vM,triangle:fM},yM={line:function(t,e,i,n,r){r.x1=t,r.y1=e+n/2,r.x2=t+i,r.y2=e+n/2},rect:function(t,e,i,n,r){r.x=t,r.y=e,r.width=i,r.height=n},roundRect:function(t,e,i,n,r){r.x=t,r.y=e,r.width=i,r.height=n,r.r=Math.min(i,n)/4},square:function(t,e,i,n,r){var a=Math.min(i,n);r.x=t,r.y=e,r.width=a,r.height=a},circle:function(t,e,i,n,r){r.cx=t+i/2,r.cy=e+n/2,r.r=Math.min(i,n)/2},diamond:function(t,e,i,n,r){r.cx=t+i/2,r.cy=e+n/2,r.width=i,r.height=n},pin:function(t,e,i,n,r){r.x=t+i/2,r.y=e+n/2,r.width=i,r.height=n},arrow:function(t,e,i,n,r){r.x=t+i/2,r.y=e+n/2,r.width=i,r.height=n},triangle:function(t,e,i,n,r){r.cx=t+i/2,r.cy=e+n/2,r.width=i,r.height=n}},xM={};f(mM,function(t,e){xM[e]=new t});var _M=ga({type:"symbol",shape:{symbolType:"",x:0,y:0,width:0,height:0},calculateTextPosition:function(t,e,i){var n=Ki(t,e,i),r=this.shape;return r&&"pin"===r.symbolType&&"inside"===e.textPosition&&(n.y=i.y+.4*i.height),n},buildPath:function(t,e,i){var n=e.symbolType;if("none"!==n){var r=xM[n];r||(n="rect",r=xM[n]),yM[n](e.x,e.y,e.width,e.height,r.shape),r.buildPath(t,r.shape,i)}}}),wM={isDimensionStacked:pu,enableDataStack:fu,getStackedDimension:gu},bM=(Object.freeze||Object)({createList:Qu,getLayoutRect:$o,dataStack:wM,createScale:Ju,mixinAxisModelCommonMethods:tc,completeDimensions:su,createDimensions:zb,createSymbol:$u}),MM=1e-8;nc.prototype={constructor:nc,properties:null,getBoundingRect:function(){var t=this._rect;if(t)return t;for(var e=Number.MAX_VALUE,i=[e,e],n=[-e,-e],r=[],a=[],o=this.geometries,s=0;s<o.length;s++)if("polygon"===o[s].type){var l=o[s].exterior;Br(l,r,a),oe(i,i,r),se(n,n,a)}return 0===s&&(i[0]=i[1]=n[0]=n[1]=0),this._rect=new Ii(i[0],i[1],n[0]-i[0],n[1]-i[1])},contain:function(t){var e=this.getBoundingRect(),i=this.geometries;if(!e.contain(t[0],t[1]))return!1;t:for(var n=0,r=i.length;r>n;n++)if("polygon"===i[n].type){var a=i[n].exterior,o=i[n].interiors;if(ic(a,t[0],t[1])){for(var s=0;s<(o?o.length:0);s++)if(ic(o[s]))continue t;return!0}}return!1},transformTo:function(t,e,i,n){var r=this.getBoundingRect(),a=r.width/r.height;i?n||(n=i/a):i=a*n;for(var o=new Ii(t,e,i,n),s=r.calculateTransform(o),l=this.geometries,h=0;h<l.length;h++)if("polygon"===l[h].type){for(var u=l[h].exterior,c=l[h].interiors,d=0;d<u.length;d++)ae(u[d],u[d],s);for(var f=0;f<(c?c.length:0);f++)for(var d=0;d<c[f].length;d++)ae(c[f][d],c[f][d],s)}r=this._rect,r.copy(o),this.center=[r.x+r.width/2,r.y+r.height/2]},cloneShallow:function(t){null==t&&(t=this.name);var e=new nc(t,this.geometries,this.center);return e._rect=this._rect,e.transformTo=null,e}};var SM=function(t,e){return rc(t),p(v(t.features,function(t){return t.geometry&&t.properties&&t.geometry.coordinates.length>0
}),function(t){var i=t.properties,n=t.geometry,r=n.coordinates,a=[];"Polygon"===n.type&&a.push({type:"polygon",exterior:r[0],interiors:r.slice(1)}),"MultiPolygon"===n.type&&f(r,function(t){t[0]&&a.push({type:"polygon",exterior:t[0],interiors:t.slice(1)})});var o=new nc(i[e||"name"],a,i.cp);return o.properties=i,o})},AM=hr(),IM=[0,1],TM=function(t,e,i){this.dim=t,this.scale=e,this._extent=i||[0,0],this.inverse=!1,this.onBand=!1};TM.prototype={constructor:TM,contain:function(t){var e=this._extent,i=Math.min(e[0],e[1]),n=Math.max(e[0],e[1]);return t>=i&&n>=t},containData:function(t){return this.scale.contain(t)},getExtent:function(){return this._extent.slice()},getPixelPrecision:function(t){return To(t||this.scale.getExtent(),this._extent)},setExtent:function(t,e){var i=this._extent;i[0]=t,i[1]=e},dataToCoord:function(t,e){var i=this._extent,n=this.scale;return t=n.normalize(t),this.onBand&&"ordinal"===n.type&&(i=i.slice(),_c(i,n.count())),wo(t,IM,i,e)},coordToData:function(t,e){var i=this._extent,n=this.scale;this.onBand&&"ordinal"===n.type&&(i=i.slice(),_c(i,n.count()));var r=wo(t,i,IM,e);return this.scale.scale(r)},pointToData:function(){},getTicksCoords:function(t){t=t||{};var e=t.tickModel||this.getTickModel(),i=sc(this,e),n=i.ticks,r=p(n,function(t){return{coord:this.dataToCoord(t),tickValue:t}},this),a=e.get("alignWithLabel");return wc(this,r,a,t.clamp),r},getMinorTicksCoords:function(){if("ordinal"===this.scale.type)return[];var t=this.model.getModel("minorTick"),e=t.get("splitNumber");e>0&&100>e||(e=5);var i=this.scale.getMinorTicks(e),n=p(i,function(t){return p(t,function(t){return{coord:this.dataToCoord(t),tickValue:t}},this)},this);return n},getViewLabels:function(){return oc(this).labels},getLabelModel:function(){return this.model.getModel("axisLabel")},getTickModel:function(){return this.model.getModel("axisTick")},getBandWidth:function(){var t=this._extent,e=this.scale.getExtent(),i=e[1]-e[0]+(this.onBand?1:0);0===i&&(i=1);var n=Math.abs(t[1]-t[0]);return Math.abs(n)/i},isHorizontal:null,getRotate:null,calculateCategoryInterval:function(){return vc(this)}};var CM=SM,DM={};f(["map","each","filter","indexOf","inherits","reduce","filter","bind","curry","isArray","isString","isObject","isFunction","extend","defaults","clone","merge"],function(t){DM[t]=Fg[t]});var kM={};f(["extendShape","extendPath","makePath","makeImage","mergePath","resizePath","createIcon","setHoverStyle","setLabelStyle","setTextStyle","setText","getFont","updateProps","initProps","getTransform","clipPointsByRect","clipRectByRect","registerShape","getShapeClass","Group","Image","Text","Circle","Sector","Ring","Polygon","Polyline","Rect","Line","BezierCurve","Arc","IncrementalDisplayable","CompoundPath","LinearGradient","RadialGradient","BoundingRect"],function(t){kM[t]=Nx[t]});var PM=function(t){this._axes={},this._dimList=[],this.name=t||""};PM.prototype={constructor:PM,type:"cartesian",getAxis:function(t){return this._axes[t]},getAxes:function(){return p(this._dimList,bc,this)},getAxesByScale:function(t){return t=t.toLowerCase(),v(this.getAxes(),function(e){return e.scale.type===t})},addAxis:function(t){var e=t.dim;this._axes[e]=t,this._dimList.push(e)},dataToCoord:function(t){return this._dataCoordConvert(t,"dataToCoord")},coordToData:function(t){return this._dataCoordConvert(t,"coordToData")},_dataCoordConvert:function(t,e){for(var i=this._dimList,n=t instanceof Array?[]:{},r=0;r<i.length;r++){var a=i[r],o=this._axes[a];n[a]=o[e](t[a])}return n}},Mc.prototype={constructor:Mc,type:"cartesian2d",dimensions:["x","y"],getBaseAxis:function(){return this.getAxesByScale("ordinal")[0]||this.getAxesByScale("time")[0]||this.getAxis("x")},containPoint:function(t){var e=this.getAxis("x"),i=this.getAxis("y");return e.contain(e.toLocalCoord(t[0]))&&i.contain(i.toLocalCoord(t[1]))},containData:function(t){return this.getAxis("x").containData(t[0])&&this.getAxis("y").containData(t[1])},dataToPoint:function(t,e,i){var n=this.getAxis("x"),r=this.getAxis("y");return i=i||[],i[0]=n.toGlobalCoord(n.dataToCoord(t[0])),i[1]=r.toGlobalCoord(r.dataToCoord(t[1])),i},clampData:function(t,e){var i=this.getAxis("x").scale,n=this.getAxis("y").scale,r=i.getExtent(),a=n.getExtent(),o=i.parse(t[0]),s=n.parse(t[1]);return e=e||[],e[0]=Math.min(Math.max(Math.min(r[0],r[1]),o),Math.max(r[0],r[1])),e[1]=Math.min(Math.max(Math.min(a[0],a[1]),s),Math.max(a[0],a[1])),e},pointToData:function(t,e){var i=this.getAxis("x"),n=this.getAxis("y");return e=e||[],e[0]=i.coordToData(i.toLocalCoord(t[0])),e[1]=n.coordToData(n.toLocalCoord(t[1])),e},getOtherAxis:function(t){return this.getAxis("x"===t.dim?"y":"x")},getArea:function(){var t=this.getAxis("x").getGlobalExtent(),e=this.getAxis("y").getGlobalExtent(),i=Math.min(t[0],t[1]),n=Math.min(e[0],e[1]),r=Math.max(t[0],t[1])-i,a=Math.max(e[0],e[1])-n,o=new Ii(i,n,r,a);return o}},u(Mc,PM);var LM=function(t,e,i,n,r){TM.call(this,t,e,i),this.type=n||"value",this.position=r||"bottom"};LM.prototype={constructor:LM,index:0,getAxesOnZeroOf:null,model:null,isHorizontal:function(){var t=this.position;return"top"===t||"bottom"===t},getGlobalExtent:function(t){var e=this.getExtent();return e[0]=this.toGlobalCoord(e[0]),e[1]=this.toGlobalCoord(e[1]),t&&e[0]>e[1]&&e.reverse(),e},getOtherAxis:function(){this.grid.getOtherAxis()},pointToData:function(t,e){return this.coordToData(this.toLocalCoord(t["x"===this.dim?0:1]),e)},toLocalCoord:null,toGlobalCoord:null},u(LM,TM);var OM={show:!0,zlevel:0,z:0,inverse:!1,name:"",nameLocation:"end",nameRotate:null,nameTruncate:{maxWidth:null,ellipsis:"...",placeholder:"."},nameTextStyle:{},nameGap:15,silent:!1,triggerEvent:!1,tooltip:{show:!1},axisPointer:{},axisLine:{show:!0,onZero:!0,onZeroAxisIndex:null,lineStyle:{color:"#333",width:1,type:"solid"},symbol:["none","none"],symbolSize:[10,15]},axisTick:{show:!0,inside:!1,length:5,lineStyle:{width:1}},axisLabel:{show:!0,inside:!1,rotate:0,showMinLabel:null,showMaxLabel:null,margin:8,fontSize:12},splitLine:{show:!0,lineStyle:{color:["#ccc"],width:1,type:"solid"}},splitArea:{show:!1,areaStyle:{color:["rgba(250,250,250,0.3)","rgba(200,200,200,0.3)"]}}},EM={};EM.categoryAxis=r({boundaryGap:!0,deduplication:null,splitLine:{show:!1},axisTick:{alignWithLabel:!1,interval:"auto"},axisLabel:{interval:"auto"}},OM),EM.valueAxis=r({boundaryGap:[0,0],splitNumber:5,minorTick:{show:!1,splitNumber:5,length:3,lineStyle:{}},minorSplitLine:{show:!1,lineStyle:{color:"#eee",width:1}}},OM),EM.timeAxis=s({scale:!0,min:"dataMin",max:"dataMax"},EM.valueAxis),EM.logAxis=s({scale:!0,logBase:10},EM.valueAxis);var zM=["value","category","time","log"],BM=function(t,e,i,n){f(zM,function(o){e.extend({type:t+"Axis."+o,mergeDefaultAndTheme:function(e,n){var a=this.layoutMode,s=a?Jo(e):{},l=n.getTheme();r(e,l.get(o+"Axis")),r(e,this.getDefaultOption()),e.type=i(t,e),a&&Qo(e,s,a)},optionUpdated:function(){var t=this.option;"category"===t.type&&(this.__ordinalMeta=_u.createByAxisModel(this))},getCategories:function(t){var e=this.option;return"category"===e.type?t?e.data:this.__ordinalMeta.categories:void 0},getOrdinalMeta:function(){return this.__ordinalMeta},defaultOption:a([{},EM[o+"Axis"],n],!0)})}),l_.registerSubTypeDefaulter(t+"Axis",x(i,t))},RM=l_.extend({type:"cartesian2dAxis",axis:null,init:function(){RM.superApply(this,"init",arguments),this.resetRange()},mergeOption:function(){RM.superApply(this,"mergeOption",arguments),this.resetRange()},restoreData:function(){RM.superApply(this,"restoreData",arguments),this.resetRange()},getCoordSysModel:function(){return this.ecModel.queryComponents({mainType:"grid",index:this.option.gridIndex,id:this.option.gridId})[0]}});r(RM.prototype,dM);var NM={offset:0};BM("x",RM,Sc,NM),BM("y",RM,Sc,NM),l_.extend({type:"grid",dependencies:["xAxis","yAxis"],layoutMode:"box",coordinateSystem:null,defaultOption:{show:!1,zlevel:0,z:0,left:"10%",top:60,right:"10%",bottom:60,containLabel:!1,backgroundColor:"rgba(0,0,0,0)",borderWidth:1,borderColor:"#ccc"}});var FM=Ic.prototype;FM.type="grid",FM.axisPointerEnabled=!0,FM.getRect=function(){return this._rect},FM.update=function(t,e){var i=this._axesMap;this._updateScale(t,this.model),f(i.x,function(t){Wu(t.scale,t.model)}),f(i.y,function(t){Wu(t.scale,t.model)});var n={};f(i.x,function(t){Tc(i,"y",t,n)}),f(i.y,function(t){Tc(i,"x",t,n)}),this.resize(this.model,e)},FM.resize=function(t,e,i){function n(){f(a,function(t){var e=t.isHorizontal(),i=e?[0,r.width]:[0,r.height],n=t.inverse?1:0;t.setExtent(i[n],i[1-n]),Dc(t,e?r.x:r.y)})}var r=$o(t.getBoxLayoutParams(),{width:e.getWidth(),height:e.getHeight()});this._rect=r;var a=this._axesList;n(),!i&&t.get("containLabel")&&(f(a,function(t){if(!t.model.get("axisLabel.inside")){var e=Yu(t);if(e){var i=t.isHorizontal()?"height":"width",n=t.model.get("axisLabel.margin");r[i]-=e[i]+n,"top"===t.position?r.y+=e.height+n:"left"===t.position&&(r.x+=e.width+n)}}}),n())},FM.getAxis=function(t,e){var i=this._axesMap[t];if(null!=i){if(null==e)for(var n in i)if(i.hasOwnProperty(n))return i[n];return i[e]}},FM.getAxes=function(){return this._axesList.slice()},FM.getCartesian=function(t,e){if(null!=t&&null!=e){var i="x"+t+"y"+e;return this._coordsMap[i]}M(t)&&(e=t.yAxisIndex,t=t.xAxisIndex);for(var n=0,r=this._coordsList;n<r.length;n++)if(r[n].getAxis("x").index===t||r[n].getAxis("y").index===e)return r[n]},FM.getCartesians=function(){return this._coordsList.slice()},FM.convertToPixel=function(t,e,i){var n=this._findConvertTarget(t,e);return n.cartesian?n.cartesian.dataToPoint(i):n.axis?n.axis.toGlobalCoord(n.axis.dataToCoord(i)):null},FM.convertFromPixel=function(t,e,i){var n=this._findConvertTarget(t,e);return n.cartesian?n.cartesian.pointToData(i):n.axis?n.axis.coordToData(n.axis.toLocalCoord(i)):null},FM._findConvertTarget=function(t,e){var i,n,r=e.seriesModel,a=e.xAxisModel||r&&r.getReferringComponents("xAxis")[0],o=e.yAxisModel||r&&r.getReferringComponents("yAxis")[0],s=e.gridModel,l=this._coordsList;if(r)i=r.coordinateSystem,h(l,i)<0&&(i=null);else if(a&&o)i=this.getCartesian(a.componentIndex,o.componentIndex);else if(a)n=this.getAxis("x",a.componentIndex);else if(o)n=this.getAxis("y",o.componentIndex);else if(s){var u=s.coordinateSystem;u===this&&(i=this._coordsList[0])}return{cartesian:i,axis:n}},FM.containPoint=function(t){var e=this._coordsList[0];return e?e.containPoint(t):void 0},FM._initCartesian=function(t,e){function i(i){return function(o,s){if(Ac(o,t,e)){var l=o.get("position");"x"===i?"top"!==l&&"bottom"!==l&&(l=n.bottom?"top":"bottom"):"left"!==l&&"right"!==l&&(l=n.left?"right":"left"),n[l]=!0;var h=new LM(i,Hu(o),[0,0],o.get("type"),l),u="category"===h.type;h.onBand=u&&o.get("boundaryGap"),h.inverse=o.get("inverse"),o.axis=h,h.model=o,h.grid=this,h.index=s,this._axesList.push(h),r[i][s]=h,a[i]++}}}var n={left:!1,right:!1,top:!1,bottom:!1},r={x:{},y:{}},a={x:0,y:0};return e.eachComponent("xAxis",i("x"),this),e.eachComponent("yAxis",i("y"),this),a.x&&a.y?(this._axesMap=r,void f(r.x,function(e,i){f(r.y,function(n,r){var a="x"+i+"y"+r,o=new Mc(a);o.grid=this,o.model=t,this._coordsMap[a]=o,this._coordsList.push(o),o.addAxis(e),o.addAxis(n)},this)},this)):(this._axesMap={},void(this._axesList=[]))},FM._updateScale=function(t,e){function i(t,e){f(t.mapDimension(e.dim,!0),function(i){e.scale.unionExtentFromData(t,gu(t,i))})}f(this._axesList,function(t){t.scale.setExtent(1/0,-1/0)}),t.eachSeries(function(n){if(Pc(n)){var r=kc(n,t),a=r[0],o=r[1];if(!Ac(a,e,t)||!Ac(o,e,t))return;var s=this.getCartesian(a.componentIndex,o.componentIndex),l=n.getData(),h=s.getAxis("x"),u=s.getAxis("y");"list"===l.type&&(i(l,h,n),i(l,u,n))}},this)},FM.getTooltipAxes=function(t){var e=[],i=[];return f(this.getCartesians(),function(n){var r=null!=t&&"auto"!==t?n.getAxis(t):n.getBaseAxis(),a=n.getOtherAxis(r);h(e,r)<0&&e.push(r),h(i,a)<0&&i.push(a)}),{baseAxes:e,otherAxes:i}};var VM=["xAxis","yAxis"];Ic.create=function(t,e){var i=[];return t.eachComponent("grid",function(n,r){var a=new Ic(n,t,e);a.name="grid_"+r,a.resize(n,e,!0),n.coordinateSystem=a,i.push(a)}),t.eachSeries(function(e){if(Pc(e)){var i=kc(e,t),n=i[0],r=i[1],a=n.getCoordSysModel();if(wg){if(!a)throw new Error('Grid "'+C(n.get("gridIndex"),n.get("gridId"),0)+'" not found');if(n.getCoordSysModel()!==r.getCoordSysModel())throw new Error("xAxis and yAxis must use the same grid")}var o=a.coordinateSystem;e.coordinateSystem=o.getCartesian(n.componentIndex,r.componentIndex)}}),i},Ic.dimensions=Ic.prototype.dimensions=Mc.prototype.dimensions,Is.register("cartesian2d",Ic);var WM=j_.extend({type:"series.__base_bar__",getInitialData:function(){return vu(this.getSource(),this,{useEncodeDefaulter:!0})},getMarkerPosition:function(t){var e=this.coordinateSystem;if(e){var i=e.dataToPoint(e.clampData(t)),n=this.getData(),r=n.getLayout("offset"),a=n.getLayout("size"),o=e.getBaseAxis().isHorizontal()?0:1;return i[o]+=r+a/2,i}return[0/0,0/0]},defaultOption:{zlevel:0,z:2,coordinateSystem:"cartesian2d",legendHoverLink:!0,barMinHeight:0,barMinAngle:0,large:!1,largeThreshold:400,progressive:3e3,progressiveChunkMode:"mod",itemStyle:{},emphasis:{}}});WM.extend({type:"series.bar",dependencies:["grid","polar"],brushSelector:"rect",getProgressive:function(){return this.get("large")?this.get("progressive"):!1},getProgressiveThreshold:function(){var t=this.get("progressiveThreshold"),e=this.get("largeThreshold");return e>t&&(t=e),t},defaultOption:{clip:!0,roundCap:!1,showBackground:!1,backgroundStyle:{color:"rgba(180, 180, 180, 0.2)",borderColor:null,borderWidth:0,borderType:"solid",borderRadius:0,shadowBlur:0,shadowColor:null,shadowOffsetX:0,shadowOffsetY:0,opacity:1}}});var HM=Um([["fill","color"],["stroke","borderColor"],["lineWidth","borderWidth"],["stroke","barBorderColor"],["lineWidth","barBorderWidth"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"]]),GM={getBarItemStyle:function(t){var e=HM(this,t);if(this.getBorderLineDash){var i=this.getBorderLineDash();i&&(e.lineDash=i)}return e}},ZM=ga({type:"sausage",shape:{cx:0,cy:0,r0:0,r:0,startAngle:0,endAngle:2*Math.PI,clockwise:!0},buildPath:function(t,e){var i=e.cx,n=e.cy,r=Math.max(e.r0||0,0),a=Math.max(e.r,0),o=.5*(a-r),s=r+o,l=e.startAngle,h=e.endAngle,u=e.clockwise,c=Math.cos(l),d=Math.sin(l),f=Math.cos(h),p=Math.sin(h),g=u?h-l<2*Math.PI:l-h<2*Math.PI;g&&(t.moveTo(c*r+i,d*r+n),t.arc(c*s+i,d*s+n,o,-Math.PI+l,l,!u)),t.arc(i,n,a,l,h,!u),t.moveTo(f*a+i,p*a+n),t.arc(f*s+i,p*s+n,o,h-2*Math.PI,h-Math.PI,!u),0!==r&&(t.arc(i,n,r,h,l,u),t.moveTo(c*r+i,p*r+n)),t.closePath()}}),XM=["itemStyle","barBorderWidth"],YM=[0,0];o(po.prototype,GM),zh({type:"bar",render:function(t,e,i){this._updateDrawMode(t);var n=t.get("coordinateSystem");return"cartesian2d"===n||"polar"===n?this._isLargeDraw?this._renderLarge(t,e,i):this._renderNormal(t,e,i):wg&&console.warn("Only cartesian2d and polar supported for bar."),this.group},incrementalPrepareRender:function(t){this._clear(),this._updateDrawMode(t)},incrementalRender:function(t,e){this._incrementalRenderLarge(t,e)},_updateDrawMode:function(t){var e=t.pipelineContext.large;(null==this._isLargeDraw||e^this._isLargeDraw)&&(this._isLargeDraw=e,this._clear())},_renderNormal:function(t){var e,i=this.group,n=t.getData(),r=this._data,a=t.coordinateSystem,o=a.getBaseAxis();"cartesian2d"===a.type?e=o.isHorizontal():"polar"===a.type&&(e="angle"===o.dim);var s=t.isAnimationEnabled()?t:null,l=t.get("clip",!0),h=Nc(a,n);i.removeClipPath();var u=t.get("roundCap",!0),c=t.get("showBackground",!0),d=t.getModel("backgroundStyle"),f=d.get("barBorderRadius")||0,p=[],g=this._backgroundEls||[],v=function(t){var i=$M[a.type](n,t),r=jc(a,e,i);return r.useStyle(d.getBarItemStyle()),"cartesian2d"===a.type&&r.setShape("r",f),p[t]=r,r};n.diff(r).add(function(r){var o=n.getItemModel(r),d=$M[a.type](n,r,o);if(c&&v(r),n.hasValue(r)){if(l){var f=jM[a.type](h,d);if(f)return void i.remove(p)}var p=KM[a.type](r,d,e,s,!1,u);n.setItemGraphicEl(r,p),i.add(p),Hc(p,n,r,o,d,t,e,"polar"===a.type)}}).update(function(o,m){var y=n.getItemModel(o),x=$M[a.type](n,o,y);if(c){var _;0===g.length?_=v(m):(_=g[m],_.useStyle(d.getBarItemStyle()),"cartesian2d"===a.type&&_.setShape("r",f),p[o]=_);var w=$M[a.type](n,o),b=qc(e,w,a);to(_,{shape:b},s,o)}var M=r.getItemGraphicEl(m);if(!n.hasValue(o))return void i.remove(M);if(l){var S=jM[a.type](h,x);if(S)return void i.remove(M)}M?to(M,{shape:x},s,o):M=KM[a.type](o,x,e,s,!0,u),n.setItemGraphicEl(o,M),i.add(M),Hc(M,n,o,y,x,t,e,"polar"===a.type)}).remove(function(t){var e=r.getItemGraphicEl(t);"cartesian2d"===a.type?e&&Fc(t,s,e):e&&Vc(t,s,e)}).execute();var m=this._backgroundGroup||(this._backgroundGroup=new Fv);m.removeAll();for(var y=0;y<p.length;++y)m.add(p[y]);i.add(m),this._backgroundEls=p,this._data=n},_renderLarge:function(t){this._clear(),Zc(t,this.group);var e=t.get("clip",!0)?Rc(t.coordinateSystem,!1,t):null;e?this.group.setClipPath(e):this.group.removeClipPath()},_incrementalRenderLarge:function(t,e){this._removeBackground(),Zc(e,this.group,!0)},dispose:V,remove:function(t){this._clear(t)},_clear:function(t){var e=this.group,i=this._data;t&&t.get("animation")&&i&&!this._isLargeDraw?(this._removeBackground(),this._backgroundEls=[],i.eachItemGraphicEl(function(e){"sector"===e.type?Vc(e.dataIndex,t,e):Fc(e.dataIndex,t,e)})):e.removeAll(),this._data=null},_removeBackground:function(){this.group.remove(this._backgroundGroup),this._backgroundGroup=null}});var UM=Math.max,qM=Math.min,jM={cartesian2d:function(t,e){var i=e.width<0?-1:1,n=e.height<0?-1:1;0>i&&(e.x+=e.width,e.width=-e.width),0>n&&(e.y+=e.height,e.height=-e.height);var r=UM(e.x,t.x),a=qM(e.x+e.width,t.x+t.width),o=UM(e.y,t.y),s=qM(e.y+e.height,t.y+t.height);e.x=r,e.y=o,e.width=a-r,e.height=s-o;var l=e.width<0||e.height<0;return 0>i&&(e.x+=e.width,e.width=-e.width),0>n&&(e.y+=e.height,e.height=-e.height),l},polar:function(t,e){var i=e.r0<=e.r?1:-1;if(0>i){var n=e.r;e.r=e.r0,e.r0=n}var n=qM(e.r,t.r),r=UM(e.r0,t.r0);e.r=n,e.r0=r;var a=0>n-r;if(0>i){var n=e.r;e.r=e.r0,e.r0=n}return a}},KM={cartesian2d:function(t,e,i,n,r){var a=new dx({shape:o({},e),z2:1});if(a.name="item",n){var s=a.shape,l=i?"height":"width",h={};s[l]=0,h[l]=e[l],Nx[r?"updateProps":"initProps"](a,{shape:h},n,t)}return a},polar:function(t,e,i,n,r,a){var o=e.startAngle<e.endAngle,l=!i&&a?ZM:rx,h=new l({shape:s({clockwise:o},e),z2:1});if(h.name="item",n){var u=h.shape,c=i?"r":"endAngle",d={};u[c]=i?0:e.startAngle,d[c]=e[c],Nx[r?"updateProps":"initProps"](h,{shape:d},n,t)}return h}},$M={cartesian2d:function(t,e,i){var n=t.getItemLayout(e),r=i?Gc(i,n):0,a=n.width>0?1:-1,o=n.height>0?1:-1;return{x:n.x+a*r/2,y:n.y+o*r/2,width:n.width-a*r,height:n.height-o*r}},polar:function(t,e){var i=t.getItemLayout(e);return{cx:i.cx,cy:i.cy,r0:i.r0,r:i.r,startAngle:i.startAngle,endAngle:i.endAngle}}},QM=ea.extend({type:"largeBar",shape:{points:[]},buildPath:function(t,e){for(var i=e.points,n=this.__startPoint,r=this.__baseDimIdx,a=0;a<i.length;a+=2)n[r]=i[a+r],t.moveTo(n[0],n[1]),t.lineTo(i[a],i[a+1])}}),JM=ml(function(t){var e=this,i=Xc(e,t.offsetX,t.offsetY);e.dataIndex=i>=0?i:null},30,!1),tS=Math.PI,eS=function(t,e){this.opt=e,this.axisModel=t,s(e,{labelOffset:0,nameDirection:1,tickDirection:1,labelDirection:1,silent:!0}),this.group=new Fv;var i=new Fv({position:e.position.slice(),rotation:e.rotation});i.updateTransform(),this._transform=i.transform,this._dumbGroup=i};eS.prototype={constructor:eS,hasBuilder:function(t){return!!iS[t]},add:function(t){iS[t].call(this)},getGroup:function(){return this.group}};var iS={axisLine:function(){var t=this.opt,e=this.axisModel;if(e.get("axisLine.show")){var i=this.axisModel.axis.getExtent(),n=this._transform,r=[i[0],0],a=[i[1],0];n&&(ae(r,r,n),ae(a,a,n));var s=o({lineCap:"round"},e.getModel("axisLine.lineStyle").getLineStyle());this.group.add(new px({anid:"line",subPixelOptimize:!0,shape:{x1:r[0],y1:r[1],x2:a[0],y2:a[1]},style:s,strokeContainThreshold:t.strokeContainThreshold||5,silent:!0,z2:1}));var l=e.get("axisLine.symbol"),h=e.get("axisLine.symbolSize"),u=e.get("axisLine.symbolOffset")||0;if("number"==typeof u&&(u=[u,u]),null!=l){"string"==typeof l&&(l=[l,l]),("string"==typeof h||"number"==typeof h)&&(h=[h,h]);var c=h[0],d=h[1];f([{rotate:t.rotation+Math.PI/2,offset:u[0],r:0},{rotate:t.rotation-Math.PI/2,offset:u[1],r:Math.sqrt((r[0]-a[0])*(r[0]-a[0])+(r[1]-a[1])*(r[1]-a[1]))}],function(e,i){if("none"!==l[i]&&null!=l[i]){var n=$u(l[i],-c/2,-d/2,c,d,s.stroke,!0),a=e.r+e.offset,o=[r[0]+a*Math.cos(t.rotation),r[1]-a*Math.sin(t.rotation)];n.attr({rotation:e.rotate,position:o,silent:!0,z2:11}),this.group.add(n)}},this)}}},axisTickLabel:function(){var t=this.axisModel,e=this.opt,i=id(this,t,e),n=rd(this,t,e);$c(t,n,i),nd(this,t,e)},axisName:function(){var t=this.opt,e=this.axisModel,i=C(t.axisName,e.get("name"));if(i){var n,r=e.get("nameLocation"),a=t.nameDirection,s=e.getModel("nameTextStyle"),l=e.get("nameGap")||0,h=this.axisModel.axis.getExtent(),u=h[0]>h[1]?-1:1,c=["start"===r?h[0]-u*l:"end"===r?h[1]+u*l:(h[0]+h[1])/2,td(r)?t.labelOffset+a*l:0],d=e.get("nameRotate");null!=d&&(d=d*tS/180);var f;td(r)?n=rS(t.rotation,null!=d?d:t.rotation,a):(n=Kc(t,r,d||0,h),f=t.axisNameAvailableWidth,null!=f&&(f=Math.abs(f/Math.sin(n.rotation)),!isFinite(f)&&(f=null)));var p=s.getFont(),g=e.get("nameTruncate",!0)||{},v=g.ellipsis,m=C(t.nameTruncateMaxWidth,g.maxWidth,f),y=null!=v&&null!=m?e_(i,m,p,v,{minChar:2,placeholder:g.placeholder}):i,x=e.get("tooltip",!0),_=e.mainType,w={componentType:_,name:i,$vars:["name"]};w[_+"Index"]=e.componentIndex;var b=new tx({anid:"name",__fullText:i,__truncatedText:y,position:c,rotation:n.rotation,silent:aS(e),z2:1,tooltip:x&&x.show?o({content:i,formatter:function(){return i},formatterParams:w},x):null});Za(b.style,s,{text:y,textFont:p,textFill:s.getTextColor()||e.get("axisLine.lineStyle.color"),textAlign:s.get("align")||n.textAlign,textVerticalAlign:s.get("verticalAlign")||n.textVerticalAlign}),e.get("triggerEvent")&&(b.eventData=nS(e),b.eventData.targetType="axisName",b.eventData.name=i),this._dumbGroup.add(b),b.updateTransform(),this.group.add(b),b.decomposeTransform()}}},nS=eS.makeAxisEventDataBase=function(t){var e={componentType:t.mainType,componentIndex:t.componentIndex};return e[t.mainType+"Index"]=t.componentIndex,e},rS=eS.innerTextLayout=function(t,e,i){var n,r,a=Do(e-t);return ko(a)?(r=i>0?"top":"bottom",n="center"):ko(a-tS)?(r=i>0?"bottom":"top",n="center"):(r="middle",n=a>0&&tS>a?i>0?"right":"left":i>0?"left":"right"),{rotation:a,textAlign:n,textVerticalAlign:r}},aS=eS.isLabelSilent=function(t){var e=t.get("tooltip");return t.get("silent")||!(t.get("triggerEvent")||e&&e.show)},oS=f,sS=x,lS=Oh({type:"axis",_axisPointer:null,axisPointerClass:null,render:function(t,e,i,n){this.axisPointerClass&&cd(t),lS.superApply(this,"render",arguments),vd(this,t,e,i,n,!0)},updateAxisPointer:function(t,e,i,n){vd(this,t,e,i,n,!1)},remove:function(t,e){var i=this._axisPointer;i&&i.remove(e),lS.superApply(this,"remove",arguments)},dispose:function(t,e){md(this,e),lS.superApply(this,"dispose",arguments)}}),hS=[];lS.registerAxisPointerClass=function(t,e){if(wg&&hS[t])throw new Error("axisPointer "+t+" exists");hS[t]=e},lS.getAxisPointerClass=function(t){return t&&hS[t]};var uS=["axisLine","axisTickLabel","axisName"],cS=["splitArea","splitLine","minorSplitLine"],dS=lS.extend({type:"cartesianAxis",axisPointerClass:"CartesianAxisPointer",render:function(t,e,i,n){this.group.removeAll();var r=this._axisGroup;if(this._axisGroup=new Fv,this.group.add(this._axisGroup),t.get("show")){var a=t.getCoordSysModel(),o=yd(a,t),s=new eS(t,o);f(uS,s.add,s),this._axisGroup.add(s.getGroup()),f(cS,function(e){t.get(e+".show")&&this["_"+e](t,a)},this),ao(r,this._axisGroup,t),dS.superCall(this,"render",t,e,i,n)}},remove:function(){_d(this)},_splitLine:function(t,e){var i=t.axis;if(!i.scale.isBlank()){var n=t.getModel("splitLine"),r=n.getModel("lineStyle"),a=r.get("color");a=_(a)?a:[a];for(var o=e.coordinateSystem.getRect(),l=i.isHorizontal(),h=0,u=i.getTicksCoords({tickModel:n}),c=[],d=[],f=r.getLineStyle(),p=0;p<u.length;p++){var g=i.toGlobalCoord(u[p].coord);l?(c[0]=g,c[1]=o.y,d[0]=g,d[1]=o.y+o.height):(c[0]=o.x,c[1]=g,d[0]=o.x+o.width,d[1]=g);var v=h++%a.length,m=u[p].tickValue;this._axisGroup.add(new px({anid:null!=m?"line_"+u[p].tickValue:null,subPixelOptimize:!0,shape:{x1:c[0],y1:c[1],x2:d[0],y2:d[1]},style:s({stroke:a[v]},f),silent:!0}))}}},_minorSplitLine:function(t,e){var i=t.axis,n=t.getModel("minorSplitLine"),r=n.getModel("lineStyle"),a=e.coordinateSystem.getRect(),o=i.isHorizontal(),s=i.getMinorTicksCoords();if(s.length)for(var l=[],h=[],u=r.getLineStyle(),c=0;c<s.length;c++)for(var d=0;d<s[c].length;d++){var f=i.toGlobalCoord(s[c][d].coord);o?(l[0]=f,l[1]=a.y,h[0]=f,h[1]=a.y+a.height):(l[0]=a.x,l[1]=f,h[0]=a.x+a.width,h[1]=f),this._axisGroup.add(new px({anid:"minor_line_"+s[c][d].tickValue,subPixelOptimize:!0,shape:{x1:l[0],y1:l[1],x2:h[0],y2:h[1]},style:u,silent:!0}))}},_splitArea:function(t,e){xd(this,this._axisGroup,t,e)}});dS.extend({type:"xAxis"}),dS.extend({type:"yAxis"}),Oh({type:"grid",render:function(t){this.group.removeAll(),t.get("show")&&this.group.add(new dx({shape:t.coordinateSystem.getRect(),style:s({fill:t.get("backgroundColor")},t.getItemStyle()),silent:!0,z2:-1}))}}),bh(function(t){t.xAxis&&t.yAxis&&!t.grid&&(t.grid={})}),Ch(Jw.VISUAL.LAYOUT,x(Eu,"bar")),Ch(Jw.VISUAL.PROGRESSIVE_LAYOUT,Yb),Dh({seriesType:"bar",reset:function(t){t.getData().setVisual("legendSymbol","roundRect")}}),j_.extend({type:"series.line",dependencies:["grid","polar"],getInitialData:function(t){if(wg){var e=t.coordinateSystem;if("polar"!==e&&"cartesian2d"!==e)throw new Error("Line not support coordinateSystem besides cartesian and polar")}return vu(this.getSource(),this,{useEncodeDefaulter:!0})},defaultOption:{zlevel:0,z:2,coordinateSystem:"cartesian2d",legendHoverLink:!0,hoverAnimation:!0,clip:!0,label:{position:"top"},lineStyle:{width:2,type:"solid"},step:!1,smooth:!1,smoothMonotone:null,symbol:"emptyCircle",symbolSize:4,symbolRotate:null,showSymbol:!0,showAllSymbol:"auto",connectNulls:!1,sampling:"none",animationEasing:"linear",progressive:0,hoverLayerThreshold:1/0}});var fS=wd.prototype,pS=wd.getSymbolSize=function(t,e){var i=t.getItemVisual(e,"symbolSize");return i instanceof Array?i.slice():[+i,+i]};fS._createSymbol=function(t,e,i,n,r){this.removeAll();var a=e.getItemVisual(i,"color"),o=$u(t,-1,-1,2,2,a,r);o.attr({z2:100,culling:!0,scale:bd(n)}),o.drift=Md,this._symbolType=t,this.add(o)},fS.stopSymbolAnimation=function(t){this.childAt(0).stopAnimation(t)},fS.getSymbolPath=function(){return this.childAt(0)},fS.getScale=function(){return this.childAt(0).scale},fS.highlight=function(){this.childAt(0).trigger("emphasis")},fS.downplay=function(){this.childAt(0).trigger("normal")},fS.setZ=function(t,e){var i=this.childAt(0);i.zlevel=t,i.z=e},fS.setDraggable=function(t){var e=this.childAt(0);e.draggable=t,e.cursor=t?"move":e.cursor},fS.updateData=function(t,e,i){this.silent=!1;var n=t.getItemVisual(e,"symbol")||"circle",r=t.hostModel,a=pS(t,e),o=n!==this._symbolType;if(o){var s=t.getItemVisual(e,"symbolKeepAspect");this._createSymbol(n,t,e,a,s)}else{var l=this.childAt(0);l.silent=!1,to(l,{scale:bd(a)},r,e)}if(this._updateCommon(t,e,a,i),o){var l=this.childAt(0),h=i&&i.fadeIn,u={scale:l.scale.slice()};h&&(u.style={opacity:l.style.opacity}),l.scale=[0,0],h&&(l.style.opacity=0),eo(l,u,r,e)}this._seriesModel=r};var gS=["itemStyle"],vS=["emphasis","itemStyle"],mS=["label"],yS=["emphasis","label"];fS._updateCommon=function(t,e,i,n){function r(e){return b?t.getName(e):Lc(t,e)}var a=this.childAt(0),s=t.hostModel,l=t.getItemVisual(e,"color");"image"!==a.type?a.useStyle({strokeNoScale:!0}):a.setStyle({opacity:1,shadowBlur:null,shadowOffsetX:null,shadowOffsetY:null,shadowColor:null});var h=n&&n.itemStyle,u=n&&n.hoverItemStyle,c=n&&n.symbolOffset,d=n&&n.labelModel,f=n&&n.hoverLabelModel,p=n&&n.hoverAnimation,g=n&&n.cursorStyle;if(!n||t.hasItemOption){var v=n&&n.itemModel?n.itemModel:t.getItemModel(e);h=v.getModel(gS).getItemStyle(["color"]),u=v.getModel(vS).getItemStyle(),c=v.getShallow("symbolOffset"),d=v.getModel(mS),f=v.getModel(yS),p=v.getShallow("hoverAnimation"),g=v.getShallow("cursor")}else u=o({},u);var m=a.style,y=t.getItemVisual(e,"symbolRotate");a.attr("rotation",(y||0)*Math.PI/180||0),c&&a.attr("position",[bo(c[0],i[0]),bo(c[1],i[1])]),g&&a.attr("cursor",g),a.setColor(l,n&&n.symbolInnerColor),a.setStyle(h);var x=t.getItemVisual(e,"opacity");null!=x&&(m.opacity=x);var _=t.getItemVisual(e,"liftZ"),w=a.__z2Origin;null!=_?null==w&&(a.__z2Origin=a.z2,a.z2+=_):null!=w&&(a.z2=w,a.__z2Origin=null);var b=n&&n.useNameLabel;Ha(m,u,d,f,{labelFetcher:s,labelDataIndex:e,defaultText:r,isRectText:!0,autoColor:l}),a.__symbolOriginalScale=bd(i),a.hoverStyle=u,a.highDownOnUpdate=p&&s.isAnimationEnabled()?Sd:null,Na(a)},fS.fadeOut=function(t,e){var i=this.childAt(0);this.silent=i.silent=!0,!(e&&e.keepLabel)&&(i.style.text=null),to(i,{style:{opacity:0},scale:[0,0]},this._seriesModel,this.dataIndex,t)},u(wd,Fv);var xS=Ad.prototype;xS.updateData=function(t,e){e=Td(e);var i=this.group,n=t.hostModel,r=this._data,a=this._symbolCtor,o=Cd(t);r||i.removeAll(),t.diff(r).add(function(n){var r=t.getItemLayout(n);if(Id(t,r,n,e)){var s=new a(t,n,o);s.attr("position",r),t.setItemGraphicEl(n,s),i.add(s)}}).update(function(s,l){var h=r.getItemGraphicEl(l),u=t.getItemLayout(s);return Id(t,u,s,e)?(h?(h.updateData(t,s,o),to(h,{position:u},n)):(h=new a(t,s),h.attr("position",u)),i.add(h),void t.setItemGraphicEl(s,h)):void i.remove(h)}).remove(function(t){var e=r.getItemGraphicEl(t);e&&e.fadeOut(function(){i.remove(e)})}).execute(),this._data=t},xS.isPersistent=function(){return!0},xS.updateLayout=function(){var t=this._data;t&&t.eachItemGraphicEl(function(e,i){var n=t.getItemLayout(i);e.attr("position",n)})},xS.incrementalPrepareUpdate=function(t){this._seriesScope=Cd(t),this._data=null,this.group.removeAll()},xS.incrementalUpdate=function(t,e,i){function n(t){t.isGroup||(t.incremental=t.useHoverLayer=!0)}i=Td(i);for(var r=t.start;r<t.end;r++){var a=e.getItemLayout(r);if(Id(e,a,r,i)){var o=new this._symbolCtor(e,r,this._seriesScope);o.traverse(n),o.attr("position",a),this.group.add(o),e.setItemGraphicEl(r,o)}}},xS.remove=function(t){var e=this.group,i=this._data;i&&t?i.eachItemGraphicEl(function(t){t.fadeOut(function(){e.remove(t)})}):e.removeAll()};var _S=function(t,e,i,n,r,a,o,s){for(var l=Ld(t,e),h=[],u=[],c=[],d=[],f=[],p=[],g=[],v=Dd(r,e,o),m=Dd(a,t,s),y=0;y<l.length;y++){var x=l[y],_=!0;switch(x.cmd){case"=":var w=t.getItemLayout(x.idx),b=e.getItemLayout(x.idx1);(isNaN(w[0])||isNaN(w[1]))&&(w=b.slice()),h.push(w),u.push(b),c.push(i[x.idx]),d.push(n[x.idx1]),g.push(e.getRawIndex(x.idx1));break;case"+":var M=x.idx;h.push(r.dataToPoint([e.get(v.dataDimsForPoint[0],M),e.get(v.dataDimsForPoint[1],M)])),u.push(e.getItemLayout(M).slice()),c.push(Pd(v,r,e,M)),d.push(n[M]),g.push(e.getRawIndex(M));break;case"-":var M=x.idx,S=t.getRawIndex(M);S!==M?(h.push(t.getItemLayout(M)),u.push(a.dataToPoint([t.get(m.dataDimsForPoint[0],M),t.get(m.dataDimsForPoint[1],M)])),c.push(i[M]),d.push(Pd(m,a,t,M)),g.push(S)):_=!1}_&&(f.push(x),p.push(p.length))}p.sort(function(t,e){return g[t]-g[e]});for(var A=[],I=[],T=[],C=[],D=[],y=0;y<p.length;y++){var M=p[y];A[y]=h[M],I[y]=u[M],T[y]=c[M],C[y]=d[M],D[y]=f[M]}return{current:A,next:I,stackedOnCurrent:T,stackedOnNext:C,status:D}},wS=oe,bS=se,MS=Y,SS=H,AS=[],IS=[],TS=[],CS=ea.extend({type:"ec-polyline",shape:{points:[],smooth:0,smoothConstraint:!0,smoothMonotone:null,connectNulls:!1},style:{fill:null,stroke:"#000"},brush:nx(ea.prototype.brush),buildPath:function(t,e){var i=e.points,n=0,r=i.length,a=Rd(i,e.smoothConstraint);if(e.connectNulls){for(;r>0&&Od(i[r-1]);r--);for(;r>n&&Od(i[n]);n++);}for(;r>n;)n+=Ed(t,i,n,r,r,1,a.min,a.max,e.smooth,e.smoothMonotone,e.connectNulls)+1}}),DS=ea.extend({type:"ec-polygon",shape:{points:[],stackedOnPoints:[],smooth:0,stackedOnSmooth:0,smoothConstraint:!0,smoothMonotone:null,connectNulls:!1},brush:nx(ea.prototype.brush),buildPath:function(t,e){var i=e.points,n=e.stackedOnPoints,r=0,a=i.length,o=e.smoothMonotone,s=Rd(i,e.smoothConstraint),l=Rd(n,e.smoothConstraint);
if(e.connectNulls){for(;a>0&&Od(i[a-1]);a--);for(;a>r&&Od(i[r]);r++);}for(;a>r;){var h=Ed(t,i,r,a,a,1,s.min,s.max,e.smooth,o,e.connectNulls);Ed(t,n,r+h-1,h,a,-1,l.min,l.max,e.stackedOnSmooth,o,e.connectNulls),r+=h+1,t.closePath()}}});dl.extend({type:"line",init:function(){var t=new Fv,e=new Ad;this.group.add(e.group),this._symbolDraw=e,this._lineGroup=t},render:function(t,e,i){var n=t.coordinateSystem,r=this.group,a=t.getData(),o=t.getModel("lineStyle"),l=t.getModel("areaStyle"),h=a.mapArray(a.getItemLayout),u="polar"===n.type,c=this._coordSys,d=this._symbolDraw,f=this._polyline,p=this._polygon,g=this._lineGroup,v=t.get("animation"),m=!l.isEmpty(),y=l.get("origin"),x=Dd(n,a,y),_=Wd(n,a,x),w=t.get("showSymbol"),b=w&&!u&&Zd(t,a,n),M=this._data;M&&M.eachItemGraphicEl(function(t,e){t.__temp&&(r.remove(t),M.setItemGraphicEl(e,null))}),w||d.remove(),r.add(g);var S,A=!u&&t.get("step");n&&n.getArea&&t.get("clip",!0)&&(S=n.getArea(),null!=S.width?(S.x-=.1,S.y-=.1,S.width+=.2,S.height+=.2):S.r0&&(S.r0-=.5,S.r1+=.5)),this._clipShapeForSymbol=S,f&&c.type===n.type&&A===this._step?(m&&!p?p=this._newPolygon(h,_,n,v):p&&!m&&(g.remove(p),p=this._polygon=null),g.setClipPath(Yd(n,!1,t)),w&&d.updateData(a,{isIgnore:b,clipShape:S}),a.eachItemGraphicEl(function(t){t.stopAnimation(!0)}),Nd(this._stackedOnPoints,_)&&Nd(this._points,h)||(v?this._updateAnimation(a,_,n,i,A,y):(A&&(h=Hd(h,n,A),_=Hd(_,n,A)),f.setShape({points:h}),p&&p.setShape({points:h,stackedOnPoints:_})))):(w&&d.updateData(a,{isIgnore:b,clipShape:S}),A&&(h=Hd(h,n,A),_=Hd(_,n,A)),f=this._newPolyline(h,n,v),m&&(p=this._newPolygon(h,_,n,v)),g.setClipPath(Yd(n,!0,t)));var I=Gd(a,n)||a.getVisual("color");f.useStyle(s(o.getLineStyle(),{fill:"none",stroke:I,lineJoin:"bevel"}));var T=t.get("smooth");if(T=Vd(t.get("smooth")),f.setShape({smooth:T,smoothMonotone:t.get("smoothMonotone"),connectNulls:t.get("connectNulls")}),p){var C=a.getCalculationInfo("stackedOnSeries"),D=0;p.useStyle(s(l.getAreaStyle(),{fill:I,opacity:.7,lineJoin:"bevel"})),C&&(D=Vd(C.get("smooth"))),p.setShape({smooth:T,stackedOnSmooth:D,smoothMonotone:t.get("smoothMonotone"),connectNulls:t.get("connectNulls")})}this._data=a,this._coordSys=n,this._stackedOnPoints=_,this._points=h,this._step=A,this._valueOrigin=y},dispose:function(){},highlight:function(t,e,i,n){var r=t.getData(),a=lr(r,n);if(!(a instanceof Array)&&null!=a&&a>=0){var o=r.getItemGraphicEl(a);if(!o){var s=r.getItemLayout(a);if(!s)return;if(this._clipShapeForSymbol&&!this._clipShapeForSymbol.contain(s[0],s[1]))return;o=new wd(r,a),o.position=s,o.setZ(t.get("zlevel"),t.get("z")),o.ignore=isNaN(s[0])||isNaN(s[1]),o.__temp=!0,r.setItemGraphicEl(a,o),o.stopSymbolAnimation(!0),this.group.add(o)}o.highlight()}else dl.prototype.highlight.call(this,t,e,i,n)},downplay:function(t,e,i,n){var r=t.getData(),a=lr(r,n);if(null!=a&&a>=0){var o=r.getItemGraphicEl(a);o&&(o.__temp?(r.setItemGraphicEl(a,null),this.group.remove(o)):o.downplay())}else dl.prototype.downplay.call(this,t,e,i,n)},_newPolyline:function(t){var e=this._polyline;return e&&this._lineGroup.remove(e),e=new CS({shape:{points:t},silent:!0,z2:10}),this._lineGroup.add(e),this._polyline=e,e},_newPolygon:function(t,e){var i=this._polygon;return i&&this._lineGroup.remove(i),i=new DS({shape:{points:t,stackedOnPoints:e},silent:!0}),this._lineGroup.add(i),this._polygon=i,i},_updateAnimation:function(t,e,i,n,r,a){var o=this._polyline,s=this._polygon,l=t.hostModel,h=_S(this._data,t,this._stackedOnPoints,e,this._coordSys,i,this._valueOrigin,a),u=h.current,c=h.stackedOnCurrent,d=h.next,f=h.stackedOnNext;if(r&&(u=Hd(h.current,i,r),c=Hd(h.stackedOnCurrent,i,r),d=Hd(h.next,i,r),f=Hd(h.stackedOnNext,i,r)),Fd(u,d)>3e3||s&&Fd(c,f)>3e3)return o.setShape({points:d}),void(s&&s.setShape({points:d,stackedOnPoints:f}));o.shape.__points=h.current,o.shape.points=u,to(o,{shape:{points:d}},l),s&&(s.setShape({points:u,stackedOnPoints:c}),to(s,{shape:{points:d,stackedOnPoints:f}},l));for(var p=[],g=h.status,v=0;v<g.length;v++){var m=g[v].cmd;if("="===m){var y=t.getItemGraphicEl(g[v].idx1);y&&p.push({el:y,ptIdx:v})}}o.animators&&o.animators.length&&o.animators[0].during(function(){for(var t=0;t<p.length;t++){var e=p[t].el;e.attr("position",o.shape.__points[p[t].ptIdx])}})},remove:function(){var t=this.group,e=this._data;this._lineGroup.removeAll(),this._symbolDraw.remove(!0),e&&e.eachItemGraphicEl(function(i,n){i.__temp&&(t.remove(i),e.setItemGraphicEl(n,null))}),this._polyline=this._polygon=this._coordSys=this._points=this._stackedOnPoints=this._data=null}});var kS=function(t,e,i){return{seriesType:t,performRawSeries:!0,reset:function(t,n){function r(e,i){if(f){var n=t.getRawValue(i),r=t.getDataParams(i);u&&e.setItemVisual(i,"symbol",o(n,r)),c&&e.setItemVisual(i,"symbolSize",s(n,r)),d&&e.setItemVisual(i,"symbolRotate",h(n,r))}if(e.hasItemOption){var a=e.getItemModel(i),l=a.getShallow("symbol",!0),p=a.getShallow("symbolSize",!0),g=a.getShallow("symbolRotate",!0),v=a.getShallow("symbolKeepAspect",!0);null!=l&&e.setItemVisual(i,"symbol",l),null!=p&&e.setItemVisual(i,"symbolSize",p),null!=g&&e.setItemVisual(i,"symbolRotate",g),null!=v&&e.setItemVisual(i,"symbolKeepAspect",v)}}var a=t.getData(),o=t.get("symbol"),s=t.get("symbolSize"),l=t.get("symbolKeepAspect"),h=t.get("symbolRotate"),u=w(o),c=w(s),d=w(h),f=u||c||d,p=!u&&o?o:e,g=c?null:s;return a.setVisual({legendSymbol:i||p,symbol:p,symbolSize:g,symbolKeepAspect:l,symbolRotate:h}),n.isSeriesFiltered(t)?void 0:{dataEach:a.hasItemOption||f?r:null}}}},PS=function(t){return{seriesType:t,plan:Q_(),reset:function(t){function e(t,e){for(var i=t.end-t.start,r=a&&new Float32Array(i*s),l=t.start,h=0,u=[],c=[];l<t.end;l++){var d;if(1===s){var f=e.get(o[0],l);d=!isNaN(f)&&n.dataToPoint(f,null,c)}else{var f=u[0]=e.get(o[0],l),p=u[1]=e.get(o[1],l);d=!isNaN(f)&&!isNaN(p)&&n.dataToPoint(u,null,c)}a?(r[h++]=d?d[0]:0/0,r[h++]=d?d[1]:0/0):e.setItemLayout(l,d&&d.slice()||[0/0,0/0])}a&&e.setLayout("symbolPoints",r)}var i=t.getData(),n=t.coordinateSystem,r=t.pipelineContext,a=r.large;if(n){var o=p(n.dimensions,function(t){return i.mapDimension(t)}).slice(0,2),s=o.length,l=i.getCalculationInfo("stackResultDimension");return pu(i,o[0])&&(o[0]=l),pu(i,o[1])&&(o[1]=l),s&&{progress:e}}}}},LS={average:function(t){for(var e=0,i=0,n=0;n<t.length;n++)isNaN(t[n])||(e+=t[n],i++);return 0===i?0/0:e/i},sum:function(t){for(var e=0,i=0;i<t.length;i++)e+=t[i]||0;return e},max:function(t){for(var e=-1/0,i=0;i<t.length;i++)t[i]>e&&(e=t[i]);return isFinite(e)?e:0/0},min:function(t){for(var e=1/0,i=0;i<t.length;i++)t[i]<e&&(e=t[i]);return isFinite(e)?e:0/0},nearest:function(t){return t[0]}},OS=function(t){return Math.round(t.length/2)},ES=function(t){return{seriesType:t,modifyOutputEnd:!0,reset:function(t){var e=t.getData(),i=t.get("sampling"),n=t.coordinateSystem;if("cartesian2d"===n.type&&i){var r=n.getBaseAxis(),a=n.getOtherAxis(r),o=r.getExtent(),s=Math.abs(o[1]-o[0]),l=Math.round(e.count()/s);if(l>1){var h;"string"==typeof i?h=LS[i]:"function"==typeof i&&(h=i),h&&t.setData(e.downSample(e.mapDimension(a.dim),1/l,h,OS))}}}}};Dh(kS("line","circle","line")),Ch(PS("line")),Mh(Jw.PROCESSOR.STATISTIC,ES("line"));var zS=function(t,e,i){e=_(e)&&{coordDimensions:e}||o({},e);var n=t.getSource(),r=zb(n,e),a=new Lb(r,t);return a.initData(n,i),a},BS={updateSelectedMap:function(t){this._targetList=_(t)?t.slice():[],this._selectTargetMap=g(t||[],function(t,e){return t.set(e.name,e),t},N())},select:function(t,e){var i=null!=e?this._targetList[e]:this._selectTargetMap.get(t),n=this.get("selectedMode");"single"===n&&this._selectTargetMap.each(function(t){t.selected=!1}),i&&(i.selected=!0)},unSelect:function(t,e){var i=null!=e?this._targetList[e]:this._selectTargetMap.get(t);i&&(i.selected=!1)},toggleSelected:function(t,e){var i=null!=e?this._targetList[e]:this._selectTargetMap.get(t);return null!=i?(this[i.selected?"unSelect":"select"](t,e),i.selected):void 0},isSelected:function(t,e){var i=null!=e?this._targetList[e]:this._selectTargetMap.get(t);return i&&i.selected}},RS=Eh({type:"series.pie",init:function(t){RS.superApply(this,"init",arguments),this.legendVisualProvider=new Ud(y(this.getData,this),y(this.getRawData,this)),this.updateSelectedMap(this._createSelectableList()),this._defaultLabelLine(t)},mergeOption:function(t){RS.superCall(this,"mergeOption",t),this.updateSelectedMap(this._createSelectableList())},getInitialData:function(){return zS(this,{coordDimensions:["value"],encodeDefaulter:x(fs,this)})},_createSelectableList:function(){for(var t=this.getRawData(),e=t.mapDimension("value"),i=[],n=0,r=t.count();r>n;n++)i.push({name:t.getName(n),value:t.get(e,n),selected:Js(t,n,"selected")});return i},getDataParams:function(t){var e=this.getData(),i=RS.superCall(this,"getDataParams",t),n=[];return e.each(e.mapDimension("value"),function(t){n.push(t)}),i.percent=Co(n,t,e.hostModel.get("percentPrecision")),i.$vars.push("percent"),i},_defaultLabelLine:function(t){er(t,"labelLine",["show"]);var e=t.labelLine,i=t.emphasis.labelLine;e.show=e.show&&t.label.show,i.show=i.show&&t.emphasis.label.show},defaultOption:{zlevel:0,z:2,legendHoverLink:!0,hoverAnimation:!0,center:["50%","50%"],radius:[0,"75%"],clockwise:!0,startAngle:90,minAngle:0,minShowLabelAngle:0,selectedOffset:10,hoverOffset:10,avoidLabelOverlap:!0,percentPrecision:2,stillShowZeroSum:!0,left:0,top:0,right:0,bottom:0,width:null,height:null,label:{rotate:!1,show:!0,position:"outer",alignTo:"none",margin:"25%",bleedMargin:10,distanceToLabelLine:5},labelLine:{show:!0,length:15,length2:15,smooth:!1,lineStyle:{width:1,type:"solid"}},itemStyle:{borderWidth:1},animationType:"expansion",animationTypeUpdate:"transition",animationEasing:"cubicOut"}});c(RS,BS);var NS=Kd.prototype;NS.updateData=function(t,e,i){var n=this.childAt(0),r=this.childAt(1),a=this.childAt(2),l=t.hostModel,h=t.getItemModel(e),u=t.getItemLayout(e),c=o({},u);c.label=null;var d=l.getShallow("animationTypeUpdate");if(i){n.setShape(c);var f=l.getShallow("animationType");"scale"===f?(n.shape.r=u.r0,eo(n,{shape:{r:u.r}},l,e)):(n.shape.endAngle=u.startAngle,to(n,{shape:{endAngle:u.endAngle}},l,e))}else"expansion"===d?n.setShape(c):to(n,{shape:c},l,e);var p=t.getItemVisual(e,"color");n.useStyle(s({lineJoin:"bevel",fill:p},h.getModel("itemStyle").getItemStyle())),n.hoverStyle=h.getModel("emphasis.itemStyle").getItemStyle();var g=h.getShallow("cursor");g&&n.attr("cursor",g),jd(this,t.getItemLayout(e),l.isSelected(t.getName(e)),l.get("selectedOffset"),l.get("animation"));var v=!i&&"transition"===d;this._updateLabel(t,e,v),this.highDownOnUpdate=l.get("silent")?null:function(t,e){var i=l.isAnimationEnabled()&&h.get("hoverAnimation");"emphasis"===e?(r.ignore=r.hoverIgnore,a.ignore=a.hoverIgnore,i&&(n.stopAnimation(!0),n.animateTo({shape:{r:u.r+l.get("hoverOffset")}},300,"elasticOut"))):(r.ignore=r.normalIgnore,a.ignore=a.normalIgnore,i&&(n.stopAnimation(!0),n.animateTo({shape:{r:u.r}},300,"elasticOut")))},Na(this)},NS._updateLabel=function(t,e,i){var n=this.childAt(1),r=this.childAt(2),a=t.hostModel,o=t.getItemModel(e),s=t.getItemLayout(e),l=s.label,h=t.getItemVisual(e,"color");if(!l||isNaN(l.x)||isNaN(l.y))return void(r.ignore=r.normalIgnore=r.hoverIgnore=n.ignore=n.normalIgnore=n.hoverIgnore=!0);var u={points:l.linePoints||[[l.x,l.y],[l.x,l.y],[l.x,l.y]]},c={x:l.x,y:l.y};i?(to(n,{shape:u},a,e),to(r,{style:c},a,e)):(n.attr({shape:u}),r.attr({style:c})),r.attr({rotation:l.rotation,origin:[l.x,l.y],z2:10});var d=o.getModel("label"),f=o.getModel("emphasis.label"),p=o.getModel("labelLine"),g=o.getModel("emphasis.labelLine"),h=t.getItemVisual(e,"color");Ha(r.style,r.hoverStyle={},d,f,{labelFetcher:t.hostModel,labelDataIndex:e,defaultText:l.text,autoColor:h,useInsideStyle:!!l.inside},{textAlign:l.textAlign,textVerticalAlign:l.verticalAlign,opacity:t.getItemVisual(e,"opacity")}),r.ignore=r.normalIgnore=!d.get("show"),r.hoverIgnore=!f.get("show"),n.ignore=n.normalIgnore=!p.get("show"),n.hoverIgnore=!g.get("show"),n.setStyle({stroke:h,opacity:t.getItemVisual(e,"opacity")}),n.setStyle(p.getModel("lineStyle").getLineStyle()),n.hoverStyle=g.getModel("lineStyle").getLineStyle();var v=p.get("smooth");v&&v===!0&&(v=.4),n.setShape({smooth:v})},u(Kd,Fv);var FS=(dl.extend({type:"pie",init:function(){var t=new Fv;this._sectorGroup=t},render:function(t,e,i,n){if(!n||n.from!==this.uid){var r=t.getData(),a=this._data,o=this.group,s=e.get("animation"),l=!a,h=t.get("animationType"),u=t.get("animationTypeUpdate"),c=x(qd,this.uid,t,s,i),d=t.get("selectedMode");if(r.diff(a).add(function(t){var e=new Kd(r,t);l&&"scale"!==h&&e.eachChild(function(t){t.stopAnimation(!0)}),d&&e.on("click",c),r.setItemGraphicEl(t,e),o.add(e)}).update(function(t,e){var i=a.getItemGraphicEl(e);l||"transition"===u||i.eachChild(function(t){t.stopAnimation(!0)}),i.updateData(r,t),i.off("click"),d&&i.on("click",c),o.add(i),r.setItemGraphicEl(t,i)}).remove(function(t){var e=a.getItemGraphicEl(t);o.remove(e)}).execute(),s&&r.count()>0&&(l?"scale"!==h:"transition"!==u)){for(var f=r.getItemLayout(0),p=1;isNaN(f.startAngle)&&p<r.count();++p)f=r.getItemLayout(p);var g=Math.max(i.getWidth(),i.getHeight())/2,v=y(o.removeClipPath,o);o.setClipPath(this._createClipPath(f.cx,f.cy,g,f.startAngle,f.clockwise,v,t,l))}else o.removeClipPath();this._data=r}},dispose:function(){},_createClipPath:function(t,e,i,n,r,a,o,s){var l=new rx({shape:{cx:t,cy:e,r0:0,r:i,startAngle:n,endAngle:n,clockwise:r}}),h=s?eo:to;return h(l,{shape:{endAngle:n+(r?1:-1)*Math.PI*2}},o,a),l},containPoint:function(t,e){var i=e.getData(),n=i.getItemLayout(0);if(n){var r=t[0]-n.cx,a=t[1]-n.cy,o=Math.sqrt(r*r+a*a);return o<=n.r&&o>=n.r0}}}),function(t,e){f(e,function(e){e.update="updateView",Ah(e,function(i,n){var r={};return n.eachComponent({mainType:"series",subType:t,query:i},function(t){t[e.method]&&t[e.method](i.name,i.dataIndex);var n=t.getData();n.each(function(e){var i=n.getName(e);r[i]=t.isSelected(i)||!1})}),{name:i.name,selected:r,seriesId:i.seriesId}})})}),VS=function(t){return{getTargetSeries:function(e){var i={},n=N();return e.eachSeriesByType(t,function(t){t.__paletteScope=i,n.set(t.uid,t)}),n},reset:function(t){var e=t.getRawData(),i={},n=t.getData();n.each(function(t){var e=n.getRawIndex(t);i[e]=t}),e.each(function(r){var a,o=i[r],s=null!=o&&n.getItemVisual(o,"color",!0),l=null!=o&&n.getItemVisual(o,"borderColor",!0);if(s&&l||(a=e.getItemModel(r)),!s){var h=a.get("itemStyle.color")||t.getColorFromPalette(e.getName(r)||r+"",t.__paletteScope,e.count());null!=o&&n.setItemVisual(o,"color",h)}if(!l){var u=a.get("itemStyle.borderColor");null!=o&&n.setItemVisual(o,"borderColor",u)}})}}},WS=Math.PI/180,HS=function(t,e,i,n,r,a){var o,s,l=t.getData(),h=[],u=!1,c=(t.get("minShowLabelAngle")||0)*WS;l.each(function(n){var a=l.getItemLayout(n),d=l.getItemModel(n),f=d.getModel("label"),p=f.get("position")||d.get("emphasis.label.position"),g=f.get("distanceToLabelLine"),v=f.get("alignTo"),m=bo(f.get("margin"),i),y=f.get("bleedMargin"),x=f.getFont(),_=d.getModel("labelLine"),w=_.get("length");w=bo(w,i);var b=_.get("length2");if(b=bo(b,i),!(a.angle<c)){var M,S,A,I,T=(a.startAngle+a.endAngle)/2,C=Math.cos(T),D=Math.sin(T);o=a.cx,s=a.cy;var k=t.getFormattedLabel(n,"normal")||l.getName(n),P=Xi(k,x,I,"top"),L="inside"===p||"inner"===p;if("center"===p)M=a.cx,S=a.cy,I="center";else{var O=(L?(a.r+a.r0)/2*C:a.r*C)+o,E=(L?(a.r+a.r0)/2*D:a.r*D)+s;if(M=O+3*C,S=E+3*D,!L){var z=O+C*(w+e-a.r),B=E+D*(w+e-a.r),R=z+(0>C?-1:1)*b,N=B;M="edge"===v?0>C?r+m:r+i-m:R+(0>C?-g:g),S=N,A=[[O,E],[z,B],[R,N]]}I=L?"center":"edge"===v?C>0?"right":"left":C>0?"left":"right"}var F,V=f.get("rotate");F="number"==typeof V?V*(Math.PI/180):V?0>C?-T+Math.PI:-T:0,u=!!F,a.label={x:M,y:S,position:p,height:P.height,len:w,len2:b,linePoints:A,textAlign:I,verticalAlign:"middle",rotation:F,inside:L,labelDistance:g,labelAlignTo:v,labelMargin:m,bleedMargin:y,textRect:P,text:k,font:x},L||h.push(a.label)}}),!u&&t.get("avoidLabelOverlap")&&Qd(h,o,s,e,i,n,r,a)},GS=2*Math.PI,ZS=Math.PI/180,XS=function(t,e,i){e.eachSeriesByType(t,function(t){var e=t.getData(),n=e.mapDimension("value"),r=tf(t,i),a=t.get("center"),o=t.get("radius");_(o)||(o=[0,o]),_(a)||(a=[a,a]);var s=bo(r.width,i.getWidth()),l=bo(r.height,i.getHeight()),h=Math.min(s,l),u=bo(a[0],s)+r.x,c=bo(a[1],l)+r.y,d=bo(o[0],h/2),f=bo(o[1],h/2),p=-t.get("startAngle")*ZS,g=t.get("minAngle")*ZS,v=0;e.each(n,function(t){!isNaN(t)&&v++});var m=e.getSum(n),y=Math.PI/(m||v)*2,x=t.get("clockwise"),w=t.get("roseType"),b=t.get("stillShowZeroSum"),M=e.getDataExtent(n);M[0]=0;var S=GS,A=0,I=p,T=x?1:-1;if(e.each(n,function(t,i){var n;if(isNaN(t))return void e.setItemLayout(i,{angle:0/0,startAngle:0/0,endAngle:0/0,clockwise:x,cx:u,cy:c,r0:d,r:w?0/0:f,viewRect:r});n="area"!==w?0===m&&b?y:t*y:GS/v,g>n?(n=g,S-=g):A+=t;var a=I+T*n;e.setItemLayout(i,{angle:n,startAngle:I,endAngle:a,clockwise:x,cx:u,cy:c,r0:d,r:w?wo(t,M,[d,f]):f,viewRect:r}),I=a}),GS>S&&v)if(.001>=S){var C=GS/v;e.each(n,function(t,i){if(!isNaN(t)){var n=e.getItemLayout(i);n.angle=C,n.startAngle=p+T*i*C,n.endAngle=p+T*(i+1)*C}})}else y=S/A,I=p,e.each(n,function(t,i){if(!isNaN(t)){var n=e.getItemLayout(i),r=n.angle===g?g:t*y;n.startAngle=I,n.endAngle=I+T*r,I+=T*r}});HS(t,f,r.width,r.height,r.x,r.y)})},YS=function(t){return{seriesType:t,reset:function(t,e){var i=e.findComponents({mainType:"legend"});if(i&&i.length){var n=t.getData();n.filterSelf(function(t){for(var e=n.getName(t),r=0;r<i.length;r++)if(!i[r].isSelected(e))return!1;return!0})}}}};FS("pie",[{type:"pieToggleSelect",event:"pieselectchanged",method:"toggleSelected"},{type:"pieSelect",event:"pieselected",method:"select"},{type:"pieUnSelect",event:"pieunselected",method:"unSelect"}]),Dh(VS("pie")),Ch(x(XS,"pie")),Mh(YS("pie"));{var US=(j_.extend({type:"series.gauge",getInitialData:function(){return zS(this,["value"])},defaultOption:{zlevel:0,z:2,center:["50%","50%"],legendHoverLink:!0,radius:"75%",startAngle:225,endAngle:-45,clockwise:!0,min:0,max:100,splitNumber:10,axisLine:{show:!0,lineStyle:{color:[[.2,"#91c7ae"],[.8,"#63869e"],[1,"#c23531"]],width:30}},splitLine:{show:!0,length:30,lineStyle:{color:"#eee",width:2,type:"solid"}},axisTick:{show:!0,splitNumber:5,length:8,lineStyle:{color:"#eee",width:1,type:"solid"}},axisLabel:{show:!0,distance:5,color:"auto"},pointer:{show:!0,length:"80%",width:8},itemStyle:{color:"auto"},title:{show:!0,offsetCenter:[0,"-40%"],color:"#333",fontSize:15},detail:{show:!0,backgroundColor:"rgba(0,0,0,0)",borderWidth:0,borderColor:"#ccc",width:100,height:null,padding:[5,10],offsetCenter:[0,"40%"],color:"auto",fontSize:30}}}),ea.extend({type:"echartsGaugePointer",shape:{angle:0,width:10,r:10,x:0,y:0},buildPath:function(t,e){var i=Math.cos,n=Math.sin,r=e.r,a=e.width,o=e.angle,s=e.x-i(o)*a*(a>=r/3?1:2),l=e.y-n(o)*a*(a>=r/3?1:2);o=e.angle-Math.PI/2,t.moveTo(s,l),t.lineTo(e.x+i(o)*a,e.y+n(o)*a),t.lineTo(e.x+i(e.angle)*r,e.y+n(e.angle)*r),t.lineTo(e.x-i(o)*a,e.y-n(o)*a),t.lineTo(s,l)}})),qS=2*Math.PI;dl.extend({type:"gauge",render:function(t,e,i){this.group.removeAll();var n=t.get("axisLine.lineStyle.color"),r=ef(t,i);this._renderMain(t,e,i,n,r)},dispose:function(){},_renderMain:function(t,e,i,n,r){for(var a=this.group,o=t.getModel("axisLine"),s=o.getModel("lineStyle"),l=t.get("clockwise"),h=-t.get("startAngle")/180*Math.PI,u=-t.get("endAngle")/180*Math.PI,c=(u-h)%qS,d=h,f=s.get("width"),p=o.get("show"),g=0;p&&g<n.length;g++){var v=Math.min(Math.max(n[g][0],0),1),u=h+c*v,m=new rx({shape:{startAngle:d,endAngle:u,cx:r.cx,cy:r.cy,clockwise:l,r0:r.r-f,r:r.r},silent:!0});m.setStyle({fill:n[g][1]}),m.setStyle(s.getLineStyle(["color","borderWidth","borderColor"])),a.add(m),d=u}var y=function(t){if(0>=t)return n[0][1];for(var e=0;e<n.length;e++)if(n[e][0]>=t&&(0===e?0:n[e-1][0])<t)return n[e][1];return n[e-1][1]};if(!l){var x=h;h=u,u=x}this._renderTicks(t,e,i,y,r,h,u,l),this._renderPointer(t,e,i,y,r,h,u,l),this._renderTitle(t,e,i,y,r),this._renderDetail(t,e,i,y,r)},_renderTicks:function(t,e,i,n,r,a,o){for(var s=this.group,l=r.cx,h=r.cy,u=r.r,c=+t.get("min"),d=+t.get("max"),f=t.getModel("splitLine"),p=t.getModel("axisTick"),g=t.getModel("axisLabel"),v=t.get("splitNumber"),m=p.get("splitNumber"),y=bo(f.get("length"),u),x=bo(p.get("length"),u),_=a,w=(o-a)/v,b=w/m,M=f.getModel("lineStyle").getLineStyle(),S=p.getModel("lineStyle").getLineStyle(),A=0;v>=A;A++){var I=Math.cos(_),T=Math.sin(_);if(f.get("show")){var C=new px({shape:{x1:I*u+l,y1:T*u+h,x2:I*(u-y)+l,y2:T*(u-y)+h},style:M,silent:!0});"auto"===M.stroke&&C.setStyle({stroke:n(A/v)}),s.add(C)}if(g.get("show")){var D=nf(Mo(A/v*(d-c)+c),g.get("formatter")),k=g.get("distance"),P=n(A/v);s.add(new tx({style:Za({},g,{text:D,x:I*(u-y-k)+l,y:T*(u-y-k)+h,textVerticalAlign:-.4>T?"top":T>.4?"bottom":"middle",textAlign:-.4>I?"left":I>.4?"right":"center"},{autoColor:P}),silent:!0}))}if(p.get("show")&&A!==v){for(var L=0;m>=L;L++){var I=Math.cos(_),T=Math.sin(_),O=new px({shape:{x1:I*u+l,y1:T*u+h,x2:I*(u-x)+l,y2:T*(u-x)+h},silent:!0,style:S});"auto"===S.stroke&&O.setStyle({stroke:n((A+L/m)/v)}),s.add(O),_+=b}_-=b}else _+=w}},_renderPointer:function(t,e,i,n,r,a,o){var s=this.group,l=this._data;if(!t.get("pointer.show"))return void(l&&l.eachItemGraphicEl(function(t){s.remove(t)}));var h=[+t.get("min"),+t.get("max")],u=[a,o],c=t.getData(),d=c.mapDimension("value");c.diff(l).add(function(e){var i=new US({shape:{angle:a}});eo(i,{shape:{angle:wo(c.get(d,e),h,u,!0)}},t),s.add(i),c.setItemGraphicEl(e,i)}).update(function(e,i){var n=l.getItemGraphicEl(i);to(n,{shape:{angle:wo(c.get(d,e),h,u,!0)}},t),s.add(n),c.setItemGraphicEl(e,n)}).remove(function(t){var e=l.getItemGraphicEl(t);s.remove(e)}).execute(),c.eachItemGraphicEl(function(t,e){var i=c.getItemModel(e),a=i.getModel("pointer");t.setShape({x:r.cx,y:r.cy,width:bo(a.get("width"),r.r),r:bo(a.get("length"),r.r)}),t.useStyle(i.getModel("itemStyle").getItemStyle()),"auto"===t.style.fill&&t.setStyle("fill",n(wo(c.get(d,e),h,[0,1],!0))),Na(t,i.getModel("emphasis.itemStyle").getItemStyle())}),this._data=c},_renderTitle:function(t,e,i,n,r){var a=t.getData(),o=a.mapDimension("value"),s=t.getModel("title");if(s.get("show")){var l=s.get("offsetCenter"),h=r.cx+bo(l[0],r.r),u=r.cy+bo(l[1],r.r),c=+t.get("min"),d=+t.get("max"),f=t.getData().get(o,0),p=n(wo(f,[c,d],[0,1],!0));this.group.add(new tx({silent:!0,style:Za({},s,{x:h,y:u,text:a.getName(0),textAlign:"center",textVerticalAlign:"middle"},{autoColor:p,forceRich:!0})}))}},_renderDetail:function(t,e,i,n,r){var a=t.getModel("detail"),o=+t.get("min"),s=+t.get("max");if(a.get("show")){var l=a.get("offsetCenter"),h=r.cx+bo(l[0],r.r),u=r.cy+bo(l[1],r.r),c=bo(a.get("width"),r.r),d=bo(a.get("height"),r.r),f=t.getData(),p=f.get(f.mapDimension("value"),0),g=n(wo(p,[o,s],[0,1],!0));this.group.add(new tx({silent:!0,style:Za({},a,{x:h,y:u,text:nf(p,a.get("formatter")),textWidth:isNaN(c)?null:c,textHeight:isNaN(d)?null:d,textAlign:"center",textVerticalAlign:"middle"},{autoColor:g,forceRich:!0})}))}}})}lf.prototype={constructor:lf,pointToData:function(t,e){return this.polar.pointToData(t,e)["radius"===this.dim?0:1]},dataToRadius:TM.prototype.dataToCoord,radiusToData:TM.prototype.coordToData},u(lf,TM);var jS=hr();hf.prototype={constructor:hf,pointToData:function(t,e){return this.polar.pointToData(t,e)["radius"===this.dim?0:1]},dataToAngle:TM.prototype.dataToCoord,angleToData:TM.prototype.coordToData,calculateCategoryInterval:function(){var t=this,e=t.getLabelModel(),i=t.scale,n=i.getExtent(),r=i.count();if(n[1]-n[0]<1)return 0;var a=n[0],o=t.dataToCoord(a+1)-t.dataToCoord(a),s=Math.abs(o),l=Xi(a,e.getFont(),"center","top"),h=Math.max(l.height,7),u=h/s;isNaN(u)&&(u=1/0);var c=Math.max(0,Math.floor(u)),d=jS(t.model),f=d.lastAutoInterval,p=d.lastTickCount;return null!=f&&null!=p&&Math.abs(f-c)<=1&&Math.abs(p-r)<=1&&f>c?c=f:(d.lastTickCount=r,d.lastAutoInterval=c),c}},u(hf,TM);var KS=function(t){this.name=t||"",this.cx=0,this.cy=0,this._radiusAxis=new lf,this._angleAxis=new hf,this._radiusAxis.polar=this._angleAxis.polar=this};KS.prototype={type:"polar",axisPointerEnabled:!0,constructor:KS,dimensions:["radius","angle"],model:null,containPoint:function(t){var e=this.pointToCoord(t);return this._radiusAxis.contain(e[0])&&this._angleAxis.contain(e[1])},containData:function(t){return this._radiusAxis.containData(t[0])&&this._angleAxis.containData(t[1])},getAxis:function(t){return this["_"+t+"Axis"]},getAxes:function(){return[this._radiusAxis,this._angleAxis]},getAxesByScale:function(t){var e=[],i=this._angleAxis,n=this._radiusAxis;return i.scale.type===t&&e.push(i),n.scale.type===t&&e.push(n),e},getAngleAxis:function(){return this._angleAxis},getRadiusAxis:function(){return this._radiusAxis},getOtherAxis:function(t){var e=this._angleAxis;return t===e?this._radiusAxis:e},getBaseAxis:function(){return this.getAxesByScale("ordinal")[0]||this.getAxesByScale("time")[0]||this.getAngleAxis()},getTooltipAxes:function(t){var e=null!=t&&"auto"!==t?this.getAxis(t):this.getBaseAxis();return{baseAxes:[e],otherAxes:[this.getOtherAxis(e)]}},dataToPoint:function(t,e){return this.coordToPoint([this._radiusAxis.dataToRadius(t[0],e),this._angleAxis.dataToAngle(t[1],e)])},pointToData:function(t,e){var i=this.pointToCoord(t);return[this._radiusAxis.radiusToData(i[0],e),this._angleAxis.angleToData(i[1],e)]},pointToCoord:function(t){var e=t[0]-this.cx,i=t[1]-this.cy,n=this.getAngleAxis(),r=n.getExtent(),a=Math.min(r[0],r[1]),o=Math.max(r[0],r[1]);n.inverse?a=o-360:o=a+360;var s=Math.sqrt(e*e+i*i);e/=s,i/=s;for(var l=Math.atan2(-i,e)/Math.PI*180,h=a>l?1:-1;a>l||l>o;)l+=360*h;return[s,l]},coordToPoint:function(t){var e=t[0],i=t[1]/180*Math.PI,n=Math.cos(i)*e+this.cx,r=-Math.sin(i)*e+this.cy;return[n,r]},getArea:function(){var t=this.getAngleAxis(),e=this.getRadiusAxis(),i=e.getExtent().slice();i[0]>i[1]&&i.reverse();var n=t.getExtent(),r=Math.PI/180;return{cx:this.cx,cy:this.cy,r0:i[0],r:i[1],startAngle:-n[0]*r,endAngle:-n[1]*r,clockwise:t.inverse,contain:function(t,e){var i=t-this.cx,n=e-this.cy,r=i*i+n*n,a=this.r,o=this.r0;return a*a>=r&&r>=o*o}}}};var $S=l_.extend({type:"polarAxis",axis:null,getCoordSysModel:function(){return this.ecModel.queryComponents({mainType:"polar",index:this.option.polarIndex,id:this.option.polarId})[0]}});r($S.prototype,dM);var QS={angle:{startAngle:90,clockwise:!0,splitNumber:12,axisLabel:{rotate:!1}},radius:{splitNumber:5}};BM("angle",$S,uf,QS.angle),BM("radius",$S,uf,QS.radius),Lh({type:"polar",dependencies:["polarAxis","angleAxis"],coordinateSystem:null,findAxisModel:function(t){var e,i=this.ecModel;return i.eachComponent(t,function(t){t.getCoordSysModel()===this&&(e=t)},this),e},defaultOption:{zlevel:0,z:0,center:["50%","50%"],radius:"80%"}});var JS={dimensions:KS.prototype.dimensions,create:function(t,e){var i=[];return t.eachComponent("polar",function(t,n){var r=new KS(n);r.update=df;var a=r.getRadiusAxis(),o=r.getAngleAxis(),s=t.findAxisModel("radiusAxis"),l=t.findAxisModel("angleAxis");ff(a,s),ff(o,l),cf(r,t,e),i.push(r),t.coordinateSystem=r,r.model=t}),t.eachSeries(function(e){if("polar"===e.get("coordinateSystem")){var i=t.queryComponents({mainType:"polar",index:e.get("polarIndex"),id:e.get("polarId")})[0];if(wg&&!i)throw new Error('Polar "'+C(e.get("polarIndex"),e.get("polarId"),0)+'" not found');e.coordinateSystem=i.coordinateSystem}}),i}};Is.register("polar",JS);var tA=["axisLine","axisLabel","axisTick","minorTick","splitLine","minorSplitLine","splitArea"];lS.extend({type:"angleAxis",axisPointerClass:"PolarAxisPointer",render:function(t){if(this.group.removeAll(),t.get("show")){var e=t.axis,i=e.polar,r=i.getRadiusAxis().getExtent(),a=e.getTicksCoords(),o=e.getMinorTicksCoords(),s=p(e.getViewLabels(),function(t){var t=n(t);return t.coord=e.dataToCoord(t.tickValue),t});vf(s),vf(a),f(tA,function(n){!t.get(n+".show")||e.scale.isBlank()&&"axisLine"!==n||this["_"+n](t,i,a,o,r,s)},this)}},_axisLine:function(t,e,i,n,r){var a,o=t.getModel("axisLine.lineStyle"),s=gf(e),l=s?0:1;a=0===r[l]?new ex({shape:{cx:e.cx,cy:e.cy,r:r[s]},style:o.getLineStyle(),z2:1,silent:!0}):new ax({shape:{cx:e.cx,cy:e.cy,r:r[s],r0:r[l]},style:o.getLineStyle(),z2:1,silent:!0}),a.style.fill=null,this.group.add(a)},_axisTick:function(t,e,i,n,r){var a=t.getModel("axisTick"),o=(a.get("inside")?-1:1)*a.get("length"),l=r[gf(e)],h=p(i,function(t){return new px({shape:pf(e,[l,l+o],t.coord)})});this.group.add(Ex(h,{style:s(a.getModel("lineStyle").getLineStyle(),{stroke:t.get("axisLine.lineStyle.color")})}))},_minorTick:function(t,e,i,n,r){if(n.length){for(var a=t.getModel("axisTick"),o=t.getModel("minorTick"),l=(a.get("inside")?-1:1)*o.get("length"),h=r[gf(e)],u=[],c=0;c<n.length;c++)for(var d=0;d<n[c].length;d++)u.push(new px({shape:pf(e,[h,h+l],n[c][d].coord)}));this.group.add(Ex(u,{style:s(o.getModel("lineStyle").getLineStyle(),s(a.getLineStyle(),{stroke:t.get("axisLine.lineStyle.color")}))}))}},_axisLabel:function(t,e,i,n,r,a){var o=t.getCategories(!0),s=t.getModel("axisLabel"),l=s.get("margin"),h=t.get("triggerEvent");f(a,function(i){var n=s,a=i.tickValue,u=r[gf(e)],c=e.coordToPoint([u+l,i.coord]),d=e.cx,f=e.cy,p=Math.abs(c[0]-d)/u<.3?"center":c[0]>d?"left":"right",g=Math.abs(c[1]-f)/u<.3?"middle":c[1]>f?"top":"bottom";o&&o[a]&&o[a].textStyle&&(n=new po(o[a].textStyle,s,s.ecModel));var v=new tx({silent:eS.isLabelSilent(t)});this.group.add(v),Za(v.style,n,{x:c[0],y:c[1],textFill:n.getTextColor()||t.get("axisLine.lineStyle.color"),text:i.formattedLabel,textAlign:p,textVerticalAlign:g}),h&&(v.eventData=eS.makeAxisEventDataBase(t),v.eventData.targetType="axisLabel",v.eventData.value=i.rawLabel)},this)},_splitLine:function(t,e,i,n,r){var a=t.getModel("splitLine"),o=a.getModel("lineStyle"),l=o.get("color"),h=0;l=l instanceof Array?l:[l];for(var u=[],c=0;c<i.length;c++){var d=h++%l.length;u[d]=u[d]||[],u[d].push(new px({shape:pf(e,r,i[c].coord)}))}for(var c=0;c<u.length;c++)this.group.add(Ex(u[c],{style:s({stroke:l[c%l.length]},o.getLineStyle()),silent:!0,z:t.get("z")}))},_minorSplitLine:function(t,e,i,n,r){if(n.length){for(var a=t.getModel("minorSplitLine"),o=a.getModel("lineStyle"),s=[],l=0;l<n.length;l++)for(var h=0;h<n[l].length;h++)s.push(new px({shape:pf(e,r,n[l][h].coord)}));this.group.add(Ex(s,{style:o.getLineStyle(),silent:!0,z:t.get("z")}))}},_splitArea:function(t,e,i,n,r){if(i.length){var a=t.getModel("splitArea"),o=a.getModel("areaStyle"),l=o.get("color"),h=0;l=l instanceof Array?l:[l];for(var u=[],c=Math.PI/180,d=-i[0].coord*c,f=Math.min(r[0],r[1]),p=Math.max(r[0],r[1]),g=t.get("clockwise"),v=1;v<i.length;v++){var m=h++%l.length;u[m]=u[m]||[],u[m].push(new rx({shape:{cx:e.cx,cy:e.cy,r0:f,r:p,startAngle:d,endAngle:-i[v].coord*c,clockwise:g},silent:!0})),d=-i[v].coord*c}for(var v=0;v<u.length;v++)this.group.add(Ex(u[v],{style:s({fill:l[v%l.length]},o.getAreaStyle()),silent:!0}))}}});var eA=["axisLine","axisTickLabel","axisName"],iA=["splitLine","splitArea","minorSplitLine"];lS.extend({type:"radiusAxis",axisPointerClass:"PolarAxisPointer",render:function(t){if(this.group.removeAll(),t.get("show")){var e=t.axis,i=e.polar,n=i.getAngleAxis(),r=e.getTicksCoords(),a=e.getMinorTicksCoords(),o=n.getExtent()[0],s=e.getExtent(),l=mf(i,t,o),h=new eS(t,l);f(eA,h.add,h),this.group.add(h.getGroup()),f(iA,function(n){t.get(n+".show")&&!e.scale.isBlank()&&this["_"+n](t,i,o,s,r,a)},this)}},_splitLine:function(t,e,i,n,r){var a=t.getModel("splitLine"),o=a.getModel("lineStyle"),l=o.get("color"),h=0;l=l instanceof Array?l:[l];for(var u=[],c=0;c<r.length;c++){var d=h++%l.length;u[d]=u[d]||[],u[d].push(new ex({shape:{cx:e.cx,cy:e.cy,r:r[c].coord}}))}for(var c=0;c<u.length;c++)this.group.add(Ex(u[c],{style:s({stroke:l[c%l.length],fill:null},o.getLineStyle()),silent:!0}))},_minorSplitLine:function(t,e,i,n,r,a){if(a.length){for(var o=t.getModel("minorSplitLine"),l=o.getModel("lineStyle"),h=[],u=0;u<a.length;u++)for(var c=0;c<a[u].length;c++)h.push(new ex({shape:{cx:e.cx,cy:e.cy,r:a[u][c].coord}}));this.group.add(Ex(h,{style:s({fill:null},l.getLineStyle()),silent:!0}))}},_splitArea:function(t,e,i,n,r){if(r.length){var a=t.getModel("splitArea"),o=a.getModel("areaStyle"),l=o.get("color"),h=0;l=l instanceof Array?l:[l];for(var u=[],c=r[0].coord,d=1;d<r.length;d++){var f=h++%l.length;
u[f]=u[f]||[],u[f].push(new rx({shape:{cx:e.cx,cy:e.cy,r0:c,r:r[d].coord,startAngle:0,endAngle:2*Math.PI},silent:!0})),c=r[d].coord}for(var d=0;d<u.length;d++)this.group.add(Ex(u[d],{style:s({fill:l[d%l.length]},o.getAreaStyle()),silent:!0}))}}});var nA=function(t,e){var i,n=[],r=t.seriesIndex;if(null==r||!(i=e.getSeriesByIndex(r)))return{point:[]};var a=i.getData(),o=lr(a,t);if(null==o||0>o||_(o))return{point:[]};var s=a.getItemGraphicEl(o),l=i.coordinateSystem;if(i.getTooltipPosition)n=i.getTooltipPosition(o)||[];else if(l&&l.dataToPoint)n=l.dataToPoint(a.getValues(p(l.dimensions,function(t){return a.mapDimension(t)}),o,!0))||[];else if(s){var h=s.getBoundingRect().clone();h.applyTransform(s.transform),n=[h.x+h.width/2,h.y+h.height/2]}return{point:n,el:s}},rA=f,aA=x,oA=hr(),sA=function(t,e,i){var n=t.currTrigger,r=[t.x,t.y],a=t,o=t.dispatchAction||y(i.dispatchAction,i),s=e.getComponent("axisPointer").coordSysAxesInfo;if(s){Tf(r)&&(r=nA({seriesIndex:a.seriesIndex,dataIndex:a.dataIndex},e).point);var l=Tf(r),h=a.axesInfo,u=s.axesInfo,c="leave"===n||Tf(r),d={},f={},p={list:[],map:{}},g={showPointer:aA(_f,f),showTooltip:aA(wf,p)};rA(s.coordSysMap,function(t,e){var i=l||t.containPoint(r);rA(s.coordSysAxesInfo[e],function(t){var e=t.axis,n=Af(h,t);if(!c&&i&&(!h||n)){var a=n&&n.value;null!=a||l||(a=e.pointToData(r)),null!=a&&yf(t,a,g,!1,d)}})});var v={};return rA(u,function(t,e){var i=t.linkGroup;i&&!f[e]&&rA(i.axesInfo,function(e,n){var r=f[n];if(e!==t&&r){var a=r.value;i.mapper&&(a=t.axis.scale.parse(i.mapper(a,If(e),If(t)))),v[t.key]=a}})}),rA(v,function(t,e){yf(u[e],t,g,!0,d)}),bf(f,u,d),Mf(p,r,t,o),Sf(u,o,i),d}},lA=(Lh({type:"axisPointer",coordSysAxesInfo:null,defaultOption:{show:"auto",triggerOn:null,zlevel:0,z:50,type:"line",snap:!1,triggerTooltip:!0,value:null,status:null,link:[],animation:null,animationDurationUpdate:200,lineStyle:{color:"#aaa",width:1,type:"solid"},shadowStyle:{color:"rgba(150,150,150,0.3)"},label:{show:!0,formatter:null,precision:"auto",margin:3,color:"#fff",padding:[5,7,5,7],backgroundColor:"auto",borderColor:null,borderWidth:0,shadowBlur:3,shadowColor:"#aaa"},handle:{show:!1,icon:"M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4h1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7v-1.2h6.6z M13.3,22H6.7v-1.2h6.6z M13.3,19.6H6.7v-1.2h6.6z",size:45,margin:50,color:"#333",shadowBlur:3,shadowColor:"#aaa",shadowOffsetX:0,shadowOffsetY:2,throttle:40}}}),hr()),hA=f,uA=Oh({type:"axisPointer",render:function(t,e,i){var n=e.getComponent("tooltip"),r=t.get("triggerOn")||n&&n.get("triggerOn")||"mousemove|click";Cf("axisPointer",i,function(t,e,i){"none"!==r&&("leave"===t||r.indexOf(t)>=0)&&i({type:"updateAxisPointer",currTrigger:t,x:e&&e.offsetX,y:e&&e.offsetY})})},remove:function(t,e){Ef(e.getZr(),"axisPointer"),uA.superApply(this._model,"remove",arguments)},dispose:function(t,e){Ef("axisPointer",e),uA.superApply(this._model,"dispose",arguments)}}),cA=hr(),dA=n,fA=y;zf.prototype={_group:null,_lastGraphicKey:null,_handle:null,_dragging:!1,_lastValue:null,_lastStatus:null,_payloadInfo:null,animationThreshold:15,render:function(t,e,i,n){var r=e.get("value"),a=e.get("status");if(this._axisModel=t,this._axisPointerModel=e,this._api=i,n||this._lastValue!==r||this._lastStatus!==a){this._lastValue=r,this._lastStatus=a;var o=this._group,s=this._handle;if(!a||"hide"===a)return o&&o.hide(),void(s&&s.hide());o&&o.show(),s&&s.show();var l={};this.makeElOption(l,r,t,e,i);var h=l.graphicKey;h!==this._lastGraphicKey&&this.clear(i),this._lastGraphicKey=h;var u=this._moveAnimation=this.determineAnimation(t,e);if(o){var c=x(Bf,e,u);this.updatePointerEl(o,l,c,e),this.updateLabelEl(o,l,c,e)}else o=this._group=new Fv,this.createPointerEl(o,l,t,e),this.createLabelEl(o,l,t,e),i.getZr().add(o);Vf(o,e,!0),this._renderHandle(r)}},remove:function(t){this.clear(t)},dispose:function(t){this.clear(t)},determineAnimation:function(t,e){var i=e.get("animation"),n=t.axis,r="category"===n.type,a=e.get("snap");if(!a&&!r)return!1;if("auto"===i||null==i){var o=this.animationThreshold;if(r&&n.getBandWidth()>o)return!0;if(a){var s=dd(t).seriesDataCount,l=n.getExtent();return Math.abs(l[0]-l[1])/s>o}return!1}return i===!0},makeElOption:function(){},createPointerEl:function(t,e){var i=e.pointer;if(i){var n=cA(t).pointerEl=new Nx[i.type](dA(e.pointer));t.add(n)}},createLabelEl:function(t,e,i,n){if(e.label){var r=cA(t).labelEl=new dx(dA(e.label));t.add(r),Nf(r,n)}},updatePointerEl:function(t,e,i){var n=cA(t).pointerEl;n&&e.pointer&&(n.setStyle(e.pointer.style),i(n,{shape:e.pointer.shape}))},updateLabelEl:function(t,e,i,n){var r=cA(t).labelEl;r&&(r.setStyle(e.label.style),i(r,{shape:e.label.shape,position:e.label.position}),Nf(r,n))},_renderHandle:function(t){if(!this._dragging&&this.updateHandleTransform){var e=this._axisPointerModel,i=this._api.getZr(),n=this._handle,r=e.getModel("handle"),a=e.get("status");if(!r.get("show")||!a||"hide"===a)return n&&i.remove(n),void(this._handle=null);var o;this._handle||(o=!0,n=this._handle=lo(r.get("icon"),{cursor:"move",draggable:!0,onmousemove:function(t){tv(t.event)},onmousedown:fA(this._onHandleDragMove,this,0,0),drift:fA(this._onHandleDragMove,this),ondragend:fA(this._onHandleDragEnd,this)}),i.add(n)),Vf(n,e,!1);var s=["color","borderColor","borderWidth","opacity","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"];n.setStyle(r.getItemStyle(null,s));var l=r.get("size");_(l)||(l=[l,l]),n.attr("scale",[l[0]/2,l[1]/2]),yl(this,"_doDispatchAxisPointer",r.get("throttle")||0,"fixRate"),this._moveHandleToValue(t,o)}},_moveHandleToValue:function(t,e){Bf(this._axisPointerModel,!e&&this._moveAnimation,this._handle,Ff(this.getHandleTransform(t,this._axisModel,this._axisPointerModel)))},_onHandleDragMove:function(t,e){var i=this._handle;if(i){this._dragging=!0;var n=this.updateHandleTransform(Ff(i),[t,e],this._axisModel,this._axisPointerModel);this._payloadInfo=n,i.stopAnimation(),i.attr(Ff(n)),cA(i).lastProp=null,this._doDispatchAxisPointer()}},_doDispatchAxisPointer:function(){var t=this._handle;if(t){var e=this._payloadInfo,i=this._axisModel;this._api.dispatchAction({type:"updateAxisPointer",x:e.cursorPoint[0],y:e.cursorPoint[1],tooltipOption:e.tooltipOption,axesInfo:[{axisDim:i.axis.dim,axisIndex:i.componentIndex}]})}},_onHandleDragEnd:function(){this._dragging=!1;var t=this._handle;if(t){var e=this._axisPointerModel.get("value");this._moveHandleToValue(e),this._api.dispatchAction({type:"hideTip"})}},getHandleTransform:null,updateHandleTransform:null,clear:function(t){this._lastValue=null,this._lastStatus=null;var e=t.getZr(),i=this._group,n=this._handle;e&&i&&(this._lastGraphicKey=null,i&&e.remove(i),n&&e.remove(n),this._group=null,this._handle=null,this._payloadInfo=null)},doClear:function(){},buildLabel:function(t,e,i){return i=i||0,{x:t[i],y:t[1-i],width:e[i],height:e[1-i]}}},zf.prototype.constructor=zf,mr(zf);var pA=zf.extend({makeElOption:function(t,e,i,n,r){var a=i.axis,o=a.grid,s=n.get("type"),l=Kf(o,a).getOtherAxis(a).getGlobalExtent(),h=a.toGlobalCoord(a.dataToCoord(e,!0));if(s&&"none"!==s){var u=Wf(n),c=gA[s](a,h,l);c.style=u,t.graphicKey=c.type,t.pointer=c}var d=yd(o.model,i);Yf(e,t,d,i,n,r)},getHandleTransform:function(t,e,i){var n=yd(e.axis.grid.model,e,{labelInside:!1});return n.labelMargin=i.get("handle.margin"),{position:Xf(e.axis,t,n),rotation:n.rotation+(n.labelDirection<0?Math.PI:0)}},updateHandleTransform:function(t,e,i){var n=i.axis,r=n.grid,a=n.getGlobalExtent(!0),o=Kf(r,n).getOtherAxis(n).getGlobalExtent(),s="x"===n.dim?0:1,l=t.position;l[s]+=e[s],l[s]=Math.min(a[1],l[s]),l[s]=Math.max(a[0],l[s]);var h=(o[1]+o[0])/2,u=[h,h];u[s]=l[s];var c=[{verticalAlign:"middle"},{align:"center"}];return{position:l,rotation:t.rotation,cursorPoint:u,tooltipOption:c[s]}}}),gA={line:function(t,e,i){var n=Uf([e,i[0]],[e,i[1]],$f(t));return{type:"Line",subPixelOptimize:!0,shape:n}},shadow:function(t,e,i){var n=Math.max(1,t.getBandWidth()),r=i[1]-i[0];return{type:"Rect",shape:qf([e-n/2,i[0]],[n,r],$f(t))}}};lS.registerAxisPointerClass("CartesianAxisPointer",pA),bh(function(t){if(t){(!t.axisPointer||0===t.axisPointer.length)&&(t.axisPointer={});var e=t.axisPointer.link;e&&!_(e)&&(t.axisPointer.link=[e])}}),Mh(Jw.PROCESSOR.STATISTIC,function(t,e){t.getComponent("axisPointer").coordSysAxesInfo=ad(t,e)}),Ah({type:"updateAxisPointer",event:"updateAxisPointer",update:":updateAxisPointer"},sA);var vA=zf.extend({makeElOption:function(t,e,i,n,r){var a=i.axis;"angle"===a.dim&&(this.animationThreshold=Math.PI/18);var o,s=a.polar,l=s.getOtherAxis(a),h=l.getExtent();o=a["dataTo"+Yo(a.dim)](e);var u=n.get("type");if(u&&"none"!==u){var c=Wf(n),d=mA[u](a,s,o,h,c);d.style=c,t.graphicKey=d.type,t.pointer=d}var f=n.get("label.margin"),p=Qf(e,i,n,s,f);Hf(t,i,n,r,p)}}),mA={line:function(t,e,i,n){return"angle"===t.dim?{type:"Line",shape:Uf(e.coordToPoint([n[0],i]),e.coordToPoint([n[1],i]))}:{type:"Circle",shape:{cx:e.cx,cy:e.cy,r:i}}},shadow:function(t,e,i,n){var r=Math.max(1,t.getBandWidth()),a=Math.PI/180;return"angle"===t.dim?{type:"Sector",shape:jf(e.cx,e.cy,n[0],n[1],(-i-r/2)*a,(-i+r/2)*a)}:{type:"Sector",shape:jf(e.cx,e.cy,i-r/2,i+r/2,0,2*Math.PI)}}};lS.registerAxisPointerClass("PolarAxisPointer",vA),Ch(x(of,"bar")),Oh({type:"polar"}),Lh({type:"title",layoutMode:{type:"box",ignoreSize:!0},defaultOption:{zlevel:0,z:6,show:!0,text:"",target:"blank",subtext:"",subtarget:"blank",left:0,top:0,backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",borderWidth:0,padding:5,itemGap:10,textStyle:{fontSize:18,fontWeight:"bolder",color:"#333"},subtextStyle:{color:"#aaa"}}}),Oh({type:"title",render:function(t,e,i){if(this.group.removeAll(),t.get("show")){var n=this.group,r=t.getModel("textStyle"),a=t.getModel("subtextStyle"),o=t.get("textAlign"),s=D(t.get("textBaseline"),t.get("textVerticalAlign")),l=new tx({style:Za({},r,{text:t.get("text"),textFill:r.getTextColor()},{disableBox:!0}),z2:10}),h=l.getBoundingRect(),u=t.get("subtext"),c=new tx({style:Za({},a,{text:u,textFill:a.getTextColor(),y:h.height+t.get("itemGap"),textVerticalAlign:"top"},{disableBox:!0}),z2:10}),d=t.get("link"),f=t.get("sublink"),p=t.get("triggerEvent",!0);l.silent=!d&&!p,c.silent=!f&&!p,d&&l.on("click",function(){jo(d,"_"+t.get("target"))}),f&&c.on("click",function(){jo(f,"_"+t.get("subtarget"))}),l.eventData=c.eventData=p?{componentType:"title",componentIndex:t.componentIndex}:null,n.add(l),u&&n.add(c);var g=n.getBoundingRect(),v=t.getBoxLayoutParams();v.width=g.width,v.height=g.height;var m=$o(v,{width:i.getWidth(),height:i.getHeight()},t.get("padding"));o||(o=t.get("left")||t.get("right"),"middle"===o&&(o="center"),"right"===o?m.x+=m.width:"center"===o&&(m.x+=m.width/2)),s||(s=t.get("top")||t.get("bottom"),"center"===s&&(s="middle"),"bottom"===s?m.y+=m.height:"middle"===s&&(m.y+=m.height/2),s=s||"top"),n.attr("position",[m.x,m.y]);var y={textAlign:o,textVerticalAlign:s};l.setStyle(y),c.setStyle(y),g=n.getBoundingRect();var x=m.margin,_=t.getItemStyle(["color","opacity"]);_.fill=t.get("backgroundColor");var w=new dx({shape:{x:g.x-x[3],y:g.y-x[0],width:g.width+x[1]+x[3],height:g.height+x[0]+x[2],r:t.get("borderRadius")},style:_,subPixelOptimize:!0,silent:!0});n.add(w)}}}),Lh({type:"tooltip",dependencies:["axisPointer"],defaultOption:{zlevel:0,z:60,show:!0,showContent:!0,trigger:"item",triggerOn:"mousemove|click",alwaysShowContent:!1,displayMode:"single",renderMode:"auto",confine:!1,showDelay:0,hideDelay:100,transitionDuration:.4,enterable:!1,backgroundColor:"rgba(50,50,50,0.7)",borderColor:"#333",borderRadius:4,borderWidth:0,padding:5,extraCssText:"",axisPointer:{type:"line",axis:"auto",animation:"auto",animationDurationUpdate:200,animationEasingUpdate:"exponentialOut",crossStyle:{color:"#999",width:1,type:"dashed",textStyle:{}}},textStyle:{color:"#fff",fontSize:14}}});var yA=f,xA=Fo,_A=["","-webkit-","-moz-","-o-"],wA="position:absolute;display:block;border-style:solid;white-space:nowrap;z-index:9999999;";np.prototype={constructor:np,_enterable:!0,update:function(t){var e=this._container,i=e.currentStyle||document.defaultView.getComputedStyle(e),n=e.style;"absolute"!==n.position&&"absolute"!==i.position&&(n.position="relative");var r=t.get("alwaysShowContent");r&&this._moveTooltipIfResized()},_moveTooltipIfResized:function(){var t=this._styleCoord[2],e=this._styleCoord[3],i=t*this._zr.getWidth(),n=e*this._zr.getHeight();this.moveTo(i,n)},show:function(t){clearTimeout(this._hideTimeout);var e=this.el,i=this._styleCoord;e.style.cssText=wA+ep(t)+";left:"+i[0]+"px;top:"+i[1]+"px;"+(t.get("extraCssText")||""),e.style.display=e.innerHTML?"block":"none",e.style.pointerEvents=this._enterable?"auto":"none",this._show=!0},setContent:function(t){this.el.innerHTML=null==t?"":t},setEnterable:function(t){this._enterable=t},getSize:function(){var t=this.el;return[t.clientWidth,t.clientHeight]},moveTo:function(t,e){var i=this._styleCoord;ip(i,this._zr,this._appendToBody,t,e);var n=this.el.style;n.left=i[0]+"px",n.top=i[1]+"px"},hide:function(){this.el.style.display="none",this._show=!1},hideLater:function(t){!this._show||this._inContent&&this._enterable||(t?(this._hideDelay=t,this._show=!1,this._hideTimeout=setTimeout(y(this.hide,this),t)):this.hide())},isShow:function(){return this._show},dispose:function(){this.el.parentNode.removeChild(this.el)},getOuterSize:function(){var t=this.el.clientWidth,e=this.el.clientHeight;if(document.defaultView&&document.defaultView.getComputedStyle){var i=document.defaultView.getComputedStyle(this.el);i&&(t+=parseInt(i.borderLeftWidth,10)+parseInt(i.borderRightWidth,10),e+=parseInt(i.borderTopWidth,10)+parseInt(i.borderBottomWidth,10))}return{width:t,height:e}}},ap.prototype={constructor:ap,_enterable:!0,update:function(t){var e=t.get("alwaysShowContent");e&&this._moveTooltipIfResized()},_moveTooltipIfResized:function(){var t=this._styleCoord[2],e=this._styleCoord[3],i=t*this._zr.getWidth(),n=e*this._zr.getHeight();this.moveTo(i,n)},show:function(){this._hideTimeout&&clearTimeout(this._hideTimeout),this.el.attr("show",!0),this._show=!0},setContent:function(t,e,i){this.el&&this._zr.remove(this.el);for(var n={},r=t,a="{marker",o="|}",s=r.indexOf(a);s>=0;){var l=r.indexOf(o),h=r.substr(s+a.length,l-s-a.length);n["marker"+h]=h.indexOf("sub")>-1?{textWidth:4,textHeight:4,textBorderRadius:2,textBackgroundColor:e[h],textOffset:[3,0]}:{textWidth:10,textHeight:10,textBorderRadius:5,textBackgroundColor:e[h]},r=r.substr(l+1),s=r.indexOf("{marker")}var u=i.getModel("textStyle"),c=u.get("fontSize"),d=i.get("textLineHeight");null==d&&(d=Math.round(3*c/2)),this.el=new tx({style:Za({},u,{rich:n,text:t,textBackgroundColor:i.get("backgroundColor"),textBorderRadius:i.get("borderRadius"),textFill:i.get("textStyle.color"),textPadding:i.get("padding"),textLineHeight:d}),z:i.get("z")}),this._zr.add(this.el);var f=this;this.el.on("mouseover",function(){f._enterable&&(clearTimeout(f._hideTimeout),f._show=!0),f._inContent=!0}),this.el.on("mouseout",function(){f._enterable&&f._show&&f.hideLater(f._hideDelay),f._inContent=!1})},setEnterable:function(t){this._enterable=t},getSize:function(){var t=this.el.getBoundingRect();return[t.width,t.height]},moveTo:function(t,e){if(this.el){var i=this._styleCoord;rp(i,this._zr,t,e),this.el.attr("position",[i[0],i[1]])}},hide:function(){this.el&&this.el.hide(),this._show=!1},hideLater:function(t){!this._show||this._inContent&&this._enterable||(t?(this._hideDelay=t,this._show=!1,this._hideTimeout=setTimeout(y(this.hide,this),t)):this.hide())},isShow:function(){return this._show},dispose:function(){clearTimeout(this._hideTimeout),this.el&&this._zr.remove(this.el)},getOuterSize:function(){var t=this.getSize();return{width:t[0],height:t[1]}}};var bA=y,MA=f,SA=bo,AA=new dx({shape:{x:-1,y:-1,width:2,height:2}});Oh({type:"tooltip",init:function(t,e){if(!Ag.node){var i=t.getComponent("tooltip"),n=i.get("renderMode");this._renderMode=pr(n);var r;"html"===this._renderMode?(r=new np(e.getDom(),e,{appendToBody:i.get("appendToBody",!0)}),this._newLine="<br/>"):(r=new ap(e),this._newLine="\n"),this._tooltipContent=r}},render:function(t,e,i){if(!Ag.node){this.group.removeAll(),this._tooltipModel=t,this._ecModel=e,this._api=i,this._lastDataByCoordSys=null,this._alwaysShowContent=t.get("alwaysShowContent");var n=this._tooltipContent;n.update(t),n.setEnterable(t.get("enterable")),this._initGlobalListener(),this._keepShow()}},_initGlobalListener:function(){var t=this._tooltipModel,e=t.get("triggerOn");Cf("itemTooltip",this._api,bA(function(t,i,n){"none"!==e&&(e.indexOf(t)>=0?this._tryShow(i,n):"leave"===t&&this._hide(n))},this))},_keepShow:function(){var t=this._tooltipModel,e=this._ecModel,i=this._api;if(null!=this._lastX&&null!=this._lastY&&"none"!==t.get("triggerOn")){var n=this;clearTimeout(this._refreshUpdateTimeout),this._refreshUpdateTimeout=setTimeout(function(){!i.isDisposed()&&n.manuallyShowTip(t,e,i,{x:n._lastX,y:n._lastY})})}},manuallyShowTip:function(t,e,i,n){if(n.from!==this.uid&&!Ag.node){var r=sp(n,i);this._ticket="";var a=n.dataByCoordSys;if(n.tooltip&&null!=n.x&&null!=n.y){var o=AA;o.position=[n.x,n.y],o.update(),o.tooltip=n.tooltip,this._tryShow({offsetX:n.x,offsetY:n.y,target:o},r)}else if(a)this._tryShow({offsetX:n.x,offsetY:n.y,position:n.position,dataByCoordSys:n.dataByCoordSys,tooltipOption:n.tooltipOption},r);else if(null!=n.seriesIndex){if(this._manuallyAxisShowTip(t,e,i,n))return;var s=nA(n,e),l=s.point[0],h=s.point[1];null!=l&&null!=h&&this._tryShow({offsetX:l,offsetY:h,position:n.position,target:s.el},r)}else null!=n.x&&null!=n.y&&(i.dispatchAction({type:"updateAxisPointer",x:n.x,y:n.y}),this._tryShow({offsetX:n.x,offsetY:n.y,position:n.position,target:i.getZr().findHover(n.x,n.y).target},r))}},manuallyHideTip:function(t,e,i,n){var r=this._tooltipContent;!this._alwaysShowContent&&this._tooltipModel&&r.hideLater(this._tooltipModel.get("hideDelay")),this._lastX=this._lastY=null,n.from!==this.uid&&this._hide(sp(n,i))},_manuallyAxisShowTip:function(t,e,i,n){var r=n.seriesIndex,a=n.dataIndex,o=e.getComponent("axisPointer").coordSysAxesInfo;if(null!=r&&null!=a&&null!=o){var s=e.getSeriesByIndex(r);if(s){var l=s.getData(),t=op([l.getItemModel(a),s,(s.coordinateSystem||{}).model,t]);if("axis"===t.get("trigger"))return i.dispatchAction({type:"updateAxisPointer",seriesIndex:r,dataIndex:a,position:n.position}),!0}}},_tryShow:function(t,e){var i=t.target,n=this._tooltipModel;if(n){this._lastX=t.offsetX,this._lastY=t.offsetY;var r=t.dataByCoordSys;r&&r.length?this._showAxisTooltip(r,t):i&&null!=i.dataIndex?(this._lastDataByCoordSys=null,this._showSeriesItemTooltip(t,i,e)):i&&i.tooltip?(this._lastDataByCoordSys=null,this._showComponentItemTooltip(t,i,e)):(this._lastDataByCoordSys=null,this._hide(e))}},_showOrMove:function(t,e){var i=t.get("showDelay");e=y(e,this),clearTimeout(this._showTimout),i>0?this._showTimout=setTimeout(e,i):e()},_showAxisTooltip:function(t,e){var i=this._ecModel,n=this._tooltipModel,a=[e.offsetX,e.offsetY],o=[],s=[],l=op([e.tooltipOption,n]),h=this._renderMode,u=this._newLine,c={};MA(t,function(t){MA(t.dataByAxis,function(t){var e=i.getComponent(t.axisDim+"Axis",t.axisIndex),n=t.value,a=[];if(e&&null!=n){var l=Zf(n,e.axis,i,t.seriesDataIndices,t.valueLabelOpt);f(t.seriesDataIndices,function(o){var u=i.getSeriesByIndex(o.seriesIndex),d=o.dataIndexInside,f=u&&u.getDataParams(d);if(f.axisDim=t.axisDim,f.axisIndex=t.axisIndex,f.axisType=t.axisType,f.axisId=t.axisId,f.axisValue=Xu(e.axis,n),f.axisValueLabel=l,f){s.push(f);var p,g=u.formatTooltip(d,!0,null,h);if(M(g)){p=g.html;var v=g.markers;r(c,v)}else p=g;a.push(p)}});var d=l;o.push("html"!==h?a.join(u):(d?Vo(d)+u:"")+a.join(u))}})},this),o.reverse(),o=o.join(this._newLine+this._newLine);var d=e.position;this._showOrMove(l,function(){this._updateContentNotChangedOnAxis(t)?this._updatePosition(l,d,a[0],a[1],this._tooltipContent,s):this._showTooltipContent(l,o,s,Math.random(),a[0],a[1],d,void 0,c)})},_showSeriesItemTooltip:function(t,e,i){var n=this._ecModel,r=e.seriesIndex,a=n.getSeriesByIndex(r),o=e.dataModel||a,s=e.dataIndex,l=e.dataType,h=o.getData(l),u=op([h.getItemModel(s),o,a&&(a.coordinateSystem||{}).model,this._tooltipModel]),c=u.get("trigger");if(null==c||"item"===c){var d,f,p=o.getDataParams(s,l),g=o.formatTooltip(s,!1,l,this._renderMode);M(g)?(d=g.html,f=g.markers):(d=g,f=null);var v="item_"+o.name+"_"+s;this._showOrMove(u,function(){this._showTooltipContent(u,d,p,v,t.offsetX,t.offsetY,t.position,t.target,f)}),i({type:"showTip",dataIndexInside:s,dataIndex:h.getRawIndex(s),seriesIndex:r,from:this.uid})}},_showComponentItemTooltip:function(t,e,i){var n=e.tooltip;if("string"==typeof n){var r=n;n={content:r,formatter:r}}var a=new po(n,this._tooltipModel,this._ecModel),o=a.get("content"),s=Math.random();this._showOrMove(a,function(){this._showTooltipContent(a,o,a.get("formatterParams")||{},s,t.offsetX,t.offsetY,t.position,e)}),i({type:"showTip",from:this.uid})},_showTooltipContent:function(t,e,i,n,r,a,o,s,l){if(this._ticket="",t.get("showContent")&&t.get("show")){var h=this._tooltipContent,u=t.get("formatter");o=o||t.get("position");var c=e;if(u&&"string"==typeof u)c=Wo(u,i,!0);else if("function"==typeof u){var d=bA(function(e,n){e===this._ticket&&(h.setContent(n,l,t),this._updatePosition(t,o,r,a,h,i,s))},this);this._ticket=n,c=u(i,n,d)}h.setContent(c,l,t),h.show(t),this._updatePosition(t,o,r,a,h,i,s)}},_updatePosition:function(t,e,i,n,r,a,o){var s=this._api.getWidth(),l=this._api.getHeight();e=e||t.get("position");var h=r.getSize(),u=t.get("align"),c=t.get("verticalAlign"),d=o&&o.getBoundingRect().clone();if(o&&d.applyTransform(o.transform),"function"==typeof e&&(e=e([i,n],a,r.el,d,{viewSize:[s,l],contentSize:h.slice()})),_(e))i=SA(e[0],s),n=SA(e[1],l);else if(M(e)){e.width=h[0],e.height=h[1];var f=$o(e,{width:s,height:l});i=f.x,n=f.y,u=null,c=null}else if("string"==typeof e&&o){var p=up(e,d,h);i=p[0],n=p[1]}else{var p=lp(i,n,r,s,l,u?null:20,c?null:20);i=p[0],n=p[1]}if(u&&(i-=cp(u)?h[0]/2:"right"===u?h[0]:0),c&&(n-=cp(c)?h[1]/2:"bottom"===c?h[1]:0),t.get("confine")){var p=hp(i,n,r,s,l);i=p[0],n=p[1]}r.moveTo(i,n)},_updateContentNotChangedOnAxis:function(t){var e=this._lastDataByCoordSys,i=!!e&&e.length===t.length;return i&&MA(e,function(e,n){var r=e.dataByAxis||{},a=t[n]||{},o=a.dataByAxis||[];i&=r.length===o.length,i&&MA(r,function(t,e){var n=o[e]||{},r=t.seriesDataIndices||[],a=n.seriesDataIndices||[];i&=t.value===n.value&&t.axisType===n.axisType&&t.axisId===n.axisId&&r.length===a.length,i&&MA(r,function(t,e){var n=a[e];i&=t.seriesIndex===n.seriesIndex&&t.dataIndex===n.dataIndex})})}),this._lastDataByCoordSys=t,!!i},_hide:function(t){this._lastDataByCoordSys=null,t({type:"hideTip",from:this.uid})},dispose:function(t,e){Ag.node||(this._tooltipContent.dispose(),Ef("itemTooltip",e))}}),Ah({type:"showTip",event:"showTip",update:"tooltip:manuallyShowTip"},function(){}),Ah({type:"hideTip",event:"hideTip",update:"tooltip:manuallyHideTip"},function(){});var IA=No,TA=Vo,CA=Lh({type:"marker",dependencies:["series","grid","polar","geo"],init:function(t,e,i){if(wg&&"marker"===this.type)throw new Error("Marker component is abstract component. Use markLine, markPoint, markArea instead.");this.mergeDefaultAndTheme(t,i),this._mergeOption(t,i,!1,!0)},isAnimationEnabled:function(){if(Ag.node)return!1;var t=this.__hostSeries;return this.getShallow("animation")&&t&&t.isAnimationEnabled()},mergeOption:function(t,e){this._mergeOption(t,e,!1,!1)},_mergeOption:function(t,e,i,n){var r=this.constructor,a=this.mainType+"Model";i||e.eachSeries(function(t){var i=t.get(this.mainType,!0),s=t[a];return i&&i.data?(s?s._mergeOption(i,e,!0):(n&&dp(i),f(i.data,function(t){t instanceof Array?(dp(t[0]),dp(t[1])):dp(t)}),s=new r(i,this,e),o(s,{mainType:this.mainType,seriesIndex:t.seriesIndex,name:t.name,createdBySelf:!0}),s.__hostSeries=t),void(t[a]=s)):void(t[a]=null)},this)},formatTooltip:function(t,e,i,n){var r=this.getData(),a=this.getRawValue(t),o=_(a)?p(a,IA).join(", "):IA(a),s=r.getName(t),l=TA(this.name),h="html"===n?"<br/>":"\n";return(null!=a||s)&&(l+=h),s&&(l+=TA(s),null!=a&&(l+=" : ")),null!=a&&(l+=TA(o)),l},getData:function(){return this._data},setData:function(t){this._data=t}});c(CA,X_),CA.extend({type:"markLine",defaultOption:{zlevel:0,z:5,symbol:["circle","arrow"],symbolSize:[8,16],precision:2,tooltip:{trigger:"item"},label:{show:!0,position:"end",distance:5},lineStyle:{type:"dashed"},emphasis:{label:{show:!0},lineStyle:{width:3}},animationEasing:"linear"}});var DA=h,kA=x,PA={min:kA(gp,"min"),max:kA(gp,"max"),average:kA(gp,"average")},LA=px.prototype,OA=vx.prototype,EA=ga({type:"ec-line",style:{stroke:"#000",fill:null},shape:{x1:0,y1:0,x2:0,y2:0,percent:1,cpx1:null,cpy1:null},buildPath:function(t,e){this[bp(e)?"_buildPathLine":"_buildPathCurve"](t,e)},_buildPathLine:LA.buildPath,_buildPathCurve:OA.buildPath,pointAt:function(t){return this[bp(this.shape)?"_pointAtLine":"_pointAtCurve"](t)},_pointAtLine:LA.pointAt,_pointAtCurve:OA.pointAt,tangentAt:function(t){var e=this.shape,i=bp(e)?[e.x2-e.x1,e.y2-e.y1]:this._tangentAtCurve(t);return te(i,i)},_tangentAtCurve:OA.tangentAt}),zA=["fromSymbol","toSymbol"],BA=Cp.prototype;BA.beforeUpdate=Tp,BA._createLine=function(t,e,i){var n=t.hostModel,r=t.getItemLayout(e),a=Ap(r);a.shape.percent=0,eo(a,{shape:{percent:1}},n,e),this.add(a);var o=new tx({name:"label",lineLabelOriginalOpacity:1});this.add(o),f(zA,function(i){var n=Sp(i,t,e);this.add(n),this[Mp(i)]=t.getItemVisual(e,i)},this),this._updateCommonStl(t,e,i)},BA.updateData=function(t,e,i){var n=t.hostModel,r=this.childOfName("line"),a=t.getItemLayout(e),o={shape:{}};Ip(o.shape,a),to(r,o,n,e),f(zA,function(i){var n=t.getItemVisual(e,i),r=Mp(i);if(this[r]!==n){this.remove(this.childOfName(i));var a=Sp(i,t,e);this.add(a)}this[r]=n},this),this._updateCommonStl(t,e,i)},BA._updateCommonStl=function(t,e,i){var n=t.hostModel,r=this.childOfName("line"),a=i&&i.lineStyle,o=i&&i.hoverLineStyle,l=i&&i.labelModel,h=i&&i.hoverLabelModel;if(!i||t.hasItemOption){var u=t.getItemModel(e);a=u.getModel("lineStyle").getLineStyle(),o=u.getModel("emphasis.lineStyle").getLineStyle(),l=u.getModel("label"),h=u.getModel("emphasis.label")}var c=t.getItemVisual(e,"color"),d=k(t.getItemVisual(e,"opacity"),a.opacity,1);r.useStyle(s({strokeNoScale:!0,fill:"none",stroke:c,opacity:d},a)),r.hoverStyle=o,f(zA,function(t){var e=this.childOfName(t);e&&(e.setColor(c),e.setStyle({opacity:d}))},this);var p,g,v=l.getShallow("show"),m=h.getShallow("show"),y=this.childOfName("label");if((v||m)&&(p=c||"#000",g=n.getFormattedLabel(e,"normal",t.dataType),null==g)){var x=n.getRawValue(e);g=null==x?t.getName(e):isFinite(x)?Mo(x):x}var w=v?g:null,b=m?D(n.getFormattedLabel(e,"emphasis",t.dataType),g):null,M=y.style;if(null!=w||null!=b){Za(y.style,l,{text:w},{autoColor:p}),y.__textAlign=M.textAlign,y.__verticalAlign=M.textVerticalAlign,y.__position=l.get("position")||"middle";var S=l.get("distance");_(S)||(S=[S,S]),y.__labelDistance=S}y.hoverStyle=null!=b?{text:b,textFill:h.getTextColor(!0),fontStyle:h.getShallow("fontStyle"),fontWeight:h.getShallow("fontWeight"),fontSize:h.getShallow("fontSize"),fontFamily:h.getShallow("fontFamily")}:{text:null},y.ignore=!v&&!m,Na(this)},BA.highlight=function(){this.trigger("emphasis")},BA.downplay=function(){this.trigger("normal")},BA.updateLayout=function(t,e){this.setLinePoints(t.getItemLayout(e))},BA.setLinePoints=function(t){var e=this.childOfName("line");Ip(e.shape,t),e.dirty()},u(Cp,Fv);var RA=Dp.prototype;RA.isPersistent=function(){return!0},RA.updateData=function(t){var e=this,i=e.group,n=e._lineData;e._lineData=t,n||i.removeAll();var r=Op(t);t.diff(n).add(function(i){kp(e,t,i,r)}).update(function(i,a){Pp(e,n,t,a,i,r)}).remove(function(t){i.remove(n.getItemGraphicEl(t))}).execute()},RA.updateLayout=function(){var t=this._lineData;t&&t.eachItemGraphicEl(function(e,i){e.updateLayout(t,i)},this)},RA.incrementalPrepareUpdate=function(t){this._seriesScope=Op(t),this._lineData=null,this.group.removeAll()},RA.incrementalUpdate=function(t,e){function i(t){t.isGroup||Lp(t)||(t.incremental=t.useHoverLayer=!0)}for(var n=t.start;n<t.end;n++){var r=e.getItemLayout(n);if(zp(r)){var a=new this._ctor(e,n,this._seriesScope);a.traverse(i),this.group.add(a),e.setItemGraphicEl(n,a)}}},RA.remove=function(){this._clearIncremental(),this._incremental=null,this.group.removeAll()},RA._clearIncremental=function(){var t=this._incremental;t&&t.clearDisplaybles()};var NA=Oh({type:"marker",init:function(){this.markerGroupMap=N()},render:function(t,e,i){var n=this.markerGroupMap;n.each(function(t){t.__keep=!1});var r=this.type+"Model";e.eachSeries(function(t){var n=t[r];n&&this.renderSeries(t,n,e,i)},this),n.each(function(t){!t.__keep&&this.group.remove(t.group)},this)},renderSeries:function(){}}),FA=function(t,e,i,a){var s=t.getData(),l=a.type;if(!_(a)&&("min"===l||"max"===l||"average"===l||"median"===l||null!=a.xAxis||null!=a.yAxis)){var h,u;if(null!=a.yAxis||null!=a.xAxis)h=e.getAxis(null!=a.yAxis?"y":"x"),u=C(a.yAxis,a.xAxis);else{var c=mp(a,s,e,t);h=c.valueAxis;var d=gu(s,c.valueDataDim);u=wp(s,d,l)}var f="x"===h.dim?0:1,p=1-f,g=n(a),v={};g.type=null,g.coord=[],v.coord=[],g.coord[p]=-1/0,v.coord[p]=1/0;var m=i.get("precision");m>=0&&"number"==typeof u&&(u=+u.toFixed(Math.min(m,20))),g.coord[f]=v.coord[f]=u,a=[g,v,{type:l,valueIndex:a.valueIndex,value:u}]}return a=[vp(t,a[0]),vp(t,a[1]),o({},a[2])],a[2].type=a[2].type||"",r(a[2],a[0]),r(a[2],a[1]),a};NA.extend({type:"markLine",updateTransform:function(t,e,i){e.eachSeries(function(t){var e=t.markLineModel;if(e){var n=e.getData(),r=e.__from,a=e.__to;r.each(function(e){Fp(r,e,!0,t,i),Fp(a,e,!1,t,i)}),n.each(function(t){n.setItemLayout(t,[r.getItemLayout(t),a.getItemLayout(t)])}),this.markerGroupMap.get(t.id).updateLayout()}},this)},renderSeries:function(t,e,i,n){function r(e,i,r){var a=e.getItemModel(i);Fp(e,i,r,t,n),e.setItemVisual(i,{symbolRotate:a.get("symbolRotate"),symbolSize:a.get("symbolSize")||g[r?0:1],symbol:a.get("symbol",!0)||p[r?0:1],color:a.get("itemStyle.color")||s.getVisual("color")})}var a=t.coordinateSystem,o=t.id,s=t.getData(),l=this.markerGroupMap,h=l.get(o)||l.set(o,new Dp);this.group.add(h.group);var u=Vp(a,t,e),c=u.from,d=u.to,f=u.line;e.__from=c,e.__to=d,e.setData(f);var p=e.get("symbol"),g=e.get("symbolSize");_(p)||(p=[p,p]),"number"==typeof g&&(g=[g,g]),u.from.each(function(t){r(c,t,!0),r(d,t,!1)}),f.each(function(t){var e=f.getItemModel(t).get("lineStyle.color");f.setItemVisual(t,{color:e||c.getItemVisual(t,"color")}),f.setItemLayout(t,[c.getItemLayout(t),d.getItemLayout(t)]),f.setItemVisual(t,{fromSymbolRotate:c.getItemVisual(t,"symbolRotate"),fromSymbolSize:c.getItemVisual(t,"symbolSize"),fromSymbol:c.getItemVisual(t,"symbol"),toSymbolRotate:d.getItemVisual(t,"symbolRotate"),toSymbolSize:d.getItemVisual(t,"symbolSize"),toSymbol:d.getItemVisual(t,"symbol")})}),h.updateData(f),u.line.eachItemGraphicEl(function(t){t.traverse(function(t){t.dataModel=e})}),h.__keep=!0,h.group.silent=e.get("silent")||t.get("silent")}}),bh(function(t){t.markLine=t.markLine||{}}),l_.registerSubTypeDefaulter("dataZoom",function(){return"slider"});var VA=["x","y","z","radius","angle","single"],WA=["cartesian2d","polar","singleAxis"],HA=Hp(VA,["axisIndex","axis","index","id"]),GA=function(t,e,i,n,r,a){t=t||0;var o=i[1]-i[0];if(null!=r&&(r=Xp(r,[0,o])),null!=a&&(a=Math.max(a,null!=r?r:0)),"all"===n){var s=Math.abs(e[1]-e[0]);s=Xp(s,[0,o]),r=a=Xp(s,[r,a]),n=0}e[0]=Xp(e[0],i),e[1]=Xp(e[1],i);var l=Zp(e,n);e[n]+=t;var h=r||0,u=i.slice();l.sign<0?u[0]+=h:u[1]-=h,e[n]=Xp(e[n],u);var c=Zp(e,n);null!=r&&(c.sign!==l.sign||c.span<r)&&(e[1-n]=e[n]+l.sign*r);var c=Zp(e,n);return null!=a&&c.span>a&&(e[1-n]=e[n]+c.sign*a),e},ZA=f,XA=So,YA=function(t,e,i,n){this._dimName=t,this._axisIndex=e,this._valueWindow,this._percentWindow,this._dataExtent,this._minMaxSpan,this.ecModel=n,this._dataZoomModel=i};YA.prototype={constructor:YA,hostedBy:function(t){return this._dataZoomModel===t},getDataValueWindow:function(){return this._valueWindow.slice()},getDataPercentWindow:function(){return this._percentWindow.slice()},getTargetSeriesModels:function(){var t=[],e=this.ecModel;
return e.eachSeries(function(i){if(Wp(i.get("coordinateSystem"))){var n=this._dimName,r=e.queryComponents({mainType:n+"Axis",index:i.get(n+"AxisIndex"),id:i.get(n+"AxisId")})[0];this._axisIndex===(r&&r.componentIndex)&&t.push(i)}},this),t},getAxisModel:function(){return this.ecModel.getComponent(this._dimName+"Axis",this._axisIndex)},getOtherAxisModel:function(){var t,e,i=this._dimName,n=this.ecModel,r=this.getAxisModel(),a="x"===i||"y"===i;a?(e="gridIndex",t="x"===i?"y":"x"):(e="polarIndex",t="angle"===i?"radius":"angle");var o;return n.eachComponent(t+"Axis",function(t){(t.get(e)||0)===(r.get(e)||0)&&(o=t)}),o},getMinMaxSpan:function(){return n(this._minMaxSpan)},calculateDataWindow:function(t){function e(t,e,i,n,r){var o=r?"Span":"ValueSpan";GA(0,t,i,"all",u["min"+o],u["max"+o]);for(var s=0;2>s;s++)e[s]=wo(t[s],i,n,!0),r&&(e[s]=a.parse(e[s]))}var i,n=this._dataExtent,r=this.getAxisModel(),a=r.axis.scale,o=this._dataZoomModel.getRangePropMode(),s=[0,100],l=[],h=[];ZA(["start","end"],function(e,r){var u=t[e],c=t[e+"Value"];"percent"===o[r]?(null==u&&(u=s[r]),c=a.parse(wo(u,s,n))):(i=!0,c=null==c?n[r]:a.parse(c),u=wo(c,n,s)),h[r]=c,l[r]=u}),XA(h),XA(l);var u=this._minMaxSpan;return i?e(h,l,n,s,!1):e(l,h,s,n,!0),{valueWindow:h,percentWindow:l}},reset:function(t){if(t===this._dataZoomModel){var e=this.getTargetSeriesModels();this._dataExtent=Yp(this,this._dimName,e),jp(this);var i=this.calculateDataWindow(t.settledOption);this._valueWindow=i.valueWindow,this._percentWindow=i.percentWindow,qp(this)}},restore:function(t){t===this._dataZoomModel&&(this._valueWindow=this._percentWindow=null,qp(this,!0))},filterData:function(t){function e(t){return t>=a[0]&&t<=a[1]}if(t===this._dataZoomModel){var i=this._dimName,n=this.getTargetSeriesModels(),r=t.get("filterMode"),a=this._valueWindow;"none"!==r&&ZA(n,function(t){var n=t.getData(),o=n.mapDimension(i,!0);o.length&&("weakFilter"===r?n.filterSelf(function(t){for(var e,i,r,s=0;s<o.length;s++){var l=n.get(o[s],t),h=!isNaN(l),u=l<a[0],c=l>a[1];if(h&&!u&&!c)return!0;h&&(r=!0),u&&(e=!0),c&&(i=!0)}return r&&e&&i}):ZA(o,function(i){if("empty"===r)t.setData(n=n.map(i,function(t){return e(t)?t:0/0}));else{var o={};o[i]=a,n.selectRange(o)}}),ZA(o,function(t){n.setApproximateExtent(a,t)}))})}}};var UA=f,qA=HA,jA=Lh({type:"dataZoom",dependencies:["xAxis","yAxis","zAxis","radiusAxis","angleAxis","singleAxis","series"],defaultOption:{zlevel:0,z:4,orient:null,xAxisIndex:null,yAxisIndex:null,filterMode:"filter",throttle:null,start:0,end:100,startValue:null,endValue:null,minSpan:null,maxSpan:null,minValueSpan:null,maxValueSpan:null,rangeMode:null},init:function(t,e,i){this._dataIntervalByAxis={},this._dataInfo={},this._axisProxies={},this.textStyleModel,this._autoThrottle=!0,this._rangePropMode=["percent","percent"];var n=Kp(t);this.settledOption=n,this.mergeDefaultAndTheme(t,i),this.doInit(n)},mergeOption:function(t){var e=Kp(t);r(this.option,t,!0),r(this.settledOption,e,!0),this.doInit(e)},doInit:function(t){var e=this.option;Ag.canvasSupported||(e.realtime=!1),this._setDefaultThrottle(t),$p(this,t);var i=this.settledOption;UA([["start","startValue"],["end","endValue"]],function(t,n){"value"===this._rangePropMode[n]&&(e[t[0]]=i[t[0]]=null)},this),this.textStyleModel=this.getModel("textStyle"),this._resetTarget(),this._giveAxisProxies()},_giveAxisProxies:function(){var t=this._axisProxies;this.eachTargetAxis(function(e,i,n,r){var a=this.dependentModels[e.axis][i],o=a.__dzAxisProxy||(a.__dzAxisProxy=new YA(e.name,i,this,r));t[e.name+"_"+i]=o},this)},_resetTarget:function(){var t=this.option,e=this._judgeAutoMode();qA(function(e){var i=e.axisIndex;t[i]=tr(t[i])},this),"axisIndex"===e?this._autoSetAxisIndex():"orient"===e&&this._autoSetOrient()},_judgeAutoMode:function(){var t=this.option,e=!1;qA(function(i){null!=t[i.axisIndex]&&(e=!0)},this);var i=t.orient;return null==i&&e?"orient":e?void 0:(null==i&&(t.orient="horizontal"),"axisIndex")},_autoSetAxisIndex:function(){var t=!0,e=this.get("orient",!0),i=this.option,n=this.dependentModels;if(t){var r="vertical"===e?"y":"x";n[r+"Axis"].length?(i[r+"AxisIndex"]=[0],t=!1):UA(n.singleAxis,function(n){t&&n.get("orient",!0)===e&&(i.singleAxisIndex=[n.componentIndex],t=!1)})}t&&qA(function(e){if(t){var n=[],r=this.dependentModels[e.axis];if(r.length&&!n.length)for(var a=0,o=r.length;o>a;a++)"category"===r[a].get("type")&&n.push(a);i[e.axisIndex]=n,n.length&&(t=!1)}},this),t&&this.ecModel.eachSeries(function(t){this._isSeriesHasAllAxesTypeOf(t,"value")&&qA(function(e){var n=i[e.axisIndex],r=t.get(e.axisIndex),a=t.get(e.axisId),o=t.ecModel.queryComponents({mainType:e.axis,index:r,id:a})[0];if(wg&&!o)throw new Error(e.axis+' "'+C(r,a,0)+'" not found');r=o.componentIndex,h(n,r)<0&&n.push(r)})},this)},_autoSetOrient:function(){var t;this.eachTargetAxis(function(e){!t&&(t=e.name)},this),this.option.orient="y"===t?"vertical":"horizontal"},_isSeriesHasAllAxesTypeOf:function(t,e){var i=!0;return qA(function(n){var r=t.get(n.axisIndex),a=this.dependentModels[n.axis][r];a&&a.get("type")===e||(i=!1)},this),i},_setDefaultThrottle:function(t){if(t.hasOwnProperty("throttle")&&(this._autoThrottle=!1),this._autoThrottle){var e=this.ecModel.option;this.option.throttle=e.animation&&e.animationDurationUpdate>0?100:20}},getFirstTargetAxisModel:function(){var t;return qA(function(e){if(null==t){var i=this.get(e.axisIndex);i.length&&(t=this.dependentModels[e.axis][i[0]])}},this),t},eachTargetAxis:function(t,e){var i=this.ecModel;qA(function(n){UA(this.get(n.axisIndex),function(r){t.call(e,n,r,this,i)},this)},this)},getAxisProxy:function(t,e){return this._axisProxies[t+"_"+e]},getAxisModel:function(t,e){var i=this.getAxisProxy(t,e);return i&&i.getAxisModel()},setRawRange:function(t){var e=this.option,i=this.settledOption;UA([["start","startValue"],["end","endValue"]],function(n){(null!=t[n[0]]||null!=t[n[1]])&&(e[n[0]]=i[n[0]]=t[n[0]],e[n[1]]=i[n[1]]=t[n[1]])},this),$p(this,t)},setCalculatedRange:function(t){var e=this.option;UA(["start","startValue","end","endValue"],function(i){e[i]=t[i]})},getPercentRange:function(){var t=this.findRepresentativeAxisProxy();return t?t.getDataPercentWindow():void 0},getValueRange:function(t,e){if(null!=t||null!=e)return this.getAxisProxy(t,e).getDataValueWindow();var i=this.findRepresentativeAxisProxy();return i?i.getDataValueWindow():void 0},findRepresentativeAxisProxy:function(t){if(t)return t.__dzAxisProxy;var e=this._axisProxies;for(var i in e)if(e.hasOwnProperty(i)&&e[i].hostedBy(this))return e[i];for(var i in e)if(e.hasOwnProperty(i)&&!e[i].hostedBy(this))return e[i]},getRangePropMode:function(){return this._rangePropMode.slice()}}),KA=K_.extend({type:"dataZoom",render:function(t,e,i){this.dataZoomModel=t,this.ecModel=e,this.api=i},getTargetCoordInfo:function(){function t(t,e,i,n){for(var r,a=0;a<i.length;a++)if(i[a].model===t){r=i[a];break}r||i.push(r={model:t,axisModels:[],coordIndex:n}),r.axisModels.push(e)}var e=this.dataZoomModel,i=this.ecModel,n={};return e.eachTargetAxis(function(e,r){var a=i.getComponent(e.axis,r);if(a){var o=a.getCoordSysModel();o&&t(o,a,n[o.mainType]||(n[o.mainType]=[]),o.componentIndex)}},this),n}}),$A=(jA.extend({type:"dataZoom.slider",layoutMode:"box",defaultOption:{show:!0,right:"ph",top:"ph",width:"ph",height:"ph",left:null,bottom:null,backgroundColor:"rgba(47,69,84,0)",dataBackground:{lineStyle:{color:"#2f4554",width:.5,opacity:.3},areaStyle:{color:"rgba(47,69,84,0.3)",opacity:.3}},borderColor:"#ddd",fillerColor:"rgba(167,183,204,0.4)",handleIcon:"M8.2,13.6V3.9H6.3v9.7H3.1v14.9h3.3v9.7h1.8v-9.7h3.3V13.6H8.2z M9.7,24.4H4.8v-1.4h4.9V24.4z M9.7,19.1H4.8v-1.4h4.9V19.1z",handleSize:"100%",handleStyle:{color:"#a7b7cc"},labelPrecision:null,labelFormatter:null,showDetail:!0,showDataShadow:"auto",realtime:!0,zoomLock:!1,textStyle:{color:"#333"}}}),dx),QA=wo,JA=So,tI=y,eI=f,iI=7,nI=1,rI=30,aI="horizontal",oI="vertical",sI=5,lI=["line","bar","candlestick","scatter"],hI=KA.extend({type:"dataZoom.slider",init:function(t,e){this._displayables={},this._orient,this._range,this._handleEnds,this._size,this._handleWidth,this._handleHeight,this._location,this._dragging,this._dataShadowInfo,this.api=e},render:function(t,e,i,n){return hI.superApply(this,"render",arguments),yl(this,"_dispatchZoomAction",this.dataZoomModel.get("throttle"),"fixRate"),this._orient=t.get("orient"),this.dataZoomModel.get("show")===!1?void this.group.removeAll():(n&&"dataZoom"===n.type&&n.from===this.uid||this._buildView(),void this._updateView())},remove:function(){hI.superApply(this,"remove",arguments),xl(this,"_dispatchZoomAction")},dispose:function(){hI.superApply(this,"dispose",arguments),xl(this,"_dispatchZoomAction")},_buildView:function(){var t=this.group;t.removeAll(),this._resetLocation(),this._resetInterval();var e=this._displayables.barGroup=new Fv;this._renderBackground(),this._renderHandle(),this._renderDataShadow(),t.add(e),this._positionGroup()},_resetLocation:function(){var t=this.dataZoomModel,e=this.api,i=this._findCoordRect(),n={width:e.getWidth(),height:e.getHeight()},r=this._orient===aI?{right:n.width-i.x-i.width,top:n.height-rI-iI,width:i.width,height:rI}:{right:iI,top:i.y,width:rI,height:i.height},a=Jo(t.option);f(["right","top","width","height"],function(t){"ph"===a[t]&&(a[t]=r[t])});var o=$o(a,n,t.padding);this._location={x:o.x,y:o.y},this._size=[o.width,o.height],this._orient===oI&&this._size.reverse()},_positionGroup:function(){var t=this.group,e=this._location,i=this._orient,n=this.dataZoomModel.getFirstTargetAxisModel(),r=n&&n.get("inverse"),a=this._displayables.barGroup,o=(this._dataShadowInfo||{}).otherAxisInverse;a.attr(i!==aI||r?i===aI&&r?{scale:o?[-1,1]:[-1,-1]}:i!==oI||r?{scale:o?[-1,-1]:[-1,1],rotation:Math.PI/2}:{scale:o?[1,-1]:[1,1],rotation:Math.PI/2}:{scale:o?[1,1]:[1,-1]});var s=t.getBoundingRect([a]);t.attr("position",[e.x-s.x,e.y-s.y])},_getViewExtent:function(){return[0,this._size[0]]},_renderBackground:function(){var t=this.dataZoomModel,e=this._size,i=this._displayables.barGroup;i.add(new $A({silent:!0,shape:{x:0,y:0,width:e[0],height:e[1]},style:{fill:t.get("backgroundColor")},z2:-40})),i.add(new $A({shape:{x:0,y:0,width:e[0],height:e[1]},style:{fill:"transparent"},z2:0,onclick:y(this._onClickPanelClick,this)}))},_renderDataShadow:function(){var t=this._dataShadowInfo=this._prepareDataShadowInfo();if(t){var e=this._size,i=t.series,n=i.getRawData(),r=i.getShadowDim?i.getShadowDim():t.otherDim;if(null!=r){var a=n.getDataExtent(r),o=.3*(a[1]-a[0]);a=[a[0]-o,a[1]+o];var l,h=[0,e[1]],u=[0,e[0]],c=[[e[0],0],[0,0]],d=[],f=u[1]/(n.count()-1),p=0,g=Math.round(n.count()/e[0]);n.each([r],function(t,e){if(g>0&&e%g)return void(p+=f);var i=null==t||isNaN(t)||""===t,n=i?0:QA(t,a,h,!0);i&&!l&&e?(c.push([c[c.length-1][0],0]),d.push([d[d.length-1][0],0])):!i&&l&&(c.push([p,0]),d.push([p,0])),c.push([p,n]),d.push([p,n]),p+=f,l=i});var v=this.dataZoomModel;this._displayables.barGroup.add(new lx({shape:{points:c},style:s({fill:v.get("dataBackgroundColor")},v.getModel("dataBackground.areaStyle").getAreaStyle()),silent:!0,z2:-20})),this._displayables.barGroup.add(new hx({shape:{points:d},style:v.getModel("dataBackground.lineStyle").getLineStyle(),silent:!0,z2:-19}))}}},_prepareDataShadowInfo:function(){var t=this.dataZoomModel,e=t.get("showDataShadow");if(e!==!1){var i,n=this.ecModel;return t.eachTargetAxis(function(r,a){var o=t.getAxisProxy(r.name,a).getTargetSeriesModels();f(o,function(t){if(!(i||e!==!0&&h(lI,t.get("type"))<0)){var o,s=n.getComponent(r.axis,a).axis,l=Qp(r.name),u=t.coordinateSystem;null!=l&&u.getOtherAxis&&(o=u.getOtherAxis(s).inverse),l=t.getData().mapDimension(l),i={thisAxis:s,series:t,thisDim:r.name,otherDim:l,otherAxisInverse:o}}},this)},this),i}},_renderHandle:function(){var t=this._displayables,e=t.handles=[],i=t.handleLabels=[],n=this._displayables.barGroup,r=this._size,a=this.dataZoomModel;n.add(t.filler=new $A({draggable:!0,cursor:Jp(this._orient),drift:tI(this._onDragMove,this,"all"),ondragstart:tI(this._showDataInfo,this,!0),ondragend:tI(this._onDragEnd,this),onmouseover:tI(this._showDataInfo,this,!0),onmouseout:tI(this._showDataInfo,this,!1),style:{fill:a.get("fillerColor"),textPosition:"inside"}})),n.add(new $A({silent:!0,subPixelOptimize:!0,shape:{x:0,y:0,width:r[0],height:r[1]},style:{stroke:a.get("dataBackgroundColor")||a.get("borderColor"),lineWidth:nI,fill:"rgba(0,0,0,0)"}})),eI([0,1],function(t){var r=lo(a.get("handleIcon"),{cursor:Jp(this._orient),draggable:!0,drift:tI(this._onDragMove,this,t),ondragend:tI(this._onDragEnd,this),onmouseover:tI(this._showDataInfo,this,!0),onmouseout:tI(this._showDataInfo,this,!1)},{x:-1,y:0,width:2,height:2}),o=r.getBoundingRect();this._handleHeight=bo(a.get("handleSize"),this._size[1]),this._handleWidth=o.width/o.height*this._handleHeight,r.setStyle(a.getModel("handleStyle").getItemStyle());var s=a.get("handleColor");null!=s&&(r.style.fill=s),n.add(e[t]=r);var l=a.textStyleModel;this.group.add(i[t]=new tx({silent:!0,invisible:!0,style:{x:0,y:0,text:"",textVerticalAlign:"middle",textAlign:"center",textFill:l.getTextColor(),textFont:l.getFont()},z2:10}))},this)},_resetInterval:function(){var t=this._range=this.dataZoomModel.getPercentRange(),e=this._getViewExtent();this._handleEnds=[QA(t[0],[0,100],e,!0),QA(t[1],[0,100],e,!0)]},_updateInterval:function(t,e){var i=this.dataZoomModel,n=this._handleEnds,r=this._getViewExtent(),a=i.findRepresentativeAxisProxy().getMinMaxSpan(),o=[0,100];GA(e,n,r,i.get("zoomLock")?"all":t,null!=a.minSpan?QA(a.minSpan,o,r,!0):null,null!=a.maxSpan?QA(a.maxSpan,o,r,!0):null);var s=this._range,l=this._range=JA([QA(n[0],r,o,!0),QA(n[1],r,o,!0)]);return!s||s[0]!==l[0]||s[1]!==l[1]},_updateView:function(t){var e=this._displayables,i=this._handleEnds,n=JA(i.slice()),r=this._size;eI([0,1],function(t){var n=e.handles[t],a=this._handleHeight;n.attr({scale:[a/2,a/2],position:[i[t],r[1]/2-a/2]})},this),e.filler.setShape({x:n[0],y:0,width:n[1]-n[0],height:r[1]}),this._updateDataInfo(t)},_updateDataInfo:function(t){function e(t){var e=io(n.handles[t].parent,this.group),i=ro(0===t?"right":"left",e),s=this._handleWidth/2+sI,l=no([c[t]+(0===t?-s:s),this._size[1]/2],e);r[t].setStyle({x:l[0],y:l[1],textVerticalAlign:a===aI?"middle":i,textAlign:a===aI?i:"center",text:o[t]})}var i=this.dataZoomModel,n=this._displayables,r=n.handleLabels,a=this._orient,o=["",""];if(i.get("showDetail")){var s=i.findRepresentativeAxisProxy();if(s){var l=s.getAxisModel().axis,h=this._range,u=t?s.calculateDataWindow({start:h[0],end:h[1]}).valueWindow:s.getDataValueWindow();o=[this._formatLabel(u[0],l),this._formatLabel(u[1],l)]}}var c=JA(this._handleEnds.slice());e.call(this,0),e.call(this,1)},_formatLabel:function(t,e){var i=this.dataZoomModel,n=i.get("labelFormatter"),r=i.get("labelPrecision");(null==r||"auto"===r)&&(r=e.getPixelPrecision());var a=null==t||isNaN(t)?"":"category"===e.type||"time"===e.type?e.scale.getLabel(Math.round(t)):t.toFixed(Math.min(r,20));return w(n)?n(t,a):b(n)?n.replace("{value}",a):a},_showDataInfo:function(t){t=this._dragging||t;var e=this._displayables.handleLabels;e[0].attr("invisible",!t),e[1].attr("invisible",!t)},_onDragMove:function(t,e,i,n){this._dragging=!0,tv(n.event);var r=this._displayables.barGroup.getLocalTransform(),a=no([e,i],r,!0),o=this._updateInterval(t,a[0]),s=this.dataZoomModel.get("realtime");this._updateView(!s),o&&s&&this._dispatchZoomAction()},_onDragEnd:function(){this._dragging=!1,this._showDataInfo(!1);var t=this.dataZoomModel.get("realtime");!t&&this._dispatchZoomAction()},_onClickPanelClick:function(t){var e=this._size,i=this._displayables.barGroup.transformCoordToLocal(t.offsetX,t.offsetY);if(!(i[0]<0||i[0]>e[0]||i[1]<0||i[1]>e[1])){var n=this._handleEnds,r=(n[0]+n[1])/2,a=this._updateInterval("all",i[0]-r);this._updateView(),a&&this._dispatchZoomAction()}},_dispatchZoomAction:function(){var t=this._range;this.api.dispatchAction({type:"dataZoom",from:this.uid,dataZoomId:this.dataZoomModel.id,start:t[0],end:t[1]})},_findCoordRect:function(){var t;if(eI(this.getTargetCoordInfo(),function(e){if(!t&&e.length){var i=e[0].model.coordinateSystem;t=i.getRect&&i.getRect()}}),!t){var e=this.api.getWidth(),i=this.api.getHeight();t={x:.2*e,y:.2*i,width:.6*e,height:.6*i}}return t}});Mh({getTargetSeries:function(t){var e=N();return t.eachComponent("dataZoom",function(t){t.eachTargetAxis(function(t,i,n){var r=n.getAxisProxy(t.name,i);f(r.getTargetSeriesModels(),function(t){e.set(t.uid,t)})})}),e},modifyOutputEnd:!0,overallReset:function(t,e){t.eachComponent("dataZoom",function(t){t.eachTargetAxis(function(t,i,n){n.getAxisProxy(t.name,i).reset(n,e)}),t.eachTargetAxis(function(t,i,n){n.getAxisProxy(t.name,i).filterData(n,e)})}),t.eachComponent("dataZoom",function(t){var e=t.findRepresentativeAxisProxy(),i=e.getDataPercentWindow(),n=e.getDataValueWindow();t.setCalculatedRange({start:i[0],end:i[1],startValue:n[0],endValue:n[1]})})}}),Ah("dataZoom",function(t,e){var i=Gp(y(e.eachComponent,e,"dataZoom"),HA,function(t,e){return t.get(e.axisIndex)}),n=[];e.eachComponent({mainType:"dataZoom",query:t},function(t){n.push.apply(n,i(t).nodes)}),f(n,function(e){e.setRawRange({start:t.start,end:t.end,startValue:t.startValue,endValue:t.endValue})})}),jA.extend({type:"dataZoom.inside",defaultOption:{disabled:!1,zoomLock:!1,zoomOnMouseWheel:!0,moveOnMouseMove:!0,moveOnMouseWheel:!1,preventDefaultMouseMove:!0}});var uI="\x00_ec_interaction_mutex";Ah({type:"takeGlobalCursor",event:"globalCursorTaken",update:"update"},function(){}),c(ig,Ug);var cI="\x00_ec_dataZoom_roams",dI=y,fI=KA.extend({type:"dataZoom.inside",init:function(){this._range},render:function(t,e,i){fI.superApply(this,"render",arguments),this._range=t.getPercentRange(),f(this.getTargetCoordInfo(),function(e,n){var r=p(e,function(t){return fg(t.model)});f(e,function(e){var a=e.model,o={};f(["pan","zoom","scrollMove"],function(t){o[t]=dI(pI[t],this,e,n)},this),cg(i,{coordId:fg(a),allCoordIds:r,containsPoint:function(t,e,i){return a.coordinateSystem.containPoint([e,i])},dataZoomId:t.id,dataZoomModel:t,getRange:o})},this)},this)},dispose:function(){dg(this.api,this.dataZoomModel.id),fI.superApply(this,"dispose",arguments),this._range=null}}),pI={zoom:function(t,e,i,n){var r=this._range,a=r.slice(),o=t.axisModels[0];if(o){var s=gI[e](null,[n.originX,n.originY],o,i,t),l=(s.signal>0?s.pixelStart+s.pixelLength-s.pixel:s.pixel-s.pixelStart)/s.pixelLength*(a[1]-a[0])+a[0],h=Math.max(1/n.scale,0);a[0]=(a[0]-l)*h+l,a[1]=(a[1]-l)*h+l;var u=this.dataZoomModel.findRepresentativeAxisProxy().getMinMaxSpan();return GA(0,a,[0,100],0,u.minSpan,u.maxSpan),this._range=a,r[0]!==a[0]||r[1]!==a[1]?a:void 0}},pan:xg(function(t,e,i,n,r,a){var o=gI[n]([a.oldX,a.oldY],[a.newX,a.newY],e,r,i);return o.signal*(t[1]-t[0])*o.pixel/o.pixelLength}),scrollMove:xg(function(t,e,i,n,r,a){var o=gI[n]([0,0],[a.scrollDelta,a.scrollDelta],e,r,i);return o.signal*(t[1]-t[0])*a.scrollDelta})},gI={grid:function(t,e,i,n,r){var a=i.axis,o={},s=r.model.coordinateSystem.getRect();return t=t||[0,0],"x"===a.dim?(o.pixel=e[0]-t[0],o.pixelLength=s.width,o.pixelStart=s.x,o.signal=a.inverse?1:-1):(o.pixel=e[1]-t[1],o.pixelLength=s.height,o.pixelStart=s.y,o.signal=a.inverse?-1:1),o},polar:function(t,e,i,n,r){var a=i.axis,o={},s=r.model.coordinateSystem,l=s.getRadiusAxis().getExtent(),h=s.getAngleAxis().getExtent();return t=t?s.pointToCoord(t):[0,0],e=s.pointToCoord(e),"radiusAxis"===i.mainType?(o.pixel=e[0]-t[0],o.pixelLength=l[1]-l[0],o.pixelStart=l[0],o.signal=a.inverse?1:-1):(o.pixel=e[1]-t[1],o.pixelLength=h[1]-h[0],o.pixelStart=h[0],o.signal=a.inverse?-1:1),o},singleAxis:function(t,e,i,n,r){var a=i.axis,o=r.model.coordinateSystem.getRect(),s={};return t=t||[0,0],"horizontal"===a.orient?(s.pixel=e[0]-t[0],s.pixelLength=o.width,s.pixelStart=o.x,s.signal=a.inverse?1:-1):(s.pixel=e[1]-t[1],s.pixelLength=o.height,s.pixelStart=o.y,s.signal=a.inverse?-1:1),s}};t.version=Fw,t.dependencies=Vw,t.PRIORITY=Jw,t.init=gh,t.connect=vh,t.disConnect=mh,t.disconnect=xb,t.dispose=yh,t.getInstanceByDom=xh,t.getInstanceById=_h,t.registerTheme=wh,t.registerPreprocessor=bh,t.registerProcessor=Mh,t.registerPostUpdate=Sh,t.registerAction=Ah,t.registerCoordinateSystem=Ih,t.getCoordinateSystemDimensions=Th,t.registerLayout=Ch,t.registerVisual=Dh,t.registerLoading=Ph,t.extendComponentModel=Lh,t.extendComponentView=Oh,t.extendSeriesModel=Eh,t.extendChartView=zh,t.setCanvasCreator=Bh,t.registerMap=Rh,t.getMap=Nh,t.dataTool=_b,t.zrender=Rm,t.number=jx,t.format=i_,t.throttle=ml,t.helper=bM,t.matrix=sv,t.vector=Xg,t.color=Iv,t.parseGeoJSON=SM,t.parseGeoJson=CM,t.util=DM,t.graphic=kM,t.List=Lb,t.Model=po,t.Axis=TM,t.env=Ag});