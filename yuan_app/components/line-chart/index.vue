<template>
	<view>
		<!-- #ifdef MP-WEIXIN -->
		<uni-ec-canvas class="uni-ec-canvas" ref="lineCanvas" canvas-id="badroom-lazy-load-chart"
			:ec="ec"></uni-ec-canvas>
		<!-- #endif -->
		<!-- #ifdef APP -->
			<text>app</text>
			<view ref="lineCanvas" class="uni-ec-canvas"></view>
		<!-- #endif -->
		<!-- #ifdef (H5 || H5-HEJIA) -->
		<view :id="optionId" class="uni-ec-canvas"></view>
		<!-- #endif -->
	</view>
</template>

<script>
	import uniEcCanvas from '@/components/uni-ec-canvas/uni-ec-canvas.vue';
	import * as echarts from "@/components/uni-ec-canvas/echarts";
	let lineChart = null
	export default {
		name: 'LineChart',
		components: {
			uniEcCanvas
		},
		props: {
			lineColor: {
				type: String,
				default: '#01B09A'
			},
			datas: {
				type: Array,
				default: function() {
					return []
				}
			},
			markLines: {
				type: Array,
				default: function() {
					return [ 0, 0, 0 ]
				}
			},
			max: {
				type: Number || String,
				default: 0
			},
			showAxisX: {
				type: Boolean,
				default: true
			},
			title: {
				type: String
			}
		},
		data() {
			return {
				optionId: undefined,
				ec: {
					lazyLoad: true
				},
				option: {
					tooltip: {
						show: true,
						trigger: 'axis',
						formatter: function(a) {
							var str = '';
							for (var h = 0; h < a.length; h++) {
								str += `${a[h].name}\r\n${a[h].seriesName}: ${a[h].value}`;
							}
							return str;
						},
						textStyle: {
							fontSize: 12,
						}
					},
					// dataZoom: [
					// 	{
					// 		show: true,
					// 		realtime: true,
					// 		start: 30,
					// 		end: 70,
					// 		xAxisIndex: [0, 1],
					// 	},
					// 	{
					// 		type: 'inside',
					// 		realtime: true,
					// 		start: 30,
					// 		end: 70,
					// 		xAxisIndex: [0, 1],
					// 	},
					// ],
					grid: {
						left: '8%',
						right: '10%',
						bottom: '0',
						top: '20',
						containLabel: true,
					},
					xAxis: {
						data: [],
					 	show: this.hiddenAxisX,
						axisLabel: {
							color: '#01B09A',
							textStyle: {
								fontSize: '13'
							},
						},
						axisTick: {
							show: true,
							lineStyle: {
								color: '#01B09A',
								width: '2'
							}
						},
						axisLine: {
							lineStyle: {
								color: '#01B09A',
								width: '2'
							}
						},
						splitLine: {
							show: false,
							lineStyle: {
								color: '#01B09A',
								width: '2'
							}
						}
					},
					yAxis: {
						min: 0,
						axisLine: {
							show: false,
						},
						axisTick: {
							show: false,
						},
						splitLine: {
							show: false,
						},
						axisLabel: {
							show: false,
						},
					},
					series: [{
						name: this.title,
						type: 'line',
						smooth: true,
						showSymbol: false,
						data: [],
						lineStyle: { width: 2, color: this.lineColor },
						markLine: {
							symbol: false,
							tooltip: {
								show: false,
							},
							data: [
								{
									yAxis: this.markLines[0], 
									name: '过速',
									lineStyle: {
										color: '#ED2E1C',
									},
									label: {
										normal: {
											color: '#666',
											formatter: '过速'
										}
									},
								},
								{
									yAxis: this.markLines[1], 
									name: '正常',
									lineStyle: {
										color: '#81D88A',
									},
									label: {
										normal: {
											color: '#666',
											formatter: '正常'
										}
									},
								},
								{
									yAxis: this.markLines[2], 
									name: '过缓',
									lineStyle: {
										color: '#FAD347',
									},
									label: {
										normal: {
											color: '#666',
											formatter: '过缓'
										}
									},
								}
							]
						}
					}]
				}
			}
		},
		mounted() {
			// 设置随机数id
			let t = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
			let len = t.length
			let id = ''
			for (let i = 0; i < 32; i++) {
				id += t.charAt(Math.floor(Math.random() * len))
			}
			this.optionId = id
			
			let _titles = [];
			let _values = [];
			this.datas.map((item) => {
				_titles.push(item[0]);
				_values.push(item[1]);
			});
			let _max = Math.max(..._values) || 0;
			if (_max < this.max) {
				_max = this.max
			}
			this.option.xAxis.data = _titles || [];
			this.option.yAxis.max = _max > 0 ? _max + parseInt(_max * 0.1) : _max;
			this.option.series[0].data = _values || [];
			this.$nextTick(() => {
				console.log("init line chart")
				// #ifdef MP-WEIXIN
				this.$refs.lineCanvas.init(this.initChart)
				// #endif
				
				// #ifdef (H5 || H5-HEJIA)
				console.log("h5")
				let el = document.getElementById(this.optionId);
				let myChart= echarts.init(el);
				myChart.setOption(this.option);
				// #endif
			})
		},
		beforeDestroy() {
			// #ifdef MP-WEIXIN
			lineChart.dispose();
			// #endif
			console.log('lineChart.dispose');
		},
		methods: {
			initChart(canvas, width, height, canvasDpr) {
				lineChart = echarts.init(canvas, null, {
					width: width,
					height: height,
					devicePixelRatio: canvasDpr
				})
				canvas.setChart(lineChart)
				lineChart.setOption(this.option)
				return lineChart
			}
		}
	}
</script>

<style>
	.uni-ec-canvas {
		width: 100%;
		height: 200rpx;
		display: block;
	}
</style>
