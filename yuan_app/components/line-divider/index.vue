<template>
  <view :class="{'vertical-line': direction === 'vertical', 'horizontal-line': direction === 'horizontal'}" :style="lineStyle"></view>
</template>

<script>
export default {
  props: {
    direction: {
      type: String,
      default: 'horizontal' // 默认横向
    },
    lineWidth: {
      type: String,
      default: '1px' // 默认线条宽度
    },
    lineColor: {
      type: String,
      default: '#000' // 默认线条颜色
    },
    lineLength: {
      type: String,
      default: '100%' // 默认线条长度
    }
  },
  computed: {
    lineStyle() {
      return {
        width: this.direction === 'horizontal' ? this.lineLength : this.lineWidth,
        height: this.direction === 'vertical' ? this.lineLength : this.lineWidth,
        backgroundColor: this.lineColor
      }
    }
  }
}
</script>

<style scoped>
.vertical-line {
  width: 0;
}

.horizontal-line {
  height: 0;
}
</style>