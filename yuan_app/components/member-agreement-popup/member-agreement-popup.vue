<template>
	<u-popup
		v-model="showModal"
		mode="bottom"
		:mask-close-able="false"
		class="popup"
	>	
		<view style="padding: 20rpx;">
			<view class="popup__title u-line-1">
				{{title}}
			</view>
			<scroll-view scroll-y="true" style="margin-bottom:140rpx;max-height:75vh;">
				<rich-text :nodes="content"></rich-text>
			</scroll-view>
			<view class="popup__btns">
				<u-button shape="circle" class="agree-btn diy-btn" size="medium" 
					:custom-style="{ 'width': '300rpx', 'color': '#01B09A'}"  hover-class="none"
					@click="cancel">{{cancelText}}</u-button>
				
				<u-button shape="circle" class="agree-btn diy-btn" size="medium" 
					:custom-style="{ 'width': '300rpx', 'color': 'white', 'background-color': '#01B09A'}"  hover-class="none"
					@click="confirm">{{confirmText}}</u-button>
			</view>
		</view>
	</u-popup>
</template>

<script>
const events = {
	confirm: "confirm",
	cancel: "cancel",
}
export default {
	name: "member-agreement-popup",
	props: {
		show: {
			type: Boolean,
		},
		title: {
			type: String,
			default: "服务协议条款",
		},
		content: {
			type: String,
		},
		cancelText: {
			type: String,
			default: "不同意",
		},
		confirmText: {
			type: String,
			default: "我已阅读并同意",
		},
	},
	data() {
		return {
			showModal: false,
		};
	},
	watch: {
		show: {
			handler: function(nVal) {
				this.showModal = nVal;
				if (!nVal) {
					return;
				}
			},
			immediate: true
		}
	},
	methods: {
		confirm() {
			this.$emit(events.confirm);
		},
		cancel() {
			this.$emit(events.cancel);
		}
	}
}
</script>

<style lang="scss" scoped>
.popup {
	&__title {
		padding: 48rpx 0;
		font-size: 32rpx;
		font-weight: 500;
		text-align: center;
		color: $u-main-color;
	}
	&__btns {
		margin-top: 30rpx;
		margin-bottom: 30rpx;
		text-align: center;
		position: fixed;
		width: 100%;
		bottom: 0;
		.agree-btn {
			display: inline-block;
			margin: 0 20rpx;
		}
	}
}
</style>