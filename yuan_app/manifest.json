{
    "name" : "pinanbao-uni",
    "appid" : "__UNI__18962C2",
    "description" : "多平台快速开发的UI框架",
    "versionName" : "1.8.4",
    "versionCode" : "100",
    "transformPx" : false,
    "app-plus" : {
        // APP-VUE分包，可提APP升启动速度，2.7.12开始支持，兼容微信小程序分包方案，默认关闭
        "optimization" : {
            "subPackages" : true
        },
        "safearea" : {
            "bottom" : {
                "offset" : "none"
            }
        },
        "splashscreen" : {
            "alwaysShowBeforeRender" : true,
            "waiting" : true,
            "autoclose" : true,
            "delay" : 0
        },
        "usingComponents" : true,
        "nvueCompiler" : "uni-app",
        "compilerVersion" : 3,
        "modules" : {},
        "distribute" : {
            "android" : {
                "permissions" : [
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_CONTACTS\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_CONTACTS\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.RECORD_AUDIO\"/>",
                    "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
                    "<uses-permission android:name=\"android.permission.MODIFY_AUDIO_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.CALL_PHONE\"/>",
                    "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>",
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"
                ],
                "abiFilters" : [ "armeabi-v7a", "arm64-v8a" ]
            },
            "ios" : {},
            "sdkConfigs" : {
                "ad" : {},
                "maps" : {}
            },
            "icons" : {
                "android" : {
                    "hdpi" : "unpackage/res/icons/72x72.png",
                    "xhdpi" : "unpackage/res/icons/96x96.png",
                    "xxhdpi" : "unpackage/res/icons/144x144.png",
                    "xxxhdpi" : "unpackage/res/icons/192x192.png"
                },
                "ios" : {
                    "appstore" : "unpackage/res/icons/1024x1024.png",
                    "ipad" : {
                        "app" : "unpackage/res/icons/76x76.png",
                        "app@2x" : "unpackage/res/icons/152x152.png",
                        "notification" : "unpackage/res/icons/20x20.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "proapp@2x" : "unpackage/res/icons/167x167.png",
                        "settings" : "unpackage/res/icons/29x29.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "spotlight" : "unpackage/res/icons/40x40.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png"
                    },
                    "iphone" : {
                        "app@2x" : "unpackage/res/icons/120x120.png",
                        "app@3x" : "unpackage/res/icons/180x180.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "notification@3x" : "unpackage/res/icons/60x60.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "settings@3x" : "unpackage/res/icons/87x87.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png",
                        "spotlight@3x" : "unpackage/res/icons/120x120.png"
                    }
                }
            }
        }
    },
    "quickapp" : {},
    "mp-weixin" : {
        "appid" : "wxda02870032b8f928",
        "setting" : {
            "urlCheck" : true,
            "es6" : false,
            "minified" : true,
            "postcss" : true
        },
        "usingComponents" : true,
        "optimization" : {
            "subPackages" : true,
            "treeShaking" : {
                "enable" : true
            }
        },
        "requiredPrivateInfos" : [ "chooseLocation", "getLocation" ],
        "permission" : {
            "scope.userLocation" : {
                "desc" : "获取用户当前位置进行定位操作"
            }
        },
        "lazyCodeLoading" : "requiredComponents",
        "navigateToMiniProgramAppIdList" : [ "wx701c3bb9fbea5d63", "wx739a9c524b1c5aa1" ]
    },
    "mp-alipay" : {
        "usingComponents" : true,
        "component2" : true
    },
    "mp-qq" : {
        "optimization" : {
            "subPackages" : true
        },
        "appid" : ""
    },
    "mp-baidu" : {
        "usingComponents" : true,
        "appid" : ""
    },
    "mp-toutiao" : {
        "usingComponents" : true,
        "appid" : ""
    },
    "h5" : {
        "devServer" : {
            "port" : 80,
            "https" : false
        },
        "template" : "template.h5.html",
        "router" : {
            "mode" : "history",
            "base" : ""
        },
        "optimization" : {
            "treeShaking" : {
                "enable" : false
            }
        },
        "title" : "与安宝",
        "sdkConfigs" : {
            "maps" : {
                "qqmap" : {
                    "key" : "FCABZ-6CIKF-HHUJP-NEYSL-652YV-JQFJ6"
                }
            }
        }
    }
}
