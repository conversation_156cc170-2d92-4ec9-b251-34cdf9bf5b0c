<template>
	<view class="family-familys-add-page">
		<view class="top-header-line"></view>
		
		<u-input v-model="familyName" :border="true" placeholder="请输入家庭名称" placeholder-style="color: #6B6B6B" :maxlength="15" />
		
		<view class="footer-btns">
			<u-button shape="circle" class="next-btn diy-btn" size="medium" :loading="submitLoading"
			:custom-style="{ 'width': '100%', 'color': 'white', 'background-color': '#01B09A' }"  hover-class="none"
			@click="handleCreateFamily"><u-icon name="plus" color="#fff" size="28"></u-icon>创建家庭</u-button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				familyName: '',
				submitLoading: false
			}
		},
		onShow() {
		},
		methods: {
			handleCreateFamily() {
				if (!this.familyName) {
					this.$toast('请填写家庭名称');
					return;
				}
				this.submitLoading = true;
				this.$u.api.execCreateFamily({ name: this.familyName }).then(res => {
					this.$toast('创建家庭成功');
					setTimeout(() => {
						this.submitLoading = false;
						uni.navigateBack({
							delta: 1
						})
					}, 500)
				}).catch(err => {
					this.submitLoading = false;
					uni.showToast({ duration: 2000, title: err.message || '发生未知错误', icon: 'none' })
				})
			}
		}
	}
</script>

<style lang="scss">
page {
	background-color: #fff;
}
</style>
<style lang="scss" scoped>
.family-familys-add-page {
	padding: 30rpx;
	.footer-btns {
		position: absolute;
		bottom: 40rpx;
		left: 40rpx;
		right: 40rpx;
		margin: 0 auto;
		.next-btn {
			display: block;
			margin-top: 20rpx;
		}
	}
}
::v-deep .u-input {
	border-radius: 50rpx !important;
	border-color: #6B6B6B !important;
}
</style>
