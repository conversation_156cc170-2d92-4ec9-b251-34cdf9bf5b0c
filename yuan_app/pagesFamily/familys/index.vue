<template>
	<view class="family-familys-page">
		<view class="top-header-line"></view>
		<u-empty v-if="!familys.length" text="暂无家庭数据" mode="list"></u-empty>
		<!-- <scroll-view v-else scroll-y style="height: 100%; width: 100%;"> -->
			<view class="content">
				<view v-for="(family, index) in familys" :key="index" class="family-item" :class="{ 'actived': currFamily.id === family.id }">
					<view class="item-content">
						<view class="detail">
							{{ family.name }}<span style="font-size: 22rpx; font-weight: normal; color: #666;" :style="{ 'color': currFamily.id === family.id ? 'white' : '#666' }">{{ family.selfCreate ? '' : `(来自 ${ family.createMemberName || '' } 的分享)` }}</span>
							<view class="infos">
								<view class="r-flex">
									<view class="c-flex-1">设备：{{ family.devCount || 0 }}</view>
									<view class="c-flex-1">被监护人：{{ family.olderCount || 0 }}</view>
								</view>
								<view class="r-flex">
									<view class="c-flex-1">成员：{{ family.memberCount || 0 }}</view>
									<view class="c-flex-1">紧急联系人：{{ family.contactCount || 0 }}</view>
								</view>
							</view>
						</view>
						<view class="btns">
							<!-- 可删除的条件：分享的、没数据的、不是默认的。第一条为默认的-->
							<text class="setting-btn" v-if=" !family.selfCreate||((family.devCount+family.olderCount+family.contactCount)<1 && index!=0)" @click="del(family.id)">删除</text>
							<text class="setting-btn" @click.stop="$navTo(`pagesFamily/detail/index?id=${family.id}`)" style="margin-left: 20rpx;">设置</text>
						</view>
					</view>
				</view>
				<u-loadmore v-if="familys && familys.length >= 10" :status="loadStatus" class="loadmore" />
			</view>
		<!-- </scroll-view> -->
		
		<view class="footer-btns">
			<!-- <u-button shape="circle" class="next-btn diy-btn" size="medium" 
			:custom-style="{ 'width': '100%', 'color': 'white', 'background-color': '#01B09A' }"  hover-class="none"
			@click="$navTo('pagesFamily/familys/add')"><u-icon name="plus" color="#fff" size="28"></u-icon>创建家庭</u-button> -->
			<u-button shape="circle" class="next-btn diy-btn" size="medium" 
			:custom-style="{ 'width': '100%', 'color': 'white', 'background-color': '#01B09A' }"  hover-class="none"
			@click="$navTo(`pagesFamily/address/index`)"><u-icon name="plus" color="#fff" size="28"></u-icon>创建家庭</u-button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				loadStatus: 'loadmore',
				page: 1,
				familys: [],
				currFamily: {},
			}
		},
		onShow() {
			this.fetchDatas();
			if (uni.getStorageSync('curr_family')) {
				this.currFamily = uni.getStorageSync('curr_family')
			}
		},
		methods: {
			// reachBottom() {
			// 	if(this.page >= 1) {
			// 		this.loadStatus = 'loading';
			// 		setTimeout(() => {
			// 			this.page = this.page + 1;
			// 			this.fetchDatas();
			// 		}, 1200);
			// 	}
			// },
			fetchDatas() {
				this.$u.api.fetchFamilyList({ current: this.page }).then(res => {
					let _familys = res || []
					if (this.page > 1) {
						this.familys = this.familys.concat(_familys)
					} else {
						this.familys = _familys
					}
					this.loadStatus = 'loadmore'
				})
			},
			del(familyId){
				uni.showModal({
					content: '删除家庭？',
					showCancel: true,
					success: ({ confirm, cancel }) => {
						if (cancel) {
							return;
						}
						uni.showLoading({
							title: '删除中...',
							mask: true
						})
						this.$u.api.deleteFamily({ id:familyId }).then(res => {
							console.log("删除成功！")
							uni.hideLoading();
							this.fetchDatas()
							let _currFamily = uni.getStorageSync('curr_family')
							if(_currFamily.id==familyId){
								uni.removeStorageSync('curr_family');
							}
							
							if(this.familys.lenght>0){
								let currFamily = this.familys[0]
								uni.setStorageSync('curr_family', currFamily);
							}
						})
					}
				})
			}
		}
	}
</script>

<style lang="scss">
page {
	background-color: #f7f7f7;
}
</style>
<style lang="scss" scoped>
.family-familys-page {
	padding-bottom: 100rpx;
	// height: 100vh;
	// // #ifdef H5
	// height: 100%;
	// // #endif
	.content {
		// margin-top: 20upx;
		padding: 22upx 26upx;
		box-sizing: border-box;
		margin-bottom: 30upx;
		padding-bottom: 20rpx;
		.family-item {
			background: #FFFFFF;
			// box-shadow: 0px 8upx 24upx 0px rgba(0, 0, 0, 0.1);
			border-radius: 16upx;
			// padding: 40upx;
			color: #000;
			display: flex;
			flex-direction: column;
			align-items: center;
			align-content: center;
			font-size: 30upx;
			.item-content {
				width: 100%;
				padding: 30upx;
				// padding-top: 25upx;
				// padding-bottom: 25upx;
				display: flex;
				flex-direction: row;
				align-items: center;
				align-content: center;
				.detail {
					width: 65%;
					flex: 1;
					font-size: 32rpx;
					font-weight: bold;
					.infos {
						font-weight: normal;
						font-size: 24rpx;
						margin-top: 16rpx;
						letter-spacing: 4rpx;
						color: #888;
					}
				}
				.btns {
					width: 35%;
					text-align: right;
					// width: 120upx;
					.setting-btn {
						padding: 8rpx 20rpx;
						border: 2rpx solid #000;
						font-size: 24rpx;
						border-radius: 30rpx;
					}
				}
			}
			
			&:nth-child(n + 2) {
				margin-top: 20upx;
			}
			&.actived {
				background: #01B09A;
				color: white !important;
				.detail {
					.infos {
						color: white;
					}
				}
				.btns {
					.setting-btn {
						border: 2rpx solid white;
					}
				}
			}
		}
	}
	
	.footer-btns {
		position: fixed;
		bottom: 40rpx;
		left: 40rpx;
		right: 40rpx;
		margin: 0 auto;
		.next-btn {
			display: block;
			margin-top: 20rpx;
		}
	}
	::v-deep .u-load-more-wrap {
		background: transparent !important;
		.u-more-text {
			background: transparent !important;
		}
	}
	::v-deep .u-load-more-wrap {
		margin-top: 20upx !important;
	}
}
</style>
