<template>
	<view class="family-exception-info-page">
		<view class="top-header-line"></view>
		<view class="item-block">
			<view class="u-flex">
				<view style="width: 140rpx;">
					发生时间:
				</view>
				<view class="u-flex-1">
					{{ exception.startTime || '' }}
				</view>
			</view>
			<view class="u-flex">
				<view style="width: 140rpx;">
					设备名称:
				</view>
				<view class="u-flex-1">
					{{ (exception.devName || exception.devCode) || '' }}
				</view>
			</view>
			<view class="u-flex">
				<view style="width: 140rpx;">
					地址:
				</view>
				<view class="u-flex-1">
					{{ exception.addr || '' }}
				</view>
			</view>
		</view>
		<view class="item-block">
			<view class="title">人员实时状态</view>
			<LineChart v-if="[ '1', '2' ].includes(exception.type) && lineDatas && lineDatas.length" line-color="#4D7BFF" :datas="lineDatas" title="心率" :hiddenAxisX="false" :markLines="[ 100, 60, 20 ]" :max="130"></LineChart>
			<LineChart v-if="[ '3', '4' ].includes(exception.type) && lineDatas && lineDatas.length"  line-color="#01B09A" :datas="lineDatas" title="呼吸" :hiddenAxisX="false" :markLines="[ 20, 12, 1 ]" :max="28"></LineChart>
		</view>
		<view class="item-block">
			<view class="u-flex">
				<view style="width: 140rpx;">
					结果分析: 
				</view>
				<view class="u-flex-1">
					{{ exception.resultAnalyse || '' }}
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import LineChart from '@/components/line-chart/index.vue';
	export default {
		components: {
			LineChart
		},
		data() {
			return {
				sleepValue: [],
				setStyle: {
					marginLeft: "5%",
					width: "500rpx"
				},
				exception: {},
				lineDatas: [],
			}
		},
		onLoad(option) {
			this.currId = option.id;
			this.fetchExceptionDetail(option.id);
		},
		methods: {
			fetchExceptionDetail(id) {
				this.$u.api.fetchHeartBreathExceptionDetail({ heartBreathExceptionId: id }).then(res => {
					this.exception = res || {};
					let _datas = [];
					(res.heartBreathDetailVOList || []).map(m => {
						if ([ '1', '2' ].includes(res.type)) {
							_datas.push([ m.createTime.substr(11), m.heartRate ]);
						} else if ([ '3', '4' ].includes(res.type)) {
							_datas.push([ m.createTime.substr(11), m.breathRate ]);
						}
					});
					console.log('_datas', _datas)
					this.lineDatas = _datas;
				})
			},
		}
	}
</script>

<style lang="scss">
page {
	background-color: #f7f7f7;
}
</style>
<style lang="scss" scoped>
.family-exception-info-page {
	
	.item-block {
		background: white;
		padding: 30rpx 20rpx;
		font-size: 28rpx;
		margin: 30rpx;
		border-radius: 20rpx;
	}
	
	.u-flex {
		align-items: flex-start;
		line-height: 50rpx;
	}
}
</style>
