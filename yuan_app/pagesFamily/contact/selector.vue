<template>
	<view class="device-connector-selector-page">
		<view class="top-header-line"></view>
		
		<RecordNotFound v-if="rows.length === 0" :pic="`${staticUrl}/images/device/pic-connector-notfound.png`" desc="暂无紧急联系人"></RecordNotFound>
		
		<view v-for="(row, index) in rows" :key="index" class="item-block">
			<u-row>
				<u-col span="8">
					<!-- #ifdef MP-WEIXIN -->
					<view class="left-title" @click="radioChange(row)">
						<text v-if="!!row.id" class="icon iconfont icon-xuanzhong radio" style="color: #01B09A"></text>
						<text v-else class="icon iconfont icon-weixuanzhong radio"></text>
						<text style="margin-left: 14rpx;">{{ row.contactName }}</text>
					</view>
					<!-- #endif -->
					<!-- #ifdef H5 -->
					<view class="left-title" @click="radioChange(row)">
						<text v-if="!!row.id" class="icon iconfont icon-xuanzhong radio" style="color: #01B09A"></text>
						<text v-else class="icon iconfont icon-weixuanzhong radio"></text>
						<text style="margin-left: 14rpx;">{{ row.contactName }}</text>
					</view>
					<!-- #endif -->
				</u-col>
				<u-col span="4">
					<view class="right-val">
						{{ row.contactPhone || '-' }}
					</view>
				</u-col>
			</u-row>
		</view>
		
		<view class="footer-btns">
			<u-button shape="circle" class="next-btn diy-btn" size="medium" 
				:custom-style="{ 'width': '100%', 'color': 'white', 'background-color': '#01B09A' }"  hover-class="none"
				@click="$navTo('pagesMy/device/connector/edit')">新增紧急联系人</u-button>
		</view>
	</view>
</template>

<script>
	import RecordNotFound from '../components/record-not-found/index'
	export default {
		components: {
			RecordNotFound
		},
		data() {
			return {
				radioValue: undefined,
				deleteDialog: false,
				deviceName: '',
				deviceNameDialog: false,
				rows: [],
				source: undefined,
				familyId: null,
			}
		},
		onLoad(option) {
			this.source = option.source;
			this.familyId = option.familyId;
		},
		onShow() {
			this.fetchSelectedDatas();
		},
		methods: {
			gotoUrl(url){
				if (!url) return;
				uni.navigateTo({
					url: url
				})
			},
			async radioChange(row) {
				const { familyId } = this;
				uni.showLoading({
					title: '操作中...',
					mask: true
				});
				let { id, contactId } = row;
				if ( id ) {
					const res = await this.$u.api.unbindFamilyContact({id, familyId});
					if (res !== false) {
						row.id = null;
						uni.showToast({ duration: 1000, title: '取消成功', icon: 'none' })
					}
				} else {
					const res = await this.$u.api.bindFamilyContact({familyId, contactId});
					if (res !== false) {
						row.id = res;
						uni.showToast({ duration: 1000, title: '选择成功', icon: 'none' })
					}
				}
				
				setTimeout(() => {
					uni.hideLoading();
				}, 1000);
			},
			fetchSelectedDatas() {
				uni.showLoading({
					title: '加载中...',
					mask: true
				})
				let api = null, params = null;
				//有家庭id则使用家庭id
				if (this.familyId) {
					api = this.$u.api.listMyContact;
					params = this.familyId;
				} else {
					api = this.$u.api.fetchContactMyList;
					params = {};
				}
				api(params).then(res => {
					this.rows = res;
				}).catch(err => {
					console.error(err);
				}).finally(() => {
					uni.hideLoading();
				});
			},
			handleSelectedItem(flag, rowId) {
				uni.showLoading({
					title: '操作中...',
					mask: true
				})
				let _api = flag ? this.$u.api.execDeviceBindHouseContact : this.$u.api.execDeviceunBindHouseContact;
				_api({ devId: uni.getStorageSync('devId'), contactId: rowId }).then(res => {
					uni.showToast({ duration: 1000, title: !flag ? '取消成功' : '选择成功', icon: 'none' })
				}).catch(err => {
					console.log('err', err)
					uni.showToast({ duration: 2000, title: err.message || '发生未知错误', icon: 'none' })
				}).finally(() => {
					setTimeout(() => {
						uni.hideLoading();
					}, 1000)
				})
			}
		}
	}
</script>

<style lang="scss">
page {
	background-color: #f7f7f7;
}
</style>
<style lang="scss" scoped>
@import url("/static/css/iconfont.css");
.device-connector-selector-page {
	// margin-top: 20rpx;
	background: transparent;
	.item-block {
		padding: 14rpx 30rpx;
		color: $u-content-color;
		font-size: 28rpx;
		border-bottom: 2rpx solid #ececec;
		background: white;
		//#ifdef H5
		line-height: 70rpx;
		//#endif
		.left-title {
			color: #000;
			font-size: 32rpx;
		}
		.right-val {
			color: #000;
			text-align: right;
			.activate-now {
				padding: 10rpx 28rpx;
				background: #EDEFF9;
				border-radius: 45rpx;
				color: #4166F5;
				font-size: 26rpx;
				line-height: initial;
				text-align: center;
				margin-top: 10rpx;
				display: inline-block;
			}
			::v-deep .u-icon {
				margin-left: 10rpx;
				position: relative;
				top: -2rpx;
			}
		}
		// &:nth-child(n + 2) {
		// 	margin-top: 20rpx;
		// }
	}
	u-row {
		line-height: 80rpx;
	}
	.footer-btns {
		position: absolute;
		bottom: 40rpx;
		left: 40rpx;
		right: 40rpx;
		margin: 0 auto;
		display: flex;
		justify-content: space-around;
		.next-btn {
			margin-top: 20rpx;
		}
	}
}
</style>
