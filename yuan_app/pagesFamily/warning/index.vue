<template>
	<view class="family-warning-page">
		<view class="top-header-line"></view>
		<u-empty v-if="!days.length && !devNameOrCode && firstIsNull && !date" text="暂无告警数据" mode="list"></u-empty>
		<scroll-view v-else scroll-y style="height: 100%; width: 100%;" @scrolltolower="reachBottom">
			<view class="r-flex" style="padding: 26rpx; padding-bottom: 0rpx;">
				<view class="r-flex-1">
					<u-input v-model="devNameOrCode" :border="true" placeholder="请输入设备名称或编号" placeholder-style="color: #bbbbbb" />
				</view>
				<view class="ta-r" style="width: 100rpx;">
					<u-icon name="calendar" :color="date ? '#01B09A' : '#bbb'" size="70" @click="dialog.calendar.show = true"></u-icon>
				</view>
			</view>
			<view class="content">
				<view v-for="(day, dayIndex) in days" :key="dayIndex">
					<view class="day-title">{{ todayYmd === day ? '今天' : day }}</view>
					<view class="warning-item" v-for="(warning, index) in dayMapping[day]" :key="index" @click="$navTo(`pagesFamily/timeline/index?id=${warning.id}`)">
						<view class="info">
							<view>
								{{ warning.createTime }}
								<text v-if="warning.tag" style="color: white; background: #01B09A; font-size: 20rpx; padding: 4rpx 10rpx; margin-left: 10rpx;">{{ warning.tag }}</text>
							</view>
							<view>{{ `设备：${warning.devName}(${warning.devCode})`}}</view>
							<view style="font-size: 23rpx;">{{ warning.houseName }}</view>
							<view class="red" style="text-align: right;">发生{{ warning.orderTypeName }}</view>
						</view>
						<!-- <view class="arrow">
							<u-icon name="arrow-right" class="icon-arrow-right"></u-icon>
						</view> -->
					</view>
				</view>
				<u-empty v-if="!days.length" text="暂无告警数据" mode="list" style="position: relative; top: 250rpx;"></u-empty>
				<u-loadmore v-if="days && warningCount >= 10" :status="loadStatus" class="loadmore" />
			</view>
		</scroll-view>
		<u-calendar v-model="dialog.calendar.show" mode="date" btn-type="success" active-bg-color="#01B09A" @change="handleDateChange"></u-calendar>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				dialog: {
					calendar: {
						show: false
					}
				},
				dict: {
					statusColor: {
						'1': '#ff3609',
						'2': '#25c525',
						'3': '#999',
					}
				},
				loadStatus: 'loadmore',
				page: 1,
				currFamilyId: undefined,
				days: [],
				dayMapping: {},
				devNameOrCode: '',
				date: '',
				todayYmd: this.$u.timeFormat(new Date(), 'yyyy-mm-dd'),
				firstIsNull: true,
			}
		},
		computed: {
			warningCount() {
				let _count = 0;
				if (this.days && this.days.length) {
					Object.values(this.dayMapping).map(m => {
						_count += m.length;
					})
				}
				return _count
			}
		},
		watch: {
			'devNameOrCode': function(val) {
				this.$u.debounce(this.handleSearch, 700)
			}
		},
		onLoad(option) {
			this.currFamilyId = option.id;
			this.fetchDatas(true);
		},
		methods: {
			handleDateChange(day) {
				this.date = day.result;
				this.handleSearch();
			},
			handleSearch() {
				this.page = 1;
				this.days = [];
				this.dayMapping = {};
				this.fetchDatas();
			},
			reachBottom() {
				if(this.page >= 1) {
					this.loadStatus = 'loading';
					setTimeout(() => {
						this.page = this.page + 1;
						this.fetchDatas();
					}, 1200);
				}
			},
			fetchDatas(first) {
				let _today = this.$u.timeFormat(new Date(), 'yyyy-mm-dd');
				let _days = [];
				let _dayMapping = {};
				this.$u.api.fetchHisEventList({ familyId: this.currFamilyId, current: this.page, devNameOrCode: this.devNameOrCode, date: this.date }).then(res => {
					res.records.map(m => {
						let _tempDay = this.$u.timeFormat(m.createTime, 'yyyy-mm-dd')
						if (!this.dayMapping[_tempDay]) {
							this.days.push(_tempDay);
							this.dayMapping[_tempDay] = [];
						}
						this.dayMapping[_tempDay].push(m);
					})
					if (first && res.records && res.records.length) {
						this.firstIsNull = false;
					}
					this.loadStatus = 'loadmore'
				})
			},
		}
	}
</script>

<style lang="scss">
page {
	background-color: #f7f7f7;
}
</style>
<style lang="scss" scoped>
.family-warning-page {
	height: 100vh;
	//#ifdef H5
	height: 100%;
	//#endif
	.content {
		// margin-top: 20upx;
		padding: 20upx 26upx;
		box-sizing: border-box;
		margin-bottom: 30upx;
		padding-top: 0rpx;
		.day-title {
			font-size: 24rpx;
			color: #666;
			margin-bottom: 30rpx;
			margin-top: 30upx;
		}
		.warning-item {
			background: #FFFFFF;
			box-shadow: 0px 8upx 24upx 0px rgba(0, 0, 0, 0.1);
			border-radius: 16upx;
			// padding: 40upx;
			color: #333;
			display: flex;
			flex-direction: row;
			align-items: center;
			align-content: center;
			font-size: 30upx;
			view {
				&:nth-child(n + 2) {
					margin-top: 16upx;
				}
			}
			.info {
				padding: 20upx;
				font-size: 28upx;
				color: #000;
				width: 100%;
				.red {
					color: #ff2a2a;
				}
			}
			.arrow {
				width: 80rpx;
				text-align: center;
				.icon-arrow-right {
					vertical-align: middle;
					color: #6B6B6B;
					position: relative;
					top: 2rpx;
				}
			}
			
			&:nth-child(n + 2) {
				margin-top: 20upx;
			}
		}
	}
	
}
::v-deep .u-input {
	border-radius: 50rpx !important;
	border-color: #bbbbbb !important;
}
::v-deep .u-load-more-wrap {
	background: transparent !important;
	.u-more-text {
		background: transparent !important;
	}
}
::v-deep .u-load-more-wrap {
	margin-top: 30upx !important;
}
::v-deep .u-calendar__bottom {
	margin-top: 40rpx;
}
::v-deep .u-calendar__bottom__choose {
	display: none;
}
::v-deep .u-btn--success {
	border-color: #01B09A !important;
	background-color: #01B09A !important;
}
::v-deep .u-success-hover {
	border-color: #00ada2 !important;
	background-color: #00ada2 !important;
}
</style>
