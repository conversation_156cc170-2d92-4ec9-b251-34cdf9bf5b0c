<template>
	<view class="page-wrap">
		<view class="top-header-line"></view>
		<view class="item">
			<view class="item-title">
				<u-icon name="warning" size="64" color="#01B09A" class="title-icon"></u-icon>
				<text>{{deviceMsg.title}}</text>
			</view>
			<view class="content-wrap">
				<view class="item-content">{{ deviceMsg.createTime }}</view>
				<view class="item-content">{{ deviceMsg.devName }}</view>
				<view class="item-content">{{ deviceMsg.content }}</view>
			</view>
		</view>

		<view class="item">
			<view class="item-title">
				<u-icon name="info-circle" size="64" color="#01B09A" class="title-icon"></u-icon>
				<text>解决办法</text>
			</view>
			<view v-if="deviceMsg.msgType !=='1'" class="content-wrap">
				<view class="item-content">1、请确保设备<text style="color:5fb6bd;">已连接电源</text></view>
				<view class="item-content">2、请确保设备所连接的<text style="color:5fb6bd;">网络正常</text></view>
				<view class="item-content">3、请确保路由器名称或密码<text style="color:5fb6bd;">未发生变化</text></view>
			</view>
			<view v-else class="content-wrap">
				<view class="item-content">1、可在与安宝【首页】观察设备的WiFi信号，请确保设备<text style="color:5fb6bd;">超过2格信号</text></view>
				<view class="item-content">2、请将无线路由器靠近与安宝设备</view>
				<view class="item-content">3、可选择安装WiFi信号放大器</view>
			</view>
		</view>

		<view v-if="showViewBtn" class="footer-btns">
			<u-button shape="circle" class="next-btn diy-btn" size="medium"
				:custom-style="{ 'width': '100%', 'color': '#01B09A', 'background-color': 'white' }" hover-class="none"
				@click="navDevStat">查看设备</u-button>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			unreadConst: "0",
			id: null,
			deviceMsg: {},
			device: {},

			showViewBtn: false,
		}
	},
	onLoad(option) {
		this.id = option.id;
		this.fetchData();
	},
	methods: {
		async fetchData() {
			const { id } = this;
			try {
				const res = await this.$u.api.getDetailDevMsg({ id });
				this.deviceMsg = res;
				this.fetchDeviceDetail();
				this.updateRead();
			} catch (err) {
				this.deviceMsg = {};
				console.error(err);
			}
		},
		async updateRead() {
			const { deviceMsg: { readStatus, id }, unreadConst } = this;
			if (readStatus !== unreadConst) {
				return;
			}
			await this.$u.api.updateDevMsgRead({ id });
		},
		async fetchDeviceDetail() {
			try {
				const { devId } = this.deviceMsg;
				const device = await this.$u.api.fetchMyDeviceInfo({ devId });
				this.device = device || {};
				this.showViewBtn = device.id == devId;
			} catch (err) {
				this.device = {};
				this.showViewBtn = false;
				console.error(err);
			}
		},
		navDevStat() {
			const navMode = "redirectTo";
			const { devScene, id } = this.device;
			if (devScene === '1' || devScene === '4') {
				// #ifdef H5
				this.$navTo(`pagesHome/parlour/index?id=${id}`);
				// #endif
				// #ifdef MP-WEIXIN
				this.$navTo(`pagesHome/parlour/index?id=${id}`, navMode);
				// #endif
			} else if (devScene === '2') {
				// #ifdef H5
				this.$navTo(`pagesHome/toilet/index?id=${id}`);
				// #endif
				// #ifdef MP-WEIXIN
				this.$navTo(`pagesHome/toilet/index?id=${id}`, navMode);
				// #endif
			} else if (devScene === '3') {
				// #ifdef H5
				this.$navTo(`pagesHome/bedroom/index?id=${id}`);
				// #endif
				// #ifdef MP-WEIXIN
				this.$navTo(`pagesHome/bedroom/index?id=${id}`, navMode);
				// #endif
			}
		},
	}
}
</script>

<style lang="scss" scoped>
.page-wrap {
	height: 100vh;
	//#ifdef H5
	height: 100%;
	//#endif
	padding-top: 1rpx;
	background-color: #f2f2f2;

	.item {
		margin: 10rpx;
		padding: 10rpx 0;
		background-color: #FFFFFF;

		.item-title {
			display: flex;
			align-items: center;
			padding: 20rpx;
			font-size: 32rpx;

			.title-icon {
				margin-right: 20rpx;
			}
		}

		.content-wrap {
			font-size: 28rpx;

			.item-content {
				margin: 10rpx 10rpx 10rpx 80rpx;
				font-size: 28rpx;
			}
		}
	}

	.footer-btns {
		position: fixed;
		z-index: 100;
		bottom: 40rpx;
		left: 40rpx;
		right: 40rpx;
		margin: 0 auto;

		.next-btn {
			display: block;
			margin-top: 20rpx;
		}
	}
}
</style>
