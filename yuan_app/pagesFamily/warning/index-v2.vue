<template>
	<view class="family-warning-page">
		<view class="top-header-line"></view>
		<view class="tab-btns">
			<view @click="switchWarnType('warn')" :class="[warnType === 'warn' ? 'active-btn' : 'rest-btn']">告警提醒</view>
			<view @click="switchWarnType('notice')" :class="[warnType === 'notice' ? 'active-btn' : 'rest-btn']">设备通知</view>
		</view>

		<event-order-list v-show="warnType === 'warn'" ref="eventOrder"></event-order-list>
		<device-msg-list v-show="warnType === 'notice'" ref="devMsgList"></device-msg-list>
	</view>
</template>

<script>
export default {
	data() {
		return {
			warnType: null,
		}
	},
	onLoad(option) {
		this.warnType = option.warnType || "warn";
	},
	onShow() {
		this.fetchData(this.warnType == "notice");
	},
	onReachBottom() {
		this.fetchData(false);
	},
	methods: {
		switchWarnType(warnType) {
			this.warnType = warnType;
			this.fetchData(this.warnType == "notice");
		},
		fetchData(reset) {
			setTimeout(() => {// 该定时器防止子组件未渲染导致获取不到
				if (this.warnType == "notice"&& this.$refs["devMsgList"]!=null) {
					this.$refs["devMsgList"].fetchNextPage(reset);
				} 
				if(this.warnType == "warn"&& this.$refs["eventOrder"]!=null){
					this.$refs["eventOrder"].fetchNextPage(reset);
				}
			}, 200)
		}
	}
}
</script>

<style lang="scss" scoped>
.family-warning-page {
	height: 100vh;
	//#ifdef H5
	height: 100%;

	//#endif
	.tab-btns {
		padding: 20rpx 80rpx;
		display: flex;
		text-align: center;
		background-color: #F2F2F2;

		.btn {
			flex: 1;

			font-size: 48rpx;
		}

		.rest-btn {
			@extend .btn;
			color: #000000;
		}

		.active-btn {
			@extend .btn;
			color: #01B09A;
		}
	}
}
</style>
