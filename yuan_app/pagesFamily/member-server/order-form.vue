<template>
	<view class="page-wrap">
		<view class="top-header-line"></view>

		<u-card v-if="source!='card'" title="购买人" class="content-wrap" :border="false" :border-radius="0"
			:head-style="{ 'font-weight': 600 }" margin="30rpx 0">
			<template slot="body">
				<u-field v-model="member.name" prop="name" label="姓名" placeholder="请输入" required :disabled="!isAdd" />
				<u-field v-model="member.phone" type="number" label="手机号" disabled placeholder="请输入" required />

				<u-field @click="showIdcardTypeAction = isAdd" v-model="dictFieldText.idcardType" disabled label="证件类型"
					placeholder="请选择" :right-icon="isAdd ? 'arrow-down-fill' : ''">
				</u-field>
				<u-action-sheet v-if="isAdd" @click="selectedIdcardType" :list="dict.idcardType" :tips="idcardTypeTips"
					v-model="showIdcardTypeAction"></u-action-sheet>

				<u-field v-model="member.idcardCode" label="证件号码" placeholder="请输入" maxlength="18" :disabled="!isAdd" />
			</template>
		</u-card>

		<u-card title="被监护人" class="content-wrap" :border="false" :border-radius="0"
			:head-style="{ 'font-weight': 600 }" margin="30rpx 0">
			<template slot="body">
				<view class="r-flex">
					<view style="flex:1">
						<u-field v-model="older.name" label="姓名" disabled required></u-field>
						<u-field v-model="older.idcardCode" label="证件号码" disabled required></u-field>
					</view>
					<view v-if="isAdd" class="select-btn" @click="toSelectOlder"> 选择 </view>
				</view>
			</template>
		</u-card>

		<u-card title="紧急联系人" class="content-wrap" :border="false" :border-radius="0"
			:head-style="{ 'font-weight': 600 }" margin="30rpx 0">
			<template slot="body">
				<view v-for="(contact, index) in contacts" :key="index">
					<view class="r-flex">
						<u-field style="flex:1 1" v-model="contact.contactName" prop="name" label="姓名" required
							disabled />
						<view class="select-btn" v-if="isAdd || index == 0" @click="toSelectContact(index)"> 选择 </view>
					</view>
					<u-field v-model="contact.contactPhone" type="number" label="手机号" required disabled />
					<u-field @click="showRelationTypeAction = true" v-model="contact.relationTypeDesc" label="关系"
						disabled />
					<u-field type="textarea" v-model="contact.addr" label="地址" disabled />
					<view v-if="index != contacts.length - 1"
						style="width:100%;height: 20rpx; background-color:#f4f6f8;"></view>
				</view>
			</template>
		</u-card>

		<view class="footer-btns">
			<u-button shape="circle" class="next-btn diy-btn" size="medium"
				:custom-style="{ 'width': '100%', 'color': 'white', 'background-color': '#01B09A' }" hover-class="none"
				@click="handleSave">
				<text v-if="source=='card'">激活</text>
				<text v-else>确认</text>
			</u-button>
		</view>

		<u-modal v-model="showConfirm" content="您确认信息填写真实且正确吗？"></u-modal>

		<!-- 激活成功弹出框 -->
		<u-popup v-model="showCardPopup" mode="center" width="720rpx" height="980rpx" :mask-close-able="false">
			<view class="c-flex popup-main"
				:style="{'background-image': `url(${staticUrl}/images/member_card/card_popup.png)`}">
				<view class="ta">
					<text class="dscr">您将获得{{year}}年的守护服务，</text>
					<text v-if="year!=1" class="dscr">可</text>
					<text v-if="year!=1" class="dscr qb">指定设备</text>
					<text v-if="year!=1" class="dscr">免费延长服务期限，</text>
					<text class="dscr">以免影响服务使用，请</text>
					<text class="dscr qb">确保</text>
					<text class="dscr">家庭已设置被监护人</text>
				</view>
				<!-- <view v-if="year!=1" class="vcr-flex button"
					@click="$navTo(`pagesFamily/member-card/delay-device?cardId=${cardId}`)">
					<text class="btn-font">指定延期设备</text>
				</view> -->
				<view v-if="year!=1" class="vcr-flex button"
					@click="$navTo(`pagesFamily/member-card/take-delivery-goods?cardId=${cardId}&source=tihuo`)">
					<text class="btn-font">设备提货</text>
				</view>
				<view v-if="year!=1" class="vcr-flex button_2" @click="$navTo(`pagesFamily/familys/index`)">
					<text class="btn-font text">前往家庭管理</text>
				</view>
				<view v-else class="vcr-flex button" @click="$navTo(`pagesFamily/familys/index`)">
					<text class="btn-font">前往家庭管理</text>
				</view>
				<view class="vcr-flex button_2" @click="$navTo(`pagesFamily/member-card/activate-list`)">
					<text class="btn-font text">查看激活记录</text>
				</view>
			</view>
			<view class="vcr-flex">
				<text class="icon iconfont icon-guanbi close-icon" @click="handlePopupClose"></text>
			</view>
		</u-popup>
	</view>
</template>

<script>
	import {
		dictTypesToCodeText
	} from "../../utils/sysDict";
	export default {
		components: {},
		data() {
			return {
				staticUrl: this.$u.http.config.staticBaseUrl,
				source: null,
				cardId: null,
				serverId: null,
				order: {},
				member: {
					name: undefined,
					phone: undefined,
					idcardType: undefined,
					idcardCode: undefined,
				},
				older: {
					id: undefined,
					name: undefined,
					idcardType: undefined,
					idcardCode: undefined,
				},
				contacts: [{}, {}],
				//care_house_contact.id
				initFirstContactId: null,
				//care_server_order_item_contact.id
				initFirstContactDataId: null,
				memberServer: null,

				dict: {
					idcardType: [],
				},
				dictConst: {
					relationType: [{
							code: 'kinsman',
							text: '亲属'
						},
						{
							code: 'neighbor',
							text: '邻居'
						}
					]
				},
				dictFieldText: {
					idcardType: undefined,
					relationType: [],
				},
				showIdcardTypeAction: false,
				showRelationTypeAction: false,
				idcardTypeTips: {
					text: '证件类型',
					color: '#909399',
					fontSize: 24
				},
				relationTypeTips: {
					text: '关系',
					color: '#909399',
					fontSize: 24
				},

				showConfirm: false,
				showCardPopup: false,
				isAdd: true,
				year: undefined,
			}
		},
		async onLoad(option) {
			const {
				serverId,
				billingMethod,
				memberServerId,
				cardId,
				source
			} = option;
			this.isAdd = !memberServerId;
			this.serverId = serverId;
			this.memberServerId = memberServerId;
			this.cardId = cardId;
			this.source = source;

			if (this.isAdd) {
				if (!serverId) {
					uni.showToast({
						duration: 2000,
						title: "服务ID无效，无法购买",
						icon: 'none'
					});
					return;
				}
				if (!billingMethod) {
					uni.showToast({
						duration: 2000,
						title: "参数无效，无法购买",
						icon: 'none'
					});
					return;
				}
				if (billingMethod == "Y") {
					this.year = 1;
				} else if (billingMethod == "TwoY") {
					this.year = 2;
				} else if (billingMethod == "ThreeY") {
					this.year = 3;
				}
			} else {
				if (!memberServerId) {
					uni.showToast({
						duration: 2000,
						title: "参数无效",
						icon: 'none'
					});
					setTimeout(uni.navigateBack, 2000);
					return;
				}
			}

			const dict = await this.$u.api.listDcitMapByTypes({
				typeCodes: Object.keys(this.dict)
			});
			this.dict = dictTypesToCodeText(dict);

			if (this.isAdd) {
				this.order = {
					serverId,
					billingMethod
				};
				this.$u.api.fetchMemberDetailInfo({}).then(res => {
					if (res.idcardType) {
						const dictItem = this.dict.idcardType.find(item => item.code == res.idcardType);
						this.dictFieldText.idcardType = dictItem.text;
					}
					this.member = res;
				})

				this.eventChannel = this.getOpenerEventChannel();

				return;
			}

			this.fetchMemberServerDetail();
		},
		methods: {
			selectedIdcardType(index) {
				if (index < 0) {
					return;
				}
				this.member.idcardType = this.dict.idcardType[index].code;
				this.dictFieldText.idcardType = this.dict.idcardType[index].text;
			},
			async toSelectOlder() {
				const that = this;
				const excludeIds = [];
				if (that.older?.olderId) {
					excludeIds.push(this.older.olderId);
				}

				const params = this.$u.queryParams({
					excludeIds,
					source: "order"
				}, true, "comma");
				const url = `/pagesMy/device/guardian/selector${params}`;
				console.log(url);
				uni.navigateTo({
					url,
					events: {
						selected: function({
							data: {
								id: olderId,
								...data
							}
						}) {
							that.older = {
								olderId,
								...data
							};
						},
					}
				});
			},
			async toSelectContact(index) {
				if(index==0 && this.memberServer && this.memberServer.serverStatus=='3'){
					uni.showToast({
						duration: 2000,
						title: "该会员还未备案，不能变更信息，请在【家庭管理】中设置该会员为家庭被监护人。",
						icon: 'none'
					});
					return;
				}
				const that = this;
				const {
					contacts,
					isAdd,
					initFirstContactDataId
				} = this;
				const excludeIds = [];
				if (contacts[index].contactId) {
					excludeIds.push(contacts[index].contactId);
				}
				if (!isAdd) {
					//编辑时两个原联系人都不允许选择
					excludeIds.push(contacts[1].contactId);
				}
				const params = this.$u.queryParams({
					excludeIds,
					source: "order"
				}, true, "comma");
				uni.navigateTo({
					url: `/pagesMy/device/connector/selector${params}`,
					events: {
						selected: function({
							data: {
								id,
								...contact
							}
						}) {
							if (!isAdd && index == 0) {
								contact.id = initFirstContactDataId;
							}
							contact.contactId = id;
							that.$set(that.contacts, index, contact)
							contacts.forEach((item, idx) => {
								//选择了同一联系人，位置发生变化
								if (item.contactId == contact.contactId) {
									if (idx != index) {
										that.$set(that.contacts, idx, {})
									}
								}
							})
						},
					}
				});
			},
			//未使用
			handleRelationTypeClick(index, contactIndex) {
				this.contacts[contactIndex].relationTypeDesc = this.dictConst.relationType[index].text;
				this.contacts[contactIndex].relationType = this.dictConst.relationType[index].code;
			},
			//未使用
			openMapSelectDialog(index) {
				let _that = this;
				uni.chooseLocation({
					success: (res) => {
						_that.contacts[index].addr = res.address.indexOf(res.name) != -1 ? res.address : res
							.address + res.name;
					}
				});
			},
			async fetchMemberServerDetail() {
				const {
					memberServerId,
					dictConst: {
						relationType
					}
				} = this;
				const orderDetail = await this.$u.api.getMemberServerDetail({
					memberServerId
				});

				const {
					older,
					contacts,
					member,
					memberServer
				} = orderDetail;

				//证件类型
				const dictIndex = this.dict.idcardType.findIndex(item => member.idcardType == item.code);
				this.selectedIdcardType(dictIndex);

				//关系
				contacts.forEach((item, index) => {
					if (index === 0) {
						this.initFirstContactDataId = item.id;
						this.initFirstContactId = item.contactId;
					}
					relationType.find(rel => {
						if (rel.code == item.relationType) {
							item.relationTypeDesc = rel.text
							return true;
						}
					});
				});

				this.member = member;
				this.older = older;
				this.contacts = contacts;
				this.memberServer = memberServer;
			},
			async handleSave() {
				const {
					isAdd,
					order,
					older,
					contacts
				} = this;
				if (!this.validMember()) {
					return;
				}
				if (!older.olderId) {
					uni.showModal({
						content: '请选择被监护人',
						showCancel: false,
						confirmText: '关闭'
					});
					return;
				}

				for (const item of contacts) {
					if (!item.contactId) {
						uni.showModal({
							content: '请选择紧急联系人',
							showCancel: false,
							confirmText: '关闭'
						});
						return;
					}
					if (!item.contactName || !item.contactPhone) {
						uni.showModal({
							content: '紧急联系人信息无效',
							showCancel: false,
							confirmText: '关闭'
						});
						return;
					}
				}

				const confirm = await new Promise((resolve, reject) => {
					uni.showModal({
						content: `您确认信息填写真实且正确吗？`,
						confirmColor: "#01B09A",
						success: function({
							confirm
						}) {
							resolve(confirm === true)
						}
					});
				})

				if (confirm !== true) {
					return;
				}

				uni.showLoading({
					title: '保存中...',
					mask: true
				});

				const {
					phone: memberPhone,
					idcardType: memberIdcardType,
					idcardCode: memberIdcardCode
				} = this.member;
				Object.assign(order, {
					memberPhone,
					memberIdcardType,
					memberIdcardCode
				});

				if (isAdd) {
					try {
						await this.$u.api.execUpdateMyInfo(this.member);
					} catch (err) {
						uni.hideLoading();
						this.eventChannel.emit('complete', {
							success: false
						});
						console.error(err);
						return;
					}
					try {
						let res;
						if (this.source == 'card') {
							res = await this.$u.api.createMemberServerByCard({
								order,
								olderId: older.olderId,
								cardId: this.cardId,
								contacts,
							});
							this.showCardPopup = true
							// this.eventChannel.emit('complete', {success: true, orderId: res});
							uni.hideLoading();
						} else {
							res = await this.$u.api.createMemberServerOrder({
								order,
								olderId: older.olderId,
								contacts,
							});
							this.eventChannel.emit('complete', {
								success: true,
								orderId: res
							});
							uni.hideLoading();
							uni.navigateBack();
						}
					} catch (err) {
						uni.hideLoading();
						this.eventChannel.emit('complete', {
							success: false
						});
						uni.showToast({
							duration: 2000,
							title: err.message || '激活失败',
							icon: 'none'
						})
						console.error(err);
						return;
					}

				} else {
					const {
						memberServer,
						older,
						contacts,
						initFirstContactId
					} = this;

					if (contacts[0].contactId == initFirstContactId) {
						uni.showToast({
							duration: 2000,
							title: "数据无变化",
							icon: 'none'
						});
						return;
					}
					try {
						await this.$u.api.editServerOrder({
							memberServer,
							older,
							contacts
						});
						uni.showToast({
							duration: 2000,
							title: "修改成功",
							icon: 'none'
						});
						uni.hideLoading();
						setTimeout(uni.navigateBack, 1000);
					} catch (error) {
						uni.hideLoading();
						console.error(error);
					}
				}
			},
			validMember() {
				if (!this.member.name) {
					uni.showModal({
						content: '姓名必填',
						showCancel: false,
						confirmText: '关闭'
					});
					return false;
				}
				if (!this.member.phone) {
					uni.showModal({
						content: '电话必填',
						showCancel: false,
						confirmText: '关闭'
					});
				}
				if (!this.$u.test.mobile(this.member.phone)) {
					uni.showModal({
						content: '电话号无效',
						showCancel: false,
						confirmText: '关闭'
					});
				}
				if (this.member.idcardCode) {
					if (!this.member.idcardType) {
						uni.showModal({
							content: '请选择证件类型',
							showCancel: false,
							confirmText: '关闭'
						});
						return false;
					}
					if (this.member.idcardType == "0" && !this.$u.test.idCard(this.member.idcardCode)) {
						uni.showModal({
							content: '身份证号无效',
							showCancel: false,
							confirmText: '关闭'
						});
						return false;
					} else {
						if (!this.$u.test.enOrNum(this.member.idcardCode)) {
							uni.showModal({
								content: '证件号无效',
								showCancel: false,
								confirmText: '关闭'
							});
							return false;
						}
					}
				}
				return true;
			},
			handlePopupClose() {
				this.showCardPopup = false;
				uni.navigateBack();
			},
	
	}
}
</script>

<style lang="scss" scoped>
	@import url("/static/css/iconfont.css");

	::v-deep .u-mode-center-box {
		background-color: transparent !important;
	}

	.page-wrap {
		background-color: #F7F7F7;

		::v-deep .u-field {
			input {
				text-align: right;
			}
		}

		.direction-field {
			::v-deep .u-field-inner {
				height: 120rpx;
				align-items: baseline;
			}
		}

		.footer-btns {
			padding: 40rpx 80rpx;

			.next-btn {
				display: block;
			}
		}

		// ::v-deep .uni-easyinput__content {
		// 	padding: 14rpx 6rpx;
		// }
		::v-deep .u-model__footer__button {
			height: 88rpx;
			line-height: 88rpx;
		}

		::v-deep .u-field {
			padding: 28rpx;
		}

		.content-wrap {
			background-color: #ffffff;

			// margin: 20rpx 0;
			::v-deep u-button {
				button {
					outline: none;
					background: transparent;
					position: relative;
					top: 14rpx;
					padding: 0rpx;
					right: 20rpx;
				}
			}
		}

		.select-btn {
			width: 100rpx;
			height: 90rpx;
			line-height: 90rpx;
			color: #01B09A;
			border-left: 1rpx solid #f4f6f8;
			text-align: center;
		}


		.popup-main {
			padding: 340rpx 76rpx 64rpx;
			background-size: 100% 100%;
			background-repeat: no-repeat;
		}

		.dscr {
			font-size: 32rpx;
			line-height: 44rpx;
			color: #666666;
		}

		.qb {
			color: #33BFAE;
		}

		.btn-font {
			font-size: 36rpx;
			line-height: 34rpx;
			font-weight: 700;
			color: #ffffff;
		}

		.button {
			margin: 32rpx 48rpx 0;
			padding: 32rpx 0;
			background-color: #33bfae;
			border-radius: 50rpx;
			box-shadow: 0px 24rpx 48rpx #01b09a14;
		}

		.button_2 {
			margin: 14rpx 48rpx 0;
			padding: 28rpx 0;
			border-radius: 50rpx;
			border: solid 2rpx #33bfae;

			.text {
				color: #33bfae;
				line-height: 33rpx;
			}
		}

		.close-icon {
			margin-top: 10rpx;
			font-size: 80rpx;
			color: #ffffff;
		}

	}
</style>
