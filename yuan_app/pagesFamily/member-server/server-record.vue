<template>
	<view class="page-wrap">
		<view class="top-header-line"></view>

		<view class="r-flex" style="justify-content:space-between;padding: 0 20rpx;">
			<view class="radio-btns">
				<view @click="switchFindMode(0)" :class="[findMode === 0 ? 'active-btn' : 'rest-btn']">按天</view>
				<view @click="switchFindMode(1)" :class="[findMode === 1 ? 'active-btn' : 'rest-btn']">按月</view>
			</view>
			<view>
				<u-icon name="calendar" color="5fb6bd" size="70" @click="showDateSelector">
				</u-icon>
			</view>
		</view>
		

		<view class="item-wrap">
			<view v-for="(data, index) in datas" :key="index" @click="toDetail(data.id, data.orderId)" class="item">
				<view class="item-title">
					{{data.toReimburseTime || ''}}
				</view>
				<view class="item-content">
					服务工单：{{ data.orderCode || '' }}
				</view>
				<view class="item-content">
					被监护人：{{ data.name || '' }}
				</view>
			</view>
		</view>

		<u-empty v-if="!datas.length" text="暂无服务记录" mode="list" style="position: relative; top: 250rpx;"></u-empty>
		<u-divider v-else-if="alreadyFetchAll">没有更多了</u-divider>
		<u-loadmore v-else :status="loadStatus" class="loadmore" />

		<!-- 日期弹出框 -->
		<u-calendar v-model="showCalendar" @change="selectedDate" mode="date"></u-calendar>
		<u-select v-model="showYMSelector" mode="mutil-column-auto" :list="yearMonthList" @confirm="selectedYearMonth">
		</u-select>
	</view>
</template>

<script>
export default {
	data() {
		return {
			olderId: null,
			dict: {
				statusColor: {
					'1': '#ff3609',
					'2': '#25c525',
					'3': '#999',
				}
			},
			loadStatus: 'loadmore',
			page: 0,
			datas: [],
			currFamilyId: undefined,
			devNameOrCode: '',
			firstIsNull: true,


			todayYmd: '',

			findMode: 0,
			yearMonthList: [],

			showCalendar: false,
			showYMSelector: false,

			findDate: null,
			findYearMonth: null,

			alreadyFetchAll: false,
		}
	},
	computed: {
	},
	onLoad(options) {
		this.olderId = options.olderId;
		this.initData();
		this.fetchDatas();
	},
	onReachBottom() {
		this.fetchDatas(false);
	},
	methods: {
		initData() {
			const now = new Date();
			this.todayYmd = this.$u.timeFormat(now, 'yyyy-mm-dd');

			const months = [];
			for (let i = 1; i <= 12; i++) {
				months.push({
					label: `${i}`,
					value: i < 10 ? `0${i}` : i,
				})
			}

			let yearMonthList = [];
			let year = now.getFullYear();
			for (let i = 0; i < 10; i++, year--) {
				yearMonthList.push({
					label: `${year}`,
					value: year,
					children: months
				});
			}
			this.yearMonthList = yearMonthList;
		},
		showDateSelector() {
			const { findMode } = this;
			this.showCalendar = findMode == 0;
			this.showYMSelector = findMode == 1;
		},
		switchFindMode(findMode) {
			if (this.findMode === findMode) {
				return;
			}
			this.findMode = findMode;
			this.fetchDatas()
		},
		selectedDate({ result }) {
			this.showCalendar = false;
			this.findDate = result;
			this.fetchDatas()
		},
		selectedYearMonth([year, month]) {
			this.showYMSelector = false;
			this.findYearMonth = `${year.value}-${month.value}`;
			this.fetchDatas()
		},
		async fetchDatas(reset = true) {
			if (reset) {
				this.page = 0;
				this.datas = [];
				this.alreadyFetchAll = false;
			}
			const { alreadyFetchAll, findMode, findDate, findYearMonth, datas, olderId = "" } = this;
			if (alreadyFetchAll) {
				return;
			}
			this.page = this.page + 1;
			
			let startDate = "", endDate = "";
			if (findMode === 0 && findDate) {
				startDate = new Date(findDate);
				endDate = new Date(findDate);
				endDate.setDate(startDate.getDate() + 1);
				startDate = this.$u.timeFormat(startDate, "yyyy-mm-dd");
				endDate = this.$u.timeFormat(endDate, "yyyy-mm-dd");
			} else if (findYearMonth) {
				startDate = new Date(findYearMonth + "-01");
				endDate = new Date(findYearMonth + "-01");
				endDate.setMonth(startDate.getMonth() + 1);
				startDate = this.$u.timeFormat(startDate, "yyyy-mm-dd");
				endDate = this.$u.timeFormat(endDate, "yyyy-mm-dd");
			}

			try {
				this.loadStatus = 'loading';
				const res = await this.$u.api.pageMyServerRecordWorryfree({ startDate, endDate, olderId, current: this.page });
				//10为分页大小
				this.alreadyFetchAll = res.length < 10;
				this.datas = datas.concat(res);
				this.loadStatus = 'loadmore';
			} catch (err) {
				console.error(err);
			}
		},
		toDetail(serverRecordId, eventOrderId) {
			console.log(serverRecordId)
			this.$navTo(`pagesFamily/member-server/record-event-timeline?serverRecordId=${serverRecordId}`);
		}
	}
}
</script>

<style lang="scss">
page {
	background-color: #f7f7f7;
}
</style>
<style lang="scss" scoped>
.page-wrap {
	.radio-btns {
		padding: 20rpx 0;
		display: flex;
		text-align: center;
		align-items: center;
		.btn {
			width: 180rpx;
			height: 60rpx;
			line-height: 60rpx;
		}
		.rest-btn {
			@extend .btn;
			color: #aaaaaa;
			border: 1rpx solid #aaaaaa;
			border-right: 0;
		}
		.rest-btn:nth-last-child(1) {
			border-right: 1rpx solid #aaaaaa;
		}
		.active-btn {
			@extend .btn;
			color: #01B09A;
			border: 1rpx solid #01B09A;
		}
		.active-btn + .rest-btn {
			border-left: 0;
		}
	}

	.item-wrap {
		padding: 20rpx 0;
		background-color: #F2F2F2;
		.item {
			margin-bottom: 20rpx;
			background-color: #FFFFFF;
			.item-title {
				height: 70rpx;
				line-height: 70rpx;
				padding: 0 10rpx;
				margin-bottom: 10rpx;
				border-bottom: 1rpx solid #d7d7d7;
				font-size: 26rpx;
			}
			.item-content {
				padding: 10rpx 20rpx 10rpx 20rpx;
				font-size: 24rpx;
			}
		}
		
	}

}
</style>
