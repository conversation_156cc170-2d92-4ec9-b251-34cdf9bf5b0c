<template>
	<view class="page-wrap">
		<view class="top-header-line"></view>
		<view class="item-wrap">
			<view class="item">
				<view class="item-title">
					<view>{{ serverRecord.name }}</view>
					<view>{{ serverRecord.orderCode }}</view>
				</view>
				<view class="item-content">
					{{ serverRecord.phone }}
				</view>
				<view class="item-content">
					{{ serverRecord.idcardCode }}
				</view>
			</view>
		</view>
		<view style="padding-left: 40rpx;padding-bottom:10rpx; background-color: #ffffff;">
			<u-time-line>
				<u-time-line-item>
					<template #node>
						<text class="icon iconfont icon-shebeigaojing" style="font-size: 52rpx; color: #D9001B;margin-left: 8rpx;"></text>
					</template>
					<template #content>
						<view>
							<view style="font-size:32rpx;font-weight:700;margin: 24rpx 0 12rpx 12rpx;">操作事件记录</view>
						</view>
					</template>
				</u-time-line-item>

				<u-time-line-item nodeTop="12">
					<template #content>
						<view>
							<view class="u-order-title">{{ serverRecord.alertTime }}</view>
							<view class="u-order-desc">
								<text class="blod">
									{{ serverRecord.name }} &nbsp;&nbsp; {{ serverRecord.phone }}
								</text>
								<template v-if="serverRecord.handlingDesc">
									- <text class="red">{{ serverRecord.handlingDesc }}，</text>
								</template>
								<template v-if="serverRecord.alertLocation">
									已前往<text class="red">{{ serverRecord.alertLocation }}</text>进行救授。
								</template>
								<template>
									本次服务<text class="red">已垫付{{ serverRecord.advanceAmount }}元，</text>
									您的保障额度为{{ serverRecord.guardAmount||0 }}元/年，<text class="red">剩余可垫付额度{{ serverRecord.remainAmount||0 }}元</text>
								</template>
							</view>
						</view>
					</template>
				</u-time-line-item>

				<u-time-line-item nodeTop="12" v-for="(item, index) in serverRecord.orderHandleHisVOList" :key="index">
					<template #content>
						<view>
							<view class="u-order-title">{{ item.createTime }}</view>
							<view class="u-order-desc">
								{{ item.logObjectName }}({{ item.relationTypeDesc }}) -
								<text class="red">{{ item.logResult }} </text>
							</view>
						</view>
					</template>
				</u-time-line-item>
			</u-time-line>
		</view>
	</view>
</template>

<script>
export default {
	components: {},
	data() {
		return {
			serverRecordId: null,
			serverRecord: {},
		}
	},
	async onLoad(option) {
		this.serverRecordId = option.serverRecordId;
		this.fetchDatas();
	},
	methods: {
		async fetchDatas() {
			try {
				this.serverRecord = await this.$u.api.detailServerRecordWorryfree({ id: this.serverRecordId });
			} catch (err) {
				console.error(err);
			}
		}
	}
}
</script>

<style lang="scss" scoped>
@import url("/static/css/iconfont.css");

.page-wrap {
	background-color: #F7F7F7;
	height: 100vh;

	.item-wrap {
		background-color: #F2F2F2;
		.item {
			margin-bottom: 20rpx;
			background-color: #FFFFFF;

			.item-title {
				height: 60rpx;
				line-height: 60rpx;
				padding: 0 20rpx;
				margin-bottom: 10rpx;
				// border-bottom: 1rpx solid #d7d7d7;
				color: #333333;
				font-size: 28rpx;
				display: flex;
				justify-content: space-between;
			}

			.item-content {
				padding: 0rpx 20rpx 20rpx 40rpx;
				font-size: 22rpx;
				color: #7f7f7f;
			}
		}

	}

	.u-node {
		width: 44rpx;
		height: 44rpx;
		border-radius: 100rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		background: #d0d0d0;
	}

	.u-order-title {
		color: #D9001B;
		font-weight: bold;
		font-size: 26rpx;
	}

	.u-order-desc {
		color: rgb(150, 150, 150);
		font-size: 28rpx;
		margin: 6rpx;
		// padding-right: 10rpx;
		.red {
			color: #D9001B;
		}

		.blod {
			color: #333333;
			font-weight: 700;
		}
	}

}
</style>
