<template>
	<view class="page-wrap">
		<view class="top-wrap">
			
			<view v-if="dataType === dataTypeEnum.history" class="title-r"
				@click="switchDataType">
				服务中 ( {{ currentTotal }} )
			</view>
			<view v-else class="title-r" @click="switchDataType">
				历史服务 ( {{ historyTotal }} )
			</view>

			<older-avatar-view :olders="olders" @clickOlder="switchOlder" hideTitle hideTips />
		</view>

		<view class="bottom-wrap">
			<view v-if="dataType === dataTypeEnum.current" class="title-l">
				服务中 ( {{ currentTotal }} )
			</view>
			<view v-else class="r-flex" style="align-items: baseline;">
				<view class="title-l" style="flex:1;">
					历史服务 ( {{ historyTotal }} )
				</view>
				<view @click="showYearSelect = true" class="title-r">
					{{year}} 年 
					<u-select v-model="showYearSelect" :list="years" @confirm="selectYear"></u-select>
				</view>
			</view>

			<view v-if="servers.length == 0" class="order-item">
				<view class="item-title">
					<view class="title">您还没有相关服务</view>
				</view>
			</view>
			<view v-for="(server, index) in servers" :key="index" class="order-item"
					@click="$navTo(`pagesFamily/member-server/order-detail?memberServerId=${server.id}`)">
				<view class="item-title">
					<view class="title">服务单号: {{server.orderNo}}</view>
				</view>
				<view class="item-content">
					<view class="name">{{server.serverName}}</view>
				</view>
				<view class="item-content">
					<view>被监护人: {{server.olderName}}</view>
				</view>
				<view v-if="server.serverStatus !='3'" class="item-content">
					<view>服务有效期: {{server.serverStartDate}} 至 {{server.serverEndDate}}</view>
				</view>
				<view v-if="server.serverStatus =='3'" class="item-content">
					<view>服务有效期: - <text style="color: #ff9900;">(为家庭设置被监护人即可生效)</text></view>
				</view>
				<view v-if="dataType === dataTypeEnum.current && server.serverStatus !='3'" class="r-flex jc-fe item-content">
					<!-- <text @click="$navTo(`pagesFamily/member-server/order-form?memberServerId=${server.id}`)">变更紧急联系人></text> -->
					<u-button shape="circle" size="mini"
						:custom-style="{ 'color': 'white', 'background-color': '#01b09a' }"
						@click="$navTo(`pagesFamily/member-server/order-form?memberServerId=${server.id}`)">去变更</u-button>
						
					<!-- <u-button shape="circle" class="diy-btn" size="medium"
						:custom-style="{ 'color': 'white', 'background-color': '#01b09a' }" hover-class="none"
						@click="$navTo(`pagesFamily/member-server/order-form?orderId=${server.orderId}&serverId=${serverId}`)">去变更</u-button> -->
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	components: { },
	data() {
		return {
			historyTotal: 0,
			currentTotal: 0,

			member: null,
			serverId: null,

			olders: [],
			servers: [],
			years: null,

			older: null,
			year: null,
			dataTypeEnum: {
				"current": "current",
				"history": "history",
			},
			dataType: 'current',
			showYearSelect: false,
		}
	},
	async onLoad(option) {
		if (!option.serverId) {
			uni.showToast({ duration: 2000, title: "服务ID无效", icon: 'none' });
			return;
		}
		this.serverId = option.serverId;

		try {
			this.member = await this.$u.api.fetchMemberDetailInfo({});
			if (!this.member) {
				return;
			}
		} catch (err) {
			console.error(err);
			return;
		}
		
		let year = (new Date()).getFullYear();
		this.year = year;
		let years = [];
		for (let i = 0; i < 10; i++, year--) {
			years.push({
				label: year,
				value: year,
			});
		}
		this.years = years;

		this.fetchTotal();
		this.listOlder();
		this.listMemberServer();
	},
	methods: {
		async listOlder() {
			const { serverId } = this;
			this.olders = await this.$u.api.listMyOrderItemOlderWorryfree({ serverId }) || [];
			this.older = { olderId: false, name: "全部" }
			this.olders.unshift(this.older);
		},
		async listMemberServer() {
			const params = this.createQueryParam();
			const { dataType, dataTypeEnum: { history } } = this;
			if (dataType === history) {
				this.servers = await this.$u.api.listMemberServerHistory(params) || [];
				this.historyTotal = this.servers.length;
			} else {
				//不需要年参数
				this.servers = await this.$u.api.listMemberServerCurrent(params) || [];
				this.currentTotal = this.servers.length;
			}
		},
		async fetchTotal() {
			const { serverId } = this;
			const params = { serverId };
			const { dataType, history } = this.dataTypeEnum;
			if (dataType == history) {
				this.currentTotal = await this.$u.api.countMemberServerCurrent(params) || 0;
			} else {
				this.historyTotal = await this.$u.api.countMemberServerHistory(params) || 0;
			}
		},
		switchDataType() {
			const { current, history } = this.dataTypeEnum;
			if (this.dataType == current) {
				this.dataType = history;
			} else {
				this.dataType = current;
			}
			this.listMemberServer();
			this.fetchTotal();
		},
		switchOlder(older) {
			this.older = older;
			this.listMemberServer();
			this.fetchTotal();
		},
		selectYear([{ value }]) {
			this.year = value;
			this.showYearSelect = false;
			this.listMemberServer();
		},
		toModify() {
			
		},
		createQueryParam() {
			const { serverId, older, year, dataType, dataTypeEnum: { history } } = this;
			const params = { serverId };
			if (older?.olderId) {
				params.olderId = older.olderId;
			}
			if (dataType === history) {
				params.year = year;
			}
			return params;
		},
		
	}
}
</script>

<style lang="scss" scoped>
$color-page: #FFFFFF;
$color-bg: #F2F2F2;
// $color-bg: #cccccc;
$color-btn: #01b09a;

.page-wrap {
	background-color: $color-bg;
	height: 100vh;

	.top-wrap {
		padding: 20rpx;
		background-color: $color-page;
	}

	.bottom-wrap {
		padding: 10rpx;
		background-color: $color-bg;
	}

	.title-l {
		font-size: 28rpx;
		font-weight: 400;
		color: $color-btn;
		padding: 40rpx 0 30rpx 0 ;
		// background-color: $color-bg;
	}

	.title-r {
		@extend .title-l;
		text-align: right;
		background-color: transparent;
	}

	.order-item {
		margin-bottom: 20rpx;
		background: $color-page;
		box-shadow: 0px 8rpx 24rpx 0px rgba(0, 0, 0, 0.1);
		border-radius: 16rpx;
		color: #7F7F7F;
		.item-title {
			padding: 20rpx;
			display: flex;
			flex-direction: row;
			font-size: 28rpx;
			border-bottom: 2rpx solid #ececec;
			width: 100%;
			align-items: center;
			.title {
				flex: 1;
				font-size: 30rpx;
			}
			.status {
				width: 140rpx;
				text-align: right;
				color: #ED2E1C;
				font-weight: bold;
				&.green {
					color: #47DF9B;
				}
				&.blue {
					color: #4166F5;
				}
			}
		}
		.item-content {
			width: 100%;
			padding: 10rpx 20rpx;
			font-size: 26rpx;
			display: flex;
			flex-direction: row;
			align-items: center;
			.name {
				font-size: 28rpx;
				color: #333333;
			}
		}

		// .item-btns {
		// 	text-align: right;
		// 	padding: 30rpx 0;
		// }

		// &:nth-child(n + 2) {
		// 	margin-top: 20rpx;
		// }
	}
	::v-deep uni-button {
		margin-left: 0;
		margin-right: 0;
	}
}
</style>