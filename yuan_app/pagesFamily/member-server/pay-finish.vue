<template>
	<view class="page-el">
		<view class="success">支付成功</view>

		<view class="company">熵行与安宝</view>

		<view class="amount">￥ {{order.payAmount}}</view>

		<view class="footer-btns">
			<u-button shape="circle" class="next-btn diy-btn" size="medium" plain
				:custom-style="{ 'width': '100%', 'color': '#01B09A' }" hover-class="none"
				@click="$navTo(`pagesMy/order/index`)">完成</u-button>
		</view>
	</view>
</template>

<script>
export default {
	components: {},
	data() {
		return {
			order: null,
		}
	},
	async onLoad(option) {
		const { orderId: id } = option;
		try {
			this.order = await this.$u.api.fetchOrderDetail({ id })
		} catch (err) {
			console.error(err);
		}
	},
	methods: {
	}
}
</script>

<style lang="scss" scoped>
.page-el {
	background-color: #F7F7F7;
	height: 100vh;

	.success {
		color: #01B09A;
		font-size: 48rpx;
		text-align: center;
		font-weight: 400;
		padding-top: 100rpx;
		padding-bottom: 200rpx;
	}
	
	.company {
		font-size: 36rpx;
		text-align: center;
		color: #7f7f7f;
		font-weight: 400;
		margin-bottom: 40rpx;
	}

	.amount {
		font-size: 80rpx;
		text-align: center;
		color: #7f7f7f;
		font-weight: 400;
	}

	.footer-btns {
		width: 750rpx;
		padding: 0px 60rpx;
		text-align: center;
		position: fixed;
		bottom: 60rpx;
		.next-btn {
			display: block;
		}
	}
}
</style>
