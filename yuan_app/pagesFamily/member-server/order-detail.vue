<template>
	<view class="page-wrap">
		<view style="height:4rpx"></view>

		<view class="order-item">
			<view class="item-title">
				<view class="title">{{ memberServer.serverName || "与安守护"}}</view>
			</view>
			<view class="item-content">
				<view>服务单号: {{ memberServer.orderNo || '-' }}</view>
			</view>
			<view class="item-content">
				<view>服务状态: {{ dictConst.serverStatus[memberServer.serverStatus]  || '-'}}</view>
			</view>
			<view v-if="memberServer.serverStatus!='3'" class="item-content">
				<view>服务有效期: {{ memberServer.serverStartDate  || '-'}} 至 {{ memberServer.serverEndDate  || '-'}}</view>
			</view>
			<view v-if="memberServer.serverStatus=='3'" class="item-content">
				<view>服务有效期: - <text style="color: #ff9900;">(为家庭设置被监护人即可生效)</text></view>
			</view>
			<view class="item-content">
				<view>服务交费时间: {{ order.payTime }}</view>
			</view>
			<view class="item-content">
				<view>保障额度: {{ memberServer.guardAmount || 3000 }}</view>
			</view>
			<view v-if="memberServer.serverStatus && memberServer.serverStatus>0" class="item-content">
				<view>剩余垫付额度: {{ memberServer.guardAmountRemain}}</view>
			</view>
			<view v-else class="item-content">
				<view>剩余垫付额度: 0 <text style="color: #ff9900;">(余额不足，及时购买)</text></view>
			</view>
		</view>

		<view class="order-item">
			<view class="item-title">
				<view class="title">购买人</view>
			</view>
			<view class="item-content">
				<view>姓名: {{ member.name || '-' }}</view>
			</view>
			<view class="item-content">
				<view>手机号码: {{ member.phone || '-' }}</view>
			</view>
			<view class="item-content">
				<view>证件类型: {{ dict.idcardType[member.idcardType] || '-'}}</view>
			</view>
			<view class="item-content">
				<view>证件号码: {{ member.idcardCode || '-' }}</view>
			</view>
		</view>
		<view class="order-item">
			<view class="item-title">
				<view class="title">
					被监护人
				</view>
				<text class="u-font-13 u-color-content">请确保家庭已设置被监护人信息</text>
			</view>
			<view class="item-content">
				<view>姓名: {{ older.name || '-'}}</view>
				<view v-if="serverRecordCount > 0"
					@click="toServerRecord"
					style="margin-left:auto;color:#01B09A">服务记录({{serverRecordCount}})</view>
			</view>
			<view class="item-content">
				<view>性别: {{ older.gender ? dictConst.gender[older.gender] : '-'}}</view>
			</view>
			<view class="item-content">
				<view>手机号码: {{ older.phone }}</view>
			</view>
			<view class="item-content">
				<view>证件类型: {{ dict.idcardType[older.idcardType] || '-' }}</view>
			</view>
			<view class="item-content">
				<view>证件号码: {{ older.idcardCode }}</view>
			</view>
			<view class="item-content">
				<view>所在区域: {{ older.province  || '-'}} / {{ older.city  || '-'}}</view>
			</view>
			<view class="item-content">
				<view>身高: {{ `${older.height ? older.height + 'CM' : '-'}`}}</view>
			</view>
			<view class="item-content">
				<view>体重: {{ `${older.weight ? older.weight + 'KG' : '-'}`}}</view>
			</view>
			<view class="item-content">
				<view>血型: {{ older.bloodtype ? dict.bloodType[older.bloodtype] : '-'}}</view>
			</view>
			<view class="item-content">
				<view>既往病例: {{ older.medicalHistory || '-'}}</view>
			</view>
			<view class="item-content">
				<view>过敏史: {{ older.allergies || '-'}}</view>
			</view>
			<view class="item-content">
				<view>出生日期: {{ older.birthday  || '-'}}</view>
			</view>
			<view class="item-content">
				<view>居住类型: {{ older.liveType ? dictConst.liveType[older.liveType] : '-'}}</view>
			</view>
		</view>

		<view v-for="(contact, index) in contacts" :key="index" class="order-item">
			<view class="item-title">
				<view class="title">
					紧急联系人{{index + 1}}
				</view>
				<text v-if="true" class="u-font-13 u-color-content">请确保家庭已设置紧急联系人信息</text>
			</view>
			<view class="item-content">
				<view>姓名: {{ contact.contactName || '-' }}</view>
			</view>
			<view class="item-content">
				<view>手机号码: {{ contact.contactPhone }}</view>
			</view>
			<view class="item-content">
				<view>关系: {{ dictConst.relationType[contact.relationType] || '-' }}</view>
			</view>
			<view class="item-content">
				<view>地址: {{ contact.addr || '-' }}</view>
			</view>
		</view>

		<view style="height:10rpx"></view>
	</view>
</template>

<script>
import { dictTypesToMap } from "../../utils/sysDict";
export default {
	components: {},
	data() {
		return {
			memberServerId: null,
			memberServer: null,
			order: {},
			older: {},
			contacts: [],
			member: {},

			dictConst: {
				gender: {'M': '男', 'W': '女'},
				serverStatus: { "1": "生效中", "2": "已过期", "3": "未生效" },
				relationType: { "kinsman": "亲属", "neighbor": "邻居" },
				liveType: { '1': '独居', '2': '非独居', '3': '集中居住', '4': '其他' },
			},
			dict: {
				idcardType: [],
				bloodType: []
			},

			serverRecordCount: 0,
		}
	},
	async onLoad(option) {
		this.memberServerId = option.memberServerId;

		const dict = await this.$u.api.listDcitMapByTypes({ typeCodes: Object.keys(this.dict) });
		this.dict = dictTypesToMap(dict);
		this.fetchMemberServerDetail();
	},
	methods: {
		async fetchMemberServerDetail() {
			const { memberServerId } = this;
			const orderDetail = await this.$u.api.getMemberServerDetail({ memberServerId });
			const { older, contacts, member, memberServer, order } = orderDetail;
			this.member = member;
			this.order = order;
			this.older = older;
			this.contacts = contacts;
			this.memberServer = memberServer;

			this.serverRecordCount = await this.$u.api.countOlderServerRecordWorryfree({ olderId: older.olderId }) || 0;
		},
		maskPhone(phone) {
			if (!phone) {
				return '';
			}
			return phone.substring(0,3) + "****" + phone.substring(7);
		},
		maskIdcardCode(idcardCode) {
			if (!idcardCode) {
				return '';
			}
			return idcardCode.substring(0,8) + "******" + idcardCode.substring(14);
		},
		toServerRecord() {
			this.$navTo(`pagesFamily/member-server/server-record?olderId=${this.older.olderId}`);
		}
	}
}
</script>
<style lang="scss">
page {
	-webkit-user-select: text; 
}	
</style>
<style lang="scss" scoped>
$color-page: #FFFFFF;
$color-bg: #F2F2F2;
// $color-bg: #cccccc;
$color-btn: #01B09A;

.page-wrap {
	background-color: $color-bg;
	height: 100vh;
	.order-item {
		background: $color-page;
		box-shadow: 0px 8rpx 24rpx 0px rgba(0, 0, 0, 0.1);
		border-radius: 16rpx;
		color: #7F7F7F;
		margin: 20rpx 10rpx;
		padding-bottom: 20rpx;

		.item-title {
			padding: 20rpx;
			display: flex;
			flex-direction: row;
			font-size: 28rpx;
			border-bottom: 2rpx solid #ececec;
			width: 100%;
			align-items: center;

			.title {
				// flex: 0;
				font-size: 30rpx;
				color: #333333;
				margin-right: 10rpx;
			}

			.status {
				width: 140rpx;
				text-align: right;
				color: #ED2E1C;
				font-weight: bold;

				&.green {
					color: #47DF9B;
				}

				&.blue {
					color: #4166F5;
				}
			}
		}

		.item-content {
			width: 100%;
			padding: 10rpx 20rpx;
			font-size: 26rpx;
			display: flex;
			flex-direction: row;
			align-items: center;

			.name {
				font-size: 28rpx;
				color: #333333;
			}
		}
	}
}
</style>