<template>
	<view class="timeline-page">
		<view class="top-header-line"></view>
		<view class="item-block">
			<view class="event-title">
				<view style="display: flex;">
					<view class="left-wrap" style="width: 100rpx;">
						<image src="../../static/images/my/alarm.png" />
						<!-- <view class="event-status">
							{{ event.eventOrderVO.orderTypeName || '' }}
						</view> -->
					</view>
					<view class="right-wrap" style="flex: 1;">
						<view class="addr" @longtap="longtapCopy(event.eventOrderVO.houseName)">{{ event.eventOrderVO.houseName || '' }}</view>
						<view class="r-flex jc-sb ta-c">
							<text class="event-status">	{{ event.eventOrderVO.orderTypeName || '' }}{{ event.eventOrderVO.statusName || '' }}</text>
							<text class="feedback" @click="$navTo(`pagesMy/feedback/submit?feedbackTypeId=5&problemTypeId=30&eventOrderId=${event.eventOrderVO.id}`)">误报反馈</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="item-block">
			<view class="infos-block" style="margin-top: 10px;">
				<view class="info-item">
					<view class="field-name-chamb">设备名称:</view>
					<view class="field-value" @longtap="longtapCopy(event.eventOrderVO.devName)">{{ (event.eventOrderVO.devName || event.eventOrderVO.devCode) || '' }}</view>
					<!-- <view v-if="event.eventOrderVO.orderType == '21'||event.eventOrderVO.orderType == '22'||event.eventOrderVO.orderType == '23'"
					 @click="$navTo('pages/webview/index?id=' + event.eventOrderVO.devId)">
						<text class="icon iconfont icon-Dxuanzhuan" style="margin-right: 6rpx; font-size: 36rpx; color: #01B09A;"></text>
						<text>实时查看</text>
					</view> -->
				</view>
				<view class="info-item">
					<view class="field-name-chamb">3D场景:</view>
					<view class="field-value" v-if="event.eventOrderVO.orderType == '21'||event.eventOrderVO.orderType == '22'||event.eventOrderVO.orderType == '23'" @click="$navTo('pages/webview/index?id=' + event.eventOrderVO.devId)">
						<text class="icon iconfont icon-Dxuanzhuan" style="margin-right: 6rpx; font-size: 36rpx; color: #01B09A;"></text>
						<text>实时</text>
					</view>
					<view class="field-value" v-if="event.eventOrderVO.orderType == '21'" @click="$navTo('pages/webview/index?id=' + event.eventOrderVO.devId+'&ts='+event.eventOrderVO.ts+'&orderType='+event.eventOrderVO.orderType)">
						<text class="icon iconfont icon-Dxuanzhuan" style="margin-right: 6rpx; font-size: 36rpx; color: #01B09A;"></text>
						<text>回看</text>
					</view>
				</view>
				<view class="info-item" v-if="event.chambList && event.chambList.length">
					<view class="field-name-chamb">管家:</view>
					<view class="field-value">{{ event.chambList[0].name }}</view>
				</view>
				<view class="info-item" v-if="event.chambList && event.chambList.length">
					<view class="field-name-chamb">管家电话:</view>
					<view class="field-value" @click="clickCall(event.chambList[0].phone)">{{ event.chambList[0].phone }} 
					<!-- <view v-if="event.chambList[0].subStatusDesc" class="b-color">({{ event.chambList[0].subStatusDesc }})</view> -->
					</view>
				</view>
				<!-- <view v-if="event.chambList.length > 1" class="info-item">
					<view class="field-name-chamb">改派管家:</view>
					<view class="field-value">{{ event.chambList[1].name }}</view>
				</view>
				<view v-if="event.chambList.length > 1" class="info-item">
					<view class="field-name-chamb">改派管家电话:</view>
					<view class="field-value">{{ event.chambList[1].phone }} <view v-if="event.chambList[1].subStatusDesc" class="b-color">({{ event.chambList[1].subStatusDesc }})</view></view>
				</view> -->
			</view>
			<view v-if="event.olderList && event.olderList.length" class="infos-block" style="margin-top: 10px; padding-bottom: 10px;">
				<view class="refs-title">关联档案</view>
				<view class="refs-avatar">
					<!-- <u-avatar v-for="(older, index) in event.olderList" :key="index" :class="{ actived: currOlder && currOlder.id === older.id }" 
						:src="'https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fup.enterdesk.com%2Fedpic%2Fc0%2F97%2F86%2Fc0978614c9ac5490d0f4faea9fac8979.jpg&refer=http%3A%2F%2Fup.enterdesk.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=jpeg?sec=1633864147&t=bfa835c1d1f75993f18bfd720f98b31d'" 
						@click="changeOlderItem(older)"></u-avatar> -->
					<u-avatar v-for="(older, index) in event.olderList" :key="index" :class="{ actived: currOlder && currOlder.id === older.id }" 
						@click="changeOlderItem(older)"
						mode="circle" 
						size="50" 
						:text="older.name || ''"
						:bgColor="currOlder && currOlder.id === older.id ? '#01B09A' : '#D7D7D7'"
						:show-level="older.guard"
						:level-bg-color="currOlder && currOlder.id === older.id ? '#01B09A' : '#D7D7D7'"
					></u-avatar>
				</view>
				<view class="info-item">
					<view class="field-name-chamb chamb-name">{{ currOlder.name }}</view>
				</view>
				<view class="info-item">
					<view class="field-name-chamb">{{ dict.sexMapping[currOlder.gender] }}  {{ currOlder.age ? currOlder.age + '岁' : '' }}  {{ currOlder.liveTypeDesc || '' }}</view>
				</view>
				<view class="info-item">
					<view class="field-name-chamb" style="width: 100%;" @click="clickCall(currOlder.phone)">电话：{{ currOlder.phone }} <view class="b-color">({{ currOlder.statusDesc }})</view></view>
				</view>
				<view v-if="event.contactList && event.contactList.length">
					<view class="info-item" style="margin-top: 30rpx;">
						<view class="field-name-chamb">紧急联系人</view>
					</view>
					<view v-for="(contact, index) in event.contactList" :key="index" class="info-item">
						<view class="field-name-chamb">{{ contact.contactName }} <text v-if="contact.relationTypeDesc">({{ contact.relationTypeDesc }})</text>:</view>
						<view class="field-value"  @click="clickCall(contact.contactPhone)">{{ contact.contactPhone }}</view>
					</view>
				</view>
			</view>
		</view>
		<!-- <view v-if="events && events.length" class="item-block"> -->
		<view class="item-block">
			<view style="font-size: 32rpx; color: #000000; margin-bottom: 26rpx; font-weight: bold;">操作事件记录</view>
			<view>
				<!-- <view v-for="(event, index) in event.orderHandleHisVOList" :key="index" style="margin-bottom: 20rpx;">
					<view class="u-order-time">{{ event.createTime }}</view>
					<view class="u-order-desc">{{ event.logObjectName }}({{ event.relationTypeDesc }})-<view class="status">{{ event.logResult }}</view></view>
				</view> -->
				<u-time-line>
					<u-time-line-item v-for="(event, index) in event.orderHandleHisVOList" :key="index" nodeTop="2">
						<!-- <template v-slot:node>
							<view class="u-node" :style="{ 'background': index === 0 ? '#4166F5' : '' }">
								<u-icon name="bookmark" color="#fff" :size="24"></u-icon>
							</view>
						</template> -->
						<template v-slot:content>
							<view>
								<view class="u-order-time">{{ event.createTime }}</view>
								<view class="u-order-desc">{{ event.logObjectName }}({{ event.relationTypeDesc }})-<view class="status">{{ event.logResult }}</view></view>
							</view>
						</template>
					</u-time-line-item>
				</u-time-line>
			</view>
		</view>
	</view>
</template>

<script>
	import {longtapCopy,clickCall} from '../../utils/util'
	export default {
		data() {
			return {
				dict: {
					sexMapping: {
						'M': '男',
						'W': '女'
					}
				},
				currOlder: {
					id: undefined
				},
				event: {
					eventOrderVO: {
						
					},
					chambList: [],
					contactList: [],
					olderList: [],
					orderHandleHisVOList: [],
				}
			}
		},
		onLoad(option) {
			this.fetchDatas(option.id)
		},
		methods: {
			longtapCopy,
			clickCall,
			changeOlderItem(item) {
				this.currOlder = item
			},
			async fetchDatas(orderId) {
				if (!orderId) return;
				try {
					let res = await this.$u.api.fetchEventInfo({ orderId: orderId });
					const len = res.olderList.length;
					if (res?.olderList && len) {
						this.currOlder = res.olderList[0];
						// TODO 这里需改成在服务端查询
						// const olders = res.olderList;
						// for (let i=0;i<len;i++) {
						// 	olders[i].isGuard = await this.$u.api.isGuardOlderWorryfree({ olderId: olders[i].id });
						// }
					}

					this.event = res;
				} catch (err) {
					console.log(err);
				}
			},
		}
	}
</script>

<style lang="scss">
	@import url("/static/css/iconfont.css");
page {
	background-color: #F7f7f7;
	-webkit-user-select: text; 
}	
</style>
<style lang="scss" scoped>
	.timeline-page {
		padding: 24rpx 24rpx 24rpx 24rpx;
		
		.item-block {
			padding: 24rpx 30rpx;
			color: $u-content-color;
			font-size: 28rpx;
			// box-shadow: 0rpx 8rpx 24rpx 0rpx rgba(0, 0, 0, 0.1);
			background-color: white;
			border-radius: 16rpx;
			.left-title {
				.device-name {
					color: #0D0D0D;
					font-size: 32rpx;
				}
			}
			.right-val {
				color: #b9b9b9;
				text-align: right;
				.activate-now {
					padding: 10rpx 28rpx;
					background: #EDEFF9;
					border-radius: 45rpx;
					color: #01B09A;
					font-size: 22rpx;
					line-height: initial;
					text-align: center;
					margin-top: 10rpx;
					display: inline-block;
				}
				::v-deep .u-icon {
					margin-left: 10rpx;
					position: relative;
					top: -2rpx;
				}
			}
			.event-title {
				// margin-bottom: 30rpx;
				// border-bottom: 2rpx solid #eee;
				padding: 10rpx 0rpx;
				position: relative;
				image {
					width: 80rpx;
					height: 80rpx;
					vertical-align: middle;
				}
				.addr {
					display: inline-block;
					font-size: 30rpx;
					vertical-align: middle;
					padding-bottom: 10rpx;
				}
				.event-status {
					// margin-left: 110rpx;
					font-size: 30rpx;
					color: #F52B13;
				}
				.feedback{
					font-size: 24rpx;
					color: #01B09A;
				}
			}
			.infos-block {
				margin-top: 20rpx;
				margin-right: 20rpx;
				padding-bottom: 30rpx;
				border-bottom: 2rpx dashed #eee;
				font-size: 30rpx;
				.refs-title {
					font-size: 32rpx;
					color: #000000;
					margin-bottom: 26rpx;
					font-weight: bold;
				}
				.refs-avatar {
					u-avatar {
						::v-deep image {
							box-sizing: border-box;
						}
						&:nth-child(n + 2) {
							margin-left: 20rpx;
						}
						&.actived {
							::v-deep image {
								// box-sizing: border-box;
								border: 6rpx solid #01B09A;
								// border-radius: 100%;
							}
							// ::v-deep .u-avatar {
							// 	border: 6rpx solid #01B09A;
							// }
						}
					}
				}
				.other-fields {
					color: #666;
					margin-top: 10rpx;
					font-size: 24rpx;
					background: #f5faff;
					padding: 6rpx 4rpx;
					.name {
						font-size: 22rpx;
						font-weight: bold;
						margin-right: 16rpx;
					}
				}
				.info-item {
					display: flex;
					flex-direction: row;
					&:nth-child(n + 2) {
						margin-top: 10rpx;
					}
					.field-icon {
						margin-right: 6rpx;
						img {
							position: relative;
							top: 2rpx;
						}
					}
					.field-name {
						display: inline-block;
						color: #666;
						margin-right: 10rpx;
						width: 120rpx;
					}
					.field-name-chamb {
						display: inline-block;
						color: #666;
						margin-right: 10rpx;
						width: 220rpx;
					}
					.chamb-name {
						font-size: 32rpx;
						color: #000000;
						margin-top: 10rpx;
						font-weight: bold;
					}
					.field-value {
						flex: 1;
						color: #333;
						.link-record {
							color: #1890ff;
							margin-left: 6rpx;
						}
					}
					.event-status {
						font-size: 30rpx;
						color: #F52B13;
					}
				}
			}
			&:nth-child(n + 2) {
				margin-top: 20rpx;
			}
		}
	}
	
	.u-node {
		width: 44rpx;
		height: 44rpx;
		border-radius: 100rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		background: #d0d0d0;
	}
	
	.u-order-title {
		color: #333333;
		font-weight: bold;
		font-size: 28rpx;
	}
	
	.u-order-title.unacive {
		color: rgb(150, 150, 150);
	}
	
	.u-order-desc {
		color: #1B1B1B;
		font-size: 26upx;
		margin-top: 6upx;
		// font-weight: bold;
		.status {
			color: #FF0000;
			display: inline-block;
		}
	}
	
	.u-order-time {
		color: rgba(0,0,0,0.65);
		font-size: 28upx;
	}
	
	.tel {
		color: $u-type-primary;
	}
	
	.b-color {
		display: inline-block;
		color: #01B09A;
	}
	
::v-deep .u-avatar {
	.u-line-1 {
		font-size: 32rpx !important;
		color: white;
	}
	&__level {
		bottom: -4rpx !important;
    right: -10rpx !important;
    width: 24rpx !important;
    height: 24rpx !important;
		background-color: #01B09A !important;
		color: #ffffff !important;
		text {
			font-size: 16rpx !important;
		}
	}
}
</style>
