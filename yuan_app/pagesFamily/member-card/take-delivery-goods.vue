<template>
	<view class="c-flex container">
		<view class="c-flex main">
			<view class="c-flex card logistics">
				<text class="title">配送信息</text>
				<view v-if="source =='tihuo'" class="r-flex detail">
					<view v-if="consignee" class="c-flex left-area">
						<view class="r-flex left-area-top">
							<text class="name">{{consignee.consignee}}</text>
							<text class="phone">{{consignee.phone}}</text>
						</view>
						<text class="address">{{consignee.address+consignee.detail}}</text>
					</view>
					<view v-else class="left-area">
						<text class="noAddress">请点击右侧地址簿，选择收货地址</text>
					</view>
					<view class="horiz-divider"></view>
					<view class="c-flex right-area" @click="$navTo('pagesMy/address/index?source=goods')">
						<text class="icon iconfont icon-dingwei dingwei"></text>
						<text class="text">地址簿</text>
					</view>
				</view>
				<view v-else class="r-flex detail">
					<view v-if="consignee" class="c-flex left-area">
						<view class="r-flex left-area-top">
							<text class="name">{{consignee.consignee}}</text>
							<text class="phone">{{consignee.phone}}</text>
						</view>
						<text class="address">{{consignee.address+consignee.detail}}</text>
						<view class="r-flex item">
							<text class="label">提货时间：</text>
							<text class="value">{{consignee.createTime || '-'}}</text>
						</view>
						<view v-if="consignee.deliveryTime">
							<view class="r-flex item">
								<text class="label">发货时间：</text>
								<text class="value">{{consignee.deliveryTime || '-'}}</text>
							</view>
							<view class="r-flex item">
								<text class="label">快递公司：</text>
								<text class="value">{{consignee.expressCompany || '-'}}</text>
							</view>
							<view class="r-flex item">
								<text class="label">快递单号：</text>
								<view class="r-flex">
									<text class="value">{{consignee.expressNumber || '-'}}</text>
									<u-line direction="col" length="26rpx" color="#ababab" margin="0rpx 16rpx" />
									<view class="c-flex">
										<u-tag class="tag" text="复制" @click="copy(consignee.expressNumber)" mode="plain"
											size="mini" color="#fff" bg-color="#ababab" />
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
				<view v-if="source =='tihuo'" class="c-flex status">
					<text class="text">未提货</text>
				</view>
			</view>
			<view class="c-flex card member-card">
				<text class="title">会员信息</text>
				<view class="c-flex">
					<view class="r-flex item">
						<text class="label">会员卡：</text>
						<text class="value">{{cardInfo.cardName}}</text>
					</view>
					<view class="r-flex item">
						<text class="label">会员卡号：</text>
						<view class="r-flex">
							<text class="value">{{cardInfo.cardNumber}}</text>
							<u-line direction="col" length="26rpx" color="#ababab" margin="0rpx 16rpx" />
							<view class="c-flex">
								<u-tag class="tag" text="复制" @click="copy(cardInfo.cardNumber)" mode="plain" size="mini"
									color="#fff" bg-color="#ababab" />
							</view>
						</view>
					</view>
					<view class="r-flex item">
						<text class="label">服务单号：</text>
						<view class="r-flex">
							<text class="value">{{cardInfo.orderNo || '-'}}</text>
							<u-line direction="col" length="26rpx" color="#ababab" margin="0rpx 16rpx" />
							<view class="c-flex">
								<u-tag class="tag" text="复制" @click="copy(cardInfo.orderNo)" mode="plain" size="mini"
									color="#fff" bg-color="#ababab" />
							</view>
						</view>
					</view>
					<view class="r-flex item">
						<text class="label">激活日期：</text>
						<text class="value">{{cardInfo.activeTime || '-'}}</text>
					</view>
					<view class="r-flex item">
						<text class="label">姓名：</text>
						<text class="value">{{cardInfo.name || '-'}}</text>
					</view>
					<view class="r-flex item">
						<text class="label">手机号：</text>
						<text class="value">{{cardInfo.phone || '-'}}</text>
					</view>
					<view class="r-flex item">
						<text class="label">证件类型：</text>
						<text class="value">{{cardInfo.idcardTypeDesc || '-'}}</text>
					</view>
					<view class="r-flex item">
						<text class="label">证件号码：</text>
						<text class="value">{{cardInfo.idcardCode || '-'}}</text>
					</view>
				</view>
			</view>
			<view class="c-flex card">
				<text class="title">赠送明细</text>
				<view class="table">
					<u-table>
						<u-tr class="u-tr">
							<u-th class="u-th" width="26%">名称</u-th>
							<u-th class="u-th" width="40%">型号</u-th>
							<u-th class="u-th" width="12%">数量</u-th>
							<u-th class="u-th" width="22%">服务年限</u-th>
						</u-tr>
						<!-- 当前不支持多种类型设备 -->
						<!-- <view v-for="(item, index) in list" :key="index"> -->
						<u-tr class="u-tr">
							<u-td class="u-td" width="26%">{{cardInfo.giftName||'-'}}</u-td>
							<u-td class="u-td" width="40%">{{cardInfo.modelName||'-'}}</u-td>
							<u-td class="u-td" width="12%">{{cardInfo.devNum||'-'}}</u-td>
							<u-td class="u-td" width="22%">{{cardInfo.serverTerm&&cardInfo.serverTerm/12||'-'}}年</u-td>
						</u-tr>
						<!-- </view> -->
					</u-table>
				</view>
			</view>
		</view>
		<view class="footer-btns" v-if="source=='tihuo'">
			<u-button shape="circle" class="next-btn diy-btn" size="medium"
				:custom-style="{ 'width': '100%', 'color': 'white', 'background-color': '#01B09A' }" hover-class="none"
				@click="submit">确认提货</u-button>
		</view>
	</view>
</template>

<script>
	import {
		copy
	} from '../../utils/util'
	export default {
		data() {
			return {
				consignee: null,
				cardId: undefined,
				cardInfo: {
					cardExpress: undefined,
				},
				list: [{}],
				source: "",
			}
		},
		onLoad(option) {
			const {
				cardId,
				source
			} = option;
			this.cardId = cardId;
			this.source = source;
		},
		onShow() {
			this.init();
		},
		methods: {
			copy,
			async init() {
				this.cardInfo = await this.$u.api.getCardById({
					id: this.cardId,
				})
				if (this.source == "tihuo") {
					this.consignee = uni.getStorageSync('consignee');
					if (!this.consignee) {
						let res = await this.$u.api.getDefaultAddress({})
						uni.setStorageSync('consignee', res);
						this.consignee = res;
					}
				} else {
					this.consignee = this.cardInfo.cardExpress || null;
				}
			},
			submit() {
				uni.showLoading({
					title: '请等待',
					mask: true
				})
				if (!this.consignee) {
					uni.showModal({
						content: '请检查电话是否填写正确',
						showCancel: false,
						confirmText: '关闭'
					})
					uni.hideLoading();
					return;
				}
				this.$u.api.takeDeliveryGoods({
					cardId: this.cardId,
					consignee: this.consignee.consignee,
					phone: this.consignee.phone,
					address: this.consignee.address+consignee.detail
				}).then(res => {
					uni.showToast({
						duration: 2000,
						title: '操作成功',
						icon: 'none'
					})
					this.$navTo(
						`pagesFamily/member-card/take-delivery-goods-list`
					)
				}).catch(err => {
					uni.showToast({
						duration: 2000,
						title: err.message || '操作失败',
						icon: 'none'
					})
				})
				uni.hideLoading();

			},
		}
	}
</script>

<style lang="scss" scoped>
	@import url("/static/css/iconfont.css");

	.container {
		background-color: #f2f2f2;
		width: 100%;
		height: 100%;
		flex: 1 1 auto;
		overflow-y: auto;
		overflow-x: hidden;

		.main {
			padding: 32rpx 32rpx 100rpx 32rpx;
			;

			.card {
				margin-bottom: 32rpx;
				padding: 32rpx;
				background-color: #ffffff;
				border-radius: 16rpx;

				.title {
					font-size: 32rpx;
					align-self: flex-start;
					line-height: 30rpx;
					font-weight: 700;
					color: #333333;
				}
			}

			.logistics {
				position: relative;

				.detail {
					margin-top: 24rpx;

					.noAddress {
						font-size: 26rpx;
						line-height: 19rpx;
						color: #999999;
					}

					.left-area {
						// margin: 4rpx 0;
						// padding: 0rpx 32rpx;
						flex: 1 1 auto;

						.left-area-top {
							align-items: center;

							.name {
								font-size: 26rpx;
								line-height: 24rpx;
								font-weight: 700;
								color: #333333;
							}

							.phone {
								margin-left: 32rpx;
								line-height: 19rpx;
								font-size: 26rpx;
								line-height: 24rpx;
								font-weight: 700;
								color: #333333;
							}
						}

						.address {
							margin-top: 20rpx;
							line-height: 32rpx;
							font-size: 26rpx;
							color: #999999;
						}

					}

					.right-area {
						margin-top: 8rpx;
						width: 126rpx;
						flex-shrink: 0;
						align-self: flex-start;
						align-items: center;

						.dingwei {
							font-size: 72rpx;
							color: #01b09a;
						}

						.text {
							margin-top: 12rpx;
							font-size: 26rpx;
							line-height: 24rpx;
							font-weight: 700;
							color: #01b09a;
						}
					}

					.horiz-divider {
						margin-left: 16rpx;
						background-color: #eeeeee;
						width: 2rpx;
						height: 160rpx;
						flex-shrink: 0;
					}
				}

				.status {
					position: absolute;
					right: 0;
					top: 0;
					padding: 12rpx 0;
					background-color: #fa6400;
					border-radius: 0px 16rpx 0px 16rpx;
					width: 160rpx;
					align-items: center;
					justify-content: flex-start;

					.text {
						color: #ffffff;
						font-size: 30rpx;
						font-family: PingFang;
						font-weight: 700;
						line-height: 28rpx;
					}
				}
			}


			.item {
				align-items: center;
				margin-top: 24rpx;
				justify-content: space-between;

				.label {
					font-size: 26rpx;
					line-height: 40rpx;
					font-weight: 700;
					color: #333333;
				}

				.value {
					align-items: center;
					font-size: 26rpx;
					line-height: 40rpx;
					color: #999999;
				}
			}


			.table {
				margin-top: 24rpx;
			}
		}
	}

	.footer-btns {
		position: fixed;
		bottom: 40rpx;
		left: 40rpx;
		right: 40rpx;
		margin: 0 auto;

		.next-btn {
			display: block;
			margin-top: 20rpx;
		}
	}
</style>
