<template>
	<view class="main c-flex">
		<image class="bg_img" :src="`${staticUrl}/images/member_server/delay_device_bg.png`"></image>
		<view class="c-flex top_area">
			<view class="r-flex notice_area space-x-8">
				<text class="icon iconfont icon-xitongxiaoxi1 notice_icon"></text>
				<text class="notice_desc">
					您已为{{ userName }}激活会员卡，您可以指定{{ devNum }}台设备，延长设备产品服务期限。
				</text>
			</view>
			<view v-if="showForm" class="c-flex form">
				<view class="c-flex">
					<text class="title">指定延期设备</text>
					<text v-if="list.length<devNum" class="descr">请扫码设备背后的二维码或输入设备编号</text>
					<text v-else class="descr">设备已全部延期,感谢您对与安宝的支持</text>
				</view>
				<view v-for="(item, index) in delayDevList" :key="index">
					<view class="r-flex device_area">
						<view class="r-flex device_code items-baseline flex-auto space-x-6">
							<text class="label">{{`设备${index+1}:`}}</text>
							<u-input v-model="item.devCode" placeholder="请扫描或输入设备编号" input-align="left" />
						</view>
						<!-- #ifdef MP-WEIXIN -->
						<text class="icon iconfont icon-tiaomasaoma scan_icon" @click="clickScan(index)"></text>
						<!-- #endif -->
					</view>
				</view>
				<view v-if="list.length<devNum" class="c-flex button" @click="btn_click">
					<text class="text">确定延期</text>
				</view>
			</view>
		</view>

		<view class="c-flex bottom_area">
			<view class="c-flex selected_area">
				<view class="count_area">
					<text class="title">已指定延期设备(</text>
					<text class="num">{{list.length}}台</text>
					<text class="title">)</text>
				</view>
				<view class="c-flex list_area">
					<view v-for="(card, index) in list" :key="index">
						<view class="c-flex item">
							<view class="r-flex date">
								<text class="text">{{ card.createTime }}</text>
							</view>
							<view class="r-flex detail">
								<image class="left_img" :src="staticUrl + '/images/device/yuanbao.png'" />
								<view class="c-flex right_area">
									<text class="title">设备编号</text>
									<view class="r-flex items-center space-x-8">
										<text class="device_name">{{ card.devCode }}</text>
										<u-tag class="tag" text="复制" @click="copy(card.devCode)" mode="plain"
											size="mini" color="#fff" bg-color="#ababab" />
									</view>
								</view>
							</view>
						</view>
					</view>
					<view v-if="!list.length" class="c-flex nodata">
						<text class="text">请指定延期设备，感谢您对与安宝的支持！</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		copy
	} from '../../utils/util'
	export default {
		data() {
			return {
				staticUrl: this.$u.http.config.staticBaseUrl,
				list: [{}],
				showForm: true,
				delayDevList: [{}],
				userName: "",
				devNum: 0,
				cardId: undefined,
			}
		},
		async onLoad(option) {
			const {
				cardId
			} = option;
			this.cardId = cardId;
			await this.$u.api.getCardById({
				id: cardId,
			}).then(res => {
				this.userName = res.name;
				this.devNum = res.devNum;
			}).catch(err => {
				uni.showToast({
					duration: 2000,
					title: err.message || '获取会员卡信息失败',
					icon: 'none'
				})
			})
			await this.$u.api.getCardDevicesByCardId({
				cardId: cardId,
			}).then(res => {
				this.list = res;
			}).catch(err => {
				uni.showToast({
					duration: 2000,
					title: err.message || '获取会员卡信息失败',
					icon: 'none'
				})
			})
			let arr = [];
			for (let i = 0; i < this.devNum - this.list.length; i++) {
				arr.push({
					devCode: '',
				});
			}
			this.delayDevList = arr;
		},
		methods: {
			getCardDevices(cardId) {
				this.$u.api.getCardDevicesByCardId({
					cardId: cardId,
				}).then(res => {
					this.list = res;
					let arr = [];
					for (let i = 0; i < this.devNum - this.list.length; i++) {
						arr.push({
							devCode: '',
						});
					}
					this.delayDevList = arr;
				}).catch(err => {
					uni.showToast({
						duration: 2000,
						title: err.message || '获取会员卡信息失败',
						icon: 'none'
					})
				})
			},
			copy,
			clickScan(idx) {
				console.log("开始扫描");
				uni.scanCode({
					success: (res) => {
						console.log('条码类型：' + res.scanType);
						console.log('条码内容：' + res.result);
						if (!res.result) {
							uni.showLoading({
								title: '解析二维码错误...',
								mask: true
							})
						} else {
							const code = res.result;
							this.delayDevList[idx].devCode = code;
						}
					},
					fail: (err) => {
						console.log(err);
						uni.showToast({
							duration: 1000,
							title: '二维码无法识别',
							icon: 'none'
						})
					}
				});
			},
			btn_click() {
				uni.showLoading({
					title: '请等待',
					mask: true
				})

				var list = [];
				for (let i = 0; i < this.delayDevList.length; i++) {
					if (this.delayDevList[i].devCode != "") {
						list.push(this.delayDevList[i].devCode)
					}
				}
				if (list.length <= 0) {
					uni.showToast({
						duration: 2000,
						title: '请扫码或输入设备编号',
						icon: 'none'
					})
					return;
				}
				// 设备延期
				this.$u.api.insertCardDevices({
					devCodes: list,
					cardId: this.cardId,
				}).then(res => {
					this.getCardDevices(this.cardId);
					uni.showToast({
						duration: 2000,
						title: '设备延期成功',
						icon: 'none'
					})
				}).catch(err => {
					uni.showToast({
						duration: 2000,
						title: err.message || '设备延期失败',
						icon: 'none'
					})
				})
				uni.hideLoading();
			},
		}
	}
</script>
<style lang="scss" scoped>
	@import url("/static/css/iconfont.css");
	.main {
		background-color: #f2f2f2;
		width: 100%;
		height: 100%;
		overflow-y: auto;
		overflow-x: hidden;

		.bg_img {
			width: 750rpx;
			top: 0;
			right: 0;
			bottom: 0;
			left: 0;
			z-index: 1;
			position: absolute;
		}

		.top_area {
			z-index: 10;
			padding: 32rpx 32rpx 0;
			background-size: 100% 100%;

			.notice_area {
				margin-left: 16rpx;
				padding: 28rpx 24rpx;
				background-color: #eefffd;
				border-radius: 16rpx;

				.notice_icon {
					flex-shrink: 0;
					display: inline-block;
					align-self: flex-start;
					font-size: 34rpx;
					color: #01B09A;
				}

				.notice_desc {
					margin-left: 20rpx;
					margin-right: 24rpx;
					color: #01b09a;
					font-size: 32rpx;
					line-height: 38rpx;
					align-self: center;
				}
			}

			.form {
				padding: 28rpx 24rpx;

				.title {
					margin-top: 16rpx;
					color: #333333;
					font-size: 40rpx;
					font-weight: 700;
					line-height: 38rpx;
				}

				.descr {
					font-size: 26rpx;
					line-height: 24rpx;
					margin-top: 24rpx;
					color: #999999;
				}

				.device_area {
					align-items: center;
					margin-top: 20rpx;
					padding: 28rpx 32rpx;
					background-color: #ffffff;
					border-radius: 16rpx;

					.device_code {
						flex: 1 1 auto;
						align-items: baseline;
					}

					.label {
						font-size: 36rpx;
						margin-right: 10rpx;
						line-height: 34rpx;
						font-weight: 700;
						color: #666666;
					}

					.scan_icon {
						margin-left: 48rpx;
						font-size: 72rpx;
						color: #01B09A;
					}
				}
			}

			.button {
				margin-top: 32rpx;
				padding: 32rpx 0;
				border-radius: 48rpx;
				align-items: center;
				justify-content: flex-start;
				filter: drop-shadow(0px 16rpx 16rpx #01b09a14);
				background-image: linear-gradient(#99DFD6, #01B09A);
				background-size: 100% 100%;
				background-repeat: no-repeat;

				.text {
					font-size: 36rpx;
					line-height: 34rpx;
					font-weight: 700;
					color: #ffffff;
				}
			}
		}

		.bottom_area {
			z-index: 10;
			padding: 0 48rpx;

			.selected_area {
				margin-top: 32rpx;

				.count_area {

					word-break: break-all;
					font-size: 32rpx;
					line-height: 30rpx;
					font-weight: 700;

					.title {
						color: #393947;
					}

					.num {
						color: #01b09a;
					}
				}

				.list_area {

					.nodata {
						margin-top: 20rpx;
						align-items: center;

						.text {
							height: 22px;
							font-size: 14px;
							font-weight: 400;
							line-height: 24px;
							color: #666666;
							opacity: 1;
						}
					}

					.item {
						margin-top: 24rpx;
						padding: 0 32rpx;
						background-color: #ffffff;
						border-radius: 16rpx;
						box-shadow: 0px 24rpx 48rpx #0000000a;

						.date {
							padding: 24rpx 0;
							border-bottom: solid 2rpx #eeeeee;
							align-items: center;

							.text {
								line-height: 29rpx;
								font-size: 32rpx;
								font-weight: 700;
								color: #333333;
							}
						}

						.detail {
							padding: 16rpx 0 24rpx;
							align-items: center;

							.left_img {
								width: 96rpx;
								height: 96rpx;
							}

							.right_area {
								margin-left: 32rpx;
								flex: 1 1 auto;

								.title {
									font-size: 32rpx;
									line-height: 30rpx;
									font-weight: 700;
									color: #333333;
								}

								.device_name {
									color: #666666;
									line-height: 26rpx;
									font-size: 26rpx;
									margin-right: 16rpx;
								}

								.tag {
									margin-left: 16rpx;
								}
							}
						}
					}
				}

			}
		}
	}
</style>
