<template>
	<view class="c-flex container">
		<view class="c-flex section"
			:style="{'background-image': `url(${staticUrl}/images/member_card/card_info/1_bg.png)`}">
			<view class="r-flex items-center space-x-4">
				<text class="icon iconfont icon-logo logo"></text>
				<text class="font_1 text">与安</text>
				<view class="section_2"></view>
				<text class="font_1 text_2">与您安全健康</text>
			</view>
			<view class="c-flex items-center group space-y-12">
				<image class="image_2" :src="`${staticUrl}/images/member_card/card_info/name.png`"></image>
				<text class="text_3">基础保障/全屋覆盖/无缝陪伴</text>
			</view>
			<view class="c-flex section_3 space-y-34"
				:style="{'background-image': `url(${staticUrl}/images/member_card/card_info/2_bg.png)`}">
				<text class="self-center font_2">为什么要买会员卡</text>
				<text class="font_3 text_4">
					为您提供专业级别的客服服务，可享受医疗级别的救援，我们将直接为您垫付费用，同时您还可以免费获得与安宝设备
				</text>
			</view>

		</view>
		<view class="c-flex group_2">
			<view class="c-flex justify-start items-center relative">
				<text class="font_4 text_5">开通会员享5大权益</text>
				<image class="image_3 pos" :src="`${staticUrl}/images/member_card/card_info/line.png`" />
			</view>
			<view class="r-flex equal-division">
				<view class="c-flex items-center equal-division-item">
					<image class="image_4" :src="`${staticUrl}/images/member_card/card_info/quanyi1.png`" />
					<!-- <text class="icon iconfont icon-a-24xiaoshizhuanyerengongkefu quanyi-icon"></text> -->
					<text class="font_5 text_6">24小时专业</text>
					<text class="font_5">人工客服</text>
					<text class="font_6 text_7">均通过专业能力认证</text>
				</view>
				<view class="c-flex items-center equal-division-item">
					<image class="image_4" :src="`${staticUrl}/images/member_card/card_info/quanyi2.png`" />
					<!-- <text class="icon iconfont icon-a-xiezhuhujiaojianhurenji120 quanyi-icon"></text> -->
					<text class="font_5 text_6">协助呼叫</text>
					<text class="font_5">监护人及120</text>
					<text class="font_6 text_7">安全事件双坐席服务</text>
				</view>
				<view class="c-flex items-center equal-division-item">
					<image class="image_4" :src="`${staticUrl}/images/member_card/card_info/quanyi3.png`" />
					<!-- <text class="icon iconfont icon-a-zhuanyejiuyuanzhidao quanyi-icon"></text> -->
					<text class="font_5 text_6">专业救援</text>
					<text class="font_5">指导</text>
					<text class="font_6 text_7">医疗级别急救指导</text>
				</view>
			</view>
			<view class="r-flex justify-between self-center equal-division_2">
				<view class="c-flex items-center">
					<image class="image_4" :src="`${staticUrl}/images/member_card/card_info/quanyi4.png`" />
					<!-- <text class="icon iconfont icon-a-120feiyongshunshidianfu quanyi-icon"></text> -->
					<text class="font_5 text_8">覆盖全国</text>
					<text class="font_5">的救援能力</text>
					<text class="font_7 text_10">全国364家急救中心</text>
					<text class="font_7 text_12">2800+县级以上城市</text>
				</view>
				<view class="c-flex items-center">
					<image class="image_4" :src="`${staticUrl}/images/member_card/card_info/quanyi5.png`" />
					<!-- <text class="icon iconfont icon-a-120feiyongshunshidianfu1 quanyi-icon"></text> -->
					<text class="font_5 text_9">120費用</text>
					<text class="font_5">瞬時垫付</text>
					<text class="font_7 text_11">3000元/年垫付救援</text>
					<text class="font_7 text_13">过程中的医疗费用</text>
				</view>
			</view>
		</view>
		<view class="c-flex group_3 space-y-48">
			<view class="c-flex section_4 space-y-38"
				:style="{'background-image': `url(${staticUrl}/images/member_card/card_info/3_bg.png)`}">
				<text class="self-center font_2">3大会员卡套餐</text>
				<view class="c-flex space-y-18">
					<view class="flex-col list-item space-y-8" v-for="(item, index) in list" :key="index"
						:style="{'background-image': `url(${staticUrl}/images/member_card/card_info/4_bg.png)`}">
						<view class="c-flex">
							<text class="self-start font_8">{{item.name}}</text>
							<text class="self-center font_9 text_14">{{`含${item.dateNum}年守护服务`}}</text>
						</view>
						<view class="r-flex items-center group_4 space-x-6">
							<image class="image_5" :src="`${staticUrl}/images/member_card/card_info/zengpin.png`" />
							<!-- <text class="icon iconfont icon-zengpin quanyi-icon"></text> -->
							<text class="font_4">赠送</text>
						</view>
						<view class="r-flex items-center group_4 space-x-8">
							<view class="section_5"></view>
							<text class="font_3">{{`${item.dateNum}年基础保障`}}</text>
						</view>
						<view class="r-flex items-end group_4 space-x-8">
							<view class="section_5"></view>
							<text class="font_3 text_15">{{`${item.devNum}台${item.modelName}${item.devName}设备`}}</text>
						</view>
					</view>
				</view>
			</view>
			<!-- <view class="r-flex justify-center section_6"
				:style="{'background-image': `url(${staticUrl}/images/member_card/card_info/5_bg.png)`}">
				<u-button shape="circle" class="button" size="medium" hairline="false"
					:custom-style="{ 'width': '80%', 'color': 'white', 'background-color': 'rgb(248,146,30)' }"
					hover-class="none" @click="$navTo(`pagesFamily/member-card/activate-card?cardNo=${cardNo}`)">激活会员卡
				</u-button>
			</view> -->
			<view class="r-flex jc-c section_6"
				:style="{'background-image': `url(${staticUrl}/images/member_card/card_info/5_bg.png)`}">
				<view class="r-flex jc-c btn" @click="$navTo(`pagesFamily/member-card/activate-card?cardNo=${cardNo}`)">
					<text>激活会员卡</text>
				</view>
				</u-button>
			</view>
		</view>
	</view>

	</view>
</template>

<script>
	import {
		copy
	} from '../../utils/util'
	export default {
		data() {
			return {
				cardNo: '',
				list: [{
						name: '基础保障',
						dateNum: 1,
						devNum: 1,
						devName: '与安宝',
						modelName: 'RFCS-M01-03'
					},
					{
						name: '全屋覆盖',
						dateNum: 2,
						devNum: 3,
						devName: '与安宝',
						modelName: 'RFCS-M01-03'
					},
					{
						name: '无缝陪伴',
						dateNum: 3,
						devNum: 4,
						devName: '与安宝',
						modelName: 'RFCS-M01-03'
					},
				],
				staticUrl: this.$u.http.config.staticBaseUrl,
			}
		},
		onLoad(option) {
			const {
				cardNo,
			} = option;
			console.log("cardNo:"+cardNo);
			if(!cardNo){
				console.log("卡号为空")
				return ;
			}
			this.cardNo = cardNo;
			// 记录用户扫码的历史,必须得在小程序里面做，在web端的跳转页面(mp.vue)做无法获取当前用户信息
			this.cardScan(cardNo)
		},
		onShow() {},
		methods: {
			cardScan(cardNo) {
				this.$u.api.cardScan({
					cardNo: cardNo,
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	@import url("/static/css/iconfont.css");

	.container {
		background-color: #0f1d36;
		width: 100%;
		height: 100%;
		flex: 1 1 auto;
		overflow-y: auto;
		overflow-x: hidden;
	}

	.section {
		padding: 32rpx 32rpx 0;
		width: 100%;
		height: 1106rpx;
		// padding-bottom: 35%;
		background-repeat: no-repeat;
		background-size: auto 1106rpx;
	}

	.space-x-4>view:not(:first-child),
	.space-x-4>text:not(:first-child),
	.space-x-4>image:not(:first-child) {
		margin-left: 8rpx;
	}

	.logo {
		color: #ffffff;
		font-size: 32rpx;
	}

	.quanyi-icon {
		color: #ffc16b;
		// color: #0f1d36;
		// background-color: #ffc16b;
		font-size: 80rpx;
	}

	.font_1 {
		font-size: 26rpx;
		line-height: 26rpx;
		color: #ffffff;
	}

	.text {
		font-size: 28rpx;
	}

	.section_2 {
		background-color: #ffffff;
		width: 10rpx;
		height: 2rpx;
	}

	.text_2 {
		font-size: 28rpx;
	}

	.group {
		margin-top: 58rpx;
	}

	.space-y-12>view:not(:first-child),
	.space-y-12>text:not(:first-child),
	.space-y-12>image:not(:first-child) {
		margin-top: 24rpx;
	}

	.image_2 {
		width: 534rpx;
		height: 78rpx;
	}

	.text_3 {
		color: #ffe6b2;
		font-size: 40rpx;
		font-weight: 300;
		line-height: 37rpx;
	}

	.section_3 {
		margin-top: 452rpx;
		padding: 24rpx 40rpx 76rpx;
		background-size: 100% 100%;
		background-repeat: no-repeat;
	}

	.space-y-34>view:not(:first-child),
	.space-y-34>text:not(:first-child),
	.space-y-34>image:not(:first-child) {
		margin-top: 68rpx;
	}

	.font_2 {
		font-size: 44rpx;
		line-height: 41rpx;
		font-weight: 800;
		color: #0f1d36;
	}

	.font_3 {
		font-size: 32rpx;
		line-height: 30rpx;
		color: #b17f3c;
	}

	.text_4 {
		color: #f9a85b;
		font-size: 30rpx;
		line-height: 36rpx;
	}

	.group_2 {
		margin-top: 108rpx;
	}

	.font_4 {
		font-size: 40rpx;
		line-height: 37rpx;
		font-weight: 800;
		color: #4c2c11;
	}

	.text_5 {
		color: #ffc16b;
	}

	.image_3 {
		width: 596rpx;
		height: 8rpx;
	}

	.pos {
		position: absolute;
		left: 50%;
		top: 50%;
		transform: translate(-50%, -50%);
	}

	.equal-division {
		margin-top: 40rpx;
	}

	.equal-division-item {
		flex: 1 1 250rpx;
		padding: 8rpx;
	}

	.image_4 {
		width: 80rpx;
		height: 80rpx;
	}

	.font_5 {
		font-size: 26rpx;
		line-height: 34rpx;
		font-weight: 800;
		color: #ffc16b;
	}

	.text_6 {
		margin-top: 12rpx;
	}

	.font_6 {
		font-size: 22rpx;
		line-height: 20rpx;
		font-weight: 300;
		color: #ffffff;
	}

	.text_7 {
		margin-top: 12rpx;
	}

	.equal-division_2 {
		margin-top: 52rpx;
		width: 519rpx;
	}

	.text_8 {
		margin-top: 10rpx;
	}

	.font_7 {
		font-size: 22rpx;
		line-height: 26rpx;
		font-weight: 300;
		color: #ffffff;
	}

	.text_10 {
		margin-top: 12rpx;
	}

	.text_12 {
		margin-top: 8rpx;
	}

	.text_9 {
		margin-top: 10rpx;
	}

	.text_11 {
		margin-top: 12rpx;
	}

	.text_13 {
		margin-top: 8rpx;
	}

	.group_3 {
		margin-top: 72rpx;
	}

	.space-y-48>view:not(:first-child),
	.space-y-48>text:not(:first-child),
	.space-y-48>image:not(:first-child) {
		margin-top: 96rpx;
	}

	.section_4 {
		margin: 0 32rpx;
		padding: 24rpx 24rpx 68rpx;
		background-size: 100% 100%;
		background-repeat: no-repeat;
	}

	.space-y-38>view:not(:first-child),
	.space-y-38>text:not(:first-child),
	.space-y-38>image:not(:first-child) {
		margin-top: 76rpx;
	}

	.space-y-18>view:not(:first-child),
	.space-y-18>text:not(:first-child),
	.space-y-18>image:not(:first-child) {
		margin-top: 36rpx;
	}

	.list-item {
		padding: 12rpx 16rpx 32rpx;
		background-size: 100% 100%;
		background-repeat: no-repeat;
	}

	.font_8 {
		font-size: 32rpx;
		line-height: 30rpx;
		font-weight: 800;
		color: #0f1d36;
	}

	.font_9 {
		font-size: 32rpx;
		line-height: 30rpx;
		color: #333333;
	}

	.text_14 {
		margin-top: -8rpx;
	}

	.group_4 {
		padding: 0 24rpx;
	}

	.space-x-6>view:not(:first-child),
	.space-x-6>text:not(:first-child),
	.space-x-6>image:not(:first-child) {
		margin-left: 12rpx;
	}

	.image_5 {
		width: 48rpx;
		height: 48rpx;
	}

	.space-x-8>view:not(:first-child),
	.space-x-8>text:not(:first-child),
	.space-x-8>image:not(:first-child) {
		margin-left: 16rpx;
	}

	.section_5 {
		background-color: #7f562b;
		border-radius: 50%;
		width: 12rpx;
		height: 12rpx;
	}

	.text_15 {
		font-size: 30rpx;
		line-height: 28rpx;
	}

	.space-y-8>view:not(:first-child),
	.space-y-8>text:not(:first-child),
	.space-y-8>image:not(:first-child) {
		margin-top: 16rpx;
	}

	.section_6 {
		padding: 62rpx 0 108rpx;
		background-size: 100% 100%;
		background-repeat: no-repeat;
	}

	.button {
		font-size: 44rpx;
		font-weight: 700;
		line-height: 41rpx;
	}
	.btn{
		background-color: rgb(248,146,30);
		width: 80%;
		border-radius: 42rpx;
		text{
			color: white;
			font-size: 44rpx;
			font-weight: 700;
			line-height: 84rpx;
			align-items: center;
		}
	}
</style>
