<template>
	<view class="c-flex page">
		<view class="c-flex search-area">
			<view class="r-flex search">
				<text class="icon iconfont icon-fangdajing search_icon" @click="search"></text>
				<u-input v-model="searchValue" style="width:200px" placeholder="请输入会员卡名称或者卡号" />
			</view>
		</view>
		<view class="c-flex list-area">
			<view v-for="(card, index) in cardList" :key="index">
				<view class="c-flex card-item">
					<view class="card-item-main"
						:style="{'background-image': `url(${staticUrl}/images/member_card/card_item_bg.png)`}">
						<view class="r-flex item-row">
							<view class="c-flex left-top">
								<text class="title">{{ card.cardName }}</text>
								<view class="r-flex time-area" v-if="card.activeStatus=='1'">
									<text class="text activate-time-label">激活时间：</text>
									<text class="text activate-time">{{ card.activeTime }}</text>
								</view>
							</view>
							<view class="c-flex right-top">
								<view class="c-flex activate-status" v-if="card.activeStatus=='1'"><text
										class="status">已激活</text></view>
								<view class="c-flex activate-status" v-else><text class="status">未激活</text></view>
								<view v-if="1==1" class="r-flex family-setting"
									@click="$navTo('pagesFamily/familys/index')">
									<text class="label">家庭设置</text>
									<text class="icon iconfont icon-qianjin1 bt_icon"></text>
								</view>
								<view v-if="card.devNum>1" class="r-flex family-setting"
									@click="$navTo(`pagesFamily/member-card/delay-device?cardId=${card.id}`)">
									<text class="label">延期设备</text>
									<text class="icon iconfont icon-qianjin1 bt_icon"></text>
								</view>
							</view>
						</view>
						<view class="r-flex item-row card-bottom">
							<view class="r-flex left-area" @click="showDetail(index)">
								<text class="label">激活记录</text>
								<u-icon v-if="showIndex!=index" class="icon iconfont icon-xiangxia bt_icon"></u-icon>
								<u-icon v-else class="icon iconfont icon-xiangshang bt_icon"></u-icon>
							</view>
							<view class="c-flex right-area">
								<!-- <view v-for="(xx, index) in xxlist" :key="index"> -->
								<view class="label" v-if="card.serverTerm===12">1年守护服务</view>
								<view class="label" v-if="card.serverTerm===24">2年守护服务</view>
								<view class="label" v-if="card.serverTerm===36">3年守护服务</view>
								<view class="label" v-if="card.serverTerm===12">1台与安宝设备</view>
								<view class="label" v-if="card.serverTerm===24">3台与安宝设备</view>
								<view class="label" v-if="card.serverTerm===36">4台与安宝设备</view>
							</view>
						</view>
					</view>
					<view v-if="showIndex==index" class="card-detail">
						<!-- <u-line color="white"/> -->
						<view class="r-flex card-info">
							<text class="label">卡号：</text>
							<text class="value">{{ card.cardNumber }}</text>
						</view>
						<view class="r-flex card-info">
							<text class="label">服务单号：</text>
							<text class="value">{{ card.orderNo }}</text>
						</view>
						<view class="r-flex card-info">
							<text class="label">姓名：</text>
							<text class="value">{{ card.name }}</text>
						</view>
						<view class="r-flex card-info">
							<text class="label">手机号：</text>
							<text class="value">{{ card.phone }}</text>
						</view>
						<view class="r-flex card-info">
							<text class="label">证件类型：</text>
							<text class="value">{{ card.idcardType }}</text>
						</view>
						<view class="r-flex card-info">
							<text class="label">证件号码：</text>
							<text class="value">{{ card.idcardCode }}</text>
						</view>
						<view class="r-flex card-info">
							<text class="label">服务状态：</text>
							<u-tag v-if="card.serverStatus=='1'" text="已生效" border-color="#ff7d17" bg-color="#ff7d17"
								color="#fff" />
							<u-tag v-else text="未生效" border-color="#999999" bg-color="#999999" color="#EEFFFD" />
						</view>
						<view class="r-flex card-info">
							<text class="label">服务有效期：</text>
							<text v-if="card.serverStatus==='1'"
								class="value">{{ card.serverStartDate }}至{{ card.serverEndDate }}</text>
							<text v-else class="value" style="color: #666666;">-(为家庭设置被监护人即可生效)</text>
						</view>
					</view>
				</view>
			</view>
			<u-empty v-if="!cardList.length" text="暂无数据" mode="list" style="background-color: #f2f2f2;"></u-empty>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				searchValue: '',
				cardList: [{}],
				showIndex: 0.1,
				staticUrl: this.$u.http.config.staticBaseUrl,

			}
		},
		onShow() {
			this.search();
		},
		methods: {
			search() {
				this.page = 1;
				this.fetchDatas();
			},
			fetchDatas() {
				this.$u.api.getCardsByMemberId({
					pageNum: this.page,
					nameOrNo: this.searchValue
				}).then(res => {
					this.cardList = res.records;
				})
			},
			showDetail(index) {
				if (this.showIndex != index) {
					this.showIndex = index;
				} else {
					this.showIndex = 0.1;
				}
			},
		}
	}
</script>
<style lang="scss">
	@import url("/static/css/iconfont.css");

	page {
		-webkit-user-select: text;
		user-select: text;
	}
</style>

<style lang="scss" scoped>
	.page {
		background-color: #f2f2f2;
		width: 100%;
		overflow-y: auto;
		overflow-x: hidden;
		height: 100%;
		padding-bottom: 48rpx;
		flex: 1 1 auto;

		.search-area {
			padding: 32rpx 0;
			background-color: #ffffff;
			justify-content: flex-start;

			.search {
				margin: 0 32rpx;
				padding: 6rpx 18rpx;
				border-radius: 16rpx;
				border: solid 2rpx #01b09a;

				.search_icon {
					font-size: 80rpx;
					color: #dddddd;
				}
			}
		}

		.list-area {
			padding: 32rpx 32rpx;

			.card-item {
				margin-bottom: 32rpx;
				padding-bottom: 12rpx;
				background-color: #fc9b4f;
				border-radius: 16rpx;
				box-shadow: 0px 8rpx 16rpx #00000014;

				.card-item-main {
					padding-left: 32rpx;
					padding-bottom: 34rpx;
					background-size: 100% 100%;
					background-repeat: no-repeat;
				}

				.item-row {
					justify-content: space-between;
				}

				.left-top {
					margin-top: 40rpx;

					.title {
						font-size: 40rpx;
						line-height: 37rpx;
						font-weight: 700;
						color: #ffffff;
					}

					.time-area {
						margin-top: 20rpx;

						.text {
							line-height: 20rpx;
							opacity: 0.6;
							font-size: 22rpx;
							color: #ffffff;
						}

						.activate-time-label {
							margin-top: 2rpx;
							opacity: 0.6;
							font-size: 22rpx;
							line-height: 19rpx;
							color: #ffffff;
						}

						.activate-time {
							font-size: 22rpx;
							line-height: 16rpx;
							color: #ffffff;
						}
					}
				}

				.right-top {
					.activate-status {
						justify-content: flex-start;
						align-items: center;
						align-self: center;
						padding: 12rpx 0;
						background-color: #01b09a;
						border-radius: 0px 16rpx 0px 16rpx;
						width: 160rpx;

						.status {
							font-size: 30rpx;
							font-family: PingFang;
							line-height: 28rpx;
							font-weight: 700;
							color: #ffffff;
						}

					}

					.family-setting {
						margin-top: 14rpx;
						padding: 16rpx 18rpx;
						background-color: #ffffff;
						border-radius: 32rpx 0px 0px 32rpx;
						box-shadow: 0px 8rpx 16rpx #33333314;

						.label {
							margin: 4rpx 0;
							font-size: 26rpx;
							line-height: 24rpx;
							font-weight: 700;
							color: #01b09a;
						}

						.bt_icon {
							color: #01b09a;
							margin-right: 6rpx;
							font-size: 32rpx;
						}
					}
				}

				.card-bottom {
					margin-top: 72rpx;
					justify-content: space-between;

					.left-area {
						margin-top: 32rpx;

						.label {
							margin: 4rpx 0;
							font-size: 26rpx;
							line-height: 24rpx;
							color: #ffffff;
						}

						.bt_icon {
							color: #ffffff;
							margin-left: 4rpx;
							font-size: 32rpx;
						}
					}

					.right-area {
						margin-bottom: 4rpx;
						width: 187rpx;

						.label {
							margin-top: 16rpx;
							margin-right: 10rpx;
							align-self: flex-end;
							color: #ffffff;
							font-size: 24rpx;
							line-height: 22rpx;
						}
					}
				}

				.card-detail {
					margin-top: 12rpx;

					.card-info {
						margin-top: 28rpx;
						padding: 0 32rpx;

						.label {
							font-size: 26rpx;
							line-height: 24rpx;
							font-weight: 700;
							color: #ffffff;
						}

						.value {
							margin: 4rpx 0 2rpx 4rpx;
							font-size: 26rpx;
							line-height: 19rpx;
							color: #ffffff;
						}
					}
				}

			}

		}

	}
</style>
