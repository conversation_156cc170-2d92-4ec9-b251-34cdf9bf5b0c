<template>
	<view class="c-flex">
		<view class="main">
			<view class="c-flex member-card">
				<image class="bg_img" :src="`${staticUrl}/images/member_card/member_card_bg.png`"></image>
				<view class="c-flex card_verify">
					<view class="r-flex history" @click="$navTo('pagesFamily/member-card/activate-list')">
						<text class="text">激活记录</text>
						<text class="icon iconfont icon-qianjin1"></text>
					</view>
					<view class="c-flex title">
						<view class="text">会员卡验证</view>
						<view class="descr">
							请扫码或输入会员卡反面的序列号（请参考上方示例图）
						</view>
						<view class="c-flex form">
							<view class="r-flex serial_no">
								<text class="name">序列号：</text>
								<view class="serial1">
									<u-input v-model="cardInfo.serial1" placeholder=" " maxlength="4"
										@blur="checkSerial1" input-align="center" />
								</view>
								<view class="serial2"></view>
								<view class="serial3">
									<u-input v-model="cardInfo.serial2" placeholder=" " maxlength="4"
										@blur="checkSerial2" input-align="center" />
								</view>
								<!-- #ifdef MP-WEIXIN -->
								<text class="icon iconfont icon-tiaomasaoma scan_icon" @click="clickScan"></text>
								<!-- #endif -->
							</view>
							<view class="r-flex pwd">
								<view class="name">密码：</view>
								<view class="pwd_value">
									<u-input v-model="cardInfo.pwd" placeholder="请您输入密码" maxlength="4" type="digit">
									</u-input>
								</view>
							</view>
							<view class="c-flex btn" @click="verify">
								<text class="text">验 证</text>
							</view>
						</view>
						<view class="agreement" style="margin-top: 20rpx;display:flex;align-items:center;">
							<u-checkbox-group :size="30" style="display: inline-block; vertical-align: middle;">
								<u-checkbox v-model="agreement" active-color="#f59a23"></u-checkbox>
							</u-checkbox-group>
							<text style="font-size: 30rpx;left: -20rpx;">同意</text>
							<view
								style="display: inline-block; font-size: 30rpx; vertical-align: middle; position:relative;">
								<view style="display: inline-block; color: #0596d8;"
									@click="$navTo('pagesFamily/device/agreement')">《熵行科技服务协议》</view>
							</view>
							<!-- <u-radio-group v-model="agreement" shape="square" style="display: inline-block; vertical-align: middle;" >
                <u-radio :icon-size="18" :label-size="20" active-color="#f59a23" :name="1">同意<view style="display: inline-block; color: #0596d8;" @click="gotoUrl('/pagesDevice/spec/agreement')">《熵行科技服务协议》</view></u-radio>
              </u-radio-group> -->
						</view>
					</view>
				</view>
			</view>
			<view class="c-flex use-flow">
				<view class="c-flex title">
					<text class="text">使用流程</text>
					<image class="image_1" :src="`${staticUrl}/images/member_card/title_ball.png`"></image>
				</view>
				<view class="c-flex flow">
					<view class="r-flex icon_area">
						<image class="lc" :src="`${staticUrl}/images/member_card/yanzhen.png`"></image>
						<image class="image_1" :src="`${staticUrl}/images/member_card/line.png`"></image>
						<image class="lc" :src="`${staticUrl}/images/member_card/shezhi.png`"></image>
						<image class="image_1" :src="`${staticUrl}/images/member_card/line.png`"></image>
						<image class="lc" :src="`${staticUrl}/images/member_card/jihuo.png`"></image>
					</view>
					<view class="r-flex name_area">
						<text class="name">会员卡验证</text>
						<text class="name">设置会员信息</text>
						<text class="name">激活会员卡</text>
					</view>
				</view>
			</view>
			<view class="c-flex activate-descr">
				<view class="c-flex activate-title">
					<text class="text">激活说明</text>
					<image class="image_1" :src="`${staticUrl}/images/member_card/title_ball.png`"></image>
				</view>
				<view class="activate-detail">
					<view class="text">1、会员卡成功激活以后，不能解绑，不能重新绑定，不能跨与安宝账号使用。</view>
					<view class="text">2、同一个会员超过1个月才过期，不能重复为该会员激活会员卡。</view>
					<view class="text">3、会员卡激活成功后，请确保家庭已设置该会员为被监护人。</view>
				</view>
				<view class="c-flex activate-bottom"
					:style="{'background-image': `url(${staticUrl}/images/member_card/home_sample.png)`}">
					<text class="text">请进入【首页】-【家庭管理】</text>
					<text class="text">为家庭设置被监护人信息</text>
				</view>

			</view>
		</view>

		<rich-text-modal :show="serverAgreement.agreeStep == 1" :content="serverAgreement.simpleAgreement"
			@confirm="agreeAgreement(true)" />
		<member-agreement-popup :show="serverAgreement.agreeStep == 2" :content="serverAgreement.agreement"
			@confirm="agreeAgreement(true)" @cancel="agreeAgreement(false)" />
	</view>
</template>

<script>
	import {
		validateCharNum
	} from "../../utils/util.js";
	export default {
		data() {
			return {
				cardInfo: {
					serial1: undefined,
					serial2: undefined,
					serialNo: undefined,
					cardNo: undefined,
					pwd: undefined,
				},
				serverId: undefined,
				billingMethod: undefined,
				staticUrl: this.$u.http.config.staticBaseUrl,
				agreement: false,
				serverAgreement: {
					agreeStep: 0,
					serverIds: [],
					simpleAgreement: null,
					agreement: null,
				},
			}
		},
		onLoad(option) {
			const {
				cardNo,
			} = option;
			console.log("cardNo:"+cardNo);
		},
		methods: {
			checkSerial1() {
				if (!this.cardInfo.serial1 || this.cardInfo.serial1.length != 4) {
					uni.showToast({
						duration: 2000,
						title: '请扫描或输入会员卡上的序列号',
						icon: 'none'
					})
					return;
				}
			},
			checkSerial2() {
				if (!this.cardInfo.serial2 || this.cardInfo.serial2.length != 4) {
					uni.showToast({
						duration: 2000,
						title: '请扫描或输入会员卡上的序列号',
						icon: 'none'
					})
					return;
				}
			},
			async agreeAgreement(agree) {
				if (agree === false) {
					this.serverAgreement.agreeStep = 0;
					return;
				}
				this.serverAgreement.agreeStep++;
				if (this.serverAgreement.agreeStep == 3) {
					try {
						await this.$u.api.agreeByServerAgreement(this.serverAgreement.serverIds, this.devId);
					} catch (err) {
						console.error(err);
						return;
					} finally {
						this.serverAgreement.agreeStep = 0;
					}
					this.getCardByPassword()
				}
			},
			clickScan() {
				console.log("开始扫描");
				uni.scanCode({
					success: (res) => {
						console.log('条码类型：' + res.scanType);
						console.log('条码内容：' + res.result);
						if (!res.result) {
							uni.showLoading({
								title: '解析二维码错误...',
								mask: true
							})
						} else {
							const code = res.result;
							this.cardInfo.serialNo = code;
							const arr = code.split('-');
							console.log(arr);
							this.cardInfo.serial1 = arr[0];
							this.cardInfo.serial2 = arr[1];
							let key = this.cardInfo.serial1 + this.cardInfo.serial2
						}
					},
					fail: (err) => {
						console.log(err);
						uni.showToast({
							duration: 1000,
							title: '二维码无法识别',
							icon: 'none'
						})
					}
				});
			},
			verify() {
				console.log("开始验证");
				if (!this.cardInfo.serial1 || !this.cardInfo.serial2 || this.cardInfo.serial1.length != 4 || this.cardInfo
					.serial2.length != 4) {
					uni.showToast({
						duration: 2000,
						title: '请扫描或输入会员卡上的序列号',
						icon: 'none'
					})
					return;
				}
				if (!validateCharNum(this.cardInfo.serial1) || !validateCharNum(this.cardInfo.serial2)) {
					uni.showToast({
						duration: 2000,
						title: '序列号错误，请输入正确的序列号',
						icon: 'none'
					})
					return;
				}
				if (!this.cardInfo.pwd || this.cardInfo.pwd.length != 4) {
					uni.showToast({
						duration: 2000,
						title: '输入会员卡上的密码',
						icon: 'none'
					})
					return;
				}
				if (!validateCharNum(this.cardInfo.pwd)) {
					uni.showToast({
						duration: 2000,
						title: '密码错误，请输入正确的密码',
						icon: 'none'
					})
					return;
				}

				this.checkAgreement();
			},
			async checkAgreement() {
				if (!this.agreement) {
					uni.showToast({
						duration: 2000,
						title: '请先阅读并同意《熵行科技服务协议》',
						icon: 'none'
					})
					return;
				}

				try {
					this.serverAgreement = await this.$u.api.listByServerType("WYFW");
					if (this.serverAgreement.serverIds.length) {
						this.serverAgreement.agreeStep = 1;
						return;
					}
				} catch (err) {
					console.error(err);
					return;
				}

				this.getCardByPassword();
			},
			getCardByPassword() {
				let key = this.cardInfo.serial1 + this.cardInfo.serial2;
				let password = this.cardInfo.pwd;
				// 验证成功后跳转
				this.$u.api.getCardByPassword({
					key: key,
					password: password,
				}).then(res => {
					let serverId = res.serverId;
					let billingMethod;
					if (res.serverTerm == 12) {
						billingMethod = "Y";
					} else if (res.serverTerm == 24) {
						billingMethod = "TwoY";
					} else if (res.serverTerm == 36) {
						billingMethod = "ThreeY";
					}
					let cardId = res.id;
					this.$navTo(
						`pagesFamily/member-server/order-form?serverId=${serverId}&billingMethod=${billingMethod}&cardId=${cardId}&source=card`
					)
				}).catch(err => {
					uni.showToast({
						duration: 2000,
						title: err.message || '验证密码失败',
						icon: 'none'
					})
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	@import url("/static/css/iconfont.css");

	.main {
		padding-bottom: 28rpx;
		flex: 1 1 auto;
		overflow-y: auto;


		.member-card {
			padding: 348rpx 0 72rpx;
			height: 1178rpx;
			position: relative;
			justify-content: flex-start;

			.bg_img {
				width: 750rpx;
				height: 1178rpx;
				top: 0;
				right: 0;
				bottom: 0;
				left: 0;
				position: absolute;
			}

			.card_verify {
				margin-left: 32rpx;
				position: relative;
			}

			.history {
				width: 236rpx;
				padding: 24rpx 32rpx 24rpx 40rpx;
				background-color: #ffffff;
				border-radius: 40rpx 0px 0px 40rpx;
				align-self: flex-end;
				justify-content: space-evenly;
				color: #33bfad;

				.text {
					font-size: 32rpx;
					line-height: 30rpx;
				}

				.ic {
					width: 32rpx;
					height: 32rpx;
				}
			}

			.title {
				padding: 64rpx 0;
				color: #ffffff;

				.text {
					align-self: center;
					font-size: 76rpx;
					font-weight: 700;
					line-height: 71rpx;
				}

				.descr {
					margin-top: 28rpx;
					align-self: center;
					font-size: 26rpx;
					line-height: 25rpx;
					opacity: 0.6;
				}

				.form {
					margin-top: 28rpx;

					.serial_no {
						margin-right: 32rpx;
						padding: 20rpx 32rpx;
						background-color: #ffffff;
						border-radius: 16rpx;

						.name {
							align-self: center;
							line-height: 34rpx;
							font-size: 36rpx;
							line-height: 32rpx;
							font-weight: 700;
							color: #393947;
						}

						.serial1 {
							flex-shrink: 0;
							background-color: #f2f2f2;
							border-radius: 8rpx;
							width: 160rpx;
							height: 72rpx;
						}

						.serial2 {
							margin-left: 10rpx;
							flex-shrink: 0;
							align-self: center;
							background-color: #ffffff;
							border-radius: 4rpx;
							width: 16rpx;
							height: 4rpx;
							border: solid 2rpx #707070;
						}

						.serial3 {
							flex-shrink: 0;
							background-color: #f2f2f2;
							border-radius: 8rpx;
							width: 160rpx;
							height: 72rpx;
							margin-left: 12rpx;
						}

						.scan_icon {
							margin-left: 48rpx;
							font-size: 72rpx;
							color: #01B09A;
						}
					}

					.pwd {
						margin-top: 32rpx;
						margin-right: 32rpx;
						padding: 36rpx 32rpx 40rpx;
						background-color: #ffffff;
						border-radius: 16rpx;

						.name {
							line-height: 33rpx;
							font-size: 36rpx;
							font-family: PingFang;
							font-weight: 700;
							color: #393947;
						}

						.pwd_value {
							margin-left: 62rpx;
							margin-top: 4rpx;
							color: #dddddd;
							font-size: 32rpx;
							font-family: PingFang SC Regular;
							line-height: 30rpx;
						}
					}

					.btn {
						margin-right: 32rpx;
						margin-top: 32rpx;
						padding: 32rpx 0 40rpx;
						border-radius: 16rpx;
						align-items: center;
						justify-content: flex-start;
						background-image: linear-gradient(#99DFD6, #01B09A);

						.text {
							color: #ffffff;
							font-size: 44rpx;
							font-family: PingFang;
							font-weight: 700;
							line-height: 40rpx;
						}
					}
				}
			}
		}

		.use-flow {
			margin-top: 60rpx;
			padding-left: 48rpx;
			padding-right: 40rpx;

			.title {
				align-self: center;
				width: 368rpx;
				position: relative;
				justify-content: flex-start;
				align-items: center;

				.text {
					font-size: 40rpx;
					font-family: PingFang;
					line-height: 38rpx;
					font-weight: 700;
					color: #01b09a;
				}

				.image_1 {
					flex-shrink: 0;
					width: 368rpx;
					height: 32rpx;
					top: 0;
					right: 0;
					bottom: 0;
					left: 0;
					position: absolute;
				}
			}

			.flow {
				margin-top: 36rpx;

				.icon_area {
					padding: 0 14rpx;
					justify-content: space-around;

					.lc {
						width: 128rpx;
						height: 128rpx;
					}

					.image_1 {
						align-self: center;
						width: 80rpx;
						height: 2rpx;
					}
				}

				.name_area {
					margin-top: 20rpx;
					justify-content: space-between;

					.name {
						line-height: 29rpx;
						font-size: 32rpx;
						color: #666666;
					}
				}
			}
		}

		.activate-descr {
			margin-top: 102rpx;
			align-self: center;
			padding-left: 48rpx;
			padding-right: 40rpx;

			.activate-title {
				position: relative;
				width: 368rpx;
				align-items: center;
				align-self: center;
				justify-content: flex-start;

				.text {
					font-size: 40rpx;
					line-height: 38rpx;
					font-weight: 700;
					color: #01b09a;
				}

				.image_1 {
					flex-shrink: 0;
					width: 368rpx !important;
					height: 32rpx !important;
					top: 0;
					right: 0;
					bottom: 0;
					left: 0;
					position: absolute;
				}
			}

			.activate-detail {
				position: relative;
				margin-top: 40rpx;
				height: 69rpx;

				.text {
					margin-top: 26rpx;
					font-size: 26rpx;
					line-height: 32rpx;
					color: #666666;
				}
			}

			.activate-bottom {
				margin-top: 244rpx;
				padding: 764rpx 0 28rpx;
				align-self: center;
				align-items: center;
				background-size: 100% 100%;
				background-repeat: no-repeat;
				width: 484rpx;
				border: dotted 2rpx #707070;

				.text {
					font-size: 32rpx;
					line-height: 38rpx;
					font-weight: 700;
					color: #01b09a;
				}
			}
		}
	}
</style>
