<template>
	<view class="container">
		<view class="search">
			<u-search placeholder="根据会员卡信息搜索" v-model="keyword" @custom="fetchDatas" @search="fetchDatas"></u-search>
		</view>
		<view class="main">
			<scroll-view scroll-y style="height: 100%; width: 100%;" @scrolltolower="reachBottom">
				<view class="content">
					<view class="list">
						<view v-for="(item, index) in list" :key="index">
							<view class="c-flex card">
								<view @click="gotoPage(item)">
									<view class="r-flex jc-sb row">
										<text class="name">{{item.cardName ||'-'}}</text>
										<text v-if="item.deliveryType=='0'"
											style="color: #01b09a;">{{item.deliveryTypeDesc}}</text>
										<text v-else-if="item.deliveryType=='1'">{{item.deliveryTypeDesc}}</text>
										<text v-else style="color: #fa6400;">未提货</text>
									</view>
									<view class="r-flex jc-sb row">
										<text class="info">会员卡号：{{item.cardNumber ||'-'}}</text>
										<text class="info">{{item.devNum}}台设备</text>
									</view>
								</view>
								<view class="r-flex jc-sb row" v-if="item.deliveryType=='1'">
									<text class="info">{{item.expressCompany ||'-'}}：</text>
									<view class="r-flex">
										<text class="info">{{item.expressNumber ||'-'}}</text>
										<u-line direction="col" length="26rpx" color="#ababab" margin="0rpx 16rpx" />
										<view class="c-flex">
											<u-tag class="tag" text="复制" @click="copy(item.expressNumber)" mode="plain"
												size="mini" color="#fff" bg-color="#ababab" />
										</view>
									</view>
								</view>
								<view class="r-flex jc-fe">
									<!--
									<u-button v-if="item.deliveryType=='1'" :custom-style="{ 'color': 'white', 'background-color': '#01b09a' }" shape="circle" size="mini" >确认收货
									</u-button>
									<u-button v-else :custom-style="{ 'color': 'white', 'background-color': '#01b09a' }" shape="circle" size="mini">提货</u-button>
									-->
									<u-button v-if="!item.deliveryType"
										@click="$navTo(`pagesFamily/member-card/take-delivery-goods?cardId=${item.id}&source=tihuo`)"
										:custom-style="{ 'color': '#01b09a', 'background-color': 'white' }"
										shape="circle" size="mini">提货</u-button>
								</view>
							</view>
						</view>
						<u-empty v-if="!list.length" text="暂无数据" mode="list"></u-empty>
					</view>
					<u-loadmore v-if="hasNext" :status="loadStatus" class="loadmore" />
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script>
	import {
		copy
	} from '../../utils/util'
	export default {
		data() {
			return {
				list: [{}],
				currentPage: 0,
				keyword: '',
				loadStatus: 'loadmore',
				hasNext: 0,
			}
		},
		onLoad(option) {
			this.source = option.source;

		},
		onShow() {
			this.fetchDatas();
		},
		methods: {
			copy,
			gotoPage(item) {
				if (!item.deliveryType) {
					this.$navTo(`pagesFamily/member-card/take-delivery-goods?cardId=${item.id}&source=tihuo`)
				} else {
					this.$navTo(`pagesFamily/member-card/take-delivery-goods?cardId=${item.id}`)
					//this.$navTo(`pagesFamily/member-card/card-info?cardId=${item.id}`)
				}
			},
			reachBottom() {
				if (this.currentPage >= 1) {
					this.loadStatus = 'loading';
					setTimeout(() => {
						this.currentPage = this.currentPage + 1;
						this.fetchDatas();
					}, 1200);
				}
			},
			fetchDatas() {
				uni.showLoading({
					title: '加载中...',
					mask: true
				})
				this.$u.api.selectDeliveryByPage({
					pageReqVo: {
						current: this.currentPage,
						pageSize: 10,
					},
					keyword: this.keyword,
				}).then(res => {
					console.log(res)
					if (res) {
						this.list = res.records
						this.hasNext = res.current < res.pages
					}
					this.loadStatus = 'loadmore'
				}).catch(err => {
					this.list = {}
				}).finally(() => {
					setTimeout(() => {
						uni.hideLoading();
					}, 1000)
				})
			},
		}
	}
</script>
<style lang="scss">
	page {
		background-color: #f7f7f7;
	}
</style>
<style lang="scss" scoped>
	@import url("/static/css/iconfont.css");

	.container {
		width: 100%;
		height: 100%;

		.search {
			position: sticky;
			top: 0rpx;
			left: 0rpx;
			padding: 32rpx;
			z-index: 10;
			background-color: #ffffff;
		}

		.main {
			padding: 32rpx;

			.list {
				border-radius: 20rpx;

				.card {
					margin-bottom: 32rpx;
					padding: 24rpx;
					background-color: #ffffff;
					border-radius: 16rpx;

					// .btn {
					// 	color: #ffffff;
					// 	background-color: #01b09a;
					// }

					.row {
						margin-bottom: 18rpx;
						color: #999999;
						font-size: 32rpx;

						.name {
							color: #333333;
							font-weight: 700;
						}

						.info {
							font-size: 28rpx;

						}

					}
				}
			}
		}

	}

	::v-deep uni-button {
		margin-left: 0;
		margin-right: 0;
	}
</style>
