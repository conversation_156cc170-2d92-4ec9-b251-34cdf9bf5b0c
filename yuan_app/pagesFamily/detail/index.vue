<template>
	<view class="family-detail-page">
		<view class="top-header-line"></view>
		<view class="item-block">
			<view class="title">
				{{ family.selfCreate === false ? `${ family.createMemberName } 共享的家庭` : '我创建的家庭' }}
			</view>
			<u-row v-for="(row, index) in rows"
				:style="{ 'border-bottom': index < rows.length - 1 ? '2rpx solid #f1f1f1' : 'none' }"
				 :key="index" @click="handleTo(row)">
				<u-col span="4">
					<view class="left-info">
						<view class="field-name" style="position: relative; top: 6rpx;">{{ row.name }}</view>
					</view>
				</u-col>
				<u-col span="8">
					<view v-if="row.type === 'address'"  @longtap="longtapCopy(row.remark)" class="right-status">
						{{ row.remark || '' }}
						<u-icon name="arrow-right" color="#b9b9b9" size="22"></u-icon>
					</view>
					<view v-else class="right-status">
						{{ row.remark || '' }}
						<u-icon name="arrow-right" color="#b9b9b9" size="22"></u-icon>
					</view>
				</u-col>
			</u-row>
		</view>
		<view v-if="family.selfCreate === true" class="move-device" @click="$navTo(`pagesFamily/device/choice?id=${family.id}`)">
			<text class="icon iconfont icon-tianjiazujian"></text>转入设备
		</view>
		
		<u-modal v-model="familyNameDialog" title="修改家庭的名称" :show-cancel-button="true" @confirm="handleConfirmNameDialog">
			<view class="slot-content" style="padding: 40rpx;">
				<uni-easyinput v-model="familyNameDialogValue" placeholder="请输入内容" :maxlength="15"></uni-easyinput>
			</view>
		</u-modal>
	</view>
</template>

<script>
	import {longtapCopy} from '../../utils/util'
	export default {
		data() {
			return {
				rows: [
					{ name: '家庭名称', remark: '', type: 'home' },
					{ name: '家庭位置', remark: '', type: 'address' },
					{ name: '家庭成员', remark: '', type: 'member' },
					{ name: '紧急联系人', remark: '', type: 'contact' },
					{ name: '被监护人', remark: '', type: 'older' },
					{ name: '设备', remark: '', type: 'device' }
				],
				currFamilyId: undefined,
				family: {},
				familyNameDialog: false,
				familyNameDialogValue: ''
			}
		},
		onLoad(option) {
			this.currFamilyId = option.id
		},
		onShow() {
			this.fetchDetail(this.currFamilyId);
		},
		methods: {
			longtapCopy,
			fetchDetail(id) {
				this.$u.api.fetchFamilyDetail({ familyId: id }).then(res => {
					let _family = res || {};
					this.rows = [
						{ name: '家庭名称', remark: _family.name || '我的家', type: 'home' },
						{ name: '家庭位置', remark: _family.addr || '-', type: 'address' },
						{ name: '家庭成员', remark: `${_family.memberCount}个成员`, type: 'member' },
						{ name: '紧急联系人', remark: `${_family.contactCount}个联系人`, type: 'contact' },
						{ name: '被监护人', remark: `${_family.olderCount}个被监护人`, type: 'older' },
						{ name: '设备', remark: `${_family.devCount}个设备`, type: 'device' }
					]
					this.family = _family;
				})
			},
			handleTo(row) {
				if (!this.family || !this.family.id) return;
				if (row.type === 'home' && this.family.selfCreate === true) {
					this.handleOpenNameDialog();
				} else if (row.type === 'address' && this.family.selfCreate === true) {
					this.$navTo(`pagesFamily/address/index?id=${ this.family.id }`)
				} else if (row.type === 'member') {
					this.$navTo(`pagesFamily/member/index?id=${ this.family.id }&name=${ this.family.name }&selfCreate=${this.family.selfCreate}`)
				} else if (row.type === 'device') {
					this.$navTo(`pagesFamily/device/index?id=${ this.family.id }&name=${ this.family.name }&selfCreate=${this.family.selfCreate}`)
				} else if (row.type === 'contact') {
					this.$navTo(`pagesMy/device/connector/selector?source=family&id=${ this.family.id }&name=${ this.family.name }&selfCreate=${this.family.selfCreate}`)
				} else if (row.type === 'older') {
					this.$navTo(`pagesMy/device/guardian/selector?source=family&id=${ this.family.id }&name=${ this.family.name }&selfCreate=${this.family.selfCreate}`)
				} 
			},
			handleOpenNameDialog() {
				this.familyNameDialog = true
				this.familyNameDialogValue = this.family.name
			},
			handleConfirmNameDialog() {
				this.handleSubmit();
			},
			handleSubmit() {
				if (!this.family.id || !this.familyNameDialogValue) {
					uni.showModal({ content: '请检查数据是否填写完整', showCancel: false, confirmText: '关闭' })
					return;
				}
				let _params = {
					id: this.family.id,
					name: this.familyNameDialogValue,
					// latitude: this.latitude,
					// longitude: this.longitude,
					// houseNumber: this.houseNumber
				}
				uni.showLoading({
					title: '保存中...',
					mask: true
				})
				this.$u.api.execUpdateFamily(_params).then(res => {
					uni.showToast({ duration: 2000, title: '保存名称成功', icon: 'none' })
					this.family.name = this.familyNameDialogValue
					this.rows[0]['remark'] = this.familyNameDialogValue
				}).catch(err => {
					console.log('err', err)
					uni.showToast({ duration: 2000, title: err.message || '发生未知错误', icon: 'none' })
				}).finally(() => {
					setTimeout(() => {
						uni.hideLoading();
					}, 1000)
				})
			},
		}
	}
</script>

<style lang="scss">
page {
	background-color: #F7F7F7;
}
</style>

<style lang="scss" scoped>
@import url("/static/css/iconfont.css");
.family-detail-page {
	// margin-top: 20rpx;
	.item-block {
		// height: 784rpx;
		background: white;
		padding: 24rpx 30rpx;
		color: $u-content-color;
		font-size: 28rpx;
		// box-shadow: 0rpx 8rpx 24rpx 0rpx rgba(0, 0, 0, 0.1);
		border-radius: 16rpx;
		padding-top: 0rpx;
		
		.title {
			padding-left: 10rpx;
			color: #6B6B6B;
			font-size: 24rpx;
			margin-top: 18rpx;
			margin-bottom: 12rpx;
			border-bottom: 2rpx solid #f1f1f1;
			padding: 30rpx 0rpx;
		}
		
		u-row {
			display: block;
			line-height: 40rpx;
			padding: 28rpx 0;
		}
		//#ifdef H5
		// line-height: 30px;
		.u-row {
			padding: 15px 0px 20px 0px;
		}
		//#endif
		.left-info {
			image {
				width: 36rpx;
				height: 36rpx;
				display: inline-block;
				margin-right: 20rpx;
				vertical-align: middle;
			}
			.field-name {
				display: inline-block;
				color: #454444;
				font-size: 28rpx;
				position: relative;
				top: 2rpx;
				font-weight: bold;
			}
		}
		.right-status {
			color: #b9b9b9;
			text-align: right;
			// margin-top: 28rpx;
			::v-deep .u-icon {
				margin-left: 10rpx;
				position: relative;
				top: -3rpx;
			}
			::v-deep .circle-tip-num {
				display: inline-block;
				width: 30rpx;
				height: 30rpx;
				border-radius: 30rpx;
				text-align: center;
				background: red;
				color: white;
			}
			
		}
		&:nth-child(n + 2) {
			margin-top: 20rpx;
		}
	}
		
	.move-device {
		color: #000;
		font-size: 28rpx;
		margin-top: 30rpx;
		padding-left: 40rpx;
		font-weight: bold;
		::v-deep .icon {
			color: #01B09A;
			position: relative;
			top: 2rpx;
			margin-right: 16rpx;
		}
	}
}
</style>
