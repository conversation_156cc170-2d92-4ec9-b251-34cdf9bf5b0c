<template>
	<view class="common-not-found">
		<view class="not-found">
			<image class="pic" :src="`${pic} || ${staticUrl}/images/device/pic-guardian-notfound.png`"></image>
			<view class="desc">{{ desc }}</view>
			<view class="desc2" v-if="desc2">{{ desc2 }}</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'RecordNotFound',
	props: {
		pic: String,
		desc: String,
		desc2: String,
	},
	computed: {
	},
	methods: {
	}
}
</script>

<style lang="scss" scoped>
.common-not-found {
	text-align: center;
	.pic {
		height: 268rpx;
		width: 268rpx;
	}
	.desc {
		margin-top: 69rpx;
		color: #5E5D5D;
	}
	.desc2 {
		color: #5E5D5D;
	}
}
</style>
