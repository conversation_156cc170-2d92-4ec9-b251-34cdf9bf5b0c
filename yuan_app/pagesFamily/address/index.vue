<template>
	<view class="address-page">
		<view class="top-header-line"></view>
		
		<view class="top-wrap">
			<image class="img-center-bg" :src="`${staticUrl}/images/device/pic-address.jpg`"></image>
		</view>
		
		<view>
			<u-form :model="form" ref="uForm" label-position="top">
				<u-form-item label="家庭名称" prop="name" required :maxlength="15" >
					<u-input v-model="form.name" type="text" :border="true" placeholder="我的家"/>
				</u-form-item>
				<u-form-item label="安装地址" prop="address">
					<view class="reqired-icon">*</view>
					<u-input :value="form.address" placeholder="详细地址" disabled :clearable="false" :custom-style="{ 'color': '#8e8e8e' }" />
					<!-- <u-icon name="map-fill" class="map-icon"></u-icon> -->
					<text class="icon iconfont icon-dingwei map-icon expand-hotspot" style="margin-left: 10rpx;" @click="openMapSelectDialog"></text>
				</u-form-item>
				<u-form-item prop="houseNumber">
					<u-input v-model="form.houseNumber" placeholder="门牌号" :clearable="false" :custom-style="{ 'color': '#8e8e8e' }" />
				</u-form-item>
				<view class="sub-tip">注：地址填写到门牌号，否则将影响告警救援</view>
			</u-form>
		</view>
		
		<view class="footer-btns">
			<u-button shape="circle" class="next-btn diy-btn" size="medium" 
			:custom-style="{ 'width': '100%', 'color': 'white', 'background-color': '#01B09A' }"  hover-class="none"
			@click="submitSave">{{first?'下一步':'保存'}}</u-button>
		</view>
	</view>
</template>

<script>
	import QQMapWX from '../../common/qqmap-wx-jssdk.min.js'
	export default {
		data() {
			return {
				staticUrl: this.$u.http.config.staticBaseUrl,
				currFamilyId: undefined,
				form: {
					name: "我的家",
					address: undefined,
					latitude: undefined,
					longitude: undefined,
					houseNumber: undefined
				},
				rules: {
					address: [
						{
							required: true,
							message: '请选择地址',
							trigger: ['blur']
						}
					]
				},
				//第一次创建家庭，需要完善被监护人和紧急联系人
				//http://localhost/pagesFamily/address/index?first=true
				first : false
			}
		},
   //      onLoad() {//默认加载
   //          // this.login();
			// wx.hideHomeButton()
   //      },
		onLoad(option) {
			this.currFamilyId = option.id;
			if (this.currFamilyId) {
				this.fetchFamilyInfo();
			} else {
				uni.setNavigationBarTitle("创建家庭")
			}
			this.qqmapsdk = new QQMapWX({
			        // 在腾讯平台自己申请的秘钥
			        key: 'FCABZ-6CIKF-HHUJP-NEYSL-652YV-JQFJ6' 
			    })
			this.first=option.first;
			
		},
		methods: {
			fetchFamilyInfo() {
				uni.showLoading({
					title: '加载中...',
					mask: true
				})
				this.$u.api.fetchFamilyDetail({ familyId: this.currFamilyId }).then(res => {
					if (res) {
						this.form.name = res.name;
						this.form.address = res.addr
						this.form.houseNumber = res.houseNumber
						this.form.latitude = res.latitude
						this.form.longitude = res.longitude
					}
				}).finally(() => {
					setTimeout(() => {
						uni.hideLoading();
					}, 1000);
				})
			},
			async submitSave() {
				if (!this.form.name) {
					uni.showToast({ duration: 2000, title: '请填写家庭名称', icon: 'none' })
					return;
				}
				if (!this.form.address) {
					uni.showToast({ duration: 2000, title: '请先选择安装地址', icon: 'none' })
					return;
				}
				if (!this.form.houseNumber) {
					uni.showToast({ duration: 2000, title: '请填写具体的门牌号', icon: 'none' })
					return;
				}
				let _params = {
					id: this.currFamilyId,
					name: this.form.name,
					addr: this.form.address,
					houseNumber: this.form.houseNumber,
					latitude: this.form.latitude,
					longitude: this.form.longitude,
				}
				uni.showLoading({
					title: '保存中...',
					mask: true
				})

				setTimeout(uni.hideLoading, 2000);

				if (this.currFamilyId) {
					try {
						await this.$u.api.execUpdateFamily(_params);
						uni.showToast({ duration: 2000, title: '修改成功', icon: 'none' });
					} catch (err) {
						console.error(err);
					}
				} else {
					try {
						await this.$u.api.execCreateFamily(_params);
						uni.showToast({ duration: 2000, title: '创建成功', icon: 'none' });
					} catch (err) {
						console.error(err);
					}
				}
				if(this.first){
					console.log("首次创建，进入创建被监护人页面")
					this.$navTo(`pagesMy/device/guardian/edit?source=createFamily`)
				}else{
					setTimeout(uni.navigateBack, 500);
				}
			},
			openMapSelectDialog() {
				let _that = this;
				uni.chooseLocation({
					success: (res) => {
						 console.log(res);
						 console.log('位置名称：' + res.name);
						 console.log('详细地址：' + res.address);
						 console.log('纬度：' + res.latitude);
						 console.log('经度：' + res.longitude);
						 _that.form.address = res.address.indexOf(res.name) != -1 ? res.address : res.address + res.name;
						 _that.form.latitude = res.latitude;
						 _that.form.longitude = res.longitude;
						 //https://lbs.qq.com/miniProgram/jsSdk/jsSdkGuide/methodReverseGeocoder
						 this.qqmapsdk.reverseGeocoder({
							//Object格式
							location: {
								latitude: res.latitude,
								longitude: res.longitude
							}, 
							success: function(res) {//成功后的回调
							   const result=res.result;
							   console.log("result:",result)
							   const mapdata=result.ad_info;
							   /* result:
							   {
							   	"ad_info": {
							   		"adcode": "430104",
							   		"city": "长沙市",
							   		"city_code": "156430100",
							   		"district": "岳麓区",
							   		"location": {
							   			"lat": 28.234202,
							   			"lng": 112.930116
							   		},
							   		"name": "中国,湖南省,长沙市,岳麓区",
							   		"nation": "中国",
							   		"nation_code": "156",
							   		"province": "湖南省"
							   	},
							   	"address": "湖南省长沙市岳麓区环湖路393号",
							   	"address_component": {
							   		"city": "长沙市",
							   		"district": "岳麓区",
							   		"nation": "中国",
							   		"province": "湖南省",
							   		"street": "环湖路",
							   		"street_number": "环湖路393号"
							   	},
							   	"address_reference": {
							   		"crossroad": {
							   			"_dir_desc": "西南",
							   			"_distance": 129.69999999999999,
							   			"id": "11750876",
							   			"location": {
							   				"lat": 28.18695,
							   				"lng": 112.90221
							   			},
							   			"title": "环湖路/近湖二路(路口)"
							   		},
							   		"landmark_l2": {
							   			"_dir_desc": "内",
							   			"_distance": 0,
							   			"id": "3816544955698278400",
							   			"location": {
							   				"lat": 28.186378000000001,
							   				"lng": 112.900993
							   			},
							   			"title": "中海·梅溪湖壹号"
							   		},
							   		"street": {
							   			"_dir_desc": "北",
							   			"_distance": 10.4,
							   			"id": "2287274187843627947",
							   			"location": {
							   				"lat": 28.184892999999999,
							   				"lng": 112.89965100000001
							   			},
							   			"title": "环湖路"
							   		},
							   		"street_number": {
							   			"_distance": 1.3999999999999999,
							   			"id": "425227616330003115819570",
							   			"location": {
							   				"lat": 28.186389999999999,
							   				"lng": 112.90098999999999
							   			},
							   			"title": "环湖路393号"
							   		},
							   		"town": {
							   			"_dir_desc": "内",
							   			"_distance": 0,
							   			"id": "430104010",
							   			"location": {
							   				"lat": 28.187453999999999,
							   				"lng": 112.865016
							   			},
							   			"title": "梅溪湖街道"
							   		}
							   	},
							   	"formatted_addresses": {
							   		"recommend": "岳麓区中海·梅溪湖壹号(环湖路北)",
							   		"rough": "岳麓区中海·梅溪湖壹号(环湖路北)"
							   	},
							   	"location": {
							   		"lat": 28.186378000000001,
							   		"lng": 112.900993
							   	}
							   }
							   
								*/
							   _that.city = mapdata.city;
						   },fail: function(error) {
							   console.error(error);
							 },
							 complete: function(res) {
							   //console.log(res);
							 }
						});
						 
					 }
				});
			}
		}
	}
</script>

<style lang="scss">
// #ifdef H5
page {
	height: 100%;
}
// #endif
</style>
<style lang="scss" scoped>
@import url("/static/css/iconfont.css");
.address-page {
	height: 100vh;
	// #ifdef H5
	height: 100%;
	// #endif
	position: relative;
	padding: 60upx;
	box-sizing: border-box;
	text-align: center;
	.top-wrap {
		padding-top: 128rpx;
		.img-center-bg {
			width: 327rpx;
			height: 268rpx;
			margin: 0 auto;
		}
		.tip {
			font-size: 30upx;
			color: #5E5D5D;
			font-size: 30rpx;
			font-weight: 500;
			margin-top: 55rpx;
			font-weight: bold;
		}
	}
	.reqired-icon {
		color: red;
		display: inline-block;
		position: absolute;
		top: 26rpx;
		font-size: 43rpx;
		left: 0rpx;
		// #ifdef H5
		top: 19rpx;
		// #endif
	}
	.map-icon {
		display: inline-block;
		position: absolute;
		font-size: 43rpx;
		right: 0rpx;
	    top: 70rpx;
	    color: #3e70fd;
		z-index: 2;
		// #ifndef MP-WEIXIN
		top: 90rpx;
		// #endif
	}
	.sub-tip {
		margin-top: 14rpx;
		text-align: left;
		font-size: 26rpx;
		color: #333;
	}
	.footer-btns {
		position: absolute;
		bottom: 40rpx;
		left: 40rpx;
		right: 40rpx;
		margin: 0 auto;
		.next-btn {
			display: block;
			margin-top: 20rpx;
		}
	}
	::v-deep input {
		padding-right: 54rpx;
	}
	::v-deep .u-form-item {
		padding: 10rpx 0px;
	}
	::v-deep .u-form-item--left__content__label {
		padding-left: 30rpx;
	}
}

</style>
