<template>
	<view class="family-device-page" :class="{ 'empty-body-100': !devs || !devs.length }">
		<view class="top-header-line"></view>
		<u-empty v-if="!devs || !devs.length" text="暂无设备" mode="list"></u-empty>
		<view v-else class="item-block">
			<view class="title">
				<u-row>
					<u-col span="8">家庭名称</u-col>
					<u-col span="4">
						<view style="text-align: right; color: #888;">
							{{ familyName || '' }}
							<u-icon name="arrow-right" color="#b9b9b9" size="22" style="margin-left: 10rpx;"></u-icon>
						</view>
					</u-col>
				</u-row>
			</view>
			<u-row v-for="(dev, index) in devs" :key="index" @click="handleShowMove(dev)">
				<u-col span="10">
					<view class="infos">
						<image class="left-img" :src="staticUrl + '/images/device/device-1-1.png'"></image>
						<view class="right-info">
							<view class="device-name">{{ (dev.devName || dev.devCode) || '' }}</view>
							<view v-if="dev.selfBind === false" class="device-share-info">{{ dev.bindMemberName ? `来自 ${dev.bindMemberName} 的分享` : '' }}</view>
						</view>
					</view>
				</u-col>
				<u-col span="2">
					<view class="right-status">
						<u-icon name="arrow-right" color="#b9b9b9" size="22"></u-icon>
					</view>
				</u-col>
			</u-row>
		</view>
		
		<u-action-sheet :list="dialog.move.list" v-model="dialog.move.show" :safe-area-inset-bottom="true" @click="handleMove">
			<template v-slot="{ curr }">
				<view style="color: #888; font-size: 22rpx; margin-bottom: 22rpx;">{{ (curr.item.devName || curr.item.devCode) || '' }}({{ curr.item.devCode || '' }})</view>
				<view style="text-align: center;">{{ curr.item.text }}</view>
			</template>
		</u-action-sheet>
		
	</view>
</template>

<script>
	export default {
		data() {
			return {
				staticUrl: this.$u.http.config.staticBaseUrl,
				dialog: {
					move: {
						list: [{
							devName: '',
							devCode: '',
							text: '转移设备'
						}],
						row: {},
						show: false,
					}
				},
				familyId: undefined,
				familyName: '',
				devs: [],
				family: {}
			}
		},
		onLoad(option) {
			this.familyId = option.id;
			this.familyName = option.name;
			this.fetchDevList(option.id);
		},
		onShow() {
			this.fetchDetail(this.familyId);
		},
		methods: {
			fetchDetail(id) {
				this.$u.api.fetchFamilyDetail({ familyId: id }).then(res => {
					this.family = res || {};
				})
			},
			fetchDevList(id) {
				this.$u.api.fetchFamilyDevList({ familyId: id }).then(res => {
					this.devs = (res || [])
				}).catch(err => {
					uni.showToast({ duration: 2000, title: err.message || '发生未知错误', icon: 'none' })
				})
			},
			handleMove(index) {
				if (!this.dialog.move.row || !this.dialog.move.row.id) return;
				this.$navTo(`pagesFamily/device/move?familyId=${this.familyId}&devId=${ this.dialog.move.row.id }`)
			},
			handleShowMove(row) {
				if (this.family.selfCreate === true) {
					if (row.selfBind === true) {
						this.dialog.move.row = row
						this.dialog.move.list = [
							{
								devName: row.devName,
								devCode: row.devCode,
								text: '转移设备'
							}
						]
						this.dialog.move.show = true
					} else {
						this.$toast('分享设备无法转移');
					}
				}
			}
		}
	}
</script>

<style lang="scss">
page {
	background-color: #F7F7F7;
}
</style>

<style lang="scss" scoped>
.family-device-page {
	.item-block {
		// height: 784rpx;
		background: white;
		padding: 24rpx 30rpx;
		color: $u-content-color;
		font-size: 28rpx;
		// box-shadow: 0rpx 8rpx 24rpx 0rpx rgba(0, 0, 0, 0.1);
		border-radius: 16rpx;
		padding-top: 0rpx;
		
		.title {
			// display: inline-block;
			padding-left: 10rpx;
			color: #000;
			font-size: 30rpx;
			margin-top: 28rpx;
			margin-bottom: 12rpx;
		}
		
		u-row {
			display: block;
			line-height: 40rpx;
			padding: 18rpx 0;
			border-top: 2rpx solid #ECECEC;
			margin-top: 20rpx;
			padding-top: 30rpx;
		}
		//#ifndef MP-WEIXIN
		.u-row {
			line-height: 60px;
			border-top: 2rpx solid #ECECEC;
		}
		//#endif
		.infos {
			display: flex;
			flex-direction: row;
			align-items: center;
			.left-img {
				width: 100rpx;
				height: 100rpx;
				display: inline-block;
				margin-right: 20rpx;
				vertical-align: middle;
			}
			.right-info {
				flex: 1;
				color: #000;
				.device-name {
					font-size: 30rpx;
				}
				.device-share-info {
					font-size: 24rpx;
					margin-top: 10rpx;
				}
			}
		}
		.right-status {
			color: #b9b9b9;
			text-align: right;
			margin-top: 30rpx;
			::v-deep .u-icon {
				margin-left: 10rpx;
				position: relative;
				top: -3rpx;
			}
			
		}
		&:nth-child(n + 2) {
			margin-top: 20rpx;
		}
	}
		
		.move-device {
			color: #000;
			font-size: 28rpx;
			margin-top: 30rpx;
			padding-left: 40rpx;
			font-weight: bold;
		}
}
::v-deep .u-drawer-content {
	bottom: 20rpx !important;
	left: 20rpx !important;
	right: 20rpx !important;
	width: initial !important;
	background-color: transparent !important;
}
::v-deep .u-gab {
	background-color: transparent !important;
}
::v-deep .u-action-sheet-item {
	border-radius: 10rpx;
	background: white;
	font-size: 28rpx !important;
	padding: 26rpx 0 !important;
	// &.u-actionsheet-cancel {
		
	// }
}
.empty-body-100 {
	height: calc(100vh - 20rpx);
}
</style>
