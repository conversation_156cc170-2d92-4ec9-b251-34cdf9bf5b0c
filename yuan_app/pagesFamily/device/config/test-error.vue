<template>
	<view class="device-test-dev-page">
		<view class="top-header-line"></view>
		<view>
			<u-row gutter="16" class="block-row">
				<u-col span="12" style="color: #333">
					<view style="text-align: center;">
						<text class="icon iconfont icon-shebeigaojing" style="color: red; font-size: 60rpx;"></text>
						<view style="margin-top: 20rpx;" >未监测到事件</view>
					</view>
				</u-col>
			</u-row>
		</view>
		<view class="footer-btns">
			<!-- <u-button v-if="checked === true" type="primary" shape="circle" class="scan-code-btn" @click="scan">下一步</u-button> -->
			<u-button type="primary" shape="circle" class="next-btn diy-btn" @click="$navTo('pagesFamily/device/config/test-doing', {}, 'redirectTo')">重新测试</u-button>
			<u-button type="primary" shape="circle" class="next-btn diy-btn" plain @click="goBack">返回配置页面</u-button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				doing: false,
			}
		},
		onLoad(option) {
			this.devCode = option.devCode
		},
		methods: {
			goBack() {
				var _pages = getCurrentPages();
				uni.navigateBack({ delta: _pages.length - _pages.findIndex(f => f.route === 'pagesDevice/config/room-config') - 1 })
			}
		}
	}
</script>

<style lang="scss" scoped>
@import url("/static/css/iconfont.css");
	.device-test-dev-page {
		padding: 90px 60px;
		box-sizing: border-box;
		height: 100vh;
		.block-row {
			display: block;
			// #ifdef H5
			display: flex;
			// #endif
			font-size: 32rpx;
			.left-img {
				width: 100rpx;
				height: 100rpx;
			}
			.right-wrap {
				text-align: left;
				.title {
					color: #0D0D0D;
					font-weight: bold;
				}
				.desc {
					margin-top: 12rpx;
					color: #5E5D5D;
					margin-bottom: 30rpx;
				}
				.step-item {
					font-size: 28rpx;
					color: #5E5D5D;
					&:nth-child(n + 2) {
						margin-top: 10rpx;
					}
				}
			}
			&:nth-child(n + 2) {
				margin-top: 90rpx;
			}
		}
		.footer-btns {
			position: absolute;
			bottom: 40rpx;
			left: 40rpx;
			right: 40rpx;
			margin: 0 auto;
			.next-btn {
				display: block;
				margin-top: 20rpx;
			}
		}
	}
</style>