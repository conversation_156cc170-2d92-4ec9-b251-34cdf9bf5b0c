<template>
	<view class="device-step-page">
		<view class="top-header-line"></view>
		<!-- <view>
			<view class="desc" style="margin-bottom: 30rpx;">请停止不动, 保持10秒以上</view>
			<image :src="staticUrl + '/images/device/pic-test-dev.jpg'" style="width: 100%; border-radius: 10rpx;"></image>
		</view> -->
		<web-view v-if="web3dUrl" :webview-styles="webviewStyles" :src="web3dUrl"></web-view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				web3dUrl: '',
				webviewStyles: {
					progress: {
						color: '#FF3333'
					}
				},
				staticUrl: this.$u.http.config.staticBaseUrl,
				timer: undefined,
				outTimer: undefined
			}
		},
		onShow() {
			this.web3dUrl = ''
			this.fetchRealTimeMonitor();
			this.execStartTestDev();
		},
		onUnload() {
			this.removeTimer();
			this.removeOutTimer();
		},
		methods: {
			fetchRealTimeMonitor() {
				this.$u.api.fetchRealTimeMonitorUrl({ devId: uni.getStorageSync('devId') || uni.getStorageSync('testDeviceId') }).then(res => {
					this.web3dUrl = res
				})
			},
			execStartTestDev() {
				this.$u.api.execStartTestDev({ devCode: uni.getStorageSync('devCode') }).then(res => {
					// uni.showLoading({
					// 	title: '测试中...',
					// 	mask: true
					// })
					this.timer = setInterval(() => {
						this.fetchQueryTestOrder();
					}, 1000)
					this.outTimer = setTimeout(() => {
						this.removeTimer();
						this.removeOutTimer();
						uni.hideLoading();
						this.$navTo('pagesFamily/device/config/test-error', {}, 'redirectTo')
					}, 31 * 1000)
				}).catch(err => {
					console.log('err', err)
				})
			},
			fetchQueryTestOrder() {
				this.$u.api.fetchQueryTestOrder({ devCode: uni.getStorageSync('devCode'), needTip: false, removeLoading: false }).then(res => {
					console.log(res);
					if (res === 1) {
						this.removeOutTimer();
						this.removeTimer();
						uni.hideLoading();
						if (uni.getStorageSync('testDeviceId')) {
							uni.removeStorageSync('testDeviceId')
						}
						uni.reLaunch({
							url:'/pagesMy/event/index'
						})
					}
				})
			},
			removeTimer() {
				if (this.timer) {
					clearInterval(this.timer);
					this.timer = undefined;
				}
			},
			removeOutTimer() {
				if (this.outTimer) {
					clearTimeout(this.outTimer)
					this.outTimer = undefined;
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
	.device-step-page {
		padding: 30px;
		box-sizing: border-box;
		height: 100vh;
		.footer-btns {
			position: absolute;
			bottom: 40rpx;
			left: 60rpx;
			right: 60rpx;
			margin: 0 auto;
			.next-btn {
				display: block;
				margin-top: 20rpx;
			}
		}
	}
</style>