<template>
	<view class="device-test-dev-page">
		<view class="top-header-line"></view>
		<view>
			<u-row gutter="16" class="block-row">
				<!-- <u-col span="3" style="color: #0D0D0D">
					<image class="left-img" :src="'../../../static/images/device/icon-wifi-1.png'"></image>
				</u-col> -->
				<u-col span="12" class="right-wrap">
					<view class="title">第一步</view>
					<view class="desc">为了测试准确性, 测试区域请保持一人</view>
				</u-col>
			</u-row>
			<u-row gutter="16" class="block-row">
				<!-- <u-col span="3" style="color: #0D0D0D">
					<image class="left-img" :src="'../../../static/images/device/icon-room-2.png'"></image>
				</u-col> -->
				<u-col span="12" class="right-wrap">
					<view class="title">第二步</view>
					<view class="desc">请在测试区域站立不动, 并保持10秒以上</view>
				</u-col>
			</u-row>
		</view>
		<view class="footer-btns">
			<!-- <u-button v-if="checked === true" type="primary" shape="circle" class="scan-code-btn" @click="scan">下一步</u-button> -->
			<u-button type="primary" shape="circle" class="next-btn diy-btn" @click="$navTo('pagesFamily/device/config/test-doing')">开始测试</u-button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
			}
		},
		methods: {
			
		}
	}
</script>

<style lang="scss" scoped>
	.device-test-dev-page {
		padding: 90px 60px;
		box-sizing: border-box;
		height: 100vh;
		.block-row {
			display: block;
			// #ifdef H5
			display: flex;
			// #endif
			font-size: 32rpx;
			.left-img {
				width: 100rpx;
				height: 100rpx;
			}
			.right-wrap {
				text-align: left;
				.title {
					color: #0D0D0D;
					font-weight: bold;
				}
				.desc {
					margin-top: 12rpx;
					color: #5E5D5D;
					margin-bottom: 30rpx;
				}
				.step-item {
					font-size: 28rpx;
					color: #5E5D5D;
					&:nth-child(n + 2) {
						margin-top: 10rpx;
					}
				}
			}
			&:nth-child(n + 2) {
				margin-top: 90rpx;
			}
		}
		.footer-btns {
			position: absolute;
			bottom: 40rpx;
			left: 40rpx;
			right: 40rpx;
			margin: 0 auto;
			.next-btn {
				display: block;
				margin-top: 20rpx;
			}
		}
	}
</style>