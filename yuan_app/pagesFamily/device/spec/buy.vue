<template>
	<view class="device-spec-buy-page">
		<view class="top-header-line"></view>
		<view class="top-wrap">
			<view class="item-block" style="margin-bottom: 40rpx;">
				<u-row>
					<u-col span="6">
						<view class="left-title" style="margin-bottom: 20rpx;">
							<view class="device-name">{{ vo.serverName ||''}}</view>
						</view>
					</u-col>
					<u-col span="6">
						<view class="right-val">
							{{ vo.serverStatusName ||''}}
						</view>
					</u-col>
				</u-row>
				<u-alert-tips v-if="!allowBuy" type="warning" title="提示" :description="tips"></u-alert-tips>
				<!-- <u-row>
					<u-col span="4">
						<view class="child-left-title">
							<view class="device-name">服务使用状态:</view>
						</view>
					</u-col>
					<u-col span="8">
						<view class="right-val">
							{{ vo.serverStatusName ||''}}
						</view>
					</u-col>
				</u-row> -->
				<u-row v-if="vo.serverStatus !== '0'">
					<u-col span="4">
						<view class="child-left-title">
							<view class="device-name">服务失效时间:</view>
						</view>
					</u-col>
					<u-col span="8">
						<view class="right-val" style="white-space: pre-wrap;">
							{{ vo.serverEndDate || '' }}
						</view>
					</u-col>
				</u-row>
				<u-row>
					<u-col span="12">
						<view class="right-val" style="text-align: left;">
							<!-- {{ vo.serverDesc || '' }} -->
							<rich-text :nodes="vo.serverDesc"></rich-text>
						</view>
					</u-col>
				</u-row>

			</view>
			
			<u-row gutter="20">
				<u-col v-for="(item, index) in vo.priceList" :key="index" span="4">
					<view class="func-item" :class="{ 'actived': buyItemKey === item.billingMethod }" @click="buyItemKey = item.billingMethod">
						<view class="name">{{ item.billingMethodName }}</view>
						<view v-if="item.discountPrice">
							<view class="price">¥ <view class="num">{{ item.discountPrice }}</view></view>
							<view class="discount-rice">¥ {{ item.unitPrice }}</view>
						</view>
						<view v-else class="price">¥ <view class="num">{{ item.unitPrice }}</view></view>
					</view>
				</u-col>
				<!-- <u-col span="4">
					<view class="func-item">短信通知</view>
				</u-col>
				<u-col span="4">
					<view class="func-item">客厅统计</view>
				</u-col>
				<u-col span="4">
					<view class="func-item">语音通知</view>
				</u-col>
				<u-col span="4">
					<view class="func-item">短信通知</view>
				</u-col>
				<u-col span="4">
					<view class="func-item">客厅统计</view>
				</u-col> -->
			</u-row>
		</view>
		
		<view class="footer-pay-wrap">
			<view>支付方式</view>
			<!-- #ifdef (H5 || H5-HEJIA) -->
			<u-row gutter="0" style="display: flex; margin-top: 30rpx; padding-left: 0rpx;"/>
			<!-- #endif -->
			<!-- #ifndef (H5 || H5-HEJIA) -->
			<u-row gutter="0" style="display: block; margin-top: 30rpx; padding-left: 0rpx;">
			<!-- #endif -->
				<u-col span="6" style="padding-left: 0rpx;">
					<view>
						<image style="width: 50rpx; height: 50rpx; vertical-align: middle; margin-right: 10rpx;" mode="widthFix" src="@/static/images/icon-wx-pay.png" />
						微信支付
					</view>
				</u-col>
				<u-col span="6">
					<view class="pay-radio" style="text-align: right;">
						<u-radio-group v-model="wxPayBtn" style="display: inline-block; vertical-align: middle; position: relative; top: -8rpx;" >
							<u-radio active-color="#f59a23" :name="1"></u-radio>
						</u-radio-group>
					</view>
				</u-col>
			</u-row>
			<u-button type="warning" size="medium" style="display: block; margin-top: 20rpx;" @click="handleRetryPay"
				:disabled="!allowBuy"
			>
			<!-- <u-button type="warning" size="medium" style="display: block; margin-top: 20rpx;" open-type="getUserInfo" @getuserinfo="wxGetUserInfo"> -->
				立即以{{ calcPrice }}元{{ vo.serverStatus !== '0' ? '续费' : '开通' }}
			</u-button>
			<view class="agreement" style="margin-top: 20rpx;display:flex;align-items:center;">
				<u-checkbox-group :size="30" style="display: inline-block; vertical-align: middle;">
					<u-checkbox v-model="agreement" active-color="#f59a23"></u-checkbox>
				</u-checkbox-group>
				<text style="font-size: 30rpx;left: -20rpx;">同意</text>
				<view style="display: inline-block; font-size: 30rpx; vertical-align: middle; position:relative;">
					<view style="display: inline-block; color: #0596d8;" @click="$navTo('pagesFamily/device/agreement')">《熵行科技服务协议》</view>
				</view>
				<!-- <u-radio-group v-model="agreement" shape="square" style="display: inline-block; vertical-align: middle;" >
					<u-radio :icon-size="18" :label-size="20" active-color="#f59a23" :name="1">同意<view style="display: inline-block; color: #0596d8;" @click="gotoUrl('/pagesDevice/spec/agreement')">《熵行科技服务协议》</view></u-radio>
				</u-radio-group> -->
			</view>
		</view>
		
		<u-modal v-model="confirmDialog" title="请确认微信支付是否已完成？" :title-style="{ color: '#666' }" :show-confirm-button="false" :show-cancel-button="false">
			<view class="slot-content" style="padding: 20rpx; text-align: center; font-size: 30rpx;">
				<view style="margin-top: 50rpx;"></view>
				<u-gap height="2" bg-color="#e9e9e9"></u-gap>
				<view style="margin-top: 24rpx; margin-bottom: 24rpx; color: #19be6b" @click="handleCheckStatus">已完成支付</view>
				<u-gap height="2" bg-color="#e9e9e9"></u-gap>
				<view style="margin-top: 24rpx; margin-bottom: 20rpx; color: #999;" @click="confirmDialog = false">支付遇到问题, 重新支付</view>
			</view>
		</u-modal>

		<rich-text-modal
			:show="serverAgreement.agreeStep == 1"
			:content="serverAgreement.simpleAgreement"
			@confirm="agreeAgreement(true)"/>
		<member-agreement-popup
			:show="serverAgreement.agreeStep == 2"
			:content="serverAgreement.agreement"
			@confirm="agreeAgreement(true)"
			@cancel="agreeAgreement(false)"/>
		
	</view>
</template>

<script>
	import graceRichText  from '@/utils/richtext.js'
	let systemInfo = uni.getSystemInfoSync();
	export default {
		data() {
			return {
				devId: undefined,
				serverId: undefined,
				buyItemKey: undefined,
				wxPayBtn: 1,
				agreement: false,
				vo: {
					priceList: [],
				},
				payTimer: undefined,
				showLoading: false,
				confirmDialog: false,
				orderId: undefined,
				callbackUrl: undefined,

				allowBuy: true,
				tips: null,
				serverAgreement: {
					agreeStep: 0,
					serverIds:[],
					simpleAgreement: null,
					agreement: null,
				},
				wyfwConst: "wyfw",
			}
		},
		async onLoad(option) {
			this.devId = option.devId;
			this.serverId = option.serverId;
			this.callbackUrl = option.callback;
			this.source = option.source;
			this.serverId = option.serverId;

			if (this.source === this.wyfwConst) {
				if (!this.serverId) {
					this.allowBuy = false;
					return;
				}
				this.fetchServerPrices(this.serverId);
				await this.hasUnpaidOrder(this.serverId);
				if (!this.allowBuy) {
					return
				}
				this.hasOtherLikeServer(this.serverId);
			} else {
				this.fetchDatas(option.payed_redirect);
			}
		},
		onUnload() {
			if (this.payTimer) {
				this.showLoading = false;
				clearTimeout(this.payTimer);  
				this.payTimer = null;  
			}  
		},
		computed: {
			calcPrice() {
				if (!this.vo || !this.vo.priceList || !this.vo.priceList.length) return 0;
				let _filters = this.vo.priceList.filter(f => f.billingMethod === this.buyItemKey)
				if (_filters && _filters.length) {
					return _filters[0].discountPrice || _filters[0].unitPrice
				}
				return 0;
			}
		},
		methods: {
			gotoUrl(url){
				if (!url) return;
				uni.navigateTo({
					url: url
				})
			},
			fetchDatas(orderId) {
				this.$u.api.fetchServerPackageInfo({ devId: this.devId, serverId: this.serverId }).then(res => {
					this.vo = res
					this.vo.serverDesc = graceRichText.format(this.vo.serverDesc);
					if (res && res.priceList && res.priceList.length) {
						this.buyItemKey = res.priceList[0].billingMethod
					}
					
					// #ifdef (H5 || H5-HEJIA)
					if (location.href.indexOf('payed_redirect') != -1) {
						if (orderId) {
							this.confirmDialog = true;
							if (orderId.indexOf(',') != -1) {
								orderId = orderId.split(',')[0];
							}
							this.orderId = orderId;
						}
					}
					// #endif
				})
			},
			async fetchServerPrices(serverId) {
				try {
					const res = await this.$u.api.getServerPrices({ serverId });
					if (!res.priceList.length) {
						this.allowBuy = false;
						return;
					}
					this.vo = res;
					this.vo.serverDesc = graceRichText.format(this.vo.serverDesc);
					this.buyItemKey = res.priceList[0].billingMethod;
				} catch (err) {
					this.allowBuy = false;
					console.error(err);
				}
			},
			/* 有未支付的订单 */
			async hasUnpaidOrder(serverId) {
				try {
					const res = await this.$u.api.countUnpaidOrder({ serverId });
					if (res > 0) {
						this.allowBuy = false;
						this.tips = "您还有未支付的订单，请前往支付订单或取消订单";
						// uni.showToast({
						// 	duration: 2000,
						// 	title: '您还有未支付的订单，请前往支付订单或取消订单',
						// 	icon: 'none'
						// })
					}
				} catch (err) {
					this.allowBuy = false;
					console.error(err);
				}
			},
			/* 有其它类似服务 */
			async hasOtherLikeServer(serverId) {
				const has = false;
				if (has) {
					this.allowBuy = false;
					this.tips = "您已经有第三方机构救援的服务，不能购买该服务";
					// uni.showToast({
					// 	duration: 2000,
					// 	title: '您已经有第三方机构救援的服务，不能购买该服务',
					// 	icon: 'none'
					// })
				}
				return has;
			},
			async checkAgreement() {
				if (!this.agreement) {
					uni.showToast({
						duration: 2000,
						title: '请先阅读并同意《熵行科技服务协议》',
						icon: 'none'
					})
					return;
				}
				if (!this.buyItemKey) {
					uni.showToast({
						duration: 2000,
						title: '请先选择',
						icon: 'none'
					})
					return;
				}

				try {
					this.serverAgreement = await this.$u.api.listByServerIdAgreement(this.serverId);
					if (this.serverAgreement.serverIds.length) {
						this.serverAgreement.agreeStep = 1;
						return;
					}
				} catch (err) {
					console.error(err);
					return;
				}

				this.handleFetchUserProfile();
			},
			async handleFetchUserProfile() {
				//aladding
				if (this.source === this.wyfwConst) {
					this.toFillOrder();
				} else {
					const nickName = await this.getNickName();
					this.handleSubmitOrder(nickName);
				}
			},
			handleSubmitOrder(nickName) {
				let _filters = this.vo.priceList.filter(f => f.billingMethod === this.buyItemKey)
				this.$u.api.execSaveOrder({ 
					devId: this.devId,
					serverId: this.serverId,
					billingMethod: this.buyItemKey,
					needPayPrice: this.calcPrice,
					unitPrice: _filters[0].unitPrice,
					nickName: nickName
				}).then(res => {
					this.execPrepay(res);
					// this.execPay(res.data, res.map.sn);
				}).catch(err => {
					uni.showToast({
						duration: 2000,
						title: err.data.msg || '创建订单失败',
						icon: 'none'
					})
				})
			},
			handleRetryPay() {
				
				// #ifdef (H5 || H5-HEJIA)
				if (this.orderId) {
					this.$u.api.fetchOrderDetail({ id: this.orderId, needTip: false }).then(res => {
						if (res && res.payStatus === '1') {
							this.execPrepay(this.orderId);
						} else {
							uni.showToast({
								duration: 2000,
								title: `该订单已${ res.payStatus === '2' ? '已支付' : '' }${ res.payStatus === '3' ? '已取消' : '' }`,
								icon: 'none'
							})
						}
					}).catch(err => { })
				} else {
					this.checkAgreement();
				}
				// #endif
				// #ifndef (H5 || H5-HEJIA)
				if (this.allowBuy) {
					this.checkAgreement();
				}
				// #endif
			},
			execPrepay(orderId) {
				if (!orderId) return;
				let _api = this.$u.api.execPrepay;
				// #ifdef (H5 || H5-HEJIA)
				_api = this.$u.api.execH5Prepay;
				// #endif
				let that = this;
				_api({
					orderId: orderId
				}).then(res => {
					// #ifdef (H5 || H5-HEJIA)
					let _href = res.mwebUrl;
					if (_href) {
						let _arrs = _href.split('&');
						if (_href.indexOf('&http') != -1 || _href.indexOf('redirect_uri') != -1 || _href.indexOf('redirect_url') != -1) {
							_arrs.pop();
						}
						// if (location.href.indexOf('paymode1') != -1) {
						if (systemInfo.platform == 'ios') {
							_href = _arrs.join('&') + '&redirect_url=' + encodeURIComponent(`cmcc.rfcare.cn://${ location.href }&payed_redirect=${ orderId }`)
						} else {
							_href = _arrs.join('&') + '&redirect_url=' + encodeURIComponent(`${ location.href }&payed_redirect=${ orderId }`)
						}
						console.log('to platform: ' + systemInfo.platform)
						if (systemInfo.platform == 'ios') {
							if (orderId && `${orderId}`.indexOf(',') != -1) {
								orderId = `${orderId}`.split(',')[0];
							}
							this.orderId = orderId;
							this.confirmDialog = true;
							// location.replace(_href)
							// location.href = _href
							const iframe = document.createElement('iframe')
							iframe.style.display ='none'
							iframe.setAttribute('src', _href)
							iframe.setAttribute('sandbox','allow-top-navigation allow-scripts')
							document.body.appendChild(iframe)
							console.log('append iframe');
						} else {
							location.href = _href
						}
					} else {
						uni.showToast({
							duration: 2000,
							title: '未获取到回调地址',
							icon: 'none'
						})
					}
					// #endif
					// #ifndef (H5 || H5-HEJIA)
					this.execPay(orderId, res);
					// #endif
				}).catch(err => {
					console.log('err', err);
					this.allowBuy = false;
					uni.showToast({
						duration: 2000,
						title: err.data.msg || '获取预下单失败',
						icon: 'none'
					})
				})
				
			},
			execPay(orderId, reqParams) {
				if (!reqParams.paySign) {
					uni.showToast({
						duration: 2000,
						title: '缺少支付必要条件',
						icon: 'none'
					})
					return;
				}
				let that = this;
				wx.requestPayment({
					timeStamp: reqParams.timeStamp,
					nonceStr: reqParams.nonceStr,
					package: reqParams.package_,
					signType: reqParams.signType,
					paySign: reqParams.paySign,
					success(res) {
						uni.showLoading({
							mask: true,
							title: '支付中...'
						})
						that.payTimer = setInterval(() => {
							that.fetchPayStatus(orderId)
						}, 2500)
					},
					fail(res) {
						that.allowBuy = false;
						that.tips = "可以在“我的-订单”完成支付";
						// uni.showToast({
						// 	duration: 2000,
						// 	title: '可以在“我的-订单”完成支付',
						// 	icon: 'none'
						// })
						console.log(res)
					}
				})
			},
			handleCheckStatus() {
				if (!this.orderId) return;
				uni.showLoading({
					mask: true,
					title: '查询中...'
				})
				this.$u.api.fetchOrderDetail({ id: this.orderId, needTip: false }).then(res => {
					uni.hideLoading();
					if (res) {
						if (res.payStatus === '1' || res.payStatus === 1) {
							uni.showToast({
								duration: 2000,
								title: '该订单未支付',
								icon: 'none'
							})
						} else if (res.payStatus === '3' || res.payStatus === 3) {
							uni.showToast({
								duration: 2000,
								title: '该订单已取消',
								icon: 'none'
							})
							this.confirmDialog = false;
						} else if (res.payStatus === '2' || res.payStatus === 2) {
							if (this.callbackUrl) {
								setTimeout(() => {
									location.replace(this.callbackUrl + '&payed=true')
								}, 10);
							} else {
								uni.showToast({
									duration: 2000,
									title: '支付成功',
									icon: 'none'
								})
								this.confirmDialog = false;
								this.fetchDatas()
							}
						}
					} else {
						uni.showToast({
							duration: 2000,
							title: '未成功支付',
							icon: 'none'
						})
					}
				}).catch(err => { })
			},
			fetchPayStatus(orderId) {
				if (!orderId) return;
				
				// #ifdef (H5 || H5-HEJIA)
					// this.$u.api.fetchOrderDetail({ id: orderId, needTip: false }).then(res => {
					// 	if (res && (res.payStatus === '2' || res.payStatus === 2)) {
					// 		uni.hideLoading();
					// 		setTimeout(() => {
					// 			location.replace(this.callbackUrl + '&payed=true')
					// 		}, 10);
					// 	}
					// }).catch(err => { })
				// #endif
				// #ifndef (H5 || H5-HEJIA)
					this.$u.api.fetchPayStatus({
						id: orderId, needTip: false
					}).then(res => {
						if (res.tradeState === 'SUCCESS' || res.tradeState === 'CLOSED' || res.tradeState === 'PAYERROR') {
							uni.hideLoading();
							if (this.payTimer) {
								clearTimeout(this.payTimer);
								this.payTimer = null;
							}
							uni.redirectTo({
								url: `/pagesMy/order/index`
							})
							// 备案会员改到后端
							// if (this.source == this.wyfwConst) {
							// 	uni.showLoading({
							// 		title: '加载中...',
							// 		mask: true
							// 	})
								//备案会员
								// this.$u.api.addAccountByOrderWorryfree({ orderId })
								// 	.then(res => {
								// 		// uni.redirectTo({
								// 		// 	url: `/pagesFamily/member-server/pay-finish?orderId=${orderId}`
								// 		// })
								// 		uni.redirectTo({
								// 			url: `/pagesMy/order/index`
								// 		})
								// 	})
								// 	.catch(res => {
								// 		console.log(res);
								// 		uni.hideLoading();
								// 		uni.showModal({
								// 			title: '提示',
								// 			content: '支付成功，但会员备案失败',
								// 			showCancel: false,
								// 			success: ({ confirm, cancel }) => {
								// 				// uni.redirectTo({
								// 				// 	url: `/pagesFamily/member-server/pay-finish?orderId=${orderId}`
								// 				// })
								// 				uni.redirectTo({
								// 					url: `/pagesMy/order/index`
								// 				})
								// 			}
								// 		})
								// 	});
							// 	return;
							// }
							uni.navigateBack({
								delta: 1
							})
						}
					}).catch(err => {
						this.allowBuy = false;
						if (this.payTimer) {
							clearTimeout(this.payTimer);
							this.payTimer = null;
						}
					})
				// #endif
			},
			async agreeAgreement(agree) {
				if (agree === false) {
					this.serverAgreement.agreeStep = 0;
					return;
				}
				this.serverAgreement.agreeStep++;
				if (this.serverAgreement.agreeStep == 3) {
					try {
						await this.$u.api.agreeByServerAgreement(this.serverAgreement.serverIds, this.devId);
					} catch (err) {
						console.error(err);
						return;
					} finally {
						this.serverAgreement.agreeStep = 0;
					}
					this.handleFetchUserProfile()
				}
			},
			toFillOrder() {
				const { buyItemKey: billingMethod, serverId } = this;
				if (!serverId || !billingMethod) {
					uni.showToast({
						title: '参数无效，无法购买',
						icon: 'error',
						duration: 2000
					});
					return;
				}
				const that = this;
				const params = this.$u.queryParams({ billingMethod, serverId });
				//aladding 跳转到订单输入页面；这里不保存同意的协议，在完成定单时一起保存
				const url = `/pagesFamily/member-server/order-form${params}`;
				console.log(url)
				uni.navigateTo({
					url,
					events: {
						complete: function ({ success, orderId }) {
							if (success) {
								that.execPrepay(orderId);
							}
							this.allowBuy = false;
						},
					}
				})
			},
			async getNickName() {
				let _member = uni.getStorageSync('member');
				// #ifdef (H5 || H5-HEJIA)
				if (_member) {
					let _name = _member.nickname || _member.name;
					if (!_name) {
						_name = _member.phone
					}
					return _name;
				} else {
					uni.showToast({
						title: '支付失败, 无法获取用户参数',
						icon: 'error',
						duration: 2000
					});
					throw '支付失败, 无法获取用户参数';
				}
				// #endif
				// #ifndef (H5 || H5-HEJIA)
				if (_member && (_member.nickname || _member.name)) {
					return _member.nickname || _member.name;
				} else {
					return new Promise((resove, reject) => {
						wx.getUserProfile({
							desc: '用于完善订单信息',
							success: (res) => {
								_member.nickname = res.userInfo.nickName
								uni.setStorageSync('member', _member);
								resove(res.userInfo.nickName);
							},
							fail: res => {
								uni.showToast({
									title: '您拒绝了请求, 无法完善相应信息',
									icon: 'error',
									duration: 2000
								});
								reject('您拒绝了请求, 无法完善相应信息');
							}
						})
					});
				}
				// #endif
			}
		}
	}
</script>

<style lang="scss">
page {
	background: #f7f7f7;
	height: 100vh;
	position: relative;
	//#ifdef (H5 || H5-HEJIA)
	height: 100%;
	//#endif
}
</style>
<style lang="scss" scoped>
.device-spec-buy-page {
	height: 100vh;
	
	//#ifdef (H5 || H5-HEJIA)
	height: 100%;
	//#endif
	// padding-bottom: 190rpx;
	display: flex;
	flex-direction: column;
	overflow: hidden;
	.top-wrap {
		padding: 29rpx;
		flex: 1;
		overflow-y: auto;
		.top-title {
			font-size: 36rpx;
			color: #333;
			margin-top: 40rpx;
			margin-bottom: 24rpx;
		}
		.item-block {
			padding: 24rpx 30rpx;
			color: $u-content-color;
			background: white;
			font-size: 28rpx;
			box-shadow: 0rpx 8rpx 24rpx 0rpx rgba(0, 0, 0, 0.1);
			border-radius: 16rpx;
			//#ifdef (H5 || H5-HEJIA)
			line-height: 50rpx;
			//#endif
			.left-title {
				.device-name {
					color: #0D0D0D;
					font-size: 32rpx;
				}
			}
			.child-left-title {
				.device-name {
					color: #666;
					font-size: 26rpx;
				}
			}
			.right-val {
				color: #808080;
				text-align: right;
				font-size: 26rpx;
				.activate-now {
					padding: 10rpx 28rpx;
					background: #EDEFF9;
					border-radius: 45rpx;
					color: #4166F5;
					font-size: 26rpx;
					line-height: initial;
					text-align: center;
					margin-top: 10rpx;
					display: inline-block;
				}
				::v-deep .u-icon {
					margin-left: 10rpx;
					position: relative;
					top: -2rpx;
				}
			}
			&:nth-child(n + 2) {
				margin-top: 20rpx;
			}
		}
		u-row {
			line-height: 60rpx;
		}
		.footer-btns {
			position: fixed;
			bottom: 40rpx;
			left: 60rpx;
			right: 60rpx;
			margin: 0 auto;
			.next-btn {
				display: block;
				margin-top: 20rpx;
			}
		}
		.func-item {
			border: 4rpx solid #e5e5e5;
			border-radius: 10rpx;
			background: white;
			// padding: 0rpx;
			padding-top: 20rpx;
			padding-bottom: 30rpx;
			margin-bottom: 20rpx;
			// padding-left: 90rpx;
			text-align: center;
			position: relative;
			.name {
				font-size: 26rpx;
			}
			.price {
				font-size: 26rpx;
				color: #f59a23;
				margin-top: 10rpx;
				.num {
					display: inline-block;
					font-weight: bold;
					font-size: 42rpx;
					margin-left: 10rpx;
				}
			}
			.discount-rice {
				font-size: 26rpx;
				text-decoration: line-through;
				color: #666;
				line-height: 28rpx;
			}
			&.actived {
				border: 4rpx solid #f59a23;
			}
		}
	}
	
	
	.footer-pay-wrap {
		height: 360rpx;
		// left: 0rpx;
		// right: 0rpx;
		// bottom: 0rpx;
		background: white;
		border-top: 1rpx solid #e2e2e2;
		padding: 30rpx;
		box-shadow: 0rpx 33rpx 54rpx 0rpx rgba(0, 0, 0, 0.1);
	}
	
	// //#ifdef (H5 || H5-HEJIA)
	// .footer-pay-wrap {
	// 	position: fixed;
	// 	bottom: 0rpx;
	// 	left: 0rpx;
	// 	right: 0rpx;
	// 	padding-bottom: 20rpx;
	// }
	// //#endif
	
	u-button {
		::v-deep button {
			width: 100%;
		}
	}
	.pay-radio {
		::v-deep .u-radio__label {
			display: none;
		}
	}
	
	.agreement {
		::v-deep .u-radio__icon-wrap {
			width: 24rpx !important;
			height: 24rpx !important;
		}
	}
}
</style>
