<template>
	<view class="device-spec-page">
		<view class="top-header-line"></view>
		<view class="item-block" style="min-height: 220rpx;">
			<view class="title">{{ modelName }}</view>
			<view v-if="activeStatus === '1'">
				<view class="status">{{ serverStatusName === 'null' ? '-' : serverStatusName }}</view>
				<view v-if="serverEndDate !== 'null'">{{ serverEndDate }} 到期</view>
			</view>
			<image class="right-img" mode="widthFix" src="data:image/svg+xml;base64,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"></image>
		</view>
		
		<u-row v-if="activeStatus === '1'" gutter="20">
			<u-col v-for="(item, index) in datas" span="4">
				<view class="func-item" :style="{ paddingLeft: item.serverIcon ? '82rpx' : '30rpx' }" @click="gotoUrl(`/pagesFamily/device/spec/buy?devId=${devId}&serverId=${item.serverId}`)">
					<view v-if="item.serverStatus === '2'" class="invalid-tip">已过期</view>
					<image v-if="item.serverIcon" :src="item.serverIcon" mode="widthFix" />
					<view>{{ item.serverName }}</view>
					<view v-if="item.serverStatus !== '2'" class="desc">{{ item.serverStatusName }}</view>
				</view>
			</u-col>
			<!-- <u-col span="4">
				<view class="func-item">短信通知</view>
			</u-col>
			<u-col span="4">
				<view class="func-item">客厅统计</view>
			</u-col>
			<u-col span="4">
				<view class="func-item">语音通知</view>
			</u-col>
			<u-col span="4">
				<view class="func-item">短信通知</view>
			</u-col>
			<u-col span="4">
				<view class="func-item">客厅统计</view>
			</u-col> -->
		</u-row>
		<u-popup v-model="popupShow" mode="bottom" :border-radius="20" @close="handlePopupClose">
			<view style="text-align: center; padding: 70rpx;">
				<view style="display: inline-block; width: 180rpx; height: 180rpx; border-radius: 100%; background-color: #38cd82; padding-top: 50rpx;">
					<u-icon name="checkmark" color="white" size="90"></u-icon>
				</view>
				<view style="margin-top: 50rpx; font-size: 38rpx; color: #666;">支付成功</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				devId: undefined,
				devName: undefined,
				modelName: undefined,
				serverStatus: undefined,
				serverStatusName: undefined,
				serverEndDate: undefined,
				activeStatus: undefined,
				datas: [],
				popupShow: false,
				topBackChange: true,
			}
		},
		onLoad(option) {
			this.devId = option.devId;
			this.devName = option.devName;
			this.modelName = option.modelName;
			this.serverStatus = option.serverStatus;
			this.serverStatusName = option.serverStatusName;
			this.serverEndDate = option.serverEndDate;
			this.activeStatus = option.activeStatus;
			uni.setNavigationBarTitle({
				title: option.devName
			});
			if (option.payed === 'true' || option.payed === true) {
				this.popupShow = true;
				this.topBackChange = true
			}
		},
		onShow() {
			this.fetchDatas();
		},
		methods: {
			fetchDatas() {
				this.$u.api.fetchZengzhiServerList({ devId: this.devId }).then(res => {
					this.datas = res
				})
			},
			handlePopupClose() {
				this.popupShow = false;
			},
			// onBackPress(e) {
			// 	console.log('e', e);
			// 	if (e.from === 'backbutton') {
			// 		console.log(e);
			// 		if (this.topBackChange === true) {
			// 			uni.navigateBack({
			// 				delta: 3
			// 			});
			// 			return true
			// 		}
			// 	}
			// },
			gotoUrl(url){
				if (!url) return;
				// #ifdef MP-WEIXIN
				uni.navigateTo({
					url: url
				})
				// #endif
				// #ifdef (H5 || H5-HEJIA)
				// let _member = uni.getStorageSync('member');
				// if (_member && _member.phone === '15011905011') {
					uni.navigateTo({
						url: url + '&callback=' + encodeURIComponent(location.href)
					})
				// }
				// #endif
			},
		}
	}
</script>

<style lang="scss" scoped>
.device-spec-page {
	padding: 29rpx;
	padding-bottom: 190rpx;
	.item-block {
		padding: 60rpx 30rpx;
		color: $u-content-color;
		font-size: 28rpx;
		box-shadow: 0rpx 8rpx 24rpx 0rpx rgba(0, 0, 0, 0.1);
		border-radius: 16rpx;
		position: relative;
		margin-bottom: 30rpx;
		.title {
			font-size: 30rpx;
			font-weight: bold;
		}
		.status {
			margin: 20rpx 0rpx 4rpx 0rpx;
		}
		.right-img {
			position: absolute;
			top: 20rpx;
			right: 30rpx;
			width: 220rpx;
		}
	}
	
	.func-item {
		background: #6fb0ff;
		height: 102rpx;
		border-radius: 10rpx;
		color: white;
		padding: 18rpx 0rpx 0rpx 0rpx;
		margin-bottom: 20rpx;
		padding-left: 78rpx;
		position: relative;
		.name {
			font-size: 20rpx;
		}
		.desc {
			font-size: 20rpx;
			color: #fcfff4;
		}
		.invalid-tip {
			display: inline-block;
			position: absolute;
			top: -18rpx;
			right: -14rpx;
			background: red;
			border-radius: 20rpx;
			border: 4rpx solid #fff;
			padding: 6rpx 17rpx;
			font-size: 18rpx;
		}
		image {
			width: 50rpx;
			position: absolute;
			left: 20rpx;
			top: 26rpx;
			border-radius: 10rpx;
		}
		// &.actived {
		// 	background: #4590ec;
		// }
	}
}
</style>
