<template>
	<view class="family-device-move-page">
		<view class="top-header-line"></view>
		<u-empty v-if="!familys || !familys.length" text="暂无其他可转入家庭" mode="list"></u-empty>
		<view v-else>
			<view class="item-block">
				<view class="title">将 {{ (device.devName || device.devCode) || '' }}({{ device.devCode || '' }}) 转移至</view>
				<view class="family-list">
					<view v-for="(family, index) in familys" :key="index" class="family-item" @click="handleFamilyClick(family)">
						<text v-if="familySelectRdo === family.id" class="icon iconfont icon-xuanzhong radio" style="color: #01B09A"></text>
						<text v-else class="icon iconfont icon-weixuanzhong radio"></text>
						<text>{{ family.name }}</text>
					</view>
				</view>
			</view>
			
			<view class="footer-btns">
				<u-button shape="circle" class="next-btn diy-btn" size="medium" :loading="submitLoading"
				:custom-style="{ 'width': '100%', 'color': 'white', 'background-color': '#01B09A' }"  hover-class="none"
				@click="handleMove">转移</u-button>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				familySelectRdo: undefined,
				familys: [],
				device: [],
				currFamilyId: undefined,
				currDevId: undefined,
				submitLoading: false,
			}
		},
		onLoad(option) {
			this.currFamilyId = option.familyId;
			this.currDevId = option.devId;
			this.fetchDetail(option.devId);
			this.fetchFamilys();
		},
		methods: {
			handleFamilyClick(family) {
				this.familySelectRdo = this.familySelectRdo === family.id ? undefined : family.id
			},
			fetchDetail(id) {
				this.$u.api.fetchMyDeviceInfo({ devId: id }).then(res => {
					this.device = res || {};
				})
			},
			fetchFamilys() {
				this.$u.api.fetchFamilyList({ }).then(res => {
					this.familys = (res || []).filter(f => `${f.id}` !== this.currFamilyId && f.selfCreate === true)
				}).catch(err => {
					uni.showToast({ duration: 2000, title: err.message || '发生未知错误', icon: 'none' })
				})
			},
			handleMove() {
				if (!this.currFamilyId || !this.currDevId) {
					this.$toast('参数有误, 无法转移')
					return;
				}
				if (!this.familySelectRdo) {
					this.$toast('请选择家庭')
					return;
				}
				uni.showLoading({
					title: '保存中...',
					mask: true
				})
				this.submitLoading = true;
				this.$u.api.execTransferFamilyDev({ fromFamilyId: this.currFamilyId, devId: this.currDevId, toFamilyId: this.familySelectRdo }).then(res => {
					this.$toast('设备转移成功')
					setTimeout(() => {
						this.submitLoading = false;
						uni.reLaunch({
							url: '/pages/index/index'
						})
					}, 500);
				}).catch(err => {
					this.submitLoading = false;
					console.log('err', err)
					this.$toast(err.message || '发生未知错误')
				})
			}
		}
	}
</script>

<style lang="scss">
page {
	background-color: #f7f7f7;
}
</style>

<style lang="scss" scoped>
@import url("/static/css/iconfont.css");
.family-device-move-page {
	padding: 20rpx 40rpx;
	.item-block {
		// height: 784rpx;
		// background: white;
		// padding: 24rpx 30rpx;
		// color: $u-content-color;
		font-size: 28rpx;
		margin-top: 30rpx;
		.title {
			background: white;
			padding: 20rpx 20rpx;
			border-radius: 10rpx;
			margin-bottom: 20rpx;
			font-size: 28rpx;
		}
		.family-list {
			line-height: 44rpx;
			.family-item {
				.radio {
					margin-right: 10rpx;
					font-size: 36rpx;
					vertical-align: middle;
				}
				&:nth-child(n + 2) {
					margin-top: 10rpx;
				}
			}
		}
		// box-shadow: 0rpx 8rpx 24rpx 0rpx rgba(0, 0, 0, 0.1);
		// border-radius: 16rpx;
		// padding-top: 0rpx;
	}
	.footer-btns {
		position: absolute;
		bottom: 40rpx;
		left: 40rpx;
		right: 40rpx;
		margin: 0 auto;
		.next-btn {
			display: block;
			margin-top: 20rpx;
		}
	}
}
::v-deep .u-empty {
	height: calc(100vh - 40rpx) !important;
}
</style>
