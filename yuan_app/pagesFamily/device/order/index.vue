<template>
	<view class="page-wrap">
		<view class="view-wrap" style="background-color: #01B09A; color:#ffffff;padding: 30rpx 20rpx;">
			<view class="title">
				产品型号: {{ device.productModelName || '' }}
			</view>
			<view class="label">
				{{ device.serverStatusName || '' }}&nbsp;&nbsp;&nbsp;&nbsp;
				{{ device.serverEndDate ? `${device.serverEndDate}&nbsp;&nbsp;&nbsp;&nbsp;到期` : '' }}
			</view>
		</view>
		<view class="view-wrap">
			<view class="title">
				基础服务
			</view>
			<view class="server-wrap">
				<view v-for="(item, index) in baseServerList" :key="index" class="base-server">
					<view @click="switchBaseServer(index)" class="item" :class="{ selected: baseServerIndex == index }">{{
							item.serverName
					}}</view>
				</view>
				<view v-if="baseServerList.length == 0">没有可用服务</view>
			</view>
		</view>

		<scroll-view class="view-wrap" scroll-y style="height:350rpx;padding-top: 10rpx;padding-bottom: 10rpx;">
			<rich-text :nodes="contentDesc"></rich-text>
		</scroll-view>

		<view class="price-wrap">
			<!-- <view v-if="productPriceList.length === 0" class="item">
				<view class="title">产品未定价</view>
			</view> -->
			<view v-for="(item, index) in productPriceList" :key="index" class="item" @click="switchPrice(index)"
				:class="{ selected: selectedPriceIndex == index }">
				<view class="title">{{ item.billingMethodName }}</view>
				<view class="price">
					<text style="font-size: 32rpx">￥</text>
					&nbsp; {{ item.discountPrice || item.unitPrice }}
				</view>
				<view v-if="item.discountPrice" class="strikethrough">
					<text style="font-size: 32rpx">￥</text>
					&nbsp; {{ item.unitPrice }}
				</view>
				<view v-else class="empty"></view>
			</view>
		</view>

		<view class="view-wrap" style="padding-bottom: 30rpx;">
			<view class="title">支付方式</view>
			<view class="r-flex">
				<view style="flex:1;">
					<image style="width: 100rpx; height: 100rpx; vertical-align: middle; margin-right: 10rpx;" mode="widthFix"
						src="@/static/images/icon-wx-pay2.png" />
					微信支付
				</view>
				<view class="pay-radio" style="text-align: right;">
					<u-radio-group v-model="wxPay"
						style="display: inline-block; vertical-align: middle; position: relative; top: -8rpx;">
						<u-radio active-color="#01B09A" :name="1"></u-radio>
					</u-radio-group>
				</view>
			</view>

			<view style="padding: 10rpx 0;">
				<u-button :custom-style="{ 'color': '#ffffff', 'background-color': '#01B09A' }" class="diy-btn" shape="circle"
					hover-class="none" @click="checkSubmit">
					{{ payBtnTxt }}
				</u-button>
			</view>

			<view class="r-flex">
				<u-checkbox-group :size="30" style="display: inline-block; vertical-align: middle;">
					<u-checkbox v-model="agreement" active-color="#01B09A"></u-checkbox>
				</u-checkbox-group>
				<text style="font-size: 30rpx;left: -20rpx;">同意</text>
				<view style="font-size: 30rpx;display: inline-block; color: #01B09A;" @click="$navTo('pagesFamily/device/agreement')">
					《熵行科技服务协议》</view>
			</view>
		</view>

		<view class="view-wrap">
			<view>
				<text style="color: #ad742d; margin-right: 20rpx; display: inline-block; font-size: 28rpx;">增值服务</text>
				<text style="font-size: 26rpx;">{{ addServerList.length ? "增值服务请点击购买" : "无可用" }}</text>
			</view>
			<view class="server-wrap">
				<view v-for="(item, index) in addServerList" :key="index" class="extend-server">
					<view @click="switchAddServer(index)" class="item" :class="{ selected: addServerIndex == index }">
						<div :class="[`status-${item.serverStatus}`]">
							{{ item.serverStatusName || "" }}
						</div>
						{{ item.serverName || "" }}
						<div class="desc" v-if="item.serverStatus == '1'">{{ item.serverEndDate ? `${item.serverEndDate} 到期` : '' }}
						</div>
					</view>
				</view>
			</view>
		</view>
		
		<rich-text-modal :show="serverAgreement.agreeStep == 1" :content="serverAgreement.simpleAgreement"
			@confirm="agreeAgreement(true)" />
		<member-agreement-popup :show="serverAgreement.agreeStep == 2" :content="serverAgreement.agreement"
			@confirm="agreeAgreement(true)" @cancel="agreeAgreement(false)" />

	</view>
</template>

<script>
let systemInfo = uni.getSystemInfoSync();
export default {
	data() {
		return {
			devId: null,
			device: {},

			baseServerList: [],
			baseServer: null,
			baseServerIndex: -1,

			addServerList: [],
			addServer: null,
			addServerIndex: -1,

			productPriceList: [],
			selectedPrice: null,
			selectedPriceIndex: -1,
		
			agreement: false,
			serverAgreement: {
				agreeStep: 0,
				serverIds: [],
				simpleAgreement: null,
				agreement: null,
			},
			showLoading: false,
			confirmDialog: false,
			orderId: undefined,
			payTimer: undefined,
			wxPay: 1,
		}
	},

	computed: {
		contentDesc() {
			const { device, baseServer } = this;
			if (baseServer) {
				return baseServer.serverDesc || "";
			} else {
				return device?.productModelDesc || "";
			}
		},
		calcPrice() {
			const { selectedPrice } = this;
			if (!selectedPrice) {
				return 0;
			}
			return selectedPrice.discountPrice ?? selectedPrice.unitPrice;
		},
		payBtnTxt() {
			const { selectedPrice, calcPrice } = this;
			if (!selectedPrice) {
				return "请选择购买方式";
			}
			return `立即以 ${calcPrice} 元续费`;
		}
	},

	async onLoad(option) {
		if (!option.devId) {
			uni.showToast({ duration: 1000, title: '设备ID无效', icon: 'none' })
			setTimeout(uni.navigateBack, 1000)
			return;
		}
		this.devId = option.devId;

		this.fetchProductPackage();
		// this.fetchAddServers();
	},
	onUnload() {
		if (this.payTimer) {
			this.showLoading = false;
			clearTimeout(this.payTimer);
			this.payTimer = null;
		}
	},
	onShow() {
		this.fetchAddServers();
	},
	methods: {
		async fetchProductPackage() {
			const { devId } = this;
			try {
				let res = await this.$u.api.fetchProductPackageDetail({ devId });
				const { priceList, baseServerList, ...device } = res;

				this.device = device;
				this.baseServerList = baseServerList;
				this.productPriceList = priceList;

				this.switchPrice();
			} catch (err) {
				console.error(err);
			}
		},
		async fetchAddServers() {
			this.addServerList = await this.$u.api.listAddServerDevice({ devId: this.devId }) || [];
		},
		switchBaseServer(index) {
			return;
			//不需要
			// const { baseServerList } = this;
			// if (this.baseServerIndex === index) {
			// 	this.baseServer = null;
			// 	this.baseServerIndex = -1;
			// } else {
			// 	this.baseServerIndex = index;
			// 	this.baseServer = baseServerList[index];
			// }
		},
		async switchAddServer(index = 0) {
			const server = this.addServerList[index];
			this.$navTo(`pagesFamily/device/spec/buy?devId=${this.devId}&serverId=${server.serverId}`);
		},
		switchPrice(index = 0) {
			this.selectedPriceIndex = index;
			this.selectedPrice = this.productPriceList[index];
		},
		async checkSubmit() {
			const { baseServerList } = this;
			if (baseServerList.length == 0) {
				uni.showModal({
					content: "该产品型号的服务存在禁用情况，请联系管理员，感谢您的支持",
					showCancel: false,
					confirmColor: "#01B09A"
				})
				return;
			}
			let nickName;
			try {
				nickName = await this.getNickName();
			} catch (err) {
				uni.showToast({ title: err, icon: 'error', duration: 2000 });
				return;
			}
			if (this.selectedPriceIndex < 0) {
				uni.showToast({ duration: 2000, title: '请先选择购买方式', icon: 'none' });
				return;
			}

			if (!this.agreement) {
				uni.showToast({ duration: 2000, title: '请先阅读并同意《熵行科技服务协议》', icon: 'none' });
				return;
			}

			try {
				
				this.serverAgreement = await this.$u.api.listByDevModelAgreement(this.devId);
				if (this.serverAgreement.serverIds.length) {
					this.serverAgreement.agreeStep = 1;
					return;
				}
			} catch (err) {
				console.error(err);
			}
			this.handleSubmitOrder(nickName);
		},
		handleSubmitOrder(nickName) {
			const { devId, selectedPrice, calcPrice } = this;
			this.$u.api.addProductOrder({
				devId: devId,
				billingMethod: selectedPrice.billingMethod,
				needPayPrice: calcPrice,
				unitPrice: selectedPrice.unitPrice,
				nickName: nickName
			}).then(res => {
				this.execPrepay(res);
			}).catch(err => {
				uni.showToast({
					duration: 2000,
					title: err.data.msg || '创建订单失败',
					icon: 'none'
				})
			})
		},
		execPrepay(orderId) {
			if (!orderId) return;
			let _api = this.$u.api.execPrepay;
			// #ifdef (H5 || H5-HEJIA)
			_api = this.$u.api.execH5Prepay;
			// #endif
			_api({
				orderId: orderId
			}).then(res => {
				// #ifdef (H5 || H5-HEJIA)
				let _href = res.mwebUrl;
				if (_href) {
					let _arrs = _href.split('&');
					if (_href.indexOf('&http') != -1 || _href.indexOf('redirect_uri') != -1 || _href.indexOf('redirect_url') != -1) {
						_arrs.pop();
					}
					// if (location.href.indexOf('paymode1') != -1) {
					if (systemInfo.platform == 'ios') {
						_href = _arrs.join('&') + '&redirect_url=' + encodeURIComponent(`cmcc.rfcare.cn://${location.href}&payed_redirect=${orderId}`)
					} else {
						_href = _arrs.join('&') + '&redirect_url=' + encodeURIComponent(`${location.href}&payed_redirect=${orderId}`)
					}
					console.log('to platform: ' + systemInfo.platform)
					if (systemInfo.platform == 'ios') {
						if (orderId && `${orderId}`.indexOf(',') != -1) {
							orderId = `${orderId}`.split(',')[0];
						}
						this.orderId = orderId;
						this.confirmDialog = true;
						// location.replace(_href)
						// location.href = _href
						const iframe = document.createElement('iframe')
						iframe.style.display = 'none'
						iframe.setAttribute('src', _href)
						iframe.setAttribute('sandbox', 'allow-top-navigation allow-scripts')
						document.body.appendChild(iframe)
						console.log('append iframe');
					} else {
						location.href = _href
					}
				} else {
					uni.showToast({
						duration: 2000,
						title: '未获取到回调地址',
						icon: 'none'
					})
				}
				// #endif
				// #ifndef (H5 || H5-HEJIA)
				this.execPay(orderId, res);
				// #endif
			}).catch(err => {
				console.log('err', err);
				this.allowBuy = false;
				uni.showToast({
					duration: 2000,
					title: err.data.msg || '获取预下单失败',
					icon: 'none'
				})
			})
		},
		execPay(orderId, reqParams) {
			if (!reqParams.paySign) {
				uni.showToast({ duration: 2000, title: '缺少支付必要条件', icon: 'none' })
				return;
			}
			let that = this;
			wx.requestPayment({
				timeStamp: reqParams.timeStamp,
				nonceStr: reqParams.nonceStr,
				package: reqParams.package_,
				signType: reqParams.signType,
				paySign: reqParams.paySign,
				success(res) {
					uni.showLoading({
						mask: true,
						title: '支付中...'
					})
					that.payTimer = setInterval(() => {
						that.fetchPayStatus(orderId)
					}, 2500)
				},
				fail(res) {
					that.allowBuy = false;
					uni.showModal({
						title: '取消支付',
						content: '可以在 “我的-订单” 完成操作',
						showCancel: false,
						confirmColor: '#01B09A',
						success: ({ confirm, cancel }) => {
							uni.navigateBack({ delta: 1 });
						}
					});
				}
			})
		},
		async fetchPayStatus(orderId) {
			if (!orderId) return;
			// #ifndef (H5 || H5-HEJIA)
			try {
				const res = await this.$u.api.fetchPayStatus({ id: orderId, needTip: false });
				if (res.tradeState === 'SUCCESS' || res.tradeState === 'CLOSED' || res.tradeState === 'PAYERROR') {
					uni.hideLoading();
					if (this.payTimer) {
						clearTimeout(this.payTimer);
						this.payTimer = null;
					}
					uni.navigateBack({
						delta: 1
					})
				}
			} catch (err) {
				this.allowBuy = false;
				if (this.payTimer) {
					clearTimeout(this.payTimer);
					this.payTimer = null;
				}
			}
			// #endif
		},
		async agreeAgreement(agree) {
			if (agree === false) {
				this.serverAgreement.agreeStep = 0;
				return;
			}
			this.serverAgreement.agreeStep++;
			if (this.serverAgreement.agreeStep == 3) {
				try {
					await this.$u.api.agreeByDevIdAgreement({ devId: this.devId });
					this.handleSubmitOrder();
				} catch (err) {
					console.error(err);
				} finally {
					this.serverAgreement.agreeStep = 0;
				}
			}
		},
		async getNickName() {
			let _member = uni.getStorageSync('member');
			// #ifdef (H5 || H5-HEJIA)
			if (_member) {
				let _name = _member.nickname || _member.name;
				if (!_name) {
					_name = _member.phone
				}
				return _name;
			} else {
				throw '支付失败, 无法获取用户参数';
			}
			// #endif
			// #ifndef (H5 || H5-HEJIA)
			if (_member && (_member.nickname || _member.name)) {
				return _member.nickname || _member.name;
			} else {
				return new Promise((resove, reject) => {
					wx.getUserProfile({
						desc: '用于完善订单信息',
						success: (res) => {
							_member.nickname = res.userInfo.nickName
							uni.setStorageSync('member', _member);
							resove(res.userInfo.nickName);
						},
						fail: res => {
							reject('您拒绝了请求, 无法完善相应信息');
						}
					})
				});
			}
			// #endif
		}
	}
}
</script>

<style lang="scss" scoped>
$color-page: #F2F2F2;
$color-bg: #FFFFFF;
$color-main: #01B09A;

.page-wrap {
	background-color: $color-page;

	.view-wrap {
		margin: 0 0 10rpx 0;
		background-color: $color-bg;
		padding: 20rpx 20rpx;

		.txt-base {
			height: 56rpx;
			line-height: 56rpx;
		}

		.title {
			@extend .txt-base;
			font-size: 28rpx;
		}

		.label {
			@extend .txt-base;
			font-size: 26rpx;
		}

		.server-wrap {
			display: flex;
			flex-wrap: wrap;
			justify-content: flex-start;
			text-align: center;

			.base-server {
				width: 33.3%;

				.item {
					height: 90rpx;
					line-height: 90rpx;
					border: 1rpx solid $color-main;
					margin: 10rpx 20rpx;
					color: $color-main;
				}

				.selected {
					border: 4rpx solid $color-main;
				}
			}

			.extend-server {
				width: 50%;

				.item {
					border: 1rpx solid #ad742d;
					border-radius: 16rpx;
					position: relative;
					margin: 10rpx 20rpx;
					padding-top: 45rpx;
					min-height: 150rpx;
					background-color: #fbebd2;
					color: #7d550d;
					font-size: 32rpx;
					font-weight: 500;

					.desc {
						padding-top: 10rpx;
						font-size: 26rpx;
					}

					.status-1 {
						position: absolute;
						border-radius: 0 16rpx 0 16rpx;
						top: -1rpx;
						right: -1rpx;
						width: 120rpx;
						height: 48rpx;
						line-height: 48rpx;
						background-color: #ad742d;
						color: #FFFFFF;
						font-size: 26rpx;
					}

					.status-2 {
						@extend .status-1;
						background-color: #fad2d2;
						color: #EB0400;
					}

					.status-0 {
						@extend .status-1;
						background-color: #bbbbbb;
						color: #FFFFFF;
					}
				}

				.selected {
					border: 4rpx solid #ad742d;
				}
			}
		}
	}

	.price-wrap {
		display: flex;
		padding: 0 10rpx;

		.item {
			display: flex;
			flex-flow: column;
			height: 250rpx;
			background-color: $color-bg;
			flex: 1;
			text-align: center;
			justify-content: center;
			margin: 10rpx;
			border: 1rpx solid #e2e2e2;
			border-radius: 16rpx;

			.title {
				font-size: 28rpx;
				color: #000000;
			}

			.price {
				font-size: 56rpx;
				color: #c9891e;
			}

			.empty {
				height: 30rpx;
			}

			.strikethrough {
				@extend .empty;
				color: #c9891e;
				text-decoration: line-through;
			}

		}

		.selected {
			border: 4rpx solid #eac67c;
			border-radius: 16rpx;
			background-color: #fff7e2;
		}

		.pay-radio {
			::v-deep .u-radio__label {
				display: none;
			}
		}
	}

}
</style>
