<template>
	<view class="family-device-choice-page">
		<view class="top-header-line"></view>
		
		<u-empty v-if="!familys || !familys.length" text="暂无可转入家庭" mode="list"></u-empty>
		
		<view v-else>
			<u-tabs :list="familys" :is-scroll="false" :current="tabActived" :item-width="100" bg-color="transparent"
				:active-item-style="{ 'color': '#01B09A' }" 
				:bar-style="{ 'width': '20rpx', 'background-color': '#01B09A', 'border-radius': '20rpx;' }"
				@change="handleTabChange"></u-tabs>
			
			<u-empty v-if="!devs.length" text="暂无转入的设备" mode="list" class="ccc"></u-empty>
			<view v-else v-for="(dev, index) in devs" :key="index" class="item-block">
				<view class="family-item" @click="handleDevClick(dev)">
					<view class="rdo">
						<text v-if="devSelectRdo === dev.id" class="icon iconfont icon-xuanzhong radio" style="color: #01B09A"></text>
						<text v-else class="icon iconfont icon-weixuanzhong radio"></text>
					</view>
					<view class="infos">
						<image class="left-img" :src="staticUrl + '/images/device/device-1-1.png'"></image>
						<view class="right-info">
							<view class="device-name">{{ (dev.devName || dev.devCode) || '' }}({{ dev.devCode }})</view>
							<view v-if="dev.selfBind === false" class="device-share-info" >{{ dev.bindMemberName ? `来自 ${dev.bindMemberName} 的分享` : '' }}</view>
						</view>
					</view>
				</view>
			</view>
			<view v-if="devs.length" class="footer-btns">
				<u-button shape="circle" class="next-btn diy-btn" size="medium" @loading="submitLoading" 
				:custom-style="{ 'width': '100%', 'color': 'white', 'background-color': '#01B09A' }"  hover-class="none"
				@click="handleMove">确认</u-button>
			</view>
		</view>
		
		
	</view>
</template>

<script>
	export default {
		data() {
			return {
				staticUrl: this.$u.http.config.staticBaseUrl,
				devSelectRdo: undefined,
				tabActived: 0,
				familys: [],
				devs: [],
				currFamilyId: undefined,
				submitLoading: false,
			}
		},
		onLoad(option) {
			this.currFamilyId = option.id;
			this.fetchFamilys();
		},
		methods: {
			handleDevClick(dev) {
				this.devSelectRdo = this.devSelectRdo === dev.id ? undefined : dev.id
			},
			fetchFamilys() {
				this.$u.api.fetchFamilyList({ }).then(res => {
					this.familys = (res || []).filter(f => `${f.id}` !== this.currFamilyId && f.selfCreate === true)
					if (this.familys && this.familys.length) {
						this.fetchFamilyDevList(this.familys[0].id);
					}
				}).catch(err => {
					uni.showToast({ duration: 2000, title: err.message || '发生未知错误', icon: 'none' })
				})
			},
			fetchFamilyDevList(id) {
				this.$u.api.fetchFamilyDevList({ familyId: id }).then(res => {
					this.devs = (res || []).filter(f => f.selfBind === true)
				}).catch(err => {
					uni.showToast({ duration: 2000, title: err.message || '发生未知错误', icon: 'none' })
				})
			},
			handleTabChange(index) {
				this.tabActived = index;
				this.fetchFamilyDevList(this.familys[index].id);
				this.this.familySelectRdo = undefined;
			},
			handleMove() {
				let _fromFamilyId = this.familys[this.tabActived].id;
				if (!_fromFamilyId || !this.currFamilyId) {
					this.$toast('参数有误, 无法转移')
					return;
				}
				if (!this.devSelectRdo) {
					this.$toast('请选择设备')
					return;
				}
				uni.showLoading({
					title: '保存中...',
					mask: true
				})
				this.submitLoading = true;
				this.$u.api.execTransferFamilyDev({ fromFamilyId: _fromFamilyId, devId: this.devSelectRdo, toFamilyId: this.currFamilyId }).then(res => {
					this.$toast('设备转移成功')
					setTimeout(() => {
						this.submitLoading = false;
						uni.reLaunch({
							url: '/pages/index/index'
						})
					}, 500);
				}).catch(err => {
					this.submitLoading = false;
					console.log('err', err)
					this.$toast(err.message || '发生未知错误')
				})
			}
		}
	}
</script>

<style lang="scss">
page {
	background-color: #f7f7f7;
}
</style>

<style lang="scss" scoped>
@import url("/static/css/iconfont.css");
.family-device-choice-page {
	padding: 20rpx;
	.item-block {
		// height: 784rpx;
		background: white;
		padding: 40rpx;
		// color: $u-content-color;
		font-size: 28rpx;
		margin-top: 20rpx;
		border-radius: 20rpx;
		.title {
			font-size: 32rpx;
			margin-bottom: 16rpx;
			font-weight: bold;
		}
		.family-item {
			display: flex;
			flex-direction: row;
			align-items: center;
			.rdo {
				width: 70rpx;
				.radio {
					margin-right: 10rpx;
					font-size: 36rpx;
					vertical-align: middle;
				}
			}
			.infos {
				display: flex;
				flex-direction: row;
				align-items: center;
				flex: 1;
				.left-img {
					width: 90rpx;
					height: 90rpx;
					display: inline-block;
					margin-right: 20rpx;
					vertical-align: middle;
				}
				.right-info {
					flex: 1;
					color: #000;
					.device-name {
						font-size: 30rpx;
					}
					.device-share-info {
						font-size: 24rpx;
						margin-top: 10rpx;
					}
				}
			}
		}
		// box-shadow: 0rpx 8rpx 24rpx 0rpx rgba(0, 0, 0, 0.1);
		// border-radius: 16rpx;
		// padding-top: 0rpx;
	}
	.footer-btns {
		position: absolute;
		bottom: 40rpx;
		left: 40rpx;
		right: 40rpx;
		margin: 0 auto;
		.next-btn {
			display: block;
			margin-top: 20rpx;
		}
	}
}

::v-deep .u-tabs-scorll-flex {
	justify-content: flex-start !important;
}
::v-deep .u-tab-item {
	flex: initial !important;
	width: 160rpx !important;
}
::v-deep .u-tab-bar {
	left: 10rpx !important;
}

.ccc {
	::v-deep .u-empty {
		height: calc(100vh - 200rpx) !important;
	}
}
</style>
