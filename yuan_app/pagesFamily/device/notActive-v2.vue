<template>
	<view class="device-not-active-page">
		<view class="top-header-line"></view>
		<!--#ifdef MP-WEIXIN -->
		<view v-if="!isFollowedMp" style="width:100%;">
			<official-account></official-account>
		</view>
		<!--#endif -->
		<view class="item-block">
			<view class="item-title">基本信息</view>
			<u-row>
				<u-col span="4">
					<view class="left-title">
						<view class="device-name">设备名称</view>
					</view>
				</u-col>
				<u-col span="8">
					<view v-if="deviceInfo.selfBind === true" class="right-val" @click="handleOpenNameDialog" @longtap="longtapCopy(deviceInfo.devName || deviceInfo.devCode)">
						{{ (deviceInfo.devName || deviceInfo.devCode) || '' }}
						<u-icon class="expand-hotspot" name="edit-pen-fill" color="#8B8B8B" size="32"></u-icon>
					</view>
					<view v-else class="right-val">{{ (deviceInfo.devName || deviceInfo.devCode) || '' }}</view>
				</u-col>
			</u-row>
			<u-row>
				<u-col span="5">
					<view class="left-title">
						<view class="device-name">设备编码</view>
					</view>
				</u-col>
				<u-col span="7">
					<view class="right-val">
						{{ deviceInfo.devCode }}
					</view>
				</u-col>
			</u-row>
			<u-row>
				<u-col span="5">
					<view class="left-title">
						<view class="device-name">运行状态</view>
					</view>
				</u-col>
				<u-col span="7">
					<view class="right-val">
						{{ deviceInfo.statusName }}
					</view>
				</u-col>
			</u-row>
			<u-row>
				<u-col span="5">
					<view class="left-title">
						<view class="device-name">设备类型</view>
					</view>
				</u-col>
				<u-col span="7">
					<view class="right-val">
						{{ deviceInfo.deviceTypeName ||''}}
					</view>
				</u-col>
			</u-row>
			<u-row v-if="checkDeviceType(deviceInfo,['1','2','5'])">
				<u-col span="5">
					<view class="left-title">
						<view class="device-name">产品型号</view>
					</view>
				</u-col>
				<u-col span="7">
					<!-- <view v-if="deviceInfo.productModelName" class="right-val"
						@click="gotoUrl(`/pagesFamily/device/spec/index?devId=${deviceInfo.id}&devName=${deviceInfo.devName || deviceInfo.devCode}&modelName=${deviceInfo.productModelName}&serverStatus=${deviceInfo.serverStatus}&serverStatusName=${deviceInfo.serverStatusName}&serverEndDate=${deviceInfo.serverEndDate}&activeStatus=${deviceInfo.activeStatus}`)">
						{{ deviceInfo.productModelName || '' }}<u-icon name="arrow-right" color="#b9b9b9" size="22"></u-icon>
					</view> -->
					<view v-if="deviceInfo.productModelName" class="right-val"
						@click="$navTo(`pagesFamily/device/order/index?devId=${deviceInfo.id}`)">
						{{ deviceInfo.productModelName || '' }}<u-icon name="arrow-right" color="#b9b9b9" size="22"></u-icon>
					</view>
					<view v-else class="right-val">
						{{ deviceInfo.productModelName || '' }}
					</view>
				</u-col>
			</u-row>
			<u-row v-if="checkDeviceType(deviceInfo,['1','2','5'])">
				<u-col span="4">
					<view class="left-title">
						<view class="device-name">安装地址</view>
					</view>
				</u-col>
				<u-col span="8">
					<!-- <view class="right-val" style="line-height:50rpx" @click="gotoUrl(`/pages/address/index?source=${deviceConfigEnum}`)"> -->
					<view class="right-val" style="line-height:50rpx">
						{{ deviceInfo.houseAddr || '' }}
						<!-- <u-icon name="arrow-right" color="#b9b9b9" size="22"></u-icon> -->
					</view>
				</u-col>
			</u-row>
			<u-row style="border-bottom: none;" v-if="checkDeviceType(deviceInfo,['1','2','5'])">
				<u-col span="8">
					<view class="left-title">
						<view class="device-name">设备模式</view>
					</view>
				</u-col>
				<u-col span="4">
					<view class="right-val" @click="handleShowDevModeAction">
						{{ deviceInfo.devModelName || '' }}<u-icon name="arrow-right" color="#b9b9b9" size="22"></u-icon>
					</view>
				</u-col>
			</u-row>
			<u-row style="border-bottom: none;" v-if="checkDeviceType(deviceInfo,['1','2','5'])">
				<u-col span="8">
					<view class="left-title">
						<view class="device-name">检查新版本</view>
					</view>
				</u-col>
				<u-col span="4">
					<view class="right-val" @click="checkUpgradeableVersion">
						检查<u-icon name="arrow-right" color="#b9b9b9" size="22"></u-icon>
					</view>
				</u-col>
			</u-row>
		</view>

		<view class="item-block" v-if="checkDeviceType(deviceInfo,['1','2','5'])">
			<view class="item-title">设备信息</view>
			<u-row v-if="checkDeviceType(deviceInfo,['1','2','5'])">
				<u-col span="8">
					<view class="left-title">
						<view class="device-name">房间场景</view>
					</view>
				</u-col>
				<u-col span="4">
					<view class="right-val" @click="handleShowDevSenceAction">
						{{ deviceInfo.devSceneName || '' }}<u-icon name="arrow-right" color="#b9b9b9" size="22"></u-icon>
					</view>
				</u-col>
			</u-row>
			<u-row v-if="checkDeviceType(deviceInfo,['1','2','5'])">
				<u-col span="8">
					<view class="left-title">
						<view class="device-name">房间配置</view>
					</view>
				</u-col>
				<u-col span="4">
					<!--#ifdef MP-WEIXIN -->
					<view class="right-val" @click="toConfigInstall(`pagesDevice/config/room-web?token=${token}&devCode=${deviceInfo.devCode}&sourcePage=setting&devScene=${deviceInfo.devScene}`)">
						{{ statusInfo.roomVO === true ? '已设置' : '未设置' }}
						<u-icon name="arrow-right" color="#b9b9b9" size="22"></u-icon>
					</view>
					<!--#endif -->
					
					<!--#ifndef MP-WEIXIN -->
					<view class="right-val" @click="toConfigInstall(`pagesDevice/config/room?devCode=${deviceInfo.devCode}&sourcePage=setting&devScene=${deviceInfo.devScene}`)">
						{{ statusInfo.roomVO === true ? '已设置' : '未设置' }}
						<u-icon name="arrow-right" color="#b9b9b9" size="22"></u-icon>
					</view>
					<!--#endif -->
				</u-col>
			</u-row>
			<!-- <u-row>
				<u-col span="8">
					<view class="left-title">
						<view class="device-name">安装方式</view>
					</view>
				</u-col>
				<u-col span="4">
					<view class="right-val" @click="toSwitchSpotType">
						{{ deviceInfo.spotTypeName || '未设置' }}
						<u-icon name="arrow-right" color="#b9b9b9" size="22"></u-icon>
					</view>
				</u-col>
			</u-row>
			<u-row>
				<u-col span="8">
					<view class="left-title">
						<view class="device-name">房间尺寸</view>
					</view>
				</u-col>
				<u-col span="4">
					<view class="right-val" @click="toConfigInstall(`pagesDevice/config/room-size`)">
						{{ statusInfo.roomVO === true ? '已设置' : '未设置' }}
						<u-icon name="arrow-right" color="#b9b9b9" size="22"></u-icon>
					</view>
				</u-col>
			</u-row>
			<u-row>
				<u-col span="8">
					<view class="left-title">
						<view class="device-name">设备方位</view>
					</view>
				</u-col>
				<u-col span="4">
					<view class="right-val" @click="toConfigInstall(`pagesDevice/config/sensor`)">
						{{ statusInfo.roomSensorVO === true ? '已设置' : '未设置' }}<u-icon name="arrow-right" color="#b9b9b9" size="22">
						</u-icon>
					</view>
				</u-col>
			</u-row>
			<u-row>
				<u-col span="8">
					<view class="left-title">
						<view class="device-name">门方位</view>
					</view>
				</u-col>
				<u-col span="4">
					<view class="right-val" @click="toConfigInstall(`pagesDevice/config/door`)">
						{{ statusInfo.listRoomGateVO === true ? '已设置' : '未设置' }}<u-icon name="arrow-right" color="#b9b9b9" size="22"></u-icon>
					</view>
				</u-col>
			</u-row>
			<u-row>
				<u-col span="8">
					<view class="left-title">
						<view class="device-name">区域</view>
					</view>
				</u-col>
				<u-col span="4">
					<view class="right-val" @click="toConfigInstall(`pagesDevice/config/area`)">
						{{ statusInfo.listRoomRegionVO === true ? '已设置' : '未设置' }}<u-icon name="arrow-right" color="#b9b9b9" size="22"></u-icon>
					</view>
				</u-col>
			</u-row>
			<u-row>
				<u-col span="8">
					<view class="left-title">
						<view class="device-name">房间标定</view>
					</view>
				</u-col>
				<u-col span="4">
					<view class="right-val" @click="toRoomCalibration">
						<u-icon name="arrow-right" color="#b9b9b9" size="22"></u-icon>
					</view>
				</u-col>
			</u-row> -->
			<u-row style="border-bottom: none;" v-if="checkDeviceType(deviceInfo,['1','2','5'])">
				<u-col span="8">
					<view class="left-title">
						<view class="device-name">设备响应时间</view>
					</view>
				</u-col>
				<u-col span="4">
					<view v-if="deviceInfo.devModel=='0'" class="right-val" @click="gotoUrl('/pagesDevice/responseSetting')">
						{{ deviceInfo.isHasResponseTime === true ? '已设置' : '未设置' }}
						<u-icon name="arrow-right" color="#b9b9b9" size="22"></u-icon>
					</view>
					<view v-else class="right-val">
						{{ deviceInfo.isHasResponseTime === true ? '已设置' : '未设置' }}
					</view>
				</u-col>
			</u-row>
			
			<u-row style="border-bottom: none;" v-if="deviceInfo.devScene=='3' && checkDeviceType(deviceInfo,['1','2','5'])">
				<u-col span="8">
					<view class="left-title">
						<view class="device-name">预警配置</view>
					</view>
				</u-col>
				<u-col span="4">
					<view class="right-val" @click="gotoUrl('/pagesDevice/bedOutAlarmSetting?id='+deviceInfo.id)">
						{{ deviceInfo.outBedAlarm=='1' ? '已设置' : '未设置' }}
						<u-icon name="arrow-right" color="#b9b9b9" size="22"></u-icon>
					</view>
				</u-col>
			</u-row>
			<u-row style="border-bottom: none;" v-if="deviceInfo.devScene=='3' && checkDeviceType(deviceInfo,['1','2','5'])">
				<u-col span="8">
					<view class="left-title">
						<view class="device-name">睡眠统计配置</view>
					</view>
				</u-col>
				<u-col span="4">
					<view class="right-val" @click="gotoUrl('/pagesDevice/sleepReportSetting?id='+deviceInfo.id)">
						{{ !!deviceInfo.sleepBegin ? '已设置' : '未设置' }}
						<u-icon name="arrow-right" color="#b9b9b9" size="22"></u-icon>
					</view>
				</u-col>
			</u-row>
			
		</view>


		<view class="item-block">
			<view class="item-title">人员信息</view>
			<u-row>
				<u-col span="8">
					<view class="left-title">
						<view class="device-name">被监护人</view>
					</view>
				</u-col>
				<u-col span="4">
					<view class="right-val"
						@click="$navTo(`pagesMy/device/guardian/index?source=device&notEdit=true&readonly=true`)">
						{{ deviceInfo.isHasOlder === true ? '已设置' : '未设置' }}<u-icon name="arrow-right" color="#b9b9b9" size="22">
						</u-icon>
					</view>
				</u-col>
			</u-row>
			<u-row style="border-bottom: none;" v-if="checkDeviceType(deviceInfo,['1','2','5'])">
				<u-col span="8">
					<view class="left-title">
						<view class="device-name">紧急联系人</view>
					</view>
				</u-col>
				<u-col span="4">
					<view class="right-val"
						@click="$navTo(`pagesMy/device/connector/index?source=device&notEdit=true&readonly=true`)">
						{{ deviceInfo.isHasConnector === true ? '已设置' : '未设置' }}<u-icon name="arrow-right" color="#b9b9b9"
							size="22"></u-icon>
					</view>
				</u-col>
			</u-row>
		</view>

		<view class="item-block">
			<view class="item-title">通用设置</view>
			<!-- WiFi配置：小程序环境或App环境都显示 -->
			<!--#ifdef MP-WEIXIN -->
			<u-row v-if="checkDeviceType(deviceInfo,['1','2','5'])">
				<u-col span="8">
					<view class="left-title">
						<view class="device-name">WIFI配置</view>
					</view>
				</u-col>
				<u-col span="4">
					<view class="right-val" @click="toConfigWifi">
						<u-icon name="arrow-right" color="#b9b9b9" size="22"></u-icon>
					</view>
				</u-col>
			</u-row>
			<!--#endif -->

			<!--#ifndef MP-WEIXIN -->
			<u-row v-if="checkDeviceType(deviceInfo,['1','2','5'])">
				<u-col span="8">
					<view class="left-title">
						<view class="device-name">WIFI配置</view>
					</view>
				</u-col>
				<u-col span="4">
					<view class="right-val" @click="toConfigWifi">
						<u-icon name="arrow-right" color="#b9b9b9" size="22"></u-icon>
					</view>
				</u-col>
			</u-row>
			<!--#endif -->
			<u-row>
				<u-col span="8">
					<view class="left-title">
						<view class="device-name">设备分享</view>
					</view>
				</u-col>
				<u-col span="4">
					<view class="right-val"
						@click="$navTo(`pagesFamily/devMember/index?id=${deviceInfo.id}&name=${deviceInfo.devName || deviceInfo.devCode}`)">
						{{ deviceInfo.memberCount }}<u-icon name="arrow-right" color="#b9b9b9" size="22"></u-icon>
					</view>
				</u-col>
			</u-row>
		</view>

		<view class="item-block" v-if="checkDeviceType(deviceInfo,['1','2','5'])">
			<view class="item-title">服务信息</view>
			<u-row>
				<u-col span="6">
					<view class="left-title">
						<view class="device-name">管家</view>
					</view>
				</u-col>
				<u-col span="6">
					<view class="right-val">
						{{ deviceInfo.chambName || '您还没有专属管家' }}
					</view>
				</u-col>
			</u-row>
			<u-row>
				<u-col span="6">
					<view class="left-title">
						<view class="device-name">管家电话</view>
					</view>
				</u-col>
				<u-col span="6">
					<view class="right-val">
						{{ deviceInfo.chambPhone || '您还没有专属管家' }}
					</view>
				</u-col>
			</u-row>
			<u-row>
				<u-col span="8">
					<view class="left-title">
						<view class="device-name">设备所属服务站</view>
					</view>
				</u-col>
				<u-col span="4">
					<view class="right-val">
						{{ deviceInfo.stationName || '-' }}
					</view>
				</u-col>
			</u-row>
			<u-row style="border-bottom: none;">
				<u-col span="8">
					<view class="left-title">
						<view class="device-name">服务站简介</view>
					</view>
				</u-col>
				<u-col span="4">
					<view class="right-val" @click="$navTo(`pagesDevice/stationInfo?id=${deviceInfo.stationId}`)">
						<u-icon name="arrow-right" color="#b9b9b9" size="22"></u-icon>
					</view>
				</u-col>
			</u-row>
		</view>

		<view class="footer-btns">
			<!-- <u-button v-if="checked === true" type="primary" shape="circle" class="scan-code-btn" @click="scan">下一步</u-button> -->
			<!-- <u-button type="primary" shape="circle" class="next-btn" @click="handleSubmit">保存</u-button> -->
			<!-- <u-button type="primary" shape="circle" class="next-btn" plain @click="handleOpenDeleteDialog">删除设备</u-button> -->
			<!-- 此处不能用条件编译的方式，不然得和家亲与H5各一套代码进行部署，所以这个采取变量isHejia的方式控制 -->
			<template v-if="deviceInfo.devSource == 1 && !isHejia">
				<u-button shape="circle" class="scan-code-btn  diy-btn" size="medium"
					:custom-style="{ 'width': '100%', 'color':'#fff' , 'background-color': '#DE4D4A' }" hover-class="none"
					@click="handleOpenDeleteDialog">删除设备</u-button>
			</template>
			<template v-if="deviceInfo.devSource == 2">
				<u-button shape="circle" class="scan-code-btn  diy-btn" size="medium"
					:custom-style="{ 'width': '100%', 'color': '#fff', 'background-color': '#01B09A' }" hover-class="none"
					@click="leaveShared">退出共享</u-button>
			</template>

		</view>

		<u-modal v-model="deleteDialog" title="是否在设备列表中删除设备？" :title-style="{ color: '#ff1f1f' }" :show-cancel-button="true"
			@confirm="handleConfirmDelete">
			<view class="slot-content" style="padding: 20rpx;">
				{{worryfreeWarn || ''}}
			</view>
		</u-modal>

		<u-modal v-model="deviceNameDialog" title="修改设备的名称" :show-cancel-button="true" @confirm="handleConfirmNameDialog">
			<view class="slot-content" style="padding: 40rpx;">
				<uni-easyinput v-model="value" placeholder="请输入内容" maxlength="15"></uni-easyinput>
			</view>
		</u-modal>

		<u-action-sheet @click="handleDevModeClick" :list="dict.devModes" :tips="relationTypeTips"
			v-model="showDevModeActionSheet"></u-action-sheet>
		<u-action-sheet @click="handleDevSenceClick" :list="dict.devSence" :tips="relationTypeTips"
			v-model="showDevSenceActionSheet"></u-action-sheet>
		<u-action-sheet @click="handleSpotTypeClick" :list="dict.spotType" :tips="relationTypeTips"
			v-model="showSpotTypeActionSheet"></u-action-sheet>
	</view>
</template>

<script>

import { gotoRoomCalibration, gotoConfigWifi } from "../../utils/gotoOutside";
import {longtapCopy} from "../../utils/util";
import appBridge from "../../utils/bridge/AppBridge.js";
// #ifdef H5-HEJIA
import '../../static/common/js/hejia-latest.min.js'
const {	Hejia} = window;
// #endif

export default {
	data() {
		return {
			isFollowedMp:false,
			deleteDialog: false,
			showDevModeActionSheet: false,
			showDevSenceActionSheet: false,
			showSpotTypeActionSheet: false,
			relationTypeTips: {
				text: '请选择',
				color: '#909399',
				fontSize: 24
			},
			dict: {
				devModes: [
					{
						code: '0',
						text: '日常模式'
					},
					{
						code: '1',
						text: '测试模式'
					}
				],
				// 1客厅，2卫生间，3卧室，4其他
				devSence: [
					{
						code: '1',
						text: '客厅'
					},
					{
						code: '2',
						text: '卫生间'
					},
					{
						code: '3',
						text: '卧室'
					},
					{
						code: '4',
						text: '其他'
					}
				],
				spotType: [
					{
						code: '1',
						text: '顶装'
					},
					{
						code: '2',
						text: '侧装'
					}
				]
			},
			constants: {
				deviceOnlineStatus: '1',
			},

			deviceName: '',
			deviceNameDialog: false,
			deviceInfo: {},
			value: '',
			currDevId: undefined,

			deviceConfigEnum: "deviceConfigEnum",
			statusInfo: {},

			worryfreeWarn: null,
			isHejia:false,
			token:null,
		}
	},
	onLoad(option) {
		this.currDevId = option.devId;
		if (option.devId) {
			uni.setStorageSync('devId', option.devId)
		}
		if (option.devCode) {
			uni.setStorageSync('devCode', option.devCode)
		}
		this.token = uni.getStorageSync('token');
	},
	onShow() {
		const {followedMp} = uni.getStorageSync('member')
		console.log("followedMp："+followedMp);
		this.isFollowedMp = followedMp;
		this.fetchDeviceInfo();
		//TODO 后续合到详情接口中
		this.fetchStatusInfo();
		// #ifdef H5-HEJIA
		Hejia.ready(() => {
			console.log("运行在和家亲",Hejia)
			this.isHejia = true;
		});
		// #endif
	},
	methods: {
		longtapCopy,
		checkDeviceType(device,typeArr){
			return typeArr.includes(device.deviceType);
		},
		checkUpgradeableVersion(){
			if (this.deviceInfo.status !== this.constants.deviceOnlineStatus) {
				uni.showToast({ duration: 2000, title: "离线设备不允许操作", icon: 'none' })
				return;
			}
			uni.showLoading({
				title: '检查中...',
				mask: true
			})
			try {
				this.$u.api.checkUpgradeableVersion({ devCode:this.deviceInfo.devCode})
				.then(res => {
					uni.hideLoading();
					if(res!==1){
						uni.showModal({
							title: '设备升级',
							content: '当前设备的固件为最新版，无需升级',
							confirmText: '关闭',
							showCancel:false
						});
						return;
					}
					uni.showModal({
						title: '设备升级',
						content: '您的设备有可升级固件，是否升级？',
						confirmText: '升级',
						cancelText: '取消',
						success: async (res) => {
							if (res.confirm) {
								let flag = await this.$u.api.updateFirmwareVersion({ devCode:this.deviceInfo.devCode})
								if(flag === 1 ){
									uni.showToast({ duration: 10000, title: "已发起升级请求，请等待设备重启,这个过程需要几分钟", icon: 'none' })
								}else{
									uni.showToast({ duration: 2000, title: "当前设备的固件为最新版，无需升级", icon: 'none' })
								}
							}
						}
					});
				}).catch(err => {
					uni.hideLoading();
					console.log('err', err)
					uni.showToast({ duration: 2000, title: err.message || '发生未知错误', icon: 'none' })
				}).finally(() => {
					// setTimeout(() => {
					// 	uni.hideLoading();
					// }, 1000)
				})
			} catch (err) {
				console.error(err);
			}
		},
		handleShowDevModeAction() {
			if (this.deviceInfo.status !== this.constants.deviceOnlineStatus) {
				uni.showToast({ duration: 2000, title: "离线设备不允许修改", icon: 'none' })
				return;
			}
			this.showDevModeActionSheet = true;
		},
		handleShowDevSenceAction() {
			if (this.deviceInfo.status !== this.constants.deviceOnlineStatus) {
				uni.showToast({ duration: 2000, title: "离线设备不允许修改", icon: 'none' })
				return;
			}
			this.showDevSenceActionSheet = true;
		},
		handleDevSenceClick(index) {
			uni.showModal({
				title: '场景提醒',
				content: '您将修改设备场景，是否确认？',
				confirmText: '确定',
				cancelText: '关闭',
				success: async (res) => {
					if (res.confirm) {
						this.execModifyDevSence(this.dict.devSence[index])
					}
				}
			});
		},
		handleDevModeClick(index) {
			uni.showModal({
				title: '模式提醒',
				content: this.dict.devModes[index].code === '0' ? '您将进入日常模式，是否确认？' : '您将进入测试模式，可能会频繁收到信息，是否确认？',
				confirmText: '确定',
				cancelText: '关闭',
				success: async (res) => {
					if (res.confirm) {
						this.execModifyDevModel(this.dict.devModes[index].code)
					}
				}
			});
		},
		async handleSpotTypeClick(index) {
			const { code, text } = this.dict.spotType[index];
			const { spotType, devCode } = this.deviceInfo;
			if (spotType === code) {
				return;
			}

			try {
				await this.$u.api.updateDeviceSpotType({ devCode, spotType: code });
				this.deviceInfo.spotType = code;
				this.deviceInfo.spotTypeName = text;
				uni.showModal({ content: '设备安装方式发生变化，请确保房间信息正确', showCancel: false, confirmText: '关闭' })

				this.fetchStatusInfo();
			} catch (err) {
				console.error(err);
			}
		},
		execModifyDevModel(mode) {
			uni.showLoading({
				title: '设置中...',
				mask: true
			})
			this.$u.api.execModifyDevModel({ devId: uni.getStorageSync('devId') || this.currDevId, devModel: mode }).then(res => {
				uni.hideLoading();
				uni.showToast({ duration: 2000, title: '设置模式成功', icon: 'none' })
				if (mode === '0') {
					this.deviceInfo.devModel = '0';
					this.deviceInfo.devModelName = '日常模式';
					this.dict.devModes[0].color = '#2a47d2';
					this.dict.devModes[1].color = '#333';
				} else if (mode === '1') {
					this.deviceInfo.devModel = '1';
					this.deviceInfo.devModelName = '测试模式';
					this.dict.devModes[0].color = '#333';
					this.dict.devModes[1].color = '#2a47d2';
				}
			}).catch(err => {
				uni.hideLoading();
				console.log('err', err)
				uni.showToast({ duration: 2000, title: err.message || '发生未知错误', icon: 'none' })
			}).finally(() => {
				// setTimeout(() => {
				// 	uni.hideLoading();
				// }, 1000)
			})
		},
		execModifyDevSence(sence) {
			uni.showLoading({
				title: '设置中...',
				mask: true
			})
			this.$u.api.execModifyDevSence({ devId: uni.getStorageSync('devId') || this.currDevId, devSence: sence.code }).then(res => {
				uni.hideLoading();
				uni.showToast({ duration: 2000, title: '设置场景成功', icon: 'none' })
				this.deviceInfo.devSceneName = sence.text;
			}).catch(err => {
				uni.hideLoading();
				console.log('err', err)
				uni.showToast({ duration: 2000, title: res.message || '发生未知错误', icon: 'none' })
			}).finally(() => {
				// setTimeout(() => {
				// 	uni.hideLoading();
				// }, 1000)
			})
		},
		toConfigInstall(url) {
			if (!url) {
				return;
			}
			if (this.deviceInfo.status !== this.constants.deviceOnlineStatus) {
				uni.showToast({ duration: 2000, title: "离线设备不允许修改", icon: 'none' })
				return;
			}
			const { spotType } = this.deviceInfo;
			if (!spotType) {
				uni.showToast({ duration: 2000, title: '请选择安装方式', icon: 'none' });
				return;
			}
			if (url.indexOf("?") > -1) {
				this.$navTo(`${url}&spotType=${spotType}&source=dev`);
			} else {
				this.$navTo(`${url}?spotType=${spotType}&source=dev`);
			}
		},
		gotoUrl(url) {
			if (!url) {
				return;
			}
			if (this.deviceInfo.status !== this.constants.deviceOnlineStatus) {
				uni.showToast({ duration: 2000, title: "离线设备不允许修改", icon: 'none' })
				return;
			}
			console.log(url);

			uni.navigateTo({
				url: url
			})
		},
		toSwitchSpotType() {
			if (this.deviceInfo.status !== this.constants.deviceOnlineStatus) {
				uni.showToast({ duration: 2000, title: "离线设备不允许修改", icon: 'none' })
				return;
			}
			this.showSpotTypeActionSheet = true;
		},
		toRoomCalibration() {
			if (this.deviceInfo.status !== this.constants.deviceOnlineStatus) {
				uni.showToast({ duration: 2000, title: "离线设备不允许修改", icon: 'none' })
				return;
			}
			gotoRoomCalibration(this.deviceInfo.devCode);
		},
		async toConfigWifi() {
			// 检查是否在App的WebView环境中
			if (appBridge.isInAppWebView()) {
				try {
					console.log("在App环境中，调用原生WiFi配置功能");
					uni.showLoading({
						title: '正在打开WiFi配置...',
						mask: true
					});

					const result = await appBridge.sendWiFiConfig({
						devCode: this.deviceInfo.devCode,
						token: this.token
					});

					uni.hideLoading();

					if (result.success) {
						uni.showToast({
							title: 'WiFi配置完成',
							icon: 'success',
							duration: 2000
						});
					} else if (result.cancelled) {
						console.log("用户取消了WiFi配置");
					} else {
						uni.showToast({
							title: result.message || 'WiFi配置失败',
							icon: 'none',
							duration: 2000
						});
					}
				} catch (error) {
					uni.hideLoading();
					console.error("调用原生WiFi配置失败:", error);
					uni.showToast({
						title: '无法打开WiFi配置',
						icon: 'none',
						duration: 2000
					});
				}
			} else {
				// 非App环境，使用原有的小程序跳转方式
				gotoConfigWifi(this.deviceInfo.devCode);
			}
		},
		handleOpenNameDialog() {
			this.deviceNameDialog = true
			this.value = this.deviceInfo.devName
		},
		handleConfirmNameDialog() {
			this.handleSubmit();
		},
		async handleOpenDeleteDialog() {
			try {
				// 设备不与被监护人关联
				// const isGuard = await this.$u.api.isGuardDeviceWorryfree({ devId: this.currDevId });
				// if (isGuard) {
				// 	this.worryfreeWarn = "被监护人处于安全保障中，删除设备会影响安全服务的使用，您确定删除吗？";
				// } else {
				// 	this.worryfreeWarn = null;
				// }
				this.deleteDialog = true;
			} catch (err) {
				console.error(err);
			}
		},
		fetchDeviceInfo() {
			uni.showLoading({
				title: '加载中...',
				mask: true
			})
			this.$u.api.fetchMyDeviceInfo({ devId: uni.getStorageSync('devId') || this.currDevId }).then(res => {
				if (res) {
					this.deviceInfo = res
					uni.setStorageSync('devCode',this.deviceInfo.devCode)
					if (res.devModel === '0') {
						this.dict.devModes[0].color = '#2a47d2';
						this.dict.devModes[1].color = '#333';
					} else if (res.devModel === '1') {
						this.dict.devModes[0].color = '#333';
						this.dict.devModes[1].color = '#2a47d2';
					}
				}
			}).catch(err => {
				this.deviceInfo = {}
			}).finally(() => {
				setTimeout(() => {
					uni.hideLoading();
				}, 1000)
			})
		},
		async fetchStatusInfo() {
			uni.showLoading({
				title: '加载中...',
				mask: true
			});
			try {
				this.statusInfo = await this.$u.api.fetchConfStatusInfo({ devCode: uni.getStorageSync('devCode') });
			} catch (err) {
				console.error(err);
			} finally {
				setTimeout(() => {
					uni.hideLoading();
				}, 1000);
			}
		},
		handleSubmit() {
			if (!this.deviceInfo.id || !this.value) {
				uni.showModal({ content: '请检查数据是否填写完整', showCancel: false, confirmText: '关闭' })
				return;
			}
			if (!this.value.length) {
				uni.showModal({ content: '设备名称不能为空', showCancel: false, confirmText: '关闭' })
				return;
			}
			let _params = {
				id: uni.getStorageSync('devId') || this.currDevId,
				devName: this.value,
				...this.form,
			}
			uni.showLoading({
				title: '保存中...',
				mask: true
			})
			this.$u.api.execUpdateMyDevice(_params).then(res => {
				uni.hideLoading();
				uni.showToast({ duration: 2000, title: '保存名称成功', icon: 'none' })
				this.deviceInfo.devName = this.value
				this.isChange = true
				// setTimeout(() => {
				// 	uni.navigateBack({
				// 		delta: 1
				// 	})
				// }, 1000);
			}).catch(err => {
				uni.hideLoading();
				console.log('err', err)
				uni.showToast({ duration: 2000, title: err.message || '发生未知错误', icon: 'none' })
			})
		},
		handleConfirmDelete() {
			uni.showLoading({
				title: '删除中...',
				mask: true
			})
			/*
			和家亲的解绑api调用不成功，改为和家亲下不支持删除设备
			const devCode = uni.getStorageSync('devCode');
			const cmccDeviceId =  uni.getStorageSync('cmccMdids_'+devCode);
			if(cmccDeviceId){
				console.log("跳转和家亲管控页解绑设备："+cmccDeviceId)
				this.$navTo(`pages/cmcc/index?deviceType=589710&deviceId=${cmccDeviceId}&optType=unBind`)
			}else{
				console.log("解绑和家亲设备失败，找不到和家亲设备ID")
			}*/
			this.$u.api.execUnBindMyDevice({ devId: uni.getStorageSync('devId') || this.currDevId }).then(res => {
				console.log("删除成功！")
				uni.showToast({ duration: 2000, title: '删除设备成功', icon: 'none' })
				// this.deleteDialog = true
				uni.removeStorageSync('devCode')
				uni.removeStorageSync('devId')
				this.$navTo(`pages/index/index`)
				// setTimeout(() => {
				// 	uni.navigateBack({
				// 		delta: 1
				// 	})
				// }, 500);
			}).catch(err => {
				console.log('err', err)
				uni.showToast({ duration: 2000, title: res.message || '发生未知错误', icon: 'none' })
			}).finally(() => {
				setTimeout(() => {
					uni.hideLoading();
				}, 1000)
			})
		},
		leaveShared() {
			const that = this;
			const memberId = uni.getStorageSync('member').id;
			const devId = this.deviceInfo.id;
			uni.showModal({
				content: "退出分享....",
				showCancel: true,
				confirmColor: "#01B09A",
				success: ({ cancel, content }) => {
					if (cancel === true) {
						return;
					}
					that.$u.api.execRemoveDevMember({ memberId, devId, })
						.then(res => {
							setTimeout(() => {
								uni.navigateBack({
									delta: 1
								})
							}, 1000);
						}).catch(err => {
							console.error(err);
						})
				}
			});
		}
	}
}
</script>
<style lang="scss">
page {
	background: #f7f7f7;
}
</style>
<style lang="scss">
::v-deep .u-round-circle {
	&::after {
		border: none !important;
	}
}
</style>
<style lang="scss" scoped>
.device-not-active-page {
	padding-bottom: 150rpx;

	.top-title {
		font-size: 36rpx;
		color: #333;
		margin-top: 40rpx;
		margin-bottom: 24rpx;
	}

	.item-block {
		padding: 0rpx 30rpx;
		color: $u-content-color;
		background: white;
		font-size: 28rpx;

		// box-shadow: 0rpx 8rpx 24rpx 0rpx rgba(0, 0, 0, 0.1);
		// border-radius: 16rpx;
		//#ifdef H5
		.u-row {
			line-height: 50px;
			border-bottom: 2rpx solid #ECECEC;
		}

		//#endif
		.item-title {
			padding-top: 10rpx;
			font-size: 32rpx;
			color: #7F7F7F;
		}

		.left-title {
			.device-name {
				color: #0D0D0D;
				font-size: 30rpx;
			}
		}

		.right-val {
			color: #888888;
			text-align: right;

			.activate-now {
				padding: 8rpx 20rpx;
				background: #01B09A;
				border-radius: 45rpx;
				color: white;
				font-size: 24rpx;
				line-height: initial;
				text-align: center;
				// position: relative;
				// top: 0rpx;
				display: inline-block;
			}

			::v-deep .u-icon {
				margin-left: 10rpx;
				position: relative;
				top: -2rpx;
			}
		}

		&:nth-child(n + 2) {
			margin-top: 20rpx;
		}
	}

	u-row {
		display: block;
		line-height: 100rpx;
		border-bottom: 2rpx solid #f1f1f1;
		// padding: 18rpx 0;
	}

	.footer-btns_old {
		// position: fixed;
		// bottom: 35rpx;
		// left: 40rpx;
		// right: 40rpx;
		padding: 40rpx 40rpx;
		margin: 0 auto;

		.next-btn {
			display: block;
			margin-top: 20rpx;
		}
	}
	.footer-btns {
		position: fixed;
		bottom: 0rpx;
		left: 0rpx;
		right: 0rpx;
		padding:20rpx 40rpx 40rpx;
		z-index: 999;
		background-color: white;
		display: flex;
		align-items: center;
		justify-content: center;
		.next-btn{
			display: block;
			flex: 1;
		}
	}
}
</style>
