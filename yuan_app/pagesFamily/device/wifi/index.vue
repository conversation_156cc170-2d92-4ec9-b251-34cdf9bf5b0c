<template>
	<view class="wifi-page">
		<view class="top-header-line"></view>
		
		<view class="top-wrap">
			<image class="img-center-bg" :src="`${staticUrl}/images/device/top-center-img.png`"></image>
			<!-- #ifdef MP-WEIXIN -->
			<view class="tip">请先在“配置小程序中”配置蓝牙、wifi<br/>点击下方按钮快速进入配置</view>
			<u-button type="primary" shape="circle" size="medium" class="config-wifi-btn" plain @click="configWifi">配置蓝牙WIFI</u-button>
			<!-- #endif -->
			<!-- #ifdef H5 -->
			<view class="tip">请先在“配置小程序中”配置蓝牙、wifi</view>
			<!-- #endif -->
		</view>
		
		<view class="footer-btns">
			<!-- <u-checkbox-group>
				<u-checkbox v-model="checked" shape="circle">已完成上述操作</u-checkbox>
			</u-checkbox-group> -->
			
			<view style="text-align: left;" @click="handleCheckedClick">
				<text v-if="checked" class="icon iconfont icon-xuanzhong radio" style="color: #01B09A"></text>
				<text v-else class="icon iconfont icon-weixuanzhong radio"></text>
				<text style="margin-left: 14rpx;">已完成上述操作</text>
			</view>
			
			<!-- <u-button v-if="checked === true" type="primary" shape="circle" class="next-btn" @click="next">下一步</u-button>
			<u-button v-else type="default" shape="circle" class="next-btn" disabled>下一步</u-button> -->
			
			<u-button v-if="checked === true" shape="circle" class="next-btn diy-btn" size="medium" 
			:custom-style="{ 'width': '100%', 'color': 'white', 'background-color': '#01B09A' }"  hover-class="none"
			@click="next">下一步</u-button>
			<u-button v-else shape="circle" class="next-btn diy-btn" size="medium" 
			:custom-style="{ 'width': '100%', 'color': '#333', 'background-color': '#f7f7f7' }"  hover-class="none" >下一步</u-button>
		</view>
		
	</view>
</template>

<script>
	export default {
		data() {
			return {
				staticUrl: this.$u.http.config.staticBaseUrl,
				checked: false
			}
		},
		methods: {
			handleCheckedClick() {
				this.checked = this.checked === true ? false : true
			},
			gotoUrl(url){
				if (!url) return;
				uni.navigateTo({
					url: url
				})
			},
			configWifi() {
				// uni.showToast({
				// 	duration: 2000,
				// 	title: '敬请关注',
				// 	icon: 'none'
				// });
				// TODO: wifi配置appid
				uni.navigateToMiniProgram({
					// appId: 'wx739a9c524b1c5aa1',
					appId: 'wx6d5e4dd85fa3338c',
					path: '/pages/index/index',
                    envVersion: __wxConfig.envVersion,
					extraData: {
						mdid: uni.getStorageSync('devCode')
					},
					success: res => {
						// 打开成功
						console.log("打开成功", res);
					},
					fail: err => {
						uni.showToast({
							duration: 2000,
							title: '无法配置WIFI及蓝牙信息，请重试',
							icon: 'none'
						})
					}
				});
			},
			next() {
				if (!this.checked) {
					uni.showToast({
						duration: 2000,
						title: '请确认已完成上述操作',
						icon: 'none'
					})
					return;
				}
				uni.redirectTo({
					url: '/pagesDevice/config/tag-config'
				})
			}
		}
	}
</script>

<style lang="scss">
// #ifdef H5
page {
	height: 100%;
}
// #endif
::v-deep .u-round-circle {
	&::after {
		border: none !important;
	}
}
</style>

<style lang="scss" scoped>
@import url("/static/css/iconfont.css");
.wifi-page {
	height: 100vh;
	// #ifdef H5
	height: 100%;
	// #endif
	position: relative;
	// padding: 30upx;
	box-sizing: border-box;
	text-align: center;
	padding-top: 200rpx;
	.img-center-bg {
		width: 268rpx;
		height: 268rpx;
		margin: 0 auto;
	}
	.tip {
		margin-left: 28upx;
		margin-top: 20rpx;
		font-size: 32upx;
		color: #5E5D5D;
		font-size: 30rpx;
		font-weight: 500;
		line-height: 50rpx;
	}
	.config-wifi-btn {
		display: inline-block;
		margin-top: 20rpx;
		::v-deep button {
			padding-left: 50rpx;
			padding-right: 50rpx;
			font-size: 28rpx;
			font-weight: bold;
			background-color: white !important;
		}
	}
	
	.footer-btns {
		position: absolute;
		bottom: 40rpx;
		left: 40rpx;
		right: 40rpx;
		margin: 0 auto;
		.next-btn {
			display: block;
			margin-top: 37rpx;
			&:after{
				border: none;
			}
		}
	}
	
}
</style>
