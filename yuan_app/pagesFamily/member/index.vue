<template>
	<view class="family-member-page">
		<view class="top-header-line"></view>
		<view v-if="family.name" class="title">
			{{ family.name }}
		</view>
		<view class="content">
			<u-grid :col="4" :border="false">
				<u-grid-item v-for="(user, index) in members">
					<u-avatar :src="user.profile" mode="square"></u-avatar>
					<view v-if="user.name || user.nickname" class="grid-text">{{ user.name || user.nickname }}</view>
					<view v-else class="grid-text">{{ user.phone || '' }}</view>
				</u-grid-item>
				<!--#ifdef MP-WEIXIN -->
				<u-grid-item v-if="family.selfCreate === true">
					<view class="grid-add" @click="dialog.add.show = true">
						<u-icon name="plus" color="#99999F" size="40"></u-icon>
					</view>
				</u-grid-item>
				<u-grid-item v-if="family.selfCreate === true">
					<view class="grid-minus" @click="$navTo(`pagesFamily/member/remove?id=${currFamilyId}`)">
						<u-icon name="minus" color="#99999F" size="40"></u-icon>
					</view>
				</u-grid-item>
				<!--#endif -->
			</u-grid>
		</view>
		
		<u-action-sheet :list="dialog.add.list" v-model="dialog.add.show" :safe-area-inset-bottom="true" @close="fetchDatas">
			<template v-slot="{ curr }">
				<u-button shape="circle" class="scan-code-btn" size="medium" open-type="share"
				:custom-style="{ 'width': '100%', 'color': 'white', 'background-color': '#01b09a' }"  hover-class="none">
					<image src="/static/images/icon-wx-pay.png" mode="aspectFill" style="width: 80rpx; height: 80rpx; vertical-align: middle;"></image>
					<view style="display: inline-block; vertical-align: middle;">{{ curr.item.text }}</view>
				</u-button>
				<!-- <image src="/static/images/icon-wx-pay.png" mode="aspectFill" style="width: 80rpx; height: 80rpx;"></image>
				<view style="text-align: center;">{{ curr.item.text }}</view> -->
			</template>
		</u-action-sheet>
		
	</view>
</template>

<script>
	export default {
		data() {
			return {
				members: [],
				dialog: {
					add: {
						list: [{
							text: '微信'
						}],
						show: false
					}
				},
				currFamilyId: undefined,
				family: {}
			}
		},
		onShareAppMessage(res) {
			let _member = uni.getStorageSync('member');
			this.dialog.add.show=false;
			let _refName = _member.name || _member.nickname;
			if (!_refName) {
				_refName = _member.phone
			}
			let time = new Date().getTime();
			return {
				title: '邀请你一起加入我的家庭',
				path: `pages/invitation/index?familyId=${this.currFamilyId}&time=${time}&familyName=${this.family.name || ''}&name=${ _refName }`,
				imageUrl: this.$u.http.config.staticBaseUrl + '/images/home/<USER>',
				// desc:this.share.desc,
				// content:this.share.content,
				success(res){
					uni.showToast({
						title:'分享成功'
					})
				},
				fail(res){
					uni.showToast({
						title:'分享失败',
						icon:'none'
					})
				}
			}
		},
		onLoad(option) {
			this.currFamilyId = option.id;
			this.fetchDetail(option.id);
		},
		onShow() {
			console.log(1234)
			this.fetchDatas();
		},
		methods: {
			fetchDatas() {
				this.$u.api.fetchFamilyMemberList({ familyId: this.currFamilyId }).then(res => {
					this.members = res || []
				})
			},
			fetchDetail(id) {
				this.$u.api.fetchFamilyDetail({ familyId: id }).then(res => {
					this.family = res || {};
				})
			},
		}
	}
</script>

<style lang="scss">
page {
	background-color: #f7f7f7;
}
</style>
<style lang="scss" scoped>
.family-member-page {
	.title {
		padding-left: 10rpx;
		color: #6B6B6B;
		font-size: 24rpx;
		border-bottom: 2rpx solid #f1f1f1;
		padding: 20rpx 34rpx 30rpx;
	}
}
		
::v-deep .u-grid-item {
	background: transparent !important;
	height: 180rpx;
	&-box {
		padding: 0rpx 12rpx 0rpx 12rpx !important;
	}
}
.grid-add, .grid-minus {
	width: 120rpx;
	height: 120rpx;
	border: 2rpx dashed #99999F;
    border-radius: 14rpx;
    text-align: center;
    display: flex;
    justify-content: center;
}

::v-deep .u-drawer-content {
	bottom: 20rpx !important;
	left: 20rpx !important;
	right: 20rpx !important;
	width: initial !important;
	background-color: transparent !important;
}
::v-deep .u-gab {
	background-color: transparent !important;
}
::v-deep .u-action-sheet-item {
	border-radius: 10rpx;
	background: white;
	font-size: 28rpx !important;
	padding: 26rpx 0 !important;
	// &.u-actionsheet-cancel {
		
	// }
}
::v-deep button {
	&::after {
		border: 0rpx !important;
	}
}
.scan-code-btn {
	::v-deep button {
		width: 700rpx;
		display: block;
		text-align: center;
	}
}
.grid-text {
	word-break: break-all;
	display: -webkit-box;
	-webkit-line-clamp: 1;
	-webkit-box-orient: vertical;
	overflow: hidden;
	font-size: 25rpx;
}
</style>
