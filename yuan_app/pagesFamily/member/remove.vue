<template>
	<view class="family-member-remove-page">
		<view class="top-header-line"></view>
		
		<u-index-list v-if="indexList" :scrollTop="scrollTop">
			<view v-for="(item, index) in indexList" :key="index">
				<u-index-anchor :index="item" />
				<view v-for="(childItem, childIndex) in userMapping[item]" :key="childIndex" class="list-cell">
					<text v-if="userSelectRdo === childItem.id" class="icon iconfont icon-xuanzhong radio" style="color: #01B09A" @click="userSelectRdo = undefined"></text>
					<text v-else class="icon iconfont icon-weixuanzhong radio" @click="userSelectRdo = childItem.id"></text>
					<image v-if="childItem.profile" :src="childItem.profile" mode="aspectFill" style="width: 60rpx; height: 60rpx;"></image>
					<u-avatar v-else src="http://asdasdasdasd.svdsdvsdvsdv.png" mode="square" :size="52" style="vertical-align: middle;"></u-avatar>
					<text v-if="childItem.name || childItem.nickname" style="margin-left: 20rpx;">{{ childItem.name || childItem.nickname }}</text>
					<text v-else style="margin-left: 20rpx;">{{ childItem.phone || '' }}</text>
				</view>
			</view>
		</u-index-list>
		
		<view class="safe-area-inset-bottom">
			<view class="footer-btns">
				<u-button shape="circle" class="scan-code-btn diy-btn" size="medium" 
				:custom-style="{ 'width': '100%', 'color': '#DE4D4A', 'background-color': '#F6F6F6' }"  hover-class="none"
				@click="handleDel">删除成员</u-button>
			</view>
		</view>
		
	</view>
</template>

<script>
	export default {
		data() {
			return {
				scrollTop: 0,
				indexList: [],
				userMapping: {},
				userSelectRdo: undefined,
				currFamilyId: undefined,
			}
		},
		onPageScroll(e) {
			this.scrollTop = e.scrollTop;
		},
		onLoad(option) {
			this.currFamilyId = option.id;
			this.fetchDatas(option.id);
		},
		methods: {
			fetchDatas(id) {
				this.$u.api.fetchFamilyMemberList({ familyId: id }).then(res => {
					let _member = uni.getStorageSync('member');
					this.members = (res || []).filter(f => f.id !== _member.id);
					
					let _mapping = {};
					this.members.map(m => {
						let _key = m.firstLetter.toUpperCase() || '☆';
						if (!_mapping[_key]) {
							_mapping[_key] = [ m ]
						} else {
							_mapping[_key].push(m);
						}
					})
					this.userMapping = _mapping;
					this.indexList = Object.keys(_mapping);
				})
			},
			handleDel() {
				if (!this.userSelectRdo) {
					this.$toast('请选择待删除成员');
					return;
				}
				let _member = uni.getStorageSync('member');
				if (_member.id === parseInt(this.userSelectRdo)) {
					this.$toast('不能删除自身');
					return;
				}
				this.$u.api.execRemoveFamilyMember({ familyId: this.currFamilyId, memberId: this.userSelectRdo }).then(res => {
					this.$toast('删除成功');
					setTimeout(() => {
						uni.navigateBack({
							delta: 1
						})
					}, 500)
				})
				
			}
		}
	}
</script>

<style lang="scss">
page {
	background-color: white;
	padding-bottom: 120rpx;
}
::v-deep .u-round-circle {
	&::after {
		border: none !important;
	}
}
</style>
<style lang="scss" scoped>
@import url("/static/css/iconfont.css");
.family-member-remove-page {
	.list-cell {
		display: flex;
		box-sizing: border-box;
		width: 100%;
		padding: 16rpx;
		overflow: hidden;
		color: #323233;
		font-size: 24rpx;
		line-height: 58rpx;
		background-color: #fff;
		.radio {
			position: relative;
			top: -2rpx;
			margin-right: 10rpx;
			font-size: 36rpx;
			vertical-align: middle;
		}
	}
	.footer-btns {
		position: fixed;
		bottom: 122rpx;
		left: 54rpx;
		right: 54rpx;
		margin: 0 auto;
		.scan-code-btn {
			display: block;
			margin-top: 37rpx;
		}
	}
}
::v-deep .u-avatar {
	vertical-align: middle;
}
.top-header-line {
    z-index: 1000;
}
</style>
