/*
  * hejia-jssdk v1.5.0 (http://open.home.komect.com/dev/index.jsp)
  * @Copyright 2014-2019 The CMCC HY Authors (http://hy.10086.cn/)
  * @Licensed under ISC (https://opensource.org/licenses/isc)
  */
!function(){"use strict";"function"!=typeof Object.assign&&Object.defineProperty(Object,"assign",{value:function(e,n){if(null==e)throw new TypeError("Cannot convert undefined or null to object");for(var i=Object(e),t=1;t<arguments.length;t++){var r=arguments[t];if(null!=r)for(var a in r)Object.prototype.hasOwnProperty.call(r,a)&&(i[a]=r[a])}return i},writable:!0,configurable:!0});var I=function(e,n,i){return n in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i,e};var i=null,b=null,C={getUrlParam:function(e,n){if(!e)throw new Error("Hejia.utils.getUrlParam parameter url is required.");if(!n)throw new Error("Hejia.utils.getUrlParam parameter key is required.");return decodeURIComponent(e).replace(new RegExp("^(?:.*[&\\?]"+encodeURIComponent(n).replace(/[\.\+\*]/g,"\\$&")+"(?:\\=([^&]*))?)?.*$","i"),"$1").split("#")[0]},isNullOrUndefined:function(e){return null==e},isObject:function(e){return null!=e&&"[object Object]"===Object.prototype.toString.call(e)},isFunction:function(e){return null!=e&&"[object Function]"===Object.prototype.toString.call(e)}};function n(e){var t,r,a,o,l,n,c,s,u,d,f,g,p,w,v,O,m;i=e,window.Hejia=Object.assign(window.Hejia,(c=i,s=C,u="phoneNumber",d="area",f="loginType",g="account",p="gatewayMAC",w="gatewaySN",v="deviceId",O="appVersion",m="token",{getPhoneNumber:function(n,i){c.callHandler("getInfomation",{key:u},function(e){e?s.isFunction(n)&&n(e):i()})},getUserArea:function(n,i){c.callHandler("getInfomation",{key:d},function(e){e?s.isFunction(n)&&n(e):i()})},getLoginType:function(n,i){c.callHandler("getInfomation",{key:f},function(e){s.isNullOrUndefined(e)?i():(e=e.toString(),s.isFunction(n)&&n(e))})},getAccount:function(n,i){c.callHandler("getInfomation",{key:g},function(e){e?s.isFunction(n)&&n(e):i()})},getGatewayMAC:function(n,i){c.callHandler("getInfomation",{key:p},function(e){e?s.isFunction(n)&&n(e):i()})},getGatewaySN:function(n,i){c.callHandler("getInfomation",{key:w},function(e){e?s.isFunction(n)&&n(e):i()})},getDeviceId:function(n,i){c.callHandler("getInfomation",{key:v},function(e){e?s.isFunction(n)&&n(e):i()})},getAppVersion:function(n,i){c.callHandler("getInfomation",{key:O},function(e){e?s.isFunction(n)&&n(e):i()})},getToken:function(n,i){c.callHandler("getInfomation",{key:m},function(e){e?s.isFunction(n)&&n(e):i()})},getApiKey:function(i,t){c.callHandler("getApiKey",null,function(e){var n=s.isNullOrUndefined(e)?{}:window.JSON.parse(e);n.apiKey?s.isFunction(i)&&i(n.apiKey):t()})},addLog:function(e){c.callHandler("addLog",e)},isAppInstalled:function(e,n,i){c.callHandler("isAppInstalled",e,function(e){"1"==(e=e.toString())?s.isFunction(n)&&n(e):i(e)})}}),(n=i,{setToolbarItems:function(e){n.callHandler("setToolbarItems",e)},openUrl:function(e){n.callHandler("openUrl",e)},closeWebView:function(){n.callHandler("closeWebView")},refreshWebView:function(){n.callHandler("refreshWebView")}}),(t=i,r=1,a=2,o=4,l=5,{shareToWeChatMessage:function(e,n,i){t.callHandler("shareImageToOtherAPP",Object.assign(e,{platformType:r}),function(e){"1"==e?n(e):i(e)})},shareToWeChatTimeLine:function(e,n,i){t.callHandler("shareImageToOtherAPP",Object.assign(e,{platformType:a}),function(e){"1"==e?n(e):i(e)})},shareToQQ:function(e,n,i){t.callHandler("shareImageToOtherAPP",Object.assign(e,{platformType:o}),function(e){"1"==e?n(e):i(e)})},shareToQZone:function(e,n,i){t.callHandler("shareImageToOtherAPP",Object.assign(e,{platformType:l}),function(e){"1"==e?n(e):i(e)})}}),function(r,o){var a=0;function e(r,a){return function(){var e=null,n=null,i=null,t=Array.prototype.slice.call(arguments,0);return o.isObject(t[0])||(n=t[0],e=t[1],i={}),a&&(i=Object.assign({},I({},a,o.getUrlParam(window.location.href,a)),i||t[0])),r.call(this,i,n||t[1],e||t[2])}}return{checkIfReadyForAndlink:function(i,t){r.callHandler("checkIfReadyForAndlink",null,function(e){var n=o.isNullOrUndefined(e)?{}:window.JSON.parse(e);parseInt(n.resultCode,10)===a?o.isFunction(i)&&i(n.resultCodeMessage):o.isFunction(t)&&t(n.resultCodeMessage,n)})},startAndlink:function(e,i,t){o.isObject(e)||(t=i,i=e,e={});var n=Object.assign({},{deviceType:o.getUrlParam(window.location.href,"deviceType")},e);r.callHandler("startAndlink",n,function(e){var n=o.isNullOrUndefined(e)?{}:window.JSON.parse(e);parseInt(n.resultCode,10)===a?o.isFunction(i)&&i(n.deviceId):o.isFunction(t)&&t(n.resultCodeMessage,n)})},updateAndlinkPlugin:function(e,i,t){o.isObject(e)||(t=i,i=e,e={}),r.callHandler("updateAndlinkPlugin",e,function(e){var n=o.isNullOrUndefined(e)?{}:window.JSON.parse(e);parseInt(n.resultCode,10)===a?o.isFunction(i)&&i(n.resultCodeMessage):o.isFunction(t)&&t(n.resultCodeMessage,n)})},getCurrentParam:function(e,i,t){o.isObject(e)||(t=i,i=e,e={});var n=Object.assign({},{deviceId:o.getUrlParam(window.location.href,"deviceId")},e);r.callHandler("fetchDeviceCurrentParam",n,function(e){var n=o.isNullOrUndefined(e)?{}:window.JSON.parse(e);parseInt(n.resultCode,10)===a?o.isFunction(i)&&i(n.devices[0]||{}):o.isFunction(t)&&t(n.resultCodeMessage,n)})},getHistoryParam:function(e,i,t){var n=Object.assign({},{deviceId:o.getUrlParam(window.location.href,"deviceId")},e);r.callHandler("fetchDeviceHistoryParam",n,function(e){var n=o.isNullOrUndefined(e)?{}:window.JSON.parse(e);parseInt(n.resultCode,10)===a?o.isFunction(i)&&i(n.readings||[]):o.isFunction(t)&&t(n.resultCodeMessage,n)})},getAlarmParam:function(e,i,t){var n=Object.assign({},{deviceId:o.getUrlParam(window.location.href,"deviceId")},e);r.callHandler("fetchDeviceAlarmParam",n,function(e){var n=o.isNullOrUndefined(e)?{}:window.JSON.parse(e);parseInt(n.resultCode,10)===a?o.isFunction(i)&&i(n.alerts||[]):o.isFunction(t)&&t(n.resultCodeMessage,n)})},setControlParam:function(e,i,t){var n=Object.assign({},{deviceId:o.getUrlParam(window.location.href,"deviceId")},e);r.callHandler("fetchDeviceControlParam",n,function(e){var n=o.isNullOrUndefined(e)?{}:window.JSON.parse(e);parseInt(n.resultCode,10)===a?o.isFunction(i)&&i():o.isFunction(t)&&t(n.resultCodeMessage,n)})},getDeviceInfo:e(function(e,i,t){r.callHandler("fetchDeviceInfo",e,function(e){var n=o.isNullOrUndefined(e)?{}:window.JSON.parse(e);parseInt(n.resultCode,10)===a?o.isFunction(i)&&i({device:n.device,location:n.location}):o.isFunction(t)&&t(n.resultCodeMessage,n)})},"deviceId"),getRuleElements:function(i,t){r.callHandler("requestRuleElements",null,function(e){var n=o.isNullOrUndefined(e)?{}:window.JSON.parse(e);parseInt(n.resultCode,10)===a?o.isFunction(i)&&i({triggers:n.triggers||[],states:n.states||[],actions:n.actions||[]}):o.isFunction(t)&&t(n.resultCodeMessage,n)})},getUserRuleList:e(function(e,i,t){e.details=!0,r.callHandler("getUserRuleList",e,function(e){var n=o.isNullOrUndefined(e)?{}:window.JSON.parse(e);parseInt(n.resultCode,10)===a?o.isFunction(i)&&i({rules:n.rules||[]}):o.isFunction(t)&&t(n.resultCodeMessage,n)})},"deviceId"),createRule:function(e,i,t){r.callHandler("createRule",e,function(e){var n=o.isNullOrUndefined(e)?{}:window.JSON.parse(e);parseInt(n.resultCode,10)===a?o.isFunction(i)&&i(n):o.isFunction(t)&&t(n.resultCodeMessage,n)})},updateRule:function(e,i,t){r.callHandler("updateRule",e,function(e){var n=o.isNullOrUndefined(e)?{}:window.JSON.parse(e);parseInt(n.resultCode,10)===a?o.isFunction(i)&&i():o.isFunction(t)&&t(n.resultCodeMessage,n)})},updateRuleAttrRsp:function(e,i,t){r.callHandler("updateRuleAttrRsp",e,function(e){var n=o.isNullOrUndefined(e)?{}:window.JSON.parse(e);parseInt(n.resultCode,10)===a?o.isFunction(i)&&i(n):o.isFunction(t)&&t(n.resultCodeMessage,n)})},deleteRule:function(e,i,t){r.callHandler("deleteRule",e,function(e){var n=o.isNullOrUndefined(e)?{}:window.JSON.parse(e);parseInt(n.resultCode,10)===a?o.isFunction(i)&&i(n):o.isFunction(t)&&t(n.resultCodeMessage,n)})},onMessage:function(i){function e(e){var n=JSON.parse(e);"function"==typeof i&&i.call(this,n)}return r.registerHandler("onMessage",e),window.onMessage=e},getSonDeviceInfo:function(e,i,t){o.isObject(e)||(t=i,i=e,e={});var n=Object.assign({},{deviceId:o.getUrlParam(window.location.href,"deviceId"),deviceTypeId:o.getUrlParam(window.location.href,"deviceType")},e);r.callHandler("getSonDeviceInfo",n,function(e){var n=o.isNullOrUndefined(e)?{}:window.JSON.parse(e);parseInt(n.resultCode,10)===a?o.isFunction(i)&&i(n.devices||{}):o.isFunction(t)&&t(n.resultCodeMessage,n)})},registerDevice:function(e,i,t){var n=Object.assign({},{deviceId:o.getUrlParam(window.location.href,"deviceId"),deviceTypeId:o.getUrlParam(window.location.href,"deviceType")},e);r.callHandler("registerDevice",n,function(e){var n=o.isNullOrUndefined(e)?{}:window.JSON.parse(e);parseInt(n.resultCode,10)===a?o.isFunction(i)&&i(n||{}):o.isFunction(t)&&t(n.resultCodeMessage,n)})},unBindDevice:function(e,i,t){var n=Object.assign({},{deviceId:o.getUrlParam(window.location.href,"deviceId"),deviceTypeId:o.getUrlParam(window.location.href,"deviceType")},e);r.callHandler("unBindDevice",n,function(e){var n=o.isNullOrUndefined(e)?{}:window.JSON.parse(e);parseInt(n.resultCode,10)===a?o.isFunction(i)&&i():o.isFunction(t)&&t(n.resultCodeMessage,n)})}}}(i,C)),b&&b()}window.Hejia=window.Hejia||{ready:function(e){b=C.isFunction(e)?e:function(){},function(e){if(window.WebViewJavascriptBridge)return e(window.WebViewJavascriptBridge);if(document.addEventListener("WebViewJavascriptBridgeReady",function(){e(window.WebViewJavascriptBridge)},!1),window.WVJBCallbacks)return window.WVJBCallbacks.push(e);window.WVJBCallbacks=[e];var n=document.createElement("iframe");n.style.display="none",n.src="https://__bridge_loaded__",document.documentElement.appendChild(n),setTimeout(function(){document.documentElement.removeChild(n)},0)}(function(e){n(e)})},utils:C}}();