// errorCodes.js

// 定义错误代码（字符串）和描述的映射关系
export const errorCodeMessages = {
	"99": "未知错误",
	"400": "请求参数不正确",
	"404": "请求未找到",
	"3001000": "固话系统系统内部错误",
	"3001001": "当前账号未开户",
	"3001003": "验证码请求过于频繁，请稍后再试",
	"3001006": "绑定关系不存在",
	"3001007": "验证码不正确",
	"3001009": "重复绑定，号码已经与该设备绑定",
	"3001011": "TUI设备前缀码不存在",
	"3001012": "秘钥不存在",
	"3001020": "解绑失败",
	"3001019": "绑定失败",
	"3001022": "参数非法，解密失败",
	"3001023": "设备重复绑定，该设备已与其他号码绑定，请先解绑",
	"3001024": "号码重复绑定，该号码已与其他设备绑定，请先解绑",
	"3001027": "手机号码未开通智能固话业务",
};

/**
 * 根据错误代码获取描述信息
 * @param {string} code - 错误代码（字符串）
 * @returns {string} - 描述信息
 */
export function getErrorMessage(code) {
	return errorCodeMessages[code]+":"+code || "错误码："+code;
}