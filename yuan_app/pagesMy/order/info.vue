<template>
	<view class="my-order-info-page">
		<view class="top-header-line"></view>
		<view class="item-block">
			<u-row>
				<u-col span="4">
					<view class="left-title">
						<view class="device-name">订单编号</view>
					</view>
				</u-col>
				<u-col span="8">
					<view class="right-val"  @longtap="longtapCopy(orderInfo.orderNo)">
						{{ orderInfo.orderNo || '' }}
					</view>
				</u-col>
			</u-row>
			<u-row>
				<u-col span="4">
					<view class="left-title">
						<view class="device-name">订单名称</view>
					</view>
				</u-col>
				<u-col span="8">
					<view class="right-val">
						{{ orderInfo.orderName || '' }}
					</view>
				</u-col>
			</u-row>
			<u-row>
				<u-col span="4">
					<view class="left-title">
						<view class="device-name">设备名称</view>
					</view>
				</u-col>
				<u-col span="8">
					<view class="right-val">
						{{ (orderInfo.devName || orderInfo.devCode) || '' }}
					</view>
				</u-col>
			</u-row>
			<u-row>
				<u-col span="4">
					<view class="left-title">
						<view class="device-name">创建时间</view>
					</view>
				</u-col>
				<u-col span="8">
					<view class="right-val">
						{{ orderInfo.createTime || '' }}
					</view>
				</u-col>
			</u-row>
		</view>
		
		<view class="item-block">
			<u-row>
				<u-col span="4">
					<view class="left-title">
						<view class="device-name">支付状态</view>
					</view>
				</u-col>
				<u-col span="8">
					<view class="right-val" :style="{ 'color': dict.statusColor[orderInfo.payStatus] || '#999' }">
						{{ orderInfo.payStatusCName || '' }}
					</view>
				</u-col>
			</u-row>
			<u-row>
				<u-col span="4">
					<view class="left-title">
						<view class="device-name">支付金额</view>
					</view>
				</u-col>
				<u-col span="8">
					<view class="right-val price">
						¥ {{ orderInfo.totalAmount || '' }}
					</view>
				</u-col>
			</u-row>
		</view>
		
		<view v-if="paySecond > 0 && orderInfo.payStatus === '1'" class="item-block">
			<u-row>
				<u-col span="12">
					<view class="left-title">
						<view class="device-name" style="font-size: 24rpx;">
							订单将在 <u-count-down :timestamp="paySecond" color="red" style="margin: 0rpx 20rpx;" @end="handleEndEvent"></u-count-down> 之后自动取消, 请尽快支付
						</view>
					</view>
				</u-col>
			</u-row>
		</view>
		<view v-if="paySecond > 0 && orderInfo.payStatus === '1'" style="position: absolute; bottom: 20rpx; left: 20rpx; right: 20rpx;">
			<u-button type="warning" size="medium" style="width: 100%" @click="execPrepay">
				继续支付
			</u-button>
		</view>
	</view>
</template>

<script>
	import {longtapCopy} from '../../utils/util'
	export default {
		data() {
			return {
				dict: {
					statusColor: {
						'1': '#ff3609',
						'2': '#25c525',
						'3': '#999',
					}
				},
				orderId: undefined,
				orderInfo: {},
				paySecond: 0,
			}
		},
		onLoad(option) {
			this.orderId = option.id;
		},
		onShow() {
			this.paySecond = 0;
			this.fetchOrderInfo(this.orderId)
			console.log('come show');
		},
		methods: {
			longtapCopy,
			gotoUrl(url){
				if (!url) return;
				uni.navigateTo({
					url: url
				})
			},
			fetchOrderInfo(id) {
				uni.showLoading({
					title: '加载中...',
					mask: true
				})
				this.$u.api.fetchOrderDetail({ id: id }).then(res => {
					if (res) {
						this.orderInfo = res
						if (res.payStatus === '1') {
							var newDate = new Date(res.createTime.replace(/-/g, '/'));
							var addMinute = new Date(newDate.setMinutes(newDate.getMinutes() + 15));
							this.paySecond = parseInt(parseInt(addMinute - new Date()) / 1000)
						}
					}
				}).catch(err => {
					this.orderInfo = {}
				}).finally(() => {
					setTimeout(() => {
						uni.hideLoading();
					}, 1000)
				})
			},
			execPrepay(orderId) {
				if (!this.orderInfo || !this.orderInfo.id) return;
				var newDate = new Date(this.orderInfo.createTime.replace(/-/g, '/'));
				var addMinute = new Date(newDate.setMinutes(newDate.getMinutes() + 15));
				let _paySecond = parseInt(parseInt(addMinute - new Date()) / 1000)
				if (_paySecond <= 0) {
					uni.showToast({
						duration: 2000,
						title: '已超过付款时限',
						icon: 'none'
					})
					return;
				}
				if (!this.orderInfo.id) return;
				this.$u.api.execPrepay({
					orderId: this.orderInfo.id
				}).then(res => {
					this.execPay(this.orderInfo.id, res);
				}).catch(err => {
					uni.showToast({
						duration: 2000,
						title: err.data.msg || '获取预下单失败',
						icon: 'none'
					})
				})
			},
			execPay(orderId, reqParams) {
				if (!reqParams.paySign) {
					uni.showToast({
						duration: 2000,
						title: '缺少支付必要条件',
						icon: 'none'
					})
					return;
				}
				let that = this;
				wx.requestPayment({
					timeStamp: reqParams.timeStamp,
					nonceStr: reqParams.nonceStr,
					package: reqParams.package_,
					signType: reqParams.signType,
					paySign: reqParams.paySign,
					success(res) {
						uni.showLoading({
							mask: true,
							title: '支付中...'
						})
						that.payTimer = setInterval(() => {
							that.fetchPayStatus(orderId)
						}, 2500)
					},
					fail(res) {
						console.log(res)
					}
				})
			},
			fetchPayStatus(orderId) {
				if (!orderId) return;
				this.$u.api.fetchPayStatus({
					id: orderId, needTip: false
				}).then(res => {
					if (res.tradeState === 'SUCCESS') {
						uni.hideLoading();
						if (this.payTimer) {
							clearTimeout(this.payTimer);
							this.payTimer = null;
						}
						uni.navigateBack({
							delta: 1
						})
					}
				}).catch(err => { })
			},
			handleEndEvent() {
				this.$u.api.execCancelOrder({ id: this.orderId }).then(res => {
					this.fetchOrderInfo(this.orderId)
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
.my-order-info-page {
	padding: 29rpx;
	padding-bottom: 190rpx;
	.top-title {
		font-size: 36rpx;
		color: #333;
		margin-top: 40rpx;
		margin-bottom: 24rpx;
	}
	.item-block {
		padding: 24rpx 30rpx;
		color: $u-content-color;
		font-size: 28rpx;
		box-shadow: 0rpx 8rpx 24rpx 0rpx rgba(0, 0, 0, 0.1);
		border-radius: 16rpx;
		//#ifdef H5
		line-height: 70rpx;
		//#endif
		.left-title {
			.device-name {
				color: #0D0D0D;
				font-size: 32rpx;
			}
		}
		.right-val {
			color: #666;
			text-align: right;
			.activate-now {
				padding: 10rpx 28rpx;
				background: #EDEFF9;
				border-radius: 45rpx;
				color: #4166F5;
				font-size: 26rpx;
				line-height: initial;
				text-align: center;
				margin-top: 10rpx;
				display: inline-block;
			}
			::v-deep .u-icon {
				margin-left: 10rpx;
				position: relative;
				top: -2rpx;
			}
		}
		.price {
			color: #f59a23;
		}
		&:nth-child(n + 2) {
			margin-top: 20rpx;
		}
	}
	u-row {
		line-height: 80rpx;
	}
	.footer-btns {
		position: fixed;
		bottom: 40rpx;
		left: 60rpx;
		right: 60rpx;
		margin: 0 auto;
		.next-btn {
			display: block;
			margin-top: 20rpx;
		}
	}
	u-button {
		::v-deep button {
			width: 100%;
		}
	}
}
</style>
