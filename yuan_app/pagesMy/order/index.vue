<template>
	<view class="my-order-index-page">
		<view class="top-header-line"></view>
		<scroll-view scroll-y style="height: 100%; width: 100%;" @scrolltolower="reachBottom">
			<view class="r-flex" style="padding: 26rpx; padding-bottom: 0rpx;">
				<view class="r-flex-1">
					<u-input v-model="devNameOrCode" :border="true" placeholder="请输入设备名称或编号"
						placeholder-style="color: #bbbbbb" />
				</view>
				<view class="ta-r" style="width: 100rpx;">
					<u-icon name="calendar" :color="date ? '#01B09A' : '#bbb'" size="70"
						@click="dialog.calendar.show = true"></u-icon>
				</view>
			</view>
			<view class="content">
			<u-empty v-if="!days.length" text="暂无订单数据" mode="list"></u-empty>
				<view v-else v-for="(day, dayIndex) in days" :key="dayIndex">
					<view style="font-size: 30rpx; color: #000; margin-bottom: 30rpx;"
						:style="{ 'margin-top': dayIndex === 0 ? '0rpx' : '30rpx' }">{{ day }}</view>
					<view class="order-item" v-for="(order, index) in dayMapping[day]" :key="index"
						@click="$navTo(`pagesMy/order/info?id=${order.id}`)">
						<view class="item-title">
							<view class="title">{{ order.orderName }}</view>
							<view class="status">{{ order.totalAmount }} 元</view>
						</view>
						<view v-if="order.devName || order.devCode" class="item-content"
							style="margin-top: 6px; padding-bottom: 8px;">
							<view class="address">
								{{ (order.devName || order.devCode) || '' }}
							</view>
						</view>
						<view v-if="order.olderNames" class="item-content"
							style="margin-top: 6px; padding-bottom: 8px;flex-flow: column;align-items: stretch;">
							<view class="address" v-for="(name, index) in order.olderNames" :key="index"
								style="margin-top: 6px">
								{{ name }}
							</view>
						</view>
						<view class="item-content">
							<!-- <view class="avatar">
								<image v-if="tabIndex === 2" class="alarm-icon" src="../../static/images/alarm-blue-icon.png"></image>
								<image v-else class="alarm-icon" src="../../static/images/alarm-icon.png"></image>
							</view> -->
							<view class="address">
								{{ order.createTime }}
							</view>
							<view class="relationship">
								<view class="status" :style="{ 'color': dict.statusColor[order.payStatus] || '#999' }">
									{{ order.payStatusCName }}</view>
							</view>
						</view>

						<view v-if="order.payStatus == 1" class="item-content">
							<view class="address"></view>
							<view class="relationship">
								<u-button shape="circle" size="mini" plain :custom-style="{ 'color': '#01B09A' }"
									hover-class="none" @click.stop="cancelOrder(day, index)">取消订单</u-button>
							</view>
						</view>
					</view>
				</view>
				<u-loadmore v-if="days && orderCount >= 10" :status="loadStatus" class="loadmore" />
			</view>
		</scroll-view>
		<u-calendar v-model="dialog.calendar.show" mode="date" btn-type="success" active-bg-color="#01B09A"
			@change="handleDateChange"></u-calendar>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				dialog: {
					calendar: {
						show: false
					}
				},
				dict: {
					statusColor: {
						'1': '#ff3609',
						'2': '#81D88A',
						'3': '#999',
					}
				},
				loadStatus: 'loadmore',
				devNameOrCode: '',
				page: 1,
				days: [],
				dayMapping: {},
				date: '',
				todayYmd: this.$u.timeFormat(new Date(), 'yyyy-mm')
			}
		},
		computed: {
			orderCount() {
				let _count = 0;
				if (this.days && this.days.length) {
					Object.values(this.dayMapping).map(m => {
						_count += m.length;
					})
				}
				return _count
			}
		},
		onShow() {
			this.page = 1;
			this.days = [];
			this.dayMapping = {};
			uni.showLoading({
				title: '加载中...',
				mask: true
			})
			this.fetchDatas();
		},
		methods: {
			handleDateChange(day) {
				this.date = day.result;
				this.handleSearch();
			},
			handleSearch() {
				this.page = 1;
				this.days = [];
				this.dayMapping = {};
				this.fetchDatas();
			},
			gotoUrl(url) {
				if (!url) return;
				uni.navigateTo({
					url: url
				})
			},
			reachBottom() {
				if (this.page >= 1) {
					this.loadStatus = 'loading';
					setTimeout(() => {
						this.page = this.page + 1;
						this.fetchDatas();
					}, 1200);
				}
			},
			fetchDatas() {
				let _days = [];
				let _dayMapping = {};
				this.$u.api.fetchOrderList({
					current: this.page,
					devNameOrCode: this.devNameOrCode,
					date: this.date
				}).then(res => {
					res.records.map(m => {
						let _tempDay = this.$u.timeFormat(m.createTime, 'yyyy-mm')
						if (!this.dayMapping[_tempDay]) {
							this.days.push(_tempDay);
							this.dayMapping[_tempDay] = [];
						}
						this.dayMapping[_tempDay].push(m);
					})
					uni.hideLoading();
					this.loadStatus = 'loadmore'
				})
			},
			reachBottom() {
				if (this.page >= 1) {
					this.loadStatus = 'loading';
					setTimeout(() => {
						this.page = this.page + 1;
						this.fetchDatas();
					}, 1200);
				}
			},
			cancelOrder(day, index) {
				const that = this;
				const order = that.dayMapping[day][index];
				const {
					id
				} = order;
				uni.showModal({
					content: '确认取消订单？',
					showCancel: true,
					success: ({
						confirm,
						cancel
					}) => {
						if (cancel === true) {
							return;
						}
						that.$u.api.execCancelOrder({
							id
						}).then(res => {
							order.payStatus = 3;
							order.payStatusCName = '取消订单';
							that.days = [...that.days];
						}).catch(err => console.error(err))
					}
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	.my-order-index-page {
		height: 100vh;
		//#ifdef H5
		height: 100%;

		//#endif
		.content {
			// margin-top: 20upx;
			background: #fff;
			padding: 22upx 26upx;
			box-sizing: border-box;
			margin-bottom: 30upx;
			padding-bottom: 20rpx;

			.title-wrap {
				position: relative;

				.title {
					font-size: 36upx;
					color: #454444;
					text-align: left;
					font-weight: bold;
				}

				.right-more {
					position: absolute;
					top: 1upx;
					right: 0upx;
					font-size: 32upx;
					color: rgba(0, 0, 0, 0.45);
				}
			}

			.order-item {
				background: #FFFFFF;
				box-shadow: 0px 8upx 24upx 0px rgba(0, 0, 0, 0.1);
				border-radius: 16upx;
				// padding: 40upx;
				color: #333;
				display: flex;
				flex-direction: column;
				align-items: center;
				align-content: center;
				font-size: 30upx;

				.item-title {
					padding: 20upx;
					display: flex;
					flex-direction: row;
					font-size: 28upx;
					border-bottom: 2rpx solid #ececec;
					width: 100%;

					.title {
						flex: 1;
						font-size: 30rpx;
						color: #555658;
					}

					.status {
						width: 140upx;
						text-align: right;
						color: #ED2E1C;
						font-weight: bold;

						&.green {
							color: #47DF9B;
						}

						&.blue {
							color: #4166F5;
						}
					}
				}

				.item-content {
					width: 100%;
					padding: 30rpx 20upx;
					padding-top: 0rpx;
					font-size: 26rpx;
					// padding-top: 40upx;
					// padding-bottom: 50upx;
					display: flex;
					flex-direction: row;
					align-items: center;
					align-content: center;

					.avatar {
						width: 100upx;
						text-align: left;

						.alarm-icon {
							width: 82upx;
							height: 82upx;
							vertical-align: middle;
							position: relative;
							top: 4upx;
						}
					}

					.address {
						flex: 1;
						color: #5E5D5D;
					}

					.relationship {
						text-align: right;
						color: #5E5D5D;
						width: 120upx;
					}
				}

				&:nth-child(n + 2) {
					margin-top: 20upx;
				}
			}
		}

		::v-deep .u-load-more-wrap {
			background: transparent !important;

			.u-more-text {
				background: transparent !important;
			}
		}

		::v-deep .u-load-more-wrap {
			margin-top: 20upx !important;
		}
	}
</style>
