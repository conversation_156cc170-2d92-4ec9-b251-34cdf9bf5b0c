<template>
	<view class="container">
		<RecordNotFound v-if="!list || !list.length" :pic="`${staticUrl}/images/device/pic-connector-notfound.png`"
			desc="暂无数据"></RecordNotFound>
		<view v-else class="list">
			<view v-for="(item, index) in list" :key="index">
				<u-row class="item-block">
					<u-col span="9">
						<view class="r-flex ta-c">
							<text class="name">{{ item.appName }}</text>
							<text class="phone">{{ item.statusDesc }}</text>
						</view>
					</u-col>
					<u-col span="3">
						<text v-if="item.status==1" style="font-size: 30rpx; color: #01B09A;"
							@click.stop="authorize(item.thirdPartyUserId,'2','解除授权')">解除授权</text>
						<text v-else style="font-size: 30rpx; color: #01B09A;"
							@click.stop="authorize(item.thirdPartyUserId,'1','授权')">授权</text>
					</u-col>
				</u-row>
			</view>
		</view>
	</view>
</template>

<script>
	import RecordNotFound from '../components/record-not-found/index'
	export default {
		components: {
			RecordNotFound
		},
		data() {
			return {
				list: [],
			}
		},
		onShow() {
			this.fetchDatas();
		},
		methods: {
			authorize(id, status,name) {
				uni.showModal({
					content: `确定要${name}吗？`,
					showCancel: true,
					success: ({
						confirm,
						cancel
					}) => {
						if (cancel) {
							return;
						}

						uni.showLoading({
							title: '操作中...',
							mask: true
						})
						this.$u.api.authorizeThirdParty({
							thirdPartyUserId: id,
							status:status,
						}).then(res => {
							uni.showToast({
								duration: 1000,
								title: '操作成功',
								icon: 'none'
							})
							this.fetchDatas();
						}).catch(err => {
							console.log('err', err)
							uni.showToast({
								duration: 2000,
								title: err.message || '发生未知错误',
								icon: 'none'
							})
						}).finally(() => {
							setTimeout(() => {
								uni.hideLoading();
							}, 1000)
						})
					}
				})
			},
			gotoUrl(url) {
				if (!url) return;
				uni.navigateTo({
					url: url
				})
			},
			fetchDatas() {
				uni.showLoading({
					title: '加载中...',
					mask: true
				})
				this.$u.api.selectAuthorizeList({}).then(res => {
					if (res) {
						this.list = res
					}
				}).catch(err => {
					this.list = {}
				}).finally(() => {
					setTimeout(() => {
						uni.hideLoading();
					}, 1000)
				})
			},
		}
	}
</script>
<style lang="scss">
	page {
		background-color: #f7f7f7;
	}
</style>
<style lang="scss" scoped>
	@import url("/static/css/iconfont.css");

	.container {
		padding: 24rpx;

		.list {
			background: white;
			padding: 12rpx 12rpx 0;
			border-radius: 20rpx;
			

			.item-block {
				padding: 24rpx 12rpx;
				border-bottom: 2rpx solid #ececec;
				font-size: 32rpx;
				
				.name {
					font-weight: 700;
				}

				.phone {
					color: #999;
					font-size: 28rpx;
					margin-left: 20rpx;
				}

				.tag {
					margin-left: 20rpx;
				}

				.address {
					margin-top: 12rpx;
					font-size: 26rpx;
				}

				&:nth-child(n + 2) {
					margin-top: 20rpx;
				}
			}
		}

		.footer-btns {
			position: fixed;
			bottom: 40rpx;
			left: 40rpx;
			right: 40rpx;
			margin: 0 auto;

			.next-btn {
				display: block;
				margin-top: 20rpx;
			}
		}
	}
</style>
