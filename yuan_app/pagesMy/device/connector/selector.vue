<template>
	<view class="device-connector-selector-page">
		<view class="top-header-line"></view>
		
		<RecordNotFound v-if="rows.length === 0" :pic="`${staticUrl}/images/device/pic-connector-notfound.png`" desc="暂无紧急联系人"></RecordNotFound>
		
		<view v-for="(row, index) in rows" :key="index" class="item-block">
			<u-row @click="gotoMyEdit(row.id, row)">
				<u-col span="5">
					<view class="left-title" v-if="source === sourceEnum.my || source === sourceEnum.order || (source === sourceEnum.family && selfFamily===false)">
						{{ row.contactName }}
					</view>
					<view class="left-title" v-else @click="radioChange(row.id)">
						<text v-if="selected[row.id] && selected[row.id] !== 99999" class="icon iconfont icon-xuanzhong radio" style="color: #01B09A"></text>
						<text v-else class="icon iconfont icon-weixuanzhong radio"></text>
						<text style="margin-left: 14rpx;">{{ row.contactName }}</text>
					</view>
				</u-col>
				<u-col span="7">
					<view class="r-flex">
						<view class="right-val">
							{{ row.contactPhone || '-' }}
						</view>
						<view v-if="row.selfCreate && selfFamily===true" style="margin-left:20rpx;">
							<text class="icon iconfont icon-rizhiguanli" style="font-size: 48rpx; color: #01B09A;" @click.stop="gotoEdit(row.id)"></text>
							<view @click.stop="gotoRemove(row.id, index)" style="display:inline-block;margin-left: 20rpx;">
								<!-- 直接使用u-icon事件无法捕获事件,会产生冒泡 -->
								<u-icon name="trash" color="#01B09A" size="48"></u-icon>
							</view>
						</view>
					</view>
				</u-col>
			</u-row>
		</view>
		
		<view v-if="selfFamily===true" class="footer-btns">
			<u-button shape="circle" class="next-btn diy-btn" size="medium" 
			:custom-style="{ 'width': '100%', 'color': 'white', 'background-color': '#01B09A' }"  hover-class="none"
			@click="$navTo('pagesMy/device/connector/edit')">新增紧急联系人</u-button>
		</view>
	</view>
</template>

<script>
	import RecordNotFound from '../../components/record-not-found/index'
	export default {
		components: {
			RecordNotFound
		},
		data() {
			return {
				radioValue: undefined,
				deleteDialog: false,
				deviceName: '',
				deviceNameDialog: false,
				familyId: null,
				familyName: null,
				selected: {
					
				},
				sourceEnum: {
					"my": "my",
					"order": "order",
					"family": "family",
					"device": "device",
				},
				rows: [],
				source: undefined,
				selfFamily:true,
			}
		},
		onLoad(option) {
			this.source = option.source
			if (this.source == this.sourceEnum.order) {
				this.eventChannel = this.getOpenerEventChannel();
				if (option.excludeIds) {
					this.excludeIds = option.excludeIds.split(",");
				}
			}
			if(this.source == this.sourceEnum.family){
				this.selfFamily = JSON.parse(option.selfCreate)
				this.familyId = new Number(option.id);
				this.familyName = option.name;
			}
		},
		onShow() {
			this.fetchSelectedDatas();
		},
		methods: {
			gotoUrl(url){
				if (!url) return;
				uni.navigateTo({
					url: url
				})
			},
			gotoMyEdit(id, row) {
				if (this.source === this.sourceEnum.my) {
					//this.gotoEdit(id);
					return;
				}
				if (this.source === this.sourceEnum.order) {
					if (!this.$u.test.mobile(row.contactPhone)) {
						uni.showModal({ content: '联系人手机号无效，请编辑并填写', showCancel: false, confirmText: '关闭' })
						return;
					}
					this.eventChannel.emit('selected', { data: row});
					uni.navigateBack();
					return;
				}
			},
			gotoEdit(id) {
				this.$navTo('pagesMy/device/connector/edit?source=my&id=' + id)
			},
			gotoRemove(contactId, index) {
				uni.showModal({
					content: '删除紧急联系人？',
					showCancel: true,
					success: ({ confirm, cancel }) => {
						if (cancel) {
							return;
						}

						uni.showLoading({
							title: '删除中...',
							mask: true
						})
						this.$u.api.execContactMyDelete({ contactId }).then(res => {
							uni.showToast({ duration: 2000, title: '删除紧急联系人成功', icon: 'none' })
							this.rows.splice(index, 1);
						}).catch(err => {
							console.log('err', err)
							uni.showToast({ duration: 2000, title: err.message || '发生未知错误', icon: 'none' })
						}).finally(() => {
							setTimeout(() => {
								uni.hideLoading();
							}, 1000)
						})

					}
				})
			},
			radioChange(rowId) {
				if (this.selected[rowId] && this.selected[rowId] !== 99999) {
					this.handleSelectedItem(false, rowId)
					this.$set(this.selected, rowId, 99999)
				} else {
					this.handleSelectedItem(true, rowId)
					this.$set(this.selected, rowId, rowId)
				}
			},
			handleOpenNameDialog() {
				this.deviceNameDialog = true
				this.value = this.deviceInfo.devName
			},
			handleConfirmNameDialog() {
				this.deviceInfo.devName = this.value
				this.isChange = true
				this.submitRename();
			},
			handleOpenDeleteDialog() {
				this.deleteDialog = true
			},
			fetchDatas() {
				const { excludeIds } = this;
				this.$u.api.fetchContactMyList({ }).then(res => {
					if (res) {
						if (excludeIds?.length) {
							res = res.filter(item => !excludeIds.find(id => id == item.id))
						}
						this.rows = res;
					} else {
						this.rows = []
					}
				}).finally(() => {
					setTimeout(() => {
						uni.hideLoading();
					}, 1000)
				})
			},
			async fetchSelectedDatas() {
				uni.showLoading({
					title: '加载中...',
					mask: true
				})
				if (this.source === this.sourceEnum.my || this.source === this.sourceEnum.order) {
					this.fetchDatas();
				} else if(this.source === this.sourceEnum.family){
					let familyList = await this.$u.api.queryContactByFamilyId({familyId: this.familyId});
					if (familyList) {
						let _mapping = {};
						familyList.map(r => {
							this.$set(this.selected, r.id, r.id)
						})						
					}
					// 合并家庭的我的
					let myList = await this.$u.api.fetchContactMyList();
					let allArr = [].concat(myList,familyList);
					if(allArr){
						// 去重,自己的放到前面，有重复的时删除的就是家庭的
						let obj = {};
						let arrays = allArr.reduce((setArr, item) => {
							obj[item.id] ? '' : obj[item.id] = true && setArr.push(item);
							return setArr;
						}, []);
						this.rows = arrays;
					}
				} else {
					this.$u.api.fetchMyContactListByDeviceId({ devId: uni.getStorageSync('devId') }).then(res => {
						if (res) {
							let _mapping = {};
							res.map(r => {
								this.$set(this.selected, r.id, r.id)
							})
							this.fetchDatas();
						}
						setTimeout(() => {
							uni.hideLoading();
						}, 1000)
					}).catch(err => {
						setTimeout(() => {
							uni.hideLoading();
						}, 1000)
					})
				}
			},
			handleSelectedItem(flag, rowId) {
				uni.showLoading({
					title: '操作中...',
					mask: true
				})
				// 220916联系人与家庭绑定
				// let _api = flag ? this.$u.api.execDeviceBindHouseContact : this.$u.api.execDeviceunBindHouseContact;
				// _api({ devId: uni.getStorageSync('devId'), contactId: rowId }).then(res => {
				let _api;
				if(this.source == this.sourceEnum.family){
					_api = this.$u.api.bindContact2Family;
				}else if(this.source == this.sourceEnum.device){
					_api = this.$u.api.bindContact2Family4Device;
				}
				_api({ familyId: this.familyId,devId: uni.getStorageSync('devId'), contactId: rowId,isBind:flag}).then(res => {
					uni.showToast({ duration: 1000, title: !flag ? '取消成功' : '选择成功', icon: 'none' })
				}).catch(err => {
					console.log('err', err)
					uni.showToast({ duration: 2000, title: err.message || '发生未知错误', icon: 'none' })
				}).finally(() => {
					setTimeout(() => {
						uni.hideLoading();
					}, 1000)
				})
			}
		}
	}
</script>

<style lang="scss">
page {
	background-color: #f7f7f7;
}
</style>
<style lang="scss" scoped>
@import url("/static/css/iconfont.css");
.device-connector-selector-page {
	// margin-top: 20rpx;
	background: transparent;
	.item-block {
		padding: 14rpx 30rpx;
		color: $u-content-color;
		font-size: 28rpx;
		border-bottom: 2rpx solid #ececec;
		background: white;
		//#ifdef H5
		line-height: 70rpx;
		//#endif
		.left-title {
			color: #000;
			font-size: 32rpx;
		}
		.right-val {
			color: #000;
			text-align: right;
			flex: 1;
			.activate-now {
				padding: 10rpx 28rpx;
				background: #EDEFF9;
				border-radius: 45rpx;
				color: #4166F5;
				font-size: 26rpx;
				line-height: initial;
				text-align: center;
				margin-top: 10rpx;
				display: inline-block;
			}
			::v-deep .u-icon {
				margin-left: 10rpx;
				position: relative;
				top: -2rpx;
			}
		}
		&:nth-child(n + 2) {
			// margin-top: 20rpx;
		}
	}
	u-row {
		line-height: 80rpx;
	}
	.footer-btns {
		position: fixed;
		bottom: 40rpx;
		left: 40rpx;
		right: 40rpx;
		margin: 0 auto;
		.next-btn {
			display: block;
			margin-top: 20rpx;
		}
	}
}
</style>
