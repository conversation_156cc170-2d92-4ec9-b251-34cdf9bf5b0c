<template>
	<view class="device-connector-index-page">
		<view class="top-header-line"></view>
		
		<view class="tip">请设置紧急联系人，当设备告警，与安系统将第一时间为您联系您的紧急联系人。</view>
		
		<RecordNotFound v-if="!connectors || !connectors.length" :pic="`${staticUrl}/images/device/pic-connector-notfound.png`" desc="暂无紧急联系人" desc2="请先新增紧急联系人"></RecordNotFound>
		<view v-for="(connector, index) in connectors" :key="index" class="item-block">
			<u-row>
				<u-col span="5">
					<view class="left-title">
						<!-- <u-avatar style="vertical-align: middle;" :src="'https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fpic4.zhimg.com%2F50%2Fv2-58eb249e8ab6e93a026a640880d95f7a_hd.jpg&refer=http%3A%2F%2Fpic4.zhimg.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=jpeg?sec=1633834756&t=6fc7d77e3867665560de09428441e1b6'"></u-avatar> -->
						<view class="connector-name">
							{{ connector.contactName || '-' }}
						</view>
					</view>
				</u-col>
				<u-col span="7">
					<view class="r-flex" style="position: relative; left: 40rpx;;justify-content: flex-end;margin-right: 10rpx;">
						<view class="right-val">
							{{ connector.contactPhone || '-' }}
						</view>
						<view v-if="devReadonly === false" style="margin-left:20rpx;">
							<view style="margin-left:20rpx;">
								<text class="icon iconfont icon-rizhiguanli" style="font-size: 48rpx; color: #01B09A;" @click.stop="goEdit(connector.id)"></text>
								<view @click.stop="gotoRemove(connector.id, index)" style="display:inline-block;margin-left: 20rpx;">
									<!-- 直接使用u-icon事件无法捕获事件,会产生冒泡 -->
									<u-icon name="trash" color="#01B09A" size="48"></u-icon>
								</view>
							</view>
						</view>
					</view>
				</u-col>
			</u-row>
		</view>
		
		<view v-if="devReadonly === false" class="footer-btns">
			<!-- <u-button type="primary" shape="circle" class="next-btn" plain @click="$navTo('pagesMy/device/connector/selector')">选择紧急联系人</u-button> -->
			<u-button shape="circle" class="next-btn diy-btn" size="medium" 
			:custom-style="{ 'width': '100%', 'color': 'white', 'background-color': '#01B09A' }"  hover-class="none"
			@click="$navTo('pagesMy/device/connector/selector?source=device')">选择紧急联系人</u-button>
			
			<!-- #ifdef MP-WEIXIN -->
			<!-- <u-image width="70rpx" height="70rpx" mode="aspectFit" src="../../../static/images/device/icon-qrcode.png" class="qrcode-img" @click="handleShowQrcode"></u-image> -->
			<!-- #endif -->
			<!-- <u-button type="primary" shape="circle" class="next-btn" plain @click="handleOpenDeleteDialog">删除设备</u-button> -->
		</view>
		
		
		
		<u-popup v-model="popupShow" mode="bottom" border-radius="14" height="600rpx" :closeable="true" @close="handleCloseQrCode">
			<view style="padding: 50rpx; text-align: center;">
				<view style="font-size: 36rpx;">扫码添加紧急联系人</view>
				<view style="text-align: center;">
					<u-image width="320rpx" height="320rpx" mode="aspectFit" :src="qrcodeUrl" class="popup-img"></u-image>
				</view>
				<view style="color: #666; font-size: 24rpx; margin-top: 20rpx;">请向紧急联系人出示此二维码进行绑定</view>
				<view style="color: #666; font-size: 24rpx; margin-top: 6rpx;">有效期: 2天</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
	import RecordNotFound from '../../components/record-not-found/index'
	export default {
		components: {
			RecordNotFound
		},
		data() {
			return {
				radioValue: undefined,
				deleteDialog: false,
				deviceName: '',
				deviceNameDialog: false,
				rows: [],
				connectors: [],
				popupShow: false,
				qrcodeUrl: '',
				devReadonly: false,
				notEdit: false,
			}
		},
		onLoad(option) {
			this.devReadonly = option.readonly === 'true'
			this.notEdit = option.notEdit === 'true'
		},
		onShow() {
			this.fetchDatas();
		},
		methods: {
			goEdit(id) {
				if (this.devReadonly === false) {
					this.$navTo('pagesMy/device/connector/edit?id=' + id)
				}
			},
			gotoRemove(contactId, index) {
				uni.showModal({
					content: '解除紧急联系人？',
					showCancel: true,
					success: ({ confirm, cancel }) => {
						if (cancel) {
							return;
						}

						uni.showLoading({
							title: '操作中...',
							mask: true
						})
						let _api = this.$u.api.execDeviceunBindHouseContact;
						_api({ devId: uni.getStorageSync('devId'), contactId }).then(res => {
							uni.showToast({ duration: 1000, title: '解除成功', icon: 'none' })
							this.connectors.splice(index, 1);
						}).catch(err => {
							console.log('err', err)
							uni.showToast({ duration: 2000, title: err.message || '发生未知错误', icon: 'none' })
						}).finally(() => {
							setTimeout(() => {
								uni.hideLoading();
							}, 1000)
						})
					}
				})
			},
			handleShowQrcode() {
				this.fetchQrcode();
				this.popupShow = true;
			},
			handleCloseQrCode() {
				this.qrcodeUrl = '';
				this.fetchDatas();
			},
			gotoUrl(url){
				if (!url) return;
				uni.navigateTo({
					url: url
				})
			},
			fetchDatas() {
				uni.showLoading({
					title: '加载中...',
					mask: true
				})
				this.$u.api.fetchMyContactListByDeviceId({ devId: uni.getStorageSync('devId') }).then(res => {
					if (res) {
						this.connectors = res
					}
				}).catch(err => {
					this.connectors = {}
				}).finally(() => {
					setTimeout(() => {
						uni.hideLoading();
					}, 1000)
				})
			},
			fetchQrcode() {
				uni.showLoading({
					title: '加载中...',
					mask: true
				})
				this.$u.api.execWxQrcodeGen({ memberId: uni.getStorageSync('member').id, deviceId: uni.getStorageSync('devId') }).then(res => {
					if (res) {
						this.qrcodeUrl = res
					}
				}).catch(err => {
					this.connectors = {}
				}).finally(() => {
					setTimeout(() => {
						uni.hideLoading();
					}, 1000)
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
@import url("/static/css/iconfont.css");
.device-connector-index-page {
	padding: 29rpx;
	.tip {
		color: #8B8B8B;
	}
	.item-block {
		padding: 34rpx 30rpx;
		color: $u-content-color;
		font-size: 28rpx;
		box-shadow: 0rpx 8rpx 24rpx 0rpx rgba(0, 0, 0, 0.1);
		border-radius: 16rpx;
		.left-title {
			.connector-name {
				display: inline-block;
				color: #0D0D0D;
				font-size: 32rpx;
				padding-left: 20rpx;
			}
		}
		.right-val {
			color: #b9b9b9;
			text-align: right;
			// padding-top: 24rpx;
			.activate-now {
				padding: 10rpx 28rpx;
				background: #EDEFF9;
				border-radius: 45rpx;
				color: #4166F5;
				font-size: 26rpx;
				line-height: initial;
				text-align: center;
				margin-top: 10rpx;
				display: inline-block;
			}
			::v-deep .u-icon {
				margin-left: 10rpx;
				position: relative;
				top: -2rpx;
			}
		}
		&:nth-child(n + 2) {
			margin-top: 20rpx;
		}
	}
	u-row {
		// line-height: 80rpx;
	}
	.footer-btns {
		position: absolute;
		bottom: 40rpx;
		left: 40rpx;
		right: 40rpx;
		margin: 0 auto;
		.next-btn {
			display: block;
			margin-top: 20rpx;
			width: 500rpx;
		}
	}
	
	.popup-img {
		display: inline-block;
		margin-top: 40rpx;
	}
	
	.qrcode-img {
		position: fixed;
		bottom: 0rpx;
		right: 0rpx;
		background: #d3e1ff;
		border-radius: 10rpx;
	}
}
</style>
