<template>
	<view class="device-connector-edit-page">
		<view class="top-header-line"></view>
		
		<view>
			<u-field
				v-model="form.contactName"
				label="姓名"
				placeholder="请输入"
				required
				maxlength="50"
			>
			</u-field>
			<u-field
				v-model="form.contactPhone"
				label="电话"
				placeholder="请输入"
				required
			>
			</u-field>
			<u-field @click="handleShowRelationTypeAction" v-model="form.relationType"  :disabled="true" label="关系" placeholder="请选择"
				right-icon="arrow-down-fill"
			>
			</u-field>
			<u-action-sheet @click="handleRelationTypeClick" :list="dict.relationType" :tips="relationTypeTips" v-model="showRelationTypeActionSheet" ></u-action-sheet>
			<u-field @click="openMapSelectDialog" type="textarea" v-model="form.addr" label="地址" disabled placeholder="请选择"
				right-icon="arrow-down-fill"
			>
			</u-field>
		</view>
		
		<view class="footer-btns">
			<u-button shape="circle" class="next-btn diy-btn" size="medium" 
			:custom-style="{ 'width': '100%', 'color': 'white', 'background-color': '#01B09A' }"  hover-class="none"
			@click="handleSave">保存</u-button>
			<u-button v-if="form.id && source === 'my'" type="default" shape="circle" class="next-btn err-oper-btn diy-btn" size="medium" style="margin-top: 56rpx"
			:custom-style="{ 'width': '100%', 'background-color': '#F6F6F6', 'color': '#000', 'border': 'none' }" :hair-line="false" hover-class="none"
			@click="handleOpenDeleteDialog">删除</u-button>
		</view>
		
		<u-modal v-model="deleteDialog" title="删除紧急联系人？" :title-style="{color: '#ff1f1f'}" :show-cancel-button="true" @confirm="handleConfirmDelete">
			<view class="slot-content" style="padding: 20rpx; text-align: center; color: #999; font-size: 26rpx;">
				请确定该操作
			</view>
		</u-modal>
	</view>
</template>

<script>
	import {validateCnName,validateEnName} from "../../../utils/util.js";
	export default {
		data() {
			return {
				showRelationTypeActionSheet: false,
				deleteDialog: false,
				dict: {
					relationType: [
						{
							code: 'kinsman',
							text: '亲属'
						},
						{
							code: 'neighbor',
							text: '邻居'
						}
					]
				},
				relationTypeTips: {
					text: '关系',
					color: '#909399',
					fontSize: 24
				},
				form: {
					contactName: undefined,
					contactPhone: undefined,
					relationType: '亲属',
					addr: undefined,
					latitude: undefined,
					longitude: undefined,
				},
				source: undefined
			}
		},
		computed: {
			screenHeight() {
				return uni.getSystemInfoSync().windowHeight + 'px';
			}
		},
		onLoad(option) {
			this.id = option.id
			this.source = option.source
			if (option.id != null && option.id != undefined) {
				this.fetchDetailInfo(option.id)
			}
		},
		methods: {
			openMapSelectDialog() {
				console.log(1)
				let _that = this;
				uni.chooseLocation({
					success: (res) => {
						 console.log('位置名称：' + res.name);
						 console.log('详细地址：' + res.address);
						 console.log('纬度：' + res.latitude);
						 console.log('经度：' + res.longitude);
						 _that.form.addr = res.address.indexOf(res.name) != -1 ? res.address : res.address + res.name;
						 _that.form.latitude = res.latitude;
						 _that.form.longitude = res.longitude;
						 
					 }
				});
			},
			handleShowRelationTypeAction() {
				this.showRelationTypeActionSheet = true;
			},
			handleRelationTypeClick(index) {
				this.form.relationType = this.dict.relationType[index].text;
			},
			handleOpenDeleteDialog() {
				this.deleteDialog = true
			},
			fetchDetailInfo(id) {
				uni.showLoading({
					title: '加载中...',
					mask: true
				})
				this.$u.api.fetchMContactInfo({ contactId: id }).then(res => {
					if (res) {
						res.relationType = res.relationTypeDesc || res.relationType
						this.form = res
					}
				}).catch(err => {
					uni.showToast({ duration: 2000, title: res.message || '发生未知错误', icon: 'none' })
				}).finally(() => {
					setTimeout(() => {
						uni.hideLoading();
					}, 1000)
				})
			},
			handleConfirmDelete() {
				
				uni.showLoading({
					title: '保存中...',
					mask: true
				})
				this.$u.api.execContactMyDelete({ contactId: this.id }).then(res => {
					uni.showToast({ duration: 2000, title: '删除紧急联系人成功', icon: 'none' })
					// this.deleteDialog = true
					setTimeout(() => {
						uni.navigateBack({
							delta: 1
						})
					}, 500);
				}).catch(err => {
					console.log('err', err)
					uni.showToast({ duration: 2000, title: err.message || '发生未知错误', icon: 'none' })
				}).finally(() => {
					setTimeout(() => {
						uni.hideLoading();
					}, 1000)
				})
			},
			convertDictByText(dictItemName, name) {
				let _dictItem = this.dict[dictItemName]
				if (!_dictItem || !_dictItem.length  || !name){
					return {};
				}
				let _filters = _dictItem.filter(d => d.text === name);
				return _filters.length ? _filters[0] : {}
			},
			handleSave() {
				
				if (!this.form.contactName || !this.form.contactPhone) {
					uni.showModal({ content: '请检查“*”必填项是否填写正确', showCancel: false, confirmText: '关闭' })
					return;
				}
				if(!validateCnName(this.form.contactName)&&!validateEnName(this.form.contactName)){
					uni.showModal({ content: '姓名只支持中/英文，中文2-10个字，英文50字符', showCancel: false, confirmText: '关闭' })
					return;
				}
				if (/^[1][3,4,5,6,7,8,9][0-9]{9}$/.test(this.form.contactPhone) === false) {
					uni.showModal({ content: '请检查电话是否填写正确', showCancel: false, confirmText: '关闭' })
					return;
				}
				
				// if (this.form.positionX <= 0 || this.form.positionY <= 0 || this.form.scaleX <= 0 || 
				// 	this.form.scaleY <= 0 || this.form.scaleZ <= 0 || this.form.rotation <= 0) {
				// 	uni.showModal({ content: '请检查填写内容是否大于0', showCancel: false, confirmText: '关闭' })
				// 	return;
				// }
				
				let _params = {
					...this.form,
				}
				_params.relationType = this.convertDictByText('relationType', _params.relationType).code
				uni.showLoading({
					title: '保存中...',
					mask: true
				})
				let _api = this.id ? this.$u.api.execContactMyUpdate : this.$u.api.execContactMyAdd
				_api(_params).then(res => {
					uni.showToast({ duration: 2000, title: (this.id ? '修改' : '添加') + '紧急联系人成功', icon: 'none' })
					const that = this;
					setTimeout(() => {
						if(that.source=='createFamily'){
							uni.reLaunch({url:`/pages/index/index`});
						}else{
							uni.navigateBack({
								delta: 1
							})
						}
					}, 1000);
				}).catch(err => {
					console.log('err', err)
					uni.showToast({ duration: 2000, title: err.message || '发生未知错误', icon: 'none' })
				}).finally(() => {
					setTimeout(() => {
						uni.hideLoading();
					}, 1000)
				})
			},
		}
	}
</script>

<style lang="scss">
page {
	background-color: #f7f7f7;
	// #ifdef H5
	height: 100%;
	// #endif
}
</style>
<style lang="scss" scoped>
.device-connector-edit-page {
	background-color: white;
	.icon-reqired {
		color: red;
		display: inline-block;
		vertical-align: middle;
		font-size: 40rpx;
		position: relative;
		top: 4rpx;
		left: 6rpx;
	}
	::v-deep .u-label {
		font-size: 32rpx;
		width: 120rpx;
		display: block;
		color: #0D0D0D;
		flex: initial !important;
		&::before {
			font-size: 40rpx;
			top: 10rpx;
		}
		.u-label-text {
			padding-left: 20rpx;
			font-weight: bold;
			box-sizing: border-box;
		}
	}
	.direction-field {
		::v-deep .u-field-inner {
			height: 120rpx;
			align-items: baseline;
		}
	}
	
	.footer-btns {
		position: absolute;
		bottom: 0rpx;
		left: 0rpx;
		right: 0rpx;
		margin: 0 auto;
		background: white;
		padding: 27rpx 54rpx 115rpx 54rpx;
		.next-btn {
			display: block;
			margin-top: 20rpx;
		}
	}
	// ::v-deep .uni-easyinput__content {
	// 	padding: 14rpx 6rpx;
	// }
	::v-deep .u-model__footer__button {
		height: 88rpx;
		line-height: 88rpx;
	}
	::v-deep .u-field {
		padding: 28rpx;
	}
}
</style>
