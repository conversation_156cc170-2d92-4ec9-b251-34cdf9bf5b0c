<template>
	<view class="device-guardian-selector-page">
		<view class="top-header-line"></view>

		<RecordNotFound v-if="rows.length === 0" :pic="`${staticUrl}/images/device/pic-guardian-notfound.png`" desc="暂无被监护人">
		</RecordNotFound>
		<!-- <view class="tip" v-if="source == sourceEnum.family">
			请设置
			<text class="u-font-32 u-margin-left-20 u-margin-right-20 u-main-color">{{familyName}}</text>
			 家庭被监护人，当被监护人出现安全事件时，
		</view> -->

		<view v-for="(row, index) in rows" :key="index" class="item-block">
			<u-row @click="gotoMyEdit(row.id, row)">
				<u-col span="5">
					<view
						v-if="source === sourceEnum.my || source === sourceEnum.order  || (source === sourceEnum.family && selfFamily===false)"
						class="left-title">
						{{ row.name }}
						<text v-if="row.guard" class="icon iconfont icon-anquan1"
							style="margin-left: 10rpx; font-size: 36rpx; color: #01B09A;"></text>
						<text v-if="!row.guard && row.hasOrder" class="icon iconfont icon-anquan1"
							style="margin-left: 10rpx; font-size: 36rpx; color: #ff9900;"></text>
					</view>
					<view v-else class="left-title" @click="radioChange(row.id)">
						<text v-if="selected[row.id] && selected[row.id] !== 99999"
							class="icon iconfont icon-xuanzhong radio" style="color: #01B09A"></text>
						<text v-else class="icon iconfont icon-weixuanzhong radio"></text>
						<text style="margin-left: 14rpx;">{{ row.name }}</text>
						<text v-if="row.guard" class="icon iconfont icon-anquan1"
							style="margin-left: 10rpx; font-size: 36rpx; color: #01B09A;"></text>
						<text v-if="!row.guard && row.hasOrder" class="icon iconfont icon-anquan1"
							style="margin-left: 10rpx; font-size: 36rpx; color: #ff9900;"></text>
					</view>
				</u-col>
				<u-col span="7">
					<view class="r-flex" style="justify-content: flex-end;margin-right: 10rpx;">
						<view class="right-val">
							{{ row.phone || '-' }}
						</view>
						<view v-if="row.selfCreate  && selfFamily===true" style="margin-left:20rpx;">
							<text class="icon iconfont icon-rizhiguanli" style="font-size: 48rpx; color: #01B09A;"
								@click.stop="gotoEdit(row.id)"></text>
							<!-- 只要有订单的，都不能删除，因为订单包括为备案生效的，或已备案取消家庭关联的 -->
							<view v-if="!row.hasOrder" @click.stop="gotoRemove(row.id, index)"
								style="display:inline-block;margin-left: 20rpx;">
								<!-- 直接使用u-icon事件无法捕获事件,会产生冒泡 -->
								<u-icon name="trash" color="#01B09A" size="48"></u-icon>
							</view>
						</view>
					</view>
				</u-col>
			</u-row>
		</view>

		<view v-if="selfFamily===true" class="footer-btns">
			<u-button shape="circle" class="next-btn diy-btn" size="medium"
				:custom-style="{ 'width': '100%', 'color': 'white', 'background-color': '#01B09A' }" hover-class="none"
				@click="$navTo(`pagesMy/device/guardian/edit?source=${source}`)">新增被监护人</u-button>
		</view>
	</view>
</template>

<script>
	import RecordNotFound from '../../components/record-not-found/index'
	export default {
		components: {
			RecordNotFound
		},
		data() {
			return {
				radioValue: undefined,
				deleteDialog: false,
				deviceName: '',
				deviceNameDialog: false,
				familyId: null,
				familyName: null,
				selected: {},
				rows: [],
				source: undefined,

				sourceEnum: {
					"my": "my",
					"order": "order",
					"family": "family",
					"device": "device",
				},
				expires: true,
				selfFamily: true,
			}
		},
		onLoad(option) {
			this.source = option.source
			if (this.source == this.sourceEnum.order) {
				this.eventChannel = this.getOpenerEventChannel();
				if (option.excludeIds) {
					this.excludeIds = option.excludeIds.split(",");
				}
			}
			if (this.source == this.sourceEnum.family) {
				this.selfFamily = JSON.parse(option.selfCreate)
				this.familyId = new Number(option.id);
				this.familyName = option.name;
			}
		},
		onShow() {
			if (this.source == this.sourceEnum.order) {
				this.fetchNotGuardOlders();
			} else {
				this.fetchSelectedDatas();
			}
		},
		methods: {
			async gotoMyEdit(id, row) {
				if (this.source === this.sourceEnum.my) {
					this.gotoEdit(id);
					return;
				}
				if (this.source === this.sourceEnum.order) {
					console.log("in");
					await this.$u.api.expiresWithinOneMonth({
						olderId: id
					}).then(res => {
						console.log("res:", res);
						this.expires = res == 0 ? false : true;
					});

					console.log("expires:", this.expires)
					if (row.guard && this.expires) {
						uni.showModal({
							content: row.name + '该被监护人已经是会员，可在过期前1个月之内或过期后，为该会员激活会员卡',
							showCancel: false,
							confirmText: '关闭'
						})
						return;
					}
					if (row.hasOrder) {
						uni.showModal({
							content: row.name + '已经有该服务未生效订单，请将其与家庭关联后即可生效',
							showCancel: false,
							confirmText: '关闭'
						})
						return;
					}
					if (!row.idcardType || !row.idcardCode) {
						uni.showModal({
							content: '证件信息不完整，请编辑并填写',
							showCancel: false,
							confirmText: '关闭'
						})
						return;
					}
					this.eventChannel.emit('selected', {
						data: row
					});
					uni.navigateBack();
					return;
				}
			},
			gotoEdit(id) {
				const {
					source
				} = this;
				this.$navTo(`pagesMy/device/guardian/edit?source=${source}&id=${id}`);
			},
			gotoRemove(id, index) {
				uni.showModal({
					title: '删除监护人？',
					showCancel: true,
					confirmColor: '#01B09A',
					success: ({
						confirm,
						cancel
					}) => {
						if (cancel) {
							return;
						}

						uni.showLoading({
							title: '保存中...',
							mask: true
						})
						this.$u.api.execOlderMyDelete({
							olderId: id
						}).then(res => {
							uni.showToast({
								duration: 2000,
								title: '删除被监护人成功',
								icon: 'none'
							});
							this.rows.splice(index, 1);
						}).catch(err => {
							console.log('err', err)
							uni.showToast({
								duration: 2000,
								title: err.message || '发生未知错误',
								icon: 'none'
							})
						}).finally(() => {
							setTimeout(() => {
								uni.hideLoading();
							}, 1000)
						})

					}
				})

			},
			radioChange(rowId) {
				if (this.selected[rowId] && this.selected[rowId] !== 99999) {
					this.handleSelectedItem(false, rowId)
					this.$set(this.selected, rowId, 99999)
				} else {
					this.handleSelectedItem(true, rowId)
					this.$set(this.selected, rowId, rowId)
				}
			},
			handleOpenNameDialog() {
				this.deviceNameDialog = true
				this.value = this.deviceInfo.devName
			},
			handleConfirmNameDialog() {
				this.deviceInfo.devName = this.value
				this.isChange = true
				this.submitRename();
			},
			handleOpenDeleteDialog() {
				this.deleteDialog = true
			},
			async fetchSelectedDatas() {
				uni.showLoading({
					title: '加载中...',
					mask: true
				})
				if (this.source === this.sourceEnum.my || this.source === this.sourceEnum.order) {
					this.fetchDatas();
				} else if (this.source === this.sourceEnum.family) {
					let familyList = await this.$u.api.queryOlderByFamilyId({
						familyId: this.familyId
					});
					if (familyList) {
						let _mapping = {};
						familyList.map(r => {
							this.$set(this.selected, r.id, r.id)
						})
					}
					// 合并家庭的被监护人和我的被监护人
					let myList = await this.$u.api.fetchOlderMyList();
					let allArr = [].concat(myList, familyList);
					if (allArr) {
						// 被监护人去重,自己的放到前面，有重复的时删除的就是家庭的
						let obj = {};
						let arrays = allArr.reduce((setArr, item) => {
							obj[item.id] ? '' : obj[item.id] = true && setArr.push(item);
							return setArr;
						}, []);
						this.rows = arrays;
					}

				} else {
					this.$u.api.fetchMyOlderListByDeviceId({
						devId: uni.getStorageSync('devId')
					}).then(res => {
						if (res) {
							let _mapping = {};
							res.map(r => {
								this.$set(this.selected, r.id, r.id)
							})
							this.fetchDatas();
						}
						setTimeout(() => {
							uni.hideLoading();
						}, 1000)
					}).catch(err => {
						setTimeout(() => {
							uni.hideLoading();
						}, 1000)
					})
				}
			},
			async fetchDatas() {
				uni.showLoading({
					title: '加载中...',
					mask: true
				})

				try {
					let res = await this.$u.api.fetchOlderMyList();
					this.rows = this.filterExclude(res);
				} catch (err) {
					console.log(err);
				} finally {
					setTimeout(() => {
						uni.hideLoading();
					}, 1000)
				}
			},
			async fetchNotGuardOlders() {
				if (this.sourceEnum.order != this.source) {
					return;
				}
				let res = await this.$u.api.fetchOlderMyList();
				this.rows = this.filterExclude(res);
			},
			handleSelectedItem(flag, rowId) {
				uni.showLoading({
					title: '操作中...',
					mask: true
				});
				let _api;
				if (this.source == this.sourceEnum.family) {
					_api = this.$u.api.bindOlder2Family;
				} else if (this.source == this.sourceEnum.device) {
					_api = this.$u.api.bindOlder2Family4Devive;
				}
				_api({
					familyId: this.familyId,
					devId: uni.getStorageSync('devId'),
					olderId: rowId,
					isBind: flag
				}).then(res => {
					uni.showToast({
						duration: 1000,
						title: !flag ? '取消成功' : '选择成功',
						icon: 'none'
					})
				}).catch(err => {
					console.log('err', err)
					uni.showToast({
						duration: 2000,
						title: err.message || '发生未知错误',
						icon: 'none'
					})
				}).finally(() => {
					uni.hideLoading();
				})
			},
			filterExclude(rows) {
				if (!rows?.length) {
					return rows;
				}
				const {
					excludeIds
				} = this;

				if (!excludeIds?.length) {
					return rows;
				}

				return rows.filter(item => !excludeIds.find(id => id == item.id));
			}
		}
	}
</script>

<style lang="scss">
	page {
		background-color: #f7f7f7;
	}
</style>
<style lang="scss" scoped>
	@import url("/static/css/iconfont.css");

	.device-guardian-selector-page {
		.tip {
			color: #8B8B8B;
		}

		// margin-top: 20rpx;
		background: transparent;

		.item-block {
			padding: 14rpx 30rpx;
			color: $u-content-color;
			font-size: 28rpx;
			border-bottom: 2rpx solid #ececec;
			background: white;
			//#ifdef H5
			line-height: 70rpx;

			//#endif
			.left-title {
				color: #000;
				font-size: 32rpx;
			}

			.right-val {
				color: #000;
				text-align: right;

				.activate-now {
					padding: 10rpx 28rpx;
					background: #EDEFF9;
					border-radius: 45rpx;
					color: #4166F5;
					font-size: 26rpx;
					line-height: initial;
					text-align: center;
					margin-top: 10rpx;
					display: inline-block;
				}

				::v-deep .u-icon {
					margin-left: 10rpx;
					position: relative;
					top: -2rpx;
				}
			}

			&:nth-child(n + 2) {
				// margin-top: 20rpx;
			}
		}

		u-row {
			line-height: 80rpx;
		}

		.footer-btns {
			position: fixed;
			bottom: 40rpx;
			left: 40rpx;
			right: 40rpx;
			margin: 0 auto;

			.next-btn {
				display: block;
				margin-top: 20rpx;
			}
		}
	}
</style>
