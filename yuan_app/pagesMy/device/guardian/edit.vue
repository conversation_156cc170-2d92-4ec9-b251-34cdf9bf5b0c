<template>
	<view class="device-guardian-edit-page">
		<view class="top-header-line"></view>
		
		<view>
			<u-field
				v-model="form.name"
				label="姓名"
				placeholder="请输入"
				maxlength="50"
				required
				:disabled = "isGuard==true"
			>
			</u-field>
			<u-field @click="handleShowSexAction" v-model="form.gender"  :disabled="true" label="性别" placeholder="请选择"
				right-icon="arrow-down-fill">
			</u-field>
			<u-action-sheet @click="handleSexClick" :list="dict.gender" :tips="sexTips" v-model="showSexActionSheet" ></u-action-sheet>

			<u-field
				v-model="form.phone"
				label="电话"
				placeholder="请输入"
				required
				:disabled = "isGuard==true"
			>
			</u-field>

			<u-field @click="showIdcardTypeAction = true" :required="sourceEnum.order === source"
				v-model="form.idcardTypeText" :disabled="true" label="证件类型" placeholder="请选择"
				right-icon="arrow-down-fill" />
			<u-action-sheet @click="onIdcardTypeAction" 
			  :list="dictDynamic.idcardType" :tips="idcardTypeTips" v-model="showIdcardTypeAction" ></u-action-sheet>

			<u-field v-model="form.idcardCode" label="证件号码" @blur="handleBlur" placeholder="请输入" maxlength="36" :required="sourceEnum.order === source" :disabled = "isGuard==true"/>
			
			<u-field v-model="form.address" @click="onShowAddress" label="省份/城市" placeholder="请选择" right-icon="arrow-down-fill" disabled />
			<u-select v-model="showAddressSelect" mode="mutil-column-auto" :list="adminAreaList" @confirm="onAddressAction"></u-select>

			<u-field v-model="form.height" label="身高(cm)" placeholder="请输入" type="digit" maxlength="5" @blur="onDecimalBlur($event,1,'height')"/>
			<u-field v-model="form.weight" label="体重(kg)" placeholder="请输入" type="digit" maxlength="6" @blur="onDecimalBlur($event,2,'weight')"/>
			<u-field v-model="form.medicalHistory" label="既往病史" placeholder="请输入" type="textarea" maxlength="200"/>
			<u-field v-model="form.allergies" label="过敏史" placeholder="请输入" type="textarea"  maxlength="200"/>
			<u-field @click="showBloodtypeAction = true"
				v-model="form.bloodtypeText" :disabled="true" label="血型" placeholder="请选择"
				right-icon="arrow-down-fill" />
			<u-action-sheet @click="onBloodtypeAction" :list="dictDynamic.bloodType" :tips="bloodtypeTips" v-model="showBloodtypeAction" ></u-action-sheet>

			<u-field @click="handleShowBirthdayAction" v-model="form.birthday"  :disabled="true" label="出生日期" placeholder="请选择"
				right-icon="arrow-down-fill"
			>
			</u-field>
			<u-picker mode="time" v-model="showBirthdayActionSheet" @confirm="handleBirthdayConfirm" start-year="1900" :end-year="currYear"
				:params="{ year: true, month: true, day: true, hour: false, minute: false, second: false }"></u-picker>
			
			<u-field @click="handleShowLiveTypeAction" v-model="form.liveTypeText"  :disabled="true" label="居住类型" placeholder="请选择"
				right-icon="arrow-down-fill"
			>
			</u-field>
			<u-action-sheet @click="handleLiveTypeClick" :list="dictDynamic.older_live_type" :tips="liveTypeTips" v-model="showLiveTypeActionSheet" ></u-action-sheet>
		</view>
		
		<view class="footer-btns">
			<u-button v-if="source=='createFamily'" shape="circle" class="next-btn diy-btn" size="medium" 
			:custom-style="{ 'width': '100%', 'color': 'white', 'background-color': '#01B09A' }"  hover-class="none"
			@click="handleSave">下一步</u-button>
			
			<u-button v-else shape="circle" class="next-btn diy-btn" size="medium"
			:custom-style="{ 'width': '100%', 'color': 'white', 'background-color': '#01B09A' }"  hover-class="none"
			@click="handleSave">保存</u-button>
			<!-- <u-button v-if="form.id && source === 'my'" type="default" shape="circle" class="next-btn err-oper-btn diy-btn" size="medium" style="margin-top: 56rpx"
			:custom-style="{ 'width': '100%', 'background-color': '#F6F6F6', 'color': '#000', 'border': 'none' }" :hair-line="false" hover-class="none"
			@click="handleOpenDeleteDialog">删除</u-button> -->
		</view>
		
		<u-modal v-model="deleteDialog" title="删除监护人？" :title-style="{color: '#ff1f1f'}" :show-cancel-button="true" @confirm="handleConfirmDelete">
			<view class="slot-content" style="padding: 20rpx; text-align: center; color: #999; font-size: 26rpx;">
				请确定该操作
			</view>
		</u-modal>
	</view>
</template>

<script>
	import { dictTypesToCodeText, buildTree } from "../../../utils/sysDict";
	import {validateCnName,validateEnName,getBirthDate} from "../../../utils/util.js";
	export default {
		data() {
			return {
				currYear: new Date().getFullYear(),
				showSexActionSheet: false,
				showLiveTypeActionSheet: false,
				showBirthdayActionSheet: false,
				deleteDialog: false,
				dict: {
					gender: [
						{
							code: 'M',
							text: '男'
						},
						{
							code: 'W',
							text: '女'
						}
					],
					
				},
				sexTips: {
					text: '性别选择',
					color: '#909399',
					fontSize: 24
				},
				liveTypeTips: {
					text: '居住类型',
					color: '#909399',
					fontSize: 24
				},
				form: {
					name: undefined,
					gender: "男",
					phone: undefined,
					birthday: undefined,
					liveType: "独居",
				},
				source: undefined,

				dictDynamic: {
					idcardType: [],
					older_live_type:[],
					bloodType: []
				},
				
				showIdcardTypeAction: false,
				idcardTypeTips: {
					text: '证件类型',
					color: '#909399',
					fontSize: 24
				},

				showBloodtypeAction: false,
				bloodtypeTips: {
					text: '血型',
					color: '#909399',
					fontSize: 24
				},

				sourceEnum: {
					my: "my",
					order: "order",
				},
				showAddressSelect: false,
				adminAreaList: [],
				isGuard:false,
			}
		},
		async onLoad(option) {
			this.id = option.id
			this.source = option.source
			const dictDynamic = await this.$u.api.listDcitMapByTypes({ typeCodes: Object.keys(this.dictDynamic) });
			this.dictDynamic = dictTypesToCodeText(dictDynamic);
			if (option.id != null && option.id != undefined) {
				this.fetchDetailInfo(option.id)
				this.isGuard = await this.$u.api.olderHasOrder({
						olderId: option.id
					});
			}
		},
		methods: {
			onDecimalBlur(event,num,filed) {
				let value = event.target.value;
				if(filed=='height'){
					let a = this.toPrecision(value,num)
					this.form.height =a ;
				}
				if(filed=='weight'){
					this.form.weight = this.toPrecision(value,num);
				}
			},
			// 设置数值精度
			toPrecision(value, precision) {
				if (precision === undefined) precision = 2;
				return parseFloat(Math.round(value * Math.pow(10, precision)) / Math.pow(10, precision)).toFixed(precision);
			},
			handleShowSexAction() {
				this.showSexActionSheet = true;
			},
			handleShowLiveTypeAction() {
				this.showLiveTypeActionSheet = true;
			},
			handleShowBirthdayAction() {
				this.showBirthdayActionSheet = true;
			},
			handleSexClick(index) {
				this.form.gender = this.dict.gender[index].text;
			},
			handleLiveTypeClick(index) {
				this.form.liveType = this.dictDynamic.older_live_type[index].code;
				this.form.liveTypeText = this.dictDynamic.older_live_type[index].text;
			},
			handleBirthdayConfirm(val) {
				const selectDate = [ val.year, val.month, val.day ].join('-');
				if(new Date(new Date().toISOString().substring(0, 10)) <= new Date(selectDate)){
					uni.showModal({ content: '出生日期选择错误', showCancel: false, confirmText: '关闭' })
					return;
				}
				this.form.birthday = selectDate;
			},
			// 选中任一radio时，由radio-group触发
			radioGroupChange(v) {
				this.form.cls = v
			},
			handleOpenDeleteDialog() {
				this.deleteDialog = true
			},
			fetchDetailInfo(id) {
				uni.showLoading({
					title: '加载中...',
					mask: true
				})
				this.$u.api.fetchMyOlderInfo({ olderId: id }).then(res => {
					if (res) {
						if (res.gender === 'M') {
							res.gender = '男'
						} else if (res.gender === 'W') {
							res.gender = '女'
						}
						const { province = "", city = "", bloodtype, idcardType,liveType} = res;
						res.address = province || city ? `${province}/${city}` : null;
						if (idcardType) {
							res.idcardTypeText = this.dictDynamic.idcardType.find(item => item.code == idcardType)?.text;
						}
						if (bloodtype) {
							res.bloodtypeText = this.dictDynamic.bloodType.find(item => item.code == bloodtype)?.text;
						}
						if (liveType) {
							res.liveTypeText = this.dictDynamic.older_live_type.find(item => item.code == liveType)?.text;
						}
						this.form = res
					}
				}).catch(err => {
					console.log(err);
					uni.showToast({ duration: 2000, title: err.message || '发生未知错误', icon: 'none' })
				}).finally(() => {
					setTimeout(() => {
						uni.hideLoading();
					}, 1000)
				})
			},
			handleConfirmDelete() {
				
				uni.showLoading({
					title: '保存中...',
					mask: true
				})
				this.$u.api.execOlderMyDelete({ olderId: this.id }).then(res => {
					uni.showToast({ duration: 2000, title: '删除被监护人成功', icon: 'none' })
					// this.deleteDialog = true
					setTimeout(() => {
						uni.navigateBack({
							delta: 1
						})
					}, 500);
				}).catch(err => {
					console.log('err', err)
					uni.showToast({ duration: 2000, title: err.message || '发生未知错误', icon: 'none' })
				}).finally(() => {
					setTimeout(() => {
						uni.hideLoading();
					}, 1000)
				})
			},
			convertDictByText(dictItemName, name) {
				let _dictItem = this.dict[dictItemName]
				if (!_dictItem || !_dictItem.length  || !name){
					return {};
				}
				let _filters = _dictItem.filter(d => d.text === name);
				return _filters.length ? _filters[0] : {}
			},
			handleBlur(){
				if(this.form.idcardType == "0" && this.$u.test.idCard(this.form.idcardCode)){
					const date  = getBirthDate(this.form.idcardCode);
					if(new Date(new Date().toISOString().substring(0, 10)) <= new Date(date)){
						uni.showModal({ content: '身份证号码错误', showCancel: false, confirmText: '关闭' })
						this.form.idcardCode = ""
					}else{
						this.form.birthday = date;
					}
				}
			},
			handleSave() {
				
				if (!this.form.name || !this.form.phone) {
					uni.showModal({ content: '请检查“*”必填项是否填写正确', showCancel: false, confirmText: '关闭' })
					return;
				}

				if(!validateCnName(this.form.name)&&!validateEnName(this.form.name)){
					uni.showModal({ content: '姓名只支持中/英文，中文2-10个字，英文50字符', showCancel: false, confirmText: '关闭' })
					return;
				}

				if (!this.$u.test.mobile(this.form.phone)) {
					uni.showModal({ content: '请检查电话是否填写正确', showCancel: false, confirmText: '关闭' })
					return;
				}

				if (this.source === this.sourceEnum.order) {
					if (!this.form.idcardType) {
						uni.showModal({ content: '请选择证件类型', showCancel: false, confirmText: '关闭' });
						return;
					}
					if (!this.form.idcardCode) {
						uni.showModal({ content: '请输入证件号', showCancel: false, confirmText: '关闭' });
						return;
					}
					if (this.form.idcardType == "0" && !this.$u.test.idCard(this.form.idcardCode)) {
						uni.showModal({ content: '身份证号无效', showCancel: false, confirmText: '关闭' });
						return;
					} else {
						if (!this.$u.test.enOrNum(this.form.idcardCode)) {
							uni.showModal({ content: '证件号无效', showCancel: false, confirmText: '关闭' });
							return;
						}
					}
				}

				if (this.form.height) {
					if (!this.$u.test.number(this.form.height)) {
						uni.showModal({ content: '身高只能输入数字', showCancel: false, confirmText: '关闭' })
						return;
					}
				}
				if (this.form.weight) {
					if (!this.$u.test.number(this.form.weight)) {
						uni.showModal({ content: '体重只能输入数字', showCancel: false, confirmText: '关闭' })
						return;
					}
				}
				
				let _params = {
					...this.form,
				}
				_params.gender = this.convertDictByText('gender', _params.gender).code
				uni.showLoading({
					title: '保存中...',
					mask: true
				})
				let _api = this.id ? this.$u.api.execOlderMyUpdate : this.$u.api.execOlderMyAdd
				_api(_params).then(res => {
					uni.showToast({ duration: 2000, title: (this.id ? '修改' : '添加') + '被监护人成功', icon: 'none' })
					const that = this;
					setTimeout(() => {
						if(that.source=='createFamily'){
							that.$navTo(`pagesMy/device/connector/edit?source=createFamily`)
						}else{
							uni.navigateBack({
								delta: 1
							})
						}
					}, 1000);
				}).catch(err => {
					console.log('err', err)
					uni.showToast({ duration: 2000, title: err.message || '发生未知错误', icon: 'none' })
				}).finally(() => {
					setTimeout(() => {
						uni.hideLoading();
					}, 1000)
				})
			},
			onIdcardTypeAction(index) {
				if(this.isGuard){
					uni.showToast({
						duration: 2000,
						title: '该被监护人已有守护服务订单，禁止修改姓名、手机号、证件类型、证件号码',
						icon: 'none'
					})
					return;
				}
				this.form.idcardType = this.dictDynamic.idcardType[index].code;
				this.form.idcardTypeText = this.dictDynamic.idcardType[index].text;
			},
			onBloodtypeAction(index) {
				this.form.bloodtype = this.dictDynamic.bloodType[index].code;
				this.form.bloodtypeText = this.dictDynamic.bloodType[index].text;
			},
			async onShowAddress() {
				if (!this.adminAreaList?.length) {
					const dicts = await this.$u.api.listDcitByType({typeCode: "adminArea"});
					this.adminAreaList = buildTree(dicts);
				}
				this.showAddressSelect = true;
			},
			onAddressAction([ a, b ]) {
				this.form.address = `${a.label}/${b.label}`;
				this.form.province = a.label;
				this.form.city = b.label;
				this.showAddressSelect = false;
			},
		}
	}
</script>

<style lang="scss">
page {
	background-color: #f7f7f7;
}
</style>
<style lang="scss" scoped>
.device-guardian-edit-page {
	background-color: white;
	.icon-reqired {
		color: red;
		display: inline-block;
		vertical-align: middle;
		font-size: 40rpx;
		position: relative;
		top: 4rpx;
		left: 6rpx;
	}
	::v-deep .u-label {
		font-size: 32rpx;
		width: 250rpx;
		display: block;
		color: #0D0D0D;
		flex: initial !important;
		&::before {
			font-size: 40rpx;
			top: 10rpx;
		}
		.u-label-text {
			padding-left: 20rpx;
			font-weight: bold;
			box-sizing: border-box;
		}
	}
	.direction-field {
		::v-deep .u-field-inner {
			height: 120rpx;
			align-items: baseline;
		}
	}
	.footer-btns {
		// position: absolute;
		// bottom: 0rpx;
		// left: 0rpx;
		// right: 0rpx;
		margin: 0 auto;
		background: white;
		padding: 27rpx 54rpx 115rpx 54rpx;
		.next-btn {
			display: block;
			margin-top: 20rpx;
		}
	}
	// ::v-deep .uni-easyinput__content {
	// 	padding: 14rpx 6rpx;
	// }
	::v-deep .u-model__footer__button {
		height: 88rpx;
		line-height: 88rpx;
	}
	::v-deep .u-field {
		padding: 28rpx;
	}
}
</style>
