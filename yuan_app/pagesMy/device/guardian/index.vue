<template>
	<view class="device-guardian-index-page">
		<view class="top-header-line"></view>
		
		<view class="tip">请设置监护人，便于“与安宝”随时守护。</view>
		
		<!-- #ifdef MP-WEIXIN -->
		<RecordNotFound v-if="!guardians || !guardians.length" :pic="`${staticUrl}/images/device/pic-guardian-notfound.png`" desc="暂无被监护人" desc2="请先新增被监护人"></RecordNotFound>
		<!-- #endif -->
		<!-- #ifdef H5 -->
		<RecordNotFound v-if="!guardians || !guardians.length" :pic="`${staticUrl}/images/device/pic-guardian-notfound.png`" desc="暂无被监护人" desc2="请先新增被监护人"></RecordNotFound>
		<!-- #endif -->
		<view v-for="(guardian, index) in guardians" :key="index" class="item-block">
			<u-row>
				<u-col span="5">
					<view class="left-title">
						<!-- <u-avatar :src="'https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fpic4.zhimg.com%2F50%2Fv2-58eb249e8ab6e93a026a640880d95f7a_hd.jpg&refer=http%3A%2F%2Fpic4.zhimg.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=jpeg?sec=1633834756&t=6fc7d77e3867665560de09428441e1b6'"></u-avatar> -->
						<view class="guardian-name">
							{{ guardian.name || '-' }}
						</view>
						<text v-if="guardian.guard" class="icon iconfont icon-anquan1" style="margin-left: 10rpx; font-size: 36rpx; color: #01B09A;"></text>
						<text v-if="!guardian.guard && guardian.hasOrder" class="icon iconfont icon-anquan1" style="margin-left: 10rpx; font-size: 36rpx; color: #ff9900;"></text>
					</view>
				</u-col>
				<u-col span="7">
					<view class="r-flex" style="position: relative; left: 40rpx;justify-content: flex-end;margin-right: 10rpx;">
						<view class="right-val">
							{{ guardian.phone || '-' }}
						</view>
					</view>
				</u-col>
			</u-row>
		</view>
		
		<view v-if="devReadonly === false" class="footer-btns">
			<!-- <u-button type="primary" shape="circle" class="next-btn" plain @click="$navTo('pagesMy/device/guardian/selector')">选择被监护人</u-button> -->
			<!-- <u-button type="primary" shape="circle" class="next-btn" plain @click="handleOpenDeleteDialog">删除设备</u-button> -->
			
			<u-button shape="circle" class="next-btn diy-btn" size="medium" 
			:custom-style="{ 'width': '100%', 'color': 'white', 'background-color': '#01B09A' }"  hover-class="none"
			@click="$navTo('pagesMy/device/guardian/selector?source=device')">选择被监护人</u-button>
		</view>
	</view>
</template>

<script>
	import RecordNotFound from '../../components/record-not-found/index'
	export default {
		components: {
			RecordNotFound
		},
		data() {
			return {
				radioValue: undefined,
				deleteDialog: false,
				deviceName: '',
				deviceNameDialog: false,
				guardians: [],
				devReadonly: false,
				notEdit: false,
			}
		},
		onLoad(option) {
			this.devReadonly = option.readonly === 'true'
			this.notEdit = option.notEdit === 'true'
		},
		onShow() {
			this.fetchDatas();
		},
		methods: {
			goEdit(id) {
				if (this.devReadonly === false) {
					this.$navTo(`pagesMy/device/guardian/edit?id=${id}`);
				}
			},
			gotoUrl(url){
				if (!url) return;
				uni.navigateTo({
					url: url
				})
			},
			handleOpenNameDialog() {
				this.deviceNameDialog = true
				this.value = this.deviceInfo.devName
			},
			handleConfirmNameDialog() {
				this.deviceInfo.devName = this.value
				this.isChange = true
				this.submitRename();
			},
			handleOpenDeleteDialog() {
				this.deleteDialog = true
			},
			fetchDatas() {
				uni.showLoading({
					title: '加载中...',
					mask: true
				})
				this.$u.api.fetchMyOlderListByDeviceId({ devId: uni.getStorageSync('devId') }).then(res => {
					if (res) {
						this.guardians = res
					}
				}).catch(err => {
					this.guardians = {}
				}).finally(() => {
					uni.hideLoading();
				})
			},
			handleConfirmDelete() {
				uni.showLoading({
					title: '删除中...',
					mask: true
				})
				this.$u.api.execDelRoomGate({ devCode: uni.getStorageSync('devCode'), gid: this.gid }).then(res => {
					uni.showToast({ duration: 2000, title: '删除门位置成功', icon: 'none' })
					// this.deleteDialog = true
					setTimeout(() => {
						uni.navigateBack({
							delta: 1
						})
					}, 500);
				}).catch(err => {
					console.log('err', err)
					uni.showToast({ duration: 2000, title: res.message || '发生未知错误', icon: 'none' })
				}).finally(() => {
					uni.hideLoading();
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
@import url("/static/css/iconfont.css");
.device-guardian-index-page {
	padding: 29rpx;
	.tip {
		color: #8B8B8B;
	}
	.item-block {
		padding: 34rpx 30rpx;
		color: $u-content-color;
		font-size: 28rpx;
		box-shadow: 0rpx 8rpx 24rpx 0rpx rgba(0, 0, 0, 0.1);
		border-radius: 16rpx;
		.left-title {
			.guardian-name {
				display: inline-block;
				color: #0D0D0D;
				font-size: 32rpx;
				padding-left: 20rpx;
			}
		}
		.right-val {
			color: #b9b9b9;
			text-align: right;
			// padding-top: 24rpx;
			.activate-now {
				padding: 10rpx 28rpx;
				background: #EDEFF9;
				border-radius: 45rpx;
				color: #4166F5;
				font-size: 26rpx;
				line-height: initial;
				text-align: center;
				margin-top: 10rpx;
				display: inline-block;
			}
			::v-deep .u-icon {
				margin-left: 10rpx;
				position: relative;
				top: -2rpx;
			}
		}
		&:nth-child(n + 2) {
			margin-top: 20rpx;
		}
	}
	u-row {
		// line-height: 80rpx;
	}
	.footer-btns {
		position: fixed;
		bottom: 40rpx;
		left: 40rpx;
		right: 40rpx;
		margin: 0 auto;
		.next-btn {
			display: block;
			margin-top: 20rpx;
		}
	}
}
</style>
