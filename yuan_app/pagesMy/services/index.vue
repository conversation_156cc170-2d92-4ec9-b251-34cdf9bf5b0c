<template>
	<view class="my-services-index-page">
		<view class="top-header-line"></view>
		<view class="service-list">
			<u-grid :col="4" :border="false">
				<u-grid-item v-for="(serverType, index) in serverTypeList" :key="index" @click="$navTo(`pagesMy/serverType/index?id=${serverType.id}&name=${serverType.serverName}`)">
					<!-- <text class="icon iconfont icon-shuaidaojiance" style="margin-bottom: 10rpx;"></text> -->
					<image :src="serverType.serverIcon" style="margin-bottom: 10rpx; height: 70rpx;" mode="heightFix"></image>
					<view class="grid-text">{{ serverType.serverName }}</view>
				</u-grid-item>
			</u-grid>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				serverTypeList: []
			}
		},
		onShow() {
			this.fetchServerList();
		},
		methods: {
			fetchServerList() {
				this.$u.api.fetchServerList({ }).then(res => {
					this.serverTypeList = res || []
				})
			},
		}
	}
</script>

<style lang="scss">
page {
	background-color: white;
}
</style>
<style lang="scss" scoped>
@import url("/static/css/iconfont.css");
.my-services-index-page {
	.service-list {
		background: #fff;
		box-sizing: border-box;
		margin: 20rpx 0rpx;
		.title-wrap {
			display: flex;
			flex-direction: row;
			margin-bottom: 20rpx;
			padding: 0rpx 20rpx;
			.title {
				flex: 1;
				font-size: 32rpx;
				font-weight: bold;
				color: #000;
			}
			.more {
				width: 200rpx;
				font-size: 30rpx;
				color: #888;
				text-align: right;
			}
		}
		.icon {
			color: #08d9ce;
			font-size: 60rpx;
		}
		.grid-text {
			color: #000;
			font-size: 26rpx;
			margin-top: 10rpx;
		}
	}
}
</style>
