<template>
	<view class="container">
		<u-input v-model="mobilePhone" border placeholder="请输入手机号" />
		<u-toast ref="uToast"></u-toast>
		<u-verification-code seconds="60" ref="uCode" @change="codeChange" @end="end"
			@start="start"></u-verification-code>
		<view class="footer-btns">
			<u-button shape="circle" class="next-btn diy-btn" size="medium"
				:custom-style="{ 'width': '100%', 'color': 'white', 'background-color': '#01B09A' }" hover-class="none"
				@click="getCode">{{tips}}</u-button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				tips: '',
				seconds: 10,
				mobilePhone: undefined
			}
		},
		onLoad() {
			const member = uni.getStorageSync('member');
			if (member) {
				this.mobilePhone = member.phone;
			}
			// 自动跳转
			// const captcha = storage.get("unicom_captcha_"+member);
			// if(captcha){
			// 	this.$navTo(`pagesMy/telephone/list?mobilePhone=${this.mobilePhone}&captcha=${captcha}`);
			// }
		},
		methods: {
			codeChange(text) {
				this.tips = text;
			},
			end() {
				// this.$u.toast('倒计时结束');
			},
			start() {
				this.$u.toast('倒计时开始');
			},
			getCode() {
				if (!this.$refs.uCode.canGetCode) {
					this.$u.toast('倒计时结束后再发送');
				}
				if (!this.mobilePhone) {
					uni.showModal({
						content: '请输入手机号',
						showCancel: false,
						confirmText: '关闭'
					})
					return;
				}
				if (/^[1][3,4,5,6,7,8,9][0-9]{9}$/.test(this.mobilePhone) === false) {
					uni.showModal({
						content: '请检查电话是否填写正确',
						showCancel: false,
						confirmText: '关闭'
					})
					return;
				}
				this.$refs.uCode.start();
				uni.showLoading({
					title: '正在获取验证码',
					mask: true
				})
				// this.$navTo(`pagesMy/telephone/captcha?mobilePhone=${this.mobilePhone}`);
				// return;
				this.$u.api.getVerificationCode({
					phone: this.mobilePhone
				}).then(res => {
					uni.hideLoading();
					uni.showToast({
						duration: 2000,
						title: '发送成功',
						icon: 'none'
					})
					this.$u.toast('验证码已发送');
					setTimeout(() => {
						this.$refs.uCode.reset();
						this.$navTo(`pagesMy/telephone/captcha?mobilePhone=${this.mobilePhone}`)
					}, 100);
				}).catch(err => {
					console.log('err', err)
					uni.showToast({
						duration: 2000,
						title: err.message || '发送失败',
						icon: 'none'
					})
				}).finally(() => {
					setTimeout(() => {
						uni.hideLoading();
					}, 1000)
				}) 
			},
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		padding: 15px;
	}

	.footer-btns {
		position: absolute;
		bottom: 40rpx;
		left: 40rpx;
		right: 40rpx;
		margin: 0 auto;

		.next-btn {
			display: block;
			margin-top: 20rpx;
		}
	}
</style>