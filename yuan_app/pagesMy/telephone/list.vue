<template>
	<view class="container">
		<view class="list flex-col">
			<view v-if="!list || !list.length" class="flex-col justify-center items-center">
				暂无数据
			</view>
			<view v-if="list&&list.length>0" class="list-item flex-row justify-between items-center"
				v-for="(item, index) in list" :key="index">
				<view class="item-left flex-col">
					<view class="phone" @longtap="longtapCopy(item.telephone)">{{item.telephone}}</view>
					<view class="status">
						<text>{{item.status==1?"未绑定":"已绑定"}}</text>
						<text v-if="item.status==2" class="code"
							@longtap="longtapCopy(item.devCode)">设备：{{item.devCode||'-'}}</text>
					</view>
				</view>
				<view class="btn">
					<u-button v-if="item.status==1" size="mini"
						:custom-style="{ 'width': '100%', 'color': 'white', 'background-color': '#01B09A' }"
						@click="showSelect(item.telephone)">绑定</u-button>
					<u-button v-if="item.status==2 && item.devCode" type="warning" size="mini" @click="showConfirmModel(item.telephone)"
						plain>解绑</u-button>
				</view>
			</view>
		</view>
		<u-select mode="single-column" v-model="showDevSelect" :list="devList" @confirm="bind" value-name="devCode"
			label-name="devCode" :default-value="selectDefault"></u-select>
		<u-modal v-model="showModal" content="确定要解绑吗" @confirm="unbind"></u-modal>
	</view>
</template>

<script>
	import {
		longtapCopy
	} from '../../utils/util'
	import storage from '@/utils/storage';
	export default {
		data() {
			return {
				mobilePhone: undefined,
				captcha: undefined,
				selectDefault:[0],
				list:[],
				/* list: [{
						devCode: "YA01030878",
						telephone: "07311234567",
						status: "2"
					},
					{
						telephone: "07311234566",
						status: "1"
					},
					{
						devCode: "**********",
						telephone: "07311234565",
						status: "2"
					},
					{
						devCode: "**********",
						telephone: "07311234564",
						status: "2"
					},
					{
						telephone: "07311234563",
						status: "1"
					},
				], */

				showDevSelect: false,
				devList: [],
				selectDevCode: undefined,
				selectTelephone: undefined,

				showModal: false,
			}
		},
		onLoad(option) {
			this.mobilePhone = option.mobilePhone;
			this.captcha = option.captcha;
			this.getLandlineNumbers();
		},
		methods: {
			onPullDownRefresh() {
				console.log("触发下拉刷新")
				setTimeout(() => {
					this.getLandlineNumbers();
					uni.stopPullDownRefresh()
				},1000)
			},
			getLandlineNumbers() {
				uni.showLoading({
					title: '请稍等...',
					mask: true
				})
				this.$u.api.getLandlineNumbers({
					mobilePhone: this.mobilePhone,
					verifyCode: this.captcha,
				}).then(res => {
					uni.hideLoading();
					this.list = res;
					// 保存验证码29分钟，下次进来不要验证码
					storage.set("unicom_captcha_"+this.mobilePhone,this.captcha,1740);
				}).catch(err => {
					console.log('err', err)
					uni.hideLoading();
					uni.showToast({
						duration: 3000,
						title: err.message || '操作失败',
						icon: 'none'
					})
				})
			},
			showSelect(telephone) {
				this.selectTelephone = telephone;
				uni.showLoading({
					title: '请稍等...',
					mask: true
				})
				this.$u.api.fetchMyDeviceList({}).then(res => {
					uni.hideLoading();
					if (res) {
						console.log("dev res:",res);
						this.devList = res.filter(item2 =>
							!this.list.some(item1 => item1.devCode === item2.devCode)
						);
						this.showDevSelect = true;
					} else {
						this.devList = []
					}
					console.log("devList:",this.devList);
					console.log("showSelect selectDefault:",this.selectDefault)
				})
			},
			bind(e) {
				console.log("bind:",e);
				this.selectDevCode = e[0].value;
				// 将默认下标赋值为当前选中的下标
				const index = this.devList.findIndex(item => item.devCode == e[0].value);
				console.log("bind index:",index)
				this.selectDefault = [index>0?index:0];
				console.log("bind selectDefault:",this.selectDefault)
				uni.showLoading({
					title: '请稍等...',
					mask: true
				})
				this.$u.api.bindTelephone({
					mobilePhone: this.mobilePhone,
					telephone: this.selectTelephone,
					devCode: this.selectDevCode
				}).then(res => {
					uni.hideLoading();
					this.getLandlineNumbers();
				}).catch(err => {
					console.log('err', err)
					uni.hideLoading();
					uni.showToast({
						duration: 2000,
						title: err.message || '操作失败',
						icon: 'none'
					})
				})
			},
			showConfirmModel(telephone) {
				this.selectTelephone = telephone;
				this.showModal = true;
			},
			unbind() {
				uni.showLoading({
					title: '请稍等...',
					mask: true
				})
				this.$u.api.unbindTelephone({
					mobilePhone: this.mobilePhone,
					telephone: this.selectTelephone,
				}).then(res => {
					uni.hideLoading();
					this.getLandlineNumbers();
				}).catch(err => {
					uni.hideLoading();
					console.log('err', err)
					uni.showToast({
						duration: 2000,
						title: err.message || '操作失败',
						icon: 'none'
					})
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		padding: 15px;
	}

	.list {
		padding: 6rpx;

		.list-item {
			padding: 6px;
			border-bottom: 1px solid #999;

			.item-left {
				.phone {
					font-size: 30rpx;

				}

				.status {
					font-size: 24rpx;
					color: #999;
					margin-top: 8rpx;
				}

				.code {
					margin-left: 1rem;
				}
			}
		}
	}
</style>