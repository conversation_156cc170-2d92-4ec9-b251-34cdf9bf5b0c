<template>
	<view class="container">
		<view class="flex-col items-center justify-center">
			<text>请输入验证码</text>
			<view class="flex-row items-center justify-center block1">
				<text class="descr">已发送至手机</text>
				<text class="descr">{{mobilePhone}}</text>
				<text @click="getCode">重新发送</text>
				<text v-if="countdown>0">({{countdown}})</text>
			</view>
			<u-message-input :maxlength="6" @change="change" @finish="finish" mode="middleLine"></u-message-input>
		</view>
		<view class="footer-btns">
			<u-button shape="circle" class="next-btn diy-btn" size="medium"
				:custom-style="{ 'width': '100%', 'color': 'white', 'background-color': '#01B09A' }" hover-class="none"
				@click="submit">获取固话</u-button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				mobilePhone: undefined,
				captcha: undefined,
				// 倒计时时间
				countdown: 60,
				timer: null,
			}
		},
		onLoad(option) {
			this.mobilePhone = option.mobilePhone;
			this.startCountdown();
		},
		onUnload() {
			// 页面卸载时如果还在倒计时，需要清理定时器以避免内存泄漏
			if (this.timer) {
				clearInterval(this.timer);
			}
		},
		methods: {
			change(e) {
				this.captcha = e;
			},
			finish(e) {
				this.captcha = e;
			},
			startCountdown() {
				this.timer = setInterval(() => {
					this.countdown--;
					if (this.countdown <= 0) {
						clearInterval(this.timer);
					}
				}, 1000);
			},
			getCode() {
				if (this.countdown > 0) {
					uni.showToast({
						duration: 2000,
						title: '请稍后再试',
						icon: 'none'
					})
					return;
				}
				this.startCountdown();
				this.$u.api.getVerificationCode({
					phone: this.mobilePhone
				}).then(res => {
					uni.showToast({
						duration: 2000,
						title: '验证码已发送',
						icon: 'none'
					})
					this.captcha = ""
				}).catch(err => {
					console.log('err', err)
					uni.showToast({
						duration: 2000,
						title: err.message || '发送失败',
						icon: 'none'
					})
				})
			},
			submit() {
				if (!this.captcha || this.captcha.length < 6) {
					uni.showToast({
						duration: 2000,
						title: '请输入验证码',
						icon: 'none'
					})
					return;
				}
				if (this.timer) {
					clearInterval(this.timer);
				}
				this.$navTo(`pagesMy/telephone/list?mobilePhone=${this.mobilePhone}&captcha=${this.captcha}`);
			},
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		padding: 15px;
	}

	.footer-btns {
		position: absolute;
		bottom: 40rpx;
		left: 40rpx;
		right: 40rpx;
		margin: 0 auto;

		.next-btn {
			display: block;
			margin-top: 20rpx;
		}
	}

	.block1 {
		margin: 12px;
	}

	.descr {
		font-size: 13px;
		color: #999;
		margin-right: 8px;
	}
</style>