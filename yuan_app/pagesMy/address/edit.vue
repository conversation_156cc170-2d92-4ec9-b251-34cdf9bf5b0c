<template>
	<view class="device-connector-edit-page">
		<u-form :model="form" ref="uForm">
			<u-field v-model="form.consignee" label="收件人" placeholder="请输入" required maxlength="10">
			</u-field>
			<u-field v-model="form.phone" label="手机号" placeholder="请输入" required>
			</u-field>
			<u-field @click="openMapSelectDialog" type="textarea" v-model="form.address" label="地区" disabled
				placeholder="省、市、区、街道" right-icon="arrow-down-fill" required>
			</u-field>
			<u-field type="textarea" v-model="form.detail" label="详细地址" placeholder="小区楼栋/乡村名称" maxlength="50" required></u-field>
			<u-form-item label="设为默认收货地址">
				<u-switch slot="right" v-model="form.isDefault"></u-switch>
			</u-form-item>
		</u-form>

		<view class="footer-btns">
			<u-button shape="circle" class="next-btn diy-btn" size="medium"
				:custom-style="{ 'width': '100%', 'color': 'white', 'background-color': '#01B09A' }" hover-class="none"
				@click="handleSave">保存</u-button>
		</view>

	</view>
</template>

<script>
	export default {
		data() {
			return {
				deleteDialog: false,
				form: {
					consignee: undefined,
					phone: undefined,
					address: undefined,
					detail: undefined,
					isDefault: false,
				},
			}
		},
		onLoad(option) {
			this.id = option.id
			if (option.id != null && option.id != undefined) {
				this.fetchDetailInfo(option.id)
			}
		},
		methods: {
			openMapSelectDialog() {
				console.log(1)
				let _that = this;
				uni.chooseLocation({
					success: (res) => {
						console.log('位置名称：' + res.name);
						console.log('详细地址：' + res.address);
						console.log('纬度：' + res.latitude);
						console.log('经度：' + res.longitude);
						_that.form.address = res.address.indexOf(res.name) != -1 ? res.address : res.address + res
							.name;
						//_that.form.latitude = res.latitude;
						//_that.form.longitude = res.longitude;

					}
				});
			},
			fetchDetailInfo(id) {
				uni.showLoading({
					title: '加载中...',
					mask: true
				})
				this.$u.api.getAddressById({
					id: id
				}).then(res => {
					if (res) {
						this.form = res
					}
				}).catch(err => {
					uni.showToast({
						duration: 2000,
						title: res.message || '发生未知错误',
						icon: 'none'
					})
				}).finally(() => {
					setTimeout(() => {
						uni.hideLoading();
					}, 1000)
				})
			},
			handleSave() {
				if (!this.form.consignee || !this.form.phone || !this.form.address || !this.form.detail) {
					uni.showModal({
						content: '请检查“*”必填项是否填写正确',
						showCancel: false,
						confirmText: '关闭'
					})
					return;
				}
				if (/^[1][3,4,5,6,7,8,9][0-9]{9}$/.test(this.form.phone) === false) {
					uni.showModal({
						content: '请检查电话是否填写正确',
						showCancel: false,
						confirmText: '关闭'
					})
					return;
				}
				const isDefault = this.form.isDefault;
				let _params = {
					...this.form,
				}
				uni.showLoading({
					title: '保存中...',
					mask: true
				})
				this.$u.api.saveAddress(_params).then(res => {
					uni.showToast({
						duration: 2000,
						title: (this.id ? '修改' : '添加') + '成功',
						icon: 'none'
					})
					if(isDefault){
						uni.removeStorageSync('consignee')
						uni.setStorageSync('consignee', this.form);
					}
					setTimeout(() => {
						uni.navigateBack({
							delta: 1
						})
					}, 100);
				}).catch(err => {
					console.log('err', err)
					uni.showToast({
						duration: 2000,
						title: err.message || '发生未知错误',
						icon: 'none'
					})
				}).finally(() => {
					setTimeout(() => {
						uni.hideLoading();
					}, 1000)
				})
			},
		}
	}
</script>

<style lang="scss">
	page {
		background-color: #f7f7f7;
		// #ifdef H5
		height: 100%;
		// #endif
	}
</style>
<style lang="scss" scoped>
	.device-connector-edit-page {
		background-color: white;

		.icon-reqired {
			color: red;
			display: inline-block;
			vertical-align: middle;
			font-size: 40rpx;
			position: relative;
			top: 4rpx;
			left: 6rpx;
		}

		::v-deep .u-label {
			font-size: 32rpx;
			width: 240rpx;
			display: block;
			color: #0D0D0D;
			flex: initial !important;

			&::before {
				font-size: 40rpx;
				top: 10rpx;
			}

			.u-label-text {
				padding-left: 20rpx;
				font-weight: bold;
				box-sizing: border-box;
			}
		}

		.direction-field {
			::v-deep .u-field-inner {
				height: 120rpx;
				align-items: baseline;
			}
		}

		.footer-btns {
			position: absolute;
			bottom: 40rpx;
			left: 40rpx;
			right: 40rpx;
			margin: 0 auto;

			.next-btn {
				display: block;
				margin-top: 20rpx;
			}
		}

		::v-deep .u-form-item--left {
			width: 300rpx !important;
			flex: 0 0 300rpx !important;
		}

		::v-deep .u-form-item--left__content__label {
			font-size: 32rpx;
			display: block;
			color: #0D0D0D;
			flex: initial !important;
			padding-left: 20rpx;
			font-weight: bold;
			box-sizing: border-box;

			&::before {
				font-size: 40rpx;
				top: 10rpx;
			}

		}

		::v-deep .u-model__footer__button {
			height: 88rpx;
			line-height: 88rpx;
		}

		::v-deep .u-field {
			padding: 28rpx;
		}
	}
</style>
