<template>
	<view class="container">
		<RecordNotFound v-if="!list || !list.length" :pic="`${staticUrl}/images/device/pic-connector-notfound.png`"
			desc="暂无收货地址" desc2="请先新增收货地址"></RecordNotFound>
		<view v-else class="list">
			<view v-for="(item, index) in list" :key="index">
				<u-row class="item-block">
					<u-col span="10">
						<view @click="select(item)">
							<view class="r-flex">
								<text class="name">{{ item.consignee || '-' }}</text>
								<text class="phone">{{ item.phone || '-' }}</text>
								<u-tag class="tag" v-if="item.isDefault" text="默认" size="mini" mode="dark"
									bg-color="#FA6400"></u-tag>
							</view>
							<view class="address">{{ (item.address||'') + (item.detail||'')}}</view>
						</view>
					</u-col>
					<u-col span="2">
						<text class="icon iconfont icon-xiugai" style="font-size: 30rpx; color: #01B09A;"
							@click.stop="goEdit(item.id)"></text>
						<view @click.stop="gotoRemove(item.id, index)" style="display:inline-block;margin-left: 20rpx;">
							<u-icon name="trash" color="#01B09A" size="36"></u-icon>
						</view>
					</u-col>
				</u-row>
			</view>
		</view>
		<view class="footer-btns">
			<u-button shape="circle" class="next-btn diy-btn" size="medium"
				:custom-style="{ 'width': '100%', 'color': 'white', 'background-color': '#01B09A' }" hover-class="none"
				@click="$navTo('pagesMy/address/edit')">新增收货地址</u-button>
		</view>
	</view>
</template>

<script>
	import RecordNotFound from '../components/record-not-found/index'
	export default {
		components: {
			RecordNotFound
		},
		data() {
			return {
				source:"",
				list: [],
			}
		},
		onLoad(option) {
			this.source = option.source;
			
		},
		onShow() {
			this.fetchDatas();
		},
		methods: {
			select(item){
				if(this.source == "goods"){
					uni.removeStorageSync('consignee')	
					uni.setStorageSync( 'consignee', item);
					setTimeout(() => {
						uni.navigateBack({
							delta: 1
						})
					}, 100);
				}
			},
			goEdit(id) {
				this.$navTo('pagesMy/address/edit?id=' + id)
			},
			gotoRemove(id, index) {
				uni.showModal({
					content: '确定要删除吗？',
					showCancel: true,
					success: ({
						confirm,
						cancel
					}) => {
						if (cancel) {
							return;
						}

						uni.showLoading({
							title: '操作中...',
							mask: true
						})
						this.$u.api.deleteAddress({
							id: id
						}).then(res => {
							uni.showToast({
								duration: 1000,
								title: '删除成功',
								icon: 'none'
							})
							this.list.splice(index, 1);
						}).catch(err => {
							console.log('err', err)
							uni.showToast({
								duration: 2000,
								title: err.message || '发生未知错误',
								icon: 'none'
							})
						}).finally(() => {
							setTimeout(() => {
								uni.hideLoading();
							}, 1000)
						})
					}
				})
			},
			gotoUrl(url) {
				if (!url) return;
				uni.navigateTo({
					url: url
				})
			},
			fetchDatas() {
				uni.showLoading({
					title: '加载中...',
					mask: true
				})
				this.$u.api.fetchAddressList({}).then(res => {
					if (res) {
						this.list = res
					}
				}).catch(err => {
					this.list = {}
				}).finally(() => {
					setTimeout(() => {
						uni.hideLoading();
					}, 1000)
				})
			},
		}
	}
</script>
<style lang="scss">
	page {
		background-color: #f7f7f7;
	}
</style>
<style lang="scss" scoped>
	@import url("/static/css/iconfont.css");

	.container {
		padding: 24rpx;

		.list {
			background: white;
			padding: 12rpx 12rpx 0;
			border-radius: 20rpx;

			.item-block {
				padding: 24rpx 12rpx;
				border-bottom: 2rpx solid #ececec;
				font-size: 32rpx;

				.name {
					font-weight: 700;
				}

				.phone {
					color: #999;
					font-size: 28rpx;
					margin-left: 20rpx;
				}

				.tag {
					margin-left: 20rpx;
				}

				.address {
					margin-top: 12rpx;
					font-size: 26rpx;
				}

				&:nth-child(n + 2) {
					margin-top: 20rpx;
				}
			}
		}

		.footer-btns {
			position: fixed;
			bottom: 40rpx;
			left: 40rpx;
			right: 40rpx;
			margin: 0 auto;

			.next-btn {
				display: block;
				margin-top: 20rpx;
			}
		}
	}
</style>
