<template>
	<view class="page-wrap">
		<view class="top-header-line"></view>
		<event-order-list ref="eventOrder"></event-order-list>
	</view>
</template>

<script>
export default {
	data() {
		return {
		}
	},
	onLoad(option) {
	},
	onReady() {
		this.fetchDatas();
	},
	onReachBottom() {
		this.fetchDatas();
	},
	onShow() {
	},
	methods: {
		fetchDatas() {
			this.$refs["eventOrder"].fetchNextPage();
		}
	}
}
</script>

<style lang="scss" scoped>
.page-wrap {
	height: 100vh;
	//#ifdef H5
	height: 100%;
	//#endif
	padding-top: 1rpx;
}
</style>
