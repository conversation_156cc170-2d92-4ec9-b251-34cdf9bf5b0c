<template>
	<view class="my-help-index-page">
		<view class="top-header-line"></view>
		<u-grid :col="4" :border="false">
			<u-grid-item @click="readPdf">
				<text class="icon iconfont icon-caozuoshuomingshushuomingshu" style="margin-bottom: 10rpx;"></text>
				<view class="grid-text">说明书</view>
			</u-grid-item>
			<!-- <u-grid-item @click="$navTo('pagesMy/article/index?key=deviceMaintain')">
				<text class="icon iconfont icon-ico_jiudianguanli_shebeiweixiudengji" style="margin-bottom: 10rpx;"></text>
				<view class="grid-text">设备维修</view>
			</u-grid-item> -->
		</u-grid>
	</view>
</template>

<script>
	export default {
		data() {
			return {
			}
		},
		onShow() {
		},
		methods: {
			readPdf() {
				uni.showLoading({
					title: '加载中...',
					mask: true
				})
				uni.downloadFile({
				  // url: `${this.$u.http.config.staticBaseUrl}/pdf/instructions.pdf`,
				  url: `${this.$u.http.config.staticBaseUrl}/images/common/instructions.pdf`,
				  success: (res) => {
				    var filePath = res.tempFilePath;
				    uni.openDocument({
				      filePath: filePath,
				      showMenu: true
				    });
				  },
				  complete: () => {
					  uni.hideLoading();
				  }
				});
			}
		}
	}
</script>

<style lang="scss">
page {
	background-color: white;
}
</style>
<style lang="scss" scoped>
@import url("/static/css/iconfont.css");
.my-help-index-page {
	.grid-text {
		font-size: 26rpx;
		color: #8b8b8b;
	}
}
::v-deep .icon {
	font-size: 50rpx;
	color: #8b8b8b;
}
</style>
