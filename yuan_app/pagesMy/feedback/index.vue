<template>
	<view class="my-feedback-page">
		<view class="top-header-line"></view>
		<view class="item-block">
			<view class="tip" style="position: relative; top: 6rpx;">反馈以下功能的描述</view>
			<u-row v-for="(qa, index) in qas" :key="index" 
				:style="{ 'border-bottom': index < qas.length - 1 ? '2rpx solid #f1f1f1' : 'none' }"
				@click="$navTo(`pagesMy/feedback/submit?feedbackTypeId=${qa.id}`)">
				<u-col span="5">
					<view class="left-info">
						<view class="field-name" style="position: relative; top: 6rpx;">{{ qa.name }}</view>
					</view>
				</u-col>
				<u-col span="7">
					<view class="right-status">
						{{ qa.remark || '' }}
						<u-icon name="arrow-right" color="#b9b9b9" size="22"></u-icon>
					</view>
				</u-col>
			</u-row>
		</view>
		
	</view>
</template>

<script>
	export default {
		data() {
			return {
				qas: []
			}
		},
		onLoad(option) {
			this.fetchList();
		},
		methods: {
			fetchList() {
				this.$u.api.fetchFeedbackTypeList({ }).then(res => {
					this.qas = res
				})
			}
		}
	}
</script>

<style lang="scss">
page {
	background-color: #F7F7F7;
}
</style>

<style lang="scss" scoped>
.my-feedback-page {
	margin-top: 20rpx;
	.item-block {
		// height: 784rpx;
		background: white;
		padding: 0rpx 30rpx;
		color: $u-content-color;
		font-size: 28rpx;
		// box-shadow: 0rpx 8rpx 24rpx 0rpx rgba(0, 0, 0, 0.1);
		border-radius: 16rpx;
		.tip {
			padding-top: 30rpx;
			display: block;
			color: #888;
			font-size: 28rpx;
			font-weight: bold;
			padding-left: 10rpx;
			padding-bottom: 30rpx;
			border-bottom: 1px solid #f1f1f1;
			//#ifdef H5
			padding-top: 5rpx;
			padding-bottom: 5rpx;
			//#endif
		}
		u-row {
			display: block;
			line-height: 60rpx;
			padding: 18rpx 0;
		}
		//#ifdef H5
		line-height: 50px;
		//#endif
		.left-info {
			image {
				width: 36rpx;
				height: 36rpx;
				display: inline-block;
				margin-right: 20rpx;
				vertical-align: middle;
			}
			.field-name {
				display: inline-block;
				color: #454444;
				font-size: 28rpx;
				position: relative;
				top: 2rpx;
				font-weight: bold;
			}
		}
		.right-status {
			color: #b9b9b9;
			text-align: right;
			// margin-top: 28rpx;
			::v-deep .u-icon {
				margin-left: 10rpx;
				position: relative;
				top: -3rpx;
			}
			::v-deep .circle-tip-num {
				display: inline-block;
				width: 30rpx;
				height: 30rpx;
				border-radius: 30rpx;
				text-align: center;
				background: red;
				color: white;
			}
			
		}
		&:nth-child(n + 2) {
			margin-top: 20rpx;
		}
	}
}
</style>
