<template>
	<view class="my-feedback-submit-page">
		<view class="top-header-line"></view>
		<view v-if="qas && qas.length" class="item-block">
			<view class="title">请选择你遇到的问题</view>
			<view class="qa-list">
				<view v-for="(qa, index) in qas" :key="index" class="qa-item" @click="handleQaClick(qa)">
					<text v-if="qaSelectRdo == qa.id" class="icon iconfont icon-xuanzhong radio" style="color: #01B09A"></text>
					<text v-else class="icon iconfont icon-weixuanzhong radio"></text>
					<text>{{ qa.name }}</text>
				</view>
			</view>
		</view>
		<view class="item-block">
			<view class="title">问题描述</view>
			<view>
				<u-input type="textarea" v-model="feedback.problemDesc" height="180" :border="true" placeholder="请输入使用中遇到的问题" />
			</view>
		</view>
		<view class="item-block">
			<view class="title">添加图片</view>
			<view>
				<u-upload :action="action" :file-list="fileList" :auto-upload="true" 
					:header="{ 'token': token }" :show-progress="false"
					:max-count="9" style="position: relative; left: -8rpx;" 
					@on-success="handleImgUploadSuccess" @on-remove="handleImgRemove"></u-upload>
			</view>
		</view>
		
		
		<view class="footer-btns">
			<u-button shape="circle" class="next-btn diy-btn" size="medium" 
			:custom-style="{ 'width': '100%', 'color': 'white', 'background-color': '#01B09A' }"  hover-class="none"
			@click="handleSubmit">保存</u-button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				qas: [],
				qaSelectRdo: undefined,
				pics: [],
				feedback: {
					feedbackPic: undefined,
					feedbackType: undefined,
					problemDesc: undefined,
					problemType: undefined,
					eventOrderId:undefined,
				},
				// 演示地址，请勿直接使用
				action: '',
				fileList: [],
				token: '',
			}
		},
		onLoad(option) {
			this.token = uni.getStorageSync('token');
			this.action = `${this.$u.http.config.baseUrl}/pinanbao/my/img/upload`;
			this.feedback.feedbackType = option.feedbackTypeId;
			this.feedback.eventOrderId = option.eventOrderId;
			this.qaSelectRdo = option.problemTypeId;
			this.fetchList(option.feedbackTypeId)
			
		},
		methods: {
			handleQaClick(qa) {
				this.qaSelectRdo = this.qaSelectRdo === qa.id ? undefined : qa.id
			},
			fetchList(feedbackTypeId) {
				if (!feedbackTypeId) return;
				this.$u.api.fetchProblemTypeList({ feedbackTypeId: feedbackTypeId }).then(res => {
					this.qas = res
				})
			},
			handleSubmit() {
				if (this.qas && this.qas.length && !this.qaSelectRdo) {
					this.$toast('请选择问题')
					return;
				} else if (!this.feedback.problemDesc) {
					this.$toast('请输入问题描述')
					return;
				} else if (!this.pics || !this.pics.length) {
					this.$toast('请选择图片')
					return;
				}
				uni.showLoading({
					title: '保存中...',
					mask: true
				})
				let _params = JSON.parse(JSON.stringify(this.feedback));
				_params['problemType'] = this.qaSelectRdo;
				_params['feedbackPic'] = this.pics.join(',');
				this.$u.api.execCommitProblemFeedback(_params).then(res => {
					this.$toast('问题反馈提交成功')
					// this.deleteDialog = true
					setTimeout(() => {
						uni.navigateBack({
							delta: 1
						})
					}, 500);
				}).catch(err => {
					console.log('err', err)
					this.$toast(err.message || '发生未知错误')
				})
			},
			handleImgUploadSuccess(res) {
				this.pics.push(res.data);
			},
			handleImgRemove(res) {
				this.pics.splice(res, 1)
			}
		}
	}
</script>

<style lang="scss">
page {
	background-color: #fff;
}
</style>

<style lang="scss" scoped>
@import url("/static/css/iconfont.css");
.my-feedback-submit-page {
	padding: 20rpx 40rpx;
	padding-bottom: 150rpx;
	.item-block {
		// height: 784rpx;
		// background: white;
		// padding: 24rpx 30rpx;
		// color: $u-content-color;
		font-size: 28rpx;
		margin-top: 30rpx;
		.title {
			font-size: 32rpx;
			margin-bottom: 16rpx;
			font-weight: bold;
		}
		.qa-list {
			line-height: 44rpx;
			.qa-item {
				.radio {
					position: relative;
					top: -2rpx;
					margin-right: 10rpx;
					font-size: 36rpx;
					vertical-align: middle;
				}
				&:nth-child(n + 2) {
					margin-top: 10rpx;
				}
			}
		}
		// box-shadow: 0rpx 8rpx 24rpx 0rpx rgba(0, 0, 0, 0.1);
		// border-radius: 16rpx;
		// padding-top: 0rpx;
	}
	.footer-btns {
		position: fixed;
		bottom: 40rpx;
		left: 40rpx;
		right: 40rpx;
		margin: 0 auto;
		.next-btn {
			display: block;
			margin-top: 20rpx;
		}
	}
}
</style>
