import Vue from 'vue';
import App from './App';

Vue.config.productionTip = false;

App.mpType = 'app';

// 此处为演示Vue.prototype使用，非uView的功能部分
Vue.prototype.vuePrototype = '枣红';

// 引入全局uView
import uView from 'uview-ui';
Vue.use(uView);
// 引入konva
import VueKonva from 'vue-konva';
Vue.use(VueKonva);

// 此处为演示vuex使用，非uView的功能部分
// import store from '@/store';

// 引入uView提供的对vuex的简写法文件
// let vuexStore = require('@/store/$u.mixin.js');
// Vue.mixin(vuexStore);

// 引入uView对小程序分享的mixin封装
// let mpShare = require('uview-ui/libs/mixin/mpShare.js');
// Vue.mixin(mpShare);

// i18n部分的配置
// 引入语言包，注意路径
// import Chinese from '@/common/locales/zh.js';
// import English from '@/common/locales/en.js';

// VueI18n
// import VueI18n from '@/common/vue-i18n.min.js';

// // #ifdef H5
// // 提交前需要注释  本地调试使用
// const vconsole = require('vconsole')
// Vue.prototype.$vconsole = new vconsole() // 使用vconsole
// // #endif

// VueI18n
// Vue.use(VueI18n);

// const i18n = new VueI18n({
// 	// 默认语言
// 	locale: 'zh',
// 	// 引入语言文件
// 	messages: {
// 		'zh': Chinese,
// 		'en': English,
// 	}
// });

// 由于微信小程序的运行机制问题，需声明如下一行，H5和APP非必填
// Vue.prototype._i18n = i18n;

import platform from './core/platform'
Vue.prototype.$platform = platform

import {
  navTo,
  showToast,
  showSuccess,
  showError,
  getShareUrlParams
} from './core/app'
// 挂载全局函数
Vue.prototype.$toast = showToast
Vue.prototype.$success = showSuccess
Vue.prototype.$error = showError
Vue.prototype.$navTo = navTo
Vue.prototype.$getShareUrlParams = getShareUrlParams

	// i18n,
	// store,
const app = new Vue({
	...App
});

// 点亮转发给朋友、分享到朋友圈、复制链接
import share from './core/share.js'
Vue.mixin(share)

// http拦截器，将此部分放在new Vue()和app.$mount()之间，才能App.vue中正常使用
import httpInterceptor from '@/common/http.interceptor.js';
Vue.use(httpInterceptor, app);

// http接口API抽离，免于写url或者一些固定的参数
import httpApi from '@/common/http.api.js';
Vue.use(httpApi, app);

app.$mount();
