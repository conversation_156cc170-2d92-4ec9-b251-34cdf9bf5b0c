<template>
	<view>
		<!-- #ifdef MP-WEIXIN -->
		<uni-ec-canvas class="uni-ec-canvas" ref="residenceTimeCanvas" canvas-id="residence-time-lazy-load-chart"
			:ec="ec"></uni-ec-canvas>
		<!-- #endif -->
		<!-- #ifndef MP-WEIXIN -->
		<view :id="optionId" class="uni-ec-canvas"></view>
		<!-- #endif -->
	</view>
</template>

<script>
	import uniEcCanvas from '@/components/uni-ec-canvas/uni-ec-canvas.vue';
	import * as echarts from "@/components/uni-ec-canvas/echarts";
	let residenceTimeChart = null
	export default {
		components: {
			uniEcCanvas
		},
		props: {
			dataObj: {
				type: Object,
				default: function() {
					return {}
				},
			},
		},
		data() {
			return {
				optionId: undefined,
				ec: {
					lazyLoad: true
				},
				option: {
					tooltip: {
						trigger: 'axis',
						axisPointer: {
							// 坐标轴指示器，坐标轴触发有效
							type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
						},
						formatter: function(params) {
							console.log('params', params[0].marker)
							return `${params[0].seriesName}: ${params[0].data}分`;
						},
						textStyle: {
							fontSize: 12,
						}
					},
					grid: {
						left: '2%',
						right: '4%',
						bottom: '4%',
						top: '10%',
						containLabel: true,
					},
					xAxis: {
						type: 'category',
						data: this.dataObj.xArrs || [],
						axisLine: {
							show: false,
						},
						axisTick: {
							show: false,
						},
						axisLabel: {
							// interval: 0,
							// rotate: 40,
							textStyle: {
								color: '#555',
								fontSize: 12,
							},
						},
					},
					yAxis: {
						type: 'value',
						axisLine: {
							show: false,
						},
						axisTick: {
							show: false,
						},
						splitLine: {
							show: false,
						},
						axisLabel: {
							textStyle: {
								color: '#555',
								fontSize: 12,
							},
						},
					},
					series: [
						{
							name: '停留时长',
							type: 'bar',
							barWidth: 16,
							itemStyle: {
								normal: {
									color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
										{
											offset: 0,
											color: '#01B09A',
										},
										{
											offset: 1,
											color: '#01B09A',
										},
									]),
									barBorderRadius: [12, 12, 0, 0],
								},
							},
							data: this.dataObj.values || [],
						},
					],
				}
			}
		},
		mounted() {
			// 设置随机数id
			let t = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
			let len = t.length
			let id = ''
			for (let i = 0; i < 32; i++) {
				id += t.charAt(Math.floor(Math.random() * len))
			}
			this.optionId = id
			this.$nextTick(() => {
				// #ifdef MP-WEIXIN
				this.$refs.residenceTimeCanvas.init(this.initChart)
				// #endif
				// #ifndef MP-WEIXIN
				let el = document.getElementById(this.optionId);
				let myChart= echarts.init(el);
				myChart.setOption(this.option);
				// #endif
			})
		},
		beforeDestroy() {
			// #ifdef MP-WEIXIN
			residenceTimeChart.dispose();
			// #endif
			console.log('residenceTimeChart.dispose');
		},
		methods: {
			initChart(canvas, width, height, canvasDpr) {
				residenceTimeChart = echarts.init(canvas, null, {
					width: width,
					height: height,
					devicePixelRatio: canvasDpr
				})
				canvas.setChart(residenceTimeChart)
				
				let _length = (this.dataObj.values || []).length;
				let _barWidth = 16;
				if (_length >= 10 && _length < 20) {
					_barWidth = 12;
				} else if (_length >= 20 && _length < 30) {
					_barWidth = 5;
				} else if (_length >= 30) {
					_barWidth = 2;
				}
				this.option.series[0].barWidth = _barWidth;
				
				residenceTimeChart.setOption(this.option)
				return residenceTimeChart
			}
		}
	}
</script>

<style>
	.uni-ec-canvas {
		width: 100%;
		height: 400rpx;
		display: block;
	}
</style>
