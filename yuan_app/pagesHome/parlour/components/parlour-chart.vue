<template>
	<view>
		<!-- #ifdef MP-WEIXIN -->
		<uni-ec-canvas class="uni-ec-canvas" ref="parlourPieCanvas" canvas-id="parlour-pie-lazy-load-chart"
			:ec="ec"></uni-ec-canvas>
		<!-- #endif -->
		<!-- #ifndef MP-WEIXIN -->
		<view :id="optionId" class="uni-ec-canvas"></view>
		<!-- #endif -->
	</view>
</template>

<script>
	import uniEcCanvas from '@/components/uni-ec-canvas/uni-ec-canvas.vue';
	import * as echarts from "@/components/uni-ec-canvas/echarts";
	let parlourPieChart = null
	export default {
		components: {
			uniEcCanvas
		},
		props: {
			num: {
				type: Number || String,
				default: 0,
			},
			unit: {
				type: String,
				default: '米'
			},
			desc: {
				type: String,
				default: ''
			}
		},
		data() {
			return {
				optionId: undefined,
				ec: {
					lazyLoad: true
				},
				option: {
					title: {
						text: '{a|' + parseInt(this.num || 0) + '}{b|' + this.unit + '}\n{c|' + this.desc + '}',
						x: 'center',
						y: 'center',
						textStyle: {
							rich: {
								a: {
									fontSize: 34,
									color: '#01B09A',
									fontWeight:'600',
								},
								b: {
									fontSize: 14,
									color: '#01B09A',
									padding: [0, 0, 10, 10]
								},
								c: {
									fontSize: 14,
									color: '#01B09A',
									padding: [5, 0]
								}
							}
						}
					},
					angleAxis: {
						max: 100,
						clockwise: true, // 逆时针
						// 隐藏刻度线
						show: false,
						boundaryGap: ['40%', '40%'],
						startAngle: 90,
					},
					radiusAxis: {
						type: 'category',
						show: true,
						axisLabel: {
							show: false,
						},
						axisLine: {
							show: false,
						},
						axisTick: {
							show: false,
						},
					},
					polar: [{
						center: ['50%', '50%'], //中心点位置
						radius: '165%', //图形大小
					}, ],
					series: [{
						name: '小环',
						type: 'gauge',
						radius: '120%', //中间装饰环
						center: ['50%', '50%'],
						startAngle: 0,
						endAngle: 360,
						axisLine: {
							show: false,
						},
						axisTick: {
							show: true,
							lineStyle: {
								color: '#ededed',
								width: 6,
								shadowBlur: 1,
								shadowColor: 'transparent',
							},
							length: 16,
							splitNumber: 4,
						},
						splitLine: {
							show: false,
						},
						axisLabel: {
							show: false,
						},
						detail: {
							show: false,
						},
					},
					{
						type: 'bar',
						z: 10,
						data: [ this.num ],
						showBackground: false,
						backgroundStyle: {
							color: 'blue',
							borderWidth: 10,
							width: 10,
						},
						coordinateSystem: 'polar',
						roundCap: true,
						barWidth: 16, //大的占比环
						itemStyle: {
							normal: {
								opacity: 1,
								color: new echarts.graphic.LinearGradient(0, 0, 1, 1, [{
										offset: 0,
										color: '#01B09A',
									},
									{
										offset: 1,
										color: '#01B09A',
									},
								]),
							},
						},
					}]
				}
			}
		},
		mounted() {
			// 设置随机数id
			let t = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
			let len = t.length
			let id = ''
			for (let i = 0; i < 32; i++) {
				id += t.charAt(Math.floor(Math.random() * len))
			}
			this.optionId = id
			this.$nextTick(() => {
				// #ifdef MP-WEIXIN
				this.$refs.parlourPieCanvas.init(this.initChart)
				// #endif
				// #ifndef MP-WEIXIN
				let el = document.getElementById(this.optionId);
				let myChart= echarts.init(el);
				myChart.setOption(this.option);
				// #endif
			})
		},
		beforeDestroy() {
			// #ifdef MP-WEIXIN
			parlourPieChart.dispose();
			// #endif
			console.log('parlourPieChart.dispose');
		},
		methods: {
			initChart(canvas, width, height, canvasDpr) {
				parlourPieChart = echarts.init(canvas, null, {
					width: width,
					height: height,
					devicePixelRatio: canvasDpr
				})
				canvas.setChart(parlourPieChart)
				parlourPieChart.setOption(this.option)
				return parlourPieChart
			}
		}
	}
</script>

<style>
	.uni-ec-canvas {
		width: 100%;
		height: 300rpx;
		display: block;
	}
</style>
