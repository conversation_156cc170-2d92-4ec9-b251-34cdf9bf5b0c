<template>
	<view class="home-events-page">
		
		<view style="background-color: white;">
			<view class="top-date">
				<text class="icon iconfont icon-fan<PERSON><PERSON>jian" style="font-size: 32rpx; vertical-align: middle; position: relative; top: -1rpx; right: 10rpx;" @click="changeDay(-1)"></text>
				<text @click="dialog.calendar.show = true">{{ dateStr }}</text>
				<text class="icon iconfont icon-dayukuangxuanze" style="font-size: 32rpx; vertical-align: middle; position: relative; top: -1rpx; left: 10rpx;" @click="changeDay(1)"></text>
				<text class="icon iconfont icon-rili" style="font-size: 42rpx; vertical-align: middle; color: #01B09A; position: absolute; top: -2rpx; right: 0rpx;" @click="dialog.calendar.show = true"></text>
			</view>
			<view class="r-flex" style="padding: 10rpx 0rpx; padding-bottom: 0rpx; margin-top: 10rpx;">
				<view class="r-flex-1">
					共 {{ eventTotal || 0 }} 个事件
				</view>
				<!-- <view class="ta-r" style="width: 100rpx;">
					<u-icon name="calendar" :color="date ? '#01B09A' : '#bbb'" size="50" @click="dialog.calendar.show = true"></u-icon>
				</view>icon-rili -->
			</view>
		</view>
		
		<u-time-line style="position: relative; left: 20rpx; top: 20rpx;">
			<u-time-line-item v-for="(event, index) in events" :key="index" nodeTop="2">
				<template v-slot:node>
					<view class="u-node" :style="{ 'background': '' }">
					{{ $u.timeFormat(event.createTime, 'hh:MM:ss') }}
						<!-- <u-icon name="bookmark" color="#fff" :size="24"></u-icon> -->
					</view>
				</template>
				<template v-slot:content>
					<view>
						<view class="u-order-time">{{ $u.timeFormat(event.createTime, 'hh:MM:ss') }}</view>
						<view class="u-order-type">{{ event.orderTypeName }}</view>
						<view class="u-order-desc"><view class="status">{{ event.statusName }}</view></view>
					</view>
				</template>
			</u-time-line-item>
		</u-time-line>
		<u-loadmore v-if="eventTotal && events.length >= 10" :status="status" :load-text="loadText" :margin-top="20" @loadmore="fetchEventDatas" />
		
		<u-calendar v-model="dialog.calendar.show" :curr-day="date" mode="date" btn-type="success" active-bg-color="#01B09A" @change="handleDateChange"></u-calendar>
	</view>
</template>

<script>
	export default {
		props: {
			devId: {
				type: String || Number,
				required: true
			}
		},
		data() {
			return {
				status: 'loadmore',
				loadText: {
					loadmore: '点击加载更多',
					loading: '努力加载中',
					nomore: '实在没有了'
				},
				dialog: {
					calendar: {
						show: false
					}
				},
				date: this.$u.timeFormat(new Date(), 'yyyy-mm-dd'),
				dateStr: this.$u.timeFormat(new Date(), 'yyyy 年 mm 月 dd 日'),
				current: 1,
				events: [],
				eventTotal: 0,
			}
		},
        mounted() {
			this.fetchEventDatas();
        },
		methods: {
			handleDateChange(day) {
				this.current = 1;
				this.events = [];
				this.eventTotal = 0;
				this.date = day.result;
				this.dateStr = this.$u.timeFormat(day.result, 'yyyy 年 mm 月 dd 日');
				this.fetchEventDatas();
			},
			fetchEventDatas() {
				this.$u.api.fetchDevEventList({ devId: this.devId, current: this.current, dateStr: this.date }).then(res => {
					this.current = this.current + 1;
					if (!res.records.length) {
						this.status = 'nomore'
					} else {
						this.status = 'loadmore'
					}
					this.events = this.events.concat(res.records)
					this.eventTotal = res.total
				})
			},
			changeDay(dayNum) {
				let _nowTime = new Date(this.$u.timeFormat(new Date(), 'yyyy/mm/dd') + ' 23:59:59').getTime();
				if (dayNum === -1) {
					this.date = this.$u.timeFormat(new Date(new Date(this.date.replace(/-/g, '/') + ' 00:00:00') - 24 * 60 * 60 * 1000), 'yyyy-mm-dd')
					this.dateStr = this.$u.timeFormat(new Date(this.date.replace(/-/g, '/') + ' 00:00:00'), 'yyyy 年 mm 月 dd 日')
				} else if (dayNum === 1) {
					let _nextTime = new Date(+new Date(this.date.replace(/-/g, '/') + ' 00:00:00') + 24 * 60 * 60 * 1000);
					if (_nextTime.getTime() > _nowTime) {
						return;
					}
					this.date = this.$u.timeFormat(_nextTime, 'yyyy-mm-dd');
					this.dateStr = this.$u.timeFormat(new Date(this.date.replace(/-/g, '/') + ' 00:00:00'), 'yyyy 年 mm 月 dd 日')
				}
				this.current = 1;
				this.events = [];
				this.eventTotal = 0;
				this.fetchEventDatas();
			},
			handleTabChange(index) {
				this.tabActived = index;
			},
		}
	}
</script>

<style lang="scss">
page {
	background-color: #f7f7f7;
	overflow-x: hidden;
}
u-time-line {
	left: 140rpx !important;
}
::v-deep .u-node {
	display: none !important;
}
</style>

<style lang="scss" scoped>
@import url("/static/css/iconfont.css");
.home-events-page {
	.top-date {
		text-align: center;
		position: relative;
	}
	.item-block {
		background: white;
		padding: 30rpx 20rpx;
		font-size: 28rpx;
		margin-top: 30rpx;
		border-radius: 20rpx;
	}
	
	.u-node {
		width: 44rpx;
		height: 44rpx;
		border-radius: 100rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		background: #d0d0d0;
	}
	
	.u-order-title {
		color: #333333;
		font-weight: bold;
		font-size: 28rpx;
	}
	
	.u-order-title.unacive {
		color: rgb(150, 150, 150);
	}
	
	.u-order-desc {
		color: #1B1B1B;
		font-size: 26upx;
		margin-top: 6upx;
		// font-weight: bold;
		.status {
			color: #FF0000;
			display: inline-block;
		}
	}
	
	.u-order-time {
		position: absolute;
		left: -180rpx;
		color: rgba(0,0,0,0.65);
		font-size: 28upx;
	}
	.u-order-type {
		color: #01B09A;
		font-size: 28upx;
	}
}
::v-deep .u-calendar__bottom {
	margin-top: 40rpx;
}
::v-deep .u-calendar__bottom__choose {
	display: none;
}
::v-deep .u-btn--success {
	border-color: #01B09A !important;
	background-color: #01B09A !important;
}
::v-deep .u-success-hover {
	border-color: #00ada2 !important;
	background-color: #00ada2 !important;
}

// #ifndef MP-WEIXIN
::v-deep .u-time-axis {
	left: 140rpx !important;
}
// #endif
</style>
