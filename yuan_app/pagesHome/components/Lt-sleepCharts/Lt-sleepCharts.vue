<template>
	<view>
		<canvas style="height: 50rpx;" :style="style" canvas-id="sleepCanvas" id="sleepCanvas"
			@click="handleSleepcharts"></canvas>

		<view style="display:flex;justify-content: space-between; font-size: 20rpx;color: #999;margin: 18rpx 0 0 0; ">
			<view>{{startSleepTime}}</view>
			<view style="text-align:center;width:300rpx;height:48rpx;border-radius:28rpx;font-size:24rpx;line-height:48rpx; background: #eee;"
				v-if="touchValue.sleepState"
				:style="{ 'color': Object.keys(colors).length === 4 && touchValue.sleepState === '人员离开' ? '#949494' : (colorMapping[touchValue.sleepState] || '#fff') }"
				>
				{{touchValue.startTime}}-{{touchValue.endTime}}
				{{touchValue.sleepState}}
			</view>
			<!-- :class="touchValue.sleepState===0?'sleep-state-0':touchValue.sleepState===1?'sleep-state-1':touchValue.sleepState===2?'sleep-state-2':touchValue.sleepState===3?'sleep-state-3':'sleep-state-99'" -->
			<view>{{endSleepTime}}</view>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			"sleepValue": {
				type: Array
			},
			"setStyle": {
				type: Object
			},
			"colors": {
				type: Object,
				default: function() {
					return {
						'监测到人': '#F66C3E',
						'人员离开': '#00A3F0',
						'人员在床': '#01B09A',
						'人员离床': '#EDEDED'
					}
				}
			}
		},
		watch: {
			"sleepValue": function(newVal, oldVal) {
				this.sleepValue = newVal;
				this.calculateSNTime();
				this.$nextTick(() => {
					if (!this.width) {
						this.setStyleFn(true);
					} else {
						this.drawCharts();
					}
				})
			},
		},
		computed: {
			convertColor() {
				return this.colors.length === 4 && this.touchValue.sleepState === '人员离开' ? '#949494' : (this.colorMapping[this.touchValue.sleepState] || '#fff');
			}
		},
		data() {
			return {
				touchValue: {},
				startSleepTime: "",
				endSleepTime: "",
				style: "",
				colorMapping: this.colors
				// {
				// 	'监测到人': '#F66C3E',
				// 	'人员离开': '#00A3F0',
				// 	'人员在床': '#01B09A',
				// 	'人员离床': '#EDEDED'
				// }
			}
		},
		mounted() {
			this.calculateSNTime();
			this.setStyleFn(true);
		},
		methods: {
			// 设置图表大小
			setStyleFn(isRender) {
				this.style = `margin-left:${this.setStyle.marginLeft}; width:${this.setStyle.width}; display: block;`
				this.$nextTick(() => {
					setTimeout(() => {
						this.getDescBox(isRender);
					}, 300)
				})
			},
			// 获取canvas容器
			getDescBox(isRender) {
				uni.createSelectorQuery().in(this).select(`#sleepCanvas`).boundingClientRect(result => {
					if (result) {
						this.width = result.width;
						this.height = result.height;
						if (isRender) {
							this.drawCharts();
						}
					} else {
						this.getDescBox();
					}
				}).exec();
			},

			// 获睡眠的总秒数
			getAllTime() {
				let allTime = this.sleepValue[this.sleepValue.length - 1].endTime - this.sleepValue[0].startTime;
				return allTime;
			},

			// 将时间戳转为YY-MM-dd
			timestampToTime(timestamp) {
				const date = new Date(timestamp * 1000); // 时间戳为10位需*1000，时间戳为13位的话不需乘1000
				const Y = date.getFullYear() + '-';
				const M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
				const D = (date.getDate() < 10 ? '0' + date.getDate() : date.getDate()) + ' ';
				const h = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':';
				const m = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()) + ':';
				const s = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds();
				return Y + M + D + h + m + s;
			},

			// 计算开始结束时间
			calculateSNTime() {
				this.startSleepTime = this.timestampToTime(this.sleepValue[0].startTime).substr(10, 6);
				this.endSleepTime = this.timestampToTime(this.sleepValue[this.sleepValue.length - 1].endTime).substr(10, 6);
			},

			// 绘制睡眠图
			drawCharts() {
				let _isIos = (uni.getSystemInfoSync().platform === 'ios');
				let ALLTIME = this.getAllTime();
				let {
					width, // x
					height // y
				} = this;
				const context = uni.createCanvasContext('sleepCanvas', this);
				context.save();
				context.beginPath();
				this.ctx = context;
				context.setLineWidth(1);
				let xValue = 0;
				this.sleepValue.map((item, index) => {
					// ============== 计算矩形的颜色 ============== //
					// switch (item.sleepState) {
					// 	case 0: // 醒着
					// 		context.setFillStyle("#ffffff");
					// 		item.sleepStateText = "醒着";
					// 		break;
					// 	case 1: // 浅睡
					// 		context.setFillStyle("#33C7F7");
					// 		item.sleepStateText = "浅睡";
					// 		break;
					// 	case 2: // REM
					// 		context.setFillStyle("#32cd99");
					// 		item.sleepStateText = "REM";
					// 		break;
					// 	case 3: // 深睡
					// 		context.setFillStyle("#3E4CA8");
					// 		item.sleepStateText = "深睡";
					// 		break;
					// 	case 13: // 离床
					// 		context.setFillStyle("#F0AD4E");
					// 		item.sleepStateText = "离床";
					// 		break;
					// 	case 14: // 在床
					// 		context.setFillStyle("#4CD964");
					// 		item.sleepStateText = "在床";
					// 		break;
					// 	case 16: // 离线
					// 		context.setFillStyle("#999999");
					// 		item.sleepStateText = "离线";
					// 		break;
					// 	case '监测到人': // 离线
					// 		context.setFillStyle("#F66C3E");
					// 		item.sleepStateText = "";
					// 		break;
					// 	case '人员离开': // 离线
					// 		context.setFillStyle("#00A3F0");
					// 		item.sleepStateText = "";
					// 		break;
					// 	case '人员在床': // 离线
					// 		context.setFillStyle("#01B09A");
					// 		item.sleepStateText = "";
					// 		break;
					// 	case '人员离床': // 离线
					// 		context.setFillStyle("#EDEDED");
					// 		item.sleepStateText = "";
					// 		break;
					// 	default:
					// 		context.setFillStyle("#fff");
					// 		item.sleepStateText = "其他";
					// }
					context.setFillStyle(this.colorMapping[item.sleepState] || '#fff')
					// ========================================= //

					// ============== 计算矩形的高度 ============== //
					let yValue = 20;
					// if (item.sleepState == 1) {
					// 	yValue = height * 0.4;
					// } else if (item.sleepState == 2) {
					// 	yValue = height * 0.6;
					// } else if (item.sleepState == 3) {
					// 	yValue = height * 0.8;
					// } else if (item.sleepState == 16) {
					// 	yValue = height;
					// } else {
					// 	yValue = height * 0.9;
					// }
					// ========================================= //

					// ============== 计算矩形的宽 ============== //
					let value = item.endTime - item.startTime;
					// ========================================= //

					value = value / ALLTIME * width;
					if (_isIos) {
						context.fillRect(xValue, height, value, -yValue);
					} else {
						context.fillRect(xValue, 10, value, 100);
					}
					// context.fillRect(xValue, height, value, yValue);
					xValue = xValue + value; // 计算下一次x轴开始画的位置
					context.stroke()
				})
				context.restore();
				context.save();
				context.draw()
				// context.setFillStyle('red')
				// context.fillRect(10, 10, 150, 100)
				// context.stroke();
				// context.draw();
			},

			// 点击睡眠图表
			handleSleepcharts(e) {
				// 这段睡眠的总秒数
				const ALLTIME = this.getAllTime();
				// 点击的坐标
				const touchX = e.detail.x - e.currentTarget.offsetLeft;
				// 图表的总宽度
				const chartsWidth = this.width;
				// 睡眠图表的比例尺
				const ruler = chartsWidth / ALLTIME;
				// 获取点击位置在睡眠中从开始到点击这一刻的秒数
				const second = touchX / chartsWidth * ALLTIME;
				// 获取点击位置的时间戳
				const timeStamp = this.sleepValue[0].startTime + second;
				let touchStartTime = null,
					touchEndTime = null;
					
					
				// 获取点击位置对应的数组项
				let _isFind = false;
				this.sleepValue.forEach(item => {
					if (!_isFind && timeStamp > item.startTime && timeStamp < item.endTime) {
						this.touchValue.startTime = this.timestampToTime(item.startTime).substr(11, 5);
						this.touchValue.endTime = this.timestampToTime(item.endTime).substr(11, 5);
						this.touchValue.sleepState = item.sleepState;
						this.touchValue.sleepStateText = item.sleepState;
						console.log('cc', item.startTime, item.endTime, this.timestampToTime(item.startTime).substr(11, 5), this.timestampToTime(item.endTime).substr(11, 5), item.sleepState)
						// _isFind = true;
						this.$forceUpdate()
					}
				})
			},
		}
	}
</script>

<style>
	.sleep-state-0 {
		border: 2rpx solid #999;
		height: 44rpx;
		line-height: 44rpx;
	}

	.sleep-state-1 {
		background-color: rgba(51, 199, 247, 0.2);
		color: #33C7F7;
	}

	.sleep-state-2 {
		background-color: rgba(50, 205, 153, 0.2);
		color: #32cd99;
	}

	.sleep-state-3 {
		background-color: #D8DBEE;
		color: #3E4CA8;
	}

	.sleep-state-99 {
		background-color: #eee;
		color: #ff0000;
	}
</style>
