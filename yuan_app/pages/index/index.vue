<template>
	<view class="index-page" :style="{ 'overflow': danDisplayFlag ? 'hidden' : 'initial' }">
		<scroll-view scroll-y class="scroll-view" @scroll="handleScroll" :class="{ 'scroll-height': familyIsNull }">
			<u-navbar :is-back="false" :background="headerBg" :border-bottom="false">
				<view class="slot-wrap">
					<view v-if="!familys || !familys.length" class="family">
						<view class="family-name">我的家<text class="icon iconfont icon-xialazujian"></text></view>
					</view>
					<view v-else class="family">
						<DeanPopover ref="familyPopover" :btnList="familys" modal-top-pos="8vw" modal-width="40vw" modal-opacity="1" direction="right" 
							@on-select="handleFamilyClick"  @on-show-change="handleShowChange" style="z-index: 2"
							@click.native="handleClosePopover('menuPopover')">
							<!-- <view class="family-name" @click="$navTo('pagesFamily/familys/index')"> -->
							<view class="family-name">
								{{ currFamily.name }}<span style="font-size: 26rpx;">{{ currFamily.selfCreate === false ? `(分享)` : '' }}</span>
								<text v-if="currFamily.guardFamily" class="icon iconfont icon-anquan1" style="margin-left: 10rpx; font-size: 36rpx; color: #01B09A;"></text>
								<text class="icon iconfont icon-xialazujian" style="margin-left: 10rpx; font-size: 22rpx; position: relative; top: -4rpx;"></text>
							</view>
						</DeanPopover>
					</view>
				</view>
				<!--#ifdef MP-WEIXIN -->
				<view slot="right" @click="shareFamily"><u-icon name="share" class="share"></u-icon></view>
				<!--#endif -->
			</u-navbar>
			<!-- <view class="family-address" v-if="!!currFamily.addr"
				  @click="$navTo(`pagesFamily/address/index?id=${currFamily.id}`)"
					@longtap="longtapCopy(currFamily.addr)">{{ currFamily.addr}}
			</view>
			<view class="family-address" v-if="!!!currFamily.addr"  @click="$navTo(`pagesFamily/address/index?id=${currFamily.id}`)">还未设置家庭地址，请前往设置</view>
			 -->
			<view style="padding: 20rpx;">
				<view class="r-flex">
					<view class="r-flex-1">
					</view>
					<view class="ta-r r-flex" style="width: 226rpx;justify-content: flex-end;">
						<!-- 进入当日提醒 -->
						<!--#ifdef H5 -->
						<!-- <u-badge v-if="deviceMsgUnreadCount" :is-dot="true" type="error" style="position: relative;left:60rpx;top:-30rpx;"></u-badge> -->
						<!--#endif -->
						<!--#ifdef MP-WEIXIN -->
						<!-- <u-badge v-if="deviceMsgUnreadCount" :is-dot="true" type="error" style="position: relative;left:82rpx;top:-48rpx;"></u-badge> -->
						<!--#endif -->
						<!-- <text class="icon iconfont icon-winfo-icon-chakanbaogao" style="margin-right:10rpx;font-size: 58rpx; color: #aaa1f6; " @click="$navTo('pages/report/index')"></text> -->
						<!-- <u-icon name="email" color="#01B09A" size="58" style="margin-right:10rpx;" @click="$navTo(`pagesFamily/warning/index-v2?id=${currFamily.id}`)"></u-icon> -->
						<!-- <u-icon name="email" color="#01B09A" size="58" style="margin-right:10rpx;" @click="$navTo(`pagesFamily/warning/dev-msg/index`)"></u-icon> -->

						<DeanPopover ref="menuPopover" :btnList="commandList" modal-left-pos="-20vw" modal-top-pos="11vw" modal-width="240rpx" modal-opacity="1" direction="left"  style="z-index: 2"
							@on-select="handleMenuClick" @on-show-change="handleShowChange"
							@click.native="handleClosePopover('familyPopover')">
							<image src="/static/images/common/icon-more.png" style="width: 85rpx; height: 85rpx; vertical-align: middle;"></image>
						</DeanPopover>
					</view>
				</view>
				
				<view v-if="!familys || !familys.length" class="add-device" style="position: fixed;">
					<view class="add-device-btn" @click="$navTo('pagesDevice/add/index')">
						扫一扫/添加设备
					</view>
					<view class="other-device-join" @click="$navTo(`pagesFamily/device/choice?id=${currFamily.id}`)">
						设备转移<text class="icon iconfont icon-icon-xuanzejinru" style="font-size: 22rpx; margin-left: 10rpx;"></text>
					</view>
				</view>
				<view v-else>
					<!-- <view class="warn-tips">
						<template v-if="devs.length == 0">
						</template>
						<template v-else-if="currFamily.guardFamily">
							温馨提示：我们正在为被监护人提供安全保障，请确保家庭已设置被监护人和紧急联系人，同时可以点击设备，点击‘续费’按钮，确保设备的摔倒检测和久滞检测服务没有过期。
						</template>
						<template v-else>
							温馨提示：点击设备，点击续费按钮，可以查看设备当前的服务情况
						</template>
					</view> -->
					<!-- <view v-if="todayEvents && todayEvents.length" class="item-block alarm"> -->
					<view class="item-block alarm" @click="$navTo(`pagesFamily/warning/index-v2?id=${currFamily.id}`)">
						<view class="r-flex">
							<text class="icon iconfont icon-lishishijian" style="width: 80rpx; font-size: 50rpx; color: red;"></text>
							<view class="r-flex-1">
								<view class="title" style="font-weight: bold;">当日提醒({{ todayStatInfo.eventCount || 0 }}次)</view>
								<!-- <view class="desc">发生<text style="tag">跌倒事件</text></view> -->
								<view v-if="todayEvents && todayEvents.length" v-for="(event, index) in todayEvents" :key="index" class="desc">
									{{ $u.timeFormat(event.createTime, 'hh:MM:ss') }} 设备 {{ event.devName }} 发生<span style="color: #FF0000;">{{ event.orderTypeName }}事件</span>
									<!-- {{ event.title }} -->
								</view>
							</view>
							<view class="warning-arrow">
								<u-icon name="arrow-right" class="icon-arrow-right"></u-icon>
							</view>
						</view>
					</view>
					<!-- <view class="item-block real-info">
						<view class="r-flex" style="margin-top: 20rpx;">
							<view class="r-flex" style="flex: 1;">
								<text class="icon iconfont icon-shebeigaojing" style="width: 110rpx; font-size: 60rpx; color: #01B09A;"></text>
								<view class="r-flex-1">
									<view class="title">今日告警次数</view>
									<view class="desc">{{ todayStatInfo.eventCount || 0 }}次</view>
								</view>
							</view>
							<view class="r-flex" style="flex: 1;">
								<text class="icon iconfont icon-paozhuoderen" style="width: 130rpx; font-size: 90rpx; color: #01B09A;"></text>
								<view class="r-flex-1">
									<view class="title">今日活动距离</view>
									<view class="desc">{{ todayStatInfo.walkingDistance || 0 }}米</view>
								</view>
							</view>
						</view>
					</view> -->

					<!-- <view class="real-info-2">
						<view class="r-flex">
							<view class="r-flex children-flex" style="margin-top: 0rpx;" @click="$navTo('pages/report/index')">
								<text class="icon iconfont icon-winfo-icon-chakanbaogao" style="font-size: 74rpx; color: #aaa1f6; text-align: right;"></text>
								<view class="r-flex-1">
									<view class="title">看报告</view>
									<view class="desc">健康数据及时查看</view>
								</view>
							</view>
							<view class="r-flex" style="width: 20rpx; margin-top: 0rpx;"></view>
							<view class="r-flex children-flex" style="margin-top: 0rpx;" @click="$navTo('pagesMy/event/index')">
								<text class="icon iconfont icon-chakan" style="font-size: 76rpx; color: #82d88b; text-align: right;"></text>
								<view class="r-flex-1">
									<view class="title">查事件</view>
									<view class="desc">跟踪记录安全安心</view>
								</view>
							</view>
						</view>
					</view> -->
					<!--#ifdef MP-WEIXIN -->
					<view v-if="!isFollowedMp" style="width:100%; margin-top: 20rpx;">
						<official-account></official-account>
					</view>
					<!--#endif -->
					<view v-for="(dev, index) in devs" :key="index" class="item-block device-info" :class="{ 'device-offline': dev.status === '2' }" >
						<view @click="navDevStat(dev)">
							<view class="r-flex top-info">
								<!-- <text v-if="dev.guardDevice" class="icon iconfont icon-anquan1"
									style="font-size: 40rpx; position: absolute; top: -24rpx; left: -24rpx; color: #01B09A;"></text> -->
								<image class="left-img" :src="`${staticUrl}/images/device/device-${dev.deviceType}-${dev.status}.png`"></image>
								<view class="r-flex-1">
									<view class="title" style="word-break: break-all;">{{ (dev.devName || dev.devCode) || '' }}
										<span v-if="dev.status === '2'" style="font-size: 22rpx; font-weight: normal; color: #d4d4d4;">{{ dev.selfBind ? '' : `(来自 ${ dev.bindMemberName } 的分享)` }}</span>
										<span v-else style="font-size: 22rpx; font-weight: normal; color: #666;">{{ dev.selfBind ? '' : `(来自 ${ dev.bindMemberName } 的分享)` }}</span>
									</view>
									<view>
										<text class="desc">{{ dev.statusName }}</text>
										<text v-if="dev.wifiRssiLevel===0 && dev.status ==='1'" class="icon iconfont icon-WIFI-0 wifi" style="color: #F00;"></text>
										<text v-if="dev.wifiRssiLevel===1 && dev.status ==='1'" class="icon iconfont icon-WIFI-1 wifi"></text>
										<text v-if="dev.wifiRssiLevel===2 && dev.status ==='1'" class="icon iconfont icon-WIFI-2 wifi"></text>
										<text v-if="dev.wifiRssiLevel===3 && dev.status ==='1'" class="icon iconfont icon-WIFI-3 wifi"></text>
										<text v-if="dev.wifiRssiLevel===4 && dev.status ==='1'" class="icon iconfont icon-WIFI-4 wifi"></text>
										<text style="margin-left: 4rpx;"></text>
										<text v-if="dev.iotRssiLevel===1 && dev.status ==='1'" class="icon iconfont icon-a-4Gxinhao-1 wifi"></text>
										<text v-if="dev.iotRssiLevel===2 && dev.status ==='1'" class="icon iconfont icon-a-4Gxinhao-2 wifi"></text>
										<text v-if="dev.iotRssiLevel===3 && dev.status ==='1'" class="icon iconfont icon-a-4Gxinhao-3 wifi"></text>
										<text v-if="dev.iotRssiLevel===4 && dev.status ==='1'" class="icon iconfont icon-a-4Gxinhao-4 wifi"></text>
									</view>
								</view>
								<view v-if="dev.deviceType=='1' || dev.deviceType=='2'">
									<view v-if="dev.status !== '2' && (dev.devScene === '1' || dev.devScene === '2' || dev.devScene === '3' || dev.devScene === '4')" style="width: 140rpx; min-width: 140rpx; color: #666; font-size: 24rpx; text-align: right;">
										<view v-if="(dev.devScene !== '1' && dev.devScene !== '4') && dev.personStatus === '0'" style="display: inline-block">
											<text class="icon iconfont icon-xuanzhong1 tubiao" ></text>无人
										</view>
										<view v-if="(dev.devScene !== '1' && dev.devScene !== '4') && dev.personStatus === '1'" style="display: inline-block">
											<text class="icon iconfont icon-xuanzhong1 tubiao" style="color: #01B09A;"></text>有人
										</view>
										<view v-if="dev.devScene === '3' && dev.inbedStatus === '1'" style="display: inline-block; margin-left: 20rpx;">
											<text class="icon iconfont icon-xuanzhong1 tubiao" style="font-size: 28rpx; color: #01B09A;"></text>在床
										</view>
										<view v-if="dev.devScene === '3' && dev.inbedStatus === '0'" style="display: inline-block; margin-left: 20rpx;">
											<text class="icon iconfont icon-xuanzhong1 tubiao" ></text>离床
										</view>
										<view v-if="(dev.devScene === '1' || dev.devScene === '4') && dev.moveStatus === '0'" style="display: inline-block; margin-left: 20rpx;">
											<text class="icon iconfont icon-xuanzhong1 tubiao" ></text>{{ dev.moveStatusName || '' }}
										</view>
										<view v-if="(dev.devScene === '1' || dev.devScene === '4') && (dev.moveStatus === '1' || dev.moveStatus === '2')" style="display: inline-block; margin-left: 20rpx;">
											<text class="icon iconfont icon-xuanzhong1" style="font-size: 28rpx; color: #01B09A; vertical-align: middle; margin-right: 10rpx; position: relative; top: -2rpx;"></text>{{ dev.moveStatusName || '' }}
										</view>
									</view>
								</view>
							</view>
							<view v-if="dev.deviceType=='1' || dev.deviceType=='2'" class="realtimeData">
								<view class="r-flex bottom-info">
									<view class="r-flex" style="flex: 1; justify-content: space-evenly;">
										<text class="icon iconfont icon-shijian1" style="width: 100rpx; font-size: 69rpx; color: #01B09A;"></text>
										<view class="r-flex-1">
											<view class="title">总停留时长</view>
											<view class="desc">{{ dev.parlorOtherStayDuration ? dev.parlorOtherStayDuration + '分' : '-' }}</view>
										</view>
									</view>
									<view class="r-flex" style="flex: 1;">
										<text class="icon iconfont icon-huodongshuju" style="width: 100rpx; font-size: 80rpx; color: #01B09A;"></text>
										<view class="r-flex-1">
											<view class="title">今日活动</view>
											<view class="desc">{{ dev.walkingDistance ? dev.walkingDistance + '米' : '-' }}</view>
										</view>
									</view>
								</view>
								<view class="r-flex bottom-info">
									<view class="r-flex" style="flex: 1; justify-content: space-evenly;">
										<text class="icon iconfont icon-a-icon_menchumen" style="width: 100rpx; font-size: 72rpx; color: #01B09A;"></text>
										<view class="r-flex-1">
											<view class="title">今日进出</view>
											<view class="desc">{{ dev.toiletInoutFrequency ? dev.toiletInoutFrequency + '次' : '-' }}</view>
										</view>
									</view>
									<view class="r-flex" style="flex: 1;">
										<text class="icon iconfont icon-dangtianshichang" style="width: 100rpx; font-size: 66rpx; color: #01B09A;"></text>
										<view class="r-flex-1">
											<view class="title">最大停留时长</view>
											<view class="desc">{{ dev.toiletMaxStayDuration ? dev.toiletMaxStayDuration + '分' : '-' }}</view>
										</view>
									</view>
								</view>
								<view class="r-flex bottom-info">
									<view class="r-flex" style="flex: 1; justify-content: space-evenly;">
										<text class="icon iconfont icon-xinshuaijiance" style="width: 100rpx; font-size: 70rpx; color: #01B09A;"></text>
										<view class="r-flex-1">
											<view class="title">心率</view>
											<view class="desc">{{ dev.heartRate ? dev.heartRate + '次/分' : '-' }}</view>
										</view>
									</view>
									<view class="r-flex" style="flex: 1;">
										<text class="icon iconfont icon-huxijiance" style="width: 100rpx; font-size: 72rpx; color: #01B09A;"></text>
										<view class="r-flex-1">
											<view class="title">呼吸</view>
											<view class="desc">{{ dev.breathRate ? dev.breathRate + '次/分' : '-' }}</view>
										</view>
									</view>
								</view>
							</view>
						</view>
						<view v-if="dev.deviceType=='1' || dev.deviceType=='2'" class="item-bottom vc-flex jc-sa">
							<view class="chart3d vc-flex"
								@click="$navTo('pages/webview/index?id=' + dev.id)">
								<text class="icon iconfont icon-Dxuanzhuan" style="width: 80rpx; font-size: 70rpx; color: #01B09A;"></text>
								<text class="text">实时查看</text>
							</view>
							<!--#ifndef MP-WEIXIN -->
							<view class="chart3d vc-flex" v-if="dev.voipNumber && dev.status === '1'" @click="handleVoipCall(dev.voipNumber)">
								<text class="icon iconfont icon-dianhua" style="width: 90rpx; font-size: 90rpx; color: #01B09A;"></text>
								<text class="text">呼叫设备</text>
							</view>
							<!--#endif -->
							<!--#ifdef MP-WEIXIN -->
							<view class="chart3d vc-flex" v-if="dev.voipNumber && dev.status === '1'" @click="$navTo(`pagesDevice/call/webview-call?token=${token}&voipNumber=${dev.voipNumber}`)">
								<text class="icon iconfont icon-dianhua" style="width: 90rpx; font-size: 90rpx; color: #01B09A;"></text>
								<text class="text">呼叫设备</text>
							</view>
							<!--#endif -->
						</view>
					</view>
					<!-- 无忧
					<view v-if="worryFree && worryFree.id" class="item-block device-info bottom-info">
						<view class="r-flex" style="justify-content:space-between;text-align: center;">
							<view class="c-flex" @click="gotoBuy">
								<text class="icon iconfont icon-anquan11 icon-big-btn"></text>
								<text style="flex:1;">{{worryFree.serverName}}</text>
							</view>
							<view class="c-flex" @click="$navTo(`pagesFamily/member-server/my-server?serverId=${worryFree.id}`)">
								<text class="icon iconfont icon-denglu-yonghu icon-big-btn"></text>
								<text style="flex:1;">我的服务</text>
							</view>
							<view class="c-flex" @click="$navTo(`pagesFamily/member-server/server-record`)">
								<text class="icon iconfont icon-shishidongtai icon-big-btn"></text>
								<text style="flex:1;">服务记录</text>
							</view>
						</view>
					</view>-->
					<view v-if="currFamily.selfCreate === true && !devs.length" class="add-device"
						style="position: initial; width: initial; margin: initial; margin-top: 30rpx;">
						<view class="add-device-btn" style="font-size: 24rpx;" @click="$navTo('pagesDevice/add/index')">
							扫一扫/添加设备
						</view>
						<view class="other-device-join" @click="$navTo(`pagesFamily/device/choice?id=${currFamily.id}`)">
							设备转移<text class="icon iconfont icon-icon-xuanzejinru" style="font-size: 22rpx; margin-left: 10rpx;"></text>
						</view>
					</view> 
				</view>
			</view>
		<view v-if="danDisplayFlag" style="position: fixed; top: 0rpx; right: 0rpx; left: 0rpx; bottom: 0rpx; background: rgba(0, 0, 0, 0.3); z-index: 0;" @click="handleCloseDan"></view>
		
		</scroll-view>

		<rich-text-modal
			:show="serverAgreement.agreeStep == 1"
			:content="serverAgreement.simpleAgreement"
			@confirm="agreeAgreement(true)"/>
		<member-agreement-popup
			:show="serverAgreement.agreeStep == 2"
			:content="serverAgreement.agreement"
			@confirm="agreeAgreement(true)"
			@cancel="agreeAgreement(false)"/>
	</view>
</template>

<script>
	import DeanPopover from  '@/components/dean-popover/dean-popover.vue'
	import {longtapCopy} from '../../utils/util'
	export default {
		components: {
			DeanPopover
		},
		data() {
			return {
				staticUrl: this.$u.http.config.staticBaseUrl,
				scrollTop: 0,
				headerBg: {
					'background-color': 'transparent',
					'box-shadow': 'none'
				},
				familys: [],
				familyId: undefined,
				devs: [],
				todayEvents: [],
				todayStatInfo: {},
				currFamily: {},
				devRealDataTimer: undefined,
				danShow: false,
				devLoading: true,
				isFollowedMp:false,

				serverAgreement: {
					agreeStep: 0,
					serverIds:[],
					simpleAgreement: null,
					agreement: null,
				},

				deviceMsgUnreadCount: 0,
				token:null,
				memberId:null,

				// APP环境相关参数
				platform: null, // iOS/Android
				appType: null,  // app
				isInAppWebView: false, // 是否在APP的WebView中

				worryFree: null,
				commandList:[{
						command: 'addDevice',
						name: '扫一扫/添加设备'
					}, {
						command: 'createFamily',
						name: '创建家庭'
					},
					{
						command: 'familyManage',
						name: '家庭管理'
					}  ],
				// #ifdef (H5 || H5-HEJIA)
				commandList:[{
						command: 'createFamily',
						name: '创建家庭'
					},
					{
						command: 'familyManage',
						name: '家庭管理'
					}  ],
				// #endif 
			}
		},
		computed: {
			familyIsNull () {
				return !this.familys || !this.familys.length
			},
			danDisplayFlag () {
				return this.danShow
			}
		},
		onPageScroll(e) {
			if (e.scrollTop >= 0 && e.scrollTop <= 8) {
				this.headerBg['background-color'] = 'rgba(255, 255, 255, 0)';
				this.headerBg['box-shadow'] = '0rpx 10rpx 10rpx rgba(236, 236, 236, 0)';
			} else if (e.scrollTop > 8 && e.scrollTop <= 16) {
				this.headerBg['background-color'] = 'rgba(255, 255, 255, 0.1)';
				this.headerBg['box-shadow'] = '0rpx 10rpx 10rpx rgba(236, 236, 236, 0.1)';
			} else if (e.scrollTop > 16 && e.scrollTop <= 24) {
				this.headerBg['background-color'] = 'rgba(255, 255, 255, 0.2)';
				this.headerBg['box-shadow'] = '0rpx 10rpx 10rpx rgba(236, 236, 236, 0.2)';
			} else if (e.scrollTop > 24 && e.scrollTop <= 32) {
				this.headerBg['background-color'] = 'rgba(255, 255, 255, 0.3)';
				this.headerBg['box-shadow'] = '0rpx 10rpx 10rpx rgba(236, 236, 236, 0.3)';
			} else if (e.scrollTop > 32 && e.scrollTop <= 40) {
				this.headerBg['background-color'] = 'rgba(255, 255, 255, 0.4)';
				this.headerBg['box-shadow'] = '0rpx 10rpx 10rpx rgba(236, 236, 236, 0.4)';
			} else if (e.scrollTop > 40 && e.scrollTop <= 48) {
				this.headerBg['background-color'] = 'rgba(255, 255, 255, 0.5)';
				this.headerBg['box-shadow'] = '0rpx 10rpx 10rpx rgba(236, 236, 236, 0.5)';
			} else if (e.scrollTop > 48 && e.scrollTop <= 56) {
				this.headerBg['background-color'] = 'rgba(255, 255, 255, 0.6)';
				this.headerBg['box-shadow'] = '0rpx 10rpx 10rpx rgba(236, 236, 236, 0.6)';
			} else if (e.scrollTop > 56 && e.scrollTop <= 64) {
				this.headerBg['background-color'] = 'rgba(255, 255, 255, 0.7)';
				this.headerBg['box-shadow'] = '0rpx 10rpx 10rpx rgba(236, 236, 236, 0.7)';
			} else if (e.scrollTop > 64 && e.scrollTop <= 72) {
				this.headerBg['background-color'] = 'rgba(255, 255, 255, 0.8)';
				this.headerBg['box-shadow'] = '0rpx 10rpx 10rpx rgba(236, 236, 236, 0.8)';
			} else if (e.scrollTop > 72 && e.scrollTop <= 80) {
				this.headerBg['background-color'] = 'rgba(255, 255, 255, 0.9)';
				this.headerBg['box-shadow'] = '0rpx 10rpx 10rpx rgba(236, 236, 236, 0.9)';
			} else if (e.scrollTop > 80) {
				this.headerBg['background-color'] = 'rgba(255, 255, 255, 1)';
				this.headerBg['box-shadow'] = '0rpx 10rpx 10rpx rgba(236, 236, 236, 1)';
			}
		},
    onLoad(option) {
            // this.login();
			//#ifdef MP-WEIXIN
			wx.hideHomeButton()
			//#endif

			//#ifdef H5
			console.log("首页 option:",option)

			// 获取APP环境参数
			const {platform, appType} = option;
			if(platform && appType) {
				this.platform = platform;
				this.appType = appType;
				this.isInAppWebView = appType === 'app';
				console.log(`检测到APP环境: platform=${platform}, appType=${appType}`);
			}

			console.log("page/index/index invoke this.vali()")
			this.vali()
			//#endif
			// uni.startPullDownRefresh();
			this.familyId = option.familyId;
    },
		async onShow() {
			// uni.removeStorageSync('curr_family')
			this.fetchFamily(this.familyId);
			this.deviceMsgUnreadCount = await this.$u.api.countDevMsgMyUnread() || 0;
			// try {
			// 	this.worryFree = await this.$u.api.getServerByType();
			// } catch {
			// 	this.worryFree = null;
			// }
			const {followedMp,id} = uni.getStorageSync('member')
			console.log("followedMp："+followedMp);
			this.isFollowedMp = followedMp;
			// this.memberId = id;
			this.token = uni.getStorageSync('token');
		},
		onHide() {
			console.log("首页hide")
			this.handleCloseDan();
			if (this.devRealDataTimer) {
				clearTimeout(this.devRealDataTimer);  
				this.devRealDataTimer = null;  
			}  
		},
		onUnload() {
			console.log("首页hide")
			if (this.devRealDataTimer) {
				clearTimeout(this.devRealDataTimer);  
				this.devRealDataTimer = null;  
			}  
		},
		methods: {
			longtapCopy,
			onPullDownRefresh() {
				console.log("触发下拉刷新")
				setTimeout(() => {
					uni.removeStorageSync('curr_family')
					this.fetchFamily();
					// this.fetchFamilyDevList(this.currFamily.id, true)
					// this.fetchTodayEventList(this.currFamily.id);
					// this.fetchTodayStatInfo(this.currFamily.id);
					//关闭下拉刷新
					uni.stopPullDownRefresh()
				},1000)
			},
			vali() {
				const devCode = uni.getStorageSync('devCode');
				console.log("devCode:"+devCode);
				if(!devCode){
					console.log("H5不是从cmcc进来或在cmcc页面没获取到设备MDID，无法校验当前设备");
					return;
				}
				this.$u.api.execCheckDevCode({ devCode: devCode }).then(res => {
					if (res.hasModel == false) {
						uni.showToast({
							duration: 2000,
							title: '该设备产品没有产品型号',
							icon: 'none'
						})
						return;
					}
					if (res.isExist == false) {
						uni.showToast({
							duration: 2000,
							title: '设备不存在',
							icon: 'none'
						})
						return;
					}
					if (res.isSelfBind === false && res.isOtherBind === false) {
						console.log(devCode+"该设备未被他人绑定，开始绑定")
						this.bindDev();
					}
					if(res.isOtherBind){
						uni.showToast({
							duration: 2000,
							title: devCode+'设备已被他人绑定，请先在与安宝中解绑',
							icon: 'none'
						})
						console.log(devCode+"该设备已被他人绑定，请先在与安宝中解绑")
						return;
					}
				}).catch(err => {
					uni.showToast({
						duration: 2000,
						title: '获取设备信息出错',
						icon: 'none'
					})
				}).finally(() => {
					setTimeout(() => {
						uni.hideLoading();
					}, 1000)
				})
			},
			gotoBuy(){
				if(this.worryFree && this.worryFree.validStatus=='0'){
					this.$navTo(`pagesFamily/device/spec/buy?source=wyfw&serverId=${this.worryFree.id}`)
				}else{
					uni.showToast({
						duration: 2000,
						title: '服务已禁用，请稍后再试',
						icon: 'none'
					})
				}
			},
			bindDev() {
				let _member = uni.getStorageSync('member');
				console.log("_member:",_member);
				let {phone}  = _member;
				if(!phone){
					uni.showToast({
						duration: 2000,
						title: '绑定失败',
						icon: 'none'
					})
					console.log("获取当登录人的电话号码为空")
					return;
				}
				let curr_family = uni.getStorageSync('curr_family')
				console.log("curr_family:",curr_family);
				const {id} = curr_family;
				if(!id){
					uni.showToast({
						duration: 2000,
						title: '绑定失败',
						icon: 'none'
					})
					console.log("获取当前家庭的ID为空")
					return;
				}
				uni.showModal({
					title: '请确认是否绑定该手机号',
					content: _member.phone,
					confirmText: '绑定',
					cancelText: '关闭',
					success: async (res) => {
						console.log(res)
						if (res.confirm) {
							this.$u.api.execBindUser({ devCode: uni.getStorageSync('devCode'), phone:phone,familyId:id }).then(res => {
								uni.showToast({
									duration: 2000,
									title: '绑定完成',
									icon: 'none'
								})
							}).catch(err => {
								uni.showToast({
									duration: 2000,
									title: '绑定失败',
									icon: 'none'
								})
							}).finally(() => {
								setTimeout(() => {
									uni.hideLoading();
								}, 1000)
							})
						}
					}
				});
			},
			handleScroll(e) {
				let _scrollTop = e.detail.scrollTop;
				// #ifndef MP-WEIXIN
				if (_scrollTop >= 0 && _scrollTop <= 8) {
					this.headerBg['background-color'] = 'rgba(255, 255, 255, 0)';
					this.headerBg['box-shadow'] = '0rpx 10rpx 10rpx rgba(236, 236, 236, 0)';
				} else if (_scrollTop > 8 && _scrollTop <= 16) {
					this.headerBg['background-color'] = 'rgba(255, 255, 255, 0.1)';
					this.headerBg['box-shadow'] = '0rpx 10rpx 10rpx rgba(236, 236, 236, 0.1)';
				} else if (_scrollTop > 16 && _scrollTop <= 24) {
					this.headerBg['background-color'] = 'rgba(255, 255, 255, 0.2)';
					this.headerBg['box-shadow'] = '0rpx 10rpx 10rpx rgba(236, 236, 236, 0.2)';
				} else if (_scrollTop > 24 && _scrollTop <= 32) {
					this.headerBg['background-color'] = 'rgba(255, 255, 255, 0.3)';
					this.headerBg['box-shadow'] = '0rpx 10rpx 10rpx rgba(236, 236, 236, 0.3)';
				} else if (_scrollTop > 32 && _scrollTop <= 40) {
					this.headerBg['background-color'] = 'rgba(255, 255, 255, 0.4)';
					this.headerBg['box-shadow'] = '0rpx 10rpx 10rpx rgba(236, 236, 236, 0.4)';
				} else if (_scrollTop > 40 && _scrollTop <= 48) {
					this.headerBg['background-color'] = 'rgba(255, 255, 255, 0.5)';
					this.headerBg['box-shadow'] = '0rpx 10rpx 10rpx rgba(236, 236, 236, 0.5)';
				} else if (_scrollTop > 48 && _scrollTop <= 56) {
					this.headerBg['background-color'] = 'rgba(255, 255, 255, 0.6)';
					this.headerBg['box-shadow'] = '0rpx 10rpx 10rpx rgba(236, 236, 236, 0.6)';
				} else if (_scrollTop > 56 && _scrollTop <= 64) {
					this.headerBg['background-color'] = 'rgba(255, 255, 255, 0.7)';
					this.headerBg['box-shadow'] = '0rpx 10rpx 10rpx rgba(236, 236, 236, 0.7)';
				} else if (_scrollTop > 64 && _scrollTop <= 72) {
					this.headerBg['background-color'] = 'rgba(255, 255, 255, 0.8)';
					this.headerBg['box-shadow'] = '0rpx 10rpx 10rpx rgba(236, 236, 236, 0.8)';
				} else if (_scrollTop > 72 && _scrollTop <= 80) {
					this.headerBg['background-color'] = 'rgba(255, 255, 255, 0.9)';
					this.headerBg['box-shadow'] = '0rpx 10rpx 10rpx rgba(236, 236, 236, 0.9)';
				} else if (_scrollTop > 80) {
					this.headerBg['background-color'] = 'rgba(255, 255, 255, 1)';
					this.headerBg['box-shadow'] = '0rpx 10rpx 10rpx rgba(236, 236, 236, 1)';
				}
				//#endif
			},
			handleClosePopover(refName) {
				if (this.$refs[refName]) {
					this.$refs[refName].handleClose();
				}
			},
			handleFamilyClick(idx) {
				if (this.familys && this.familys.length && idx < this.familys.length) {
					this.currFamily = this.familys[idx]
					uni.setStorageSync('curr_family', this.currFamily);
					this.fetchFamily();
				}
			},
			handleMenuClick(idx,item) {
				this.danShow = false;
				if (item.command === 'addDevice') {
					if (this.currFamily.selfCreate === true) {
						this.$navTo('pagesDevice/add/index');
					} else {
						uni.showToast({ duration: 2000, title: '共享家庭无法添加设备', icon: 'none' })
					}
				} else if (item.command === 'createFamily') {
					this.$navTo(`pagesFamily/address/index`)
				} else if (item.command === 'familyManage') {
					this.$navTo('pagesFamily/familys/index')
				} 
			},
			shareFamily(){
				this.$navTo(`pagesFamily/member/index?id=${ this.currFamily.id }&name=${ this.currFamily.name }`)
			},
			handleCloseDan() {
				this.danShow = false
				if (this.$refs['familyPopover']) {
					this.$refs['familyPopover'].handleClose();
				}
				if (this.$refs['menuPopover']) {
					this.$refs['menuPopover'].handleClose();
				}
			},
			handleShowChange(showFlag) {
				this.danShow = showFlag
			},
			navDevStat(dev) {
				if(dev.deviceType=='1' || dev.deviceType=='2' || dev.deviceType=='5'){
					if (dev.devScene === '1' || dev.devScene === '4') {
						this.$navTo(`pagesHome/parlour/index?id=${dev.id}`);
					} else if (dev.devScene === '2') {
						this.$navTo(`pagesHome/toilet/index?id=${dev.id}`);
					} else if (dev.devScene === '3') {
						this.$navTo(`pagesHome/bedroom/index?id=${dev.id}`);
					}
				}else if(dev.deviceType=='3'){
					console.log("随身宝",dev)
					this.$navTo(`pagesFamily/device/notActive-v2?devId=${dev.id}&devCode=${dev.devCode}`);
				}else if(dev.deviceType=='4'){
					console.log("急救宝",dev)
					this.$navTo(`pagesFamily/device/notActive-v2?devId=${dev.id}&devCode=${dev.devCode}`);
				}
			},
			fetchFamily(replaceFamilyId) {
				
				if (this.devRealDataTimer) {
					clearTimeout(this.devRealDataTimer);  
					this.devRealDataTimer = null;  
				}
				this.$u.api.fetchFamilyList({ }).then(res => {
					if (!res || !res.length) {
						this.toFamilyForm();
					} else {
						this.familys = res
						let _currFamily;
						if (replaceFamilyId) {
							let _filters = (res || []).filter(f => f.id === replaceFamilyId);
							if (_filters.length) {
								_currFamily = _filters[0]
								this.familyId = undefined;
							}
						}
						if (!_currFamily) {
							_currFamily = uni.getStorageSync('curr_family')
							if (_currFamily) {
								let _filters = (res || []).filter(f => f.id === _currFamily.id);
								if (_filters.length) {
									_currFamily = _filters[0]
								}
							}
						}
						if (!_currFamily) {
							_currFamily = res[0]
						}

						if (!_currFamily.addr) {
							//aladding 没有家庭地址跳到设置页面
							this.toFamilyForm(_currFamily.id);
							return;
						}
						this.currFamily = _currFamily;
						uni.setStorageSync('curr_family', _currFamily);

						this.$u.api.listNotAgreeAgreement()
							.then(res => {
								this.serverAgreement = res;
								if (res.serverIds.length) {
									this.serverAgreement.agreeStep = 1;
								}
							}).catch(err => {
								console.error(err);
							});

						this.fetchFamilyDevList(this.currFamily.id);
						this.fetchTodayEventList(this.currFamily.id);
						this.fetchTodayStatInfo(this.currFamily.id);

					}
				})
			},
			fetchFamilyDevList(id, isTimer) {
				this.devLoading = true;
				this.$u.api.fetchFamilyDevList({ familyId: id }).then(res => {
					//TODO 此处请求太频繁，改到服务端查询设备时查询出来
					/* res.forEach(item => {
						this.$u.api.isGuardDeviceWorryfree({ devId: item.id })
							.then(res => {
								item.isGuard = res;
							});
					}); */
					this.devs = res;
					if (res && res.length && !isTimer) {
						if (this.devRealDataTimer) {
							clearTimeout(this.devRealDataTimer);  
							this.devRealDataTimer = null;
						}
						this.devRealDataTimer = setInterval(() => {
							console.log("首页定时器")
							// 获取当前打开过的页面路由数组
							let routes = getCurrentPages();
							//获取当前页面路由
							let curRoute = routes[routes.length - 1].route 
							// 在微信小程序或是app中，通过curPage.options
							// #ifdef MP-WEIXIN
							curRoute = curRoute.options||curRoute
							// #endif
							console.log("curRoute:",curRoute);
							if(curRoute&&curRoute.indexOf("pages/index/index")<0){
								console.log("当前不是首页，停止定时器");
								clearTimeout(this.devRealDataTimer);  
								this.devRealDataTimer = null;
								return;
							}
							this.fetchFamilyDevList(id, true)
							this.fetchTodayEventList(this.currFamily.id);
							this.fetchTodayStatInfo(this.currFamily.id);
						}, 60000)
					}
					setTimeout(() => {
						this.devLoading = false;
					}, 150)
				}).catch(err => {
					setTimeout(() => {
						this.devLoading = false;
					}, 150)
					uni.showToast({ duration: 2000, title: err.message || '发生未知错误', icon: 'none' })
				})
			},
			fetchTodayEventList(id) {
				this.$u.api.fetchTodayEventList({ familyId: id }).then(res => {
					this.todayEvents = (res || []).slice(0, 2)
				}).catch(err => {
					uni.showToast({ duration: 2000, title: err.message || '发生未知错误', icon: 'none' })
				})
			},
			fetchTodayStatInfo(id) {
				this.$u.api.fetchTodayStatInfo({ familyId: id }).then(res => {
					this.todayStatInfo = res || {}
				}).catch(err => {
					uni.showToast({ duration: 2000, title: err.message || '发生未知错误', icon: 'none' })
				})
			},
			toFamilyForm(familyId) {
				const that = this;
				uni.showModal({
					showCancel: false,
					content: "家庭地址为救援地址，请设置正确的家庭地址，否则将影响告警救援",
					confirmColor: "#01B09A",
					success() {
						if (familyId) {
							that.$navTo(`pagesFamily/address/index?id=${familyId}&`)
						} else {
							that.$navTo(`pagesFamily/address/index?first=true`)
						}
					}
				})
			},
			async agreeAgreement(agree) {
				if (agree === false) {
					this.serverAgreement.agreeStep = 0;
					return;
				}
				this.serverAgreement.agreeStep++;
				if (this.serverAgreement.agreeStep == 3) {
					this.serverAgreement.agreeStep = 0;
					try {
						await this.$u.api.agreeByMember();
					} catch (err) {
						console.error(err);
					}
				}
			},

			/**
			 * 处理VoIP呼叫
			 * @param {string} voipNumber - VoIP号码
			 */
			async handleVoipCall(voipNumber) {
				console.log(`准备呼叫VoIP号码: ${voipNumber}`);

				// #ifdef H5
				if (this.isInAppWebView) {
					// 在APP的WebView中，调用APP的原生VoIP功能
					try {
						await this.callAppVoip(voipNumber);
					} catch (error) {
						console.error('APP VoIP调用失败，降级到Web端:', error);
						// 如果APP调用失败，降级到Web端实现
						this.callWebVoip(voipNumber);
					}
				} else {
					// 在普通H5环境中，使用Web端的WebRTC实现
					this.callWebVoip(voipNumber);
				}
				// #endif

				// #ifndef H5
				// 非H5环境（如小程序），使用默认的Web端实现
				this.callWebVoip(voipNumber);
				// #endif
			},

			/**
			 * 调用APP原生VoIP功能
			 * @param {string} voipNumber - VoIP号码
			 * @returns {Promise<boolean>}
			 */
			async callAppVoip(voipNumber) {
				console.log(`调用APP原生VoIP功能，号码: ${voipNumber}`);

				try {
					// 使用新的VoIP服务
					const VoIPService = (await import('@/utils/services/VoIPService.js')).default;

					const result = await VoIPService.makeCall({
						number: voipNumber,
						token: this.token,
						displayName: '客服呼叫'
					});

					if (result && result.success !== false) {
						uni.showToast({
							title: '正在呼叫...',
							icon: 'none',
							duration: 1500
						});

						// 监听呼叫状态变化
						this.setupVoIPListeners();
					}

					return true;
				} catch (error) {
					console.error('调用APP VoIP功能失败:', error);

					// 显示友好的错误提示
					uni.showModal({
						title: '呼叫提示',
						content: '无法调用APP呼叫功能，将使用网页版呼叫',
						showCancel: false,
						confirmText: '确定',
						success: () => {
							// 降级到Web端实现
							this.callWebVoip(voipNumber);
						}
					});

					throw error;
				}
			},

			/**
			 * 设置VoIP事件监听器
			 */
			async setupVoIPListeners() {
				try {
					const VoIPService = (await import('@/utils/services/VoIPService.js')).default;

					// 监听呼叫状态变化
					VoIPService.on('stateChanged', (data) => {
						console.log('VoIP状态变化:', data);
						// 可以在这里更新UI状态
					});

					// 监听呼叫连接
					VoIPService.on('callConnected', (data) => {
						console.log('VoIP呼叫已连接:', data);
						uni.showToast({
							title: '通话已连接',
							icon: 'success'
						});
					});

					// 监听呼叫结束
					VoIPService.on('callEnded', (data) => {
						console.log('VoIP呼叫已结束:', data);
						uni.showToast({
							title: '通话已结束',
							icon: 'none'
						});
					});

					// 监听呼叫失败
					VoIPService.on('callFailed', (data) => {
						console.log('VoIP呼叫失败:', data);
						uni.showToast({
							title: '呼叫失败',
							icon: 'error'
						});
					});
				} catch (error) {
					console.error('设置VoIP监听器失败:', error);
				}
			},

			/**
			 * 使用Web端WebRTC进行VoIP呼叫
			 * @param {string} voipNumber - VoIP号码
			 */
			callWebVoip(voipNumber) {
				console.log(`使用Web端WebRTC进行VoIP呼叫，号码: ${voipNumber}`);

				// 跳转到原有的Web端VoIP呼叫页面
				this.$navTo(`pagesDevice/call/call?voipNumber=${voipNumber}`);
			}
		}
	}
</script>

<style lang="scss">
page {
	background-color: #f7f7f7;
	// #ifndef MP-WEIXIN
	height: calc(100vh - 90rpx);
	// #endif
}
</style>

<style lang="scss" scoped>
@import url("/static/css/iconfont.css");
::v-deep .u-navbar-fixed {
	padding-bottom: 0rpx;
}
.chart3d {
	.text {
		font-size: 36rpx;
	}
}
.item-bottom{
	padding-top: 20rpx;
	border-top: 2rpx solid #ececec;
}
.index-page {
	height: 100vh;
	// #ifndef MP-WEIXIN
	height: 100%;
	overflow: hidden;
	// #endif
	.slot-wrap {
		display: flex;
		.titles-line {
			font-size: 28upx;
			color: #fff;
			height: 60upx;
		}
	}
	.share{
		padding-right: 40rpx;
		font-size: 36rpx;
		color: #000000;
		font-weight: bold;
	}
	.family {
		padding-left: 20rpx;
		&-name {
			font-size: 36rpx;
			color: #000000;
			font-weight: bold;
		}
	}
	.family-address {
		// padding-top: 20rpx;
		padding-left: 20rpx;
		padding-right: 20rpx;
		font-size: 23rpx;
		color: #010101;
		// margin-bottom: 16rpx;
		// margin-top: 10rpx;
	}
	.add-device {
		vertical-align: text-bottom;
		position: absolute;
		top: 50%;
		left: 50%;
		/* height: 30%; */
		width: 50%;
		margin: -0% 0 0 -25%;
		text-align: center;
		&-btn {
			display: inline-block;
			margin: 0 auto;
			font-size: 32rpx;
			border-radius: 70rpx;
			padding: 14rpx 44rpx;
			color: #000;
			border: 2rpx solid #01B09A;
		}
		.other-device-join {
			font-size: 22rpx;
			margin-top: 18rpx;
			color: #01B09A;
			.icon-dayukuangxuanze {
				border: 2rpx solid #01B09A;
				font-size: 16rpx;
				border-radius: 100%;
				padding: 2rpx;
				position: relative;
				top: -2rpx;
				left: 4rpx;
				margin-left: 6rpx;
			}
		}
	}
	.item-block {
		background: white;
		padding: 30rpx 20rpx;
		font-size: 28rpx;
		margin-top: 20rpx;
		border-radius: 20rpx;
	}
	.alarm {
		margin-top: 10rpx;
		.title {
			font-size: 28rpx;
		}
		.desc {
			font-size: 24rpx;
			margin-top: 10rpx;
			.tag {
				color: red;
			}
		}
	}
	.real-info {
		margin-top: 20rpx;
		.title {
			font-size: 30rpx;
			font-weight: bold;
		}
		.desc {
			font-size: 24rpx;
			margin-top: 10rpx;
			color: #888;
		}
	}
	.real-info-2 {
		margin-top: 20rpx;
		.children-flex {
			margin-top: 20rpx;
			flex: 1; 
			background-color: white;
			border-radius: 20rpx;
			padding: 30rpx 24rpx;
			padding-left: 20rpx;
		}
		.icon {
			position: relative;
			left: -6rpx;
		}
		.title {
			font-size: 28rpx;
			font-weight: bold;
			color: #000;
			margin-top: 14rpx;
			margin-left: 14rpx;
		}
		.desc {
			font-size: 24rpx;
			margin-top: 10rpx;
			margin-left: 14rpx;
			color: #888;
		}
	}
	.device-info {
		.top-info {
			position: relative;
			.left-img {
				width: 70rpx;
				height: 70rpx;
				display: inline-block;
				margin-right: 30rpx;
				vertical-align: middle;
			}
			.title {
				font-size: 28rpx;
				font-weight: bold;
				color: #000;
			}
			.desc {
				font-size: 26rpx;
				margin-top: 10rpx;
				color: #888;
			}
			.wifi{
				font-size: 28rpx; 
				color: #888; 
				vertical-align: middle; 
				margin-left: 10rpx; 
				position: relative; 
				top: -2rpx;
			}
			.tubiao{
				font-size: 28rpx; 
				color: #888; 
				vertical-align: middle; 
				margin-right: 10rpx; 
				position: relative; 
				top: -2rpx;
			}
		}
		.realtimeData{
			margin-top: 20px;
			border-top: 2rpx solid rgba(135, 135, 135, 0.15);
		}
		.bottom-info {
			margin-top: 20rpx;
			justify-content: space-around;
			padding-bottom: 20rpx;
			.title {
				font-size: 28rpx;
				font-weight: bold;
				color: #000;
			}
			.desc {
				font-size: 26rpx;
				margin-top: 10rpx;
				color: #888;
			}
			::v-deep .icon {
				text-align: center;
				margin-left: 50rpx;
			}
		}
	}
	.warning-arrow {
		width: 60rpx;
		text-align: center;
		.icon-arrow-right {
			vertical-align: middle;
			color: #6B6B6B;
			position: relative;
			top: 2rpx;
			font-size: 20rpx;
		}
	}

	.icon-big-btn {
		flex: 1;
		font-size: 76rpx;
		color: #01B09A;
	}
}
// ::v-deep .u-navbar {
// 	.u-navbar-inner {
// 		height: 120rpx !important;
// 	}
// }


.device-offline {
	.title, .desc {
		color: #d4d4d4 !important;;
	}
	.icon-shijian1, .icon-chakan, .icon-dangtianshichang, .icon-huodongshuju, .icon-xinshuaijiance, .icon-huxijiance, .icon-a-icon_menchumen {
		color: #d4d4d4 !important;
	}
}

.scroll-height {
	height: 100%;
}
// #ifdef H5
.scroll-view {
	height: 100%;
	// padding-bottom: 60rpx;
}
// #endif

.warn-tips {
	font-size: 13px;
	color: $u-type-warning;
}
</style>
