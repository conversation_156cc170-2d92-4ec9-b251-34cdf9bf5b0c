<template>
	<view class="main" :class="bgClass">
		<view class="center">
			<view class="circle circle1">
				<view class="circle circle2">
					<text class="icon iconfont connIcon" :class="iconClass"></text>
				</view>
				<view class="statusText">
					<text>{{connectionStatusText}}</text>
				</view>
			</view>
		</view>
		<view class="bottom">
			<view class="bottomRow">
				<view class="tabBar">
					<view>
						<text class="icon iconfont icon-fangjianweihu tabBarIcon" @click="toAddress"></text>
					</view>
					<view>
						<text class="tabBarText" @click="toAddress">房间配置</text>
					</view>
				</view>
				<!-- <view class="tabBar">
					<view>
						<text class="icon iconfont icon_xiaochengxu tabBarIcon"></text>
					</view>
					<view>
						<text class="tabBarText" @click="shareToWeChatMessage">我的家</text>
					</view>
				</view> -->
				<view class="tabBar">
					<view>
						<text class="icon iconfont icon-xiaochengxu tabBarIcon" @click="toYuanbao"></text>
					</view>
					<view>
						<text class="tabBarText" @click="toYuanbao">我的家</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	// #ifdef (H5 || H5-HEJIA)
	import '../../static/common/js/hejia-latest.min.js';
	const {
		Hejia
	} = window;
	// #endif
	export default {
		mounted(){  
		      //#ifdef H5  
		      document.querySelector('.uni-page-head-hd').style.display = 'none'  
		      //#endif  
		},
		onLoad(option) {
			console.log("进入cmcc:"+window.location.href)
			 // #ifdef (H5 || H5-HEJIA)
			this.optType = option.optType;
			uni.removeStorageSync('curr_family')
			uni.removeStorageSync('devCode')
			uni.removeStorageSync('devId')
			let _h5DeviceType = this.getQueryString('deviceType') || option.deviceType;
			let _h5DeviceId = this.getQueryString('deviceId') || option.deviceId;
			if (!_h5DeviceType && !_h5DeviceId && uni.getStorageSync('cmcc_deviceType') && uni.getStorageSync('cmcc_deviceId')) {
				var newurl = this.updateQueryStringParameter(window.location.href, 'deviceType', uni.getStorageSync('cmcc_deviceType'));
				newurl = this.updateQueryStringParameter(newurl, 'deviceId', uni.getStorageSync('cmcc_deviceId'));
				//向当前url添加参数，没有历史记录
				window.history.replaceState({
					path: newurl
				}, '', newurl);
			} else if (_h5DeviceType && _h5DeviceId) {
				uni.setStorageSync('cmcc_deviceType', _h5DeviceType);
				uni.setStorageSync('cmcc_deviceId', _h5DeviceId);
			} else {
				uni.removeStorageSync('cmcc_deviceType');
				uni.removeStorageSync('cmcc_deviceId');
			}
			console.log('_h5DeviceType, _h5DeviceId', _h5DeviceType, _h5DeviceId);
			console.log('href', window.location.href);
			// if(this.optType !=='unBind'){
				setTimeout(() => {
					this.initData();
				}, 100)
			// }
			// #endif 
		},
		onShow() {
			console.log("重新进入cmcc:"+window.location.href)
			const deviceId = this.getQueryString('deviceId')
			const optType = this.getQueryString('optType')
			console.log("deviceId："+deviceId);
			console.log("optType："+optType);
			this.getDeviceById(deviceId);
		},
		data() {
			return {
				connectionStatus: false,
				connectionStatusText: '设备离线',
				deviceInfo: {},
				phoneNum: '',
				apiKey:'',
				isInHejiaApp: false,
				isLogin:false,
				optType:'',
				disableHome:false,
			}
		},
		methods: {
			unBind(deviceId){
				console.log("进入管控页解绑设备")
				//https://open.home.10086.cn/jssdk/doc/andlink.html#.unBindDevice
				const params = {
					deviceId:deviceId,
				};
				// Hejia.ready(() => {
				// 	console.log("和家亲初始化")
					Hejia.unBindDevice(params,
					// Hejia.unBindDevice(
						(res) => {
							console.log("解绑和家亲设备成功:",res);
							this.$navTo(`pages/index/index`);
						},
						(e) => {
							console.log("解绑和家亲设备失败：",e);
						},)
				// });
			},
			getQueryString(name) {
				let _url =  window.location.search
				if (window.location.href.indexOf('/#/') != -1 && window.location.href.indexOf('?') != -1) {
					_url = window.location.href.substr(window.location.href.indexOf('?'));
				}
				var reg = new RegExp("(^|&)"+ name +"=([^&]*)(&|$)");
				var r = _url.substr(1).match(reg);
				if (r != null) return unescape(r[2]); return null;
			},
			updateQueryStringParameter(uri, key, value) {
				if(!value) {
					return uri;
				}
				var re = new RegExp("([?&])" + key + "=.*?(&|$)", "i");
				var separator = uri.indexOf('?') !== -1 ? "&" : "?";
				if (uri.match(re)) {
					return uri.replace(re, '$1' + key + "=" + value + '$2');
				}
				else {
					return uri + separator + key + "=" + value;
				}
			},
			initData() {
				// uni.setStorageSync('devCode', null);
				uni.showLoading({
					title: '加载中...',
					mask: true
				})
				// this.test();
				Hejia.ready(() => {
					console.log('Hejia ready');

					// const type = typeof(Hejia.getPhoneNumber);
					// if (type !== 'function') {
					// 	this.isInHejiaApp = false;
					// 	uni.showToast({
					// 		duration: 2000,
					// 		title: '该页面需在和家亲APP内访问' + type,
					// 		icon: 'none'
					// 	});
					// 	return;
					// }
					let that = this;
					Hejia.getPhoneNumber(
						(mobile) => {
							that.phoneNum = mobile;
							console.log("获取手机号："+that.phoneNum);
							uni.setStorageSync('phoneNum', mobile);
						},
						() => {
							uni.showToast({
								duration: 2000,
								title: '获取手机号错误',
								icon: 'none'
							});
						},
					);
					Hejia.getApiKey(
						(apiKey) => {
							console.log("apiKey:"+apiKey);
							that.apiKey = apiKey;
						},
						(e) => {
							console.log("获取apiKey失败：",e);
							// uni.showToast({
							// 	duration: 2000,
							// 	title: '获取apiKey失败',
							// 	icon: 'none'
							// });
						},
					);Hejia.getDeviceId(
						(apiKey) => {
							console.log("getDeviceId:"+getDeviceId);
						},
						(e) => {
							console.log("获取getDeviceId失败：",e);
							// uni.showToast({
							// 	duration: 2000,
							// 	title: '获取apiKey失败',
							// 	icon: 'none'
							// });
						},
					);
					Hejia.getToken(
						(token) => {
							console.log("token:"+token);
						},
						(e) => {
							console.log("获取token失败：",e);
							// uni.showToast({
							// 	duration: 2000,
							// 	title: '获取token失败',
							// 	icon: 'none'
							// });
						},
					);
					uni.hideLoading();
					Hejia.getDeviceInfo(
						(res) => {
							const {
								device
							} = res;
							this.deviceInfo = device;
							const {
								id,
								connected
							} = device;
							
							// uni.showToast({
							// 	duration: 3000,
							// 	title: '获取设备信息：' + JSON.stringify(device),
							// 	icon: 'none'
							// })
							// uni.showToast({
							// 	duration: 3000,
							// 	title: '获取设备id：' + id,
							// 	icon: 'none'
							// })

							this.connectionStatus = connected;
							this.connectionStatusText = connected ? '设备在线' : '设备离线';
							this.getDeviceById(id);
							if(this.optType ==='unBind'){
								this.unBind(id)
							}	
						},
						(msg,res) => {
							const {
								resultCodeMessage,
								resultCodeDesc
							} = res;
							console.log(res)
							uni.showToast({
								duration: 3000,
								title: '获取设备信息错误：' + JSON.stringify(res)+"=====msg:"+msg,
								icon: 'none'
							})
						},
					);
					console.log("this.phoneNum:"+this.phoneNum)
					this.$u.api.login({
						loginType: 'cmcc',
						phone: uni.getStorageSync('phoneNum'),
						cmccApiKey: "cmccApiKey",
					}).then(res => {
						// 用户信息写入缓存
						this.isLogin = true;
						// uni.showToast({title: '登录成功'})
						uni.removeStorageSync('curr_family')
						uni.setStorageSync( 'member', res.member );
						uni.setStorageSync( 'token', res.token );
						this.$u.api.fetchFamilyList({ }).then(res => {
							if (!res || !res.length) {
								console.log("未获取到家庭信息，禁用我的家按钮");
								this.disableHome = true;
							}
						})
					})
				});
			},
			test(){
				// 没设备时调试用
				console.log('未获取devcode');
				uni.setStorageSync('devCode', 'G1');
				uni.setStorageSync('phoneNum', '13044259928');
				this.phoneNum = '13044229988';
				this.apiKey = 'guanjian';
				this.$u.api.getDevCodeByMac({
					mac:'84CCA81A33F8'
				})
				.then(res => {
					// 此处如果是mdid，则需要传到房间配置中的地址页面
					uni.showToast({
						duration: 2000,
						title: '获取设备MDID:'+JSON.stringify(res),
						icon: 'none'
					})
					if (res) {
						uni.setStorageSync('devCode', res);
					}
				}).catch(err => {
					uni.showToast({
						duration: 2000,
						title: '获取设备MDID失败:'+JSON.stringify(res),
						icon: 'none'
					})
				}).finally(() => {
					setTimeout(() => {
						uni.hideLoading();
					}, 1000);
				})
				this.$u.api.login({
					loginType: 'cmcc',
					phone: this.phoneNum,
					cmccApiKey: this.apiKey,
				}).then(res => {
					// 用户信息写入缓存
					this.isLogin = true;
					// uni.showToast({title: '登录成功'})
					uni.removeStorageSync('curr_family')
					uni.setStorageSync( 'member', res.member );
					uni.setStorageSync( 'token', res.token );
					// 然后跳回原页面
					// uni.reLaunch({
					// 	url: '/pages/cmcc/index'
					// })
				})
				uni.hideLoading();
			},
			getDeviceById(id){
				if (id) {
					const mac = id.substring(id.lastIndexOf('-') + 1);
					this.$u.api.getDevCodeByMac({
							mac
						})
						.then(res => {
							if (res) {
								console.log("从服务器获取到mdid:"+res)
								uni.setStorageSync('devCode', res);
								// 保存所有和家亲设备的MDID,key为cmccIds_和家亲设备ID,用户设备详情页删除时解绑
								uni.setStorageSync('cmccMdids_'+res, id);
							}else{
								console.log(res)
								uni.showToast({
									duration: 2000,
									title: '获取设备MDID为空，请检查mac地址',
									icon: 'none'
								})
							}
						}).catch(err => {
							uni.showToast({
								duration: 2000,
								title: '获取设备MDID失败' + JSON.stringify(err),
								icon: 'none'
							})
						}).finally(() => {
							setTimeout(() => {
								uni.hideLoading();
							}, 1000);
						})
				} else {
					uni.showToast({
						duration: 2000,
						title: '获取设备ID失败',
						icon: 'none'
					})
					uni.hideLoading();
				}
			},
			toAddress() {
				if(!this.isLogin){
					uni.showToast({
						duration: 2000,
						title: '用户没登录',
						icon: 'none'
					})
					return;
				}
				console.log("进入安装地址页面")
				const mdid = uni.getStorageSync('devCode')
				if(!mdid){
					uni.showToast({
						duration: 2000,
						title: '无法获取设备MDID',
						icon: 'none'
					})
				}else{
					if(!this.disableHome){
						uni.redirectTo({
							url: `/pagesDevice/config/step`
						})
					}else{
						uni.navigateTo({
							url: `/pages/address/index`
						})
					}
				}
			},
			
			toYuanbao() {
				if(!this.isLogin){
					console.log("用户未登录")
					uni.showToast({
						duration: 2000,
						title: '获取信息出错,请重新进入管控页',
						icon: 'none'
					})
					return;
				}
				console.log("进入与安宝页面")
				const mdid = uni.getStorageSync('devCode')
				if (!mdid) {
					uni.showToast({
						duration: 2000,
						title: '无法获取设备MDID',
						icon: 'none'
					})
				} else {
					if(this.disableHome){
						uni.showToast({
							duration: 2000,
							title: '请先进行房间配置',
							icon: 'none'
						})
						return ;
					}
					uni.switchTab({
						url: `/pages/index/index?source=cmcc`
					})
				}
			},
			toMini() {
				console.log("进入小程序页面")
				uni.showToast({
					duration: 2000,
					title: '敬请关注',
					icon: 'none'
				});
			},
			shareToWeChatMessage() {
				const params = {
					"platformType":1,
					"imageUrl": "https://mobile.umeng.com/images/pic/home/<USER>/img-1.png",
					"shareType": 1,
					"imageUrl": "https://www.ventropic.com/qrcode.png"
				};
				Hejia.shareToWeChatMessage(params,
					(res) => {
						uni.showToast({
							duration: 2000,
							title: '分享成功',
							icon: 'none'
						})
					},
					(res) => {
						uni.showToast({
							duration: 2000,
							title: res,
							icon: 'none'
						})
					},
				);
			},
		},
		computed: {
			bgClass() {
				return {
					online: this.connectionStatus,
					offline: !this.connectionStatus,
				}
			},
			iconClass() {
				// 不显示图标时检查iconfont里面是否有icon_connectdevice和icon_disconnectdevice
				// 需要下划线，不是中划线
				return {
					icon_connectdevice: this.connectionStatus,
					icon_disconnectdevice: !this.connectionStatus,
				}
			}
		}
	}
</script>

<style>
	@import url("/static/css/iconfont.css");

	page {
		height: 100%;
	}

	.online {
		background-color: #16ba7e;
	}

	.offline {
		background-color: #737373;
	}

	.main {
		width: 100%;
		height: 100%;
		/* border: 1px solid red; */
		display: flex;
		flex-flow: row wrap;
	}

	.center {
		width: 100%;
		height: 500rpx;
		/* border: 1px solid yellow; */
		align-self: flex-end;
		display: flex;
		flex-wrap: wrap;
		align-content: center;
		justify-content: center;
	}

	.circle {
		border-radius: 50%;
		border-style: solid;
		border-width: 1rpx;
		border-color: #fff;
	}

	.circle1 {
		width: 372rpx;
		height: 372rpx;
	}

	.circle2 {
		width: 332rpx;
		height: 332rpx;
		margin: 19rpx 0rpx 0rpx 19rpx;
	}

	.statusText {
		margin-top: 52rpx;
		color: #fff;
		display: flex;
		justify-content: center;
		font-size: 28rpx;
	}

	.connIcon {
		font-size: 12em;
		color: #fff;
	}


	.bottom {
		width: 100%;
		height: 252rpx;
		/* border: 1px solid yellow; */
		align-self: flex-end;
		background-color: #FFFFFF;
	}

	.bottomRow {
		display: flex;
		height: 100%;
		flex-flow: row wrap;
		/* border: 1px solid blue; */
	}

	.tabBar {
		flex: 1;
		/* border: 1px solid red; */
		margin-top: 60rpx;
		text-align: center;
	}

	.tabBarIcon {
		font-size: 72rpx;
		/* border: 1px solid green; */
		color: #000;
	}

	.tabBarText {
		font-size: 26rpx;
		/* border: 1px solid green; */
		color: #000;
		margin-top: 12rpx;
	}
</style>