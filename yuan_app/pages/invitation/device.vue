<template>
	<view class="invitation-page">
		<!-- <u-navbar :is-back="false" background="transparent"	:border-bottom="false">
			<view class="slot-wrap">
				<view class="family">
					<view class="family-name">我的设备</view>
				</view>
			</view>
		</u-navbar> -->
		
		<view class="invitation-text">
			<image :src="staticUrl + '/images/home/<USER>'" mode="aspectFill" style="width: 397rpx; height: 249rpx;"></image>
			<view class="title">
				欢迎使用与安宝小程序
			</view>
			<view class="desc">
				与安宝已实现多设备的连接和管理，为用户提
				供实时的数据统计、事件告警等信息。
			</view>
		</view>
		
		<view class="safe-area-inset-bottom">
			<view class="footer-btns">
				<text class="invitation-user">{{ refName || '' }}邀请你加入“{{ devName || '' }}”</text>
				<u-button shape="circle" class="scan-code-btn diy-btn" size="medium" 
				:custom-style="{ 'width': '100%', 'color': 'white', 'background-color': '#01b09a' }"  hover-class="none"
				open-type="getPhoneNumber" @getphonenumber="onGetPhoneNumber">接受邀请</u-button>
				<view class="gotoApp" @click="$navTo('pages/index/index')">直接进入小程序 ></view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				staticUrl: this.$u.http.config.staticBaseUrl,
				code: undefined,
				devId: undefined,
				memberId:undefined,
				time:undefined,
				devName: '',
				refName: '',
			}
		},
        onLoad(option) {
			if (!option.devId) {
				this.$toast('非法的请求');
			}
			this.devId = option.devId
			this.devName = option.devName
			this.refName = option.name
			this.memberId = option.memberId
			this.time = option.time
			//#ifdef MP-WEIXIN
			wx.hideHomeButton()
			//#endif
        },
		onShow() {
			this.getCode();
		},
		methods: {
			getCode() {
				wx.login({  
					success: (res) => {
						if (res.code) {         //微信登录成功 已拿到code  
							this.code = res.code
							console.log("code:",res.code)
						} else {
							this.code = undefined
						}
					}
				})
			},
			onGetPhoneNumber(e) {
				let _that = this;
				if (e.detail.errMsg == 'getPhoneNumber:fail user deny') {       //用户决绝授权  
					//拒绝授权后弹出一些提示  
				} else {      //允许授权  
					console.log(this.code, e.detail.encryptedData, e.detail.iv)  
					// e.detail.encryptedData      //加密的用户信息  
					// e.detail.iv     //加密算法的初始向量  时要用到  
					let encryptedData = e.detail.encryptedData
					let iv = e.detail.iv
					// uni.login({
					// 	provider: 'weixin',
					// 	success: function(res) {
					if (encryptedData !== undefined && iv !== undefined) {
						_that.handleAccept(this.code, encryptedData, iv)
					}
					// 	}
					// });
				}
			},
			handleAccept(code, encryptedData, iv) {
				if (!code || !encryptedData || !iv) {
					if (!code) {
						this.getCode();
					}
					this.$toast('参数错误');
					return;
				}
				this.$u.api.execAddDevMember({time:this.time, devId: this.devId,memberId:this.memberId, code: code, encryptedData: encryptedData, iv: iv }).then(res => {
					this.$toast('接受邀请成功');
					setTimeout(() => {
						uni.reLaunch({
							url: '/pages/index/index'
						})
					}, 500)
				}).catch(err => {
					this.getCode();
					uni.showToast({ duration: 2000, title: err.message || '发生未知错误', icon: 'none' })
				})
			}
		}
	}
</script>

<style lang="scss">
page {
	background-color: #e7f4f5;
}
</style>

<style lang="scss" scoped>
.invitation-page {
	min-height: 100vh;
	padding: 20rpx;
	.family {
		padding-left: 20rpx;
		padding-top: var(--status-bar-height);
		&-name {
			font-size: 42rpx;
			color: #000000;
			font-weight: bold;
		}
		&-address {
			font-size: 24rpx;
			color: #010101;
			margin-top: 10rpx;
		}
	}
	.invitation-text {
		vertical-align: text-bottom;
		position: absolute;
		top: 50%;
		left: 50%;
		/* height: 30%; */
		width: 70%;
		margin: -40% 0 0 -35%;
		text-align: center;
		.title {
			display: inline-block;
			margin: 0 auto;
			margin-top: 80rpx;
			font-size: 40rpx;
			padding: 14rpx 44rpx;
			color: #000;
		}
		.desc {
			font-size: 24rpx;
			margin-top: 6rpx;
			color: #666;
			line-height: 40rpx;
		}
	}
	.footer-btns {
		position: fixed;
		bottom: 40rpx;
		left: 40rpx;
		right: 40rpx;
		margin: 0 auto;
		text-align: center;
		.invitation-user {
			display: inline-block;
			font-size: 24rpx;
			color: #666;
			margin-bottom: 20rpx
		}
		.scan-code-btn {
			display: block;
		}
	}
	.gotoApp{
		margin-top: 18rpx;
		font-size: 26rpx;
		color:#01b09a;
	}
}
</style>
