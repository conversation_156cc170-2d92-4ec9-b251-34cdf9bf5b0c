<template>
	<view class="report-page">
		<u-navbar :is-back="false" :background="headerBg" :border-bottom="false">
			<view class="slot-wrap">
				<view v-if="!tabs || !tabs.length" class="family">
					<view class="family-name">暂无设备<text class="icon iconfont icon-xialazujian"></text></view>
				</view>
				<view v-else class="family">
					<DeanPopover ref="familyPopover" :btnList="tabs" modal-top-pos="8vw" modal-width="40vw" modal-opacity="1" direction="right" 
						@on-select="handleTabChange" style="z-index: 2">
						<view class="family-name">
							{{ currDev.devName }}
							<text class="icon iconfont icon-xialazujian" style="margin-left: 10rpx; font-size: 22rpx; position: relative; top: -4rpx;"></text>
						</view>
					</DeanPopover>
				</view>
			</view>
		</u-navbar>
		<u-sticky>
			<!-- <view style="background-color: #EEFFFD;"> -->
			<view style="background-color: #f7f7f7;">
				<view class="top-date" @click="dialog.calendar.show = true">
					{{ date }}
					<image src="/static/images/icon-down-arrow.png" class="down-arrow"></image>
				</view>
				<!-- <view style="padding: 0rpx 32rpx;">
					<u-tabs v-if="!currDevId" :list="tabs" :is-scroll="true" :current="tabActived" :item-width="150" bg-color="transparent"
						:active-item-style="{ 'color': '#000' }" 
						:bar-style="{ 'width': '20rpx', 'background-color': '#000', 'border-radius': '20rpx;' }"
						@change="handleTabChange"></u-tabs>
				</view> -->
			</view>
		</u-sticky>
		
		<view v-if="reportInfo" class="careful-text">注意：报告仅作为参考，请勿用于医学证明。</view>
		<!-- <view class="device-name" :style="{ 'border-top': !tabs.length && !reportInfo.id ? '2rpx solid #ECECEC' : '' }">{{ currDev.devName ? `${currDev.devName}-` : '' }}{{ currDev.devCode || '' }}</view> -->
		<!-- <view v-if="reportInfo && reportInfo.id" class="device-name">{{ reportInfo.devSceneName || '' }}-{{ (reportInfo.devName || reportInfo.devCode) || '' }}</view> -->
		
		<view v-if="tabs && tabs.length">
			<view v-if="reportInfo.reportStatStartTime" class="card period flex-row items-center">
				<text class="text" style="margin-left: 12rpx;">统计周期：</text>
				<text class="time">{{getYYYYMMDDHHMM(reportInfo.reportStatStartTime)}}</text>
				至
				<text class="time">{{getYYYYMMDDHHMM(reportInfo.reportStatEndTime)}}</text>
			</view>
			<!-- 事件统计-->
			<view v-if="reportInfo.devScene" class="card">
				<view class="event-block flex-row">
					<text class="event-count-num">{{reportInfo.eventCount||0}}</text>
					<view class="event-count flex-col justify-around">
					  <text class="event-count-unit">次</text>
					  <text class="text_19">今日报警</text>
					</view>
					<view class="event-item flex-col justify-around">
					  <text class="event-type">跌倒</text>
					  <text class="event-num">{{ reportInfo.fallEventCount || 0 }}次</text>
					</view>
					<view class="event-item flex-col justify-around">
					  <text class="event-type">久滞</text>
					  <text class="event-num">{{ reportInfo.longlagEventCount || 0 }}次</text>
					</view>
				</view>
				<text class="event-descr">当日: 已处理{{reportInfo.hadHandleEventCount||0}}次 待处理{{reportInfo.noHandleEventCount||0}}次 延期处理{{reportInfo.delayHandleEventCount||0}}次</text>
			</view>
			<ParlourView v-if="(reportInfo.devScene === '1' || reportInfo.devScene === '4') && reportInfo.id" :info="reportInfo"></ParlourView>
			<ToiletView v-else-if="reportInfo.devScene === '2' && reportInfo.id" :info="reportInfo"></ToiletView>
			<BedroomView v-else-if="reportInfo.devScene === '3' && reportInfo.id" :info="reportInfo"></BedroomView>
			<u-empty v-else :text="hasNavRportNotFound ? '该设备还未生成日报' : '暂无报告'" mode="list" style="display: block;"></u-empty>
		</view>
		<u-empty v-else text="暂无设备" mode="list" style="display: block;"></u-empty>
		<u-calendar v-model="dialog.calendar.show" mode="date" btn-type="success" active-bg-color="#01B09A"
			:max-date="maxDate" :curr-day="maxDate"
			@change="handleDateChange"></u-calendar>
		<u-modal ref="createFamilyDialog" v-model="createFamilyDialog.show" title="创建家庭" :async-close="true" @confirm="handleCreateFamily">
			<view class="slot-content" style="padding: 20rpx; margin-top: 20rpx;">
				<u-input v-model="createFamilyDialog.ipt" type="text" :border="true" placeholder="我的家" :maxlength="15" />
			</view>
		</u-modal>
	</view>
</template>

<script>
	import BedroomView from './components/bedroom.vue';
	import ToiletView from './components/toilet.vue';
	import ParlourView from './components/parlour.vue';
	import DeanPopover from  '@/components/dean-popover/dean-popover.vue'
	export default {
		components: {
			BedroomView, ToiletView, ParlourView,DeanPopover
		},
		data() {
			return {
				dialog: {
					calendar: {
						show: false
					}
				},
				createFamilyDialog: {
					show: false,
					ipt: '',
				},
				tabs: [],
				devs: [],
				reportInfo: {},
				maxDate: this.$u.timeFormat(new Date(new Date()), 'yyyy-mm-dd'),
				date: this.$u.timeFormat(new Date(), 'yyyy-mm-dd'),
				tabActived: 0,
				currDev: {},
				currDevId: undefined,
				hasNavRportNotFound: false,
				headerBg: {
					// 'background-color': '#EEFFFD',
					'background-color':'#f7f7f7',
				},
			}
		},
		onLoad(option) {
			this.currDevId = option.devId;
			this.date = option.date;
			uni.showLoading({
				title: '加载中...',
				mask: true
			})
		},
		onShow() {
			this.fetchFamily();
			let _preDevInfo = uni.getStorageSync('temp_report_nav');
			if (_preDevInfo) {
				this.currDevId = _preDevInfo.id;
				if (_preDevInfo.id && _preDevInfo.name) {
					uni.setNavigationBarTitle({
						title: _preDevInfo.name
					});
				}
				this.tabActived = 0;
				this.devs = [ _preDevInfo ]
				this.tabs = [ _preDevInfo ]
				this.currDev = _preDevInfo;
				this.fetchRecentlyDateHaveReport(_preDevInfo.id, 'nav');
			} else {
				//this.currDevId = '';
				this.devs = [ ]
				this.tabs = [ ]
				this.currDev = undefined;
				this.hasNavRportNotFound = false;
				uni.setNavigationBarTitle({
					title: '日报'
				});
				this.fetchRecentlyDateHaveReport();
			}
		},
		onHide() {
			uni.removeStorageSync('temp_report_nav')
		},
		methods: {
			getYYYYMMDDHHMM(timeStr){
				if(!timeStr){
					return timeStr;
				}
				return timeStr.substring(0,16)
			},
			selectDevice(idx) {
				this.tabActived = index;
				this.currDev = this.devs[index];
				this.currDevId = this.devs[index].id;
				this.fetchReport();
			},
			fetchRecentlyDateHaveReport(id, source) {
				if(this.date){
					this.fetchDevs();
					return ;
				}
				let _params = { needTip: false };
				if (id) {
					_params['devId'] = id
				}
				this.$u.api.fetchRecentlyDateHaveReport(_params).then(res => {
					if (source === 'nav') {
						let _date = res && res.length ? res[0].reportDate : '';
						this.date = _date || this.$u.timeFormat(new Date(), 'yyyy-mm-dd');
						if (!_date) {
							this.hasNavRportNotFound = true;
						}
						this.fetchReport()
					} else {
						let _date = undefined;
						let _max = undefined;
						if (res && res.length) {
							_date = Math.max(...res.map(m => m.reportDate.replace('-', '').replace('-', ''))).toString();
						}
						console.log(res, _date)
						this.date = _date ? `${_date.substring(0, 4)}-${_date.substring(4, 6)}-${_date.substring(6, 8)}` : this.$u.timeFormat(new Date(), 'yyyy-mm-dd');
						this.fetchDevs();
					}
				})
			},
			fetchFamily() {
				this.$u.api.fetchFamilyList({ }).then(res => {
					this.createFamilyDialog.ipt = '';
					if (!res || !res.length) {
						this.createFamilyDialog.show = true;
					} else {
						this.createFamilyDialog.show = false;
					}
				})
			},
			handleCreateFamily() {
				if (!this.createFamilyDialog.ipt) {
					this.createFamilyDialog.ipt = '我的家'
					// this.$toast('请填写家庭名称');
					// this.$refs.createFamilyDialog.clearLoading();
					// return;
				}
				this.$u.api.execCreateFamily({ name: this.createFamilyDialog.ipt }).then(res => {
					this.createFamilyDialog.ipt = '';
					this.createFamilyDialog.show = false;
					this.$toast('创建家庭成功');
					uni.reLaunch({
						url: 'pages/index/index'
					})
				}).catch(err => {
					this.$refs.createFamilyDialog.clearLoading();
					uni.showToast({ duration: 2000, title: err.message || '发生未知错误', icon: 'none' })
				})
			},
			handleDateChange(day) {
				this.date = day.result;
				console.log(this.date);
				this.fetchReport();
			},
			handleTabChange(index) {
				this.tabActived = index;
				this.currDev = this.devs[index];
				this.currDevId = this.devs[index].id;
				this.fetchReport();
			},
			fetchDevs() {
				this.$u.api.fetchMyDeviceList({ }).then(res => {
					this.devs = res || []
					this.tabs = this.devs.map(m => {
						return {
							name: (m.devName || m.devCode) || ''
						}
					})
					if (this.devs && this.devs.length) {
						if(!this.currDevId){
							this.currDev = this.devs[0];
							this.currDevId = this.currDev.id;
						}else{
							 let arr = this.devs.filter(obj => obj.id == this.currDevId); 
							 this.currDev = arr?arr[0]:this.devs[0];
						}
						console.log("this.currDev:",this.currDev);
					}
					
					this.fetchReport()
				}).catch(err => {
					uni.showToast({ duration: 2000, title: err.message || '发生未知错误', icon: 'none' })
				})
			},
			fetchReport() {
				this.reportInfo = {};
				this.hasNavRportNotFound =false;
				if (!this.currDevId || !this.date) return;
				this.$u.api.fetchReport({ devId: this.currDevId, dateStr: this.date }).then(res => {
					this.reportInfo = res || {}
				}).catch(err => {
					uni.showToast({ duration: 2000, title: err.message || '发生未知错误', icon: 'none' })
				})
			},
		}
	}
</script>

<style lang="scss">
page {
	background:#f7f7f7;
	// background: linear-gradient(180deg, #EEFFFD 0%, rgba(238,255,253,0) 100%);
}
</style>

<style lang="scss" scoped>
@import url("/static/css/iconfont.css");
::v-deep .u-navbar-fixed {
	padding-bottom: 0rpx;
}
.report-page {
	padding: 24rpx;
	.event-block{
		min-height: 140rpx;
		.event-count-num {
		  min-width: 34px;
		  font-size: 56px;
		  color: #333333;
		}
		.event-count{
			margin-left: 8rpx;
			.event-count-unit{
				font-size: 11px;
				color: #999999;
				line-height: 17px;
			}
		}	
		.event-item{
			margin-left: 72rpx;
			.event-type{
				font-size: 11px;
				color: #999999;
				line-height: 17px;
			}
			.event-num{
				font-size: 13px;
				color: #333333;
				line-height: 20px;
				font-weight: 600;
			}
		} 
	}
	.event-descr{
		font-size: 11px;
		color: #999999;
		line-height: 17px;
	}
	.card{
		padding: 24rpx;
		margin-top: 24rpx;
		min-height: 100rpx;
		background-color: white;
		border-radius: 12rpx;
	}
	.family {
		padding-left: 20rpx;
		&-name {
			font-size: 36rpx;
			color: #01B09A;
			font-weight: bold;
		}
	}
	.top-date {
		text-align: center;
		position: relative;
		top: -8rpx;
		border-bottom: 2rpx solid #c7c7c7;
		padding-bottom: 18rpx;
		.down-arrow {
			width: 26rpx;
			height: 48rpx;
			margin-left: 14rpx;
			position: relative;
			top: 12rpx;
		}
	}
	.careful-text {
		color: #F6A929;
		background: #FDF4ED;
		font-size: 22rpx;
		// border-top: 2rpx solid #ECECEC;
		// border-bottom: 2rpx solid #ECECEC;
		padding: 20rpx 32rpx;
	}
	.device-name {
		color: #000;
		font-size: 32rpx;
		border-bottom: 2rpx solid #ECECEC;
		padding: 32rpx;
	}
	.period{
		font-size: 26rpx;
		.text{
			font-size: 26rpx;
			color: #666;
		}
		.time{
			margin-right:8rpx;
			margin-left: 8rpx;
			color:#01B09A;
		}
	}
}
::v-deep .u-tabs-scorll-flex {
	justify-content: flex-start !important;
}
::v-deep .u-tab-item {
	flex: initial !important;
	width: 120rpx !important;
	margin-right: 20rpx !important;
	height: 62rpx !important;
	line-height: 44rpx !important;
	padding: 0rpx !important;
	
}
::v-deep .u-tab-bar {
	left: 10rpx !important;
	bottom: -3rpx !important;
}
::v-deep .u-tabs {
	margin-bottom: 20rpx;
}
::v-deep .u-calendar__bottom {
	margin-top: 40rpx;
}
::v-deep .u-calendar__bottom__choose {
	display: none;
}
::v-deep .u-btn--success {
	border-color: #01B09A !important;
	background-color: #01B09A !important;
}
::v-deep .u-success-hover {
	border-color: #00ada2 !important;
	background-color: #00ada2 !important;
}
::v-deep .u-empty {
	text-align: center;
	margin-top: 220rpx !important;
}
</style>
