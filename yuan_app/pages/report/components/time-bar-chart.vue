<template>
	<view>
		<!-- #ifdef MP-WEIXIN -->
		<uni-ec-canvas class="uni-ec-canvas" ref="reportTimeBarCanvas" canvas-id="report-time-bar-lazy-load-chart"
			:ec="ec"></uni-ec-canvas>
		<!-- #endif -->
		<!-- #ifndef MP-WEIXIN -->
		<view :id="optionId" class="uni-ec-canvas"></view>
		<!-- #endif -->
	</view>
</template>

<script>
	import uniEcCanvas from '@/components/uni-ec-canvas/uni-ec-canvas.vue';
	import * as echarts from "@/components/uni-ec-canvas/echarts";
	import * as util from '@/utils/util'
	let reportTimeBarChart = null
	export default {
		components: {
			uniEcCanvas
		},
		props: {
			numObj: {
				type: Object,
				default: function() {
					return {};
				},
			},
		},
		data() {
			return {
				optionId: undefined,
				ec: {
					lazyLoad: true
				},
				option: {
					tooltip: { show: false },
					grid: { left: 10, top: 20, bottom: 0, right: 64, containLabel: true },
					xAxis: {
						type: 'value',
						boundaryGap: false,
						max: Math.max(...Object.values(this.numObj)), // Math.ceil(max / 4) * 5 || 10
						axisLine: { show: false, lineStyle: { color: '#ccc' } },
						axisTick: { show: false },
						axisLabel: { show: false, color: '#999' },
						splitLine: { show: false, lineStyle: { color: ['#CEEDFF'], type: [5, 8], dashOffset: 3 } },
					},
					yAxis: {
						type: 'category',
						data: [
							'无活动',
							'独处',
							'群体',
						],
						axisLine: { show: false, lineStyle: { color: '#ccc' } },
						axisTick: { show: false, length: 3 },
						axisLabel: { fontSize: 14, color: '#666', margin: 16, padding: 0 },
						inverse: true,
					},
					series: [
						{
							name: '数量',
							type: 'bar',
							itemStyle: {
								color: '#52A8FF',
								normal: {
									barBorderRadius: [18, 18, 18, 18],
									color: function (params) {
										// 定义一个颜色集合
										let colorList = [
											'#ED2E1C',
											'#FAD347',
											'#81D88A',
											'#52A8FF',
											'#00B389',
											'#FFA940',
											'#FF5A57',
											'#29EFC4',
											'#F8AEA4',
											'#FFC53D',
											'#009982',
											'#C099FC',
											'#F5855F',
										];
										// 对每个bar显示一种颜色
										return colorList[params.dataIndex];
									},
								},
							},
							barMaxWidth: 12,
							label: { show: true, position: 'right', offset: [5, 1], color: '#000000', formatter: function(param) {
								return util.getSFM(param.value * 60, 'H时i分');
							} },
							data: [ this.numObj.n1, this.numObj.n2, this.numObj.n3 ],
						},
					],
				}
			}
		},
		mounted() {
			// 设置随机数id
			let t = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
			let len = t.length
			let id = ''
			for (let i = 0; i < 32; i++) {
				id += t.charAt(Math.floor(Math.random() * len))
			}
			this.optionId = id
			this.$nextTick(() => {
				// #ifdef MP-WEIXIN
				this.$refs.reportTimeBarCanvas.init(this.initChart)
				// #endif
				// #ifndef MP-WEIXIN
				let el = document.getElementById(this.optionId);
				let myChart= echarts.init(el);
				myChart.setOption(this.option);
				// #endif
			})
		},
		beforeDestroy() {
			// #ifdef MP-WEIXIN
			reportTimeBarChart.dispose();
			// #endif
			console.log('reportTimeBarChart.dispose');
		},
		methods: {
			initChart(canvas, width, height, canvasDpr) {
				reportTimeBarChart = echarts.init(canvas, null, {
					width: width,
					height: height,
					devicePixelRatio: canvasDpr
				})
				canvas.setChart(reportTimeBarChart)
				reportTimeBarChart.setOption(this.option)
				return reportTimeBarChart
			}
		}
	}
</script>

<style>
	.uni-ec-canvas {
		width: 100%;
		height: 200rpx;
		display: block;
	}
</style>
