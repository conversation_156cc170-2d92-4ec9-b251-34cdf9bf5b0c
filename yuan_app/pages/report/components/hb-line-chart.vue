<template>
	<view>
		<!-- #ifdef MP-WEIXIN -->
		<uni-ec-canvas class="uni-ec-canvas" ref="lineCanvas" :id="optionId" canvas-id="badroom-lazy-load-chart"
			:ec="ec"></uni-ec-canvas>
		<!-- #endif -->
		<!-- #ifndef MP-WEIXIN -->
		<view :id="optionId" class="uni-ec-canvas"></view>
		<!-- #endif -->
	</view>
</template>

<script>
	import uniEcCanvas from '@/components/uni-ec-canvas/uni-ec-canvas.vue';
	import * as echarts from "@/components/uni-ec-canvas/echarts";
	let lineChart = null

	export default {
		components: {
			uniEcCanvas
		},
		props: {
			xAxisDatas: {
				type: Array,
				default: function() {
					return []
				}
			},
			seriesDatas: {
				type: Array,
				default: function() {
					return []
				}
			},
			legendData: {
				type: Array,
				default: function() {
					return []
				}
			}
		},
		data() {
			return {
				optionId: undefined,
				ec: {
					lazyLoad: true
				},
				option: {
					title: {
						show: false,
						textStyle: {
							color: 'gray',
							fontSize: 13
						},
						text: '暂无数据',
						left: 'center',
						top: 'center'
					},
					tooltip: {
						trigger: 'axis'
					},
					legend: {
						data: []
					},
					grid: {
						top: 20,
						bottom: 20,
					},
					toolbox: {
						feature: {
							saveAsImage: {}
						}
					},
					xAxis: {
						type: 'category',
						boundaryGap: false,
						data: []
					},
					yAxis: {
						type: 'value'
					},
					series: [],
				}
			}
		},
		mounted() {
			// 设置随机数id
			let t = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
			let len = t.length
			let id = ''
			for (let i = 0; i < 32; i++) {
				id += t.charAt(Math.floor(Math.random() * len))
			}
			this.optionId = id
			const aa = [{
					name: 'Email',
					type: 'line',
					stack: 'Total',
					data: [120, 132, 101, 134, 90, 230, 210]
				},
				{
					name: 'Union Ads',
					type: 'line',
					stack: 'Total',
					data: [220, 182, 191, 234, 290, 330, 310]
				},
				{
					name: 'Video Ads',
					type: 'line',
					stack: 'Total',
					data: [150, 232, 201, 154, 190, 330, 410]
				}
			];
			//this.option.legend.data = ['Email', 'Union Ads', 'Video Ads'];
			//this.option.xAxis.data = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
			// console.log("aa:",aa)
			// console.log("this.seriesDatas:",this.seriesDatas)
			if (this.legendData.length < 1 || this.xAxisDatas.length < 1 || this.seriesDatas.length < 1) {
				this.option.title.show = true;
			} else {
				this.option.title.show = false;
				this.option.legend.data = this.legendData;
				this.option.xAxis.data = this.xAxisDatas;
				this.option.series = this.seriesDatas;
			}
			this.$nextTick(() => {
				// #ifdef MP-WEIXIN
				this.$refs.lineCanvas.init(this.initChart)
				// #endif
				// #ifndef MP-WEIXIN
				let el = document.getElementById(this.optionId);
				let myChart = echarts.init(el);
				myChart.setOption(this.option);
				// #endif
			})
		},
		beforeDestroy() {
			// #ifdef MP-WEIXIN
			lineChart.dispose();
			// #endif
			console.log('lineChart.dispose');
		},
		methods: {
			initChart(canvas, width, height, canvasDpr) {
				lineChart = echarts.init(canvas, null, {
					width: width,
					height: height,
					devicePixelRatio: canvasDpr
				})
				canvas.setChart(lineChart)
				lineChart.setOption(this.option)
				return lineChart
			}
		}
	}
</script>

<style>
	.uni-ec-canvas {
		width: 100%;
		height: 300rpx;
		display: block;
	}
</style>