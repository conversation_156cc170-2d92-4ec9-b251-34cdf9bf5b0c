<template>
	<view>
		<!-- #ifdef MP-WEIXIN -->
		<uni-ec-canvas class="uni-ec-canvas" ref="lineCanvas" canvas-id="line-7d-lazy-load-chart"
			:ec="ec"></uni-ec-canvas>
		<!-- #endif -->
		<!-- #ifndef MP-WEIXIN -->
		<view :id="optionId" class="uni-ec-canvas"></view>
		<!-- #endif -->
	</view>
</template>

<script>
	import uniEcCanvas from '@/components/uni-ec-canvas/uni-ec-canvas.vue';
	import * as echarts from "@/components/uni-ec-canvas/echarts";
	let line7dChart = null

	export default {
		components: {
			uniEcCanvas
		},
		props: {
			datas: {
				type: Array,
				default: function() {
					return []
				}
			}
		},
		data() {
			return {
				optionId: undefined,
				ec: {
					lazyLoad: true
				},
				option: {
					tooltip: {
						show: true,
						trigger: 'axis',
						formatter: function(a) {
							var str = `${a[0].name}`;
							for (var h = 0; h < a.length; h++) {
								str += `\r\n${a[h].seriesName}: ${a[h].value}`;
							}
							return str;
						},
						textStyle: {
							fontSize: 12,
						}
					},
					// dataZoom: [
						// {
						// 	show: true,
						// 	realtime: true,
						// 	start: 30,
						// 	end: 70,
						// 	xAxisIndex: [0, 1],
						// },
						// {
						// 	type: 'inside',
						// 	realtime: true,
						// 	start: 30,
						// 	end: 70,
						// 	xAxisIndex: [0, 1],
						// },
					// ],
					grid: {
						left: '8%',
						right: '10%',
						bottom: '0',
						top: '20',
						containLabel: true,
					},
					xAxis: {
						data: [],
					 	show: true,
						axisLabel: {
							color: '#01B09A',
							textStyle: {
								fontSize: '13'
							},
						},
						axisTick: {
							show: true,
							lineStyle: {
								color: '#01B09A',
							}
						},
						axisLine: {
							lineStyle: {
								color: '#01B09A',
							}
						},
						splitLine: {
							show: false,
							lineStyle: {
								color: '#01B09A',
							}
						}
					},
					yAxis: {
						min: 0,
						splitNumber: 4,
						axisLine: {
							show: false,
						},
						axisTick: {
							show: false,
						},
						splitLine: {
							show: true,
							lineStyle: {
								type: 'dashed',
								color: '#4D7BFF',
							}
						},
						axisLabel: {
							show: true,
						},
					},
					series: []
				}
			}
		},
		mounted() {
			// 设置随机数id
			let t = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
			let len = t.length
			let id = ''
			for (let i = 0; i < 32; i++) {
				id += t.charAt(Math.floor(Math.random() * len))
			}
			this.optionId = id
			let _titles = [];
			let _values = [
				{
					name: '总进出',
					type: 'line',
					showSymbol: false,
					data: [],
					lineStyle: { width: 2, color: '#F66C3E' },
				}, {
					name: '白天进出',
					type: 'line',
					showSymbol: false,
					data: [],
					lineStyle: { width: 2, color: '#01B09A' },
				}, {
					name: '夜晚进出',
					type: 'line',
					showSymbol: false,
					data: [],
					lineStyle: { width: 2, color: '#4D7BFF' },
				}
			];
			let _toileInoutCount = [], _toileDayInoutCount = [], _toileNightInoutCount = [];
			this.datas.map((item) => {
				_titles.push(item.reportDate);
				_toileInoutCount.push(item.toileInoutCount || 0)
				_toileDayInoutCount.push(item.toileDayInoutCount || 0)
				_toileNightInoutCount.push(item.toileNightInoutCount || 0)
			});
			_values[0].data = _toileInoutCount;
			_values[1].data = _toileDayInoutCount;
			_values[2].data = _toileNightInoutCount;
			this.option.xAxis.data = _titles || [];
			this.option.series = _values || [];
			this.$nextTick(() => {
				// #ifdef MP-WEIXIN
				this.$refs.lineCanvas.init(this.initChart)
				// #endif
				// #ifndef MP-WEIXIN
				let el = document.getElementById(this.optionId);
				let myChart= echarts.init(el);
				myChart.setOption(this.option);
				// #endif
			})
		},
		beforeDestroy() {
			// #ifdef MP-WEIXIN
			line7dChart.dispose();
			// #endif
			console.log('line7dChart.dispose');
		},
		methods: {
			initChart(canvas, width, height, canvasDpr) {
				line7dChart = echarts.init(canvas, null, {
					width: width,
					height: height,
					devicePixelRatio: canvasDpr
				})
				canvas.setChart(line7dChart)
				line7dChart.setOption(this.option)
				return line7dChart
			}
		}
	}
</script>

<style>
	.uni-ec-canvas {
		width: 100%;
		height: 300rpx;
		display: block;
	}
</style>
