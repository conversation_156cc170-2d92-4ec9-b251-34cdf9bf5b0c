<template>
	<view>
		<!-- #ifdef MP-WEIXIN -->
		<uni-ec-canvas class="uni-ec-canvas" ref="barCanvas" canvas-id="bar-chart" :ec="ec"></uni-ec-canvas>
		<!-- #endif -->
		<!-- #ifndef MP-WEIXIN -->
		<view :id="optionId" class="uni-ec-canvas"></view>
		<!-- #endif -->
	</view>
</template>

<script>
	import uniEcCanvas from '@/components/uni-ec-canvas/uni-ec-canvas.vue';
	import * as echarts from "@/components/uni-ec-canvas/echarts";
	let BarChat = null
	export default {
		name: 'BarChat',
		components: {
			uniEcCanvas
		},
		props: {
			datas: {
				type: Array,
				default: function() {
					return []
				}
			},
		},
		data() {
			return {
				optionId: undefined,
				ec: {
					lazyLoad: true
				},
				option: {
					title: {
					      show: false, // 无数据时展示 title
					      textStyle: {
					        color: 'gray',
					        fontSize: 13
					      },
					      text: '暂无数据',
					      left: 'center',
					      top: 'center'
					},
					tooltip: {
						show:true
					},
					color:['#20CD8A'],
					grid:{
						top:10,
						bottom:2,
					},
					xAxis: {
						type: 'category',
						axisTick:{
							show:false,
						},
						data: [],
					},
					yAxis: {
						show:false,
					},
					series: [{
						data: [],
						type: 'bar'
					}],
				}
			};
		},
		mounted() {
			// 设置随机数id
			let t = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
			let len = t.length
			let id = ''
			for (let i = 0; i < 32; i++) {
				id += t.charAt(Math.floor(Math.random() * len))
			}
			this.optionId = id
			// this.option.xAxis.data = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
			// this.option.series[0].data = [120, 200, 150, 80, 70, 110, 130];t', 'Sun'];
			// this.option.xAxis.data = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
			if(!this.datas || this.datas<1){
					this.option.title.show = true;
			}else{
				this.option.series[0].data = this.datas.map(obj => obj.correctedValue);
				this.option.xAxis.data = this.datas.map(obj => obj.createDate);
			}
			this.$nextTick(() => {
				// #ifdef MP-WEIXIN
				this.$refs.barCanvas.init(this.initChart)
				// #endif
				// #ifndef MP-WEIXIN
				let el = document.getElementById(this.optionId);
				let myChart = echarts.init(el);
				myChart.setOption(this.option);
				// #endif
			})
		},
		beforeDestroy() {
			// #ifdef MP-WEIXIN
			BarChat.dispose();
			// #endif
			console.log('BarChat.dispose');
		},
		methods: {
			initChart(canvas, width, height, canvasDpr) {
				BarChat = echarts.init(canvas, null, {
					width: width,
					height: height,
					devicePixelRatio: canvasDpr
				})
				canvas.setChart(BarChat)
				BarChat.setOption(this.option)
				return BarChat
			}
		}
	}
</script>

<style>
	.uni-ec-canvas {
		width: 100%;
		height: 200rpx;
		display: block;
	}
</style>
