<template>
	<view>
		<view class="container flex-col">
			<!-- 睡眠统计 -->
			<view class="card sleep-card flex-row items-center">
				<view class="sleep-score flex-col justify-between items-center">
					<view>
						<text class="sleep-score-num">{{info.sleepMetrics.sleepScore||'--'}}</text>
						<text class="sleep-score-unit">分</text>
					</view>
					<view class="sleep-tag flex-col items-center" :style="{background: getColor(info.sleepMetrics.sleepScore>=80)}">
						<text v-if="info.sleepMetrics.sleepScore<60" class="sleep-tag-text">差</text>
						<text v-else-if="info.sleepMetrics.sleepScore >= 60 && info.sleepMetrics.sleepScore<80" class="sleep-tag-text">一般</text>
						<text v-else-if="info.sleepMetrics.sleepScore >= 80 && info.sleepMetrics.sleepScore<90" class="sleep-tag-text">良好</text>
						<text v-else="info.sleepMetrics.sleepScore>=90" class="sleep-tag-text">优秀</text>
					</view>
				</view>
				<LineDivider direction="vertical" lineWidth="1px" lineColor="#eee" :lineLength="'100rpx'" style="margin: 10rpx;"></LineDivider>
				<view class="sleep-data flex-col justify-between">
					<view class="sleep-data-item flex-row items-center">
						<!-- 图标大小不一致导致对不齐，这两处直接写在style里面 -->
						<text class="icon iconfont icon-chuang" style="font-size: 72rpx; margin-left: -12rpx;"></text>
						<text class="text">在床时间</text>
						<text class="time">{{getHHMM(info.sleepMetrics.nightInbedEarliestTime)||getHHMM(info.reportStatStartTime)+'前'}}</text>
						<LineDivider direction="vertical" lineWidth="1px" lineColor="#eee" :lineLength="'32rpx'" style="margin: 10rpx;"></LineDivider>
						<text class="text">入睡时间</text>
						<text class="time">{{getHHMM(info.sleepMetrics.sleepTime)||'--:--'}}</text>
					</view>
					<view class="sleep-warn flex-row">
						<text class="sleep-warn-text warn-left" :style="{visibility:(1==2?'visible':'hidden')}">提示语</text>
						<text class="sleep-warn-text warn-right"
							:style="{visibility:showSleepWarnText()?'visible':'hidden'}"
							>建议23:00前入睡</text>
					</view>
					<view class="sleep-data-item flex-row items-center">
						<text class="icon iconfont icon-zuidatingliushichang"></text>
						<text class="text" style="margin-left: 12rpx;">清醒时间</text>
						<text class="time">{{getHHMM(info.sleepMetrics.awakeTime)||'--:--'}}</text>
						<LineDivider direction="vertical" lineWidth="1px" lineColor="#eee" :lineLength="'32rpx'" style="margin: 10rpx;"></LineDivider>
						<text class="text">起床时间</text>
						<text class="time">{{getHHMM(info.sleepMetrics.dayOutBedEarliestTime)||getHHMM(info.reportStatEndTime)+'后'}}</text>
					</view>
					<view class="sleep-warn flex-row">
						<text class="sleep-warn-text warn-left" :style="{visibility:(1==2?'visible':'hidden')}">提示语</text>
						<text class="sleep-warn-text warn-right" :style="{visibility:(1==2?'visible':'hidden')}">提示语</text>
					</view>
				</view>
			</view>
			<!-- 睡眠周期 -->
			<view class="card sleep-cycle-card flex-col">
				<view class="flex-row items-center">
					<text class="card-icon iconfont icon-chuang" style="font-size: 64rpx"></text>
					<text class="care-title">睡眠周期</text>	
				</view>
				<RectChart :datas="info.sleepMetrics.sleepCycleList">
				</RectChart>
				<view class="flex-col sleep-chart-bottom">
					<view class="flex-row justify-between">
						<text class="time">在床{{getHHMM(info.sleepMetrics.nightInbedEarliestTime)||getHHMM(info.reportStatStartTime)+'前'}}</text>
						<text class="time">起床{{getHHMM(info.sleepMetrics.dayOutBedEarliestTime)||getHHMM(info.reportStatEndTime)+'后'}}</text>
					</view><view class="flex-row justify-between">
						<text class="date">{{convertToMonthDayFormat(getPreviousDay(info.reportDate))}}</text>
						<text class="date">{{convertToMonthDayFormat(info.reportDate)}}</text>
					</view>
				</view>
				<view class="flex-col sleep-time-count">
					<view class="flex-row justify-around">
						<view class="flex-col sleep-time-item">
							<text class="title color1">清醒</text>
							<text>{{formatSecond2String(info.sleepMetrics.awakeDuration*60)||'--'}}</text>
						</view>
						<view class="flex-col sleep-time-item">
							<text class="title color2">离床</text>
							<text class="total-time">{{formatSecond2String(info.sleepMetrics.outOfBedDuration*60)||'--'}}</text>
						</view>
					</view>
					<view class="flex-row justify-around" style="margin-top: 24rpx;">
						<view class="flex-col sleep-time-item">
							<text class="title color3">浅睡</text>
							<text class="total-time">{{formatSecond2String(info.sleepMetrics.lightSleepDuration*60)||'--'}}</text>
						</view>
						<view class="flex-col sleep-time-item">
							<text class="title color4">深睡</text>
							<text class="total-time">{{formatSecond2String(info.sleepMetrics.deepSleepDuration*60)||'--'}}</text>
						</view>
					</view>
				</view>
				<view class="flex-col gray-card">
					<text class="title">睡眠周期<text :style="{color: getColor(info.sleepMetrics.sleepCycleCount>=3 && info.sleepMetrics.sleepCycleCount<=6)}">{{info.sleepMetrics.sleepCycleCount||0}}次</text></text>
					<text class="descr">参考值：3~6次</text>
					<view v-if="info.sleepMetrics.sleepCycleCount<3" class="flex-col item-warn">
						<view class="flex-row">
							<text class="icon iconfont icon-caozuorizhi warn-title"></text>
							<text class="warn-title">睡眠周期</text>
						</view>
						<text class="warn-advise">
							尽量每天在相同的时间上床睡觉和起床，避免睡前使用电子设备；保持安静、黑暗和凉爽的睡眠环境，使用舒适的床上用品；限制咖啡因、尼古丁和酒精的摄入。
						</text>
					</view><view v-if="info.sleepMetrics.sleepCycleCount>6" class="flex-col item-warn">
						<view class="flex-row">
							<text class="icon iconfont icon-caozuorizhi warn-title"></text>
							<text class="warn-title">睡眠周期过多</text>
						</view>
						<text class="warn-advise">
							每天在相同的时间起床，不睡懒觉，避免在临近睡觉的时候进食，也不要空腹睡觉；多接触阳光有助于调整生物钟。
						</text>
					</view>
				</view>
				
				<view class="flex-col sleep-cycle-item">
					<text class="title">睡眠时长<text :style="{color: getColor(info.sleepMetrics.sleepDuration/60 >=6 && info.sleepMetrics.sleepDuration/60<=10)}">{{formatSecond2String(info.sleepMetrics.sleepDuration*60)||'--'}}</text></text>
					<text class="descr">参考值：6~10小时</text>
					<view v-if="info.sleepMetrics.sleepDuration/60<6" class="flex-col item-warn">
						<view class="flex-row">
							<text class="icon iconfont icon-caozuorizhi warn-title"></text>
							<text class="warn-title">睡眠时长缺乏</text>
						</view>
						<text class="warn-advise">
							请您要建立规律的作息时间表，尽量每天都在相同的时间入睡和起床。
						</text>
					</view>
					<view v-if="info.sleepMetrics.sleepDuration/60>10" class="flex-col item-warn">
						<view class="flex-row">
							<text class="icon iconfont icon-caozuorizhi warn-title"></text>
							<text class="warn-title">睡眠时长过长</text>
						</view>
						<text class="warn-advise">
							保存规律的作息时间，不要睡懒觉，建议午睡时间在30分钟内，建立良好的睡前习惯。
						</text>
					</view>
				</view>
				<u-line color="#eee" />
				
				<!-- 
				<view class="flex-col sleep-cycle-item">
					<text class="title">离床<text :style="{color: getColor(info.sleepMetrics.nightOutbedCount<3)}">{{info.sleepMetrics.nightOutbedCount||0}}次</text></text>
					<text class="descr">参考值：0~2次</text>
					<view v-if="info.nightOutbedCount>2" class="flex-col item-warn">
						<view class="flex-row">
							<text class="icon iconfont icon-caozuorizhi warn-title"></text>
							<text class="warn-title">离床次数过多</text>
						</view>
						<text class="warn-advise">
							夜间睡觉时频繁离床可能会影响睡眠质量，可减少睡前喝水，创建舒适的睡眠环境，可咨询医生检查药物影响。
						</text>
					</view>
				</view>
				<u-line color="#eee" /> 
				-->
				
				<view class="flex-col sleep-cycle-item">
					<text class="title">清醒时长
						<text :style="{color: getColor(info.sleepMetrics.awakeDurationRatio<0.35)}">
						{{roundToDecimal(info.sleepMetrics.awakeDurationRatio*100,0)||0}}%
						</text>
					</text>
					<text class="descr">参考值：35%</text>
					<view v-if="info.sleepMetrics.awakeDurationRatio>=0.35" class="flex-col item-warn">
						<view class="flex-row">
							<text class="icon iconfont icon-caozuorizhi warn-title"></text>
							<text class="warn-title">清醒时长过长</text>
						</view>
						<text class="warn-advise">
							确保睡眠环境安静、黑暗且舒适，减少噪音和光线的干扰；避免咖啡因、尼古丁和究竟的摄入；压力、焦虑或抑郁等心理问题可能导致睡眠问题，如有需要请寻求专业帮助。
						</text>
					</view>
				</view>
				<u-line color="#eee" />
				
				<view class="flex-col sleep-cycle-item">
					<text class="title">浅睡时长
					<text :style="{color: getColor(info.sleepMetrics.lightSleepDurationRatio>=0.5 && info.sleepMetrics.lightSleepDurationRatio<=0.6)}">
					{{roundToDecimal(info.sleepMetrics.lightSleepDurationRatio*100,0)||0}}%</text>
					</text>
					<text class="descr">参考值：50%~60%</text>
					<view v-if="info.sleepMetrics.lightSleepDurationRatio<0.5" class="flex-col item-warn">
						<view class="flex-row">
							<text class="icon iconfont icon-caozuorizhi warn-title"></text>
							<text class="warn-title">浅睡时长过短</text>
						</view>
						<text class="warn-advise">
							在睡前进行一些放松活动，如阅读、瑜伽或深呼吸练习。
						</text>
					</view>
					<view v-if="info.sleepMetrics.lightSleepDurationRatio>0.5" class="flex-col item-warn">
						<view class="flex-row">
							<text class="icon iconfont icon-caozuorizhi warn-title"></text>
							<text class="warn-title">浅睡时长过长</text>
						</view>
						<text class="warn-advise">
							创建舒适的睡眠环境，使用舒适的床上用品，保持充足的运动和户外活动可以帮助改善睡眠质量。
						</text>
					</view>
				</view>
				<u-line color="#eee" />
				
				<view class="flex-col sleep-cycle-item">
					<text class="title">深睡时长
						<text :style="{color: getColor(info.sleepMetrics.deepSleepDurationRatio>=0.15 && info.sleepMetrics.deepSleepDurationRatio<=0.3)}">
							{{roundToDecimal(info.sleepMetrics.deepSleepDurationRatio*100,0)||0}}%
							</text>
						</text>
					<text class="descr">参考值：15%~30%</text>
					<view v-if="info.sleepMetrics.deepSleepDurationRatio<0.15" class="flex-col item-warn">
						<view class="flex-row">
							<text class="icon iconfont icon-caozuorizhi warn-title"></text>
							<text class="warn-title">深睡时长过短</text>
						</view>
						<text class="warn-advise">
							每天尽量在相同的时间上床睡觉和起床，保持舒适的睡眠环境，减少噪音；考虑心理因素，如有需要请寻求专业帮助。
						</text>
					</view>
					<view v-if="info.sleepMetrics.deepSleepDurationRatio>0.3" class="flex-col item-warn">
						<view class="flex-row">
							<text class="icon iconfont icon-caozuorizhi warn-title"></text>
							<text class="warn-title">深睡时长过长</text>
						</view>
						<text class="warn-advise">
							每天尽量在相同的时间上床睡觉和起床，保持舒适的睡眠环境，减少噪音。
						</text>
					</view>
				</view>
				<u-line color="#eee" />
				
				<view class="flex-col sleep-cycle-item">
					<text class="title">入睡时长<text :style="{color: getColor(info.sleepMetrics.sleepOnsetDuration<30)}">{{info.sleepMetrics.sleepOnsetDuration||'--'}}分</text></text>
					<text class="descr">参考值：＜30分钟</text>
					<view v-if="info.sleepMetrics.sleepOnsetDuration>=30" class="flex-col item-warn">
						<view class="flex-row">
							<text class="icon iconfont icon-caozuorizhi warn-title"></text>
							<text class="warn-title">入睡时较慢</text>
						</view>
						<text class="warn-advise">
							有入睡困难的风险，可在睡前进行一些放松活动，如阅读、瑜伽或深呼吸练习；尝试播放白噪音，有助于放松身心，更快入睡。
						</text>
					</view>
				</view>
				<u-line color="#eee" />
				
				<view class="flex-col sleep-cycle-item">
					<text class="title">睡眠效率
					<text :style="{color: getColor(info.sleepMetrics.sleepEfficiency>=0.85)}">
							{{roundToDecimal(info.sleepMetrics.sleepEfficiency*100,0)||0}}%
						</text>
					</text>
					<text class="descr">参考值：≥85%</text>
					<view v-if="info.sleepMetrics.sleepEfficiency<=0.85" class="flex-col item-warn">
						<view class="flex-row">
							<text class="icon iconfont icon-caozuorizhi warn-title"></text>
							<text class="warn-title">睡眠效率低</text>
						</view>
						<text class="warn-advise">
							睡眠效率不佳可能会导致您在睡觉时感觉没有得到充分的休息，请保持规律的作息时间，创建舒适的睡眠环境，养成良好的睡眠习惯。
						</text>
					</view>
				</view>
				
			</view>
			<!-- 体动 -->
			<view class="card body-move-card flex-col">
				<view class="flex-row items-center">
					<text class="card-icon iconfont icon-zaichuangshijian"></text>
					<text class="care-title">体动</text>	
					<u-tag v-if="(info.sleepMetrics.motionCount>=75 && info.sleepMetrics.motionCount<=175)" text="正常" size="mini" :bg-color="getColor(true)" :border-color="getColor(true)" color="#fff"/>
					<u-tag v-else-if="info.sleepMetrics.motionCount<75" text="偏低" size="mini" :bg-color="getColor(false)" :border-color="getColor(false)"color="#fff"/>
					<u-tag v-else-if="info.sleepMetrics.motionCount>175" text="偏高" size="mini" :bg-color="getColor(false)" :border-color="getColor(false)" color="#fff"/>
				</view>
				<view class="flex-row justify-between">
					<text class="reference-value">参考值：75~175次</text>
					<text>共{{info.sleepMetrics.motionCount||0}}次</text>
				</view>
				<BarChat :datas="info.sleepMetrics.motionList"></BarChat>
			</view>
			<!-- 心率 -->
			<view class="card heart-card flex-col">
				<view class="flex-row items-center">
					<text class="card-icon iconfont icon-xinshuaijiance"></text>
					<text class="care-title">心率</text>	
					<!-- 
					02024-04-23去掉
					<u-tag v-if="info.benchmarkHeartRate>=55&&info.benchmarkHeartRate<=65" text="正常" size="mini" :bg-color="getColor(true)" color="#fff"/>
					<u-tag v-else-if="info.benchmarkHeartRate<55" text="偏低" size="mini" :bg-color="getColor(false)" :border-color="getColor(false)" color="#fff"/>
					<u-tag v-else-if="info.benchmarkHeartRate>65" text="偏高" size="mini" :bg-color="getColor(false)" :border-color="getColor(false)" color="#fff"/>
					-->
				</view>
				<text class="reference-value">最低{{heartLinesMin||0}}次/分钟，平均{{heartLinesAvg||0}}次/分钟，最高{{heartLinesMax||0}}次/分钟</text>
				<LineChart v-if="heartLines && heartLines.length" line-color="#20CD8A" :datas="heartLines" title="心率" :markLines="[ 100, 60, 20 ]" :max="130"></LineChart>
				<!-- 240415 去掉心律呼吸异常
				<view class="flex-col abnormal-table">
					<u-row gutter="16" class="abnormal-table-row">
						<u-col span="4">
							<text class="text">异常事件</text>
						</u-col>
						<u-col span="4">
							<text class="text">次数</text>
						</u-col>
						<u-col span="4">
							<text class="text">平均时长</text>
						</u-col>
					</u-row>
					<u-line color="#999" />
					<u-row gutter="16" class="abnormal-table-row">
						<u-col span="4">
							<text class="text">心率过缓</text>
						</u-col>
						<u-col span="4">
							<text class="text">{{ info.heartSlowCount || 0 }}</text>
						</u-col>
						<u-col span="4">
							<text class="text">{{ info.heartSlowAvgDuration ? getSFM(info.heartSlowAvgDuration, 'H时i分s秒').replace('00时', '').replace('00分', '') : '0秒' }}</text>
						</u-col>
					</u-row>
					<u-line color="#999" />
					<u-row gutter="16" class="abnormal-table-row">
						<u-col span="4">
							<text class="text">心率过速</text>
						</u-col>
						<u-col span="4">
							<text class="text">{{ info.heartFastCount || 0 }}</text>
						</u-col>
						<u-col span="4">
							<text class="text">{{ info.heartFastAvgDuration ? getSFM(info.heartFastAvgDuration, 'H时i分s秒').replace('00时', '').replace('00分', '') : '0秒' }}</text>
						</u-col>
					</u-row>
				</view>
				
				<view v-if="(heartSlowTimes && heartSlowTimes.length) || (heartFastTimes && heartFastTimes.length)" class="heart-chart-title">心率异常事件</view>
				<view v-if="heartSlowTimes && heartSlowTimes.length" style="position: relative; padding-top: 30rpx;">
					<view class="ta-r">共{{ info.heartSlowCount || 0 }}次</view>
					<view style="position: absolute; left: 20rpx; top: 36%;">心率<br/>过缓</view>
					<ExceptionChart :times="heartSlowTimes" leftOffset="60"></ExceptionChart>
				</view>
				<view v-if="heartFastTimes && heartFastTimes.length" style="position: relative; padding-top: 30rpx;">
					<view class="ta-r">共{{ info.heartFastCount || 0 }}次</view>
					<view style="position: absolute; left: 20rpx; top: 36%;">心率<br/>过速</view>
					<ExceptionChart :times="heartFastTimes" leftOffset="60"></ExceptionChart>
				</view>
				240415 去掉心律呼吸异常
				-->
				
				<!-- <view class="flex-col heart-card-bottom"> -->
				<view class="flex-col gray-card">
					<text class="text">当次睡眠基准心率<text :style="{color: getColor(info.benchmarkHeartRate>55&&info.benchmarkHeartRate<65)}">{{info.benchmarkHeartRate ||0}}次/分</text></text>
					<!-- <text class="ref-val">参考值：55~65次/分</text> -->
					<view v-if="info.benchmarkHeartRate<55" class="flex-col item-warn">
						<view class="flex-row">
							<text class="icon iconfont icon-caozuorizhi warn-title"></text>
							<text class="warn-title">建议信息</text>
						</view>
						<text class="warn-advise">
							如果您正在服用药物，建议咨询医生是否需要调整剂量或更换药物；心率过低且伴有其他症状，如头晕、气短等，建议及时就医，以排除心脏疾病的可能。
						</text>
					</view>
					<view v-if="info.benchmarkHeartRate>65" class="flex-col item-warn">
						<view class="flex-row">
							<text class="icon iconfont icon-caozuorizhi warn-title"></text>
							<text class="warn-title">建议信息</text>
						</view>
						<text class="warn-advise">
							减少咖啡因、尼古丁和酒精的摄入，心率过高且伴有其他症状，如心悸、气短等，建议及时就医，以排除心脏疾病的可能。
						</text>
					</view>
				</view>
				
				<view>
					<view class="heart-chart-title">近两周心率数据对比</view>
					<view style="margin-top: 20rpx;"></view>
					<HbLineChart :legendData="heartLegendData14days" :xAxisDatas="xAxisDatas14days"  :seriesDatas="heartLines14days" />
					<view class="flex-col item-warn">
						<view class="flex-row">
							<text class="icon iconfont icon-caozuorizhi warn-title"></text>
							<text class="warn-title">近两周心率数据对比结果</text>
						</view>
						<text class="warn-advise">
							考虑个人的年龄、身体状况和运动水平等因素，正常心率范围会有所不同，如出现呼吸困难、疲劳、胸痛等症状，请及时就医。
						</text>
					</view>
				</view>
			</view>
			
			<!-- 呼吸 -->
			<view class="card breathe-card flex-col">
				<view class="flex-row items-center">
					<text class="card-icon iconfont icon-huxijiance"></text>
					<text class="care-title">呼吸</text>	
					<!-- 2024-04-23隐藏
					<u-tag v-if="info.AHI<=5" text="正常" size="mini" :bg-color="getColor(true)" :border-color="getColor(true)" color="#fff"/>
					<u-tag v-else-if="info.AHI>5" text="偏高" size="mini" :bg-color="getColor(false)" :border-color="getColor(false)" color="#fff"/>
					-->
				</view>
				<text class="reference-value">最低{{breathLinesMin||0}}次/分钟，平均{{breathLinesAvg||0}}次/分钟，最高{{breathLinesMax||0}}次/分钟</text>
				<LineChart v-if="breathLines && breathLines.length"  line-color="#20CD8A" :datas="breathLines" title="呼吸" :markLines="[ 20, 12, 1 ]" :max="28"></LineChart>
				<!-- 240415 去掉心律呼吸异常
				<view class="abnormal-table flex-col">
					<u-row gutter="16" class="abnormal-table-row">
						<u-col span="4">
							<text class="text">异常事件</text>
						</u-col>
						<u-col span="4">
							<text class="text">次数</text>
						</u-col>
						<u-col span="4">
							<text class="text">平均时长</text>
						</u-col>
					</u-row>
					<u-line color="#999" /> 
					240415 去掉心律呼吸异常
					-->
					<!-- <u-row gutter="16" class="abnormal-table-row">
						<u-col span="4">
							<text class="text">呼吸暂停</text>
						</u-col>
						<u-col span="4">
							<text class="text">{{ info.breathSlowCount || 0 }}</text>
						</u-col>
						<u-col span="4">
							<text class="text">{{ info.breathSlowAvgDuration ? getSFM(info.breathSlowAvgDuration, 'H时i分s秒').replace('00时', '').replace('00分', '') : '0秒' }}</text>
						</u-col>
					</u-row>
					<u-line color="#999" /> -->
					<!-- 240415 去掉心律呼吸异常
					<u-row gutter="16" class="abnormal-table-row">
						<u-col span="4">
							<text class="text">呼吸过缓</text>
						</u-col>
						<u-col span="4">
							<text class="text">{{ info.breathSlowCount || 0 }}</text>
						</u-col>
						<u-col span="4">
							<text class="text">{{ info.breathSlowAvgDuration ? getSFM(info.breathSlowAvgDuration, 'H时i分s秒').replace('00时', '').replace('00分', '') : '0秒' }}</text>
						</u-col>
					</u-row>
					<u-line color="#999" />
					<u-row gutter="16" class="abnormal-table-row">
						<u-col span="4">
							<text class="text">呼吸过速</text>
						</u-col>
						<u-col span="4">
							<text class="text">{{ info.breathFastCount || 0 }}</text>
						</u-col>
						<u-col span="4">
							<text class="text">{{ info.breathFastAvgDuration ? getSFM(info.breathFastAvgDuration, 'H时i分s秒').replace('00时', '').replace('00分', '') : '0秒' }}</text>
						</u-col>
					</u-row> 
					240415 去掉心律呼吸异常
					-->
				</view>
				
				<!-- 240415 去掉心律呼吸异常
				<view v-if="(breathSlowTimes && breathSlowTimes.length) || (breathFastTimes && breathFastTimes.length)" class="heart-chart-title">呼吸异常事件</view>
				<view v-if="breathSlowTimes && breathSlowTimes.length" style="position: relative; padding-top: 30rpx;">
					<view class="ta-r">共{{ info.breathSlowCount || 0 }}次</view>
					<view style="position: absolute; left: 20rpx; top: 36%;">呼吸<br/>过缓</view>
					<ExceptionChart :times="breathSlowTimes" leftOffset="60"></ExceptionChart>
				</view>
				
				<view v-if="breathFastTimes && breathFastTimes.length" style="position: relative; padding-top: 30rpx;">
					<view class="ta-r">共{{ info.breathFastCount || 0 }}次</view>
					<view style="position: absolute; left: 20rpx; top: 36%;">呼吸<br/>过速</view>
					<ExceptionChart :times="breathFastTimes" leftOffset="60"></ExceptionChart>
				</view>
				240415 去掉心律呼吸异常
				-->
				<!-- <view class="flex-col gray-card">
					<text class="title">AHI指数<text :style="{color: getColor(info.AIH<=5)}">{{info.AIH}}次</text></text>
					<text class="descr">参考值：＜5</text>
					<view v-if="info.AIH>5" class="flex-col item-warn">
						<view class="flex-row">
							<text class="icon iconfont icon-caozuorizhi warn-title"></text>
							<text class="warn-title">AHI值过高</text>
						</view>
						<text class="warn-advise">
							AHI值在5-15次/小时之间则被认为是轻度睡眠呼吸暂停；AHI值在15-30次/小时之间则被认为是中度睡眠呼吸暂停；而AHI值高于30次/小时则被认为是严重睡眠呼吸暂停
						</text>
					</view>
				</view> -->
				<view>
					<view class="heart-chart-title">近两周呼吸数据对比</view>
					<view style="margin-top: 20rpx;"></view>
					<HbLineChart :legendData="breathLegendData14days" :xAxisDatas="xAxisDatas14days"  :seriesDatas="breathLines14days" />
					<view class="flex-col item-warn">
						<view class="flex-row">
							<text class="icon iconfont icon-caozuorizhi warn-title"></text>
							<text class="warn-title">近两周呼吸数据对比结果</text>
						</view>
						<text class="warn-advise">
							在安静状态下，正常成人的呼吸频率为12~20次/分，如出现呼吸困难、持续的呼吸增快或减缓、胸痛头晕等症状，请及时就医。
						</text>
					</view>
				</view>
			</view>
		</view>
		
		
		
		
		<!-- 老的
		<view>
			<view class="item-block event-info">
				<view class="icon-title">事件统计</view>
				<WaterPoloChart v-if="info.eventCount || info.eventCount === 0" ref="waterPoloChart" :num="info.eventCount || 0" title="当日告警数"></WaterPoloChart>
				<view style="width: 300rpx; margin: 0 auto;">
					<u-row gutter="0">
						<u-col span="6">
							<view style="text-align: center; border-right: 2rpx solid #888;">
								<text class="event-name">跌倒</text>
								<text class="event-num">{{ info.fallEventCount || 0 }}</text>
							</view>
						</u-col>
						<u-col span="6">
							<view style="text-align: center;">
								<text class="event-name">久滞</text>
								<text class="event-num">{{ info.longlagEventCount || 0 }}</text>
							</view>
						</u-col>
					</u-row>
				</view>
				<view class="r-flex" style="margin-top: 30rpx;">
					<text class="icon iconfont icon-baogaolimiandetubiao" style="width: 130rpx; font-size: 90rpx; color: #f89267;"></text>
					<view class="r-flex-1 tip-text">{{ info.eventStaResult || '' }}</view>
				</view>
			</view>
			
			<view style="height: 20rpx; background: #F7F7F7;"></view>
			
			<view class="item-block heart-rate-info">
				<view class="icon-title">心率</view>
				<view style="color: #000; font-size: 24rpx; margin-top: 20rpx;">24小时心率监测数据</view>
				<view style="color: #888; font-size: 24rpx; margin-top: 10rpx;">最低{{ info.minHeart || '-' }}次/分钟 最高{{ info.maxHeart || '-' }}次/分钟</view>
				<LineChart v-if="heartLines && heartLines.length" line-color="#4D7BFF" :datas="heartLines" title="心率" :markLines="[ 100, 60, 20 ]" :max="130"></LineChart>
				<view class="table">
					<u-row gutter="16">
						<u-col span="4">
							<view style="text-align: center;">异常事件</view>
						</u-col>
						<u-col span="4">
							<view style="text-align: center;">次数</view>
						</u-col>
						<u-col span="4">
							<view style="text-align: center;">平均时长</view>
						</u-col>
					</u-row>
					<u-line color="#999" />
					<u-row gutter="16">
						<u-col span="4">
							<view style="text-align: center;">心率过缓</view>
						</u-col>
						<u-col span="4">
							<view style="text-align: center;">{{ info.heartSlowCount || 0 }}</view>
						</u-col>
						<u-col span="4">
							<view style="text-align: center;">{{ info.heartSlowAvgDuration ? getSFM(info.heartSlowAvgDuration, 'H时i分s秒').replace('00时', '').replace('00分', '') : '0秒' }}</view>
						</u-col>
					</u-row>
					<u-line color="#999" />
					<u-row gutter="16">
						<u-col span="4">
							<view style="text-align: center;">心率过速</view>
						</u-col>
						<u-col span="4">
							<view style="text-align: center;">{{ info.heartFastCount || 0 }}</view>
						</u-col>
						<u-col span="4">
							<view style="text-align: center;">{{ info.heartFastAvgDuration ? getSFM(info.heartFastAvgDuration, 'H时i分s秒').replace('00时', '').replace('00分', '') : '0秒' }}</view>
						</u-col>
					</u-row>
				</view>
				<view v-if="((info.heartSlowCount || 0) + (info.heartFastCount || 0)) <= 0" class="careful-text">风险评估：无风险</view>
				<view v-else-if="((info.heartSlowCount || 0) + (info.heartFastCount || 0)) >= 1 && ((info.heartSlowCount || 0) + (info.heartFastCount || 0)) <= 3" class="careful-text">风险评估：疑似低风险</view>
				<view v-else class="careful-text">风险评估：疑似有风险，请持续关注！</view>
				
				<view v-if="(heartSlowTimes && heartSlowTimes.length) || (heartFastTimes && heartFastTimes.length)" style="margin-top: 30rpx;">心率异常事件</view>
				<view v-if="heartSlowTimes && heartSlowTimes.length" style="position: relative; padding-top: 30rpx;">
					<view class="ta-r">共{{ info.heartSlowCount || 0 }}次</view>
					<view style="position: absolute; left: 20rpx; top: 36%;">心率<br/>过缓</view>
					<ExceptionChart :times="heartSlowTimes" :leftOffset="60"></ExceptionChart>
				</view>
				<view v-if="heartFastTimes && heartFastTimes.length" style="position: relative; padding-top: 30rpx;">
					<view class="ta-r">共{{ info.heartFastCount || 0 }}次</view>
					<view style="position: absolute; left: 20rpx; top: 36%;">心率<br/>过速</view>
					<ExceptionChart :times="heartFastTimes" :leftOffset="60"></ExceptionChart>
				</view>
				<view v-if="info.heartStaResult" class="r-flex" style="margin-top: 30rpx;">
					<text class="icon iconfont icon-baogaolimiandetubiao" style="width: 130rpx; font-size: 90rpx; color: #f89267;"></text>
					<view class="r-flex-1 tip-text">{{ info.heartStaResult || '' }}</view>
				</view>
				
			</view>
			
			<view style="height: 20rpx; background: #F7F7F7;"></view>
			
			<view class="item-block heart-rate-info">
				<view class="icon-title">呼吸</view>
				<view style="color: #000; font-size: 24rpx; margin-top: 20rpx;">24小时心率监测数据</view>
				<view style="color: #888; font-size: 24rpx; margin-top: 10rpx;">最低{{ info.minBreath || 0 }}次/分钟 最高{{ info.maxBreath || 0 }}次/分钟</view>
				<LineChart v-if="breathLines && breathLines.length"  line-color="#01B09A" :datas="breathLines" title="呼吸" :markLines="[ 20, 12, 1 ]" :max="28"></LineChart>
				<view class="table">
					<u-row gutter="16">
						<u-col span="4">
							<view style="text-align: center;">异常事件</view>
						</u-col>
						<u-col span="4">
							<view style="text-align: center;">次数</view>
						</u-col>
						<u-col span="4">
							<view style="text-align: center;">平均时长</view>
						</u-col>
					</u-row>
					<u-row gutter="16">
						<u-col span="4">
							<view style="text-align: center;">呼吸过缓</view>
						</u-col>
						<u-col span="4">
							<view style="text-align: center;">{{ info.breathSlowCount || 0 }}</view>
						</u-col>
						<u-col span="4">
							<view style="text-align: center;">{{ info.breathSlowAvgDuration ? getSFM(info.breathSlowAvgDuration, 'H时i分s秒').replace('00时', '').replace('00分', '') : '0秒' }}</view>
						</u-col>
					</u-row>
					<u-row gutter="16">
						<u-col span="4">
							<view style="text-align: center;">呼吸过速</view>
						</u-col>
						<u-col span="4">
							<view style="text-align: center;">{{ info.breathFastCount || 0 }}</view>
						</u-col>
						<u-col span="4">
							<view style="text-align: center;">{{ info.breathFastAvgDuration ? getSFM(info.breathFastAvgDuration, 'H时i分s秒').replace('00时', '').replace('00分', '') : '0秒' }}</view>
						</u-col>
					</u-row>
				</view>
				<view v-if="((info.breathSlowCount || 0) + (info.breathFastCount || 0)) <= 0" class="careful-text">风险评估：无风险</view>
				<view v-else-if="((info.breathSlowCount || 0) + (info.breathFastCount || 0)) >= 1 && ((info.breathSlowCount || 0) + (info.breathFastCount || 0)) <= 3" class="careful-text">风险评估：疑似低风险</view>
				<view v-else class="careful-text">风险评估：疑似有风险，请持续关注！</view>
				
				<view v-if="(breathSlowTimes && breathSlowTimes.length) || (breathFastTimes && breathFastTimes.length)" style="margin-top: 30rpx;">呼吸异常事件</view>
				<view v-if="breathSlowTimes && breathSlowTimes.length" style="position: relative; padding-top: 30rpx;">
					<view class="ta-r">共{{ info.breathSlowCount || 0 }}次</view>
					<view style="position: absolute; left: 20rpx; top: 36%;">呼吸<br/>过缓</view>
					<ExceptionChart :times="breathSlowTimes" :leftOffset="60"></ExceptionChart>
				</view>
				
				<view v-if="breathFastTimes && breathFastTimes.length" style="position: relative; padding-top: 30rpx;">
					<view class="ta-r">共{{ info.breathFastCount || 0 }}次</view>
					<view style="position: absolute; left: 20rpx; top: 36%;">呼吸<br/>过速</view>
					<ExceptionChart :times="breathFastTimes" :leftOffset="60"></ExceptionChart>
				</view>
				<view v-if="info.breathStaResult" class="r-flex" style="margin-top: 30rpx;">
					<text class="icon iconfont icon-baogaolimiandetubiao" style="width: 130rpx; font-size: 90rpx; color: #f89267;"></text>
					<view class="r-flex-1 tip-text">{{ info.breathStaResult || '' }}</view>
				</view>
				
			</view>
			
			<view style="height: 20rpx; background: #F7F7F7;"></view>
			
			<view class="item-block heart-rate-info">
				<view class="icon-title">在床统计</view>
				<view style="color: #000; font-size: 24rpx; margin-top: 20rpx;">夜间在床情况分析(19:00-6:00)</view>
				<view class="r-flex subsection">
					<view class="r-flex-1 subsection-left" :class="{ 'actived-red': info.nightInbedDurationLevel === 1 }">
						<view class="chart-item"></view>
						<view class="chart-text ta-c">
							<view>较短</view>
							<view class="desc">{{ lt }}7 小时</view>
						</view>
					</view>
					<view class="r-flex-1 subsection-center" :class="{ 'actived-green': info.nightInbedDurationLevel === 2 }">
						<view class="chart-item"></view>
						<view class="chart-text ta-c">
							<view>正常</view>
							<view class="desc">{{ gt }}7 小时</view>
						</view>
					</view>
					<view class="r-flex-1 subsection-right" :class="{ 'actived-red': info.nightInbedDurationLevel === 3 }">
						<view class="chart-item"></view>
						<view class="chart-text ta-c">
							<view>较长</view>
							<view class="desc">{{ gt }}10 小时</view>
						</view>
					</view>
				</view>
				
				<view class="bed-stat">
					<view class="r-flex item">
						<view class="r-flex-1">
							<view class="title">夜间离床次数</view>
							<view class="desc">建议0-2次</view>
						</view>
						<view class="r-flex-1 ta-r">
							<text class="supplement">{{ info.nightOutbedCount || 0 }}次</text>
							<view style="width: 120rpx; display: inline-block;">
								<view v-if="info.nightOutbedCountLevel === 2" class="tag leve-tag-green">正常</view>
								<view v-else-if="info.nightOutbedCountLevel === 3" class="tag leve-tag-orange">需关注</view>
							</view>
						</view>
					</view>
					<view class="r-flex item">
						<view class="r-flex-1">
							<view class="title">夜间最早在床时间</view>
							<view class="desc">建议22点前</view>
						</view>
						<view class="r-flex-1 ta-r">
							<text class="supplement">{{ info.nightInbedEarliestTime || '' }}</text>
							<view style="width: 120rpx; display: inline-block;">
								<view v-if="info.nightInbedEarliestTimeLevel === 2" class="tag leve-tag-green">正常</view>
								<view v-else-if="info.nightInbedEarliestTimeLevel === 3" class="tag leve-tag-orange">较晚</view>
							</view>
						</view>
					</view>
					<view class="r-flex item">
						<view class="r-flex-1">
							<view class="title">白天最早离床时间</view>
							<view class="desc">建议8点前</view>
						</view>
						<view class="r-flex-1 ta-r">
							<text class="supplement">{{ info.dayOutBedEarliestTime || '' }}</text>
							<view style="width: 120rpx; display: inline-block;">
								<view v-if="info.dayOutBedEarliestTimeLevel === 2" class="tag leve-tag-green">正常</view>
								<view v-else-if="info.dayOutBedEarliestTimeLevel === 3" class="tag leve-tag-orange">需关注</view>
							</view>
						</view>
					</view>
				</view>
				
				<view v-if="info.nightOutbedMomentList && info.nightOutbedMomentList.length">离床分析</view>
				<view v-if="info.nightOutbedMomentList && info.nightOutbedMomentList.length" style="position: relative; padding-top: 30rpx;">
					<view class="ta-r">共{{ info.nightOutbedCount || 0 }}次</view>
					<view style="position: absolute; left: 20rpx; top: 36%; text-align: center;">夜间<br/>(19:00 - 06:00)</view>
					<ExceptionChart :times="info.nightOutbedMomentList"></ExceptionChart>
				</view>
				<view class="r-flex" style="margin-top: 30rpx;">
					<text class="icon iconfont icon-baogaolimiandetubiao" style="width: 130rpx; font-size: 90rpx; color: #f89267;"></text>
					<view class="r-flex-1 tip-text">{{ info.inbedStaResult || '' }}</view>
				</view>
				 -->
			</view>
		</view>
	</view>
</template>

<script>
	import LineChart from '@/components/line-chart/index.vue';
	import ExceptionChart from './exception-chart.vue';
	import HbLineChart from './hb-line-chart.vue';
	import BarChat from './bar-chart.vue';
	import RectChart from '@/components/rect-chart/index.vue';
	import LineDivider from '@/components/line-divider/index.vue'
	import * as util from '@/utils/util'
	export default {
		components: {
			LineChart, ExceptionChart,LineDivider,RectChart,BarChat,HbLineChart
		},
		props: {
			info: {
				type: Object,
				required: true,
				sleepMetrics:{},
			}
		},
		data() {
			return {
				lt: '<',
				gt: '>',
				heartLines: [],
				breathLines: [],
				heartSlowTimes: [],
				heartFastTimes: [],
				breathSlowTimes: [],
				breathFastTimes: [],
				heartLinesMax:0,
				heartLinesMin:0,
				heartLinesAvg:0,
				
				breathLinesMax:0,
				breathLinesMin:0,
				breathLinesAvg:0,
				
				xAxisDatas14days:[],
				breathLines14days:[],
				heartLines14days:[],
				heartLegendData14days:["最低心率","最高心率","基准心率"],
				breathLegendData14days:["最低呼吸","最高呼吸"],
				
			}
		},
		watch: {
			'info': {
				deep: true,
				immediate: true,
				handler(val, oldVal) {
					let _heartLines = (val.heartVOList || []).map(m => {
						return [ m.dateTime, m.heart || 0 ]
					});
					
					let _breathLines = (val.breathVOList || []).map(m => {
						return [ m.dateTime, m.breath || 0 ]
					});
					this.heartSlowTimes = val.heartSlowMomentList;
					this.heartFastTimes = val.heartFastMomentList;
					this.breathSlowTimes = val.breathSlowMomentList;
					this.breathFastTimes = val.breathFastMomentList;
					
					this.heartLines = _heartLines;
					this.breathLines = _breathLines;
					
					const heart = this.calculateStats(this.heartLines,1);
					this.heartLinesAvg = heart.average||0;
					this.heartLinesMax = heart.max||0;
					this.heartLinesMin = heart.min||0;
					
					const breath = this.calculateStats(this.breathLines,1);
					this.breathLinesAvg = breath.average||0;
					this.breathLinesMax = breath.max||0;
					this.breathLinesMin = breath.min||0;
					
					this.xAxisDatas14days = (val.sleepMetrics.heartBreathList|| []).map(m => {
						return [ m.reportDate.substring(5)]
					});
					
					const benchmarkHeartRateArr = (val.sleepMetrics.heartBreathList|| []).map(m => {
						return m.benchmarkHeartRate || 0
					});
					
					const maxHeartArr = (val.sleepMetrics.heartBreathList|| []).map(m => {
						return m.maxHeart || 0 
					});
					
					const minHeartArr = (val.sleepMetrics.heartBreathList|| []).map(m => {
						return m.minHeart || 0
					});
					
					const maxBreathArr = (val.sleepMetrics.heartBreathList|| []).map(m => {
						return m.maxBreath || 0 
					});
					
					const minBreathArr = (val.sleepMetrics.heartBreathList || []).map(m => {
						return m.minBreath || 0
					});
					
					this.heartLines14days=[{
						name: this.heartLegendData14days[0],
						type: 'line',
						data: minHeartArr
					},{
						name: this.heartLegendData14days[1],
						type: 'line',
						data: maxHeartArr
					},{
						name: this.heartLegendData14days[2],
						type: 'line',
						data: benchmarkHeartRateArr
					}];
					this.breathLines14days=[{
						name: this.breathLegendData14days[0],
						type: 'line',
						data: minBreathArr
					},{
						name: this.breathLegendData14days[1],
						type: 'line',
						data: maxBreathArr
					}];
				}
			}
		},
		methods: {
			getSFM: util.getSFM,
			formatSecond2String:util.formatSecond2String,
			convertToMonthDayFormat:util.convertToMonthDayFormat,
			roundToDecimal:util.roundToDecimal,
			getPreviousDay:util.getPreviousDay,
			calculateStats:util.calculateStats,
			getColor(flag){
				return flag?'#20CD8A':'#FF7D17';
			},
			getHHMM(timeStr){
				if(!timeStr){
					return timeStr;
				}
				return timeStr.substring(11,16)
			},
			showSleepWarnText(){
				const sleepTime = new Date(this.info.sleepMetrics.sleepTime);
				const pre23 = new Date(this.getPreviousDay(this.info.reportDate)+' 23:00:00');
				console.log("sleepTime:"+sleepTime)
				console.log("pre23:"+pre23)
				return sleepTime > pre23
			},
		}
	}
</script>

<style lang="scss">
@import url("/static/css/iconfont.css");
.container{
	// padding: 24rpx;
	.card{
		padding: 24rpx;
		margin-top: 24rpx;
		min-height: 100rpx;
		background-color: white;
		border-radius: 12rpx;
		.card-icon{
			color: #01B09A;
			font-size: 48rpx;
		}
		.care-title{
			margin:0 8rpx;
			font-size: 36rpx;
			color: #3D3D3D;
			line-height: 54rpx
		}
		.care-tag-normal{
			margin-left: 8rpx;
			width: 64rpx;
			height: 32rpx;
			background: #20CD8A;
			color: white;
			border-radius: 8rpx;
			opacity: 1;
		}
		.care-tag-abnormal{
			margin-left: 8rpx;
			width: 64rpx;
			height: 32rpx;
			background: #FF7D17;
			color: white;
			border-radius: 8rpx;
			opacity: 1;
		}
		.reference-value{
			font-size: 26rpx;
			line-height: 40rpx;
			color: #999999;
		}
		
		.abnormal-table{
			padding: 24rpx;
			min-height: 100rpx;
			background: #F5F7FB;
			border-radius: 16rpx;
			opacity: 1;
			.abnormal-table-row{
				padding: 8rpx;
				height: 68rpx;
				text-align: center;
			}
			.text{
				height: 40rpx;
				font-size: 26rpx;
				color: #666666;
			}
		}
	}
	.sleep-card{
		height: 200rpx;
		.sleep-score{
			width: 110rpx;
			.sleep-score-num{
				font-size: 54rpx;
				color: #333333;
			}
			.sleep-score-unit{
				font-size: 22rpx;
				font-weight: 400;
				color: #333333;
			}
			.sleep-tag{
				width: 64rpx;
				height: 32rpx;
				background: #01B09A;
				border-radius: 8rpx 8rpx 8rpx 8rpx;
				opacity: 1;
				.sleep-tag-text{
					font-size: 11px;
					color: #FFFFFF;
				}
			}
		}
		.sleep-data{
			.sleep-data-item{
				.icon{
					color: #01B09A;
					font-size: 48rpx;
				}
				.text{
					font-size: 26rpx;
					color: #333333;
					// width: 120rpx;
				}
				.time{
					width: 100rpx;
					margin-left: 8rpx;
					color:#01B09A;
				}
			}
			.sleep-warn{
				margin-top: -16rpx;
				.sleep-warn-text{
					width: 186rpx;
					font-size: 22rpx;
					color: #FF7D17;
					line-height: 32rpx;
					padding:0 8rpx;
					background: #FFDDC2;
					border-radius: 12rpx;
					opacity: 1;
				} 
				.warn-left{
					margin-left: 72rpx;
				}
				.warn-right{
					margin-left: 32rpx;
				}
			}
		}
	}
	.sleep-cycle-card{
		.sleep-chart-bottom{
			padding: 0 24rpx;
			.time{
				font-size: 11px;
				color: #999999;
			}
			.data{
				font-size: 11px;
				color: #666666;
			}
		}
		.sleep-time-count{
			padding: 24rpx 24rpx;
			.title{
				font-size: 11px;
				color: #666666;
				&::before {
					content: ' ';
					display: inline-block;
					position: relative;
					top: 2rpx;
					width: 8rpx;
					height: 24rpx;
					margin-right: 10rpx;
					border-radius: 10rpx;
					// background: #01B09A;
				}
			}
			.sleep-time-item{
				min-width: 160rpx;
			}
			.color1::before{
				background: #FF7D17;
			}
			.color2::before{
				background: #45B3FF;
			}
			.color3::before{
				background: #20CD8A;
			}
			.color4::before{
				background: #01B09A;
			}
			.total-time{
				font-size: 13px;
				color: #333333;
			}
		}
		.sleep-cycle-item{
			min-height: 128rpx;
			padding: 24rpx 0;
			border-radius: 16rpx;
			opacity: 1;
			.title{
				font-size: 32rpx;
				color: #333333;
			}
			.descr{
				margin-top: 12rpx;
				font-size: 26rpx;
				color: #999999;
			}
		}
		
	}
	.item-warn{
		margin-top: 24rpx;
		padding: 24rpx;
		background: #F2F2F2;
		border-radius: 16rpx;
		opacity: 1;
		.warn-title{
			margin-left: 8rpx;
			font-size: 26rpx;
			color: #333333;
		}
		
		.warn-advise{
			margin-top: 8rpx;
			font-size: 26rpx;
			color: #666666;
		}
	}
	.body-move-card{
		
	}
	.gray-card{
		min-height: 128rpx;
		padding: 24rpx;
		margin-top: 24rpx;
		background: #F5F7FB;
		border-radius: 16rpx;
		opacity: 1;
		.title{
			font-size: 32rpx;
			color: #333333;
		}
		.descr{
			margin-top: 12rpx;
			font-size: 26rpx;
			color: #999999;
		}
	}
	.abnormal-table{
		min-height: 100rpx;
		background: #F5F7FB;
		border-radius: 16rpx;
		opacity: 1;
	}
	
	.heart-card{
		.heart-card-bottom{
			padding: 24rpx;
			.text{
				height: 48rpx;
				font-size: 32rpx;
				color: #333333;
				line-height: 48rpx;
			}
			.ref-val{
				height: 40rpx;
				font-size: 26rpx;
				color: #999999;
				line-height: 40rpx;
			}
		}
	}
	
	.heart-chart-title{
		margin-top: 24rpx;
		height: 48rpx;
		font-size: 32rpx;
		color: #333333;
		line-height: 48rpx;
	}
	.breathe-card{
		
	}
}


.item-block {
	padding: 32rpx;
	.icon-title {
		font-size: 28rpx;
		font-weight: bold;
		&::before {
			content: ' ';
			display: inline-block;
			position: relative;
			top: 2rpx;
			width: 8rpx;
			height: 24rpx;
			margin-right: 10rpx;
			border-radius: 10rpx;
			background: #01B09A;
		}
	}
	.tip-text {
		font-size: 32rpx;
		color: #FF874D;
	}
}
.event-info {
	.event-name {
		font-size: 24rpx;
		color: #888;
	}
	.event-num {
		font-size: 24rpx;
		color: #000;
		margin-left: 10rpx;
	}
}
.heart-rate-info {
	.table {
		margin-top: 20rpx;
	}
	u-row {
		display: block;
		margin-bottom: 10rpx;
		padding: 10rpx 0rpx;
		&:nth-child(odd) {
			background: #f7f7f7;
		}
	}
	//#ifndef MP-WEIXIN
	.u-row {
		line-height: 40px;
		&:nth-child(odd) {
			background: #f7f7f7;
		}
	}
	//#endif
	.careful-text {
		// color: #FF874D;
		color: #84B3B7;
		font-size: 24rpx;
		border-top: 2rpx solid #ECECEC;
		border-bottom: 2rpx solid #ECECEC;
		padding: 20rpx 0rpx;
		margin-top: 20rpx;
	}
}

.subsection {
	margin-top: 30rpx;
	.chart-item {
		display: inline-block;
		height: 20rpx;
		width: 100%;
		background: #AAAAAA;
	}
	.chart-text {
		margin-top: 16rpx;
		color: #888;
		font-size: 20rpx;
		.desc {
			margin-top: 10rpx;
		}
	}
	&-left {
		padding-right: 8rpx;
		.chart-item {
			border-top-left-radius: 10rpx;
			border-bottom-left-radius: 10rpx;
		}
	}
	&-center {
		padding-left: 8rpx;
		padding-right: 8rpx;
	}
	&-right {
		padding-left: 8rpx;
		.chart-item {
			border-top-right-radius: 10rpx;
			border-bottom-right-radius: 10rpx;
		}
	}
	.actived-red {
		.chart-item {
			background: #01B09A;
		}
	}
	.actived-green {
		.chart-item {
			background: #ff5c5c;
		}
	}
}
	
.bed-stat {
	margin-top: 40rpx;
	margin-bottom: 40rpx;
	.item {
		margin-top: 30rpx;
		.title {
			font-size: 24rpx;
			color: #000;
		}
		.desc {
			margin-top: 10rpx;
			font-size: 20rpx;
			color: #888;
		}
		.supplement {
			margin-right: 20rpx;
			font-size: 24rpx;
			color: #000;
			vertical-align: middle;
		}
		.tag {
			display: inline-block;
			font-size: 20rpx;
			padding: 4rpx 26rpx;
			background: #01B09A;
			color: white;
			border-radius: 25rpx;
			vertical-align: middle;
		}
	}
}
</style>
