<template>
	<view>
		<!-- #ifdef MP-WEIXIN -->
		<uni-ec-canvas class="uni-ec-canvas" ref="lineCanvas" :id="optionId" canvas-id="badroom-lazy-load-chart"
			:ec="ec"></uni-ec-canvas>
		<!-- #endif -->
		<!-- #ifndef MP-WEIXIN -->
		<view :id="optionId" class="uni-ec-canvas"></view>
		<!-- #endif -->
	</view>
</template>

<script>
	import uniEcCanvas from '@/components/uni-ec-canvas/uni-ec-canvas.vue';
	import * as echarts from "@/components/uni-ec-canvas/echarts";
	let lineChart = null

	var data = [
		["2:00", 116],
		["3:00", 129],
		["4:00", 135],
		["5:00", 86],
		["6:00", 73],
		["7:00", 85],
		["8:00", 73],
		["9:00", 68],
		["10:00", 92],
		["11:00", 130],
		["12:00", 245],
		["13:00", 139],
		["14:00", 115],
		["15:00", 111],
		["16:00", 309],
		["17:00", 206],
		["18:00", 137],
		["19:00", 128],
		["20:00", 85],
		["21:00", 94],
		["22:00", 71],
		["23:00", 106]
	];
	const dateList = data.map(function(item) {
		return item[0];
	});
	const valueList = data.map(function(item) {
		return item[1];
	});
	export default {
		components: {
			uniEcCanvas
		},
		props: {
			lineColor: {
				type: String,
				default: '#01B09A'
			},
			datas: {
				type: Array,
				default: function() {
					return []
				}
			},
			markLines: {
				type: Array,
				default: function() {
					return [ 0, 0, 0 ]
				}
			},
			title: {
				type: String
			}
		},
		data() {
			return {
				optionId: undefined,
				ec: {
					lazyLoad: true
				},
				option: {
					tooltip: {
						show: true,
						trigger: 'axis',
						formatter: function(a) {
							var str = '';
							for (var h = 0; h < a.length; h++) {
								str += `${a[h].name}\r\n${a[h].seriesName}: ${a[h].value}`;
							}
							return str;
						},
						textStyle: {
							fontSize: 12,
						}
					},
					// dataZoom: [
						// {
						// 	show: true,
						// 	realtime: true,
						// 	start: 30,
						// 	end: 70,
						// 	xAxisIndex: [0, 1],
						// },
						// {
						// 	type: 'inside',
						// 	realtime: true,
						// 	start: 30,
						// 	end: 70,
						// 	xAxisIndex: [0, 1],
						// },
					// ],
					grid: {
						left: '8%',
						right: '10%',
						bottom: '0',
						top: '20',
						containLabel: true,
					},
					xAxis: [{
						data: dateList,
					 	show: true,
						axisLabel: {
							color: '#01B09A',
							textStyle: {
								fontSize: '13'
							},
						},
						axisTick: {
							show: true,
							lineStyle: {
								color: '#01B09A',
							}
						},
						axisLine: {
							lineStyle: {
								color: '#01B09A',
							}
						},
						splitLine: {
							show: false,
							lineStyle: {
								color: '#01B09A',
							}
						}
					}],
					yAxis: [{
						min: 0,
						axisLine: {
							show: false,
						},
						axisTick: {
							show: false,
						},
						splitLine: {
							show: false,
						},
						axisLabel: {
							show: false,
						},
					}],
					series: [{
						name: this.title,
						type: 'line',
						showSymbol: false,
						data: valueList,
						lineStyle: { width: 2, color: this.lineColor },
						markLine: {
							symbol: false,
							tooltip: {
								show: false,
							},
							data: [
								{
									yAxis: this.markLines[0], 
									name: '过速',
									lineStyle: {
										color: '#ffb0a3',
									},
									label: {
										normal: {
											formatter: '过速'
										}
									},
								},
								{
									yAxis: this.markLines[1], 
									name: '正常',
									lineStyle: {
										color: '#95d9f8',
									},
									label: {
										normal: {
											formatter: '正常'
										}
									},
								},
								{
									yAxis: this.markLines[2], 
									name: '过缓',
									lineStyle: {
										color: '#ffb0a3',
									},
									label: {
										normal: {
											formatter: '过缓'
										}
									},
								}
							]
						}
					}]
				}
			}
		},
		mounted() {
			// 设置随机数id
			let t = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
			let len = t.length
			let id = ''
			for (let i = 0; i < 32; i++) {
				id += t.charAt(Math.floor(Math.random() * len))
			}
			this.optionId = id
			
			let _titles = [];
			let _values = [];
			this.datas.map((item) => {
				_titles.push(item[0]);
				_values.push(item[1]);
			});
			console.log(_titles, _values)
			let _max = Math.max(..._values) || 0;
			this.option.xAxis[0].data = _titles || [];
			this.option.yAxis[0].max = _max > 0 ? _max + parseInt(_max * 0.1) : _max;
			this.option.series[0].data = _values || [];
			this.$nextTick(() => {
				// #ifdef MP-WEIXIN
				this.$refs.lineCanvas.init(this.initChart)
				// #endif
				// #ifndef MP-WEIXIN
				let el = document.getElementById(this.optionId);
				let myChart= echarts.init(el);
				myChart.setOption(this.option);
				// #endif
			})
		},
		beforeDestroy() {
			// #ifdef MP-WEIXIN
			lineChart.dispose();
			// #endif
			console.log('lineChart.dispose');
		},
		methods: {
			initChart(canvas, width, height, canvasDpr) {
				lineChart = echarts.init(canvas, null, {
					width: width,
					height: height,
					devicePixelRatio: canvasDpr
				})
				canvas.setChart(lineChart)
				lineChart.setOption(this.option)
				return lineChart
			}
		}
	}
</script>

<style>
	.uni-ec-canvas {
		width: 100%;
		height: 200rpx;
		display: block;
	}
</style>
