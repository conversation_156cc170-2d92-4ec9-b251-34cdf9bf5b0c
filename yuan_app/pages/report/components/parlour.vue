<template>
	<view>
		<!-- <view class="item-block event-info">
			<view class="icon-title">事件统计</view>
			<WaterPoloChart v-if="info.eventCount || info.eventCount === 0" ref="waterPoloChart" :num="info.eventCount || 0" title="当日告警数"></WaterPoloChart>
			<view style="width: 300rpx; margin: 0 auto;">
				<u-row gutter="0">
					<u-col span="6">
						<view style="text-align: center; border-right: 2rpx solid #888;">
							<text class="event-name">跌倒</text>
							<text class="event-num">{{ info.fallEventCount || 0 }}</text>
						</view>
					</u-col>
					<u-col span="6">
						<view style="text-align: center;">
							<text class="event-name">久滞</text>
							<text class="event-num">{{ info.longlagEventCount || 0 }}</text>
						</view>
					</u-col>
				</u-row>
			</view>
			<view v-if="info.eventStaResult" class="r-flex" style="margin-top: 30rpx;">
				<text class="icon iconfont icon-baogaolimiandetubiao" style="width: 130rpx; font-size: 90rpx; color: #f89267;"></text>
				<view class="r-flex-1 tip-text">{{ info.eventStaResult }}</view>
			</view>
		</view> -->
		
		<view style="height: 20rpx; background: #F7F7F7;"></view>
		
		<view class="item-block heart-rate-info">
			<view class="icon-title">活动分析</view>
			<view v-if="info.parlourWalkingDistance || info.parlourWalkingDistance === 0" style="color: #000; font-size: 24rpx; margin-top: 20rpx;">活动统计</view>
			<ReportPieChart v-if="display && (info.parlourWalkingDistance || info.parlourWalkingDistance === 0)" :num="info.parlourWalkingDistance" unit="米" desc="活动量"></ReportPieChart>
			
			<view style="color: #000; font-size: 24rpx; margin-top: 20rpx;">人员情况</view>
			<view style="flex: 1; border-bottom: 2rpx solid #ececec; padding-bottom: 30rpx; margin-top: 20rpx;" class="ta-c">
				<text class="icon iconfont icon-paozhuoderen" style="width: 90rpx; font-size: 90rpx; color: #01B09A; vertical-align: middle;"></text>
				<view class="title" style="display: inline-block;">同时最多活动人数
					<text style="font-size: 40rpx; margin: 0rpx 6rpx;">{{ info.parlourMaxPersonAmount || 0 }}</text>人
				</view>
			</view>
			
			<view style="color: #000; font-size: 24rpx; margin-top: 20rpx;">活动时间分析</view>
			<ReportTimeBarChart v-if="display" :num-obj="{ n1: info.parlourNoneDuration || 0, n2: info.parlourAlongDuration || 0, n3: info.parlourGroupDuration || 0 }"></ReportTimeBarChart>
			
			<view class="bed-stat">
				<view class="r-flex item">
					<view class="r-flex-1">
						<view class="title">活动时长</view>
						<view class="desc">建议不超过6小时</view>
					</view>
					<view class="r-flex-1 ta-r">
						<text class="supplement">{{ info.parlourActivityDuration ? getSFM(info.parlourActivityDuration * 60, 'H时i分') : '0分' }}</text>
						<view v-if="info.parlourActivityDurationLevel === 2" class="tag leve-tag-green">正常</view>
						<view v-if="info.parlourActivityDurationLevel === 3" class="tag leve-tag-red">需关注</view>
					</view>
				</view>
				<view class="r-flex item">
					<view class="r-flex-1">
						<view class="title">白天</view>
						<view class="desc">建议不超过5小时</view>
					</view>
					<view class="r-flex-1 ta-r">
						<text class="supplement">{{ info.parlourDayActivityDuration ? getSFM(info.parlourDayActivityDuration * 60, 'H时i分') : '0分' }}</text>
						<view v-if="info.parlourDayActivityDurationLevel === 2" class="tag leve-tag-green">正常</view>
						<view v-if="info.parlourDayActivityDurationLevel === 3" class="tag leve-tag-red">需关注</view>
					</view>
				</view>
				<view class="r-flex item">
					<view class="r-flex-1">
						<view class="title">夜间</view>
						<view class="desc">建议不超过3小时</view>
					</view>
					<view class="r-flex-1 ta-r">
						<text class="supplement">{{ info.parlourNightActivityDuration ? getSFM(info.parlourNightActivityDuration * 60, 'H时i分') : '0分' }}</text>
						<view v-if="info.parlourNightActivityDurationLevel === 2" class="tag leve-tag-green">正常</view>
						<view v-if="info.parlourNightActivityDurationLevel === 3" class="tag leve-tag-red">需关注</view>
					</view>
				</view>
				<view class="r-flex item">
					<view class="r-flex-1">
						<view class="title">最早活动时间</view>
						<view class="desc">建议早上6点后10点前</view>
					</view>
					<view class="r-flex-1 ta-r">
						<text class="supplement">{{ info.parlourActivityEarliestTime || '' }}</text>
						<view v-if="info.parlourActivityEarliestTimeLevel === 2" class="tag leve-tag-green">正常</view>
						<view v-if="info.parlourActivityEarliestTimeLevel === 3" class="tag leve-tag-red">较晚</view>
					</view>
				</view>
				<view class="r-flex item">
					<view class="r-flex-1">
						<view class="title">最晚离开时间</view>
						<view class="desc">建议晚上9点前</view>
					</view>
					<view class="r-flex-1 ta-r">
						<text class="supplement">{{ info.parlourLeaveLatestTime || '' }}</text>
						<view v-if="info.parlourLeaveLatestTimeLevel === 2" class="tag leve-tag-green">正常</view>
						<view v-if="info.parlourLeaveLatestTimeLevel === 3" class="tag leve-tag-red">较晚</view>
					</view>
				</view>
			</view>
			<view v-if="info.activityStaResult" class="r-flex" style="margin-top: 30rpx;">
				<text class="icon iconfont icon-baogaolimiandetubiao" style="width: 130rpx; font-size: 90rpx; color: #f89267;"></text>
				<view class="r-flex-1 tip-text">{{ info.activityStaResult || '' }}</view>
			</view>
		</view>
	</view>
</template>

<script>
	import LineChart from '@/components/line-chart/index.vue';
	import ReportPieChart from './pie-chart.vue';
	import ExceptionChart from './exception-chart.vue';
	import ReportTimeBarChart from './time-bar-chart.vue';
	import * as util from '@/utils/util'
	export default {
		components: {
			LineChart, ExceptionChart, ReportPieChart, ReportTimeBarChart
		},
		props: {
			info: {
				type: Object,
				required: true
			}
		},
		watch: {
			'info': function() {
				this.display = false;
				setTimeout(() => {
					this.display = true;
				}, 50)
			}
		},
		data() {
			return {
				display: true,
				lt: '<',
				gt: '>'
			}
		},
		methods: {
			getSFM: util.getSFM
		}
	}
</script>

<style lang="scss">
@import url("/static/css/iconfont.css");
.item-block {
	padding: 32rpx;
	background-color: white;
	.icon-title {
		font-size: 28rpx;
		font-weight: bold;
		&::before {
			content: ' ';
			display: inline-block;
			position: relative;
			top: 2rpx;
			width: 8rpx;
			height: 24rpx;
			margin-right: 10rpx;
			border-radius: 10rpx;
			background: #01B09A;
		}
	}
	.tip-text {
		font-size: 32rpx;
		color: #FF874D;
	}
}
.event-info {
	.event-name {
		font-size: 24rpx;
		color: #888;
	}
	.event-num {
		font-size: 24rpx;
		color: #000;
		margin-left: 10rpx;
	}
}
.heart-rate-info {
	.table {
		margin-top: 20rpx;
	}
	u-row {
		display: block;
		margin-bottom: 10rpx;
		padding: 10rpx 0rpx;
		&:nth-child(odd) {
			background: #f7f7f7;
		}
	}
	.careful-text {
		// color: #FF874D;
		color: #84B3B7;
		font-size: 24rpx;
		border-top: 2rpx solid #ECECEC;
		border-bottom: 2rpx solid #ECECEC;
		padding: 20rpx 32rpx;
		margin-top: 20rpx;
	}
}

.subsection {
	margin-top: 30rpx;
	.chart-item {
		display: inline-block;
		height: 20rpx;
		width: 100%;
		background: #AAAAAA;
	}
	.chart-text {
		margin-top: 16rpx;
		color: #888;
		font-size: 20rpx;
		.desc {
			margin-top: 10rpx;
		}
	}
	&-left {
		padding-right: 8rpx;
		.chart-item {
			border-top-left-radius: 10rpx;
			border-bottom-left-radius: 10rpx;
		}
	}
	&-center {
		padding-left: 8rpx;
		padding-right: 8rpx;
		.chart-item {
			background: #01B09A;
		}
	}
	&-right {
		padding-left: 8rpx;
		.chart-item {
			border-top-right-radius: 10rpx;
			border-bottom-right-radius: 10rpx;
		}
	}
}
	
.bed-stat {
	margin-top: 40rpx;
	margin-bottom: 40rpx;
	.item {
		margin-top: 30rpx;
		.title {
			font-size: 24rpx;
			color: #000;
		}
		.desc {
			margin-top: 10rpx;
			font-size: 20rpx;
			color: #888;
		}
		.supplement {
			margin-right: 20rpx;
			font-size: 24rpx;
			color: #000;
			vertical-align: middle;
		}
		.tag {
			display: inline-block;
			font-size: 20rpx;
			padding: 4rpx 30rpx;
			background: #01B09A;
			color: white;
			border-radius: 25rpx;
			vertical-align: middle;
		}
	}
}
</style>
