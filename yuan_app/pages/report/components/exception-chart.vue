<template>
	<view>
		<!-- #ifdef MP-WEIXIN -->
		<uni-ec-canvas class="uni-ec-canvas" ref="exceptionCanvas" :canvas-id="'lazy-load-canvas-' + optionId"
			:ec="ec"></uni-ec-canvas>
		<!-- #endif -->
		<!-- #ifndef MP-WEIXIN -->
		<view :id="optionId" class="uni-ec-canvas"></view>
		<!-- #endif -->
	</view>
</template>

<script>
	import uniEcCanvas from '@/components/uni-ec-canvas/uni-ec-canvas.vue';
	import * as echarts from "@/components/uni-ec-canvas/echarts";
	let currChart = null
	const genRandom = (min, max) => (Math.random() * (max - min + 1) | 0) + min;
	export default {
		components: {
			uniEcCanvas
		},
		props: {
			leftOffset: {
				type: String || Number,
				default: '130'
			},
			lineColor: {
				type: String,
				default: '#01B09A'
			},
			times: {
				type: Array,
				default: function() {
					return [];
				}
			}
		},
		data() {
			return {
				optionId: undefined,
				ec: {
					lazyLoad: true
				},
				option: {
					tooltip: {
						show: true,
						trigger: 'axis',
						formatter: function(a) {
							var str = '';
							for (var h = 0; h < a.length; h++) {
								str += `${a[h].name}`;
							}
							return str;
						},
						textStyle: {
							fontSize: 12,
						}
					},
					grid: {
						top: '5%',
						right: '3%',
						left: this.leftOffset,
						bottom: '0%',
						containLabel: true,
					},
					xAxis: [{
						type: 'category',
						data: this.times || [],
						axisLine: {
							lineStyle: {
								width: 3,
								color: '#26ded3'
							}
						},
						axisLabel: {
							// show: false
						},
						axisTick: { show: false }
					}],
					yAxis: [{
						axisLabel: {
							show: false
						},
						axisLine: {
							show: true,
							lineStyle: {
								width: 3,
								color: '#26ded3'
							}
						},
						splitLine: {
							show: false
						},
						axisTick: { show: false }
					}],
					series: [{
						type: 'bar',
						data: this.times.length ? Array(this.times.length).fill(1) : [],
						barWidth: '3rpx',
						itemStyle: {
							color:'#F66C3E'
						},
						markLine: {
							symbol: "none",
							data: [
								{
									yAxis: 1500, 
									lineStyle: {
										color: '#aac1ff',
									},
									label: {
										normal: {
											show: false,
										}
									},
								},
							]
						}
					}]
				}
			}
		},
		mounted() {
			// 设置随机数id
			let t = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
			let len = t.length
			let id = ''
			for (let i = 0; i < 32; i++) {
				id += t.charAt(Math.floor(Math.random() * len))
			}
			this.optionId = id
			this.$nextTick(() => {
				// #ifdef MP-WEIXIN
				this.$refs.exceptionCanvas.init(this.initChart)
				// #endif
				// #ifndef MP-WEIXIN
				let el = document.getElementById(this.optionId);
				let myChart= echarts.init(el);
				myChart.setOption(this.option);
				// #endif
			})
		},
		beforeDestroy() {
			// #ifdef MP-WEIXIN
			currChart.dispose();
			// #endif
			console.log('currChart.dispose');
		},
		methods: {
			initChart(canvas, width, height, canvasDpr) {
				currChart = echarts.init(canvas, null, {
					width: width,
					height: height,
					devicePixelRatio: canvasDpr
				})
				canvas.setChart(currChart)
				currChart.setOption(this.option)
				return currChart
			}
		}
	}
</script>

<style>
	.uni-ec-canvas {
		width: 100%;
		height: 160rpx;
		display: block;
	}
</style>
