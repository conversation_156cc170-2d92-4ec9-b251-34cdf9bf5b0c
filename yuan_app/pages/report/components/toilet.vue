<template>
	<view>
		<!-- <view class="item-block event-info">
			<view class="icon-title" :num="info.eventCount || 0">事件统计</view>
			<WaterPoloChart v-if="info.eventCount || info.eventCount === 0" ref="waterPoloChart" :num="info.eventCount || 0" title="当日告警数"></WaterPoloChart>
			<view style="width: 300rpx; margin: 0 auto;">
				<u-row gutter="0">
					<u-col span="6">
						<view style="text-align: center; border-right: 2rpx solid #888;">
							<text class="event-name">跌倒</text>
							<text class="event-num">{{ info.fallEventCount || 0 }}</text>
						</view>
					</u-col>
					<u-col span="6">
						<view style="text-align: center;">
							<text class="event-name">久滞</text>
							<text class="event-num">{{ info.longlagEventCount || 0 }}</text>
						</view>
					</u-col>
				</u-row>
			</view>
			<view class="r-flex" style="margin-top: 30rpx;">
				<text class="icon iconfont icon-baogaolimiandetubiao" style="width: 130rpx; font-size: 90rpx; color: #f89267;"></text>
				<view class="r-flex-1 tip-text">{{ info.eventStaResult }}</view>
			</view>
		</view> -->
		
		<view style="height: 20rpx; background: #F7F7F7;"></view>
		
		<view class="item-block heart-rate-info">
			<view class="icon-title">卫生间统计</view>
			<!-- <view style="color: #000; font-size: 24rpx; margin-top: 20rpx;">卫生间进出频率</view> -->
			<ReportPieChart v-if="display && (info.toileInoutCount || info.toileInoutCount === 0)" :num="info.toileInoutCount" unit="次" desc="总进出次数"></ReportPieChart>
			
			<view class="bed-stat">
				<view class="r-flex item">
					<view class="r-flex-1">
						<view class="title">白天</view>
						<view class="desc">建议1-10次</view>
					</view>
					<view class="r-flex-1 ta-r">
						<text class="supplement">{{ info.toileDayInoutCount || 0 }}次</text>
						<view style="width: 120rpx; display: inline-block;">
							<view v-if="info.toileDayInoutCountLevel === 1" class="tag leve-tag-red">较少</view>
							<view v-else-if="info.toileDayInoutCountLevel === 2" class="tag leve-tag-green">正常</view>
							<view v-else-if="info.toileDayInoutCountLevel === 3" class="tag leve-tag-red">频繁</view>
						</view>
					</view>
				</view>
				<view class="r-flex item">
					<view class="r-flex-1">
						<view class="title">夜间</view>
						<view class="desc">建议0-3次</view>
					</view>
					<view class="r-flex-1 ta-r">
						<text class="supplement">{{ info.toileNightInoutCount || 0 }}次</text>
						<view style="width: 120rpx; display: inline-block;">
							<view v-if="info.toileNightInoutCountLevel === 2" class="tag leve-tag-green">正常</view>
							<view v-else-if="info.toileNightInoutCountLevel === 3" class="tag leve-tag-red">频繁</view>
						</view>
					</view>
				</view>
			</view>
			
			
			<view v-if="(info.toileDayStayMomentList && info.toileDayStayMomentList.length) || (info.toileNightStayMomentList && info.toileNightStayMomentList.length)" style="border-top: 2rpx solid #ececec; padding-top: 30rpx; ">停留时间超过5分钟</view>
			<view v-if="info.toileDayStayMomentList && info.toileDayStayMomentList.length" style="position: relative; padding-top: 30rpx; text-align: center;">
				<view class="ta-r">共{{ info.toileDayStayMomentList ? info.toileDayStayMomentList.length : 0 }}次</view>
				<view style="position: absolute; left: 20rpx; top: 36%;">白天<br/>(6:00 - 19:00)</view>
				<ExceptionChart :times="info.toileDayStayMomentList || []"></ExceptionChart>
			</view>
			
			<view v-if="info.toileNightStayMomentList && info.toileNightStayMomentList.length" style="position: relative; padding-top: 30rpx; text-align: center;">
				<view class="ta-r">共{{ info.toileNightStayMomentList ? info.toileNightStayMomentList.length : 0 }}次</view>
				<view style="position: absolute; left: 20rpx; top: 36%;">夜晚<br/>(19:00 - 06:00)</view>
				<ExceptionChart :times="info.toileNightStayMomentList || []"></ExceptionChart>
			</view>
			
			<view class="bed-stat">
				<view class="r-flex item">
					<view class="r-flex-1">
						<view class="title">最长停留时长</view>
						<view class="desc">建议如厕时间不超过3分钟</view>
					</view>
					<view class="r-flex-1 ta-r">
						<text class="supplement">{{ info.toileStayMaxDuration ? getSFM(info.toileStayMaxDuration * 60, 'H时i分') : '0分' }}</text>
						<view style="width: 120rpx; display: inline-block;">
							<view v-if="info.toileStayMaxDurationLevel === 3" class="tag leve-tag-red">需关注</view>
						</view>
					</view>
				</view>
				<view class="r-flex item">
					<view class="r-flex-1">
						<view class="title">最长停留时长发生时间</view>
						<!-- <view class="desc"></view> -->
					</view>
					<view class="r-flex-1 ta-r">
						<text class="supplement">{{ info.toileStayMaxDurationMoment || '' }}</text>
						<view style="width: 120rpx; display: inline-block;">
						</view>
					</view>
				</view>
				<view class="r-flex item">
					<view class="r-flex-1">
						<view class="title">平均停留时长</view>
						<view class="desc">建议如厕时间不超过3分钟</view>
					</view>
					<view class="r-flex-1 ta-r">
						<text class="supplement">{{ info.toileStayAvgDuration ? getSFM(info.toileStayAvgDuration * 60, 'H时i分') : '0分' }}</text>
						<view style="width: 120rpx; display: inline-block;">
							<view v-if="info.toileStayAvgDurationLevel === 3" class="tag leve-tag-red">需关注</view>
						</view>
					</view>
				</view>
			</view>
			<view v-if="info.toileStaResult" class="r-flex" style="margin-top: 30rpx;">
				<text class="icon iconfont icon-baogaolimiandetubiao" style="width: 130rpx; font-size: 90rpx; color: #f89267;"></text>
				<view class="r-flex-1 tip-text">{{ info.toileStaResult || '' }}</view>
			</view>
		</view>
		<view v-if="info.toileRecentlyInoutCountVOList && info.toileRecentlyInoutCountVOList.length > 3" class="item-block heart-rate-info" style="border-top: 2rpx solid #ececec;">
			<view>最近七日进出次数数据对比</view>
			<!-- <LineChart></LineChart> -->
			<Line7dChart :datas="info.toileRecentlyInoutCountVOList"></Line7dChart>
			<view class="dynamic-list">
				<view class="item">总进出</view>
				<view class="item">白天进出</view>
				<view class="item">夜晚进出</view>
			</view>
			<view v-if="info.toileRecentlyStaResult" class="r-flex" style="margin-top: 30rpx;">
				<text class="icon iconfont icon-baogaolimiandetubiao" style="width: 130rpx; font-size: 90rpx; color: #f89267;"></text>
				<view class="r-flex-1 tip-text">{{ info.toileRecentlyStaResult || '' }}</view>
			</view>
			
		</view>
	</view>
</template>

<script>
	import LineChart from '@/components/line-chart/index.vue';
	import Line7dChart from './line-chart-7d.vue';
	import ReportPieChart from './pie-chart.vue';
	import ExceptionChart from './exception-chart.vue';
	import * as util from '@/utils/util'
	export default {
		components: {
			LineChart, ExceptionChart, ReportPieChart, Line7dChart
		},
		props: {
			info: {
				type: Object,
				required: true
			}
		},
		watch: {
			'info.toileInoutCount': function() {
				this.display = false;
				setTimeout(() => {
					this.display = true;
				}, 50)
			}
		},
		data() {
			return {
				display: true,
				lt: '<',
				gt: '>'
			}
		},
		methods: {
			getSFM: util.getSFM
		}
	}
</script>

<style lang="scss">
@import url("/static/css/iconfont.css");
.item-block {
	padding: 32rpx;
	background-color: white;
	.icon-title {
		font-size: 28rpx;
		font-weight: bold;
		&::before {
			content: ' ';
			display: inline-block;
			position: relative;
			top: 2rpx;
			width: 8rpx;
			height: 24rpx;
			margin-right: 10rpx;
			border-radius: 10rpx;
			background: #01B09A;
		}
	}
	.tip-text {
		font-size: 32rpx;
		color: #FF874D;
	}
}
.event-info {
	.event-name {
		font-size: 24rpx;
		color: #888;
	}
	.event-num {
		font-size: 24rpx;
		color: #000;
		margin-left: 10rpx;
	}
}
.heart-rate-info {
	.table {
		margin-top: 20rpx;
	}
	u-row {
		display: block;
		margin-bottom: 10rpx;
		padding: 10rpx 0rpx;
		&:nth-child(odd) {
			background: #f7f7f7;
		}
	}
	.careful-text {
		// color: #FF874D;
		color: #84B3B6;
		font-size: 24rpx;
		border-top: 2rpx solid #ECECEC;
		border-bottom: 2rpx solid #ECECEC;
		padding: 20rpx 32rpx;
		margin-top: 20rpx;
	}
}

.subsection {
	margin-top: 30rpx;
	.chart-item {
		display: inline-block;
		height: 20rpx;
		width: 100%;
		background: #AAAAAA;
	}
	.chart-text {
		margin-top: 16rpx;
		color: #888;
		font-size: 20rpx;
		.desc {
			margin-top: 10rpx;
		}
	}
	&-left {
		padding-right: 8rpx;
		.chart-item {
			border-top-left-radius: 10rpx;
			border-bottom-left-radius: 10rpx;
		}
	}
	&-center {
		padding-left: 8rpx;
		padding-right: 8rpx;
		.chart-item {
			background: #01B09A;
		}
	}
	&-right {
		padding-left: 8rpx;
		.chart-item {
			border-top-right-radius: 10rpx;
			border-bottom-right-radius: 10rpx;
		}
	}
}
	
.bed-stat {
	margin-top: 40rpx;
	margin-bottom: 40rpx;
	.item {
		margin-top: 30rpx;
		.title {
			font-size: 24rpx;
			color: #000;
		}
		.desc {
			margin-top: 10rpx;
			font-size: 20rpx;
			color: #888;
		}
		.supplement {
			margin-right: 20rpx;
			font-size: 24rpx;
			color: #000;
			vertical-align: middle;
		}
		.tag {
			display: inline-block;
			font-size: 20rpx;
			padding: 4rpx 30rpx;
			background: #01B09A;
			color: white;
			border-radius: 25rpx;
			vertical-align: middle;
		}
	}
}


.dynamic-list {
	display: flex;
	flex-direction: row;
	justify-content: center;
	.item {
		width: 23%;
		border-radius: 10rpx;
		// background: #bbb;
		margin-top: 20rpx;
		text-align: center;
		color: #000;
		padding: 6rpx 0rpx;
		font-size: 22rpx;
		// padding-left: 20rpx;
		&:nth-child(1) {
			&::before {
				background-color: #F66C3E;
			}
		}
		&:nth-child(2) {
			&::before {
				background-color: #01B09A;
			}
		}
		&:nth-child(3) {
			&::before {
				background-color: #4D7BFF;
			}
		}
		&::before {
			position: relative;
			left: -10rpx;
			top: -4rpx;
			content: ' ';
			display: inline-block;
			width: 20rpx;
			height: 6rpx;
			border-radius: 4rpx;
		}
	}
}
</style>
