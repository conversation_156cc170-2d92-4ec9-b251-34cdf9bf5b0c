<template>
	<view class="supplement-page">
		<view class="top-header-line"></view>
		<view>
			<u-field
				v-model="form.contactName"
				label="姓名"
				placeholder="请输入"
				maxlength="15"
				required
			>
			</u-field>
			<u-field
				v-model="form.contactPhone"
				label="电话"
				placeholder="请输入"
				maxlength="15"
				required
			>
			</u-field>
			<u-field @click="handleShowRelationTypeAction" v-model="form.relationType"  :disabled="true" label="关系" placeholder="请选择"
				right-icon="arrow-down-fill"
			>
			</u-field>
			<u-action-sheet @click="handleRelationTypeClick" :list="dict.relationType" :tips="relationTypeTips" v-model="showRelationTypeActionSheet" ></u-action-sheet>
			<u-field @click="openMapSelectDialog" type="textarea" v-model="form.addr" label="地址" disabled placeholder="请选择"
				right-icon="arrow-down-fill"
			>
			</u-field>
		</view>
		
		<view class="footer-btns">
			<!-- <u-button v-if="checked === true" type="primary" shape="circle" class="scan-code-btn" @click="scan">下一步</u-button> -->
			<!-- <u-button v-if="saved === false" type="primary" shape="circle" class="next-btn" @click="handleSave">保存</u-button> -->
			<u-button v-if="saved === false" shape="circle" class="next-btn err-oper-btn diy-btn" size="medium" style="margin-top: 56rpx"
			:custom-style="{ 'width': '100%', 'color': 'white', 'background-color': '#01B09A' }" :hair-line="false" hover-class="none"
			@click="handleSave">保存</u-button>
			<navigator v-if="saved === true" open-type="exit" target="miniProgram" class="close-btn">退出</navigator>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				showRelationTypeActionSheet: false,
				deleteDialog: false,
				dict: {
					relationType: [
						{
							code: 'kinsman',
							text: '亲属'
						},
						{
							code: 'neighbor',
							text: '邻居'
						}
					]
				},
				relationTypeTips: {
					text: '关系',
					color: '#909399',
					fontSize: 24
				},
				devId: undefined,
				form: {
					devId: undefined,
					contactName: undefined,
					contactPhone: undefined,
					relationType: undefined,
					addr: undefined,
					latitude: undefined,
					longitude: undefined,
					memberId: undefined,
					openIdId: undefined,
				},
				saved: false,

				//aladding
				familyId: undefined,
			}
		},
		onLoad(option) {
			this.devId = option.devId;
			this.familyId = option.familyId;
			this.memberId = option.memberId;
			this.openId = option.openId;
			this.form.devId = option.devId;
			this.form.familyId = option.familyId;
			this.form.memberId = option.memberId;
			this.form.openId = option.openId;
			if (option.memberId && option.openId) {
				this.fetchDetailInfo();
			}
			console.log('option.devId, option.memberId, option.openId', option.devId, '-', option.memberId, '-', option.openId)
		},
		methods: {
			openMapSelectDialog() {
				let _that = this;
				uni.chooseLocation({
					success: (res) => {
						 console.log('位置名称：' + res.name);
						 console.log('详细地址：' + res.address);
						 console.log('纬度：' + res.latitude);
						 console.log('经度：' + res.longitude);
						 _that.form.addr = res.address.indexOf(res.name) != -1 ? res.address : res.address + res.name;
						 _that.form.latitude = res.latitude;
						 _that.form.longitude = res.longitude;
						 
					 }
				});
			},
			fetchDetailInfo() {
				uni.showLoading({
					title: '加载中...',
					mask: true
				})
				this.$u.api.fetchMyContact({ memberId: this.memberId, openId: this.openId }).then(res => {
					if (res) {
						res.relationType = res.relationTypeDesc || res.relationType
						this.form = res
					}
				}).catch(err => {
					uni.showToast({ duration: 2000, title: res.message || '发生未知错误', icon: 'none' })
				}).finally(() => {
					setTimeout(() => {
						uni.hideLoading();
					}, 1000)
				})
			},
			handleShowRelationTypeAction() {
				this.showRelationTypeActionSheet = true;
			},
			handleRelationTypeClick(index) {
				this.form.relationType = this.dict.relationType[index].text;
			},
			handleOpenDeleteDialog() {
				this.deleteDialog = true
			},
			convertDictByText(dictItemName, name) {
				let _dictItem = this.dict[dictItemName]
				if (!_dictItem || !_dictItem.length  || !name){
					return {};
				}
				let _filters = _dictItem.filter(d => d.text === name);
				return _filters.length ? _filters[0] : {}
			},
			handleSave() {
				
				if (!this.form.memberId) {
					uni.showModal({ content: '请重新扫码后进行添加', showCancel: false, confirmText: '关闭' })
					return;
				}
				if (!this.form.contactName || !this.form.contactPhone) {
					uni.showModal({ content: '请检查“*”必填项是否填写正确', showCancel: false, confirmText: '关闭' })
					return;
				}
				if (/^[1][3,4,5,6,7,8,9][0-9]{9}$/.test(this.form.contactPhone) === false) {
					uni.showModal({ content: '请检查电话是否填写正确', showCancel: false, confirmText: '关闭' })
					return;
				}
				
				let _params = {
					...this.form,
				}
				if (!_params.devId) {
					_params.devId = this.devId;
				}
				if (!_params.familyId) {
					_params.familyId = this.familyId;
				}
				_params.relationType = this.convertDictByText('relationType', _params.relationType).code
				uni.showLoading({
					title: '保存中...',
					mask: true
				})
				let _api = this.$u.api.execContactMyAddOrUpdate
				_api(_params).then(res => {
					uni.showToast({ duration: 2000, title: '保存紧急联系人成功', icon: 'none' })
					this.saved = true;
				}).catch(err => {
					console.log('err', err)
					uni.showToast({ duration: 2000, title: err.message || '发生未知错误', icon: 'none' })
				}).finally(() => {
					setTimeout(() => {
						uni.hideLoading();
					}, 1000)
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
.supplement-page {
	.icon-reqired {
		color: red;
		display: inline-block;
		vertical-align: middle;
		font-size: 40rpx;
		position: relative;
		top: 4rpx;
		left: 6rpx;
	}
	::v-deep .u-label {
		font-size: 32rpx;
		width: 120rpx;
		display: block;
		color: #0D0D0D;
		flex: initial !important;
		&::before {
			font-size: 40rpx;
			top: 10rpx;
		}
		.u-label-text {
			padding-left: 20rpx;
			font-weight: bold;
			box-sizing: border-box;
		}
	}
	.direction-field {
		::v-deep .u-field-inner {
			height: 120rpx;
			align-items: baseline;
		}
	}
	.footer-btns {
		position: absolute;
		bottom: 40rpx;
		left: 40rpx;
		right: 40rpx;
		margin: 0 auto;
		.next-btn {
			display: block;
			margin-top: 20rpx;
		}
		.close-btn {
			color: #ffffff;
			border-color: #010102;
			background-color: #6b7990;
			position: relative;
			border: 0;
			display: inline-flex;
			overflow: visible;
			line-height: 80rpx;
			display: flex;
			flex-direction: row;
			align-items: center;
			justify-content: center;
			cursor: pointer;
			padding: 0 40rpx;
			z-index: 1;
			box-sizing: border-box;
			transition: all 0.15s;
			font-size: 30rpx;
			height: 80rpx;
			border-radius: 100rpx;
			line-height: 80rpx;
		}
	}
	// ::v-deep .uni-easyinput__content {
	// 	padding: 14rpx 6rpx;
	// }
	::v-deep .u-model__footer__button {
		height: 88rpx;
		line-height: 88rpx;
	}
	::v-deep .u-field {
		padding: 28rpx;
	}
}
</style>
