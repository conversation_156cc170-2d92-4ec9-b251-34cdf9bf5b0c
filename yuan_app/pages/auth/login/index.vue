<template>
	<view class="login-page" :style="{
		background: `url(${staticUrl}/images/login/login-bg.jpg) center center no-repeat`
	}">
		
		<view class="login-form">
			<view v-if="loginType === 'pwd'" class="form-item">
				<u-field
					v-model="form.phone"
					label="账号"
					:label-width="100"
					placeholder="请输入账号"
					:border-bottom="false"
					placeholder-style="color: white"
				>
					<text slot="icon" class="icon iconfont icon-denglu-yonghu" style="margin-right: 10rpx;"></text>
				</u-field>
			</view>
			<view v-if="loginType === 'pwd'" class="form-item">
				<u-field
					v-model="form.pwd"
					label="密码"
					:label-width="100"
					placeholder="请输入密码"
					type="password"
					:border-bottom="false"
					placeholder-style="color: white"
				>
					<text slot="icon" class="icon iconfont icon-shurumimatubiao" style="margin-right: 10rpx;"></text>
				</u-field>
			</view>
			<view v-if="loginType === 'vcode'" class="form-item">
				<u-field
					v-model="form.phone"
					label="+86"
					:label-width="70"
					placeholder="请输入手机号"
					type="number"
					:border-bottom="false"
					placeholder-style="color: white"
				>
				</u-field>
			</view>
			<view v-if="loginType === 'vcode'" class="form-item">
				<u-field
					v-model="form.vcode"
					placeholder="请输入短信验证码"
					:label-width="70"
					label-align="center"
					type="number"
					:border-bottom="false"
					placeholder-style="color: white"
				>
					<text slot="icon" class="icon iconfont icon-anquan1" style="margin-right: 10rpx;"></text>
					<view slot="right" style="color: #09d2c5;position: relative; right: -24rpx;">
						<text v-if="showSec" @click="getCode">获取验证码</text>
						<text v-else>{{second}}s后重新发送</text>
					</view>
				</u-field>
			</view>
			<view class="remember-me">
				<view class="" @click="$navTo('pages/auth/reg/index')">注册</view>
				<view v-if="loginType === 'pwd'" class="forget-pwd" @click="handleForgetPwd">忘记密码?</view>
			</view>
			<view class="login-btn">
				<u-button :loading="loginLoading" type="primary" size="medium" 
				:custom-style="{ 'width': '100%', 'background-color': '#01B09A' }" 
				hover-class="login-btn-hover"
				@click="login">登录</u-button>
				<u-button v-if="loginType === 'vcode'" :loading="loginLoading" size="medium" 
				:custom-style="{ 'width': '100%', 'background-color': 'transparent', 'margin-top': '24rpx', 'color': 'white' }" 
				hover-class="none"
				plain @click="changeLoginType('pwd')">账号登录</u-button>
				<u-button v-if="loginType === 'pwd'" :loading="loginLoading" size="medium" 
				:custom-style="{ 'width': '100%', 'background-color': 'transparent', 'margin-top': '24rpx', 'color': 'white' }" 
				hover-class="none"
				plain @click="changeLoginType('vcode')">验证码登录</u-button>
			</view>
			<!-- #ifdef MP-WEIXIN -->
			<view class="weixin-login">
				<u-divider color="white" half-width="50">其他登陆方式</u-divider>
				<button v-if="firstLogined" class="wx-btn" @click="gotoHome">
					<text class="icon iconfont icon-dianhua" style="color: #09d2c5;font-size: 80rpx;margin-top: 36rpx;"></text>
					<!-- 
					<view class="wx-bg">
						<image class="top-logo" :src="staticUrl + '/images/login/login-bottom-wx.png'" mode="widthFix"></image>
					</view>
					 -->
					<view class="wx-text">
						手机号快捷登录
					</view>
				</button>
				<button v-else class="wx-btn" open-type="getPhoneNumber" @getphonenumber="onGetPhoneNumber">
					<text  class="icon iconfont icon-dianhua" style="color: #09d2c5;font-size: 80rpx;margin-top: 36rpx;"></text>
					<!-- <view class="wx-bg">
						<image class="top-logo" :src="staticUrl + '/images/login/login-bottom-wx.png'" mode="widthFix"></image>
					</view>
					 -->
					<view class="wx-text">
						手机号快捷登录
					</view>
				</button>
			</view>
			<!-- #endif -->
			<view class="agreement" @click="handleAgreementClick">
				<text v-if="readSelectRdo === true" class="icon iconfont icon-xuanzhong radio" style="color: #01B09A; margin-right: 14rpx;"></text>
				<text v-else class="icon iconfont icon-weixuanzhong radio" style="margin-right: 14rpx;"></text>
				我已阅读并同意<text class="green" @click.stop="$navTo('pagesMy/article/index?key=agreement')">《用户协议》</text>和<text class="green" @click.stop="$navTo('pagesMy/article/index?key=privacy')">《隐私协议》</text>
			</view>
			
		</view>
	</view>
</template>

<script>
	import tokenManager from '@/utils/tokenManager.js';

	export default {
		data() {
			return {
				staticUrl: this.$u.http.config.staticBaseUrl,
				readSelectRdo: false,
				code: undefined,
				loginType: 'pwd',
				showSec: true,
				second: 60,
				form: {
					phone: undefined,
					pwd: undefined,
					vcode: undefined
				},
				loginLoading: false,
				firstLogined: false,
				firstRes: false,
				isProcessingAppToken: false // 标记是否正在处理App token
			};
		},
        async onLoad(option) {//默认加载
            // this.login();
			//#ifdef MP-WEIXIN
			wx.hideHomeButton()
			//#endif

			// 处理URL中的token参数（用于免登录跳转）
			//#ifdef H5
			console.log("login page option:", option)

			// 使用tokenManager处理App传入的token
			const appTokenResult = await tokenManager.handleAppTokenParams(option, this.$u.api);
			if (appTokenResult) {
				if (appTokenResult.success) {
					this.isProcessingAppToken = true;

					// 构建跳转URL，保留APP环境参数
					const indexUrl = tokenManager.getNavigationUrl('/pages/index/index', appTokenResult.platform, appTokenResult.appType);

					// 直接跳转到首页
					uni.showToast({title: '登录成功'});
					uni.reLaunch({
						url: indexUrl
					});
					return;
				} else {
					// App token校验失败，显示错误并继续正常登录流程
					console.error("App token校验失败：", appTokenResult.error);
					uni.showToast({
						title: appTokenResult.error || 'Token校验失败',
						icon: 'none'
					});
				}
			} else {
				// 没有App token，检查本地是否有有效token
				const hasValidToken = await this.checkLocalToken();
				if (hasValidToken) {
					// 本地token有效，直接跳转到首页
					const indexUrl = tokenManager.getNavigationUrl('/pages/index/index', option.platform, option.appType);
					uni.reLaunch({
						url: indexUrl
					});
					return;
				}
			}
			//#endif

			let _remember = uni.getStorageSync('remember');
			if (_remember) {
				this.form.phone = _remember.phone;
				this.form.pwd = _remember.password;
			}

			//#ifdef MP-WEIXIN
			wx.login({
				success: (res) => {
					console.log(res)
					if (res.code) {         //微信登录成功 已拿到code
						this.code = res.code
						this.$u.api.login({
							code: res.code,
							loginType: 'wx',
							needTip: false
						}).then(res => {
							console.log('resres', res)
							if (res?.token) {
								this.firstLogined = true
								this.firstRes = res
							}
						})
					} else {
						console.log('登录失败！' + res.errMsg)
					}
				}
			})
			//#endif
        },
		methods: {
			// 检查本地token是否有效
			async checkLocalToken() {
				if (!tokenManager.isLoggedIn()) {
					return false;
				}

				// 静默校验token
				return await tokenManager.silentValidateToken(this.$u.api);
			},

			handleAgreementClick() {
				this.readSelectRdo = !this.readSelectRdo
			},
			// 获取验证码
			getCode() {
				if (!this.form.phone) {
					this.$u.toast('请填写手机号');
					return;
				}
				uni.showLoading({
					title: '正在获取验证码',
					mask: true
				})
				this.$u.api.fetchRegVCode({ type: 4, phone: this.form.phone }).then(res => {
					this.showSec = false
					var interval = setInterval(() => {
						let times = --this.second
						this.second = times < 10 ? '0' + times : times //小于10秒补 0
					}, 1000)
					setTimeout(() => {
						clearInterval(interval)
						this.second = 60
						this.showSec = true
					}, 60000)
					this.$u.toast('验证码已发送');
				}).finally(() => {
					setTimeout(() => {
						uni.hideLoading();
					}, 1000)
				})
			},
			changeLoginType(type) {
				this.loginType = type;
				if (this.type === 'pwd') {
					this.form.vcode = undefined;
				} else {
					this.form.pwd = undefined;
				}
			},
			gotoHome() {
				if (!this.readSelectRdo) {
					uni.showToast({
						duration: 2000,
						title: '请阅读并同意协议',
						icon: 'none'
					})
					return;
				}
				if (this.firstLogined === true && this.firstRes) {
					// 使用tokenManager管理token和用户信息
					tokenManager.setTokenAndMember(this.firstRes.token, this.firstRes.member);

					uni.showToast({title: '登录成功'});

					// 跳转到首页
					uni.reLaunch({
						url: '/pages/index/index'
					});
				}
			},
			handleGotoReg() {
				uni.navigateTo({
					url: '/pages/auth/reg/index'
				})
			},
			handleForgetPwd() {
				uni.navigateTo({
					url: '/pages/auth/forget/index'
				})
			},
			onGetPhoneNumber(e) {
				if (!this.readSelectRdo) {
					uni.showToast({
						duration: 2000,
						title: '请阅读并同意协议',
						icon: 'none'
					})
					return;
				}
				let _that = this;
				if (e.detail.errMsg == 'getPhoneNumber:fail user deny') {       //用户决绝授权  
					//拒绝授权后弹出一些提示  
				} else {      //允许授权
					// e.detail.encryptedData      //加密的用户信息  
					// e.detail.iv     //加密算法的初始向量  时要用到  
					let encryptedData = e.detail.encryptedData
					let iv = e.detail.iv
					 //检查登录
					uni.checkSession({
						success: (res) => {
							if (res.errMsg === 'checkSession:ok') {
								console.log('登录暂未过期', res);
								if (encryptedData !== undefined && iv !== undefined) {
									_that.fetchPhone(encryptedData, iv)
								}
							}
						},
						fail: (res) => {
							console.log('登录已过期', res);
							//再执行一次login
							uni.login({
								provider: 'weixin',
								success: function(res) {
									console.log("res.code", res.code);
									_that.code = res.code
									if (encryptedData !== undefined && iv !== undefined) {
										_that.fetchPhone(encryptedData, iv)
									}
								}
							});
						}
					})
				}
			},
			//手机号发送至后台解密
			fetchPhone(encryptData, ivStr) {
				
				if (!this.code) {
					wx.login({
						success: (res) => {
							this.code = res.code
						}
					})
				}
				
				this.$u.api.execDecodePhoneNumber({
					code: this.code,
					encryptedData: encryptData,
					iv: ivStr,
				}).then(res => {
					console.log("获取解密后的手机号 - res: ", res)

					// 使用tokenManager管理token和用户信息
					tokenManager.setTokenAndMember(res.token, res.member);

					uni.showToast({title: '登录成功'});

					// 跳转到首页
					uni.reLaunch({
						url: '/pages/index/index'
					});
				})
			},
			// 处理登录事件
			handleLoginClick() {
				
			},
			// 第一授权获取用户信息 ===》按钮触发
			wxGetUserInfo() {
				let _self = this;
				// 1.获取用户的信息
				uni.getUserInfo({
					provider: 'weixin',
					success: ( infoRes ) => {
						console.log( infoRes )
						_self.userInfo = infoRes.userInfo
						this.login(infoRes.userInfo);
					},
					fail: () => {
						uni.showToast({ title: '登录失败', icon: 'none' });
					}
				});
				return false
			},
			//登录
			login(userInfo) {
				
				if (!this.readSelectRdo) {
					uni.showToast({
						duration: 2000,
						title: '请阅读并同意协议',
						icon: 'none'
					})
					return;
				}
				let _this = this;
				if (this.loginType === 'pwd') {
					if (!this.form.phone) {
						uni.showToast({
							duration: 2000,
							title: '请输入账号',
							icon: 'none'
						})
						return;
					}
					if (!this.form.pwd) {
						uni.showToast({
							duration: 2000,
							title: '请输入密码',
							icon: 'none'
						})
						return;
					}
				} else if (this.loginType === 'vcode') {
					if (!this.form.phone) {
						uni.showToast({
							duration: 2000,
							title: '请输入手机号',
							icon: 'none'
						})
						return;
					}
					if (!this.form.vcode) {
						uni.showToast({
							duration: 2000,
							title: '请输入短信验证码',
							icon: 'none'
						})
						return;
					}
				} else {
					return;
				}
				uni.showLoading({
					title: '登录中...'
				});
				//#ifdef MP-WEIXIN
				// 1.wx获取登录用户code
				uni.login({
					provider: 'weixin',
					success: (loginRes) => {
						let code = loginRes.code;
						this.$u.api.login({
							code: code,
							loginType: this.loginType,
							// nickname: userInfo.nickName,
							// phone: this.form.phone,
							phone: this.form.phone,
							password: this.form.pwd,
							vcode: this.form.vcode,
							// profile: userInfo.avatarUrl
						}).then(res => {
							// 使用tokenManager管理token和用户信息
							tokenManager.setTokenAndMember(res.token, res.member);

							uni.showToast({title: '登录成功'});

							// 跳转到首页
							uni.reLaunch({
								url: '/pages/index/index'
							});
						})
					},
				});
				//#endif
				
				this.$u.api.login({
					code: '',
					loginType: this.loginType,
					phone: this.form.phone,
					password: this.form.pwd,
					vcode: this.form.vcode,
					// profile: userInfo.avatarUrl
				}).then(res => {
					// 使用tokenManager管理token和用户信息
					tokenManager.setTokenAndMember(res.token, res.member);

					uni.showToast({title: '登录成功'});

					if (this.rememberChecked === true) {
						uni.setStorageSync( 'remember', {
							phone: this.form.phone,
							password: this.form.pwd
						});
					}

					// 跳转到首页
					uni.reLaunch({
						url: '/pages/index/index'
					});
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
@import url("/static/css/iconfont.css");
.login-page {
	width: 100%;
	height: 100vh;
	// background: url('http://r9a1krpiw.hb-bkt.clouddn.com/login/login-bg.jpg') center center no-repeat;
	background-size: 100% 100%;
	padding-top: 400rpx;
}
.login-form {
	padding: 0upx 60upx;
	.form-item {
		color: white;
		// background: #F6F7F9;
		// border-radius: 45upx;
		&:nth-child(n + 2) {
			margin-top: 20upx;
		}
	}
	.remember-me {
		margin-top: 40upx;
		position: relative;
		color: white;
		.forget-pwd {
			display: inline-block;
			position: absolute;
			right: 0rpx;
			top: 0rpx;
			font-size: 28rpx;
		}
	}
	.login-btn {
		padding-top: 62upx;
	}
	.weixin-login {
		position: fixed;
		bottom: 240rpx;
		left: 0rpx;
		right: 0rpx;
		text-align: center;
		
		.wx-btn {
			background: transparent;
			display: inline-block;
			line-height: initial;
			&:after{
				border: none;
			}
		}
		.wx-bg {
			display: inline-block;
			width: 90rpx;
			height: 90rpx;
			// border-radius: 92rpx;
			// background: #F6F7F9;
			margin: 0 atuo;
			padding-top: 28rpx;
			image {
				width: 100%;
				height: 100%;
			}
		}
		.wx-text {
			margin-top: 12rpx;
			color: #b7b9be;
			text-align: center;
			font-size: 24rpx;
		}
	}
}

.u-border-bottom {
	border-bottom: 0px;
}
::v-deep .u-field {
	color: white !important;
	border-bottom: 2rpx solid white;
	
}
.login-btn {
	width: '100%';
	background-color: '#01B09A';
}
.login-btn-hover[type=primary] {
	background-color: #009a90 !important;
}

.agreement {
	position: fixed;
	bottom: 70rpx;
	left: 0rpx;
	right: 0rpx;
	text-align: center;
	width: 100%;
	text-align: center;
	color: white;
	.green {
		margin: 0px 10rpx;
		color: #0FBEB7;
		text-decoration: underline;
	}
}
::v-deep .u-divider {
	background-color: transparent !important;
}
</style>
