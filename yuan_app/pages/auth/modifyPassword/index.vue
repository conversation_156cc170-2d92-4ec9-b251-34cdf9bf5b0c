<template>
	<view class="my-setting-page">
		<view class="top-header-line"></view>
		<view class="item-block">
			<u-row @click="gotoUrl('/pages/auth/modifyPassword/step1')">
				<u-col span="8">
					<view class="left-info">
						<image src="@/static/images/my/icon-ward.png"></image>
						<view class="field-name" style="position: relative; top: 6rpx;">修改密码</view>
					</view>
				</u-col>
				<u-col span="4">
					<view class="right-status">
						<u-icon name="arrow-right" color="#b9b9b9" size="22"></u-icon>
					</view>
				</u-col>
			</u-row>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
			}
		},
		methods: {
			gotoUrl(url){
				debugger;
				if (!url) return;
				uni.navigateTo({
					url: url
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
.my-setting-page {
	.item-block {
		height: 784rpx;
		background: white;
		padding: 24rpx 30rpx;
		color: $u-content-color;
		font-size: 28rpx;
		box-shadow: 0rpx 8rpx 24rpx 0rpx rgba(0, 0, 0, 0.1);
		border-radius: 16rpx;
		u-row {
			display: block;
			line-height: 70rpx;
			border-bottom: 2rpx solid #E9E9E9;
			padding: 18rpx 0;
		}
		.left-info {
			image {
				width: 40rpx;
				height: 40rpx;
				display: inline-block;
				margin-right: 20rpx;
				vertical-align: middle;
			}
			.field-name {
				display: inline-block;
				color: #454444;
				font-size: 32rpx;
				position: relative;
				top: 2rpx;
			}
		}
		.right-status {
			color: #b9b9b9;
			text-align: right;
			// margin-top: 28rpx;
			::v-deep .u-icon {
				margin-left: 10rpx;
				position: relative;
				top: 2rpx;
			}
		}
		&:nth-child(n + 2) {
			margin-top: 20rpx;
		}
	}
}
</style>
