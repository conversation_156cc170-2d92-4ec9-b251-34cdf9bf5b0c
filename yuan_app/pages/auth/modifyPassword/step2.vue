<template>
	<view class="modify-pwd-result-page">
		<view class="top-header-line"></view>
		
		<view class="top-wrap">
			<image class="img-center-bg" src="@/static/images/common/icon-verification-passed.png"></image>
			<view class="tip">密码修改成功</view>
		</view>
		
		<view class="footer-btns">
			<u-button :custom-style="{ 'width': '100%', 'color': 'white', 'background-color': '#01B09A' }" shape="circle" class="next-btn" @click="gotoHome">返回首页</u-button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				status: false,
				devCode: undefined
			}
		},
		methods: {
			gotoHome() {
				uni.switchTab({
					url: '/pages/index/index'
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
.modify-pwd-result-page {
	height: 100vh;
	position: relative;
	// padding: 30upx;
	box-sizing: border-box;
	text-align: center;
	.device-info {
		padding: 40rpx 0rpx 0rpx 40rpx;
		font-size: 30rpx;
		color: #5E5D5D;
		text-align: left;
	}
	.top-wrap {
		padding-top: 128rpx;
		.img-center-bg {
			width: 128rpx;
			height: 128rpx;
			margin: 0 auto;
		}
		.tip {
			font-size: 30upx;
			color: #5E5D5D;
			font-size: 30rpx;
			font-weight: 500;
			margin-top: 55rpx;
			font-weight: bold;
		}
	}
	.footer-btns {
		position: absolute;
		bottom: 40rpx;
		left: 60rpx;
		right: 60rpx;
		margin: 0 auto;
		.next-btn {
			display: block;
			margin-top: 20rpx;
		}
	}
}
</style>
