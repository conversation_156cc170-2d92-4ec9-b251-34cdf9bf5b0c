<template>
	<view class="reset-completed-page">
		<view class="top-header-line"></view>
		
		<view class="top-wrap">
			<image class="img-center-bg" src="@/static/images/common/icon-verification-passed.png"></image>
			<view class="tip">密码已重置成功</view>
		</view>
		
		<view class="desc-content">请使用新密码登录</view>
		
		<view class="footer-btns">
			<u-button :custom-style="{ 'color': 'white', 'background-color': '#01B09A' }" shape="circle" class="next-btn" @click="next">立即登录</u-button>
		</view>
		
	</view>
</template>

<script>
	export default {
		data() {
			return {
			}
		},
		onLoad(option) {
		},
		methods: {
			next() {
				uni.redirectTo({
					url: '/pages/auth/login/index'
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
.reset-completed-page {
	height: 100vh;
	position: relative;
	// padding: 30upx;
	box-sizing: border-box;
	text-align: center;
	.device-info {
		padding: 40rpx 0rpx 0rpx 40rpx;
		font-size: 30rpx;
		color: #5E5D5D;
		text-align: left;
	}
	.top-wrap {
		padding-top: 128rpx;
		.img-center-bg {
			width: 128rpx;
			height: 128rpx;
			margin: 0 auto;
		}
		.tip {
			font-size: 30upx;
			color: #5E5D5D;
			font-size: 30rpx;
			font-weight: 500;
			margin-top: 55rpx;
			font-weight: bold;
		}
	}
	.desc-content {
		color: #47DF9B;
		font-size: 24rpx;
		margin-top: 15rpx;
		text-align: center;
	}
	.footer-btns {
		padding: 0px 60rpx;
		margin-top: 138rpx;
		text-align: center;
		.next-btn {
			display: block;
			margin-top: 20rpx;
		}
	}
}
</style>
