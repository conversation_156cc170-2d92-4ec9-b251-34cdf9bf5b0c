<template>
	<view class="forget-pwd-page">
		<view class="top-header-line"></view>
		<u-form ref="uForm" :model="form" :rules="rules" :error-type="[ 'message' ]">
			<u-form-item label-width="150" label="+86" prop="phone">
				<u-input :clearable="false" v-model="form.phone" placeholder="请输入手机号码" @input="handlePhoneChange" />
			</u-form-item>
			<u-form-item label-width="150" label="验证码" prop="vcode">
				<u-input :clearable="false" v-model="form.vcode" placeholder=" " />
				<u-button slot="right" shape="circle" :custom-style="{ 'color': 'white', 'background-color': '#01B09A' }" size="mini" @click="getCode" class="get-valicode" :class="{ 'code-actived': isCodeActive }">{{codeTips}}</u-button>
			</u-form-item>
		</u-form>
		
		<!-- <view style="color: #47DF9B; font-size: 32rpx; margin-top: 45rpx; text-align: center;">已发送</view> -->
		
		<view class="reg-btn">
			<u-button :loading="execLoading" shape="circle" :custom-style="{ 'color': 'white', 'background-color': '#01B09A' }" @click="submit">确定</u-button>
		</view>
		
		<u-verification-code seconds="60" ref="uCode" @change="codeChange"></u-verification-code>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				execLoading: false,
				isCodeActive: false,
				codeTips: '获取',
				form: {
					phone: undefined,
					vcode: undefined
				},
				rules: {
					phone: [
						{
							required: true,
							message: '请输入手机号',
							trigger: [ 'change', 'blur' ],
						},
						{
							validator: (rule, value, callback) => {
								return /^[1][3,4,5,6,7,8,9][0-9]{9}$/.test(value)
								// 调用uView自带的js验证规则，详见：https://www.uviewui.com/js/test.html
								// return this.$u.test.mobile(value);
							},
							message: '手机号码不正确',
							// 触发器可以同时用blur和change，二者之间用英文逗号隔开
							trigger: [ 'change', 'blur' ],
						}
					],
					vcode: [
						{
							required: true,
							message: '请输入验证码',
							trigger: 'blur' ,
						},
						{
							len: 4,
							message: '验证码长度为4个字符',
							trigger: [ 'change', 'blur' ],
						},
					]
				}
			}
		},
		onReady() {
			this.$refs.uForm.setRules(this.rules);
		},
		methods: {
			handlePhoneChange(phone) {
				this.isCodeActive = this.$u.test.mobile(phone)
			},
			codeChange(text) {
				this.codeTips = text;
			},
			submit() {
				this.execLoading = true;
				this.$refs.uForm.validate(valid => {
					// uni.showModal({ content: '该账号已被注册，是否直接登录', confirmText: '直接登陆' })
					if (valid) {
						console.log('验证通过');
						this.$u.api.execCheckVCode({ ...this.form }).then(res => {
							uni.navigateTo({
								url: `/pages/auth/forget/newpwd?phone=${this.form.phone}&vcode=${this.form.vcode}`
							})
						}).finally(() => {
							this.execLoading = false;
						})
					} else {
						this.execLoading = false;
						console.log('验证失败');
					}
				});
			},
			// 获取验证码
			getCode() {
				if (!this.form.phone) {
					this.$u.toast('请填写手机号');
					return;
				}
				if (this.$refs.uCode.canGetCode) {
					uni.showLoading({
						title: '正在获取验证码',
						mask: true
					})
					console.log(this.$u.api)
					this.$u.api.fetchRegVCode({ type: 2, phone: this.form.phone }).then(res => {
						this.$u.toast('验证码已发送');
						this.$refs.uCode.start();
					}).finally(() => {
						setTimeout(() => {
							uni.hideLoading();
						}, 1000)
					})
				} else {
					// this.$u.toast('倒计时结束后再发送');
					console.log('倒计时结束后再发送')
				}
			},
		}
	}
</script>

<style lang="scss" scoped>
.forget-pwd-page {
	padding: 80rpx 70rpx;
	.reg-btn {
		padding-top: 105upx;
	}
	u-form-item {
	    margin-top: 22rpx;
	    display: block;
		::v-deep .u-form-item {
			padding-bottom: 0rpx;
			.u-form-item__message {
				padding-left: 0rpx !important;
				position: absolute;
				bottom: -39rpx;
				left: 0rpx;
				right: 0rpx;
				font-size: 22rpx;
			}
		}
		&:nth-last-child(1) {
			margin-top: 50rpx;
		}
	}
	.pwd-item {
		position: relative;
		.pwd-desc {
			position: absolute;
			bottom: -60rpx;
			left: 0rpx;
			right: 0rpx;
			font-size: 22rpx;
			color: #8B8B8B;
		}
	}
	.get-valicode {
		position: relative;
		top: -4rpx;
		::v-deep button {
			background: #F6F7F9;
			height: 60rpx;
			line-height: 60rpx;
			width: 210rpx;
			font-size: 24rpx;
			color: #8B8B8B;
			&::after {
				border: 0rpx;
			}
		}
	}
	
	.code-actived {
		::v-deep button {
			background: #2979ff;
			color: white;
			// height: 60rpx;
			// line-height: 60rpx;
			// width: 210rpx;
			// font-size: 24rpx;
			// color: #8B8B8B;
			&::after {
				border: 0rpx;
			}
		}
	}
}
</style>
