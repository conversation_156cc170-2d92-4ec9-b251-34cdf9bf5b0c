<template>
	<view class="new-pwd-page">
		<view class="top-header-line"></view>
		<u-form ref="uForm" :model="form" :rules="rules" :error-type="[ 'message' ]">
			<u-form-item prop="password" class="pwd-item">
				<u-input :clearable="false" :password-icon="true" type="password" v-model="form.password" placeholder="请输入新密码" maxlength="16"></u-input>
				<view class="pwd-desc">密码必须是字母+数字组合，且长度为8-16位</view>
			</u-form-item>
			<u-form-item prop="passwordAgain">
				<u-input :clearable="false" type="password" v-model="form.passwordAgain" placeholder="请再次输入新密码" maxlength="16"></u-input>
			</u-form-item>
		</u-form>
		<view class="reg-btn">
			<u-button :loading="execLoading" shape="circle" :custom-style="{ 'color': 'white', 'background-color': '#01B09A' }" @click="submit">确定</u-button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				execLoading: false,
				form: {
					phone: undefined,
					vcode: undefined,
					password: undefined,
					passwordAgain: undefined
				},
				rules: {
					password: [
						{
							required: true,
							message: '密码必须是字母+数字组合，且长度为8-16位',
							trigger: [ 'change', 'blur' ],
						},
						{
							// 正则不能含有两边的引号
							pattern: /^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]+\S{7,16}$/,
							message: '密码必须是字母+数字组合，且长度为8-16位',
							trigger: [ 'change', 'blur' ],
						}
					],
					passwordAgain: [
						{
							required: true,
							message: '请重新输入密码',
							trigger: [ 'change', 'blur' ],
						},
						{
							validator: (rule, value, callback) => {
								return value === this.form.password;
							},
							message: '重复密码错误',
							trigger: [ 'change', 'blur' ],
						}
					],
				}
			}
		},
		onReady() {
			this.$refs.uForm.setRules(this.rules);
		},
		onLoad(option) {
			this.form.phone = option.phone;
			this.form.vcode = option.vcode;
		},
		methods: {
			submit() {
				this.execLoading = true;
				this.$refs.uForm.validate(valid => {
					if (valid) {
						console.log('验证通过');
						this.$u.api.execChangePwd({ phone: this.form.phone, vcode: this.form.vcode, ...this.form }).then(res => {
							uni.redirectTo({
								url: '/pages/auth/forget/reset-completed'
							})
						}).finally(() => {
							this.execLoading = false;
						})
					} else {
						this.execLoading = false;
						console.log('验证失败');
					}
				});
			},
		}
	}
</script>

<style lang="scss" scoped>
.new-pwd-page {
	padding: 80rpx 70rpx;
	.reg-btn {
		padding-top: 105upx;
	}
	u-form-item {
	    margin-top: 22rpx;
	    display: block;
		::v-deep .u-form-item {
			padding-bottom: 0rpx;
			.u-form-item__message {
				padding-left: 0rpx !important;
				position: absolute;
				bottom: -39rpx;
				left: 0rpx;
				right: 0rpx;
				font-size: 22rpx;
			}
		}
		&:nth-last-child(1) {
			margin-top: 50rpx;
		}
	}
	.pwd-item {
		position: relative;
		.pwd-desc {
			position: absolute;
			bottom: -60rpx;
			left: 0rpx;
			right: 0rpx;
			font-size: 22rpx;
			color: #8B8B8B;
		}
	}
}
</style>
