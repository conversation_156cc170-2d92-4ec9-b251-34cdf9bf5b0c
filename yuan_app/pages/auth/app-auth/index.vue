<template>
	<view class="auth-page">
		<view class="auth-container">
			<!-- Logo区域 -->
			<view class="logo-section">
				<!-- <image class="logo" :src="logoUrl" mode="aspectFit"></image> -->
				<text class="app-name">与安宝</text>
			</view>
			
			<!-- 加载状态区域 -->
			<view class="loading-section">
				<view class="loading-spinner">
					<view class="spinner"></view>
				</view>
				<text class="loading-text">{{ loadingText }}</text>
			</view>
			
			<!-- 错误状态区域 -->
			<view v-if="showError" class="error-section">
				<view class="error-icon">⚠️</view>
				<text class="error-text">{{ errorMessage }}</text>
				<button class="retry-btn" @click="retryAuth">重试</button>
			</view>
		</view>
	</view>
</template>

<script>
	// 先注释掉tokenManager导入，测试基本页面是否能显示
	// import tokenManager from '@/utils/tokenManager.js';

	export default {
		data() {
			return {
				logoUrl: '/static/images/logo.png', // 替换为实际logo路径
				loadingText: '正在验证登录状态...',
				showError: false,
				errorMessage: '',
				authParams: null,
				retryCount: 0,
				maxRetries: 3
			};
		},
		
		async onLoad(option) {
			console.log('[AppAuth] 页面加载，参数:', option);
			this.authParams = option;

			// 添加调试信息
			console.log('[AppAuth] API是否存在:', !!this.$u);
			console.log('[AppAuth] API.api是否存在:', !!this.$u.api);
			if (this.$u.api) {
				console.log('[AppAuth] execCheckToken是否存在:', !!this.$u.api.execCheckToken);
			}

			// 如果没有token参数，显示错误
			if (!option.token) {
				this.showError = true;
				this.errorMessage = '缺少token参数，请从App重新登录';
				console.log('[AppAuth] 没有token，显示错误页面');
				return;
			}

			console.log('[AppAuth] 开始执行鉴权...');
			await this.performAuth();
		},
		
		methods: {
			// 执行鉴权流程
			async performAuth() {
				try {
					console.log('[AppAuth] 开始执行鉴权流程');
					this.showError = false;
					this.loadingText = '正在验证登录状态...';

					const { token, platform, appType } = this.authParams;
					console.log('[AppAuth] 解析参数 - token:', token ? '存在' : '不存在', 'platform:', platform, 'appType:', appType);

					if (!token) {
						throw new Error('缺少登录凭证');
					}

					// 校验token
					console.log('[AppAuth] 开始校验token，调用API...');
					console.log('[AppAuth] 当前baseURL:', this.$u.http.config.baseUrl);
					console.log('[AppAuth] 请求URL:', this.$u.http.config.baseUrl + '/pinanbao/checkToken');
					this.loadingText = '正在验证身份...';

					const res = await this.$u.api.execCheckToken({ content: token });
					console.log('[AppAuth] API调用完成，结果:', res);

					if (res) {
						// Token有效，保存用户信息
						console.log('[AppAuth] Token校验成功，保存用户信息');
						this.loadingText = '验证成功，正在跳转...';

						// 直接保存到storage，暂时不使用tokenManager
						uni.setStorageSync('token', token);
						uni.setStorageSync('member', res);
						console.log('[AppAuth] 用户信息已保存');

						// 延迟一下让用户看到成功状态
						await this.delay(800);

						// 构建跳转URL
						let indexUrl = '/pages/index/index';
						if (platform && appType) {
							indexUrl += `?platform=${platform}&appType=${appType}`;
						}
						console.log('[AppAuth] 准备跳转到:', indexUrl);

						// 跳转到首页
						uni.reLaunch({
							url: indexUrl
						});
					} else {
						console.log('[AppAuth] Token校验失败，res为空');
						throw new Error('登录凭证已失效');
					}
				} catch (error) {
					console.error('[AppAuth] 鉴权失败:', error);
					this.handleAuthError(error);
				}
			},
			
			// 处理鉴权错误
			handleAuthError(error) {
				this.showError = true;
				this.errorMessage = error.message || '验证失败，请重试';
				
				// 如果是网络错误且重试次数未达上限，自动重试
				if (this.isNetworkError(error) && this.retryCount < this.maxRetries) {
					setTimeout(() => {
						this.retryAuth();
					}, 2000);
				} else if (this.retryCount >= this.maxRetries) {
					this.errorMessage = '网络连接异常，请检查网络后重试';
				}
			},
			
			// 判断是否为网络错误
			isNetworkError(error) {
				const networkErrorKeywords = ['network', 'timeout', 'connection', '网络', '连接'];
				const errorMessage = (error.message || '').toLowerCase();
				return networkErrorKeywords.some(keyword => errorMessage.includes(keyword));
			},
			
			// 重试鉴权
			async retryAuth() {
				this.retryCount++;
				console.log(`[AppAuth] 重试鉴权，第${this.retryCount}次`);
				await this.performAuth();
			},
			
			// 延迟函数
			delay(ms) {
				return new Promise(resolve => setTimeout(resolve, ms));
			}
		}
	};
</script>

<style lang="scss" scoped>
	.auth-page {
		width: 100%;
		height: 100vh;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.auth-container {
		text-align: center;
		color: white;
	}
	
	.logo-section {
		margin-bottom: 80rpx;
		
		.logo {
			width: 120rpx;
			height: 120rpx;
			margin-bottom: 20rpx;
		}
		
		.app-name {
			display: block;
			font-size: 36rpx;
			font-weight: 500;
		}
	}
	
	.loading-section {
		margin-bottom: 60rpx;
		
		.loading-spinner {
			margin-bottom: 40rpx;
			
			.spinner {
				width: 60rpx;
				height: 60rpx;
				border: 4rpx solid rgba(255, 255, 255, 0.3);
				border-top: 4rpx solid white;
				border-radius: 50%;
				margin: 0 auto;
				animation: spin 1s linear infinite;
			}
		}
		
		.loading-text {
			font-size: 28rpx;
			opacity: 0.9;
		}
	}
	
	.error-section {
		.error-icon {
			font-size: 60rpx;
			margin-bottom: 20rpx;
		}
		
		.error-text {
			display: block;
			font-size: 28rpx;
			margin-bottom: 40rpx;
			opacity: 0.9;
		}
		
		.retry-btn {
			background: rgba(255, 255, 255, 0.2);
			color: white;
			border: 2rpx solid rgba(255, 255, 255, 0.5);
			border-radius: 50rpx;
			padding: 20rpx 60rpx;
			font-size: 28rpx;
			
			&:hover {
				background: rgba(255, 255, 255, 0.3);
			}
		}
	}
	
	@keyframes spin {
		0% { transform: rotate(0deg); }
		100% { transform: rotate(360deg); }
	}
</style>
