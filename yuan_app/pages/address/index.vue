<template>
	<view class="address-page">
		<view class="top-header-line"></view>

		<view class="top-wrap">
			<image class="img-center-bg" :src="`${staticUrl}/images/device/pic-address.jpg`"></image>
		</view>

		<view>
			<u-form :model="form" ref="uForm" label-position="top">
				<u-form-item label="安装地址" prop="address">
					<view class="reqired-icon">*</view>
					<u-input :value="form.address" placeholder="详细地址" disabled :clearable="false"
						:custom-style="{ 'color': '#8e8e8e' }" />
					<!-- <u-icon name="map-fill" class="map-icon"></u-icon> -->
					<text class="icon iconfont icon-dingwei map-icon expand-hotspot" style="margin-left: 10rpx;"
						@click="openMapSelectDialog"></text>
				</u-form-item>
				<u-form-item prop="houseNumber">
					<u-input v-model="form.houseNumber" placeholder="门牌号" :clearable="false"
						:custom-style="{ 'color': '#8e8e8e' }" />
				</u-form-item>
				<view class="sub-tip">注：地址填写到门牌号，否则将影响告警救援</view>
			</u-form>
		</view>

		<view class="footer-btns">
			<!-- <u-button type="primary" shape="circle" class="next-btn" @click="next">进入安装设备</u-button> -->
			<u-button shape="circle" class="next-btn diy-btn" size="medium"
				:custom-style="{ 'width': '100%', 'color': 'white', 'background-color': '#01B09A' }" hover-class="none"
				@click="next">{{ source === 'roomConfig' ? '保存' : '进入安装设备' }}</u-button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				staticUrl: this.$u.http.config.staticBaseUrl,
				form: {
					address: undefined,
					latitude: undefined,
					longitude: undefined,
					houseId: undefined,
					houseNumber: undefined
				},
				rules: {
					address: [{
						required: true,
						message: '请选择地址',
						trigger: ['blur']
					}]
				},
				source: '',
			}
		},
		//      onLoad() {//默认加载
		//          // this.login();
		// wx.hideHomeButton()
		//      },
		onLoad(option) {
			this.source = option.source
			this.fetchDeviceInfo();
		},
		methods: {
			next() {
				this.submitSave();
			},
			fetchDeviceInfo() {
				uni.showLoading({
					title: '加载中...',
					mask: true
				})
				this.$u.api.fetchDevInstallationInfo({
					devCode: uni.getStorageSync('devCode')
				}).then(res => {
					if (res) {
						this.form.address = res.houseAddr
						this.form.houseNumber = res.houseNumber
						this.form.latitude = res.latitude
						this.form.longitude = res.longitude
						this.form.houseId = res.houseId
					}
				}).catch(err => {
					this.deviceInfo = {}
				}).finally(() => {
					setTimeout(() => {
						uni.hideLoading();
					}, 1000);
				})
			},
			submitSave() {
				if (!this.form.address) {
					uni.showToast({
						duration: 2000,
						title: '请先选择安装地址',
						icon: 'none'
					})
					return;
				}
				if (!this.form.houseNumber) {
					uni.showToast({
						duration: 2000,
						title: '请填写具体的门牌号',
						icon: 'none'
					})
					return;
				}
				let _currFamily = uni.getStorageSync('curr_family') || {}
				let _params = {
					devCode: uni.getStorageSync('devCode'),
					houseAddr: this.form.address,
					houseNumber: this.form.houseNumber,
					latitude: this.form.latitude,
					longitude: this.form.longitude,
					houseId: this.form.houseId,
					familyId: _currFamily.id
				}
				uni.showLoading({
					title: '保存中...',
					mask: true
				})
				this.$u.api.execSaveDevInstallation(_params).then(res => {
					if (this.source === 'roomConfig') {
						uni.navigateBack({
							delta: 1
						})
					} else {
						uni.redirectTo({
							url: `/pagesDevice/config/step`
						})
					}
				}).catch(err => {
					console.log('err', err)
					uni.showToast({
						duration: 2000,
						title: res.message || '发生未知错误',
						icon: 'none'
					})
				}).finally(() => {
					setTimeout(() => {
						uni.hideLoading();
					}, 1000);
				})
			},
			openMapSelectDialog() {
				let _that = this;
				uni.chooseLocation({
					success: (res) => {
						console.log(res);
						console.log('位置名称：' + res.name);
						console.log('详细地址：' + res.address);
						console.log('纬度：' + res.latitude);
						console.log('经度：' + res.longitude);
						_that.form.address = res.address.indexOf(res.name) != -1 ? res.address : res.address +
							res.name;
						_that.form.latitude = res.latitude;
						_that.form.longitude = res.longitude;
						var point = new plus.maps.Point(res.longitude, res.latitude);
						plus.maps.Map.reverseGeocode(
							point, {},
							function(event) {
								var address = event.address; // 转换后的地理位置
								var point = event.coord; // 转换后的坐标信息
								var coordType = event.coordType; // 转换后的坐标系类型
								console.log(address, 'address');
							},
							function(e) {}
						);
					}
				});
			}
		}
	}
</script>

<style lang="scss">
	// #ifdef H5
	page {
		height: 100%;
	}

	// #endif
</style>
<style lang="scss" scoped>
	@import url("/static/css/iconfont.css");

	.address-page {
		height: 100vh;
		// #ifdef H5
		height: 100%;
		// #endif
		position: relative;
		padding: 60upx;
		box-sizing: border-box;
		text-align: center;

		.top-wrap {
			padding-top: 128rpx;

			.img-center-bg {
				width: 327rpx;
				height: 268rpx;
				margin: 0 auto;
			}

			.tip {
				font-size: 30upx;
				color: #5E5D5D;
				font-size: 30rpx;
				font-weight: 500;
				margin-top: 55rpx;
				font-weight: bold;
			}
		}

		.reqired-icon {
			color: red;
			display: inline-block;
			position: absolute;
			top: 18rpx;
			font-size: 43rpx;
			left: 0rpx;
			// #ifdef H5
			top: 19rpx;
			// #endif
		}

		.map-icon {
			display: inline-block;
			position: absolute;
			font-size: 43rpx;
			right: 0rpx;
			top: 86rpx;
			color: #01B09A;
			z-index: 2;
			// #ifdef H5
			top: 100rpx;
			// #endif
		}

		.sub-tip {
			margin-top: 14rpx;
			text-align: left;
			font-size: 26rpx;
			color: #333;
		}

		.footer-btns {
			position: absolute;
			bottom: 40rpx;
			left: 40rpx;
			right: 40rpx;
			margin: 0 auto;

			.next-btn {
				display: block;
				margin-top: 20rpx;
			}
		}

		::v-deep input {
			padding-right: 54rpx;
		}

		::v-deep .u-form-item {
			padding: 10rpx 0px;
		}

		::v-deep .u-form-item--left__content__label {
			padding-left: 30rpx;
		}
	}
</style>
