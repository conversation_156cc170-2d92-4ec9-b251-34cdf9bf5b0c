<template>
	<view class="my-setting-page">
		<view class="top-header-line"></view>
		<view class="item-block">
			<u-row @click="gotoUrl('/pages/auth/modifyPassword/step1')">
				<u-col span="8">
					<view class="left-info">
						<!-- <image src="../../static/images/my/icon-ward.png"></image> -->
						<view class="field-name" style="position: relative; top: 6rpx;">修改密码</view>
					</view>
				</u-col>
				<u-col span="4">
					<view class="right-status">
						<u-icon name="arrow-right" color="#b9b9b9" size="22"></u-icon>
					</view>
				</u-col>
			</u-row>
			<!-- <u-row @click="shareToWeChatMessage">
				<u-col span="8">
					<view class="left-info">
						<view class="field-name" style="position: relative; top: 6rpx;">test</view>
					</view>
				</u-col>
				<u-col span="4">
					<view class="right-status">
						<u-icon name="arrow-right" color="#b9b9b9" size="22"></u-icon>
					</view>
				</u-col>
			</u-row> -->
			<view style="margin-top: 30rpx;">
				<u-button shape="circle" class="next-btn diy-btn" size="medium"
				:custom-style="{ 'width': '100%', 'color': 'white', 'background-color': '#01B09A' }"  hover-class="none"
				@click="logout">退出登录</u-button>
			</view>
		</view>
	</view>
</template>

<script>
	// #ifdef (H5 || H5-HEJIA)
	import '../../static/common/js/hejia-latest.min.js';
	const {
		Hejia
	} = window;
	// #endif
	export default {
		data() {
			return {
				
			}
		},
		onLoad() {
			console.log('wx', wx)
		},
		methods: {
			shareToWeChatMessage() {
				this.$u.api.fetchGenScheme({ path: 'pages/invitation/index', query: 'id=1' }).then(res => {
					if (res) {
						const params = {
							"platformType": 1,
							"shareType": 2,
							"imageUrl": this.$u.http.config.staticBaseUrl + '/images/home/<USER>',
							"webPageUrl": res,
							"title": "加入我们一起关注老人健康",
						};
						// #ifdef (H5 || H5-HEJIA)
						Hejia.shareToWeChatMessage(params, (res) => {
							uni.showToast({
								duration: 2000,
								title: '分享成功',
								icon: 'none'
							})
						}, (res) => {
							uni.showToast({
								duration: 2000,
								title: res,
								icon: 'none'
							})
						});
						// #endif
					}
				})
			},
			gotoUrl(url){
				if (!url) return;
				uni.navigateTo({
					url: url
				})
			},
			logout() {
				// localStorage.clear();
				uni.removeStorageSync('member')
				uni.removeStorageSync('token')
				uni.removeStorageSync('curr_family')
				uni.removeStorageSync('devCode')
				uni.removeStorageSync('devId')
				uni.removeStorageSync('deviceType')
				uni.removeStorageSync('deviceId')		
				uni.removeStorageSync('cmcc_deviceType')	
				uni.removeStorageSync('cmcc_deviceId')	
				uni.reLaunch({
					url: '/pages/auth/login/index'
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
.my-setting-page {
	.item-block {
		background: white;
		padding: 24rpx 30rpx;
		color: $u-content-color;
		font-size: 28rpx;
		border-radius: 16rpx;
		u-row {
			display: block;
			line-height: 70rpx;
			border-bottom: 2rpx solid #E9E9E9;
			padding: 18rpx 0;
		}
		//#ifdef H5
		line-height: 50px;
		.u-row {
			border-bottom: 2rpx solid #E9E9E9;
		}
		//#endif
		.left-info {
			image {
				width: 40rpx;
				height: 40rpx;
				display: inline-block;
				margin-right: 20rpx;
				vertical-align: middle;
			}
			.field-name {
				display: inline-block;
				color: #454444;
				font-size: 32rpx;
				position: relative;
				top: 2rpx;
			}
		}
		.right-status {
			color: #b9b9b9;
			text-align: right;
			// margin-top: 28rpx;
			::v-deep .u-icon {
				margin-left: 10rpx;
				position: relative;
				top: 2rpx;
			}
		}
		&:nth-child(n + 2) {
			margin-top: 20rpx;
		}
	}
	.logout-btn {
		margin-top: 120rpx;
		display: block;
	}
}
</style>
