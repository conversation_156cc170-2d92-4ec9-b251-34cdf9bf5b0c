<template>
	<view class="my-user-center-page">
		<view class="top-header-line"></view>
		<view class="content-wrap">
			<u-field
				v-model="member.x2"
				disabled
				label="头像"
			>
				<view slot="right" style="width: 100%;">
					<!-- <span>{{ member.nickname || '' }}</span> -->
					<!-- #ifdef H5 -->
					<u-button shape="circle" type="primary" @click="wxGetUserInfo" 
						style="display: inline-block; vertical-align: middle; padding: 0; position: relative; right: 20rpx; top: -10rpx;" />
					<!-- #endif -->
					<!-- #ifndef H5 -->
					<u-button  shape="circle" type="primary" @click="wxGetUserInfo" >
					<!-- #endif -->
						<u-avatar
							mode="circle" 
							:size="90" 
							:src="member.profile || ''" 
							bgColor="#fcbd71"
						></u-avatar>
					</u-button>
					<!-- <u-icon name="arrow-right" color="#b9b9b9" size="30"></u-icon> -->
				</view>
			</u-field>
			
			<u-field
				v-model="member.name"
				label="姓名"
				placeholder="请输入" maxlength="50"
			>
			</u-field>
			<u-field @click="showIdcardTypeAction = true"
				v-model="dictFieldText.idcardType"
				:disabled="true" label="证件类型" placeholder="请选择"
				right-icon="arrow-down-fill"
			>
			</u-field>
			<u-action-sheet @click="onIdcardTypeAction" :list="dict.idcardType" :tips="idcardTypeTips" v-model="showIdcardTypeAction" ></u-action-sheet>
			<u-field
				v-model="member.idcardCode"
				label="证件号码"
				placeholder="请输入" maxlength="36"
			>
			</u-field>

			<u-field
				v-model="member.nickname"
				label="昵称"
				placeholder="请输入" maxlength="15"
			>
				<!-- <span slot="right">
					<u-icon name="arrow-right" color="#b9b9b9" size="22"></u-icon>
				</span> -->
			</u-field>
			<u-field
				v-model="member.phone"
				type="number"
				label="电话"
				disabled
				placeholder="请输入"
			>
				<!-- <span slot="right">
					<u-icon name="arrow-right" color="#b9b9b9" size="22"></u-icon>
				</span> -->
			</u-field>
		</view>
		
		<view class="footer-btns">
			<u-button shape="circle" class="next-btn diy-btn" size="medium" 
			:custom-style="{ 'width': '100%', 'color': 'white', 'background-color': '#01B09A' }"  hover-class="none"
			@click="handleSave">保存</u-button>
		</view>
		
	</view>
</template>

<script>
	import { dictTypesToCodeText } from "../../utils/sysDict";
	import {validateCnName,validateEnName} from "../../utils/util.js";
	export default {
		data() {
			return {
				member: {
					name: undefined,
					phone: undefined,
					addr: undefined,
					nickname: undefined,
					profile: undefined,
					idcardType: undefined,
					idcardCode: null,
				},

				dict: {
					idcardType: []
				},
				dictFieldText: {
					idcardType: undefined,
				},
				showIdcardTypeAction: false,
				idcardTypeTips: {
					text: '证件类型',
					color: '#909399',
					fontSize: 24
				},
			}
		},
		async onShow() {
			const dict = await this.$u.api.listDcitMapByTypes({ typeCodes: Object.keys(this.dict) });
			this.dict = dictTypesToCodeText(dict);
			
			this.$u.api.fetchMemberDetailInfo({}).then(res => {
				if (res.idcardType) {
					const dictItem = this.dict.idcardType.find(item => item.code == res.idcardType);
					this.dictFieldText.idcardType = dictItem.text;
				}
				this.member = res;
			})
		},
		methods: {
			wxGetUserInfo() {
				let _self = this;
				uni.getUserProfile({
					desc: '获取你的昵称、头像、地区及性别',
					success: res => {
						_self.member.profile = res.userInfo.avatarUrl
						_self.member.nickname = res.userInfo.nickName
					},
					fail: res => {
						//拒绝授权
						uni.showToast({
							title: '您拒绝了请求, 无法获取头像',
							icon: 'error',
							duration: 2000
						});
						return;
					}
				});
				// let _self = this;
				// uni.showModal({
				// 	title: '温馨提示',
				// 	content: '亲，授权微信获取头像',
				// 	success(res) {
				// 		console.log(0)
				// 		console.log(res)
				// 		//如果用户点击了确定按钮
				// 		if (res.confirm) {
				// 			uni.getUserProfile({
				// 				desc: '获取你的昵称、头像、地区及性别',
				// 				success: res => {
				// 					_self.member.profile = res.userInfo.avatarUrl
				// 					_self.member.nickname = res.userInfo.nickName
				// 				},
				// 				fail: res => {
				// 					//拒绝授权
				// 					uni.showToast({
				// 						title: '您拒绝了请求, 无法获取头像',
				// 						icon: 'error',
				// 						duration: 2000
				// 					});
				// 					return;
				// 				}
				// 			});
				// 		} else if (res.cancel) {
				// 			//如果用户点击了取消按钮
				// 			uni.showToast({
				// 				title: '您拒绝了请求, 无法获取头像',
				// 				icon: 'error',
				// 				duration: 2000
				// 			});
				// 			return;
				// 		}
				// 	}
				// });
				return false
			},
			isNumber(n) {
				return !isNaN(parseFloat(n)) && isFinite(n);
			},
			handleSave() {
				// if (!this.member.name || !this.member.phone) {
				// 	uni.showModal({ content: '请检查“*”必填项是否填写正确', showCancel: false, confirmText: '关闭' })
				// 	return;
				// }
				if(!validateCnName(this.member.name)&&!validateEnName(this.member.name)){
					uni.showModal({ content: '姓名只支持中/英文，中文2-10个字，英文50字符', showCancel: false, confirmText: '关闭' })
					return;
				}
				if (!this.isNumber(this.member.phone)) {
					uni.showModal({ content: '请检查手机号码是否正确', showCancel: false, confirmText: '关闭' })
					return;
				}

				/*
				if (!this.member.name) {
					uni.showModal({ content: '姓名必填', showCancel: false, confirmText: '关闭' });
					return;
				}
				if (!this.member.idcardType) {
					uni.showModal({ content: '证件类型必填', showCancel: false, confirmText: '关闭' });
					return;
				}
				if (!this.member.idcardCode) {;
					uni.showModal({ content: '证件号必填', showCancel: false, confirmText: '关闭' });
					return;
				}
				*/
				if (this.member.idcardCode) {
					if (!this.member.idcardType) {
						uni.showModal({ content: '请选择证件类型', showCancel: false, confirmText: '关闭' });
						return;
					}
					if (this.member.idcardType == "0" && !this.$u.test.idCard(this.member.idcardCode)) {
						uni.showModal({ content: '身份证号无效', showCancel: false, confirmText: '关闭' });
						return;
					} else {
						if (!this.$u.test.enOrNum(this.member.idcardCode)) {
							uni.showModal({ content: '证件号无效', showCancel: false, confirmText: '关闭' });
							return;
						}
					}
				}
				
				let _params = { ...this.member }
				uni.showLoading({
					title: '保存中...',
					mask: true
				})
				this.$u.api.execUpdateMyInfo(_params).then(res => {
					uni.setStorageSync('member', res);
					uni.showToast({ duration: 2000, title: '修改资料成功', icon: 'none' })
				}).catch(err => {
					console.log('err', err)
					uni.showToast({ duration: 2000, title: err.message || '发生未知错误', icon: 'none' })
				}).finally(() => {
					setTimeout(() => {
						uni.hideLoading();
					}, 1000)
				})
			},
			onIdcardTypeAction(index) {
				this.member.idcardType = this.dict.idcardType[index].code;
				this.dictFieldText.idcardType = this.dict.idcardType[index].text;
			}
		}
	}
</script>

<style lang="scss">
page {
	background: #fff;
}
</style>
<style lang="scss" scoped>
.my-user-center-page {
	padding: 29rpx;
	.avatar-wrap {
		margin-top: 40rpx;
		text-align: center;
		.user-name {
			margin-top: 20rpx;
		}
	}
	.icon-reqired {
		color: red;
		display: inline-block;
		vertical-align: middle;
		font-size: 40rpx;
		position: relative;
		top: 4rpx;
		left: 6rpx;
	}
	// ::v-deep .u-label {
	// 	font-size: 32rpx;
	// 	width: 250rpx;
	// 	display: block;
	// 	color: #0D0D0D;
	// 	flex: initial !important;
	// 	&::before {
	// 		font-size: 40rpx;
	// 		top: 10rpx;
	// 	}
	// 	.u-label-text {
	// 		padding-left: 20rpx;
	// 		font-weight: bold;
	// 		box-sizing: border-box;
	// 	}
	// }
	::v-deep .u-field {
		input {
			text-align: right;
		}
	}
	.direction-field {
		::v-deep .u-field-inner {
			height: 120rpx;
			align-items: baseline;
		}
	}
	.footer-btns {
		position: absolute;
		bottom: 40rpx;
		left: 40rpx;
		right: 40rpx;
		margin: 0 auto;
		.next-btn {
			display: block;
			margin-top: 20rpx;
		}
	}
	// ::v-deep .uni-easyinput__content {
	// 	padding: 14rpx 6rpx;
	// }
	::v-deep .u-model__footer__button {
		height: 88rpx;
		line-height: 88rpx;
	}
	::v-deep .u-field {
		padding: 28rpx;
	}
	
	.content-wrap {
		::v-deep u-button {
			button {
				outline: none; 
				background: transparent; 
				position: relative; 
				top: 14rpx;
				padding: 0rpx;
				right: 20rpx;
				&:hover {
					background: transparent !important; 
				}
			}
			&:hover {
				background: transparent !important; 
			}
		}
	}
}
</style>
