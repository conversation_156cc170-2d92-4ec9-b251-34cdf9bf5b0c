<template>
	<view class="bridge-test-page">
		<view class="header">
			<text class="title">通用双向通信架构测试</text>
			<text class="subtitle">验证新架构的功能和兼容性</text>
		</view>

		<view class="test-controls">
			<button 
				class="test-btn" 
				:disabled="isRunning" 
				@click="runAllTests"
			>
				{{ isRunning ? '测试运行中...' : '运行所有测试' }}
			</button>
			
			<button 
				class="test-btn secondary" 
				:disabled="isRunning" 
				@click="runVoIPTest"
			>
				测试VoIP调用
			</button>
			
			<button 
				class="test-btn secondary" 
				:disabled="isRunning" 
				@click="runNavigationTest"
			>
				测试页面导航
			</button>
		</view>

		<view class="test-results" v-if="testResults.length > 0">
			<view class="results-header">
				<text class="results-title">测试结果</text>
				<view class="results-summary">
					<text class="summary-item">总数: {{ totalTests }}</text>
					<text class="summary-item success">通过: {{ passedTests }}</text>
					<text class="summary-item error">失败: {{ failedTests }}</text>
					<text class="summary-item">成功率: {{ successRate }}%</text>
				</view>
			</view>

			<view class="results-list">
				<view 
					class="result-item" 
					v-for="(result, index) in testResults" 
					:key="index"
					:class="{ success: result.success, error: !result.success }"
				>
					<view class="result-header">
						<text class="result-icon">{{ result.success ? '✅' : '❌' }}</text>
						<text class="result-name">{{ result.name }}</text>
					</view>
					<text class="result-message">{{ result.message }}</text>
					<text class="result-time">{{ formatTime(result.timestamp) }}</text>
				</view>
			</view>
		</view>

		<view class="environment-info">
			<text class="info-title">环境信息</text>
			<view class="info-item">
				<text class="info-label">平台:</text>
				<text class="info-value">{{ platform }}</text>
			</view>
			<view class="info-item">
				<text class="info-label">在App中:</text>
				<text class="info-value">{{ isInApp ? '是' : '否' }}</text>
			</view>
			<view class="info-item">
				<text class="info-label">通信方式:</text>
				<text class="info-value">{{ communicationMethods.join(', ') }}</text>
			</view>
		</view>
	</view>
</template>

<script>
import bridgeTest from '../../utils/bridge/test/BridgeTest.js'
import VoIPService from '../../utils/services/VoIPService.js'
import NavigationService from '../../utils/services/NavigationService.js'
import AppServiceAdapter from '../../utils/bridge/AppServiceAdapter.js'

export default {
	name: 'BridgeTest',
	data() {
		return {
			isRunning: false,
			testResults: [],
			platform: 'unknown',
			isInApp: false,
			communicationMethods: []
		}
	},
	computed: {
		totalTests() {
			return this.testResults.length
		},
		passedTests() {
			return this.testResults.filter(r => r.success).length
		},
		failedTests() {
			return this.totalTests - this.passedTests
		},
		successRate() {
			if (this.totalTests === 0) return 0
			return ((this.passedTests / this.totalTests) * 100).toFixed(1)
		}
	},
	mounted() {
		this.loadEnvironmentInfo()
	},
	methods: {
		/**
		 * 加载环境信息
		 */
		loadEnvironmentInfo() {
			try {
				this.platform = AppServiceAdapter.bridge.platform
				this.isInApp = AppServiceAdapter.bridge.isInAppWebView()
				this.communicationMethods = AppServiceAdapter.bridge.communicationMethods.map(m => m.name)
			} catch (error) {
				console.error('加载环境信息失败:', error)
			}
		},

		/**
		 * 运行所有测试
		 */
		async runAllTests() {
			this.isRunning = true
			this.testResults = []

			try {
				await bridgeTest.runAllTests()
				this.testResults = bridgeTest.getTestResults()
			} catch (error) {
				console.error('运行测试失败:', error)
				uni.showToast({
					title: '测试失败: ' + error.message,
					icon: 'none'
				})
			} finally {
				this.isRunning = false
			}
		},

		/**
		 * 测试VoIP调用
		 */
		async runVoIPTest() {
			try {
				uni.showLoading({ title: '运行VoIP测试套件...' })

				// 导入VoIP测试类
				const VoIPTest = (await import('@/utils/bridge/test/VoIPTest.js')).default
				const test = new VoIPTest()

				// 运行所有测试
				const results = await test.runAllTests()

				uni.hideLoading()

				// 统计测试结果
				const passCount = results.filter(r => r.success).length
				const totalCount = results.length
				const successRate = ((passCount / totalCount) * 100).toFixed(1)

				uni.showModal({
					title: 'VoIP测试结果',
					content: `测试完成！\n通过: ${passCount}/${totalCount}\n成功率: ${successRate}%`,
					showCancel: false,
					confirmText: '查看详情',
					success: () => {
						// 显示详细结果
						console.log('VoIP测试详细结果:', results)
					}
				})

			} catch (error) {
				uni.hideLoading()
				uni.showToast({
					title: 'VoIP测试失败: ' + error.message,
					icon: 'none'
				})
				console.error('VoIP测试失败:', error)
			}
		},

		/**
		 * 测试单个VoIP呼叫
		 */
		async runSingleVoIPCall() {
			if (!this.isInApp) {
				uni.showToast({
					title: '请在App中测试VoIP功能',
					icon: 'none'
				})
				return
			}

			try {
				uni.showLoading({ title: '测试VoIP呼叫...' })

				// 测试VoIP调用
				const result = await VoIPService.makeCall({
					number: '10086',
					displayName: '测试呼叫'
				})

				uni.hideLoading()
				uni.showToast({
					title: 'VoIP调用测试成功',
					icon: 'success'
				})

				console.log('VoIP调用结果:', result)

			} catch (error) {
				uni.hideLoading()
				uni.showToast({
					title: 'VoIP调用失败: ' + error.message,
					icon: 'none'
				})
				console.error('VoIP调用测试失败:', error)
			}
		},

		/**
		 * 测试页面导航
		 */
		async runNavigationTest() {
			if (!this.isInApp) {
				uni.showToast({
					title: '请在App中测试导航功能',
					icon: 'none'
				})
				return
			}

			try {
				uni.showLoading({ title: '测试页面导航...' })

				// 测试页面跳转
				const result = await NavigationService.push({
					page: 'Settings',
					data: { from: 'bridge-test' }
				})

				uni.hideLoading()
				uni.showToast({
					title: '页面导航测试成功',
					icon: 'success'
				})

				console.log('页面导航结果:', result)

			} catch (error) {
				uni.hideLoading()
				uni.showToast({
					title: '页面导航失败: ' + error.message,
					icon: 'none'
				})
				console.error('页面导航测试失败:', error)
			}
		},

		/**
		 * 格式化时间
		 */
		formatTime(timestamp) {
			const date = new Date(timestamp)
			return date.toLocaleTimeString()
		}
	}
}
</script>

<style scoped>
.bridge-test-page {
	padding: 20rpx;
	background-color: #f5f5f5;
	min-height: 100vh;
}

.header {
	text-align: center;
	margin-bottom: 40rpx;
}

.title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 10rpx;
}

.subtitle {
	font-size: 28rpx;
	color: #666;
	display: block;
}

.test-controls {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
	margin-bottom: 40rpx;
}

.test-btn {
	background-color: #007aff;
	color: white;
	border: none;
	border-radius: 10rpx;
	padding: 20rpx;
	font-size: 32rpx;
}

.test-btn.secondary {
	background-color: #34c759;
}

.test-btn:disabled {
	background-color: #ccc;
}

.test-results {
	background-color: white;
	border-radius: 10rpx;
	padding: 20rpx;
	margin-bottom: 40rpx;
}

.results-header {
	margin-bottom: 20rpx;
}

.results-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 10rpx;
}

.results-summary {
	display: flex;
	gap: 20rpx;
	flex-wrap: wrap;
}

.summary-item {
	font-size: 24rpx;
	padding: 8rpx 16rpx;
	background-color: #f0f0f0;
	border-radius: 6rpx;
}

.summary-item.success {
	background-color: #e8f5e8;
	color: #34c759;
}

.summary-item.error {
	background-color: #ffeaea;
	color: #ff3b30;
}

.results-list {
	display: flex;
	flex-direction: column;
	gap: 15rpx;
}

.result-item {
	padding: 15rpx;
	border-radius: 8rpx;
	border-left: 6rpx solid #ddd;
}

.result-item.success {
	background-color: #f0fff0;
	border-left-color: #34c759;
}

.result-item.error {
	background-color: #fff0f0;
	border-left-color: #ff3b30;
}

.result-header {
	display: flex;
	align-items: center;
	gap: 10rpx;
	margin-bottom: 8rpx;
}

.result-icon {
	font-size: 24rpx;
}

.result-name {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
}

.result-message {
	font-size: 24rpx;
	color: #666;
	display: block;
	margin-bottom: 5rpx;
}

.result-time {
	font-size: 20rpx;
	color: #999;
	display: block;
}

.environment-info {
	background-color: white;
	border-radius: 10rpx;
	padding: 20rpx;
}

.info-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 15rpx;
}

.info-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 10rpx 0;
	border-bottom: 1rpx solid #eee;
}

.info-item:last-child {
	border-bottom: none;
}

.info-label {
	font-size: 28rpx;
	color: #666;
}

.info-value {
	font-size: 28rpx;
	color: #333;
	font-weight: bold;
}
</style>
