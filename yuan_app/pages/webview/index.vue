<template>
	<view>
		<web-view v-if="web3dUrl" :webview-styles="webviewStyles" :src="web3dUrl" @message="reciveMessage"></web-view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				web3dUrl: '',
				webviewStyles: {
					progress: {
						color: '#FF3333'
					}
				},
				//来源2d房间配置的话用来自动开启hotplace
				source:undefined,
				devId:undefined,
				devCode:undefined,
				extraData:undefined,
				ts:undefined,
				orderType:undefined,
			}
		},
		onLoad: function (option) {
			console.log("3d option:",option)
			this.web3dUrl = ''
			this.extraData = option.extraData;
			this.devCode = option.devCode;
			this.devId = option.id;
			this.ts = option.ts;
			this.orderType = option.orderType;
			this.fetchRealTimeMonitor();
		},
		methods: {
			async fetchRealTimeMonitor() {
				let url;
				if(this.extraData){
					url = await this.$u.api.fetchRealTimeMonitorUrlByCode({ devCode: this.devCode,extraData: this.extraData });
				}else{
					url = await this.$u.api.fetchRealTimeMonitorUrl({ devId: this.devId });
				}
				if(this.ts&&this.orderType){
					url = url+"&ts="+this.ts+"&orderType="+this.orderType+"&cpType=replay";
				}
				console.log("url:",url);
				this.web3dUrl = url;
			},
			reciveMessage(data) {
				uni.showToast({
					title: "reciveMessage接收到消息：" + JSON.stringify(data.detail),
					duration: 2000,
					icon: 'none'
				});
				console.log("接收到消息：" + JSON.stringify(data.detail));
				uni.setStorageSync('roomData',data.detail);
			}
		}
	}
</script>

<style>
</style>