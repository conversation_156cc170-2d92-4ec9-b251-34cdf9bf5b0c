/**
 * Token管理工具
 * 实现token缓存、静默校验和状态管理
 */

class TokenManager {
	constructor() {
		this.tokenKey = 'token';
		this.memberKey = 'member';
		this.tokenValidatedKey = 'token_validated_time';
		this.tokenValidationCacheKey = 'token_validation_cache';

		// Token校验缓存时间（毫秒）- 5分钟
		this.validationCacheTime = 5 * 60 * 1000;

		// Token有效期检查间隔（毫秒）- 30分钟
		this.tokenCheckInterval = 30 * 60 * 1000;
	}

	/**
	 * 获取当前token
	 */
	getToken() {
		return uni.getStorageSync(this.tokenKey);
	}

	/**
	 * 获取当前用户信息
	 */
	getMember() {
		return uni.getStorageSync(this.memberKey);
	}

	/**
	 * 设置token和用户信息
	 */
	setTokenAndMember(token, member) {
		uni.setStorageSync(this.tokenKey, token);
		uni.setStorageSync(this.memberK<PERSON>, member);
		// 记录token设置时间
		uni.setStorageSync(this.tokenValidatedKey, Date.now());
		// 清除旧的校验缓存
		uni.removeStorageSync(this.tokenValidationCacheKey);
	}

	/**
	 * 清除token和用户信息
	 */
	clearTokenAndMember() {
		uni.removeStorageSync(this.tokenKey);
		uni.removeStorageSync(this.memberKey);
		uni.removeStorageSync(this.tokenValidatedKey);
		uni.removeStorageSync(this.tokenValidationCacheKey);

		// 清除其他相关缓存
		uni.removeStorageSync('curr_family');
		uni.removeStorageSync('devCode');
		uni.removeStorageSync('devId');
		uni.removeStorageSync('deviceType');
		uni.removeStorageSync('deviceId');
		uni.removeStorageSync('cmcc_deviceType');
		uni.removeStorageSync('cmcc_deviceId');
	}

	/**
	 * 检查token是否需要校验
	 * 基于缓存时间判断
	 */
	needsValidation() {
		const token = this.getToken();
		if (!token) {
			return false; // 没有token，不需要校验
		}

		const lastValidated = uni.getStorageSync(this.tokenValidatedKey);
		const validationCache = uni.getStorageSync(this.tokenValidationCacheKey);

		if (!lastValidated) {
			return true; // 从未校验过
		}

		const timeSinceValidation = Date.now() - lastValidated;

		// 如果有有效的校验缓存且在缓存时间内，不需要重新校验
		if (validationCache && validationCache.valid && timeSinceValidation < this.validationCacheTime) {
			console.log('[TokenManager] Token在缓存有效期内，跳过校验');
			return false;
		}

		// 超过缓存时间或没有有效缓存，需要校验
		return timeSinceValidation >= this.validationCacheTime;
	}

	/**
	 * 静默校验token
	 * @param {Function} api - API调用函数
	 * @returns {Promise<boolean>} - 校验结果
	 */
	async silentValidateToken(api) {
		const token = this.getToken();
		if (!token) {
			console.log('[TokenManager] 没有token，跳过校验');
			return false;
		}

		if (!this.needsValidation()) {
			console.log('[TokenManager] Token不需要校验');
			return true;
		}

		try {
			console.log('[TokenManager] 开始静默校验token');
			const res = await api.execCheckToken({
				content: token
			});

			if (res) {
				// 校验成功，更新缓存
				uni.setStorageSync(this.tokenValidatedKey, Date.now());
				uni.setStorageSync(this.tokenValidationCacheKey, {
					valid: true,
					timestamp: Date.now()
				});

				// 更新用户信息（可能有变化）
				uni.setStorageSync(this.memberKey, res);

				console.log('[TokenManager] Token校验成功');
				return true;
			} else {
				// 校验失败，清除token
				console.log('[TokenManager] Token校验失败，清除本地数据');
				this.clearTokenAndMember();
				return false;
			}
		} catch (error) {
			console.error('[TokenManager] Token校验异常:', error);

			// 网络错误等情况，不清除token，但标记为需要重新校验
			uni.removeStorageSync(this.tokenValidationCacheKey);

			// 如果是token错误，清除本地数据
			if (error.message && error.message.indexOf('TOKEN WAS WRONG') !== -1) {
				this.clearTokenAndMember();
				return false;
			}

			// 其他错误，保持当前状态
			return true;
		}
	}

	/**
	 * 检查是否已登录（有有效token）
	 */
	isLoggedIn() {
		const token = this.getToken();
		const member = this.getMember();
		return !!(token && member);
	}

	/**
	 * 处理App跳转的token参数
	 * @param {Object} option - 页面参数
	 * @param {Function} api - API调用函数
	 * @returns {Promise<Object|null>} - 处理结果
	 */
	async handleAppTokenParams(option, api) {
		const {
			token,
			platform,
			appType
		} = option;

		if (!token) {
			return null;
		}

		console.log('[TokenManager] 检测到App传入的token，开始处理');

		try {
			// 校验新token
			const res = await api.execCheckToken({
				content: token
			});

			if (res) {
				// 新token有效，更新本地存储
				this.setTokenAndMember(token, res);

				console.log('[TokenManager] App token校验成功，已更新本地存储');

				return {
					success: true,
					member: res,
					platform,
					appType
				};
			} else {
				console.log('[TokenManager] App token校验失败');
				return {
					success: false,
					error: 'Token校验失败'
				};
			}
		} catch (error) {
			console.error('[TokenManager] App token校验异常:', error);
			return {
				success: false,
				error: error.message || 'Token校验异常'
			};
		}
	}

	/**
	 * 获取跳转URL，保留App环境参数
	 */
	getNavigationUrl(basePath, platform, appType) {
		let url = basePath;
		if (platform && appType) {
			const separator = basePath.includes('?') ? '&' : '?';
			url += `${separator}platform=${platform}&appType=${appType}`;
		}
		return url;
	}
}

// 创建单例实例
const tokenManager = new TokenManager();

export default tokenManager;