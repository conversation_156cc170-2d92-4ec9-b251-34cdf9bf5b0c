/**
 * APP与WebView通信桥接工具
 * 用于处理H5页面在APP的WebView中与原生APP的通信
 *
 * 注意：此文件保持向后兼容，新功能请使用 bridge/ 目录下的新架构
 *
 * @deprecated 建议使用新的通用架构：
 * - VoIPService: utils/services/VoIPService.js
 * - NavigationService: utils/services/NavigationService.js
 * - AppServiceAdapter: utils/bridge/AppServiceAdapter.js
 */

// 导入新架构的组件
import VoIPService from './services/VoIPService.js'
import NavigationService from './services/NavigationService.js'
import AppServiceAdapter from './bridge/AppServiceAdapter.js'

/**
 * 检查是否在APP的WebView环境中
 * @returns {boolean}
 */
export function isInAppWebView() {
	// 使用新架构的实现
	return AppServiceAdapter.bridge.isInAppWebView()
}

/**
 * 获取平台信息
 * @returns {string} 'iOS' | 'Android' | 'unknown'
 */
export function getPlatform() {
	// 使用新架构的实现
	return AppServiceAdapter.bridge.platform
}

/**
 * 调用APP的VoIP功能
 * @param {Object} params - 参数对象
 * @param {string} params.number - VoIP号码
 * @param {string} params.token - 用户token
 * @param {string} params.platform - 平台信息
 * @returns {Promise<boolean>} 是否成功调用
 * @deprecated 建议使用 VoIPService.makeCall() 方法
 */
export function callAppVoip(params) {
	// 使用新架构的VoIP服务，保持向后兼容
	const { number, token, platform } = params

	console.warn(`[appBridge] 使用已弃用的 callAppVoip 方法，建议使用 VoIPService.makeCall()`)
	console.log(`[appBridge] 调用APP VoIP功能 (兼容模式): number=${number}, platform=${platform}`)

	return VoIPService.makeCall({
		number,
		token,
		displayName: params.displayName
	}).then(result => {
		// 兼容旧版本返回格式
		return result && result.success !== false
	}).catch(error => {
		console.error('[appBridge] VoIP呼叫失败:', error)
		throw error
	})
}

/**
 * 获取VoIP服务状态
 * @returns {Promise<Object>} VoIP状态信息
 */
export async function getVoIPStatus() {
	try {
		return await VoIPService.getStatus()
	} catch (error) {
		console.error('[appBridge] 获取VoIP状态失败:', error)
		return {
			isLoggedIn: false,
			isInCall: false,
			error: error.message
		}
	}
}

/**
 * 检查VoIP服务是否可用
 * @returns {boolean} 是否可用
 */
export function isVoIPAvailable() {
	return VoIPService.isAvailable()
}

/**
 * 通用的APP功能调用
 * @param {string} action - 动作名称
 * @param {Object} data - 数据
 * @returns {Promise<any>} 调用结果
 * @deprecated 建议使用 AppServiceAdapter.bridge.sendMessage() 方法
 */
export function callAppFunction(action, data = {}) {
	console.log(`[appBridge] 调用APP功能 (兼容模式): action=${action}`, data)

	// 使用新架构的通用Bridge
	return AppServiceAdapter.bridge.sendMessage(action, data)
}

/**
 * 监听APP的消息回调
 * @param {Function} callback - 回调函数
 * @deprecated 建议使用 AppServiceAdapter.on() 方法
 */
export function listenAppMessage(callback) {
	console.log('[appBridge] 监听APP消息 (兼容模式)')

	// 使用新架构的事件监听
	AppServiceAdapter.on('notification', callback)
}

// 导出新架构的服务，方便业务代码使用
export { VoIPService, NavigationService, AppServiceAdapter }
