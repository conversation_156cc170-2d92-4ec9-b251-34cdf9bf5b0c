/**
 * 工具类
 */


/**
 * 格式化日期格式 (用于兼容ios Date对象)
 */
export const formatDate = (time) => {
	// 将xxxx-xx-xx的时间格式，转换为 xxxx/xx/xx的格式 
	return time.replace(/\-/g, "/");
}

/**
 * 对象转URL
 * @param {object} obj
 */
export const urlEncode = (obj = {}) => {
	const result = []
	for (const key in obj) {
		const item = obj[key]
		if (!item) {
			continue
		}
		if (isArray(item)) {
			item.forEach(val => {
				result.push(key + '=' + val)
			})
		} else {
			result.push(key + '=' + item)
		}
	}
	return result.join('&')
}

/**
 * 遍历对象
 */
export const objForEach = (obj, callback) => {
	Object.keys(obj).forEach((key) => {
		callback(obj[key], key)
	});
}

/**
 * 是否在数组内
 */
export const inArray = (search, array) => {
	for (var i in array) {
		if (array[i] == search) return true
	}
	return false
}

/**
 * 对Date的扩展，将 Date 转化为指定格式的String
 * 月(Y)、月(m)、日(d)、小时(H)、分(M)、秒(S) 可以用 1-2 个占位符，
 * 例子：
 * dateFormat('YYYY-mm-dd HH:MM:SS', new Date()) ==> 2020-01-01 08:00:00
 */
export const dateFormat = (fmt, date) => {
	const opt = {
		"Y+": date.getFullYear().toString(), // 年
		"m+": (date.getMonth() + 1).toString(), // 月
		"d+": date.getDate().toString(), // 日
		"H+": date.getHours().toString(), // 时
		"M+": date.getMinutes().toString(), // 分
		"S+": date.getSeconds().toString() // 秒
		// 有其他格式化字符需求可以继续添加，必须转化成字符串
	};
	let ret
	for (let k in opt) {
		ret = new RegExp("(" + k + ")").exec(fmt)
		if (ret) {
			fmt = fmt.replace(ret[1], (ret[1].length == 1) ? (opt[k]) : (opt[k].padStart(ret[1].length, "0")))
		};
	};
	return fmt
}

/**
 * 判断是否为空对象
 * @param {*} object 源对象
 */
export const isEmptyObject = (object) => {
	return Object.keys(object).length === 0
}

/**
 * 判断是否为对象
 * @param {*} object
 */
export const isObject = (object) => {
	return Object.prototype.toString.call(object) === '[object Object]'
}

/**
 * 判断是否为数组
 * @param {*} array
 */
export const isArray = (array) => {
	return Object.prototype.toString.call(array) === '[object Array]'
}

/**
 * 判断是否为空
 * @param {*} object 源对象
 */
export const isEmpty = (value) => {
	if (isArray(value)) {
		return value.length === 0
	}
	if (isObject(value)) {
		return isEmptyObject(value)
	}
	return !value
}

/**
 * 对象深拷贝
 * @param {*} obj 源对象
 */
export const cloneObj = (obj) => {
	let newObj = obj.constructor === Array ? [] : {};
	if (typeof obj !== 'object') {
		return;
	}
	for (let i in obj) {
		newObj[i] = typeof obj[i] === 'object' ? cloneObj(obj[i]) : obj[i];
	}
	return newObj
}

// 节流函数
// 思路： 第一次先设定一个变量true，
// 第二次执行这个函数时，会判断变量是否true，
// 是则返回。当第一次的定时器执行完函数最后会设定变量为flase。
// 那么下次判断变量时则为flase，函数会依次运行。
export function throttle(fn, delay = 100) {
	// 首先设定一个变量，在没有执行我们的定时器时为null
	var timer = null
	return function() {
		// 当我们发现这个定时器存在时，则表示定时器已经在运行中，需要返回
		if (timer) return
		timer = setTimeout(() => {
			fn.apply(this, arguments)
			timer = null
		}, delay)
	}
}

// 防抖函数
// 首次运行时把定时器赋值给一个变量， 第二次执行时，
// 如果间隔没超过定时器设定的时间则会清除掉定时器，
// 重新设定定时器， 依次反复， 当我们停止下来时，
// 没有执行清除定时器， 超过一定时间后触发回调函数。
// 参考文档：https://segmentfault.com/q/1010000021145192
export function debounce(fn, delay) {
	let timer
	return function() {
		const that = this
		const _args = arguments // 存一下传入的参数
		if (timer) {
			clearTimeout(timer)
		}
		timer = setTimeout(function() {
			fn.apply(that, _args)
		}, delay)
	}
}


/**
 * 数组交集
 * @param {Array} 数组1
 * @param {Array} 数组2
 * @return {Array}
 */
export const arrayIntersect = (array1, array2) => {
	return array1.filter(val => array2.indexOf(val) > -1)
}

/**
 * 获取时分秒
 * @param {Number} seconds 总秒数
 * @param {String} dateFormat 返回的日期格式，默认为'H:i:s'
 */
export const getSFM = (seconds, dateFormat = 'H:i:s') => {
	var obj = {};
	obj.H = Number.parseInt(seconds / 3600);
	obj.i = Number.parseInt((seconds - obj.H * 3600) / 60);
	obj.s = Number.parseInt(seconds - obj.H * 3600 - obj.i * 60);
	if (obj.H < 10) {
		obj.H = '0' + obj.H;
	}
	if (obj.i < 10) {
		obj.i = '0' + obj.i;
	}
	if (obj.s < 10) {
		obj.s = '0' + obj.s;
	}
	// 3.解析
	var rs = dateFormat.replace('H', obj.H).replace('i', obj.i).replace('s', obj.s);
	return rs;
}

/**
 * 获取时分秒
 * @param {Number} seconds 总秒数
 * @param {String} dateFormat 返回的日期格式，默认为'H:i'
 */
export const getSFMByM = (seconds) => {
	var obj = {};
	obj.H = Number.parseInt(seconds / 3600);
	obj.i = Number.parseInt((seconds - obj.H * 3600) / 60);
	obj.s = Number.parseInt(seconds - obj.H * 3600 - obj.i * 60);
	if (obj.H < 10) {
		obj.H = '0' + obj.H;
	}
	if (obj.i < 10) {
		obj.i = '0' + obj.i;
	}
	// 3.解析
	var rs = dateFormat.replace('H', obj.H).replace('i', obj.i);
	return rs;
}

/**
 * 长按复制到粘贴板
 */
export const longtapCopy = (value) => {
	uni.setClipboardData({
		data: value, //要被复制的内容
		success: () => { //复制成功的回调函数
			uni.showToast({ //提示
				title: '复制成功'
			})
		}
	});
}

/**
 * 复制到粘贴板
 */
export const copy = (value) => {
	uni.setClipboardData({
		data: value, //要被复制的内容
		success: () => { //复制成功的回调函数
			uni.showToast({ //提示
				title: '复制成功'
			})
		}
	});
}

/**
 * 长按打电话
 */
export const clickCall = (value) => {
	uni.makePhoneCall({
		phoneNumber: value, 
		success: () => { 
			console.log("拨号")
		}
	});
}

/***用于判断空，Undefined String Array Object Number boolean*/
export const isNull = (str) => {
	const type = typeof(str);
	if (Object.prototype.toString.call(str) === '[object Undefined]') {
		//空
		return true;
	} else if (Object.prototype.toString.call(str) === '[object String]' ||
		Object.prototype.toString.call(str) === '[object Array]'
	) {
		return (str.length == 0) ? true : false
	} else if (Object.prototype.toString.call(str) === '[object Object]') {
		return JSON.stringify(str) == '{}' ? true : false;
	} else if (type === 'number') {
		//Number型,数字0不算空
		if (str || str == 0) {
			return false;
		} else {
			return true;
		}
	} else if (type === 'boolean' || type === 'string') {
		if (!param) {
			return true;
		} else {
			return false;
		}
	} else {
		return true;
	}
}
// 处理 【a||'未设置'】这种值是0的情况下显示未设置
export const null2val = (val, nullVal) => {
	return isNull(val) ? nullVal : val;
}

/**
 * 姓名校验 由2-10位汉字组成
 * @param str
 * @returns {boolean}
 */
export function validateCnName(str) {
    const reg = /^[\u4e00-\u9fa5]{2,10}$/
    return reg.test(str);
}

/**
 * 英文姓名校验
 * @param str
 * @returns {boolean}
 */
export function validateEnName(str) {
	// 英文姓名的首字母一般都是大写
	//const reg = /^[A-Z][a-z]*(\s[A-Z][a-z]*)*$/
	// 不校验大小写
    const reg = /^[A-Za-z]*(\s[A-Za-z]*)*$/
	return reg.test(str);
}
/**
 * 数字和字母校验
 * @param str
 * @returns {boolean}
 */
export function validateCharNum(str) {
	const reg = /^[a-z0-9]+$/i
	return reg.test(str);
}

/**
 * 根据身份证获取出生日期
 * @param str
 * @returns {boolean}
 */
export function getBirthDate(idCard) {
    // 身份证号码的长度为15位或18位
    if (idCard.length === 15 || idCard.length === 18) {
        // 提取出生年月日
        var year, month, day;
        if (idCard.length === 15) {
            year = idCard.substring(6, 8);
            month = idCard.substring(8, 10);
            day = idCard.substring(10, 12);
        } else if (idCard.length === 18) {
            year = idCard.substring(6, 10);
            month = idCard.substring(10, 12);
            day = idCard.substring(12, 14);
        }
        // 返回出生年月字符串
        return year + "-" + month + "-" + day;
    } else {
        // 身份证号码长度不符合要求
		console.log("无法提取出生年月");
        return;
    }
}

/**
 * 去前后空格
 * @param str
 * @returns {boolean}
 */
export function trim(str) {
  return str.replace(/^\s+|\s+$/g, '');
}
/**
 * 是否为url,必须有http
 * @param str
 * @returns {boolean}
 */
export function isUrlNeedHttp(str) {
    var reg = /^https?:\/\/(([a-zA-Z0-9_-])+(\.)?)*(:\d+)?(\/((\.)?(\?)?=?&?[a-zA-Z0-9_-](\?)?)*)*$/i
    return reg.test(trim(str));
}

/**
 * 是否为url,可不含http
 * @param str
 * @returns {boolean}
 */
export function isUrl(str) {
    var reg =  /^(?:(?:https?|ftp):\/\/)?(?:www\.)?(?:[a-z0-9-]+\.)*[a-z0-9-]+\.[a-z]{2,}(?:\/[^\s]*)?$/i;
    return reg.test(trim(str));
}

/**
 * 获取url参数
 * @param str
 * @returns {boolean}
 */
export function getUrlParam(url, paramName) {
    // 用正则表达式获取参数值
    var reg = new RegExp("(^|&)" + paramName + "=([^&]*)(&|$)");
    var r = url.substr(url.indexOf("?") + 1).match(reg);
    // 如果参数存在，则返回参数值，否则返回空字符串
    if (r != null) {
        return decodeURIComponent(r[2]);
    } else {
        return "";
    }
}

// 获取时间如：1小时2分
export  function formatSecond2String(seconds) {  
    var hours = Math.floor(seconds / 3600); // 计算小时数
    var minutes = Math.floor((seconds % 3600) / 60); // 计算分钟数
    var secs = seconds % 60; // 计算剩余的秒数
   let result = ''; 
	if(hours){
		result += hours + "小时"
	}
	if(minutes){
		result += minutes + "分钟"
	}
	if(secs){
		result += secs + "秒"
	}
	
    return result ;
}

// 获取给定日期的前一天日期字符串
export function getPreviousDay(dateString) {
	if(!dateString){
		return '';
	}
	var date = new Date(dateString);
	date.setDate(date.getDate() - 1);
	return date.toISOString().split('T')[0];
}

// 获取给定日期的后一天日期字符串
export function getNextDay(dateString) {
	if(!dateString){
		return '';
	}
	var date = new Date(dateString);
	date.setDate(date.getDate() + 1);
	return date.toISOString().split('T')[0];
}

//2023-12-21字符串转成 12/21
export function convertToMonthDayFormat(dateString) {
	if(!dateString){
		return '';
	}
	var date = new Date(dateString);
	var month = date.getMonth() + 1;
	var day = date.getDate();
	return month + '/' + day;
}

/**
 * 日期转为日期区间中的数值
 * @param {Object} dataArray
 * @param {Object} timeMin
 * @param {Object} timeMax
 */
export function convertTimeToValues(dataArray, timeMin, timeMax) {
	if(!dataArray){
		return;
	}
	return dataArray.map(data => {
		const start = Math.floor((new Date(data.startX) - timeMin) / (timeMax - timeMin) * 1000);
		const end = Math.floor((new Date(data.endX) - timeMin) / (timeMax - timeMin) * 1000);
		return [
		    start,
		    end,
		    data.height
		];
	});
}
// 四舍五入保留n位小数点
export function roundToDecimal(num, n) {
	if(!num){
		return 0;
	}
    // var multiplier = Math.pow(10, n);
    // return Math.round(num * multiplier) / multiplier;
	return num.toFixed(n);
}
//从对象数组中获取这个属性(或数组下标)的最大值、最小值、平均值、中位值并 返回
export function calculateStats(objects, propertyName) {
  const propertyValues = objects.map(obj => obj[propertyName]);
  if(!propertyValues||propertyValues.length==0){
	  return {max:0,min:0,average:0,median:0}
  }
  const max = Math.max(...propertyValues);
  const min = Math.min(...propertyValues);
  const average = propertyValues.reduce((sum, value) => sum + value, 0) / propertyValues.length;
  const sortedValues = propertyValues.sort((a, b) => a - b);
  const median = sortedValues.length % 2 === 0 ? (sortedValues[sortedValues.length / 2] + sortedValues[sortedValues.length / 2 - 1]) / 2 : sortedValues[Math.floor(sortedValues.length / 2)];

  return {
    max,
    min,
    average:average.toFixed(0),
    median
  };
}

// 将时间字符串转换为分钟数以便比较
export function timeToMinutes(timeString) {
	const [hours, minutes] = timeString.split(':').map(Number);
	return hours * 60 + minutes;
}

export function limitTime(timeStr,maxTime,showModal) {
    // 比较输入时间和最大时间，并限制
    if (timeToMinutes(timeStr) > timeToMinutes(maxTime)) {
		if(showModal){
			uni.showModal({
				content: `最晚不迟于${maxTime},已改为${maxTime}`,
				showCancel: false,
				confirmText: '关闭'
			})
		}
        return maxTime;
    } else {
        return timeStr;
    }
}
