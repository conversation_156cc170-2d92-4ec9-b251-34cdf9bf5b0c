/**
 * VoIP业务服务
 * 封装VoIP相关的业务逻辑和App调用
 *
 * <AUTHOR>
 */

import AppServiceAdapter from '../bridge/AppServiceAdapter.js'
import { VoIPAction, MessageType } from '../bridge/MessageProtocol.js'

/**
 * VoIP呼叫状态枚举
 */
export const CallState = {
    IDLE: 'idle',
    CALLING: 'calling',
    RINGING: 'ringing',
    CONNECTED: 'connected',
    ENDED: 'ended',
    FAILED: 'failed'
}

/**
 * VoIP服务类
 * 提供VoIP相关的业务接口，支持呼叫管理和状态监听
 */
class VoIPService {
    constructor() {
        this.adapter = AppServiceAdapter
        this.isInitialized = false
        this.currentCallState = CallState.IDLE
        this.currentCall = null
        this.eventListeners = new Map()

        this.init()
    }

    /**
     * 初始化VoIP服务
     */
    init() {
        if (this.isInitialized) {
            return
        }

        // 监听VoIP相关的通知
        this.setupEventListeners()
        
        this.isInitialized = true
        console.log('[VoIPService] VoIP服务初始化完成')
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 监听来电通知
        this.adapter.on('notification', (message) => {
            if (message.action === VoIPAction.INCOMING_CALL) {
                this.handleIncomingCall(message.data)
            } else if (message.action === VoIPAction.CALL_ENDED) {
                this.handleCallEnded(message.data)
            } else if (message.action === VoIPAction.CALL_CONNECTED) {
                this.handleCallConnected(message.data)
            } else if (message.action === VoIPAction.CALL_FAILED) {
                this.handleCallFailed(message.data)
            }
        })
    }

    /**
     * 发起VoIP呼叫
     * @param {Object} params 呼叫参数
     * @param {string} params.number VoIP号码
     * @param {string} params.token 用户token (可选)
     * @param {string} params.displayName 显示名称 (可选)
     * @returns {Promise<Object>} 呼叫结果
     */
    async makeCall(params) {
        const { number, token, displayName } = params

        if (!number || typeof number !== 'string') {
            throw new Error('VoIP号码不能为空')
        }

        console.log(`[VoIPService] 发起VoIP呼叫: ${number}`)

        try {
            // 检查是否在App环境中
            if (!this.adapter.isServiceAvailable('voip')) {
                throw new Error('VoIP服务不可用，请在App中使用')
            }

            // 更新呼叫状态
            this.updateCallState(CallState.CALLING, {
                number,
                displayName,
                direction: 'outgoing',
                startTime: Date.now()
            })

            // 调用App的VoIP服务
            const result = await this.adapter.callService('voip', 'call', {
                number: number.trim(),
                token,
                displayName
            })

            console.log('[VoIPService] VoIP呼叫发起成功:', result)

            // 触发呼叫开始事件
            this.emit('callStarted', {
                number,
                displayName,
                callId: result.callId || null
            })

            return result

        } catch (error) {
            console.error('[VoIPService] VoIP呼叫失败:', error)
            throw error
        }
    }

    /**
     * 挂断电话
     * @returns {Promise<boolean>} 是否成功挂断
     */
    async hangupCall() {
        console.log('[VoIPService] 挂断电话')

        try {
            const result = await this.adapter.callService('voip', 'hangup')
            console.log('[VoIPService] 电话挂断成功:', result)
            return result
        } catch (error) {
            console.error('[VoIPService] 挂断电话失败:', error)
            throw error
        }
    }

    /**
     * 接听电话
     * @returns {Promise<boolean>} 是否成功接听
     */
    async answerCall() {
        console.log('[VoIPService] 接听电话')

        try {
            const result = await this.adapter.callService('voip', 'answer')
            console.log('[VoIPService] 电话接听成功:', result)
            return result
        } catch (error) {
            console.error('[VoIPService] 接听电话失败:', error)
            throw error
        }
    }

    /**
     * 拒绝电话
     * @returns {Promise<boolean>} 是否成功拒绝
     */
    async rejectCall() {
        console.log('[VoIPService] 拒绝电话')

        try {
            const result = await this.adapter.callService('voip', 'reject')
            console.log('[VoIPService] 电话拒绝成功:', result)
            return result
        } catch (error) {
            console.error('[VoIPService] 拒绝电话失败:', error)
            throw error
        }
    }

    /**
     * 检查VoIP服务是否可用
     * @returns {boolean} 是否可用
     */
    isAvailable() {
        return this.adapter.isServiceAvailable('voip')
    }

    /**
     * 获取VoIP状态
     * @returns {Promise<Object>} VoIP状态信息
     */
    async getStatus() {
        try {
            return await this.adapter.callService('voip', 'getStatus')
        } catch (error) {
            console.error('[VoIPService] 获取VoIP状态失败:', error)
            return {
                isLoggedIn: false,
                isInCall: false,
                error: error.message
            }
        }
    }

    /**
     * 更新呼叫状态
     * @param {string} state 新状态
     * @param {Object} callInfo 呼叫信息
     */
    updateCallState(state, callInfo = null) {
        const previousState = this.currentCallState
        this.currentCallState = state

        if (callInfo) {
            this.currentCall = {
                ...this.currentCall,
                ...callInfo,
                state
            }
        }

        console.log(`[VoIPService] 呼叫状态变更: ${previousState} -> ${state}`)

        // 触发状态变更事件
        this.emit('stateChanged', {
            previousState,
            currentState: state,
            callInfo: this.currentCall
        })
    }

    /**
     * 处理来电通知
     * @param {Object} data 来电数据
     */
    handleIncomingCall(data) {
        console.log('[VoIPService] 收到来电通知:', data)

        // 更新呼叫状态
        this.updateCallState(CallState.RINGING, {
            number: data.number,
            displayName: data.displayName,
            direction: 'incoming',
            startTime: Date.now()
        })

        // 触发来电事件
        this.emit('incomingCall', data)
    }

    /**
     * 处理通话结束通知
     * @param {Object} data 通话数据
     */
    handleCallEnded(data) {
        console.log('[VoIPService] 通话结束:', data)

        // 更新呼叫状态
        this.updateCallState(CallState.ENDED, {
            endTime: Date.now(),
            reason: data.reason || 'normal'
        })

        // 触发通话结束事件
        this.emit('callEnded', data)

        // 清理当前呼叫信息
        setTimeout(() => {
            this.currentCall = null
            this.updateCallState(CallState.IDLE)
        }, 1000)
    }

    /**
     * 处理通话连接通知
     * @param {Object} data 通话数据
     */
    handleCallConnected(data) {
        console.log('[VoIPService] 通话已连接:', data)

        // 更新呼叫状态
        this.updateCallState(CallState.CONNECTED, {
            connectedTime: Date.now()
        })

        // 触发通话连接事件
        this.emit('callConnected', data)
    }

    /**
     * 处理通话失败通知
     * @param {Object} data 失败数据
     */
    handleCallFailed(data) {
        console.log('[VoIPService] 通话失败:', data)

        // 更新呼叫状态
        this.updateCallState(CallState.FAILED, {
            endTime: Date.now(),
            error: data.error || '未知错误'
        })

        // 触发通话失败事件
        this.emit('callFailed', data)

        // 清理当前呼叫信息
        setTimeout(() => {
            this.currentCall = null
            this.updateCallState(CallState.IDLE)
        }, 2000)
    }

    /**
     * 事件发射器 - 简单实现
     */
    emit(event, data) {
        const listeners = this.eventListeners.get(event)
        if (listeners && listeners.length > 0) {
            listeners.forEach(callback => {
                try {
                    callback(data)
                } catch (error) {
                    console.error(`[VoIPService] 事件处理器错误 (${event}):`, error)
                }
            })
        }
    }

    /**
     * 添加事件监听器
     * @param {string} event 事件名称
     * @param {Function} callback 回调函数
     */
    on(event, callback) {
        if (typeof callback !== 'function') {
            throw new Error('回调函数不能为空')
        }

        if (!this.eventListeners.has(event)) {
            this.eventListeners.set(event, [])
        }

        this.eventListeners.get(event).push(callback)
        console.log(`[VoIPService] 添加事件监听器: ${event}`)
    }

    /**
     * 移除事件监听器
     * @param {string} event 事件名称
     * @param {Function} callback 回调函数
     */
    off(event, callback) {
        const listeners = this.eventListeners.get(event)
        if (listeners) {
            const index = listeners.indexOf(callback)
            if (index > -1) {
                listeners.splice(index, 1)
                console.log(`[VoIPService] 移除事件监听器: ${event}`)
            }
        }
    }

    /**
     * 移除所有事件监听器
     * @param {string} event 事件名称 (可选，不传则移除所有)
     */
    removeAllListeners(event = null) {
        if (event) {
            this.eventListeners.delete(event)
        } else {
            this.eventListeners.clear()
        }
        console.log(`[VoIPService] 清理事件监听器: ${event || '全部'}`)
    }

    /**
     * 获取当前呼叫状态
     * @returns {string} 当前状态
     */
    getCurrentState() {
        return this.currentCallState
    }

    /**
     * 获取当前呼叫信息
     * @returns {Object|null} 当前呼叫信息
     */
    getCurrentCall() {
        return this.currentCall
    }

    /**
     * 检查是否正在通话中
     * @returns {boolean} 是否在通话中
     */
    isInCall() {
        return [CallState.CALLING, CallState.RINGING, CallState.CONNECTED].includes(this.currentCallState)
    }

    /**
     * 销毁VoIP服务
     */
    destroy() {
        this.listeners = {}
        this.isInitialized = false
        console.log('[VoIPService] VoIP服务已销毁')
    }
}

// 创建单例实例
const voipService = new VoIPService()

export default voipService
