/**
 * 导航服务
 * 封装App页面导航相关的业务逻辑
 * 
 * <AUTHOR>
 */

import AppServiceAdapter from '../bridge/AppServiceAdapter.js'

/**
 * 导航服务类
 * 提供App页面导航的业务接口
 */
class NavigationService {
    constructor() {
        this.adapter = AppServiceAdapter
        this.isInitialized = false
        
        this.init()
    }

    /**
     * 初始化导航服务
     */
    init() {
        if (this.isInitialized) {
            return
        }

        this.isInitialized = true
        console.log('[NavigationService] 导航服务初始化完成')
    }

    /**
     * 跳转到指定页面
     * @param {Object} params 跳转参数
     * @param {string} params.page 页面名称或路径
     * @param {Object} params.data 传递的数据 (可选)
     * @param {boolean} params.animated 是否使用动画 (可选，默认true)
     * @param {string} params.transition 转场动画类型 (可选)
     * @returns {Promise<boolean>} 是否成功跳转
     */
    async push(params) {
        const { page, data = {}, animated = true, transition } = params

        if (!page || typeof page !== 'string') {
            throw new Error('页面名称不能为空')
        }

        console.log(`[NavigationService] 跳转到页面: ${page}`)

        try {
            // 检查导航服务是否可用
            if (!this.adapter.isServiceAvailable('navigation')) {
                throw new Error('导航服务不可用，请在App中使用')
            }

            const result = await this.adapter.callService('navigation', 'push', {
                page,
                data,
                animated,
                transition
            })

            console.log('[NavigationService] 页面跳转成功:', result)
            return result

        } catch (error) {
            console.error('[NavigationService] 页面跳转失败:', error)
            throw error
        }
    }

    /**
     * 返回上一页
     * @param {Object} params 返回参数
     * @param {boolean} params.animated 是否使用动画 (可选，默认true)
     * @param {Object} params.data 返回时传递的数据 (可选)
     * @returns {Promise<boolean>} 是否成功返回
     */
    async pop(params = {}) {
        const { animated = true, data } = params

        console.log('[NavigationService] 返回上一页')

        try {
            const result = await this.adapter.callService('navigation', 'pop', {
                animated,
                data
            })

            console.log('[NavigationService] 返回上一页成功:', result)
            return result

        } catch (error) {
            console.error('[NavigationService] 返回上一页失败:', error)
            throw error
        }
    }

    /**
     * 替换当前页面
     * @param {Object} params 替换参数
     * @param {string} params.page 新页面名称或路径
     * @param {Object} params.data 传递的数据 (可选)
     * @param {boolean} params.animated 是否使用动画 (可选，默认true)
     * @returns {Promise<boolean>} 是否成功替换
     */
    async replace(params) {
        const { page, data = {}, animated = true } = params

        if (!page || typeof page !== 'string') {
            throw new Error('页面名称不能为空')
        }

        console.log(`[NavigationService] 替换当前页面为: ${page}`)

        try {
            const result = await this.adapter.callService('navigation', 'replace', {
                page,
                data,
                animated
            })

            console.log('[NavigationService] 页面替换成功:', result)
            return result

        } catch (error) {
            console.error('[NavigationService] 页面替换失败:', error)
            throw error
        }
    }

    /**
     * 返回到根页面
     * @param {Object} params 返回参数
     * @param {boolean} params.animated 是否使用动画 (可选，默认true)
     * @returns {Promise<boolean>} 是否成功返回
     */
    async popToRoot(params = {}) {
        const { animated = true } = params

        console.log('[NavigationService] 返回到根页面')

        try {
            const result = await this.adapter.callService('navigation', 'popToRoot', {
                animated
            })

            console.log('[NavigationService] 返回根页面成功:', result)
            return result

        } catch (error) {
            console.error('[NavigationService] 返回根页面失败:', error)
            throw error
        }
    }

    /**
     * 跳转到VoIP呼叫页面
     * @param {Object} params 呼叫参数
     * @param {string} params.number VoIP号码
     * @param {string} params.displayName 显示名称 (可选)
     * @param {string} params.token 用户token (可选)
     * @returns {Promise<boolean>} 是否成功跳转
     */
    async pushVoIPCall(params) {
        const { number, displayName, token } = params

        return await this.push({
            page: 'VoIPCall',
            data: {
                number,
                displayName,
                token,
                autoCall: true
            }
        })
    }

    /**
     * 跳转到设置页面
     * @param {string} section 设置分区 (可选)
     * @returns {Promise<boolean>} 是否成功跳转
     */
    async pushSettings(section = null) {
        return await this.push({
            page: 'Settings',
            data: section ? { section } : {}
        })
    }

    /**
     * 跳转到用户资料页面
     * @param {string} userId 用户ID (可选，默认当前用户)
     * @returns {Promise<boolean>} 是否成功跳转
     */
    async pushUserProfile(userId = null) {
        return await this.push({
            page: 'UserProfile',
            data: userId ? { userId } : {}
        })
    }

    /**
     * 跳转到WebView页面
     * @param {Object} params WebView参数
     * @param {string} params.url 要加载的URL
     * @param {string} params.title 页面标题 (可选)
     * @param {boolean} params.showNavigationBar 是否显示导航栏 (可选，默认true)
     * @returns {Promise<boolean>} 是否成功跳转
     */
    async pushWebView(params) {
        const { url, title, showNavigationBar = true } = params

        if (!url || typeof url !== 'string') {
            throw new Error('URL不能为空')
        }

        return await this.push({
            page: 'WebView',
            data: {
                url,
                title,
                showNavigationBar
            }
        })
    }

    /**
     * 检查导航服务是否可用
     * @returns {boolean} 是否可用
     */
    isAvailable() {
        return this.adapter.isServiceAvailable('navigation')
    }

    /**
     * 获取当前页面信息
     * @returns {Promise<Object>} 当前页面信息
     */
    async getCurrentPage() {
        try {
            return await this.adapter.callService('navigation', 'getCurrentPage')
        } catch (error) {
            console.error('[NavigationService] 获取当前页面信息失败:', error)
            return {
                page: 'unknown',
                error: error.message
            }
        }
    }

    /**
     * 获取导航栈信息
     * @returns {Promise<Array>} 导航栈信息
     */
    async getNavigationStack() {
        try {
            return await this.adapter.callService('navigation', 'getNavigationStack')
        } catch (error) {
            console.error('[NavigationService] 获取导航栈失败:', error)
            return []
        }
    }

    /**
     * 销毁导航服务
     */
    destroy() {
        this.isInitialized = false
        console.log('[NavigationService] 导航服务已销毁')
    }
}

// 创建单例实例
const navigationService = new NavigationService()

export default navigationService
