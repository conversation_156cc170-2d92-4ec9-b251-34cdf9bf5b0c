/**
 * 跳转到外部小程序，去配网
 * @param {*} devCode 
 * @param {*} phone 
 */
export function gotoConfigWifi(devCode) {
	uni.navigateToMiniProgram({
		//appId: 'wx739a9c524b1c5aa1',
		appId: 'wx6d5e4dd85fa3338c',
		path: '/pages/index/index',
		extraData: {
			mdid: devCode
		},
		envVersion: __wxConfig.envVersion,
		success: res => {
			// 打开成功
			console.log("打开成功", res);
		},
		fail: err => {
			uni.showToast({
				duration: 2000,
				title: '无法配置WIFI及蓝牙信息，请重试',
				icon: 'none'
			})
		}
	});
}

/**
 * 【跳过】和【标定房间】
 * 跳转到外部小程序，房间标定
 * @param {*} devCode 
 * @param {*} phone 
 */
export function gotoRoomCalibration(devCode) {
	uni.navigateToMiniProgram({
		//appId: 'wx739a9c524b1c5aa1',
		appId: 'wx6d5e4dd85fa3338c',
		path: `/pages/guide/guide`,
		extraData: {
			mdid: devCode
		},
		envVersion: __wxConfig.envVersion,
		success: res => {
			// 打开成功
			console.log("打开成功", res);
		},
		fail: err => {
			uni.showToast({
				duration: 2000,
				title: '无法标定房间信息',
				icon: 'none'
			})
		}
	});
}