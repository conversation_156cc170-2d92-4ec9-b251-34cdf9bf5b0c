/* aladding, 字典列表转换为{code:xx, text:xx}对象列表 */
export function dictToCodeText(dicts) {
	if (dicts && dicts.length) {
		return dicts.map(({ dimCode: code, name: text }) => {
			return { code, text };
		});
	}
	return dicts;
}

/* aladding 把dictMap中的key作为字典类型，转换每个字典类型到{code:xx, text:xx}对像
	 例如：{typeCode: {dimCode:xx, name:xx}}转换为 {typeCode: {code:xx, text:xx}}*/
export function dictTypesToCodeText(dictMap) {
	const res = {};
	for (const key in dictMap) {
		res[key] = dictToCodeText(dictMap[key]);
	}
	return res;
}

export function dictToMap(dicts) {
	const res = {};
	if (dicts && dicts.length) {
		dicts.forEach(({ dimCode, name }) => {
			res[dimCode] = name;
		})
	}
	return res;
}
export function dictTypesToMap(dictMap) {
	const res = {};
	for (const key in dictMap) {
		res[key] = dictToMap(dictMap[key]);
	}
	return res;
}

/* aladding 把字典列表构建为树 */
export function buildTree(dicts, separator = "_", labelKey = "label", valueKey = "value") {
	if (!dicts?.length) {
		return [];
	}
	let groups = [], groupIndex = -1;
	dicts.forEach(item => {
		groupIndex = item.dimCode.split(separator).length - 1;
		item[labelKey] = item.name;
		item[valueKey] = item.dimCode;
		let group = groups[groupIndex];
		if (!group) {
			groups[groupIndex] = group = [];
		}
		group.push(item);
	});

	if (groups.length == 1) {
		return groups[0];
	}
	
	for (groupIndex = 1; groupIndex <= groups.length; groupIndex++) {
		let group = groups[groupIndex];
		let parentGroup = groups[groupIndex - 1];
		if (!group?.length || !parentGroup?.length) {
			continue;
		}
		group.forEach(child => {
			const parent = parentGroup.find(item => child.dimCode.startsWith(`${item.dimCode}${separator}`));
			if (parent) {
				let children = parent.children;
				if (!children) {
					parent.children = children = [];
				}
				children.push(child)
			}
		})
	}
	return groups[0];
}