/**
 * 通用双向通信架构测试
 * 用于验证新架构的功能和兼容性
 * 
 * <AUTHOR>
 */

import AppBridge from '../AppBridge.js'
import AppServiceAdapter from '../AppServiceAdapter.js'
import VoIPService from '../../services/VoIPService.js'
import NavigationService from '../../services/NavigationService.js'

/**
 * 桥接器测试类
 */
class BridgeTest {
    constructor() {
        this.testResults = []
        this.isRunning = false
    }

    /**
     * 运行所有测试
     */
    async runAllTests() {
        if (this.isRunning) {
            console.warn('[BridgeTest] 测试正在运行中...')
            return
        }

        this.isRunning = true
        this.testResults = []

        console.log('[BridgeTest] 开始运行通用双向通信架构测试')

        try {
            // 基础功能测试
            await this.testBasicFunctionality()
            
            // 消息协议测试
            await this.testMessageProtocol()
            
            // 服务适配器测试
            await this.testServiceAdapter()
            
            // VoIP服务测试
            await this.testVoIPService()
            
            // 导航服务测试
            await this.testNavigationService()
            
            // 向后兼容性测试
            await this.testBackwardCompatibility()
            
            // 错误处理测试
            await this.testErrorHandling()

        } catch (error) {
            console.error('[BridgeTest] 测试过程中发生错误:', error)
            this.addTestResult('测试异常', false, error.message)
        } finally {
            this.isRunning = false
            this.printTestResults()
        }
    }

    /**
     * 测试基础功能
     */
    async testBasicFunctionality() {
        console.log('[BridgeTest] 测试基础功能...')

        // 测试平台检测
        const platform = AppBridge.platform
        this.addTestResult('平台检测', platform !== 'unknown', `平台: ${platform}`)

        // 测试WebView环境检测
        const isInApp = AppBridge.isInAppWebView()
        this.addTestResult('WebView环境检测', typeof isInApp === 'boolean', `在App中: ${isInApp}`)

        // 测试通信方式检测
        const methods = AppBridge.communicationMethods
        this.addTestResult('通信方式检测', Array.isArray(methods), `检测到 ${methods.length} 种通信方式`)
    }

    /**
     * 测试消息协议
     */
    async testMessageProtocol() {
        console.log('[BridgeTest] 测试消息协议...')

        try {
            // 测试消息创建
            const message = AppBridge.messageProtocol.createMessage({
                type: 'request',
                action: 'test.ping',
                data: { test: true }
            })

            this.addTestResult('消息创建', !!message.id && message.action === 'test.ping', '消息格式正确')

            // 测试消息验证
            const isValid = AppBridge.messageProtocol.validateMessage(message)
            this.addTestResult('消息验证', isValid, '消息验证通过')

            // 测试action解析
            const { service, method } = AppBridge.messageProtocol.parseAction('test.ping')
            this.addTestResult('Action解析', service === 'test' && method === 'ping', `解析结果: ${service}.${method}`)

        } catch (error) {
            this.addTestResult('消息协议测试', false, error.message)
        }
    }

    /**
     * 测试服务适配器
     */
    async testServiceAdapter() {
        console.log('[BridgeTest] 测试服务适配器...')

        try {
            // 测试服务列表
            const services = AppServiceAdapter.getAvailableServices()
            this.addTestResult('服务列表获取', services.length > 0, `发现 ${services.length} 个服务`)

            // 测试服务可用性检查
            const voipAvailable = AppServiceAdapter.isServiceAvailable('voip')
            this.addTestResult('VoIP服务可用性', typeof voipAvailable === 'boolean', `VoIP服务: ${voipAvailable ? '可用' : '不可用'}`)

            // 测试方法可用性检查
            const callMethodAvailable = AppServiceAdapter.isMethodAvailable('voip', 'call')
            this.addTestResult('VoIP呼叫方法可用性', typeof callMethodAvailable === 'boolean', `呼叫方法: ${callMethodAvailable ? '可用' : '不可用'}`)

            // 测试状态获取
            const status = AppServiceAdapter.getStatus()
            this.addTestResult('适配器状态获取', !!status && typeof status === 'object', '状态信息正常')

        } catch (error) {
            this.addTestResult('服务适配器测试', false, error.message)
        }
    }

    /**
     * 测试VoIP服务
     */
    async testVoIPService() {
        console.log('[BridgeTest] 测试VoIP服务...')

        try {
            // 测试服务可用性
            const isAvailable = VoIPService.isAvailable()
            this.addTestResult('VoIP服务可用性检查', typeof isAvailable === 'boolean', `VoIP服务: ${isAvailable ? '可用' : '不可用'}`)

            // 如果在App环境中，测试实际调用（模拟）
            if (AppBridge.isInAppWebView()) {
                // 注意：这里只是测试调用接口，不会真正发起呼叫
                console.log('[BridgeTest] 在App环境中，跳过实际VoIP调用测试')
                this.addTestResult('VoIP调用接口测试', true, '在App环境中跳过实际调用')
            } else {
                // 在非App环境中测试错误处理
                try {
                    await VoIPService.makeCall({ number: '12345' })
                    this.addTestResult('VoIP错误处理测试', false, '应该抛出错误但没有')
                } catch (error) {
                    this.addTestResult('VoIP错误处理测试', true, '正确抛出错误: ' + error.message)
                }
            }

        } catch (error) {
            this.addTestResult('VoIP服务测试', false, error.message)
        }
    }

    /**
     * 测试导航服务
     */
    async testNavigationService() {
        console.log('[BridgeTest] 测试导航服务...')

        try {
            // 测试服务可用性
            const isAvailable = NavigationService.isAvailable()
            this.addTestResult('导航服务可用性检查', typeof isAvailable === 'boolean', `导航服务: ${isAvailable ? '可用' : '不可用'}`)

            // 测试导航方法（在非App环境中应该失败）
            if (!AppBridge.isInAppWebView()) {
                try {
                    await NavigationService.push({ page: 'test' })
                    this.addTestResult('导航错误处理测试', false, '应该抛出错误但没有')
                } catch (error) {
                    this.addTestResult('导航错误处理测试', true, '正确抛出错误: ' + error.message)
                }
            } else {
                this.addTestResult('导航服务测试', true, '在App环境中跳过实际导航测试')
            }

        } catch (error) {
            this.addTestResult('导航服务测试', false, error.message)
        }
    }

    /**
     * 测试向后兼容性
     */
    async testBackwardCompatibility() {
        console.log('[BridgeTest] 测试向后兼容性...')

        try {
            // 导入原有的appBridge模块
            const { isInAppWebView, getPlatform, callAppVoip, callAppFunction } = await import('../../appBridge.js')

            // 测试原有接口是否仍然可用
            const isInApp = isInAppWebView()
            this.addTestResult('原有isInAppWebView接口', typeof isInApp === 'boolean', '接口正常工作')

            const platform = getPlatform()
            this.addTestResult('原有getPlatform接口', typeof platform === 'string', `平台: ${platform}`)

            // 测试原有VoIP调用接口（在非App环境中应该失败）
            if (!AppBridge.isInAppWebView()) {
                try {
                    await callAppVoip({ number: '12345', platform: 'iOS' })
                    this.addTestResult('原有VoIP接口兼容性', false, '应该抛出错误但没有')
                } catch (error) {
                    this.addTestResult('原有VoIP接口兼容性', true, '正确抛出错误，兼容性良好')
                }
            } else {
                this.addTestResult('原有VoIP接口兼容性', true, '在App环境中跳过实际调用测试')
            }

        } catch (error) {
            this.addTestResult('向后兼容性测试', false, error.message)
        }
    }

    /**
     * 测试错误处理
     */
    async testErrorHandling() {
        console.log('[BridgeTest] 测试错误处理...')

        try {
            // 测试无效消息处理
            try {
                await AppBridge.sendMessage('', {})
                this.addTestResult('空action错误处理', false, '应该抛出错误但没有')
            } catch (error) {
                this.addTestResult('空action错误处理', true, '正确处理空action')
            }

            // 测试不存在的服务调用
            try {
                await AppServiceAdapter.callService('nonexistent', 'method')
                this.addTestResult('不存在服务错误处理', false, '应该抛出错误但没有')
            } catch (error) {
                this.addTestResult('不存在服务错误处理', true, '正确处理不存在的服务')
            }

            // 测试超时处理
            const timeoutPromise = new Promise((resolve, reject) => {
                setTimeout(() => reject(new Error('测试超时')), 100)
            })

            try {
                await timeoutPromise
                this.addTestResult('超时处理测试', false, '超时测试失败')
            } catch (error) {
                this.addTestResult('超时处理测试', true, '超时处理正常')
            }

        } catch (error) {
            this.addTestResult('错误处理测试', false, error.message)
        }
    }

    /**
     * 添加测试结果
     */
    addTestResult(testName, success, message) {
        this.testResults.push({
            name: testName,
            success,
            message,
            timestamp: new Date().toISOString()
        })

        const status = success ? '✅' : '❌'
        console.log(`[BridgeTest] ${status} ${testName}: ${message}`)
    }

    /**
     * 打印测试结果
     */
    printTestResults() {
        const totalTests = this.testResults.length
        const passedTests = this.testResults.filter(r => r.success).length
        const failedTests = totalTests - passedTests

        console.log('\n[BridgeTest] ==================== 测试结果汇总 ====================')
        console.log(`[BridgeTest] 总测试数: ${totalTests}`)
        console.log(`[BridgeTest] 通过: ${passedTests}`)
        console.log(`[BridgeTest] 失败: ${failedTests}`)
        console.log(`[BridgeTest] 成功率: ${((passedTests / totalTests) * 100).toFixed(1)}%`)

        if (failedTests > 0) {
            console.log('\n[BridgeTest] 失败的测试:')
            this.testResults.filter(r => !r.success).forEach(result => {
                console.log(`[BridgeTest] ❌ ${result.name}: ${result.message}`)
            })
        }

        console.log('[BridgeTest] ====================================================\n')

        return {
            total: totalTests,
            passed: passedTests,
            failed: failedTests,
            successRate: (passedTests / totalTests) * 100,
            results: this.testResults
        }
    }

    /**
     * 获取测试结果
     */
    getTestResults() {
        return this.testResults
    }
}

// 创建全局测试实例
const bridgeTest = new BridgeTest()

// 导出测试类和实例
export { BridgeTest, bridgeTest }
export default bridgeTest
