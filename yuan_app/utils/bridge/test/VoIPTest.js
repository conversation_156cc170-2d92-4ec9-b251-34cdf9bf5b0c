/**
 * VoIP功能测试
 * 测试重构后的VoIP通信架构
 * 
 * <AUTHOR>
 */

import VoIPService, { CallState } from '../../services/VoIPService.js'
import { VoIPAction } from '../MessageProtocol.js'

/**
 * VoIP测试类
 */
class VoIPTest {
    constructor() {
        this.testResults = []
        this.voipService = VoIPService
    }

    /**
     * 运行所有测试
     */
    async runAllTests() {
        console.log('[VoIPTest] 开始VoIP功能测试')
        
        const tests = [
            this.testServiceInitialization,
            this.testCallStateManagement,
            this.testEventListeners,
            this.testVoIPCall,
            this.testErrorHandling,
            this.testCompatibility
        ]

        for (const test of tests) {
            try {
                await test.call(this)
            } catch (error) {
                console.error(`[VoIPTest] 测试失败: ${test.name}`, error)
                this.addTestResult(test.name, false, error.message)
            }
        }

        this.printTestResults()
        return this.testResults
    }

    /**
     * 测试服务初始化
     */
    async testServiceInitialization() {
        console.log('[VoIPTest] 测试服务初始化')
        
        // 检查服务是否正确初始化
        if (!this.voipService.isInitialized) {
            throw new Error('VoIP服务未正确初始化')
        }

        // 检查初始状态
        if (this.voipService.getCurrentState() !== CallState.IDLE) {
            throw new Error('初始状态应该是IDLE')
        }

        // 检查当前呼叫信息
        if (this.voipService.getCurrentCall() !== null) {
            throw new Error('初始时不应该有当前呼叫')
        }

        this.addTestResult('testServiceInitialization', true, '服务初始化正常')
    }

    /**
     * 测试呼叫状态管理
     */
    async testCallStateManagement() {
        console.log('[VoIPTest] 测试呼叫状态管理')
        
        // 测试状态更新
        this.voipService.updateCallState(CallState.CALLING, {
            number: '10086',
            direction: 'outgoing'
        })

        if (this.voipService.getCurrentState() !== CallState.CALLING) {
            throw new Error('状态更新失败')
        }

        if (!this.voipService.isInCall()) {
            throw new Error('isInCall()应该返回true')
        }

        // 恢复初始状态
        this.voipService.updateCallState(CallState.IDLE)
        this.voipService.currentCall = null

        this.addTestResult('testCallStateManagement', true, '状态管理正常')
    }

    /**
     * 测试事件监听器
     */
    async testEventListeners() {
        console.log('[VoIPTest] 测试事件监听器')
        
        let eventReceived = false
        const testCallback = (data) => {
            eventReceived = true
            console.log('收到测试事件:', data)
        }

        // 添加监听器
        this.voipService.on('testEvent', testCallback)

        // 触发事件
        this.voipService.emit('testEvent', { test: true })

        // 检查事件是否被接收
        if (!eventReceived) {
            throw new Error('事件监听器未正常工作')
        }

        // 移除监听器
        this.voipService.off('testEvent', testCallback)

        this.addTestResult('testEventListeners', true, '事件监听器正常')
    }

    /**
     * 测试VoIP呼叫功能
     */
    async testVoIPCall() {
        console.log('[VoIPTest] 测试VoIP呼叫功能')
        
        // 检查服务可用性
        const isAvailable = this.voipService.isAvailable()
        console.log('VoIP服务可用性:', isAvailable)

        // 在非App环境中，服务不可用是正常的
        if (!isAvailable) {
            console.log('[VoIPTest] 非App环境，跳过实际呼叫测试')
            this.addTestResult('testVoIPCall', true, '非App环境，服务不可用（正常）')
            return
        }

        try {
            // 尝试发起呼叫
            const result = await this.voipService.makeCall({
                number: '10086',
                displayName: '测试呼叫'
            })

            console.log('呼叫结果:', result)
            this.addTestResult('testVoIPCall', true, '呼叫功能正常')
        } catch (error) {
            // 在测试环境中，呼叫失败是预期的
            console.log('呼叫失败（预期）:', error.message)
            this.addTestResult('testVoIPCall', true, '呼叫功能测试完成（预期失败）')
        }
    }

    /**
     * 测试错误处理
     */
    async testErrorHandling() {
        console.log('[VoIPTest] 测试错误处理')
        
        try {
            // 测试无效参数
            await this.voipService.makeCall({})
            throw new Error('应该抛出参数错误')
        } catch (error) {
            if (!error.message.includes('号码不能为空')) {
                throw new Error('错误处理不正确')
            }
        }

        try {
            // 测试无效号码
            await this.voipService.makeCall({ number: '' })
            throw new Error('应该抛出号码错误')
        } catch (error) {
            if (!error.message.includes('号码不能为空')) {
                throw new Error('错误处理不正确')
            }
        }

        this.addTestResult('testErrorHandling', true, '错误处理正常')
    }

    /**
     * 测试向后兼容性
     */
    async testCompatibility() {
        console.log('[VoIPTest] 测试向后兼容性')
        
        try {
            // 测试旧版本接口
            const { callAppVoip } = await import('../../appBridge.js')
            
            // 在非App环境中调用应该失败
            try {
                await callAppVoip({
                    number: '10086',
                    platform: 'iOS'
                })
            } catch (error) {
                // 预期的错误
                console.log('兼容接口错误（预期）:', error.message)
            }

            this.addTestResult('testCompatibility', true, '向后兼容性正常')
        } catch (error) {
            throw new Error('兼容性测试失败: ' + error.message)
        }
    }

    /**
     * 添加测试结果
     */
    addTestResult(testName, success, message) {
        this.testResults.push({
            test: testName,
            success,
            message,
            timestamp: new Date().toISOString()
        })
    }

    /**
     * 打印测试结果
     */
    printTestResults() {
        console.log('\n[VoIPTest] 测试结果汇总:')
        console.log('=' * 50)
        
        let passCount = 0
        let failCount = 0

        this.testResults.forEach(result => {
            const status = result.success ? '✅ PASS' : '❌ FAIL'
            console.log(`${status} ${result.test}: ${result.message}`)
            
            if (result.success) {
                passCount++
            } else {
                failCount++
            }
        })

        console.log('=' * 50)
        console.log(`总计: ${this.testResults.length} 个测试`)
        console.log(`通过: ${passCount} 个`)
        console.log(`失败: ${failCount} 个`)
        console.log(`成功率: ${((passCount / this.testResults.length) * 100).toFixed(1)}%`)
    }
}

// 导出测试类
export default VoIPTest

// 如果直接运行此文件，执行测试
if (typeof window !== 'undefined' && window.location) {
    // 在浏览器环境中
    const test = new VoIPTest()
    test.runAllTests().then(() => {
        console.log('[VoIPTest] 所有测试完成')
    })
}
