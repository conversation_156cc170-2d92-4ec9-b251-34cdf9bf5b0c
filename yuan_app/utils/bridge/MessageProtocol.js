/**
 * 消息协议定义
 * 负责消息的格式化、验证和序列化
 *
 * <AUTHOR>
 */

/**
 * 消息类型枚举
 */
export const MessageType = {
    REQUEST: 'request',
    RESPONSE: 'response',
    NOTIFICATION: 'notification'
}

/**
 * 支持的服务类型
 */
export const ServiceType = {
    VOIP: 'voip',
    NAVIGATION: 'navigation',
    SYSTEM: 'system',
    USER: 'user',
    DEVICE: 'device',
    STORAGE: 'storage',
    WIFI: 'wifi'
}

/**
 * VoIP相关动作
 */
export const VoIPAction = {
    CALL: 'voip.call',
    ANSWER: 'voip.answer',
    REJECT: 'voip.reject',
    HANGUP: 'voip.hangup',
    GET_STATUS: 'voip.getStatus',
    // 通知类型
    INCOMING_CALL: 'voip.incomingCall',
    CALL_CONNECTED: 'voip.callConnected',
    CALL_ENDED: 'voip.callEnded',
    CALL_FAILED: 'voip.callFailed'
}

/**
 * WiFi配置相关动作
 */
export const WiFiAction = {
    OPEN_CONFIG: 'wifi.openConfig',
    GET_STATUS: 'wifi.getStatus',
    // 通知类型
    CONFIG_STARTED: 'wifi.configStarted',
    CONFIG_COMPLETED: 'wifi.configCompleted',
    CONFIG_FAILED: 'wifi.configFailed',
    CONFIG_CANCELLED: 'wifi.configCancelled'
}

/**
 * 消息协议类
 * 定义标准的消息格式和处理方法
 */
class MessageProtocol {
    constructor() {
        this.version = '1.0'
        this.messageIdCounter = 0
        this.supportedActions = new Set([
            ...Object.values(VoIPAction),
            ...Object.values(WiFiAction),
            'navigation.push',
            'navigation.pop',
            'navigation.replace',
            'system.getVersion',
            'system.openSettings',
            'user.getInfo',
            'device.getInfo',
            'storage.get',
            'storage.set'
        ])
    }

    /**
     * 创建标准消息
     * @param {Object} options 消息选项
     * @param {string} options.type 消息类型: 'request' | 'response' | 'notification'
     * @param {string} options.action 动作名称 (如: 'voip.call', 'navigation.push')
     * @param {Object} options.data 消息数据
     * @param {string} options.platform 平台信息
     * @param {string} options.id 消息ID (可选，自动生成)
     * @returns {Object} 标准消息对象
     */
    createMessage(options) {
        if (!options || typeof options !== 'object') {
            throw new Error('消息选项不能为空')
        }

        const {
            type = MessageType.REQUEST,
            action,
            data = {},
            platform = 'unknown',
            id = this.generateMessageId()
        } = options

        // 验证必需参数
        if (!action || typeof action !== 'string') {
            throw new Error('动作名称不能为空')
        }

        // 验证消息类型
        if (!Object.values(MessageType).includes(type)) {
            throw new Error(`不支持的消息类型: ${type}`)
        }

        // 验证动作是否支持
        if (!this.supportedActions.has(action)) {
            console.warn(`[MessageProtocol] 未知的动作: ${action}`)
        }

        const message = {
            id,
            type,
            action,
            data: this.sanitizeData(data),
            timestamp: Date.now(),
            platform,
            version: this.version
        }

        // 验证消息格式
        if (!this.validateMessage(message)) {
            throw new Error('创建的消息格式无效')
        }

        return message
    }

    /**
     * 创建VoIP呼叫消息
     * @param {Object} params 呼叫参数
     * @param {string} params.number VoIP号码
     * @param {string} params.token 用户token (可选)
     * @param {string} params.displayName 显示名称 (可选)
     * @returns {Object} VoIP呼叫消息
     */
    createVoIPCallMessage(params) {
        const { number, token, displayName } = params

        if (!number || typeof number !== 'string') {
            throw new Error('VoIP号码不能为空')
        }

        return this.createMessage({
            type: MessageType.REQUEST,
            action: VoIPAction.CALL,
            data: {
                number: number.trim(),
                token: token || null,
                displayName: displayName || null
            }
        })
    }

    /**
     * 创建WiFi配置消息
     * @param {Object} params 配置参数
     * @param {string} params.devCode 设备编码
     * @param {string} params.token 用户token (可选)
     * @returns {Object} WiFi配置消息
     */
    createWiFiConfigMessage(params) {
        const { devCode, token } = params

        if (!devCode || typeof devCode !== 'string') {
            throw new Error('设备编码不能为空')
        }

        return this.createMessage({
            type: MessageType.REQUEST,
            action: WiFiAction.OPEN_CONFIG,
            data: {
                devCode: devCode.trim(),
                token: token || null
            }
        })
    }

    /**
     * 创建响应消息
     * @param {string} requestId 请求ID
     * @param {boolean} success 是否成功
     * @param {Object} data 响应数据 (可选)
     * @param {string} error 错误信息 (可选)
     * @returns {Object} 响应消息
     */
    createResponse(requestId, success, data = null, error = null) {
        if (!requestId) {
            throw new Error('请求ID不能为空')
        }

        return {
            requestId,
            success: Boolean(success),
            data: success ? this.sanitizeData(data) : null,
            error: success ? null : (error || '未知错误'),
            timestamp: Date.now(),
            version: this.version
        }
    }

    /**
     * 创建通知消息
     * @param {string} action 动作名称
     * @param {Object} data 通知数据
     * @returns {Object} 通知消息
     */
    createNotification(action, data = {}) {
        return this.createMessage({
            type: MessageType.NOTIFICATION,
            action,
            data
        })
    }

    /**
     * 创建响应消息
     * @param {string} requestId 请求消息ID
     * @param {boolean} success 是否成功
     * @param {any} data 响应数据
     * @param {string} error 错误信息 (可选)
     * @returns {Object} 响应消息对象
     */
    createResponse(requestId, success, data = null, error = null) {
        return this.createMessage({
            type: 'response',
            action: 'response',
            data: {
                requestId,
                success,
                data,
                error
            }
        })
    }

    /**
     * 创建通知消息
     * @param {string} action 动作名称
     * @param {Object} data 通知数据
     * @returns {Object} 通知消息对象
     */
    createNotification(action, data = {}) {
        return this.createMessage({
            type: 'notification',
            action,
            data
        })
    }

    /**
     * 验证消息格式
     * @param {Object} message 消息对象
     * @returns {boolean} 是否有效
     */
    validateMessage(message) {
        if (!message || typeof message !== 'object') {
            return false
        }

        // 必需字段检查
        const requiredFields = ['id', 'type', 'action', 'timestamp', 'version']
        for (const field of requiredFields) {
            if (!(field in message)) {
                console.warn(`[MessageProtocol] 消息缺少必需字段: ${field}`)
                return false
            }
        }

        // 类型检查
        if (!['request', 'response', 'notification'].includes(message.type)) {
            console.warn(`[MessageProtocol] 无效的消息类型: ${message.type}`)
            return false
        }

        // action格式检查 (应该是 service.method 格式)
        if (typeof message.action !== 'string' || message.action.length === 0) {
            console.warn(`[MessageProtocol] 无效的action: ${message.action}`)
            return false
        }

        // 版本兼容性检查
        if (message.version !== this.version) {
            console.warn(`[MessageProtocol] 版本不匹配: ${message.version} vs ${this.version}`)
            // 暂时允许版本不匹配，但记录警告
        }

        return true
    }

    /**
     * 序列化消息
     * @param {Object} message 消息对象
     * @returns {string} JSON字符串
     */
    serialize(message) {
        try {
            return JSON.stringify(message)
        } catch (error) {
            console.error('[MessageProtocol] 序列化消息失败:', error)
            throw new Error('消息序列化失败')
        }
    }

    /**
     * 反序列化消息
     * @param {string} messageString JSON字符串
     * @returns {Object} 消息对象
     */
    deserialize(messageString) {
        try {
            const message = JSON.parse(messageString)
            
            if (!this.validateMessage(message)) {
                throw new Error('反序列化后的消息格式无效')
            }
            
            return message
        } catch (error) {
            console.error('[MessageProtocol] 反序列化消息失败:', error)
            throw new Error('消息反序列化失败')
        }
    }

    /**
     * 生成唯一消息ID
     * @returns {string} 消息ID
     */
    generateMessageId() {
        this.messageIdCounter++
        const timestamp = Date.now()
        const random = Math.random().toString(36).substring(2, 11)
        return `msg_${timestamp}_${this.messageIdCounter}_${random}`
    }

    /**
     * 清理和验证数据
     * @param {any} data 原始数据
     * @returns {any} 清理后的数据
     */
    sanitizeData(data) {
        if (data === null || data === undefined) {
            return null
        }

        if (typeof data === 'string' || typeof data === 'number' || typeof data === 'boolean') {
            return data
        }

        if (Array.isArray(data)) {
            return data.map(item => this.sanitizeData(item))
        }

        if (typeof data === 'object') {
            const sanitized = {}
            for (const [key, value] of Object.entries(data)) {
                // 过滤掉函数和undefined值
                if (typeof value !== 'function' && value !== undefined) {
                    sanitized[key] = this.sanitizeData(value)
                }
            }
            return sanitized
        }

        // 其他类型转换为字符串
        return String(data)
    }

    /**
     * 检查动作是否为VoIP相关
     * @param {string} action 动作名称
     * @returns {boolean} 是否为VoIP动作
     */
    isVoIPAction(action) {
        return Object.values(VoIPAction).includes(action)
    }

    /**
     * 解析服务名称和方法名
     * @param {string} action 动作名称 (如: 'voip.call')
     * @returns {Object} {service: string, method: string}
     */
    parseAction(action) {
        if (!action || typeof action !== 'string') {
            return { service: 'unknown', method: 'unknown' }
        }

        const parts = action.split('.')
        if (parts.length >= 2) {
            return {
                service: parts[0],
                method: parts.slice(1).join('.')
            }
        }

        return {
            service: 'app',
            method: action
        }
    }

    /**
     * 构建动作名称
     * @param {string} service 服务名称
     * @param {string} method 方法名称
     * @returns {string} 完整的动作名称
     */
    buildAction(service, method) {
        if (!service || !method) {
            throw new Error('服务名称和方法名称都不能为空')
        }
        return `${service}.${method}`
    }

    /**
     * 解析action
     * @param {string} action 动作字符串 (如: 'voip.call')
     * @returns {Object} 解析结果 {service: string, method: string}
     */
    parseAction(action) {
        if (typeof action !== 'string') {
            throw new Error('action必须是字符串')
        }

        const parts = action.split('.')
        if (parts.length < 2) {
            // 如果没有点分隔符，默认为 'app' 服务
            return {
                service: 'app',
                method: action
            }
        }

        return {
            service: parts[0],
            method: parts.slice(1).join('.')
        }
    }

    /**
     * 构建action字符串
     * @param {string} service 服务名
     * @param {string} method 方法名
     * @returns {string} action字符串
     */
    buildAction(service, method) {
        if (!service || !method) {
            throw new Error('service和method都不能为空')
        }
        return `${service}.${method}`
    }

    /**
     * 检查消息是否过期
     * @param {Object} message 消息对象
     * @param {number} timeoutMs 超时时间(毫秒)
     * @returns {boolean} 是否过期
     */
    isMessageExpired(message, timeoutMs = 30000) {
        if (!message.timestamp) {
            return false
        }
        
        const now = Date.now()
        return (now - message.timestamp) > timeoutMs
    }

    /**
     * 获取消息摘要信息
     * @param {Object} message 消息对象
     * @returns {string} 摘要字符串
     */
    getMessageSummary(message) {
        if (!message) {
            return 'null message'
        }

        const { id, type, action, timestamp } = message
        const time = timestamp ? new Date(timestamp).toLocaleTimeString() : 'unknown'
        
        return `[${type}] ${action} (${id}) at ${time}`
    }

    /**
     * 克隆消息对象
     * @param {Object} message 原消息对象
     * @returns {Object} 克隆的消息对象
     */
    cloneMessage(message) {
        try {
            return JSON.parse(JSON.stringify(message))
        } catch (error) {
            console.error('[MessageProtocol] 克隆消息失败:', error)
            throw new Error('消息克隆失败')
        }
    }
}

export default MessageProtocol
