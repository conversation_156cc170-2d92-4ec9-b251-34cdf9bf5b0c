/**
 * 响应处理器
 * 负责管理请求-响应的映射和超时处理
 * 
 * <AUTHOR>
 */

/**
 * 响应处理器类
 * 管理异步请求的回调和超时
 */
class ResponseHandler {
    constructor() {
        // 存储待处理的回调函数
        this.pendingCallbacks = new Map()
        
        // 默认超时时间 (30秒)
        this.defaultTimeout = 30000
        
        // 清理定时器
        this.cleanupInterval = null
        
        // 启动定期清理
        this.startCleanup()
    }

    /**
     * 注册回调函数
     * @param {string} messageId 消息ID
     * @param {Function} resolve 成功回调
     * @param {Function} reject 失败回调
     * @param {number} timeout 超时时间(毫秒)
     */
    registerCallback(messageId, resolve, reject, timeout = this.defaultTimeout) {
        if (!messageId) {
            reject(new Error('消息ID不能为空'))
            return
        }

        // 如果已存在相同ID的回调，先清理
        if (this.pendingCallbacks.has(messageId)) {
            this.clearCallback(messageId)
        }

        const callbackInfo = {
            messageId,
            resolve,
            reject,
            timestamp: Date.now(),
            timeout,
            timeoutId: null
        }

        // 设置超时定时器
        if (timeout > 0) {
            callbackInfo.timeoutId = setTimeout(() => {
                this.handleTimeout(messageId)
            }, timeout)
        }

        this.pendingCallbacks.set(messageId, callbackInfo)
        
        console.log(`[ResponseHandler] 注册回调: ${messageId}, 超时: ${timeout}ms`)
    }

    /**
     * 处理响应消息
     * @param {Object} responseMessage 响应消息
     */
    handleResponse(responseMessage) {
        if (!responseMessage || !responseMessage.data) {
            console.warn('[ResponseHandler] 无效的响应消息:', responseMessage)
            return
        }

        const { requestId, success, data, error } = responseMessage.data
        
        if (!requestId) {
            console.warn('[ResponseHandler] 响应消息缺少requestId:', responseMessage)
            return
        }

        const callbackInfo = this.pendingCallbacks.get(requestId)
        if (!callbackInfo) {
            console.warn(`[ResponseHandler] 未找到对应的回调: ${requestId}`)
            return
        }

        console.log(`[ResponseHandler] 处理响应: ${requestId}, 成功: ${success}`)

        try {
            if (success) {
                callbackInfo.resolve(data)
            } else {
                const errorMessage = error || '未知错误'
                callbackInfo.reject(new Error(errorMessage))
            }
        } catch (callbackError) {
            console.error(`[ResponseHandler] 回调执行错误: ${requestId}`, callbackError)
        } finally {
            // 清理回调
            this.clearCallback(requestId)
        }
    }

    /**
     * 处理超时
     * @param {string} messageId 消息ID
     */
    handleTimeout(messageId) {
        const callbackInfo = this.pendingCallbacks.get(messageId)
        if (!callbackInfo) {
            return
        }

        console.warn(`[ResponseHandler] 请求超时: ${messageId}`)

        try {
            callbackInfo.reject(new Error(`请求超时 (${callbackInfo.timeout}ms)`))
        } catch (callbackError) {
            console.error(`[ResponseHandler] 超时回调执行错误: ${messageId}`, callbackError)
        } finally {
            this.clearCallback(messageId)
        }
    }

    /**
     * 清理回调
     * @param {string} messageId 消息ID
     */
    clearCallback(messageId) {
        const callbackInfo = this.pendingCallbacks.get(messageId)
        if (callbackInfo) {
            // 清理超时定时器
            if (callbackInfo.timeoutId) {
                clearTimeout(callbackInfo.timeoutId)
            }
            
            // 从Map中移除
            this.pendingCallbacks.delete(messageId)
            
            console.log(`[ResponseHandler] 清理回调: ${messageId}`)
        }
    }

    /**
     * 清理所有回调
     * @param {string} reason 清理原因
     */
    clearAllCallbacks(reason = '系统清理') {
        console.log(`[ResponseHandler] 清理所有回调, 原因: ${reason}`)
        
        for (const [messageId, callbackInfo] of this.pendingCallbacks) {
            try {
                callbackInfo.reject(new Error(`请求被取消: ${reason}`))
            } catch (error) {
                console.error(`[ResponseHandler] 清理回调错误: ${messageId}`, error)
            }
            
            if (callbackInfo.timeoutId) {
                clearTimeout(callbackInfo.timeoutId)
            }
        }
        
        this.pendingCallbacks.clear()
    }

    /**
     * 获取待处理的回调数量
     * @returns {number} 回调数量
     */
    getPendingCount() {
        return this.pendingCallbacks.size
    }

    /**
     * 获取待处理的回调列表
     * @returns {Array} 回调信息列表
     */
    getPendingCallbacks() {
        const callbacks = []
        for (const [messageId, callbackInfo] of this.pendingCallbacks) {
            callbacks.push({
                messageId,
                timestamp: callbackInfo.timestamp,
                timeout: callbackInfo.timeout,
                age: Date.now() - callbackInfo.timestamp
            })
        }
        return callbacks
    }

    /**
     * 启动定期清理
     */
    startCleanup() {
        // 每分钟清理一次过期的回调
        this.cleanupInterval = setInterval(() => {
            this.cleanupExpiredCallbacks()
        }, 60000)
    }

    /**
     * 停止定期清理
     */
    stopCleanup() {
        if (this.cleanupInterval) {
            clearInterval(this.cleanupInterval)
            this.cleanupInterval = null
        }
    }

    /**
     * 清理过期的回调
     */
    cleanupExpiredCallbacks() {
        const now = Date.now()
        const expiredCallbacks = []
        
        for (const [messageId, callbackInfo] of this.pendingCallbacks) {
            const age = now - callbackInfo.timestamp
            // 如果回调存在时间超过其超时时间的2倍，认为是泄漏的回调
            if (age > callbackInfo.timeout * 2) {
                expiredCallbacks.push(messageId)
            }
        }
        
        if (expiredCallbacks.length > 0) {
            console.warn(`[ResponseHandler] 发现 ${expiredCallbacks.length} 个过期回调，进行清理`)
            expiredCallbacks.forEach(messageId => {
                this.clearCallback(messageId)
            })
        }
    }

    /**
     * 检查是否有待处理的回调
     * @param {string} messageId 消息ID (可选)
     * @returns {boolean} 是否有待处理的回调
     */
    hasPendingCallback(messageId = null) {
        if (messageId) {
            return this.pendingCallbacks.has(messageId)
        }
        return this.pendingCallbacks.size > 0
    }

    /**
     * 获取回调的等待时间
     * @param {string} messageId 消息ID
     * @returns {number} 等待时间(毫秒)，如果不存在返回-1
     */
    getCallbackAge(messageId) {
        const callbackInfo = this.pendingCallbacks.get(messageId)
        if (!callbackInfo) {
            return -1
        }
        return Date.now() - callbackInfo.timestamp
    }

    /**
     * 销毁响应处理器
     */
    destroy() {
        this.stopCleanup()
        this.clearAllCallbacks('响应处理器销毁')
    }
}

export default ResponseHandler
