/**
 * App服务适配器
 * 提供统一的App服务调用接口，处理不同平台的差异
 * 
 * <AUTHOR>
 */

import AppBridge from './AppBridge.js'
import { VoIPAction, ServiceType } from './MessageProtocol.js'

/**
 * App服务适配器类
 * 提供面向业务的统一接口
 */
class AppServiceAdapter {
    constructor() {
        this.bridge = AppBridge
        this.services = new Map()
        this.isInitialized = false
        
        this.init()
    }

    /**
     * 初始化适配器
     */
    init() {
        if (this.isInitialized) {
            return
        }

        // 注册默认服务
        this.registerDefaultServices()
        
        this.isInitialized = true
        console.log('[AppServiceAdapter] 初始化完成')
    }

    /**
     * 注册默认服务
     */
    registerDefaultServices() {
        // VoIP服务 - 增强版本
        this.registerService(ServiceType.VOIP, {
            call: this.createVoIPMethod(VoIPAction.CALL),
            hangup: this.createServiceMethod(VoIPAction.HANGUP),
            answer: this.createServiceMethod(VoIPAction.ANSWER),
            reject: this.createServiceMethod(VoIPAction.REJECT),
            getStatus: this.createServiceMethod(VoIPAction.GET_STATUS)
        })

        // 导航服务
        this.registerService('navigation', {
            push: this.createServiceMethod('navigation.push'),
            pop: this.createServiceMethod('navigation.pop'),
            replace: this.createServiceMethod('navigation.replace'),
            popToRoot: this.createServiceMethod('navigation.popToRoot')
        })

        // 设备服务
        this.registerService('device', {
            getInfo: this.createServiceMethod('device.getInfo'),
            getLocation: this.createServiceMethod('device.getLocation'),
            vibrate: this.createServiceMethod('device.vibrate'),
            playSound: this.createServiceMethod('device.playSound')
        })

        // 存储服务
        this.registerService('storage', {
            get: this.createServiceMethod('storage.get'),
            set: this.createServiceMethod('storage.set'),
            remove: this.createServiceMethod('storage.remove'),
            clear: this.createServiceMethod('storage.clear')
        })

        // 用户服务
        this.registerService('user', {
            getInfo: this.createServiceMethod('user.getInfo'),
            login: this.createServiceMethod('user.login'),
            logout: this.createServiceMethod('user.logout'),
            updateProfile: this.createServiceMethod('user.updateProfile')
        })

        // 系统服务
        this.registerService('system', {
            getVersion: this.createServiceMethod('system.getVersion'),
            openSettings: this.createServiceMethod('system.openSettings'),
            share: this.createServiceMethod('system.share'),
            openUrl: this.createServiceMethod('system.openUrl')
        })
    }

    /**
     * 注册服务
     * @param {string} serviceName 服务名称
     * @param {Object} methods 服务方法对象
     */
    registerService(serviceName, methods) {
        if (!serviceName || typeof methods !== 'object') {
            throw new Error('服务名称和方法对象都不能为空')
        }

        this.services.set(serviceName, methods)
        console.log(`[AppServiceAdapter] 注册服务: ${serviceName}`)
    }

    /**
     * 获取服务
     * @param {string} serviceName 服务名称
     * @returns {Object|null} 服务对象
     */
    getService(serviceName) {
        return this.services.get(serviceName) || null
    }

    /**
     * 创建服务方法
     * @param {string} action 动作名称
     * @returns {Function} 服务方法
     */
    createServiceMethod(action) {
        return async (data = {}, options = {}) => {
            try {
                return await this.bridge.sendMessage(action, data, options)
            } catch (error) {
                console.error(`[AppServiceAdapter] 服务调用失败 (${action}):`, error)
                throw error
            }
        }
    }

    /**
     * 创建VoIP专用方法
     * @param {string} action VoIP动作名称
     * @returns {Function} VoIP服务方法
     */
    createVoIPMethod(action) {
        return async (params = {}) => {
            try {
                // VoIP呼叫使用专用的发送方法
                if (action === VoIPAction.CALL) {
                    return await this.bridge.sendVoIPCall(params)
                }

                // 其他VoIP操作使用普通方法
                return await this.bridge.sendMessage(action, params, {
                    timeout: 15000 // VoIP操作超时时间较长
                })
            } catch (error) {
                console.error(`[AppServiceAdapter] VoIP操作失败 (${action}):`, error)
                throw error
            }
        }
    }

    /**
     * 直接调用服务方法
     * @param {string} serviceName 服务名称
     * @param {string} methodName 方法名称
     * @param {Object} data 数据
     * @param {Object} options 选项
     * @returns {Promise<any>} 调用结果
     */
    async callService(serviceName, methodName, data = {}, options = {}) {
        const service = this.getService(serviceName)
        if (!service) {
            throw new Error(`服务不存在: ${serviceName}`)
        }

        const method = service[methodName]
        if (!method || typeof method !== 'function') {
            throw new Error(`方法不存在: ${serviceName}.${methodName}`)
        }

        return await method(data, options)
    }

    /**
     * 检查服务是否可用
     * @param {string} serviceName 服务名称
     * @returns {boolean} 是否可用
     */
    isServiceAvailable(serviceName) {
        return this.services.has(serviceName) && this.bridge.isInAppWebView()
    }

    /**
     * 检查方法是否可用
     * @param {string} serviceName 服务名称
     * @param {string} methodName 方法名称
     * @returns {boolean} 是否可用
     */
    isMethodAvailable(serviceName, methodName) {
        const service = this.getService(serviceName)
        return service && typeof service[methodName] === 'function'
    }

    /**
     * 获取所有可用的服务列表
     * @returns {Array<string>} 服务名称列表
     */
    getAvailableServices() {
        return Array.from(this.services.keys())
    }

    /**
     * 获取服务的所有方法
     * @param {string} serviceName 服务名称
     * @returns {Array<string>} 方法名称列表
     */
    getServiceMethods(serviceName) {
        const service = this.getService(serviceName)
        if (!service) {
            return []
        }

        return Object.keys(service).filter(key => typeof service[key] === 'function')
    }

    /**
     * 添加事件监听器
     * @param {string} event 事件名称
     * @param {Function} callback 回调函数
     */
    on(event, callback) {
        this.bridge.on(event, callback)
    }

    /**
     * 移除事件监听器
     * @param {string} event 事件名称
     * @param {Function} callback 回调函数
     */
    off(event, callback) {
        this.bridge.off(event, callback)
    }

    /**
     * 发送通知消息
     * @param {string} action 动作名称
     * @param {Object} data 数据
     */
    async sendNotification(action, data = {}) {
        return await this.bridge.sendMessage(action, data, { type: 'notification' })
    }

    /**
     * 批量调用服务方法
     * @param {Array} calls 调用列表 [{service, method, data, options}]
     * @returns {Promise<Array>} 调用结果列表
     */
    async batchCall(calls) {
        if (!Array.isArray(calls)) {
            throw new Error('calls必须是数组')
        }

        const promises = calls.map(async (call, index) => {
            try {
                const { service, method, data = {}, options = {} } = call
                const result = await this.callService(service, method, data, options)
                return { index, success: true, result }
            } catch (error) {
                return { index, success: false, error: error.message }
            }
        })

        return await Promise.all(promises)
    }

    /**
     * 获取适配器状态信息
     * @returns {Object} 状态信息
     */
    getStatus() {
        return {
            isInitialized: this.isInitialized,
            isInAppWebView: this.bridge.isInAppWebView(),
            platform: this.bridge.platform,
            servicesCount: this.services.size,
            pendingCallbacks: this.bridge.responseHandler.getPendingCount()
        }
    }

    /**
     * 重置适配器
     */
    reset() {
        this.services.clear()
        this.registerDefaultServices()
        console.log('[AppServiceAdapter] 适配器已重置')
    }

    /**
     * 销毁适配器
     */
    destroy() {
        this.services.clear()
        this.isInitialized = false
        console.log('[AppServiceAdapter] 适配器已销毁')
    }
}

// 创建单例实例
const appServiceAdapter = new AppServiceAdapter()

export default appServiceAdapter
