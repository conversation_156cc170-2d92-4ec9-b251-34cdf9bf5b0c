/**
 * 通用App通信桥接工具
 * 提供H5与原生App之间的双向通信能力
 * 
 * <AUTHOR>
 */

import MessageProtocol, { MessageType, VoIPAction, WiFiAction } from './MessageProtocol.js'
import ResponseHandler from './ResponseHandler.js'

/**
 * App通信桥接器
 * 负责与原生App的底层通信，支持多种通信方式
 */
class AppBridge {
    constructor() {
        this.messageProtocol = new MessageProtocol()
        this.responseHandler = new ResponseHandler()
        this.isInitialized = false
        this.platform = this.detectPlatform()
        this.communicationMethods = []
        
        this.init()
    }

    /**
     * 初始化桥接器
     */
    init() {
        if (this.isInitialized) {
            return
        }

        // 检测并初始化可用的通信方式
        this.detectCommunicationMethods()
        
        // 设置消息监听
        this.setupMessageListeners()
        
        this.isInitialized = true
        console.log('[AppBridge] 初始化完成', {
            platform: this.platform,
            methods: this.communicationMethods.map(m => m.name)
        })
    }

    /**
     * 检测平台信息
     * @returns {string} 'iOS' | 'Android' | 'unknown'
     */
    detectPlatform() {
        // #ifdef H5
        // 优先从URL参数获取
        const urlParams = new URLSearchParams(window.location.search)
        const platform = urlParams.get('platform')
        if (platform) {
            return platform
        }
        
        // 从User-Agent判断
        const userAgent = navigator.userAgent.toLowerCase()
        if (userAgent.includes('iphone') || userAgent.includes('ipad') || userAgent.includes('ios')) {
            return 'iOS'
        } else if (userAgent.includes('android')) {
            return 'Android'
        }
        // #endif
        
        return 'unknown'
    }

    /**
     * 检测可用的通信方式
     */
    detectCommunicationMethods() {
        // #ifdef H5
        this.communicationMethods = []

        // iOS WKWebView messageHandlers (优先级最高)
        if (this.platform === 'iOS' && window.webkit?.messageHandlers?.YuanApp) {
            this.communicationMethods.push({
                name: 'iOS_WKWebView',
                priority: 1,
                send: (message) => {
                    window.webkit.messageHandlers.YuanApp.postMessage(message)
                }
            })
        }

        // Android WebView JavaScript接口
        if (this.platform === 'Android' && window.YuanApp) {
            this.communicationMethods.push({
                name: 'Android_JavaScriptInterface',
                priority: 2,
                send: (message) => {
                    if (window.YuanApp.sendMessage) {
                        window.YuanApp.sendMessage(JSON.stringify(message))
                    }
                }
            })
        }

        // 通用JavaScript Bridge
        if (window.YuanApp?.callNative) {
            this.communicationMethods.push({
                name: 'Generic_JavaScriptBridge',
                priority: 3,
                send: (message) => {
                    window.YuanApp.callNative('handleMessage', message)
                }
            })
        }

        // URL Scheme (备用方案)
        this.communicationMethods.push({
            name: 'URL_Scheme',
            priority: 9,
            send: (message) => {
                const scheme = this.platform === 'iOS' ? 'yuanapp' : 'yuanapp'
                const encodedMessage = encodeURIComponent(JSON.stringify(message))
                const url = `${scheme}://message?data=${encodedMessage}`
                window.location.href = url
            }
        })

        // postMessage (最后备用)
        if (window.parent && window.parent !== window) {
            this.communicationMethods.push({
                name: 'PostMessage',
                priority: 10,
                send: (message) => {
                    window.parent.postMessage({
                        type: 'YUAN_APP_MESSAGE',
                        data: message
                    }, '*')
                }
            })
        }

        // 按优先级排序
        this.communicationMethods.sort((a, b) => a.priority - b.priority)
        // #endif
    }

    /**
     * 设置消息监听器
     */
    setupMessageListeners() {
        // #ifdef H5
        // 监听来自App的消息
        window.addEventListener('message', (event) => {
            this.handleIncomingMessage(event.data)
        })

        // 设置全局回调函数供App调用
        if (!window.YuanAppCallback) {
            window.YuanAppCallback = (data) => {
                this.handleIncomingMessage(data)
            }
        }
        // #endif
    }

    /**
     * 处理接收到的消息
     * @param {any} data 消息数据
     */
    handleIncomingMessage(data) {
        try {
            let message = data
            
            // 如果是字符串，尝试解析为JSON
            if (typeof data === 'string') {
                message = JSON.parse(data)
            }

            // 验证消息格式
            if (!this.messageProtocol.validateMessage(message)) {
                console.warn('[AppBridge] 收到无效消息格式:', data)
                return
            }

            console.log('[AppBridge] 收到消息:', message)

            // 处理响应消息
            if (message.type === 'response') {
                this.responseHandler.handleResponse(message)
            } else if (message.type === 'notification') {
                // 触发通知事件
                this.emit('notification', message)
            }

        } catch (error) {
            console.error('[AppBridge] 处理消息失败:', error, data)
        }
    }

    /**
     * 发送消息到App
     * @param {string} action 动作名称 (如: 'voip.call', 'navigation.push')
     * @param {Object} data 消息数据
     * @param {Object} options 选项
     * @returns {Promise<any>} 响应结果
     */
    async sendMessage(action, data = {}, options = {}) {
        return new Promise((resolve, reject) => {
            try {
                // 检查初始化状态
                if (!this.isInitialized) {
                    throw new Error('AppBridge未初始化')
                }

                // 检查通信方式可用性
                if (this.communicationMethods.length === 0) {
                    throw new Error('没有可用的通信方式')
                }

                // 创建标准消息
                const message = this.messageProtocol.createMessage({
                    type: options.type || MessageType.REQUEST,
                    action,
                    data,
                    platform: this.platform,
                    ...options
                })

                console.log('[AppBridge] 发送消息:', this.messageProtocol.getMessageSummary(message))

                // 注册响应处理器
                if (message.type === MessageType.REQUEST) {
                    this.responseHandler.registerCallback(
                        message.id,
                        resolve,
                        reject,
                        options.timeout || 10000
                    )
                }

                // 尝试发送消息
                this.trySendMessage(message)

                // 如果是通知类型，直接resolve
                if (message.type === MessageType.NOTIFICATION) {
                    resolve(true)
                }

            } catch (error) {
                console.error('[AppBridge] 发送消息失败:', error)
                reject(error)
            }
        })
    }

    /**
     * 发送VoIP呼叫消息
     * @param {Object} params 呼叫参数
     * @param {string} params.number VoIP号码
     * @param {string} params.token 用户token (可选)
     * @param {string} params.displayName 显示名称 (可选)
     * @returns {Promise<any>} 呼叫结果
     */
    async sendVoIPCall(params) {
        try {
            const message = this.messageProtocol.createVoIPCallMessage(params)
            return await this.sendMessage(message.action, message.data, {
                timeout: 15000 // VoIP呼叫超时时间较长
            })
        } catch (error) {
            console.error('[AppBridge] VoIP呼叫失败:', error)
            throw error
        }
    }

    /**
     * 发送WiFi配置消息
     * @param {Object} params 配置参数
     * @param {string} params.devCode 设备编码
     * @param {string} params.token 用户token (可选)
     * @returns {Promise<any>} 配置结果
     */
    async sendWiFiConfig(params) {
        try {
            const message = this.messageProtocol.createWiFiConfigMessage(params)
            return await this.sendMessage(message.action, message.data, {
                timeout: 30000 // WiFi配置超时时间较长
            })
        } catch (error) {
            console.error('[AppBridge] WiFi配置失败:', error)
            throw error
        }
    }

    /**
     * 发送通知消息
     * @param {string} action 动作名称
     * @param {Object} data 通知数据
     * @returns {Promise<boolean>} 是否发送成功
     */
    async sendNotification(action, data = {}) {
        return await this.sendMessage(action, data, {
            type: MessageType.NOTIFICATION
        })
    }

    /**
     * 尝试通过可用的通信方式发送消息
     * @param {Object} message 消息对象
     */
    trySendMessage(message) {
        if (this.communicationMethods.length === 0) {
            throw new Error('没有可用的通信方式')
        }

        // 使用优先级最高的通信方式
        const method = this.communicationMethods[0]
        
        try {
            method.send(message)
            console.log(`[AppBridge] 使用 ${method.name} 发送消息成功`)
        } catch (error) {
            console.error(`[AppBridge] 使用 ${method.name} 发送消息失败:`, error)
            
            // 如果有备用方案，尝试下一个
            if (this.communicationMethods.length > 1) {
                const backupMethod = this.communicationMethods[1]
                try {
                    backupMethod.send(message)
                    console.log(`[AppBridge] 使用备用方案 ${backupMethod.name} 发送消息成功`)
                } catch (backupError) {
                    console.error(`[AppBridge] 备用方案也失败:`, backupError)
                    throw new Error('所有通信方式都失败')
                }
            } else {
                throw error
            }
        }
    }

    /**
     * 检查是否在App的WebView环境中
     * @returns {boolean}
     */
    isInAppWebView() {
        // #ifdef H5
        // 检查User-Agent中是否包含APP标识
        const userAgent = navigator.userAgent.toLowerCase()
        if (userAgent.includes('yuanapp') || userAgent.includes('yuan_app')) {
            return true
        }

        // 检查是否存在APP注入的全局对象
        if (window.YuanApp || window.webkit?.messageHandlers?.YuanApp) {
            return true
        }

        // 检查URL参数中是否有APP标识
        const urlParams = new URLSearchParams(window.location.search)
        const appType = urlParams.get('appType')
        
        return appType === 'app'
        // #endif

        // #ifndef H5
        return false
        // #endif
    }

    /**
     * 事件发射器 - 简单实现
     */
    emit(event, data) {
        if (this.listeners && this.listeners[event]) {
            this.listeners[event].forEach(callback => {
                try {
                    callback(data)
                } catch (error) {
                    console.error(`[AppBridge] 事件处理器错误 (${event}):`, error)
                }
            })
        }
    }

    /**
     * 添加事件监听器
     */
    on(event, callback) {
        if (!this.listeners) {
            this.listeners = {}
        }
        if (!this.listeners[event]) {
            this.listeners[event] = []
        }
        this.listeners[event].push(callback)
    }

    /**
     * 移除事件监听器
     */
    off(event, callback) {
        if (this.listeners && this.listeners[event]) {
            const index = this.listeners[event].indexOf(callback)
            if (index > -1) {
                this.listeners[event].splice(index, 1)
            }
        }
    }
}

// 创建单例实例
const appBridge = new AppBridge()

export default appBridge
