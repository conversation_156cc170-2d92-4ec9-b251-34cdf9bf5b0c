//
//  ContentView.swift
//  wadar
//
//  Created by 熵行 on 2025/6/12.
//  重构为导航方式 by AI Assistant on 2025/6/25.
//
import SwiftUI
import WebKit

struct ContentView: View {
    @StateObject private var callVM = CallViewModel.shared
    @StateObject private var loginVM = LoginViewModel()
    @StateObject private var navigationManager = NavigationManager()
    @State private var globalErrorMessage: String?
    @State private var showGlobalError = false
    var body: some View {
        NavigationStack(path: $navigationManager.navigationPath) {
            // 根据当前路由显示对应的根视图
            rootView
                .navigationDestination(for: AppRoute.self) { route in
                    destinationView(for: route)
                }
        }
        .onAppear {
            setupGlobalNotifications()
        }
        .alert(isPresented: $showGlobalError) {
            Alert(
                title: Text("提示"),
                message: Text(globalErrorMessage ?? "未知错误"),
                dismissButton: .default(Text("确定")) {
                    globalErrorMessage = nil
                }
            )
        }
    }

    // MARK: - 根视图
    @ViewBuilder
    private var rootView: some View {
        switch navigationManager.currentRoute {
        case .splash:
            SplashView(loginVM: loginVM, navigationManager: navigationManager)
                .onAppear {
                    print("[ContentView] 显示SplashView")
                }
        case .login:
            LoginView(viewModel: loginVM)
                .onAppear {
                    print("[ContentView] 显示LoginView")
                    setupLoginView()
                }
        case .webView(let url):
            WebViewContainerView(url: url, callVM: callVM, navigationManager: navigationManager)
                .onAppear {
                    print("[ContentView] 显示WebViewContainerView: \(url.absoluteString)")
                }
        default:
            // 其他路由通过navigationDestination处理
            EmptyView()
        }
    }

    // MARK: - 导航目标视图
    @ViewBuilder
    private func destinationView(for route: AppRoute) -> some View {
        switch route {
        case .callIncoming:
            CallIncomingView(viewModel: callVM)
                .navigationBarHidden(true)
        case .callOutgoing:
            CallOutgoingView(viewModel: callVM)
                .navigationBarHidden(true)
        case .callConnected:
            CallConnectView(viewModel: callVM)
                .navigationBarHidden(true)
        case .setCallee:
            SetCalleeView(viewModel: callVM)
                .navigationTitle("拨号")
        case .webView(let url):
            WebViewContainerView(url: url, callVM: callVM, navigationManager: navigationManager)
        case .espProvision:
            ESPProvisionView()
                .navigationBarHidden(true)
        default:
            EmptyView()
        }
    }

    // MARK: - 设置方法
    private func setupLoginView() {
        // 自动填充模拟账号
        loginVM.phoneNumber = "13044259929"
        loginVM.userPassword = ""
        loginVM.callVM = callVM // 关键：注入 callVM

        // 监听登录成功
        loginVM.onLoginSuccess = { [weak navigationManager] url in
            navigationManager?.handleLoginSuccess(webViewURL: url)
        }
    }

    // MARK: - 全局通知设置
    private func setupGlobalNotifications() {
        // 监听网络错误通知
        NotificationCenter.default.addObserver(forName: .NetworkErrorNotification, object: nil, queue: .main) { notification in
            if let msg = notification.userInfo?["message"] as? String {
                self.globalErrorMessage = msg
                self.showGlobalError = true
            }
        }

        // 监听token失效通知
        NotificationCenter.default.addObserver(forName: .TokenInvalidNotification, object: nil, queue: .main) { _ in
            print("[ContentView] 收到token失效通知，重置登录状态")
            // 重置登录状态
            loginVM.loggedIn = false
            loginVM.showWebView = false
            loginVM.webViewURL = nil
            // 清空登录信息
            loginVM.clearLoginInfo()

            // 同时清除VoIP登录状态
            if callVM.loggedIn {
                print("[ContentView] 清除VoIP登录状态")
                callVM.unregister()
            }

            // 导航到登录页面
            navigationManager.handleLogout()
        }

        // 监听通话状态变化
        setupCallNotifications()
    }

    private func setupCallNotifications() {
        // 监听来电通知
        NotificationCenter.default.addObserver(forName: .incomingCallReceived, object: nil, queue: .main) { notification in
            print("[ContentView] 收到来电通知，准备显示来电界面")
            navigationManager.handleIncomingCall()
        }

        // 监听外呼通知
        NotificationCenter.default.addObserver(forName: .outgoingCallInitiated, object: nil, queue: .main) { notification in
            print("[ContentView] 收到外呼通知，准备显示外呼界面")
            if let userInfo = notification.userInfo {
                let number = userInfo["number"] as? String ?? "未知号码"
                let source = userInfo["source"] as? String ?? "unknown"
                print("外呼号码: \(number), 来源: \(source)")
            }
            navigationManager.handleOutgoingCall()
        }

        // 监听通话连接通知
        NotificationCenter.default.addObserver(forName: .callConnected, object: nil, queue: .main) { _ in
            print("[ContentView] 通话已连接")
            navigationManager.handleCallConnected()
        }

        // 监听通话结束通知
        NotificationCenter.default.addObserver(forName: .callEnded, object: nil, queue: .main) { notification in
            print("[ContentView] 收到通话结束通知")
            navigationManager.handleCallEnded()
        }
    }
}
