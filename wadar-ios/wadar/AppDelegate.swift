import UIKit
import UserNotifications
import PushKit
import CallKit
import linphonesw
import BackgroundTasks



class AppDelegate: NSObject, UIApplicationDelegate, UNUserNotificationCenterDelegate {

    var window: UIWindow?
    private var backgroundTaskIdentifier: UIBackgroundTaskIdentifier = .invalid
    
    func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?) -> Bool {

        print("🚀 [AppDelegate] 应用启动完成")

        // 初始化推送通知管理器 - 这会自动设置VoIP推送
        _ = PushNotificationManager.shared
        print("📱 [AppDelegate] PushNotificationManager 已初始化")

        // 配置后台任务
        setupBackgroundTasks()

        // 设置通知操作
        PushNotificationManager.shared.setupNotificationActions()

        // 在AppDelegate初始化完成后调度后台任务
        if #available(iOS 13.0, *) {
            // 延迟一点时间确保所有初始化完成
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                BackgroundTaskManager.shared.scheduleBackgroundAppRefresh()
            }
        }

        return true
    }
    

    
    // MARK: - 后台任务配置
    private func setupBackgroundTasks() {
        // 注册后台任务
        if #available(iOS 13.0, *) {
            BGTaskScheduler.shared.register(forTaskWithIdentifier: "com.wadar.voip.background", using: nil) { task in
                self.handleBackgroundVoIPTask(task: task as! BGAppRefreshTask)
            }
        }
    }
    
    // MARK: - 应用生命周期
    func applicationDidEnterBackground(_ application: UIApplication) {
        print("应用进入后台")
        startBackgroundTask()

        // 确保VoIP连接保持活跃
        BackgroundTaskManager.shared.startBackgroundTask()

        // 调度下一次后台刷新任务
        if #available(iOS 13.0, *) {
            BackgroundTaskManager.shared.scheduleBackgroundAppRefresh()
        }
    }

    func applicationWillEnterForeground(_ application: UIApplication) {
        print("应用即将进入前台")
        endBackgroundTask()
        BackgroundTaskManager.shared.endBackgroundTask()
    }

    func applicationDidBecomeActive(_ application: UIApplication) {
        print("应用已激活")
        // 清除应用图标上的角标
        UIApplication.shared.applicationIconBadgeNumber = 0

        // 确保VoIP连接状态正常
        DispatchQueue.global(qos: .background).async {
            if let core = CallViewModel.shared.mCore {
                core.refreshRegisters()
                print("应用激活时刷新VoIP注册状态")
            }
        }
    }
    
    // MARK: - 后台任务管理
    private func startBackgroundTask() {
        backgroundTaskIdentifier = UIApplication.shared.beginBackgroundTask(withName: "VoIP Background Task") {
            self.endBackgroundTask()
        }
    }
    
    private func endBackgroundTask() {
        if backgroundTaskIdentifier != .invalid {
            UIApplication.shared.endBackgroundTask(backgroundTaskIdentifier)
            backgroundTaskIdentifier = .invalid
        }
    }
    
    @available(iOS 13.0, *)
    private func handleBackgroundVoIPTask(task: BGAppRefreshTask) {
        // 处理后台VoIP任务
        task.expirationHandler = {
            task.setTaskCompleted(success: false)
        }
        
        // 执行VoIP相关的后台任务
        DispatchQueue.global(qos: .background).async {
            // 保持VoIP连接活跃
            self.maintainVoIPConnection()
            
            task.setTaskCompleted(success: true)
        }
    }
    
    private func maintainVoIPConnection() {
        // 确保Linphone Core在后台保持连接
        if let core = CallViewModel.shared.mCore {
            core.iterate()
        }
    }
    

    
    // MARK: - UNUserNotificationCenterDelegate
    func userNotificationCenter(_ center: UNUserNotificationCenter, willPresent notification: UNNotification, withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void) {
        // 应用在前台时收到通知的处理
        completionHandler([.alert, .sound, .badge])
    }
    
    func userNotificationCenter(_ center: UNUserNotificationCenter, didReceive response: UNNotificationResponse, withCompletionHandler completionHandler: @escaping () -> Void) {
        // 用户点击通知的处理
        print("用户点击了通知: \(response.notification.request.content.body)")
        completionHandler()
    }
    
    // MARK: - 远程推送通知
    func application(_ application: UIApplication, didRegisterForRemoteNotificationsWithDeviceToken deviceToken: Data) {
        print("远程推送注册成功")

        // 更新APNS令牌
        PushNotificationManager.shared.updateAPNSToken(deviceToken)
    }

    func application(_ application: UIApplication, didFailToRegisterForRemoteNotificationsWithError error: Error) {
        print("注册远程推送失败: \(error.localizedDescription)")
    }

    func application(_ application: UIApplication, didReceiveRemoteNotification userInfo: [AnyHashable: Any], fetchCompletionHandler completionHandler: @escaping (UIBackgroundFetchResult) -> Void) {
        print("收到远程推送通知: \(userInfo)")

        // 处理远程推送通知
        completionHandler(.newData)
    }

    // MARK: - URL Scheme 处理
    func application(_ app: UIApplication, open url: URL, options: [UIApplication.OpenURLOptionsKey : Any] = [:]) -> Bool {
        print("收到URL Scheme调用: \(url.absoluteString)")

        // 处理yuanapp URL scheme
        if url.scheme == "yuanapp" {
            return handleYuanAppURL(url)
        }

        return false
    }

    // 处理YuanApp的URL scheme
    private func handleYuanAppURL(_ url: URL) -> Bool {
        guard let host = url.host else {
            print("URL scheme缺少host: \(url.absoluteString)")
            return false
        }

        print("处理YuanApp URL - Host: \(host), Path: \(url.path)")

        switch host {
        case "voip":
            return handleVoIPURL(url)
        default:
            print("未知的YuanApp URL host: \(host)")
            return false
        }
    }

    // 处理VoIP相关的URL
    private func handleVoIPURL(_ url: URL) -> Bool {
        let path = url.path

        switch path {
        case "/call":
            return handleVoIPCall(url)
        default:
            print("未知的VoIP URL path: \(path)")
            return false
        }
    }

    // 处理VoIP呼叫
    private func handleVoIPCall(_ url: URL) -> Bool {
        guard let components = URLComponents(url: url, resolvingAgainstBaseURL: false),
              let queryItems = components.queryItems else {
            print("无法解析VoIP呼叫URL参数")
            return false
        }

        var number: String?
        var token: String?

        // 解析查询参数
        for item in queryItems {
            switch item.name {
            case "number":
                number = item.value
            case "token":
                token = item.value
            default:
                break
            }
        }

        guard let voipNumber = number, !voipNumber.isEmpty else {
            print("VoIP呼叫缺少号码参数")
            return false
        }

        print("准备发起VoIP呼叫 - 号码: \(voipNumber), Token: \(token ?? "无")")

        // 在主线程中处理UI相关操作
        DispatchQueue.main.async {
            self.initiateVoIPCall(number: voipNumber, token: token)
        }

        return true
    }

    // 发起VoIP呼叫
    private func initiateVoIPCall(number: String, token: String?) {
        print("发起VoIP呼叫: \(number)")

        // 设置呼叫号码到CallViewModel
        CallViewModel.shared.callNum = number

        // 如果有token，可以在这里处理认证逻辑
        if let token = token {
            print("使用Token进行认证: \(token)")
            // 这里可以添加token验证逻辑
        }

        // 检查VoIP登录状态，如果未登录则先登录
        if !CallViewModel.shared.loggedIn {
            print("[AppDelegate] VoIP未登录，尝试使用当前用户信息登录")

            // 获取当前用户的VoIP账号信息
            if let userData = UserManager.shared.getUser(),
               let voipNumber = userData.member.voipNumber,
               let voipPassword = userData.member.voipPassword {
                print("[AppDelegate] 找到VoIP账号信息，开始登录: \(voipNumber)")

                // 设置账号信息并登录
                CallViewModel.shared.username = voipNumber
                CallViewModel.shared.passwd = voipPassword
                CallViewModel.shared.login()

                // 等待登录完成后再发起呼叫
                waitForVoIPLogin { success in
                    if success {
                        self.performVoIPCall(number: number, token: token)
                    } else {
                        print("[AppDelegate] VoIP登录失败，无法发起呼叫")
                    }
                }
            } else {
                print("[AppDelegate] 未找到VoIP账号信息，无法发起呼叫")
                return
            }
        } else {
            // 已登录，直接发起呼叫
            performVoIPCall(number: number, token: token)
        }
    }

    // 执行VoIP呼叫
    private func performVoIPCall(number: String, token: String?) {
        print("[AppDelegate] 执行VoIP呼叫: \(number)")

        // 发起呼叫
        CallViewModel.shared.outgoingCall()

        // 发送通知，让UI切换到呼叫界面
        NotificationCenter.default.post(
            name: .outgoingCallInitiated,
            object: nil,
            userInfo: ["number": number, "token": token as Any]
        )
    }

    // 等待VoIP登录完成
    private func waitForVoIPLogin(completion: @escaping (Bool) -> Void) {
        let maxWaitTime = 5.0 // 最大等待5秒
        let checkInterval = 0.1 // 每100ms检查一次
        var elapsedTime = 0.0

        func checkLoginStatus() {
            if CallViewModel.shared.loggedIn && CallViewModel.shared.mCore.defaultAccount?.state == .Ok {
                print("[AppDelegate] VoIP登录成功")
                completion(true)
            } else if elapsedTime >= maxWaitTime {
                print("[AppDelegate] VoIP登录超时")
                completion(false)
            } else {
                elapsedTime += checkInterval
                DispatchQueue.main.asyncAfter(deadline: .now() + checkInterval) {
                    checkLoginStatus()
                }
            }
        }

        checkLoginStatus()
    }

}


