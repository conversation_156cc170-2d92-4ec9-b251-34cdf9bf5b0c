import Foundation
import linphonesw

class CallViewModel: ObservableObject {
    static let shared = CallViewModel()

    var mCore: Core!
    @Published var coreVersion: String = Core.getVersion

    /*------------ 登录教程相关变量 -------*/
    var mCoreDelegate : CoreDelegate!
    @Published var username : String = ""
    @Published var passwd : String = ""
    @Published var domain : String = AppConfig.shared.voipDomain
    @Published var loggedIn: Bool = false
    @Published var transportType : String = AppConfig.shared.voipTransport

    // VoIP状态监控
    @Published var voipStatus: VoIPStatus = .disconnected
    private var reconnectTimer: Timer?
    private var statusCheckTimer: Timer?
    private var reconnectAttempts: Int = 0
    private let maxReconnectAttempts: Int = 5

    // 外呼相关变量
    @Published var outgoingCallMsg : String = "No call has been initiated"
    @Published var isOutgoingCallRunning : Bool = false
    @Published var callNum : String = "1008" // 目标呼叫号码
    
    // 来电相关变量
    @Published var incomingCallMsg : String = ""
    @Published var isCallIncoming : Bool = false
    @Published var isIncomingCallRunning : Bool = false
    @Published var incomingCallRemoteAddress : String = "Nobody yet"
    @Published var isSpeakerEnabled : Bool = false
    @Published var isMicrophoneEnabled : Bool = false
    
    @Published var isCalling: Bool = false // 标记是否正在外呼

    init()
    {
        // 禁用Linphone详细日志，只保留错误日志
        LoggingService.Instance.logLevel = LogLevel.Error
        
        try? mCore = Factory.Instance.createCore(configPath: "", factoryConfigPath: "", systemContext: nil)
        // 在Core层启用视频采集和显示
        // 这并不意味着通话会自动使用视频，
        // 但它允许后续使用视频功能
        mCore.videoCaptureEnabled = true
        mCore.videoDisplayEnabled = true
        
        // 启用视频后，远端会自动应答更新请求，
        // 或根据其策略询问用户。
        // 这里配置为始终自动接受视频请求
        mCore.videoActivationPolicy!.automaticallyAccept = true
        // 如果不想自动接受，
        // 需要使用类似toggleVideo中的代码来应答收到的请求
        
        // 如果启用以下属性，创建的通话参数会自动启用视频
        //core.videoActivationPolicy.automaticallyInitiate = true

        // 配置网络设置，强制使用IPv4
        configureNetworkSettings()

        try? mCore.start()
        
        mCoreDelegate = CoreDelegateStub( onCallStateChanged: { (core: Core, call: Call, state: Call.State, message: String) in
            // 每次通话状态变化时都会调用此函数，
            // 包括新来电/外呼
            self.outgoingCallMsg = message
            
            if (state == .OutgoingInit) {
                // 外呼的第一个状态
            } else if (state == .OutgoingProgress) {
                // 外呼初始化后
            } else if (state == .OutgoingRinging) {
                // 收到180 RINGING时进入此状态
            } else if (state == .Connected) {
                // 收到200 OK时
            } else if (state == .StreamsRunning) {
                // 通话处于激活状态。
                // 可能多次进入此状态，例如暂停/恢复后
                // 或ICE协商完成后
                // 等待通话连接后再允许更新通话
                self.isOutgoingCallRunning = true

                // 发送通话连接成功通知
                NotificationCenter.default.post(name: .callConnected, object: nil)

                // 仅当有多于1个摄像头时才启用切换摄像头按钮
                // 检查core.videoDevicesList.size > 2是因为SDK会创建一个静态图片的假摄像头（见下文）
                //self.canChangeCamera = core.videoDevicesList.count > 2
            } else if (state == .Paused) {
                // 通话被暂停时
                //self.canChangeCamera = false
            } else if (state == .PausedByRemote) {
                // 远端暂停通话时
            } else if (state == .Updating) {
                // 请求通话更新时，例如切换视频
            } else if (state == .UpdatedByRemote) {
                // 远端请求通话更新时
            } else if (state == .Released || state == .Error) {
                // 通话结束或呼叫失败时，重置外呼和通话相关状态，回到SetCalleeView
                self.isOutgoingCallRunning = false
                self.isIncomingCallRunning = false
                self.isCalling = false

                // 通话结束后，发送通知以恢复WebView状态
                NotificationCenter.default.post(name: .callEnded, object: nil)
            }
            
            self.incomingCallMsg = message
            if (state == .IncomingReceived) { // 收到来电时
                self.isCallIncoming = true
                self.isIncomingCallRunning = false
                self.incomingCallRemoteAddress = call.remoteAddress!.asStringUriOnly()

                print("📞 [CallViewModel] Linphone检测到来电: \(self.incomingCallRemoteAddress)")
                print("📱 当前应用状态: \(UIApplication.shared.applicationState.rawValue)")

                // 发送内部通知，触发导航到来电界面
                NotificationCenter.default.post(name: .incomingCallReceived, object: nil, userInfo: [
                    "caller_id": self.incomingCallRemoteAddress,
                    "source": "linphone_sip"
                ])

                // 只有在应用处于前台时才通过CallKit报告来电
                // 后台来电应该通过VoIP推送处理
                if UIApplication.shared.applicationState == .active {
                    print("🔔 应用在前台，通过CallKit报告来电")

                    // 生成唯一的通话UUID
                    let callUUID = UUID()

                    // 集成CallKit弹出系统来电界面
                    CallManager.shared.reportIncomingCall(
                        uuid: callUUID,
                        handle: self.incomingCallRemoteAddress,
                        hasVideo: call.params?.videoEnabled ?? false
                    ) { error in
                        if let error = error {
                            print("❌ CallKit报告来电失败: \(error.localizedDescription)")
                            // 如果CallKit失败，显示本地通知
                            PushNotificationManager.shared.showIncomingCallNotification(
                                callerName: self.incomingCallRemoteAddress,
                                callerId: self.incomingCallRemoteAddress
                            )
                        } else {
                            print("✅ 前台来电CallKit报告成功")
                        }
                    }
                } else {
                    print("⚠️ 应用在后台，来电应该通过VoIP推送处理，这里只更新内部状态")
                    // 后台状态下，CallKit应该已经通过VoIP推送调用了
                }

            } else if (state == .Connected) { // 通话建立时
                self.isCallIncoming = false
                self.isIncomingCallRunning = true

                // 清除相关通知
                PushNotificationManager.shared.clearNotification(identifier: "incoming_call")

                // 发送通话连接成功通知，触发导航到通话界面
                NotificationCenter.default.post(name: .callConnected, object: nil)

            } else if (state == .Released) { // 通话结束时
                self.isCallIncoming = false
                self.isIncomingCallRunning = false
                self.incomingCallRemoteAddress = "Nobody yet"

                // 清除所有相关通知
                PushNotificationManager.shared.clearAllNotifications()

                // 通话结束后，发送通知以恢复WebView状态
                NotificationCenter.default.post(name: .callEnded, object: nil)
            }
        }, onAccountRegistrationStateChanged: { (core: Core, account: Account, state: RegistrationState, message: String) in
            let userIdentity = account.params?.identityAddress?.asString() ?? "未知"
            print("[CallViewModel] 注册状态变化: \(state) - 用户: \(userIdentity) - 消息: \(message)")

            DispatchQueue.main.async {
                switch state {
                case .Ok:
                    self.loggedIn = true
                    self.voipStatus = .connected
                    self.reconnectAttempts = 0
                    self.stopReconnectTimer()
                    print("[CallViewModel] ✅ VoIP登录成功")
                    self.printNetworkInfo()
                    // 发送VoIP可用通知
                    NotificationCenter.default.post(name: .VoIPAvailableNotification, object: nil)

                case .Progress:
                    self.voipStatus = .connecting
                    print("[CallViewModel] 🔄 VoIP连接中...")

                case .Cleared:
                    self.loggedIn = false
                    self.voipStatus = .disconnected
                    print("[CallViewModel] VoIP登录已清除")

                case .Failed:
                    self.loggedIn = false
                    self.voipStatus = .failed
                    print("[CallViewModel] ❌ VoIP登录失败: \(message)")
                    // 启动自动重连
                    self.startAutoReconnect()

                default:
                    print("[CallViewModel] VoIP状态: \(state)")
                }
            }
        })
        mCore.addDelegate(delegate: mCoreDelegate)

        // 启动VoIP状态定时检查
        startStatusMonitoring()
    }
    
    func login() {
        // 兼容老接口，若无账号信息则不登录
        guard !username.isEmpty, !passwd.isEmpty else {
            print("[CallViewModel] voip账号或密码为空，不进行voip登录")
            return
        }
        
        do {
            // 获取要使用的传输协议。
            // 强烈推荐使用TLS
            // 只有在别无选择时才用UDP
            var transport : TransportType
            if (transportType == "TLS") { transport = TransportType.Tls }
            else if (transportType == "TCP") { transport = TransportType.Tcp }
            else  { transport = TransportType.Udp }
            
            // 配置SIP账号需要Account对象和AuthInfo对象
            // Account用于连接代理服务器，AuthInfo存储凭证
            
            // AuthInfo可通过Factory创建，仅为数据类
            // userID设为null，因为与username相同
            // ha1设为null，使用明文密码。首次注册时会自动计算哈希。
            // realm和算法会在首次注册时自动确定
            let authInfo = try Factory.Instance.createAuthInfo(username: username, userid: "", passwd: passwd, ha1: "", realm: "", domain: domain)
            
            // Account对象替代了已弃用的ProxyConfig对象
            // 通过Core获取AccountParams对象进行配置
            let accountParams = try mCore.createAccountParams()
            
            // SIP账号通过identity address标识，可用用户名和域名构造
            let identity = try Factory.Instance.createAddress(addr: String("sip:" + username + "@" + domain))
            try! accountParams.setIdentityaddress(newValue: identity)
            
            // 还需配置代理服务器地址
            let address = try Factory.Instance.createAddress(addr: String("sip:" + domain))
            
            // 使用Address对象设置传输协议
            try address.setTransport(newValue: transport)
            try accountParams.setServeraddress(newValue: address)
            // 启用注册流程
            accountParams.registerEnabled = true
            
            // 配置好AccountParams后创建Account对象
            let account = try mCore.createAccount(params: accountParams)
            
            // 将对象添加到Core中
            mCore.addAuthInfo(info: authInfo)
            try mCore.addAccount(account: account)
            
            // 设置新添加的账号为默认账号
            mCore.defaultAccount = account
            
        } catch { NSLog(error.localizedDescription) }
    }
    
    func login(voipNumber: String? = nil, voipPassword: String? = nil) {
        // 只有当voipNumber和voipPassword都不为空时才登录voip，否则不登录
        guard let voipNumber = voipNumber, let voipPassword = voipPassword, !voipNumber.isEmpty, !voipPassword.isEmpty else {
            print("[CallViewModel] 未获取到voip账号信息，不进行voip登录")
            return
        }

        print("[CallViewModel] 开始VoIP登录 - 用户名: \(voipNumber), 域名: \(domain)")
        self.username = voipNumber
        self.passwd = voipPassword
        
        do {
            // 获取要使用的传输协议。
            // 强烈推荐使用TLS
            // 只有在别无选择时才用UDP
            var transport : TransportType
            if (transportType == "TLS") { transport = TransportType.Tls }
            else if (transportType == "TCP") { transport = TransportType.Tcp }
            else  { transport = TransportType.Udp }
            
            // 配置SIP账号需要Account对象和AuthInfo对象
            // Account用于连接代理服务器，AuthInfo存储凭证
            
            // AuthInfo可通过Factory创建，仅为数据类
            // userID设为null，因为与username相同
            // ha1设为null，使用明文密码。首次注册时会自动计算哈希。
            // realm和算法会在首次注册时自动确定
            let authInfo = try Factory.Instance.createAuthInfo(username: username, userid: "", passwd: passwd, ha1: "", realm: "", domain: domain)
            
            // Account对象替代了已弃用的ProxyConfig对象
            // 通过Core获取AccountParams对象进行配置
            let accountParams = try mCore.createAccountParams()
            
            // SIP账号通过identity address标识，可用用户名和域名构造
            let identity = try Factory.Instance.createAddress(addr: String("sip:" + username + "@" + domain))
            try! accountParams.setIdentityaddress(newValue: identity)
            
            // 还需配置代理服务器地址
            let address = try Factory.Instance.createAddress(addr: String("sip:" + domain))
            
            // 使用Address对象设置传输协议
            try address.setTransport(newValue: transport)
            try accountParams.setServeraddress(newValue: address)
            // 启用注册流程
            accountParams.registerEnabled = true
            
            // 配置好AccountParams后创建Account对象
            let account = try mCore.createAccount(params: accountParams)
            
            // 将对象添加到Core中
            mCore.addAuthInfo(info: authInfo)
            try mCore.addAccount(account: account)
            
            // 设置新添加的账号为默认账号
            mCore.defaultAccount = account

            print("[CallViewModel] VoIP账号配置完成 - Identity: \(identity.asString())")
            print("[CallViewModel] 服务器地址: \(address.asString())")

        } catch {
            print("[CallViewModel] VoIP登录配置失败: \(error.localizedDescription)")
            NSLog(error.localizedDescription)
        }
    }
    
    func unregister()
    {
        // 禁用账号注册
        if let account = mCore.defaultAccount {
            
            let params = account.params
            // 返回的params对象为const，需先克隆再修改
            let clonedParams = params?.clone()
            
            // 进行修改
            clonedParams?.registerEnabled = false
            
            // 应用修改
            account.params = clonedParams
        }
    }
    func delete() {
        // 完全移除账号
        if let account = mCore.defaultAccount {
            mCore.removeAccount(account: account)
            
            // 移除所有账号
            mCore.clearAccounts()
            
            // 同理移除所有认证信息
            mCore.clearAllAuthInfo()
        }
    }
    
    func goCall(){
        // 设置外呼标记，页面自动跳转到外呼界面
        isCalling = true
        outgoingCall()
    }
    
    func outgoingCall() {
        // 检查是否已登录
        if !loggedIn {
            print("[CallViewModel] 未登录VoIP账号，无法发起呼叫")
            return
        }
        if mCore.defaultAccount == nil {
            print("[CallViewModel] defaultAccount 为空，无法发起呼叫")
            return
        }

        // 检查账号注册状态
        let account = mCore.defaultAccount!
        let registrationState = account.state
        print("[CallViewModel] 账号注册状态: \(registrationState)")

        if registrationState != .Ok {
            print("[CallViewModel] 账号未注册成功，当前状态: \(registrationState)，无法发起呼叫")
            return
        }

        // 打印调试信息
        print("[CallViewModel] 发起外呼 - 目标号码: \(callNum)")
        print("[CallViewModel] 当前账号: \(account.params?.identityAddress?.asString() ?? "无")")
        printNetworkInfo()

        do {
            // 需要获取远端SIP URI并转换为Address
            let remoteAddress = try Factory.Instance.createAddress(addr: String("sip:" + callNum + "@" + domain))

            // 还需CallParams对象
            // createCallParams对于来电需传Call对象，外呼则传nil
            let params = try mCore.createCallParams(call: nil)

            // 进行配置
            // 这里不加密，也可选择ZRTP/SRTP/DTLS
            params.mediaEncryption = MediaEncryption.None
            // 若需直接视频通话可启用
            //params.videoEnabled = true

            // 确保使用正确的From地址（当前登录的账号）
            if let fromAddress = account.params?.identityAddress {
                params.fromHeader = fromAddress.asString()
                print("[CallViewModel] 设置From地址: \(fromAddress.asString())")
            }

            // 发起通话
            let call = mCore.inviteAddressWithParams(addr: remoteAddress, params: params)
            print("[CallViewModel] 呼叫已发起，Call对象: \(String(describing: call))")

            // 通话过程可在onCallStateChanged回调中跟踪
        } catch {
            print("[CallViewModel] 外呼失败: \(error.localizedDescription)")
            NSLog(error.localizedDescription)
        }
    }
    
    func terminateCall() {
        do {
            if (mCore.callsNb == 0) { return }
            
            // 若通话未暂停，可用core.currentCall获取，否则取第一个通话
            let coreCall = (mCore.currentCall != nil) ? mCore.currentCall : mCore.calls[0]
            
            // 结束通话很简单
            if let call = coreCall {
                try call.terminate()
            }
        } catch { NSLog(error.localizedDescription) }
    }
    
    func pauseOrResume() {
        do {
            if (mCore.callsNb == 0) { return }
            let coreCall = (mCore.currentCall != nil) ? mCore.currentCall : mCore.calls[0]
            
            if let call = coreCall {
                if (call.state != Call.State.Paused && call.state != Call.State.Pausing) {
                    // 若通话未暂停，则暂停
                    try call.pause()
                } else if (call.state != Call.State.Resuming) {
                    // 否则恢复通话
                    try call.resume()
                }
            }
        } catch { NSLog(error.localizedDescription) }
    }
    
    
    // 来电处理方法
    
    func acceptCall() {
        // 重要：请确保已允许麦克风使用（见Info.plist中的"Privacy - Microphone usage description"）！
        do {
            // 如有需要，可创建CallParams对象
            // 并用该对象应答以修改通话配置
            // （见外呼示例）
            try mCore.currentCall?.accept()
        } catch { NSLog(error.localizedDescription) }
    }
    
    func muteMicrophone() {
        // 切换麦克风开关，完全禁用/启用设备麦克风采集
        mCore.micEnabled = !mCore.micEnabled
        isMicrophoneEnabled = !isMicrophoneEnabled
    }
    
    func toggleSpeaker() {
        // 获取当前使用的音频设备
        let currentAudioDevice = mCore.currentCall?.outputAudioDevice
        let speakerEnabled = currentAudioDevice?.type == AudioDeviceType.Speaker
        
        _ = currentAudioDevice?.deviceName
        // 可获取所有可用音频设备列表
        // 注意平板等设备可能没有听筒设备
        for audioDevice in mCore.audioDevices {
            
            // 对于IOS，扬声器是个特例，Linphone无法区分输入输出。
            // 即默认输出设备（听筒）与默认麦克风配对。
            // 设置输出音频设备为麦克风会将声音切换到听筒。
            if (speakerEnabled && audioDevice.type == AudioDeviceType.Microphone) {
                mCore.currentCall?.outputAudioDevice = audioDevice
                isSpeakerEnabled = false
                return
            } else if (!speakerEnabled && audioDevice.type == AudioDeviceType.Speaker) {
                mCore.currentCall?.outputAudioDevice = audioDevice
                isSpeakerEnabled = true
                return
            }
            /* 若需切换到蓝牙耳机
            else if (audioDevice.type == AudioDevice.Type.Bluetooth) {
            core.currentCall?.outputAudioDevice = audioDevice
            }*/
        }
    }

    // MARK: - 网络配置
    private func configureNetworkSettings() {
        // 强制使用IPv4，禁用IPv6
        mCore.ipv6Enabled = false

        // 设置网络可达性检查
        mCore.networkReachable = true

        // 配置NAT策略
        if let natPolicy = mCore.natPolicy {
            natPolicy.stunEnabled = true
            natPolicy.turnEnabled = false
            natPolicy.iceEnabled = true
            natPolicy.upnpEnabled = false
        }

        // 配置传输协议偏好
        mCore.sipTransportTimeout = 30000 // 30秒超时

        print("[CallViewModel] 网络配置完成 - IPv6已禁用，强制使用IPv4")
    }

    // MARK: - 调试方法
    func printNetworkInfo() {
        print("=== Linphone 网络信息 ===")
        print("IPv6 启用状态: \(mCore.ipv6Enabled)")
        print("网络可达性: \((mCore.networkReachable ?? false) ? "可达" : "不可达")")
        print("当前账号: \(mCore.defaultAccount?.params?.identityAddress?.asString() ?? "无")")
        print("注册状态: \(mCore.defaultAccount?.state ?? .None)")

        if let account = mCore.defaultAccount {
            print("账号用户名: \(account.params?.identityAddress?.username ?? "无")")
            print("账号域名: \(account.params?.identityAddress?.domain ?? "无")")
            print("服务器地址: \(account.params?.serverAddress?.asString() ?? "无")")
        }
        print("========================")
    }

    // MARK: - VoIP自动重连机制
    private func startAutoReconnect() {
        guard reconnectAttempts < maxReconnectAttempts else {
            print("[CallViewModel] ❌ 达到最大重连次数，停止重连")
            voipStatus = .failed
            // 发送VoIP不可用通知
            NotificationCenter.default.post(name: .VoIPUnavailableNotification, object: nil, userInfo: ["reason": "重连失败"])
            return
        }

        reconnectAttempts += 1
        voipStatus = .reconnecting

        let delay = min(pow(2.0, Double(reconnectAttempts)), 30.0) // 指数退避，最大30秒
        print("[CallViewModel] 🔄 第\(reconnectAttempts)次重连，\(delay)秒后开始...")

        reconnectTimer = Timer.scheduledTimer(withTimeInterval: delay, repeats: false) { _ in
            DispatchQueue.main.async {
                self.attemptReconnect()
            }
        }
    }

    private func attemptReconnect() {
        guard !username.isEmpty, !passwd.isEmpty else {
            print("[CallViewModel] ❌ 重连失败：账号信息为空")
            voipStatus = .failed
            return
        }

        print("[CallViewModel] 🔄 尝试重连VoIP...")

        // 先清理现有连接
        unregister()

        // 延迟一下再重新登录
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            self.login()
        }
    }

    private func stopReconnectTimer() {
        reconnectTimer?.invalidate()
        reconnectTimer = nil
    }

    // 手动重连方法
    func manualReconnect() {
        print("[CallViewModel] 🔄 手动重连VoIP...")
        reconnectAttempts = 0
        stopReconnectTimer()
        attemptReconnect()
    }

    // 检查VoIP状态
    func checkVoIPStatus() {
        if let account = mCore.defaultAccount {
            let state = account.state
            print("[CallViewModel] 当前VoIP状态: \(state)")

            if state != .Ok && loggedIn {
                print("[CallViewModel] ⚠️ 检测到VoIP连接异常，尝试重连...")
                startAutoReconnect()
            }
        }
    }

    // 启动状态监控
    private func startStatusMonitoring() {
        // 每30秒检查一次VoIP状态
        statusCheckTimer = Timer.scheduledTimer(withTimeInterval: 30.0, repeats: true) { _ in
            DispatchQueue.main.async {
                self.checkVoIPStatus()

                // 如果有用户数据但VoIP未登录，尝试自动登录
                if !self.loggedIn && !self.username.isEmpty && !self.passwd.isEmpty {
                    print("[CallViewModel] 检测到VoIP未登录但有账号信息，尝试自动登录...")
                    self.login()
                }
            }
        }
    }

    // 停止状态监控
    private func stopStatusMonitoring() {
        statusCheckTimer?.invalidate()
        statusCheckTimer = nil
    }

    deinit {
        stopReconnectTimer()
        stopStatusMonitoring()
    }
}

// MARK: - 通知名称扩展
extension Notification.Name {
    static let incomingCallReceived = Notification.Name("incomingCallReceived")
    static let outgoingCallInitiated = Notification.Name("outgoingCallInitiated")
    static let callConnected = Notification.Name("callConnected")
    static let callEnded = Notification.Name("callEnded")
    static let VoIPAvailableNotification = Notification.Name("VoIPAvailableNotification")
    static let VoIPUnavailableNotification = Notification.Name("VoIPUnavailableNotification")
}
