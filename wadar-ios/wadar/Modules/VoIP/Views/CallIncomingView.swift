import SwiftUI

struct CallIncomingView: View {
    @ObservedObject var viewModel : CallViewModel
    @State private var displayName: String = ""
    @Environment(\.presentationMode) var presentationMode

    var body: some View {
        VStack {
            VStack {
                Image(systemName: "person.crop.circle.fill") // 使用系统头像图标
                    .resizable()
                    .frame(width: 120, height: 120)
                    .foregroundColor(.white)
                    .padding(.top, 60)
                Text(displayName)
                    .font(.system(size: 24, weight: .regular))
                    .foregroundColor(.white)
                    .padding(.top, 20)
            }
            Spacer()
            VStack {
                Text("来电中...")
                    .font(.system(size: 18))
                    .foregroundColor(.white)
            }
            Spacer()
            HStack {
                Spacer()
                VStack {
                    Button(action: {
                        hangUpCall()
                    }) {
                        Image(systemName: "phone.down.fill") // 系统挂断图标
                            .resizable()
                            .frame(width: 68, height: 68)
                            .foregroundColor(.red)
                    }
                    Text("挂断")
                        .font(.system(size: 16))
                        .foregroundColor(.white)
                        .padding(.top, 5)
                }
                Spacer()
                VStack {
                    Button(action: {
                        answerCall()
                    }) {
                        Image(systemName: "phone.fill") // 系统接听图标
                            .resizable()
                            .frame(width: 68, height: 68)
                            .foregroundColor(.green)
                    }
                    Text("接听")
                        .font(.system(size: 16))
                        .foregroundColor(.white)
                        .padding(.top, 5)
                }
                Spacer()
            }
            .padding(.bottom, 80)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color("call_background"))
        .onAppear {
            displayName = getRemoteDisplayName()
        }
    }

    func hangUpCall() {
        // 挂断通话逻辑，实际项目中请调用VoIP SDK
        self.viewModel.terminateCall()
        presentationMode.wrappedValue.dismiss()
    }

    func answerCall() {
        // 接听通话逻辑，实际项目中请调用VoIP SDK
        self.viewModel.acceptCall()
        // 注意：不要立即dismiss，等待通话状态变为Connected后
        // CallViewModel会发送callConnected通知，NavigationManager会处理界面跳转
        // presentationMode.wrappedValue.dismiss() // 移除这行，让导航管理器处理
    }

    func getRemoteDisplayName() -> String {
        // 获取真实的来电号码
        return viewModel.incomingCallRemoteAddress
    }
}

#Preview {
    CallIncomingView(viewModel: CallViewModel.shared)
}
