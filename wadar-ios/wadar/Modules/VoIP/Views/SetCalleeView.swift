//
//  OutgoingCallView.swift
//  wadar
//
//  Created by <PERSON><PERSON> on 25/6/24.
//

import SwiftUI
import linphonesw


struct SetCalleeView: View {
    @ObservedObject var viewModel : CallViewModel // 观察 ViewModel 状态变化，驱动界面刷新
    
    var body: some View {
        // 主界面布局
        VStack {
            VStack (spacing: 20) {
                HStack {
                    Text("Call dest:")
                        .font(.title3)
                    // 输入呼叫目标地址，未登录时禁用
                    TextField("", text : $viewModel.callNum)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .disabled(!viewModel.loggedIn)
                }
                VStack(spacing: 15) {
                    // 呼叫
                    Button(action: {
                        self.viewModel.goCall()
                    })
                    {
                        Text("Call")
                            .foregroundColor(Color.white)
                            .frame(width: 300.0, height: 45, alignment: .center)
                            .background(Color.accentColor)
                            .clipShape(.rect(cornerRadius: 15))
                    }



                }.padding(.top, 30)
                
//                Group {
//                    Spacer()
//                    // 显示 linphone core 版本信息
//                    Text("Core Version is \(viewModel.coreVersion)")
//                }
            }
            .padding()
        }
    }


}

#Preview {
    SetCalleeView(viewModel: CallViewModel.shared) // SwiftUI 预览，便于开发时查看界面效果
}
