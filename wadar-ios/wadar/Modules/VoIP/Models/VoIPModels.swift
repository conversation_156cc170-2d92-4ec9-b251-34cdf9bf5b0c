//
//  VoIPModels.swift
//  wadar
//
//  Created by guan on 2025/6/26.
//

import Foundation

// MARK: - VoIP相关数据模型

/// VoIP状态枚举
enum VoIPStatus: String, CaseIterable {
    case disconnected = "disconnected"
    case connecting = "connecting"
    case connected = "connected"
    case failed = "failed"
    case reconnecting = "reconnecting"
}

/// 通话状态枚举
enum CallState: String, CaseIterable {
    case idle = "idle"
    case incoming = "incoming"
    case outgoing = "outgoing"
    case connected = "connected"
    case ended = "ended"
    case error = "error"
}

/// 通话信息模型
struct CallInfo {
    let callId: String
    let remoteNumber: String
    let remoteName: String?
    let isIncoming: Bool
    let startTime: Date?
    let duration: TimeInterval
}

/// VoIP配置模型
struct VoIPConfig {
    let domain: String
    let transport: String
    let username: String
    let password: String
}

/// 通话统计信息
struct CallStats {
    let totalCalls: Int
    let totalDuration: TimeInterval
    let lastCallTime: Date?
}
