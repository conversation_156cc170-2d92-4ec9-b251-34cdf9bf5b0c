import UIKit
import BackgroundTasks
import linphonesw

class BackgroundTaskManager {
    static let shared = BackgroundTaskManager()
    
    private var backgroundTaskIdentifier: UIBackgroundTaskIdentifier = .invalid
    private var voipKeepAliveTimer: Timer?
    
    private init() {}
    
    // MARK: - 后台任务管理
    func startBackgroundTask() {
        guard backgroundTaskIdentifier == .invalid else {
            return // 已经有后台任务在运行
        }
        
        backgroundTaskIdentifier = UIApplication.shared.beginBackgroundTask(withName: "VoIP Keep Alive") {
            self.endBackgroundTask()
        }
        
        // 启动VoIP保活定时器
        startVoIPKeepAlive()
        
        print("后台任务已启动: \(backgroundTaskIdentifier.rawValue)")
    }
    
    func endBackgroundTask() {
        stopVoIPKeepAlive()
        
        if backgroundTaskIdentifier != .invalid {
            UIApplication.shared.endBackgroundTask(backgroundTaskIdentifier)
            backgroundTaskIdentifier = .invalid
            print("后台任务已结束")
        }
    }
    
    // MARK: - VoIP保活机制
    private func startVoIPKeepAlive() {
        // 每30秒执行一次保活操作
        voipKeepAliveTimer = Timer.scheduledTimer(withTimeInterval: 30.0, repeats: true) { _ in
            self.performVoIPKeepAlive()
        }
    }
    
    private func stopVoIPKeepAlive() {
        voipKeepAliveTimer?.invalidate()
        voipKeepAliveTimer = nil
    }
    
    private func performVoIPKeepAlive() {
        DispatchQueue.global(qos: .background).async {
            // 保持Linphone Core活跃
            if let core = CallViewModel.shared.mCore {
                core.iterate()

                // 检查网络连接状态
                let globalState = core.globalState
                print("VoIP保活 - 全局状态: \(globalState), 注册状态: \(core.defaultProxyConfig?.state.rawValue ?? -1)")

                // 如果连接断开，尝试重新连接
                if globalState == .Off || globalState == .Shutdown {
                    do {
                        try core.start()
                        print("VoIP连接已重新启动")
                    } catch {
                        print("重新启动VoIP连接失败: \(error.localizedDescription)")
                    }
                }

                // 检查注册状态，如果未注册则刷新注册
                if let proxyConfig = core.defaultProxyConfig {
                    if proxyConfig.state != .Ok {
                        core.refreshRegisters()
                        print("刷新VoIP注册状态")
                    }
                }

                // 检查是否有待处理的通话
                let callsCount = core.callsNb
                if callsCount > 0 {
                    print("后台检测到 \(callsCount) 个活跃通话")
                }
            } else {
                print("VoIP Core未初始化")
            }
        }
    }
    
    // MARK: - iOS 13+ 后台任务调度
    @available(iOS 13.0, *)
    func scheduleBackgroundAppRefresh() {
        // 取消之前的任务
        BGTaskScheduler.shared.cancel(taskRequestWithIdentifier: "com.wadar.voip.background")

        let request = BGAppRefreshTaskRequest(identifier: "com.wadar.voip.background")
        request.earliestBeginDate = Date(timeIntervalSinceNow: 5 * 60) // 5分钟后，更频繁的保活

        do {
            try BGTaskScheduler.shared.submit(request)
            print("后台应用刷新任务已调度，下次执行时间: \(request.earliestBeginDate?.description ?? "未知")")
        } catch {
            print("调度后台应用刷新任务失败: \(error.localizedDescription)")

            // 检查具体的错误类型
            if let bgError = error as? BGTaskScheduler.Error {
                switch bgError.code {
                case .unavailable:
                    print("后台任务不可用")
                case .tooManyPendingTaskRequests:
                    print("待处理的后台任务请求过多")
                case .notPermitted:
                    print("后台任务权限被拒绝")
                @unknown default:
                    print("未知的后台任务错误")
                }
            }

            // 如果调度失败，尝试使用传统的后台任务
            startBackgroundTask()
        }
    }
    
    @available(iOS 13.0, *)
    func handleBackgroundAppRefresh(task: BGAppRefreshTask) {
        // 调度下一次后台刷新
        scheduleBackgroundAppRefresh()
        
        task.expirationHandler = {
            task.setTaskCompleted(success: false)
        }
        
        // 执行VoIP保活操作
        DispatchQueue.global(qos: .background).async {
            self.performVoIPKeepAlive()
            
            // 检查是否有待处理的通话
            if let core = CallViewModel.shared.mCore {
                let callsCount = core.callsNb
                if callsCount > 0 {
                    print("后台检测到 \(callsCount) 个通话")
                }
            }
            
            task.setTaskCompleted(success: true)
        }
    }
    
    // MARK: - 网络状态监控
    func startNetworkMonitoring() {
        // 监控网络状态变化
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(networkStatusChanged),
            name: .networkStatusChanged,
            object: nil
        )
    }
    
    @objc private func networkStatusChanged() {
        print("网络状态发生变化")
        
        // 网络状态变化时，重新初始化VoIP连接
        DispatchQueue.global(qos: .background).async {
            if let core = CallViewModel.shared.mCore {
                core.refreshRegisters()
                print("已刷新VoIP注册状态")
            }
        }
    }
    
    // MARK: - 内存警告处理
    func handleMemoryWarning() {
        print("收到内存警告，优化VoIP资源使用")
        
        // 在内存警告时，可以释放一些非关键资源
        // 但要确保VoIP核心功能继续运行
        if let core = CallViewModel.shared.mCore {
            // 可以暂停视频功能以节省内存
            core.videoCaptureEnabled = false
            core.videoDisplayEnabled = false
        }
    }
    
    // MARK: - 应用状态监控
    func startApplicationStateMonitoring() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(applicationDidEnterBackground),
            name: UIApplication.didEnterBackgroundNotification,
            object: nil
        )
        
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(applicationWillEnterForeground),
            name: UIApplication.willEnterForegroundNotification,
            object: nil
        )
        
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(applicationDidReceiveMemoryWarning),
            name: UIApplication.didReceiveMemoryWarningNotification,
            object: nil
        )
    }
    
    @objc private func applicationDidEnterBackground() {
        startBackgroundTask()
    }
    
    @objc private func applicationWillEnterForeground() {
        endBackgroundTask()
        
        // 恢复视频功能
        if let core = CallViewModel.shared.mCore {
            core.videoCaptureEnabled = true
            core.videoDisplayEnabled = true
        }
    }
    
    @objc private func applicationDidReceiveMemoryWarning() {
        handleMemoryWarning()
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
        endBackgroundTask()
    }
}

// MARK: - 扩展通知名称
extension Notification.Name {
    static let networkStatusChanged = Notification.Name("networkStatusChanged")
}
