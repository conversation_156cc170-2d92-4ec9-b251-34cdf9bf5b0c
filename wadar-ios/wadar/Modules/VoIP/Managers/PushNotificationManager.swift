import Foundation
import UserNotifications
import PushKit
import UIKit

class PushNotificationManager: NSObject {
    static let shared = PushNotificationManager()
    
    private var pushRegistry: PKPushRegistry?
    private var voipToken: String?
    private var apnsToken: String?
    
    private override init() {
        super.init()
        setupPushNotifications()
    }
    
    // MARK: - 推送通知设置
    private func setupPushNotifications() {
        // 设置用户通知中心代理
        UNUserNotificationCenter.current().delegate = self
        
        // 请求推送通知权限
        requestNotificationPermissions()
        
        // 设置VoIP推送
        setupVoIPPush()
    }
    
    private func requestNotificationPermissions() {
        let options: UNAuthorizationOptions = [.alert, .sound, .badge, .criticalAlert]
        
        UNUserNotificationCenter.current().requestAuthorization(options: options) { granted, error in
            DispatchQueue.main.async {
                if granted {
                    print("推送通知权限已授予")
                    UIApplication.shared.registerForRemoteNotifications()
                } else {
                    print("推送通知权限被拒绝: \(error?.localizedDescription ?? "未知错误")")
                }
            }
        }
    }
    
    private func setupVoIPPush() {
        pushRegistry = PKPushRegistry(queue: DispatchQueue.main)
        pushRegistry?.delegate = self
        pushRegistry?.desiredPushTypes = [.voIP]
    }
    
    // MARK: - 本地通知
    func scheduleLocalNotification(title: String, body: String, identifier: String, delay: TimeInterval = 0) {
        let content = UNMutableNotificationContent()
        content.title = title
        content.body = body
        content.sound = .default
        content.badge = NSNumber(value: UIApplication.shared.applicationIconBadgeNumber + 1)
        
        let trigger = UNTimeIntervalNotificationTrigger(timeInterval: max(delay, 1), repeats: false)
        let request = UNNotificationRequest(identifier: identifier, content: content, trigger: trigger)
        
        UNUserNotificationCenter.current().add(request) { error in
            if let error = error {
                print("添加本地通知失败: \(error.localizedDescription)")
            } else {
                print("本地通知已添加: \(title)")
            }
        }
    }
    
    // MARK: - 来电通知
    func showIncomingCallNotification(callerName: String, callerId: String) {
        let content = UNMutableNotificationContent()
        content.title = "来电"
        content.body = "\(callerName) (\(callerId)) 正在呼叫您"
        content.sound = UNNotificationSound(named: UNNotificationSoundName("ring.caf"))
        content.categoryIdentifier = "INCOMING_CALL"
        content.userInfo = [
            "caller_name": callerName,
            "caller_id": callerId,
            "call_type": "incoming"
        ]
        
        // 立即触发
        let trigger = UNTimeIntervalNotificationTrigger(timeInterval: 1, repeats: false)
        let request = UNNotificationRequest(identifier: "incoming_call_\(UUID().uuidString)", content: content, trigger: trigger)
        
        UNUserNotificationCenter.current().add(request) { error in
            if let error = error {
                print("显示来电通知失败: \(error.localizedDescription)")
            } else {
                print("来电通知已显示")
            }
        }
    }
    
    // MARK: - 通知操作配置
    func setupNotificationActions() {
        // 来电通知操作
        let answerAction = UNNotificationAction(
            identifier: "ANSWER_CALL",
            title: "接听",
            options: [.foreground]
        )
        
        let declineAction = UNNotificationAction(
            identifier: "DECLINE_CALL",
            title: "挂断",
            options: [.destructive]
        )
        
        let incomingCallCategory = UNNotificationCategory(
            identifier: "INCOMING_CALL",
            actions: [answerAction, declineAction],
            intentIdentifiers: [],
            options: [.customDismissAction]
        )
        
        UNUserNotificationCenter.current().setNotificationCategories([incomingCallCategory])
    }
    
    // MARK: - Token管理
    func updateAPNSToken(_ token: Data) {
        apnsToken = token.map { String(format: "%02x", $0) }.joined()
        print("APNS Token: \(apnsToken ?? "nil")")
        
        // 发送到服务器
        sendTokenToServer(token: apnsToken!, type: "apns")
    }
    
    func updateVoIPToken(_ token: Data) {
        voipToken = token.map { String(format: "%02x", $0) }.joined()
        print("VoIP Token: \(voipToken ?? "nil")")
        
        // 发送到服务器
        sendTokenToServer(token: voipToken!, type: "voip")
    }
    
    private func sendTokenToServer(token: String, type: String) {
        // 这里应该将设备令牌发送到您的服务器
        // 服务器可以使用这些令牌发送推送通知
        print("需要将\(type)令牌发送到服务器: \(token)")
        
        // 示例API调用
        /*
        let url = URL(string: "https://your-server.com/api/register-device")!
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        let body = [
            "device_token": token,
            "token_type": type,
            "user_id": "current_user_id" // 替换为实际用户ID
        ]
        
        request.httpBody = try? JSONSerialization.data(withJSONObject: body)
        
        URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
                print("发送令牌到服务器失败: \(error.localizedDescription)")
            } else {
                print("令牌已成功发送到服务器")
            }
        }.resume()
        */
    }
    
    // MARK: - 清除通知
    func clearAllNotifications() {
        UNUserNotificationCenter.current().removeAllPendingNotificationRequests()
        UNUserNotificationCenter.current().removeAllDeliveredNotifications()
        UIApplication.shared.applicationIconBadgeNumber = 0
    }
    
    func clearNotification(identifier: String) {
        UNUserNotificationCenter.current().removePendingNotificationRequests(withIdentifiers: [identifier])
        UNUserNotificationCenter.current().removeDeliveredNotifications(withIdentifiers: [identifier])
    }
}

// MARK: - PKPushRegistryDelegate
extension PushNotificationManager: PKPushRegistryDelegate {
    func pushRegistry(_ registry: PKPushRegistry, didUpdate pushCredentials: PKPushCredentials, for type: PKPushType) {
        print("VoIP推送凭证已更新")
        updateVoIPToken(pushCredentials.token)
    }
    
    func pushRegistry(_ registry: PKPushRegistry, didInvalidatePushTokenFor type: PKPushType) {
        print("VoIP推送令牌已失效")
        voipToken = nil
    }
    
    func pushRegistry(_ registry: PKPushRegistry, didReceiveIncomingPushWith payload: PKPushPayload, for type: PKPushType, completion: @escaping () -> Void) {
        print("🔔 [PushNotificationManager] 收到VoIP推送: \(payload.dictionaryPayload)")
        print("📱 当前应用状态: \(UIApplication.shared.applicationState.rawValue) (0=active, 1=inactive, 2=background)")

        // 确保在主线程处理
        DispatchQueue.main.async {
            self.handleVoIPPush(payload: payload, completion: completion)
        }
    }

    private func handleVoIPPush(payload: PKPushPayload, completion: @escaping () -> Void) {
        let payloadDict = payload.dictionaryPayload

        // 如果没有提供caller信息，使用默认值
        let callerName = payloadDict["caller_name"] as? String ?? "未知来电"
        let callerId = payloadDict["caller_id"] as? String ?? "未知号码"
        let hasVideo = payloadDict["has_video"] as? Bool ?? false

        print("🔔 处理VoIP推送 - 来电者: \(callerName), ID: \(callerId), 视频: \(hasVideo)")

        let callUUID = UUID()

        // 启动后台任务以确保有足够时间处理来电
        let backgroundTaskId = UIApplication.shared.beginBackgroundTask(withName: "VoIP Call Processing") {
            print("⚠️ VoIP推送处理后台任务即将过期")
        }

        print("🚀 准备通过CallKit报告来电...")

        // 使用CallKit报告来电
        CallManager.shared.reportIncomingCall(
            uuid: callUUID,
            handle: callerId,
            hasVideo: hasVideo
        ) { error in
            if let error = error {
                print("❌ CallKit报告来电失败: \(error.localizedDescription)")
                print("❌ 错误代码: \((error as NSError).code)")
                print("❌ 错误域: \((error as NSError).domain)")

                // 如果CallKit失败，显示本地通知作为备选方案
                self.showIncomingCallNotification(callerName: callerName, callerId: callerId)
            } else {
                print("✅ 成功通过CallKit报告来电！系统来电界面应该已显示")
            }

            // 结束后台任务
            if backgroundTaskId != .invalid {
                UIApplication.shared.endBackgroundTask(backgroundTaskId)
            }

            completion()
        }
    }
}

// MARK: - UNUserNotificationCenterDelegate
extension PushNotificationManager: UNUserNotificationCenterDelegate {
    func userNotificationCenter(_ center: UNUserNotificationCenter, willPresent notification: UNNotification, withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void) {
        // 应用在前台时显示通知
        completionHandler([.alert, .sound, .badge])
    }
    
    func userNotificationCenter(_ center: UNUserNotificationCenter, didReceive response: UNNotificationResponse, withCompletionHandler completionHandler: @escaping () -> Void) {
        let userInfo = response.notification.request.content.userInfo
        
        switch response.actionIdentifier {
        case "ANSWER_CALL":
            print("用户选择接听电话")
            // 处理接听操作
            handleAnswerCall(userInfo: userInfo)
            
        case "DECLINE_CALL":
            print("用户选择挂断电话")
            // 处理挂断操作
            handleDeclineCall(userInfo: userInfo)
            
        default:
            print("用户点击了通知")
            // 处理默认点击操作
            handleNotificationTap(userInfo: userInfo)
        }
        
        completionHandler()
    }
    
    private func handleAnswerCall(userInfo: [AnyHashable: Any]) {
        // 实现接听逻辑
        if let callerId = userInfo["caller_id"] as? String {
            print("通过通知接听来自 \(callerId) 的电话")
            // 这里可以触发接听操作
        }
    }
    
    private func handleDeclineCall(userInfo: [AnyHashable: Any]) {
        // 实现挂断逻辑
        if let callerId = userInfo["caller_id"] as? String {
            print("通过通知挂断来自 \(callerId) 的电话")
            // 这里可以触发挂断操作
        }
    }
    
    private func handleNotificationTap(userInfo: [AnyHashable: Any]) {
        // 处理通知点击
        print("用户点击了通知: \(userInfo)")
        // 可以在这里打开应用到特定页面
    }
}
