import Foundation
import CallKit
import UIKit
import AVFoundation
import AudioToolbox

class CallManager: NSObject, CXProviderDelegate {
    static let shared = CallManager()
    private let provider: CXProvider
    private let callController = CXCallController()

    // 存储活跃的通话UUID
    private var activeCallUUIDs: Set<UUID> = []

    // 音频会话管理
    private var audioSession: AVAudioSession {
        return AVAudioSession.sharedInstance()
    }
    
    override init() {
        print("📞 [CallManager] 开始初始化...")

        let config = CXProviderConfiguration(localizedName: "wadar")

        // 基本配置
        config.supportsVideo = true
        config.maximumCallsPerCallGroup = 1
        config.maximumCallGroups = 1
        config.supportedHandleTypes = [.phoneNumber, .generic]

        // 音频配置
        config.includesCallsInRecents = true
        config.ringtoneSound = "default" // 使用系统默认铃声

        // 图标配置（可选）
        if let iconData = UIImage(systemName: "phone.fill")?.pngData() {
            config.iconTemplateImageData = iconData
        }

        // 确保在锁屏状态下也能显示来电界面的关键配置
        config.supportsVideo = true

        // 这些设置对于锁屏来电显示很重要
        config.includesCallsInRecents = true

        // 确保来电界面能在锁屏状态下显示
        if #available(iOS 14.0, *) {
            // iOS 14+ 的额外配置
        }

        self.provider = CXProvider(configuration: config)
        super.init()
        self.provider.setDelegate(self, queue: DispatchQueue.main)

        // 配置音频会话
        setupAudioSession()

        print("📞 [CallManager] 初始化完成，配置: 支持视频=\(config.supportsVideo), 最大通话数=\(config.maximumCallsPerCallGroup)")
    }
    
    // MARK: - 音频会话配置
    private func setupAudioSession() {
        do {
            try audioSession.setCategory(.playAndRecord, mode: .voiceChat, options: [.allowBluetooth, .allowBluetoothA2DP])
            try audioSession.setActive(true)
            print("音频会话配置成功")
        } catch {
            print("音频会话配置失败: \(error.localizedDescription)")
        }
    }

    // MARK: - 来电报告
    func reportIncomingCall(uuid: UUID, handle: String, hasVideo: Bool = false, completion: ((Error?) -> Void)? = nil) {
        print("🔔 开始报告来电: UUID=\(uuid), Handle=\(handle), HasVideo=\(hasVideo)")
        print("📱 当前应用状态: \(UIApplication.shared.applicationState.rawValue) (0=active, 1=inactive, 2=background)")

        // 检查是否在模拟器上运行
        #if targetEnvironment(simulator)
        print("⚠️ [CallManager] 在iOS模拟器上运行 - CallKit可能无法正常显示来电界面")
        print("⚠️ [CallManager] 模拟器限制：CallKit来电界面可能不会弹出")
        print("⚠️ [CallManager] 建议在真机上测试以获得完整的VoIP体验")
        #endif

        // 添加到活跃通话列表
        activeCallUUIDs.insert(uuid)

        let update = CXCallUpdate()
        update.remoteHandle = CXHandle(type: .phoneNumber, value: handle)
        update.hasVideo = hasVideo
        update.localizedCallerName = handle // 设置来电显示名称
        update.supportsHolding = true
        update.supportsGrouping = false
        update.supportsUngrouping = false
        update.supportsDTMF = true

        print("📞 CallKit配置: remoteHandle=\(handle), hasVideo=\(hasVideo), callerName=\(handle)")

        // 立即触发震动和音频提示
        triggerVibration()

        // 确保音频会话已激活
        activateAudioSessionForIncomingCall()

        print("🚀 准备通过CallKit报告来电...")

        provider.reportNewIncomingCall(with: uuid, update: update) { [weak self] error in
            if let error = error {
                print("❌ CallKit报告来电失败: \(error.localizedDescription)")
                print("❌ 错误代码: \((error as NSError).code)")
                print("❌ 错误域: \((error as NSError).domain)")

                #if targetEnvironment(simulator)
                print("❌ 模拟器上CallKit失败是正常现象")
                #endif

                self?.activeCallUUIDs.remove(uuid)

                // CallKit失败时显示本地通知作为备选
                print("🔔 CallKit失败，显示本地通知作为备选")
                PushNotificationManager.shared.showIncomingCallNotification(
                    callerName: "来电",
                    callerId: handle
                )
            } else {
                print("✅ 成功通过CallKit报告来电！")

                #if targetEnvironment(simulator)
                print("✅ 模拟器上CallKit调用成功，但界面可能不会显示")
                print("💡 在模拟器上，即使CallKit调用成功，来电界面也可能不会弹出")
                print("💡 这是iOS模拟器的已知限制，请在真机上测试")
                #else
                print("✅ 真机上系统来电界面应该已显示")
                #endif

                print("📊 活跃通话数量: \(self?.activeCallUUIDs.count ?? 0)")

                // 启动后台任务以保持应用活跃
                BackgroundTaskManager.shared.startBackgroundTask()

                // 持续震动提醒
                self?.startContinuousVibration()
            }
            completion?(error)
        }
    }

    // 触发震动
    private func triggerVibration() {
        // 触发系统震动
        AudioServicesPlaySystemSound(kSystemSoundID_Vibrate)

        // 如果设备支持，触发更强的震动
        if #available(iOS 10.0, *) {
            let impactFeedback = UIImpactFeedbackGenerator(style: .heavy)
            impactFeedback.prepare()
            impactFeedback.impactOccurred()
        }
    }

    // 持续震动提醒（用于来电）
    private var vibrationTimer: Timer?

    private func startContinuousVibration() {
        stopContinuousVibration() // 先停止之前的震动

        vibrationTimer = Timer.scheduledTimer(withTimeInterval: 2.0, repeats: true) { _ in
            self.triggerVibration()
        }
    }

    private func stopContinuousVibration() {
        vibrationTimer?.invalidate()
        vibrationTimer = nil
    }

    // 激活音频会话用于来电
    private func activateAudioSessionForIncomingCall() {
        do {
            try audioSession.setCategory(.playAndRecord, mode: .voiceChat, options: [.allowBluetooth, .allowBluetoothA2DP, .defaultToSpeaker])
            try audioSession.setActive(true, options: [])
            print("来电音频会话已激活")
        } catch {
            print("激活来电音频会话失败: \(error.localizedDescription)")
        }
    }

    // 结束通话报告
    func reportCallEnded(uuid: UUID, reason: CXCallEndedReason) {
        print("报告通话结束: UUID=\(uuid), Reason=\(reason)")

        activeCallUUIDs.remove(uuid)
        provider.reportCall(with: uuid, endedAt: Date(), reason: reason)

        // 停止持续震动
        stopContinuousVibration()

        // 如果没有活跃通话，结束后台任务
        if activeCallUUIDs.isEmpty {
            BackgroundTaskManager.shared.endBackgroundTask()
        }
    }
    
    // MARK: - CXProviderDelegate
    func provider(_ provider: CXProvider, perform action: CXAnswerCallAction) {
        print("用户接听通话: \(action.callUUID)")

        // 停止持续震动
        stopContinuousVibration()

        // 配置音频会话为通话模式
        configureAudioSessionForCall()

        // 通知 Linphone Core 接听
        CallViewModel.shared.acceptCall()

        action.fulfill()
    }

    func provider(_ provider: CXProvider, perform action: CXEndCallAction) {
        print("用户挂断通话: \(action.callUUID)")

        // 从活跃通话列表中移除
        activeCallUUIDs.remove(action.callUUID)

        // 停止持续震动
        stopContinuousVibration()

        // 通知 Linphone Core 挂断
        CallViewModel.shared.terminateCall()

        // 重置音频会话
        resetAudioSession()

        action.fulfill()

        // 如果没有活跃通话，结束后台任务
        if activeCallUUIDs.isEmpty {
            BackgroundTaskManager.shared.endBackgroundTask()
        }
    }

    func provider(_ provider: CXProvider, perform action: CXStartCallAction) {
        print("开始外呼: \(action.callUUID)")

        // 添加到活跃通话列表
        activeCallUUIDs.insert(action.callUUID)

        // 配置音频会话
        configureAudioSessionForCall()

        // 通知 Linphone Core 外呼
        CallViewModel.shared.outgoingCall()

        action.fulfill()
    }

    func provider(_ provider: CXProvider, perform action: CXSetMutedCallAction) {
        print("设置静音状态: \(action.isMuted)")

        // 设置麦克风静音状态
        CallViewModel.shared.muteMicrophone()

        action.fulfill()
    }

    func provider(_ provider: CXProvider, perform action: CXSetHeldCallAction) {
        print("设置通话保持状态: \(action.isOnHold)")

        if action.isOnHold {
            CallViewModel.shared.pauseOrResume()
        } else {
            CallViewModel.shared.pauseOrResume()
        }

        action.fulfill()
    }

    func provider(_ provider: CXProvider, timedOutPerforming action: CXAction) {
        print("操作超时: \(action)")
        action.fail()
    }

    func providerDidReset(_ provider: CXProvider) {
        print("⚠️ [CallManager] CallKit Provider 重置 - 这可能导致来电界面不显示")
        print("⚠️ [CallManager] Provider重置原因可能是配置错误或权限问题")

        // 清除所有活跃通话
        activeCallUUIDs.removeAll()

        // 重置音频会话
        resetAudioSession()

        // 结束后台任务
        BackgroundTaskManager.shared.endBackgroundTask()

        // 检查CallKit权限
        checkCallKitPermissions()
    }

    private func checkCallKitPermissions() {
        print("🔍 [CallManager] 检查CallKit权限...")

        // 检查是否有通话权限
        let authStatus = AVAudioSession.sharedInstance().recordPermission
        print("🎤 [CallManager] 麦克风权限状态: \(authStatus.rawValue)")

        // 在iOS模拟器上，CallKit可能不完全支持
        #if targetEnvironment(simulator)
        print("⚠️ [CallManager] 检测到iOS模拟器 - CallKit功能可能受限")
        print("⚠️ [CallManager] 建议在真机上测试VoIP功能")
        #endif
    }

    func providerDidBegin(_ provider: CXProvider) {
        print("CallKit Provider 开始")
    }

    // MARK: - 音频会话管理
    private func configureAudioSessionForCall() {
        do {
            try audioSession.setCategory(.playAndRecord, mode: .voiceChat, options: [.allowBluetooth, .allowBluetoothA2DP])
            try audioSession.setActive(true)
            print("通话音频会话配置成功")
        } catch {
            print("通话音频会话配置失败: \(error.localizedDescription)")
        }
    }

    private func resetAudioSession() {
        do {
            try audioSession.setActive(false, options: .notifyOthersOnDeactivation)
            print("音频会话已重置")
        } catch {
            print("重置音频会话失败: \(error.localizedDescription)")
        }
    }
}
