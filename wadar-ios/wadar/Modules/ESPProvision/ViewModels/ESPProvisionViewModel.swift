//
//  ESPProvisionViewModel.swift
//  wadar
//
//  Created by guan on 2025/7/1.
//  ESP设备配网视图模型
//

import Foundation
import SwiftUI
import ESPProvision
import Combine

/// ESP设备配网视图模型
/// 负责管理配网界面的状态和业务逻辑
class ESPProvisionViewModel: ObservableObject {
    
    // MARK: - 发布属性
    @Published var currentStep: ProvisionStep = .deviceScan
    @Published var selectedDevice: ESPDevice?
    @Published var selectedWiFiNetwork: ESPWifiNetwork?
    @Published var wifiSSID: String = ""
    @Published var wifiPassword: String = ""
    @Published var showWiFiList: Bool = false
    @Published var showAlert: Bool = false
    @Published var alertMessage: String = ""
    @Published var isLoading: Bool = false
    @Published var errorMessage: String?
    
    // MARK: - 私有属性
    private let provisionManager = WadarESPProvisionManager.shared
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - 配网步骤枚举
    enum ProvisionStep: Int, CaseIterable {
        case deviceScan = 0     // 设备扫描
        case deviceConnect = 1  // 设备连接
        case wifiConfig = 2     // WiFi配置
        case provisioning = 3   // 配网进行中
        case completed = 4      // 配网完成
        
        var title: String {
            switch self {
            case .deviceScan: return "扫描设备"
            case .deviceConnect: return "连接设备"
            case .wifiConfig: return "配置WiFi"
            case .provisioning: return "配网中"
            case .completed: return "完成"
            }
        }
        
        var description: String {
            switch self {
            case .deviceScan: return "搜索附近的ESP设备"
            case .deviceConnect: return "连接到选定的设备"
            case .wifiConfig: return "配置WiFi网络信息"
            case .provisioning: return "正在进行设备配网"
            case .completed: return "配网已完成"
            }
        }
    }
    
    // MARK: - 计算属性
    var discoveredDevices: [ESPDevice] {
        return provisionManager.discoveredDevices
    }
    
    var availableWiFiNetworks: [ESPWifiNetwork] {
        return provisionManager.availableWiFiNetworks
    }
    
    var provisionState: ESPProvisionState {
        return provisionManager.provisionState
    }
    
    var provisionProgress: Double {
        return provisionManager.provisionProgress
    }
    
    var statusMessage: String {
        return provisionManager.statusMessage
    }
    
    var canProceedToNext: Bool {
        switch currentStep {
        case .deviceScan:
            return selectedDevice != nil
        case .deviceConnect:
            return provisionState == .connected
        case .wifiConfig:
            return !wifiSSID.isEmpty
        case .provisioning:
            return provisionState == .success
        case .completed:
            return false
        }
    }
    
    var canGoBack: Bool {
        return currentStep.rawValue > 0 && currentStep != .provisioning
    }
    
    init() {
        setupObservers()
    }
    
    // MARK: - 设置观察者
    private func setupObservers() {
        // 监听配网管理器状态变化
        provisionManager.$provisionState
            .receive(on: DispatchQueue.main)
            .sink { [weak self] state in
                self?.handleProvisionStateChange(state)
            }
            .store(in: &cancellables)
        
        // 监听设备列表变化
        provisionManager.$discoveredDevices
            .receive(on: DispatchQueue.main)
            .sink { [weak self] devices in
                // 如果只有一个设备，自动选择
                if devices.count == 1 && self?.selectedDevice == nil {
                    self?.selectedDevice = devices.first
                }
            }
            .store(in: &cancellables)
        
        // 监听WiFi网络列表变化
        provisionManager.$availableWiFiNetworks
            .receive(on: DispatchQueue.main)
            .sink { [weak self] networks in
                // 如果当前SSID为空且有可用网络，可以预选择信号最强的
                if self?.wifiSSID.isEmpty == true && !networks.isEmpty {
                    // 不自动选择，让用户手动选择
                }
            }
            .store(in: &cancellables)
    }
    
    // MARK: - 公共方法
    
    /// 开始扫描设备
    func startDeviceScan() {
        print("[ESPProvisionViewModel] 开始扫描设备")
        currentStep = .deviceScan
        selectedDevice = nil
        isLoading = true
        errorMessage = nil
        provisionManager.startScanningDevices()
    }
    
    /// 停止扫描设备
    func stopDeviceScan() {
        print("[ESPProvisionViewModel] 停止扫描设备")
        provisionManager.stopScanningDevices()
        isLoading = false
    }
    
    /// 选择设备
    func selectDevice(_ device: ESPDevice) {
        print("[ESPProvisionViewModel] 选择设备: \(device.name)")
        selectedDevice = device

        // 显示设备详细信息
        let deviceInfo = provisionManager.getDeviceInfo(device)
        print("[ESPProvisionViewModel] 设备信息:\n\(deviceInfo)")
    }

    /// 获取设备详细信息
    func getDeviceInfo(_ device: ESPDevice) -> String {
        return provisionManager.getDeviceInfo(device)
    }
    
    /// 连接到选定设备
    func connectToSelectedDevice() {
        guard let device = selectedDevice else {
            showError("请先选择一个设备")
            return
        }
        
        print("[ESPProvisionViewModel] 连接到设备: \(device.name)")
        currentStep = .deviceConnect
        isLoading = true
        provisionManager.connectToDevice(device)
    }
    
    /// 选择WiFi网络
    func selectWiFiNetwork(_ network: ESPWifiNetwork) {
        print("[ESPProvisionViewModel] 选择WiFi网络: \(network.ssid) (信号: \(network.rssi)dBm)")
        selectedWiFiNetwork = network
        wifiSSID = network.ssid
        showWiFiList = false

        // 如果是开放网络，清空密码
        if network.auth == .open {
            wifiPassword = ""
        }

        // 清除之前的错误信息
        errorMessage = nil
    }
    
    /// 开始配网
    func startProvisioning() {
        // 验证WiFi名称
        let trimmedSSID = wifiSSID.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmedSSID.isEmpty else {
            showError("请输入WiFi名称")
            return
        }

        // 验证WiFi名称长度
        guard trimmedSSID.count <= 32 else {
            showError("WiFi名称不能超过32个字符")
            return
        }

        // 如果选择的是加密网络但没有输入密码，给出提示
        if let selectedNetwork = selectedWiFiNetwork,
           selectedNetwork.auth != .open,
           wifiPassword.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            showError("此WiFi网络需要密码")
            return
        }

        // 验证密码长度（WPA/WPA2密码至少8位）
        let trimmedPassword = wifiPassword.trimmingCharacters(in: .whitespacesAndNewlines)
        if !trimmedPassword.isEmpty && trimmedPassword.count < 8 {
            showError("WiFi密码至少需要8个字符")
            return
        }

        print("[ESPProvisionViewModel] 开始配网: \(trimmedSSID)")
        currentStep = .provisioning
        isLoading = true
        errorMessage = nil

        // 更新SSID为trimmed版本
        wifiSSID = trimmedSSID

        provisionManager.configureWiFi(ssid: trimmedSSID, password: trimmedPassword)
    }
    
    /// 下一步
    func nextStep() {
        switch currentStep {
        case .deviceScan:
            connectToSelectedDevice()
        case .deviceConnect:
            currentStep = .wifiConfig
        case .wifiConfig:
            startProvisioning()
        case .provisioning:
            currentStep = .completed
        case .completed:
            break
        }
    }
    
    /// 上一步
    func previousStep() {
        guard canGoBack else { return }
        
        switch currentStep {
        case .deviceConnect:
            currentStep = .deviceScan
            provisionManager.disconnectDevice()
        case .wifiConfig:
            currentStep = .deviceConnect
        case .completed:
            currentStep = .wifiConfig
        default:
            break
        }
    }
    
    /// 重新开始配网
    func restartProvisioning() {
        print("[ESPProvisionViewModel] 重新开始配网")
        provisionManager.resetProvisionState()
        selectedDevice = nil
        selectedWiFiNetwork = nil
        wifiSSID = ""
        wifiPassword = ""
        currentStep = .deviceScan
        isLoading = false
        startDeviceScan()
    }
    
    /// 完成配网
    func completeProvisioning() {
        print("[ESPProvisionViewModel] 配网完成")
        provisionManager.disconnectDevice()
        // 这里可以添加完成后的处理逻辑，比如跳转到其他页面
    }
    
    // MARK: - 私有方法
    
    /// 处理配网状态变化
    private func handleProvisionStateChange(_ state: ESPProvisionState) {
        switch state {
        case .idle:
            isLoading = false
            
        case .scanning:
            isLoading = true
            
        case .connecting:
            isLoading = true
            
        case .connected:
            isLoading = false
            if currentStep == .deviceConnect {
                // 自动进入WiFi配置步骤
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                    self.currentStep = .wifiConfig
                }
            }
            
        case .configuring:
            isLoading = true
            
        case .success:
            isLoading = false
            currentStep = .completed
            
        case .failed(let message):
            isLoading = false
            showError(message)
        }
    }
    
    /// 显示错误信息
    private func showError(_ message: String) {
        alertMessage = message
        showAlert = true
    }
}

// MARK: - 扩展方法
extension ESPProvisionViewModel {
    
    /// 获取WiFi网络的安全类型描述
    func getSecurityDescription(for network: ESPWifiNetwork) -> String {
        switch network.auth {
        case .open:
            return "开放网络"
        case .wep:
            return "WEP"
        case .wpaPsk:
            return "WPA"
        case .wpa2Psk:
            return "WPA2"
        case .wpa3Psk:
            return "WPA3"
        case .wpaWpa2Psk:
            return "WPA/WPA2"
        case .wpa2Wpa3Psk:
            return "WPA2/WPA3"
        case .wpa2Enterprise:
            return "WPA2企业版"
        case .UNRECOGNIZED(_):
            return "未知"
        }
    }
    
    /// 获取信号强度描述
    func getSignalStrengthDescription(rssi: Int) -> String {
        switch rssi {
        case -30...0:
            return "优秀"
        case -50...(-30):
            return "良好"
        case -70...(-50):
            return "一般"
        case -90...(-70):
            return "较差"
        default:
            return "很差"
        }
    }
    
    /// 获取信号强度图标
    func getSignalStrengthIcon(rssi: Int) -> String {
        switch rssi {
        case -30...0:
            return "wifi"
        case -50...(-30):
            return "wifi"
        case -70...(-50):
            return "wifi"
        case -90...(-70):
            return "wifi"
        default:
            return "wifi.slash"
        }
    }
}
