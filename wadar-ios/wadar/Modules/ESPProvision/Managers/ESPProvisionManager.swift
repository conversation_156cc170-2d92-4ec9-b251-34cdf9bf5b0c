//
//  ESPProvisionManager.swift
//  wadar
//
//  Created by guan on 2025/7/1.
//  ESP设备配网管理器
//

import Foundation
import ESPProvision
import CoreBluetooth
import Combine



/// ESP设备配网状态
public enum ESPProvisionState: Equatable {
    case idle           // 空闲状态
    case scanning       // 扫描设备中
    case connecting     // 连接设备中
    case connected      // 已连接设备
    case configuring    // 配置WiFi中
    case success        // 配网成功
    case failed(String) // 配网失败

    public static func == (lhs: ESPProvisionState, rhs: ESPProvisionState) -> Bool {
        switch (lhs, rhs) {
        case (.idle, .idle), (.scanning, .scanning), (.connecting, .connecting),
             (.connected, .connected), (.configuring, .configuring), (.success, .success):
            return true
        case (.failed(let lhsMessage), .failed(let rhsMessage)):
            return lhsMessage == rhsMessage
        default:
            return false
        }
    }
}

/// ESP设备配网管理器
/// 负责管理ESP设备的蓝牙扫描、连接、WiFi配置等功能
class WadarESPProvisionManager: NSObject, ObservableObject {

    // MARK: - 单例
    static let shared = WadarESPProvisionManager()
    
    // MARK: - 发布属性
    @Published var provisionState: ESPProvisionState = .idle
    @Published var discoveredDevices: [ESPDevice] = []
    @Published var selectedDevice: ESPDevice?
    @Published var availableWiFiNetworks: [ESPWifiNetwork] = []
    @Published var provisionProgress: Double = 0.0
    @Published var statusMessage: String = ""
    
    // MARK: - 私有属性
    private let espProvisionManager: ESPProvisionManager
    private var currentDevice: ESPDevice?
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - 配网配置
    private let devicePrefix = "YUAN"  // 设备名称前缀，根据实际情况修改
    private let transport: ESPTransport = .ble
    private let security: ESPSecurity = .secure2
    private let scanTimeout: TimeInterval = 10.0

    // 扫描控制
    private var scanTimer: Timer?
    private var scanRetryCount = 0
    private let maxScanRetries = 3
    private var isScanningCancelled = false
    
    private override init() {
        espProvisionManager = ESPProvisionManager.shared
        super.init()
        setupObservers()
    }
    
    // MARK: - 设置观察者
    private func setupObservers() {
        // 监听状态变化
        $provisionState
            .sink { [weak self] state in
                self?.updateStatusMessage(for: state)
            }
            .store(in: &cancellables)
    }
    
    // MARK: - 公共方法
    
    /// 开始扫描ESP设备
    func startScanningDevices() {
        guard provisionState != .scanning else {
            print("[ESPProvisionManager] 正在扫描中，忽略重复请求")
            return
        }

        print("[ESPProvisionManager] 开始扫描ESP设备...")
        provisionState = .scanning
        discoveredDevices.removeAll()
        provisionProgress = 0.1
        statusMessage = "正在搜索设备..."
        scanRetryCount = 0

        // 清除之前的定时器
        scanTimer?.invalidate()

        performDeviceScan()
    }

    /// 执行设备扫描
    private func performDeviceScan() {
        print("[ESPProvisionManager] 执行设备扫描，重试次数: \(scanRetryCount)")

        // 设置扫描超时
        scanTimer = Timer.scheduledTimer(withTimeInterval: scanTimeout, repeats: false) { [weak self] _ in
            self?.handleScanTimeout()
        }

        espProvisionManager.searchESPDevices(
            devicePrefix: devicePrefix,
            transport: transport,
            security: security
        ) { [weak self] devices, error in
            DispatchQueue.main.async {
                self?.scanTimer?.invalidate()
                self?.handleScanResult(devices: devices, error: error)
            }
        }
    }
    
    /// 停止扫描设备
    func stopScanningDevices() {
        print("[ESPProvisionManager] 停止扫描设备")

        // 清除定时器
        scanTimer?.invalidate()
        scanTimer = nil

        // 停止ESP设备搜索
        espProvisionManager.stopESPDevicesSearch()

        if provisionState == .scanning {
            provisionState = .idle
            statusMessage = "扫描已停止"
        }
    }
    
    /// 连接到指定设备
    func connectToDevice(_ device: ESPDevice) {
        guard provisionState != .connecting else {
            print("[ESPProvisionManager] 正在连接中，忽略重复请求")
            return
        }

        print("[ESPProvisionManager] 连接到设备: \(device.name)")

        // 停止设备扫描
        stopScanningDevices()

        provisionState = .connecting
        selectedDevice = device
        currentDevice = device
        provisionProgress = 0.3

        device.connect(delegate: self) { [weak self] status in
            DispatchQueue.main.async {
                self?.handleConnectionResult(status: status)
            }
        }
    }
    
    /// 断开设备连接
    func disconnectDevice() {
        print("[ESPProvisionManager] 断开设备连接")
        currentDevice?.disconnect()
        currentDevice = nil
        selectedDevice = nil
        availableWiFiNetworks.removeAll()
        provisionState = .idle
        provisionProgress = 0.0
    }
    
    /// 扫描WiFi网络
    func scanWiFiNetworks() {
        guard let device = currentDevice, provisionState == .connected else {
            print("[ESPProvisionManager] 设备未连接，无法扫描WiFi")
            statusMessage = "设备未连接"
            return
        }

        print("[ESPProvisionManager] 扫描WiFi网络...")
        statusMessage = "正在扫描WiFi网络..."
        provisionProgress = 0.5

        // 清空之前的网络列表
        availableWiFiNetworks.removeAll()

        device.scanWifiList { [weak self] networks, error in
            DispatchQueue.main.async {
                self?.handleWiFiScanResult(networks: networks, error: error)
            }
        }
    }
    
    /// 配置WiFi网络
    func configureWiFi(ssid: String, password: String) {
        guard let device = currentDevice, provisionState == .connected else {
            print("[ESPProvisionManager] 设备未连接，无法配置WiFi")
            statusMessage = "设备未连接"
            return
        }

        // 验证输入
        guard !ssid.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            print("[ESPProvisionManager] WiFi名称不能为空")
            provisionState = .failed("WiFi名称不能为空")
            return
        }

        print("[ESPProvisionManager] 配置WiFi: \(ssid)")
        provisionState = .configuring
        statusMessage = "正在配置WiFi网络..."
        provisionProgress = 0.7

        // 使用trimmed的值
        let trimmedSSID = ssid.trimmingCharacters(in: .whitespacesAndNewlines)
        let trimmedPassword = password.trimmingCharacters(in: .whitespacesAndNewlines)

        device.provision(ssid: trimmedSSID, passPhrase: trimmedPassword) { [weak self] status in
            DispatchQueue.main.async {
                self?.handleProvisionResult(status: status)
            }
        }
    }
    
    /// 重置配网状态
    func resetProvisionState() {
        print("[ESPProvisionManager] 重置配网状态")

        // 停止扫描
        stopScanningDevices()

        // 断开设备连接
        disconnectDevice()

        // 清空数据
        discoveredDevices.removeAll()
        availableWiFiNetworks.removeAll()
        provisionState = .idle
        provisionProgress = 0.0
        statusMessage = ""
    }
    
    // MARK: - 私有方法
    
    /// 处理设备扫描结果
    private func handleScanResult(devices: [ESPDevice]?, error: ESPDeviceCSSError?) {
        if let devices = devices, !devices.isEmpty {
            print("[ESPProvisionManager] 扫描到 \(devices.count) 个设备")
            discoveredDevices = devices
            provisionState = .idle
            provisionProgress = 0.2
            statusMessage = "发现 \(devices.count) 个设备"
            scanRetryCount = 0
        } else if let error = error {
            print("[ESPProvisionManager] 扫描失败: \(error.localizedDescription)")
            handleScanFailure(error: error)
        } else {
            print("[ESPProvisionManager] 未发现设备")
            handleScanFailure(error: nil)
        }
    }

    /// 处理扫描失败
    private func handleScanFailure(error: ESPDeviceCSSError?) {
        if scanRetryCount < maxScanRetries {
            scanRetryCount += 1
            statusMessage = "重试扫描中... (\(scanRetryCount)/\(maxScanRetries))"
            print("[ESPProvisionManager] 扫描重试 \(scanRetryCount)/\(maxScanRetries)")

            // 延迟重试
            DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) { [weak self] in
                self?.performDeviceScan()
            }
        } else {
            let errorMessage = error?.localizedDescription ?? "未发现设备"
            print("[ESPProvisionManager] 扫描最终失败: \(errorMessage)")
            provisionState = .failed("扫描设备失败: \(errorMessage)")
            statusMessage = "扫描失败，请重试"
            provisionProgress = 0.0
            scanRetryCount = 0
        }
    }

    /// 处理扫描超时
    private func handleScanTimeout() {
        print("[ESPProvisionManager] 扫描超时")
        espProvisionManager.stopESPDevicesSearch()
        handleScanFailure(error: nil)
    }

    /// 获取设备详细信息
    func getDeviceInfo(_ device: ESPDevice) -> String {
        var info = "设备名称: \(device.name)\n"
        info += "传输方式: \(transport == .ble ? "蓝牙" : "WiFi")\n"
        info += "安全模式: \(getSecurityDescription(device.security))\n"

        if let advertisementData = device.advertisementData {
            info += "广播数据: \(advertisementData)\n"
        }

        return info
    }

    /// 获取安全模式描述
    private func getSecurityDescription(_ security: ESPSecurity) -> String {
        switch security {
        case .unsecure:
            return "无加密"
        case .secure:
            return "Sec1 (安全)"
        case .secure2:
            return "Sec2 (高级安全)"
        }
    }
    
    /// 处理设备连接结果
    private func handleConnectionResult(status: ESPSessionStatus) {
        switch status {
        case .connected:
            print("[ESPProvisionManager] 设备连接成功")
            provisionState = .connected
            provisionProgress = 0.4
            // 自动扫描WiFi网络
            scanWiFiNetworks()
            
        case .failedToConnect(let error):
            print("[ESPProvisionManager] 设备连接失败: \(error.localizedDescription)")
            provisionState = .failed("连接设备失败: \(error.localizedDescription)")
            provisionProgress = 0.0
            
        case .disconnected:
            print("[ESPProvisionManager] 设备已断开连接")
            provisionState = .idle
            provisionProgress = 0.0
        }
    }
    
    /// 处理WiFi扫描结果
    private func handleWiFiScanResult(networks: [ESPWifiNetwork]?, error: ESPWiFiScanError?) {
        if let networks = networks {
            print("[ESPProvisionManager] 扫描到 \(networks.count) 个WiFi网络")

            // 过滤和排序网络
            let filteredNetworks = networks
                .filter { !$0.ssid.isEmpty } // 过滤空SSID
                .sorted { network1, network2 in
                    // 首先按信号强度排序
                    if network1.rssi != network2.rssi {
                        return network1.rssi > network2.rssi
                    }
                    // 信号强度相同时按名称排序
                    return network1.ssid.localizedCaseInsensitiveCompare(network2.ssid) == .orderedAscending
                }

            availableWiFiNetworks = filteredNetworks
            provisionProgress = 0.6
            statusMessage = "发现 \(filteredNetworks.count) 个WiFi网络"

            print("[ESPProvisionManager] 处理后的网络列表:")
            for (index, network) in filteredNetworks.prefix(5).enumerated() {
                print("  \(index + 1). \(network.ssid) (信号: \(network.rssi)dBm, 安全: \(getSecurityDescription(network.auth)))")
            }

        } else if let error = error {
            print("[ESPProvisionManager] WiFi扫描失败: \(error.localizedDescription)")
            statusMessage = "WiFi扫描失败，可手动输入"
            // WiFi扫描失败不影响整体流程，用户可以手动输入
            availableWiFiNetworks = []
        } else {
            print("[ESPProvisionManager] 未发现WiFi网络")
            statusMessage = "未发现WiFi网络，可手动输入"
            availableWiFiNetworks = []
        }
    }

    /// 获取安全类型描述
    private func getSecurityDescription(_ auth: WifiAuthMode) -> String {
        switch auth {
        case .open:
            return "开放"
        case .wep:
            return "WEP"
        case .wpaPsk:
            return "WPA"
        case .wpa2Psk:
            return "WPA2"
        case .wpaWpa2Psk:
            return "WPA/WPA2"
        case .wpa2Enterprise:
            return "WPA2企业"
        case .wpa3Psk:
            return "WPA3"
        case .wpa2Wpa3Psk:
            return "WPA2/WPA3"
        case .UNRECOGNIZED(_):
            return "未知"
        }
    }
    
    /// 处理配网结果
    private func handleProvisionResult(status: ESPProvisionStatus) {
        switch status {
        case .success:
            print("[ESPProvisionManager] 配网成功")
            provisionState = .success
            provisionProgress = 1.0
            statusMessage = "配网成功！设备已连接到WiFi"

        case .configApplied:
            print("[ESPProvisionManager] 配置已应用，等待连接确认...")
            statusMessage = "配置已应用，正在连接WiFi..."
            provisionProgress = 0.9

        case .failure(let error):
            print("[ESPProvisionManager] 配网失败: \(error.localizedDescription)")
            let errorMessage = getProvisionErrorMessage(error)
            provisionState = .failed("配网失败: \(errorMessage)")
            statusMessage = "配网失败: \(errorMessage)"
            provisionProgress = 0.0
        }
    }

    /// 获取配网错误的友好描述
    private func getProvisionErrorMessage(_ error: ESPProvisionError) -> String {
        // 根据错误类型返回用户友好的错误信息
        let errorDescription = error.localizedDescription.lowercased()

        if errorDescription.contains("wifi") || errorDescription.contains("network") {
            return "WiFi连接失败，请检查密码是否正确"
        } else if errorDescription.contains("timeout") {
            return "连接超时，请重试"
        } else if errorDescription.contains("password") || errorDescription.contains("auth") {
            return "WiFi密码错误"
        } else if errorDescription.contains("session") {
            return "会话错误，请重新连接设备"
        } else {
            return "配网失败，请重试"
        }
    }
    
    /// 更新状态消息
    private func updateStatusMessage(for state: ESPProvisionState) {
        switch state {
        case .idle:
            statusMessage = "准备就绪"
        case .scanning:
            statusMessage = "正在扫描设备..."
        case .connecting:
            statusMessage = "正在连接设备..."
        case .connected:
            statusMessage = "设备已连接"
        case .configuring:
            statusMessage = "正在配置WiFi..."
        case .success:
            statusMessage = "配网成功！"
        case .failed(let message):
            statusMessage = "错误: \(message)"
        }
    }
}

// MARK: - ESPDeviceConnectionDelegate
extension WadarESPProvisionManager: ESPDeviceConnectionDelegate {

    /// 获取设备的Proof of Possession (POP)
    /// 对于安全模式sec1或sec2需要提供POP
    public func getProofOfPossesion(forDevice: ESPDevice, completionHandler: @escaping (String) -> Void) {
        // 这里应该根据实际设备的POP进行配置
        // 通常POP会印在设备标签上或通过其他方式获取
        // 为了演示，这里使用默认值，实际使用时需要修改
        let pop = "abcd1234"  // 请根据实际设备修改
        print("[ESPProvisionManager] 提供POP: \(pop)")
        completionHandler(pop)
    }
    
    /// 获取用户名（仅sec2模式需要）
    public func getUsername(forDevice: ESPDevice, completionHandler: @escaping (String?) -> Void) {
        // 对于sec2模式，可能需要提供用户名
        // 这里返回nil表示不需要用户名
        print("[ESPProvisionManager] 不需要用户名")
        completionHandler(nil)
    }
}
