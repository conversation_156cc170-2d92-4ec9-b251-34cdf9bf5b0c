//
//  ProvisioningView.swift
//  wadar
//
//  Created by guan on 2025/7/1.
//  配网进度视图
//

import SwiftUI
import ESPProvision

/// 配网进度视图
/// 显示配网过程的实时状态和进度
struct ProvisioningView: View {
    
    @ObservedObject var viewModel: ESPProvisionViewModel
    
    var body: some View {
        VStack(spacing: 24) {
            // 配网进度指示器
            ProvisioningProgressView(viewModel: viewModel)
            
            // 配网状态详情
            ProvisioningStatusView(viewModel: viewModel)
            
            // WiFi配置信息
            WiFiConfigSummaryView(viewModel: viewModel)
            
            // 配网说明
            ProvisioningInstructionView()
        }
    }
}

// MARK: - 配网进度视图
struct ProvisioningProgressView: View {
    @ObservedObject var viewModel: ESPProvisionViewModel
    
    var body: some View {
        VStack(spacing: 20) {
            // 动画图标
            ZStack {
                Circle()
                    .stroke(Color.blue.opacity(0.2), lineWidth: 8)
                    .frame(width: 100, height: 100)
                
                Circle()
                    .trim(from: 0, to: viewModel.provisionProgress)
                    .stroke(Color.blue, style: StrokeStyle(lineWidth: 8, lineCap: .round))
                    .frame(width: 100, height: 100)
                    .rotationEffect(.degrees(-90))
                    .animation(.easeInOut(duration: 0.5), value: viewModel.provisionProgress)
                
                if viewModel.provisionState == .configuring {
                    Image(systemName: "wifi")
                        .font(.system(size: 32))
                        .foregroundColor(.blue)
                        .scaleEffect(viewModel.isLoading ? 1.2 : 1.0)
                        .animation(.easeInOut(duration: 1.0).repeatForever(autoreverses: true), value: viewModel.isLoading)
                } else if viewModel.provisionState == .success {
                    Image(systemName: "checkmark")
                        .font(.system(size: 32, weight: .bold))
                        .foregroundColor(.green)
                }
            }
            
            // 进度百分比
            Text("\(Int(viewModel.provisionProgress * 100))%")
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(.primary)
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(16)
    }
}

// MARK: - 配网状态视图
struct ProvisioningStatusView: View {
    @ObservedObject var viewModel: ESPProvisionViewModel
    
    var body: some View {
        VStack(spacing: 16) {
            // 状态标题
            Text(getStatusTitle())
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.primary)
            
            // 状态描述
            Text(getStatusDescription())
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            
            // 状态消息
            if !viewModel.statusMessage.isEmpty {
                Text(viewModel.statusMessage)
                    .font(.caption)
                    .foregroundColor(.blue)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(Color.blue.opacity(0.1))
                    .cornerRadius(8)
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
    }
    
    private func getStatusTitle() -> String {
        switch viewModel.provisionState {
        case .configuring:
            return "正在配置设备"
        case .success:
            return "配网成功！"
        case .failed(_):
            return "配网失败"
        default:
            return "配网进行中"
        }
    }
    
    private func getStatusDescription() -> String {
        switch viewModel.provisionState {
        case .configuring:
            return "正在将WiFi配置信息发送到设备，请稍候..."
        case .success:
            return "设备已成功连接到WiFi网络，配网完成"
        case .failed(let message):
            return "配网过程中出现错误：\(message)"
        default:
            return "设备正在处理配网请求"
        }
    }
}

// MARK: - WiFi配置摘要视图
struct WiFiConfigSummaryView: View {
    @ObservedObject var viewModel: ESPProvisionViewModel
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "wifi")
                    .foregroundColor(.blue)
                
                Text("WiFi配置信息")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
                
                Spacer()
            }
            
            VStack(spacing: 8) {
                // SSID
                ConfigInfoRow(
                    title: "网络名称",
                    value: viewModel.wifiSSID,
                    icon: "wifi"
                )
                
                // 安全类型
                if let network = viewModel.selectedWiFiNetwork {
                    ConfigInfoRow(
                        title: "安全类型",
                        value: viewModel.getSecurityDescription(for: network),
                        icon: "lock.shield"
                    )
                    
                    // 信号强度
                    ConfigInfoRow(
                        title: "信号强度",
                        value: viewModel.getSignalStrengthDescription(rssi: Int(network.rssi)),
                        icon: "antenna.radiowaves.left.and.right"
                    )
                }
                
                // 设备名称
                if let device = viewModel.selectedDevice {
                    ConfigInfoRow(
                        title: "设备名称",
                        value: device.name,
                        icon: "cpu"
                    )
                }
            }
        }
        .padding()
        .background(Color.blue.opacity(0.05))
        .cornerRadius(12)
    }
}

// MARK: - 配置信息行视图
struct ConfigInfoRow: View {
    let title: String
    let value: String
    let icon: String
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .foregroundColor(.blue)
                .font(.system(size: 14))
                .frame(width: 20)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
                .frame(width: 60, alignment: .leading)
            
            Text(value)
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(.primary)
                .lineLimit(1)
            
            Spacer()
        }
    }
}

// MARK: - 配网说明视图
struct ProvisioningInstructionView: View {
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: "info.circle")
                    .foregroundColor(.orange)
                
                Text("配网说明")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
            }
            
            VStack(alignment: .leading, spacing: 4) {
                ProvisioningInstructionItem(text: "配网过程需要1-2分钟，请耐心等待")
                ProvisioningInstructionItem(text: "请保持设备电源开启，不要断开连接")
                ProvisioningInstructionItem(text: "配网成功后设备将自动重启并连接WiFi")
            }
        }
        .padding()
        .background(Color.orange.opacity(0.05))
        .cornerRadius(8)
    }
}

// MARK: - 配网说明项视图
struct ProvisioningInstructionItem: View {
    let text: String
    
    var body: some View {
        HStack(alignment: .top, spacing: 6) {
            Text("•")
                .foregroundColor(.orange)
                .font(.caption)
            
            Text(text)
                .font(.caption)
                .foregroundColor(.secondary)
                .fixedSize(horizontal: false, vertical: true)
        }
    }
}

// MARK: - 预览
struct ProvisioningView_Previews: PreviewProvider {
    static var previews: some View {
        ProvisioningView(viewModel: ESPProvisionViewModel())
            .padding()
    }
}
