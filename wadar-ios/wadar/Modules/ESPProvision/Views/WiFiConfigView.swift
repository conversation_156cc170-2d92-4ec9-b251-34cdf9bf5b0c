//
//  WiFiConfigView.swift
//  wadar
//
//  Created by guan on 2025/7/1.
//  WiFi配置视图
//

import SwiftUI
import ESPProvision

/// WiFi配置视图
/// 允许用户选择WiFi网络并输入密码
struct WiFiConfigView: View {
    
    @ObservedObject var viewModel: ESPProvisionViewModel
    @State private var showPassword = false
    
    var body: some View {
        VStack(spacing: 20) {
            // WiFi网络选择
            WiFiNetworkSelectionView(viewModel: viewModel)
            
            // WiFi配置表单
            WiFiConfigFormView(
                viewModel: viewModel,
                showPassword: $showPassword
            )
            
            // 配置说明
            WiFiConfigInstructionView()
        }
    }
}

// MARK: - WiFi网络选择视图
struct WiFiNetworkSelectionView: View {
    @ObservedObject var viewModel: ESPProvisionViewModel
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("选择WiFi网络")
                    .font(.headline)
                    .foregroundColor(.primary)
                
                Spacer()
                
                Button("扫描网络") {
                    // 重新扫描WiFi网络
                    WadarESPProvisionManager.shared.scanWiFiNetworks()
                }
                .font(.caption)
                .foregroundColor(.blue)
            }
            
            if viewModel.availableWiFiNetworks.isEmpty {
                EmptyWiFiListView()
            } else {
                WiFiNetworkListView(viewModel: viewModel)
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

// MARK: - 空WiFi列表视图
struct EmptyWiFiListView: View {
    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: "wifi.slash")
                .font(.system(size: 24))
                .foregroundColor(.gray)
            
            Text("未发现WiFi网络")
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            Text("您也可以手动输入网络名称")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding(.vertical, 20)
    }
}

// MARK: - WiFi网络列表视图
struct WiFiNetworkListView: View {
    @ObservedObject var viewModel: ESPProvisionViewModel
    
    var body: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                ForEach(viewModel.availableWiFiNetworks.prefix(5), id: \.ssid) { network in
                    WiFiNetworkCardView(
                        network: network,
                        isSelected: viewModel.selectedWiFiNetwork?.ssid == network.ssid,
                        viewModel: viewModel
                    ) {
                        viewModel.selectWiFiNetwork(network)
                    }
                }
            }
            .padding(.horizontal, 4)
        }
        
        if viewModel.availableWiFiNetworks.count > 5 {
            Button("查看全部 \(viewModel.availableWiFiNetworks.count) 个网络") {
                viewModel.showWiFiList = true
            }
            .font(.caption)
            .foregroundColor(.blue)
            .padding(.top, 8)
        }
    }
}

// MARK: - WiFi网络卡片视图
struct WiFiNetworkCardView: View {
    let network: ESPWifiNetwork
    let isSelected: Bool
    let viewModel: ESPProvisionViewModel
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 6) {
                // 信号强度图标
                Image(systemName: viewModel.getSignalStrengthIcon(rssi: Int(network.rssi)))
                    .font(.system(size: 20))
                    .foregroundColor(isSelected ? .white : .blue)
                
                // 网络名称
                Text(network.ssid)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(isSelected ? .white : .primary)
                    .lineLimit(1)
                
                // 安全类型
                Text(viewModel.getSecurityDescription(for: network))
                    .font(.system(size: 10))
                    .foregroundColor(isSelected ? .white.opacity(0.8) : .secondary)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 10)
            .frame(width: 80)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(isSelected ? Color.blue : Color(.systemBackground))
                    .overlay(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(isSelected ? Color.blue : Color.gray.opacity(0.3), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - WiFi配置表单视图
struct WiFiConfigFormView: View {
    @ObservedObject var viewModel: ESPProvisionViewModel
    @Binding var showPassword: Bool
    
    var body: some View {
        VStack(spacing: 16) {
            // SSID输入
            VStack(alignment: .leading, spacing: 8) {
                Text("网络名称 (SSID)")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
                
                TextField("请输入WiFi网络名称", text: $viewModel.wifiSSID)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .autocapitalization(.none)
                    .disableAutocorrection(true)
            }
            
            // 密码输入
            VStack(alignment: .leading, spacing: 8) {
                Text("密码")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
                
                HStack {
                    if showPassword {
                        TextField("请输入WiFi密码", text: $viewModel.wifiPassword)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                    } else {
                        SecureField("请输入WiFi密码", text: $viewModel.wifiPassword)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                    }
                    
                    Button(action: {
                        showPassword.toggle()
                    }) {
                        Image(systemName: showPassword ? "eye.slash" : "eye")
                            .foregroundColor(.gray)
                    }
                }
                
                if viewModel.selectedWiFiNetwork?.auth == .open {
                    Text("此网络无需密码")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
    }
}

// MARK: - WiFi配置说明视图
struct WiFiConfigInstructionView: View {
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: "info.circle")
                    .foregroundColor(.blue)
                
                Text("配置说明")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
            }
            
            VStack(alignment: .leading, spacing: 4) {
                InstructionItem(text: "请确保WiFi网络可正常访问互联网")
                InstructionItem(text: "设备仅支持2.4GHz频段的WiFi网络")
                InstructionItem(text: "配网过程中请保持设备电源开启")
            }
        }
        .padding()
        .background(Color.blue.opacity(0.05))
        .cornerRadius(8)
    }
}

// MARK: - 说明项视图
struct InstructionItem: View {
    let text: String
    
    var body: some View {
        HStack(alignment: .top, spacing: 8) {
            Text("•")
                .foregroundColor(.blue)
                .font(.caption)
            
            Text(text)
                .font(.caption)
                .foregroundColor(.secondary)
                .fixedSize(horizontal: false, vertical: true)
        }
    }
}



struct CompletedView: View {
    @ObservedObject var viewModel: ESPProvisionViewModel
    
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "checkmark.circle.fill")
                .font(.system(size: 64))
                .foregroundColor(.green)
            
            Text("配网成功！")
                .font(.title2)
                .fontWeight(.bold)
            
            Text("设备已成功连接到WiFi网络")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding(.vertical, 40)
    }
}

// MARK: - 预览
struct WiFiConfigView_Previews: PreviewProvider {
    static var previews: some View {
        WiFiConfigView(viewModel: ESPProvisionViewModel())
            .padding()
    }
}
