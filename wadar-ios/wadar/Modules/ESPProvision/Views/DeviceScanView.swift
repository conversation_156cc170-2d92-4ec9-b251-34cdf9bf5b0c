//
//  DeviceScanView.swift
//  wadar
//
//  Created by guan on 2025/7/1.
//  设备扫描视图
//

import SwiftUI
import ESPProvision

/// 设备扫描视图
/// 显示扫描到的ESP设备列表，允许用户选择要配网的设备
struct DeviceScanView: View {

    @ObservedObject var viewModel: ESPProvisionViewModel
    @State private var showingDeviceInfo = false
    @State private var selectedDeviceForInfo: ESPDevice?

    var body: some View {
        VStack(spacing: 16) {
            // 扫描状态
            ScanStatusView(
                isScanning: viewModel.provisionState == .scanning,
                deviceCount: viewModel.discoveredDevices.count,
                statusMessage: viewModel.statusMessage
            )

            // 错误信息
            if let errorMessage = viewModel.errorMessage {
                ErrorMessageView(message: errorMessage) {
                    viewModel.startDeviceScan()
                }
            }

            // 设备列表
            if viewModel.discoveredDevices.isEmpty && viewModel.provisionState != .scanning {
                EmptyDeviceListView(isScanning: false)
            } else {
                DeviceListView(
                    devices: viewModel.discoveredDevices,
                    selectedDevice: viewModel.selectedDevice,
                    onDeviceSelected: { device in
                        viewModel.selectDevice(device)
                    },
                    onDeviceInfo: { device in
                        selectedDeviceForInfo = device
                        showingDeviceInfo = true
                    }
                )
            }

            // 扫描控制按钮
            ScanControlView(
                isScanning: viewModel.provisionState == .scanning,
                onStartScan: {
                    viewModel.startDeviceScan()
                },
                onStopScan: {
                    viewModel.stopDeviceScan()
                }
            )
        }
        .sheet(isPresented: $showingDeviceInfo) {
            if let device = selectedDeviceForInfo {
                DeviceInfoView(device: device, viewModel: viewModel)
            }
        }
    }
}

// MARK: - 扫描状态视图
struct ScanStatusView: View {
    let isScanning: Bool
    let deviceCount: Int
    let statusMessage: String

    var body: some View {
        HStack {
            if isScanning {
                ProgressView()
                    .scaleEffect(0.8)
                VStack(alignment: .leading, spacing: 2) {
                    Text("正在扫描设备...")
                        .font(.subheadline)
                        .foregroundColor(.primary)
                    if !statusMessage.isEmpty {
                        Text(statusMessage)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            } else {
                Image(systemName: "antenna.radiowaves.left.and.right")
                    .foregroundColor(.blue)
                VStack(alignment: .leading, spacing: 2) {
                    Text("发现 \(deviceCount) 个设备")
                        .font(.subheadline)
                        .foregroundColor(.primary)
                    if !statusMessage.isEmpty {
                        Text(statusMessage)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }

            Spacer()
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(8)
    }
}

// MARK: - 错误消息视图
struct ErrorMessageView: View {
    let message: String
    let onRetry: () -> Void

    var body: some View {
        HStack {
            Image(systemName: "exclamationmark.triangle")
                .foregroundColor(.orange)

            VStack(alignment: .leading, spacing: 4) {
                Text("扫描失败")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)

                Text(message)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
            }

            Spacer()

            Button("重试") {
                onRetry()
            }
            .font(.caption)
            .foregroundColor(.blue)
        }
        .padding()
        .background(Color.orange.opacity(0.1))
        .cornerRadius(8)
    }
}

// MARK: - 空设备列表视图
struct EmptyDeviceListView: View {
    let isScanning: Bool
    
    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: isScanning ? "antenna.radiowaves.left.and.right" : "exclamationmark.triangle")
                .font(.system(size: 48))
                .foregroundColor(isScanning ? .blue : .orange)
            
            Text(isScanning ? "正在搜索设备..." : "未发现设备")
                .font(.headline)
                .foregroundColor(.primary)
            
            Text(isScanning ? "请确保设备已开启并处于配网模式" : "请检查设备是否开启并重新扫描")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding(.vertical, 40)
    }
}

// MARK: - 设备列表视图
struct DeviceListView: View {
    let devices: [ESPDevice]
    let selectedDevice: ESPDevice?
    let onDeviceSelected: (ESPDevice) -> Void
    let onDeviceInfo: (ESPDevice) -> Void

    var body: some View {
        LazyVStack(spacing: 8) {
            ForEach(devices, id: \.name) { device in
                DeviceRowView(
                    device: device,
                    isSelected: selectedDevice?.name == device.name,
                    onTap: {
                        onDeviceSelected(device)
                    },
                    onInfo: {
                        onDeviceInfo(device)
                    }
                )
            }
        }
    }
}

// MARK: - 设备行视图
struct DeviceRowView: View {
    let device: ESPDevice
    let isSelected: Bool
    let onTap: () -> Void
    let onInfo: () -> Void

    var body: some View {
        HStack(spacing: 12) {
            // 设备图标
            ZStack {
                Circle()
                    .fill(isSelected ? Color.blue : Color.gray.opacity(0.2))
                    .frame(width: 40, height: 40)

                Image(systemName: "wifi")
                    .foregroundColor(isSelected ? .white : .gray)
                    .font(.system(size: 16))
            }

            // 设备信息
            VStack(alignment: .leading, spacing: 4) {
                Text(device.name)
                    .font(.headline)
                    .foregroundColor(.primary)
                    .lineLimit(1)

                HStack {
                    Text("安全模式: \(getSecurityDescription(device.security))")
                        .font(.caption)
                        .foregroundColor(.secondary)

                    Spacer()

                    Text("BLE")
                        .font(.caption)
                        .padding(.horizontal, 6)
                        .padding(.vertical, 2)
                        .background(Color.blue.opacity(0.1))
                        .foregroundColor(.blue)
                        .cornerRadius(4)
                }
            }

            Spacer()

            // 信息按钮
            Button(action: onInfo) {
                Image(systemName: "info.circle")
                    .foregroundColor(.gray)
                    .font(.system(size: 18))
            }
            .buttonStyle(PlainButtonStyle())

            // 选择指示器
            if isSelected {
                Image(systemName: "checkmark.circle.fill")
                    .foregroundColor(.blue)
                    .font(.system(size: 20))
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(isSelected ? Color.blue : Color.gray.opacity(0.2), lineWidth: isSelected ? 2 : 1)
                )
        )
        .onTapGesture {
            onTap()
        }
    }
    
    private func getSecurityDescription(_ security: ESPSecurity) -> String {
        switch security {
        case .unsecure:
            return "无加密"
        case .secure:
            return "Sec1"
        case .secure2:
            return "Sec2"
        }
    }
}

// MARK: - 扫描控制视图
struct ScanControlView: View {
    let isScanning: Bool
    let onStartScan: () -> Void
    let onStopScan: () -> Void
    
    var body: some View {
        HStack {
            Button(action: isScanning ? onStopScan : onStartScan) {
                HStack {
                    Image(systemName: isScanning ? "stop.circle" : "arrow.clockwise")
                        .font(.system(size: 16))
                    
                    Text(isScanning ? "停止扫描" : "重新扫描")
                        .font(.subheadline)
                }
                .foregroundColor(.blue)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(Color.blue.opacity(0.1))
                .cornerRadius(8)
            }
            
            Spacer()
        }
    }
}

// MARK: - 设备信息详情视图
struct DeviceInfoView: View {
    let device: ESPDevice
    let viewModel: ESPProvisionViewModel
    @Environment(\.presentationMode) var presentationMode

    var body: some View {
        NavigationView {
            VStack(alignment: .leading, spacing: 16) {
                // 设备基本信息
                VStack(alignment: .leading, spacing: 8) {
                    Text("设备信息")
                        .font(.headline)
                        .fontWeight(.semibold)

                    InfoRow(title: "设备名称", value: device.name)
                    InfoRow(title: "传输方式", value: "蓝牙 (BLE)")
                    InfoRow(title: "安全模式", value: getSecurityDescription(device.security))
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(12)

                // 详细信息
                VStack(alignment: .leading, spacing: 8) {
                    Text("详细信息")
                        .font(.headline)
                        .fontWeight(.semibold)

                    Text(viewModel.getDeviceInfo(device))
                        .font(.system(.body, design: .monospaced))
                        .foregroundColor(.secondary)
                        .padding()
                        .background(Color(.systemGray6))
                        .cornerRadius(8)
                }

                Spacer()

                // 操作按钮
                VStack(spacing: 12) {
                    Button(action: {
                        viewModel.selectDevice(device)
                        presentationMode.wrappedValue.dismiss()
                    }) {
                        HStack {
                            Image(systemName: "checkmark.circle")
                            Text("选择此设备")
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.blue)
                        .foregroundColor(.white)
                        .cornerRadius(12)
                    }

                    Button("关闭") {
                        presentationMode.wrappedValue.dismiss()
                    }
                    .foregroundColor(.secondary)
                }
            }
            .padding()
            .navigationTitle("设备详情")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(trailing: Button("完成") {
                presentationMode.wrappedValue.dismiss()
            })
        }
    }

    private func getSecurityDescription(_ security: ESPSecurity) -> String {
        switch security {
        case .unsecure:
            return "无加密"
        case .secure:
            return "Sec1 (安全)"
        case .secure2:
            return "Sec2 (高级安全)"
        }
    }
}

// MARK: - 信息行视图
struct InfoRow: View {
    let title: String
    let value: String

    var body: some View {
        HStack {
            Text(title)
                .font(.subheadline)
                .foregroundColor(.secondary)

            Spacer()

            Text(value)
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(.primary)
        }
    }
}

// MARK: - 预览
struct DeviceScanView_Previews: PreviewProvider {
    static var previews: some View {
        DeviceScanView(viewModel: ESPProvisionViewModel())
            .padding()
    }
}
