//
//  ESPProvisionView.swift
//  wadar
//
//  Created by guan on 2025/7/1.
//  ESP设备配网主界面
//

import SwiftUI
import ESPProvision

/// ESP设备配网主界面
/// 提供完整的设备配网流程界面
struct ESPProvisionView: View {

    @EnvironmentObject var viewModel: ESPProvisionViewModel
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 顶部进度指示器
                ProgressIndicatorView(
                    currentStep: viewModel.currentStep,
                    progress: viewModel.provisionProgress
                )
                .padding(.horizontal)
                .padding(.top)
                
                // 主要内容区域
                ScrollView {
                    VStack(spacing: 20) {
                        // 状态消息
                        if !viewModel.statusMessage.isEmpty {
                            StatusMessageView(message: viewModel.statusMessage)
                                .padding(.horizontal)
                        }
                        
                        // 根据当前步骤显示不同内容
                        switch viewModel.currentStep {
                        case .deviceScan:
                            DeviceScanView(viewModel: viewModel)
                        case .deviceConnect:
                            DeviceConnectView(viewModel: viewModel)
                        case .wifiConfig:
                            WiFiConfigView(viewModel: viewModel)
                        case .provisioning:
                            ProvisioningView(viewModel: viewModel)
                        case .completed:
                            CompletedView(viewModel: viewModel)
                        }
                    }
                    .padding(.horizontal)
                }
                
                // 底部操作按钮
                BottomActionView(viewModel: viewModel)
                    .padding()
            }
            .navigationTitle("设备配网")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(
                leading: Button("返回") {
                    dismiss()
                },
                trailing: Button("重置") {
                    viewModel.restartProvisioning()
                }
                .disabled(viewModel.isLoading)
            )
        }
        .onAppear {
            viewModel.startDeviceScan()
        }
        .onDisappear {
            viewModel.stopDeviceScan()
        }
        .alert(isPresented: $viewModel.showAlert) {
            Alert(
                title: Text("提示"),
                message: Text(viewModel.alertMessage),
                dismissButton: .default(Text("确定"))
            )
        }
    }
}

// MARK: - 进度指示器视图
struct ProgressIndicatorView: View {
    let currentStep: ESPProvisionViewModel.ProvisionStep
    let progress: Double
    
    var body: some View {
        VStack(spacing: 12) {
            // 步骤指示器
            HStack {
                ForEach(ESPProvisionViewModel.ProvisionStep.allCases, id: \.rawValue) { step in
                    StepIndicatorItem(
                        step: step,
                        isActive: step.rawValue <= currentStep.rawValue,
                        isCurrent: step == currentStep
                    )
                    
                    if step != ESPProvisionViewModel.ProvisionStep.allCases.last {
                        Rectangle()
                            .fill(step.rawValue < currentStep.rawValue ? Color.blue : Color.gray.opacity(0.3))
                            .frame(height: 2)
                            .frame(maxWidth: .infinity)
                    }
                }
            }
            
            // 进度条
            if progress > 0 {
                ProgressView(value: progress)
                    .progressViewStyle(LinearProgressViewStyle(tint: .blue))
                    .scaleEffect(y: 2)
            }
            
            // 当前步骤信息
            VStack(spacing: 4) {
                Text(currentStep.title)
                    .font(.headline)
                    .foregroundColor(.primary)
                
                Text(currentStep.description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding(.vertical)
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
}

// MARK: - 步骤指示器项
struct StepIndicatorItem: View {
    let step: ESPProvisionViewModel.ProvisionStep
    let isActive: Bool
    let isCurrent: Bool
    
    var body: some View {
        ZStack {
            Circle()
                .fill(isActive ? Color.blue : Color.gray.opacity(0.3))
                .frame(width: 30, height: 30)
            
            if isActive && !isCurrent {
                Image(systemName: "checkmark")
                    .foregroundColor(.white)
                    .font(.system(size: 12, weight: .bold))
            } else {
                Text("\(step.rawValue + 1)")
                    .foregroundColor(isActive ? .white : .gray)
                    .font(.system(size: 12, weight: .bold))
            }
        }
        .scaleEffect(isCurrent ? 1.2 : 1.0)
        .animation(.easeInOut(duration: 0.3), value: isCurrent)
    }
}

// MARK: - 状态消息视图
struct StatusMessageView: View {
    let message: String
    
    var body: some View {
        HStack {
            Image(systemName: "info.circle")
                .foregroundColor(.blue)
            
            Text(message)
                .font(.subheadline)
                .foregroundColor(.primary)
            
            Spacer()
        }
        .padding()
        .background(Color.blue.opacity(0.1))
        .cornerRadius(8)
    }
}

// MARK: - 底部操作视图
struct BottomActionView: View {
    @ObservedObject var viewModel: ESPProvisionViewModel
    
    var body: some View {
        HStack(spacing: 16) {
            // 返回按钮
            if viewModel.canGoBack {
                Button("上一步") {
                    viewModel.previousStep()
                }
                .buttonStyle(SecondaryButtonStyle())
                .disabled(viewModel.isLoading)
            }
            
            Spacer()
            
            // 主要操作按钮
            Button(actionButtonTitle) {
                handleActionButtonTap()
            }
            .buttonStyle(PrimaryButtonStyle())
            .disabled(!viewModel.canProceedToNext || viewModel.isLoading)
        }
    }
    
    private var actionButtonTitle: String {
        switch viewModel.currentStep {
        case .deviceScan:
            return "连接设备"
        case .deviceConnect:
            return "配置WiFi"
        case .wifiConfig:
            return "开始配网"
        case .provisioning:
            return "完成"
        case .completed:
            return "完成配网"
        }
    }
    
    private func handleActionButtonTap() {
        switch viewModel.currentStep {
        case .completed:
            viewModel.completeProvisioning()
        default:
            viewModel.nextStep()
        }
    }
}

// MARK: - 按钮样式
struct PrimaryButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .foregroundColor(.white)
            .padding(.horizontal, 24)
            .padding(.vertical, 12)
            .background(Color.blue)
            .cornerRadius(8)
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

struct SecondaryButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .foregroundColor(.blue)
            .padding(.horizontal, 24)
            .padding(.vertical, 12)
            .background(Color.blue.opacity(0.1))
            .cornerRadius(8)
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

// MARK: - 预览
struct ESPProvisionView_Previews: PreviewProvider {
    static var previews: some View {
        ESPProvisionView()
            .environmentObject(ESPProvisionViewModel())
    }
}
