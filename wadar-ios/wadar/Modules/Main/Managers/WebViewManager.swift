
import Foundation
import WebKit
import SwiftUI

/// WebView管理器，实现预加载和缓存策略，以及VoIP通话后的状态保持
class WebViewManager: ObservableObject {
    static let shared = WebViewManager()

    // 预加载的WebView实例
    private var preloadedWebView: WKWebView?
    private var preloadedURL: URL?

    // VoIP通话前的WebView状态保存
    private var savedWebViewState: WebViewState?

    // 保持导航代理的强引用
    private var preloadNavigationDelegate: PreloadNavigationDelegate?

    // 当前活跃的WebView实例（用于生命周期管理）
    private weak var activeWebView: WKWebView?
    private var lastForegroundTime: Date?
    private var backgroundTime: Date?

    // WebView状态结构
    private struct WebViewState {
        let url: URL
        let timestamp: Date
        let userAgent: String?
        let scrollPosition: CGPoint?
    }
    
    // WebView配置
    private var webViewConfiguration: WKWebViewConfiguration {
        let config = WKWebViewConfiguration()
        config.mediaTypesRequiringUserActionForPlayback = []
        config.defaultWebpagePreferences.allowsContentJavaScript = true
        config.preferences.javaScriptCanOpenWindowsAutomatically = false
        config.suppressesIncrementalRendering = false
        
        // 使用持久化数据存储以支持缓存
        config.websiteDataStore = WKWebsiteDataStore.default()
        
        // 使用新的通用WebView桥接器
        _ = WebViewBridge()
        // 注意：这里只是配置，实际的WebView会在使用时创建
        
        return config
    }
    
    private init() {
        setupWebViewCache()
        setupApplicationLifecycleObservers()
    }
    
    // MARK: - 缓存设置
    
    /// 设置WebView缓存策略
    private func setupWebViewCache() {
        // 设置缓存大小限制（100MB）
        let cacheSize = 100 * 1024 * 1024
        URLCache.shared.memoryCapacity = cacheSize
        URLCache.shared.diskCapacity = cacheSize

        // 配置WebView数据存储
        _ = WKWebsiteDataStore.default()

        // 设置HTTP缓存策略 - 改为更智能的缓存策略
        let configuration = URLSessionConfiguration.default
        configuration.requestCachePolicy = .useProtocolCachePolicy // 使用协议缓存策略，遵循HTTP缓存头
        configuration.urlCache = URLCache.shared
        configuration.timeoutIntervalForRequest = 30.0
        configuration.timeoutIntervalForResource = 60.0
    }
    
    // MARK: - 预加载功能
    
    /// 预加载指定URL的WebView - 当前已禁用以避免缓存问题
    /// - Parameter url: 要预加载的URL
    func preloadWebView(for url: URL) {
        NSLog("[WebViewManager] 预加载已禁用以避免缓存问题: %@", url.absoluteString)
        // 注意：已禁用预加载机制以避免缓存导致的空白页面问题
    }
    
    /// 获取预加载的WebView - 当前总是返回nil
    /// - Parameter url: 请求的URL
    /// - Returns: 总是返回nil，因为预加载已禁用
    func getPreloadedWebView(for url: URL) -> WKWebView? {
        NSLog("[WebViewManager] 预加载已禁用，返回nil: %@", url.absoluteString)
        return nil
    }
    
    /// 清理预加载的WebView
    private func cleanupPreloadedWebView() {
        if let webView = preloadedWebView {
            webView.stopLoading()
            webView.navigationDelegate = nil
            print("[WebViewManager] 清理旧的预加载WebView")
        }
        preloadedWebView = nil
        preloadedURL = nil
        preloadNavigationDelegate = nil
    }
    
    // MARK: - 缓存管理
    
    /// 清理WebView缓存
    func clearCache() {
        print("[WebViewManager] 清理WebView缓存")
        
        let dataStore = WKWebsiteDataStore.default()
        let dataTypes = WKWebsiteDataStore.allWebsiteDataTypes()
        
        dataStore.removeData(ofTypes: dataTypes, modifiedSince: Date(timeIntervalSince1970: 0)) {
            print("[WebViewManager] WebView缓存清理完成")
        }
        
        // 清理URL缓存
        URLCache.shared.removeAllCachedResponses()
    }
    
    /// 预热缓存 - 预加载常用页面
    func warmupCache() {
        guard let baseURL = URL(string: AppConfig.shared.baseURL) else { return }

        print("[WebViewManager] 开始预热缓存")

        // 预加载首页
        let indexURL = baseURL.appendingPathComponent("pages/index/index")
        preloadWebView(for: indexURL)
    }

    // MARK: - VoIP通话状态保持

    /// 保存WebView状态（在VoIP通话开始前调用）
    func saveWebViewStateBeforeCall(webView: WKWebView) {
        guard let url = webView.url else { return }

        print("[WebViewManager] 保存WebView状态: \(url.absoluteString)")

        // 获取滚动位置
        webView.evaluateJavaScript("window.pageYOffset") { result, error in
            let scrollY = result as? CGFloat ?? 0

            self.savedWebViewState = WebViewState(
                url: url,
                timestamp: Date(),
                userAgent: webView.customUserAgent,
                scrollPosition: CGPoint(x: 0, y: scrollY)
            )

            print("[WebViewManager] WebView状态已保存，滚动位置: \(scrollY)")
        }
    }

    /// 恢复WebView状态（在VoIP通话结束后调用）
    func restoreWebViewStateAfterCall(webView: WKWebView, completion: @escaping (Bool) -> Void) {
        guard let savedState = savedWebViewState else {
            print("[WebViewManager] 没有保存的WebView状态")
            completion(false)
            return
        }

        // 检查状态是否过期（超过30分钟则认为过期）
        let stateAge = Date().timeIntervalSince(savedState.timestamp)
        if stateAge > 30 * 60 {
            print("[WebViewManager] WebView状态已过期，清除保存的状态")
            savedWebViewState = nil
            completion(false)
            return
        }

        print("[WebViewManager] 恢复WebView状态: \(savedState.url.absoluteString)")

        // 如果当前URL与保存的URL相同，只恢复滚动位置
        if webView.url == savedState.url {
            restoreScrollPosition(webView: webView, scrollPosition: savedState.scrollPosition) {
                completion(true)
            }
        } else {
            // URL不同，需要重新加载页面
            var request = URLRequest(url: savedState.url)
            request.setValue("IPv4", forHTTPHeaderField: "X-Preferred-IP-Version")
            request.cachePolicy = .returnCacheDataElseLoad

            // 设置导航完成监听
            let navigationDelegate = RestoreNavigationDelegate(
                targetScrollPosition: savedState.scrollPosition,
                completion: completion
            )

            webView.navigationDelegate = navigationDelegate
            webView.load(request)
        }

        // 清除保存的状态
        savedWebViewState = nil
    }

    /// 恢复滚动位置
    private func restoreScrollPosition(webView: WKWebView, scrollPosition: CGPoint?, completion: @escaping () -> Void) {
        guard let scrollPosition = scrollPosition else {
            completion()
            return
        }

        let script = "window.scrollTo(\(scrollPosition.x), \(scrollPosition.y));"
        webView.evaluateJavaScript(script) { _, error in
            if let error = error {
                print("[WebViewManager] 恢复滚动位置失败: \(error.localizedDescription)")
            } else {
                print("[WebViewManager] 滚动位置已恢复: \(scrollPosition)")
            }
            completion()
        }
    }

    /// 检查是否有保存的WebView状态
    func hasSavedWebViewState() -> Bool {
        return savedWebViewState != nil
    }

    /// 清除保存的WebView状态
    func clearSavedWebViewState() {
        savedWebViewState = nil
        print("[WebViewManager] 已清除保存的WebView状态")
    }

    // MARK: - 应用生命周期管理

    /// 设置应用生命周期观察者
    private func setupApplicationLifecycleObservers() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(applicationDidEnterBackground),
            name: UIApplication.didEnterBackgroundNotification,
            object: nil
        )

        NotificationCenter.default.addObserver(
            self,
            selector: #selector(applicationWillEnterForeground),
            name: UIApplication.willEnterForegroundNotification,
            object: nil
        )
    }

    @objc private func applicationDidEnterBackground() {
        backgroundTime = Date()
        print("[WebViewManager] 应用进入后台，记录时间: \(backgroundTime!)")
    }

    @objc private func applicationWillEnterForeground() {
        lastForegroundTime = Date()
        print("[WebViewManager] 应用即将进入前台，记录时间: \(lastForegroundTime!)")

        // 检查是否需要刷新WebView
        checkAndRefreshWebViewIfNeeded()
    }

    /// 检查并在需要时刷新WebView
    private func checkAndRefreshWebViewIfNeeded() {
        guard let activeWebView = activeWebView,
              let backgroundTime = backgroundTime else {
            print("[WebViewManager] 没有活跃的WebView或后台时间记录")
            return
        }

        let backgroundDuration = Date().timeIntervalSince(backgroundTime)
        print("[WebViewManager] 后台时长: \(backgroundDuration) 秒")

        // 如果后台时间超过5分钟，强制刷新WebView
        if backgroundDuration > 5 * 60 {
            print("[WebViewManager] 后台时间过长，强制刷新WebView")
            refreshWebView(activeWebView)
        } else if backgroundDuration > 30 {
            // 如果后台时间超过30秒，检查页面是否需要刷新
            print("[WebViewManager] 检查页面是否需要刷新")
            checkPageFreshness(activeWebView)
        }
    }

    /// 刷新WebView内容
    func refreshWebView(_ webView: WKWebView) {
        guard let currentURL = webView.url else {
            print("[WebViewManager] WebView没有当前URL，无法刷新")
            return
        }

        print("[WebViewManager] 刷新WebView: \(currentURL.absoluteString)")

        // 清除WebView缓存，避免加载空白页面
        let websiteDataStore = webView.configuration.websiteDataStore
        let dataTypes = Set([WKWebsiteDataTypeDiskCache, WKWebsiteDataTypeMemoryCache])

        websiteDataStore.removeData(ofTypes: dataTypes, modifiedSince: Date.distantPast) {
            DispatchQueue.main.async {
                // 创建新的请求，避免缓存
                var request = URLRequest(url: currentURL)
                request.setValue("IPv4", forHTTPHeaderField: "X-Preferred-IP-Version")
                request.cachePolicy = .reloadIgnoringLocalAndRemoteCacheData
                request.setValue("no-cache", forHTTPHeaderField: "Cache-Control")
                request.setValue("no-store", forHTTPHeaderField: "Cache-Control")
                request.setValue("\(Date().timeIntervalSince1970)", forHTTPHeaderField: "X-Refresh-Timestamp")

                print("[WebViewManager] 清除缓存后重新加载")
                webView.load(request)
            }
        }
    }

    /// 检查页面新鲜度
    private func checkPageFreshness(_ webView: WKWebView) {
        // 通过JavaScript检查页面是否需要刷新
        let script = """
            (function() {
                // 检查页面是否有错误或空白
                if (!document.body || document.body.innerHTML.trim().length === 0) {
                    return 'empty';
                }

                // 检查是否有网络错误页面
                if (document.title.includes('错误') || document.title.includes('Error') ||
                    document.body.innerHTML.includes('网络错误') ||
                    document.body.innerHTML.includes('加载失败') ||
                    document.body.innerHTML.includes('页面不存在') ||
                    document.body.innerHTML.includes('服务器错误')) {
                    return 'error';
                }

                // 检查是否是空白页面或加载中页面
                var bodyText = document.body.innerText.trim();
                if (bodyText.length < 50 || bodyText.includes('加载中') || bodyText.includes('Loading')) {
                    return 'loading';
                }

                // 检查页面最后更新时间（如果页面有提供）
                var lastUpdate = document.querySelector('[data-last-update]');
                if (lastUpdate) {
                    var updateTime = parseInt(lastUpdate.getAttribute('data-last-update'));
                    var now = Date.now();
                    if (now - updateTime > 300000) { // 5分钟
                        return 'stale';
                    }
                }

                // 检查是否有Vue或React应用的根元素但没有内容
                var vueApp = document.querySelector('#app, [data-v-], .vue-app');
                var reactApp = document.querySelector('#root, [data-reactroot], .react-app');
                if ((vueApp || reactApp) && bodyText.length < 100) {
                    return 'app_not_loaded';
                }

                return 'fresh';
            })();
        """

        webView.evaluateJavaScript(script) { [weak self] result, error in
            if let error = error {
                print("[WebViewManager] 检查页面新鲜度失败: \(error.localizedDescription)")
                // 如果JavaScript执行失败，可能页面有问题，尝试刷新
                DispatchQueue.main.async {
                    self?.refreshWebView(webView)
                }
                return
            }

            if let status = result as? String {
                print("[WebViewManager] 页面状态: \(status)")

                switch status {
                case "empty", "error", "loading", "stale", "app_not_loaded":
                    print("[WebViewManager] 页面需要刷新，状态: \(status)")
                    DispatchQueue.main.async {
                        self?.refreshWebView(webView)
                    }
                case "fresh":
                    print("[WebViewManager] 页面状态良好，无需刷新")
                default:
                    print("[WebViewManager] 未知页面状态: \(status)")
                }
            }
        }
    }

    /// 注册活跃的WebView（供OptimizedWebView调用）
    func registerActiveWebView(_ webView: WKWebView) {
        activeWebView = webView
        print("[WebViewManager] 注册活跃WebView: \(webView.url?.absoluteString ?? "unknown")")
    }

    /// 注销活跃的WebView
    func unregisterActiveWebView(_ webView: WKWebView) {
        if activeWebView === webView {
            activeWebView = nil
            print("[WebViewManager] 注销活跃WebView")
        }
    }

    /// 手动刷新当前活跃的WebView
    func refreshCurrentWebView() {
        guard let activeWebView = activeWebView else {
            print("[WebViewManager] 没有活跃的WebView可以刷新")
            return
        }

        print("[WebViewManager] 手动刷新当前WebView")
        refreshWebView(activeWebView)
    }

    /// 强制重新加载当前WebView（清除所有缓存）
    func forceReloadCurrentWebView() {
        guard let activeWebView = activeWebView,
              let currentURL = activeWebView.url else {
            print("[WebViewManager] 没有活跃的WebView或URL可以重新加载")
            return
        }

        print("[WebViewManager] 强制重新加载WebView，清除缓存")

        // 注意：已移除Vue重试计数机制

        // 清除所有网站数据（更彻底的清理）
        let dataStore = WKWebsiteDataStore.default()
        let dataTypes = WKWebsiteDataStore.allWebsiteDataTypes()
        dataStore.removeData(ofTypes: dataTypes, modifiedSince: Date(timeIntervalSince1970: 0)) {
            DispatchQueue.main.async {
                print("[WebViewManager] 缓存清除完成，重新加载页面")

                var request = URLRequest(url: currentURL)
                request.setValue("IPv4", forHTTPHeaderField: "X-Preferred-IP-Version")
                request.cachePolicy = .reloadIgnoringLocalAndRemoteCacheData
                request.setValue("no-cache, no-store, must-revalidate", forHTTPHeaderField: "Cache-Control")
                request.setValue("no-cache", forHTTPHeaderField: "Pragma")
                request.setValue("0", forHTTPHeaderField: "Expires")
                request.setValue("\(Date().timeIntervalSince1970)", forHTTPHeaderField: "X-Force-Reload")

                activeWebView.load(request)
            }
        }
    }

    deinit {
        NotificationCenter.default.removeObserver(self)
    }
}

// MARK: - 恢复状态导航代理

private class RestoreNavigationDelegate: NSObject, WKNavigationDelegate {
    private let targetScrollPosition: CGPoint?
    private let completion: (Bool) -> Void

    init(targetScrollPosition: CGPoint?, completion: @escaping (Bool) -> Void) {
        self.targetScrollPosition = targetScrollPosition
        self.completion = completion
        super.init()
    }

    func webView(_ webView: WKWebView, didFinish navigation: WKNavigation!) {
        print("[RestoreNavigationDelegate] 页面加载完成，恢复滚动位置")

        // 延迟一下确保页面完全渲染
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            if let scrollPosition = self.targetScrollPosition {
                let script = "window.scrollTo(\(scrollPosition.x), \(scrollPosition.y));"
                webView.evaluateJavaScript(script) { _, error in
                    if let error = error {
                        print("[RestoreNavigationDelegate] 恢复滚动位置失败: \(error.localizedDescription)")
                        self.completion(false)
                    } else {
                        print("[RestoreNavigationDelegate] 滚动位置已恢复: \(scrollPosition)")
                        self.completion(true)
                    }
                }
            } else {
                self.completion(true)
            }
        }
    }

    func webView(_ webView: WKWebView, didFail navigation: WKNavigation!, withError error: Error) {
        print("[RestoreNavigationDelegate] 页面加载失败: \(error.localizedDescription)")
        completion(false)
    }
}

// MARK: - 预加载导航代理

private class PreloadNavigationDelegate: NSObject, WKNavigationDelegate {
    
    func webView(_ webView: WKWebView, didStartProvisionalNavigation navigation: WKNavigation!) {
        print("[WebViewManager] 预加载开始: \(webView.url?.absoluteString ?? "unknown")")
    }
    
    func webView(_ webView: WKWebView, didFinish navigation: WKNavigation!) {
        print("[WebViewManager] 预加载完成: \(webView.url?.absoluteString ?? "unknown")")
    }
    
    func webView(_ webView: WKWebView, didFail navigation: WKNavigation!, withError error: Error) {
        print("[WebViewManager] 预加载失败: \(error.localizedDescription)")
    }
    
    func webView(_ webView: WKWebView, didFailProvisionalNavigation navigation: WKNavigation!, withError error: Error) {
        print("[WebViewManager] 预加载预备失败: \(error.localizedDescription)")
    }
}

// MARK: - 优化的WebView组件

struct OptimizedWebView: UIViewRepresentable {
    let url: URL
    @StateObject private var webViewManager = WebViewManager.shared

    func makeCoordinator() -> Coordinator {
        return Coordinator(webViewManager: webViewManager)
    }
    
    func makeUIView(context: Context) -> WKWebView {
        NSLog("[OptimizedWebView] 开始创建WebView，目标URL: %@", url.absoluteString)

        // 尝试获取预加载的WebView
        if let preloadedWebView = webViewManager.getPreloadedWebView(for: url) {
            print("[OptimizedWebView] ✅ 使用预加载的WebView")
            preloadedWebView.navigationDelegate = context.coordinator
            context.coordinator.setWebView(preloadedWebView)
            return preloadedWebView
        }

        // 没有预加载的WebView，创建新的
        print("[OptimizedWebView] 🔄 创建新的WebView")
        let config = WKWebViewConfiguration()
        config.mediaTypesRequiringUserActionForPlayback = []
        config.defaultWebpagePreferences.allowsContentJavaScript = true
        config.preferences.javaScriptCanOpenWindowsAutomatically = false
        config.suppressesIncrementalRendering = false

        // 使用非持久化数据存储，禁用缓存
        config.websiteDataStore = WKWebsiteDataStore.nonPersistent()
        print("[OptimizedWebView] WebView配置完成")

        // 使用新的通用WebView桥接器
        let webViewBridge = WebViewBridge()

        let webView = WKWebView(frame: .zero, configuration: config)
        print("[OptimizedWebView] WebView实例创建完成")

        // 配置WebView桥接器
        webViewBridge.configureWebView(webView)
        print("[OptimizedWebView] WebView桥接器配置完成")

        webView.navigationDelegate = context.coordinator
        context.coordinator.setWebView(webView)

        // 注册到WebViewManager进行生命周期管理
        WebViewManager.shared.registerActiveWebView(webView)

        // 创建优化的请求，避免缓存空白页面
        var request = URLRequest(url: url)
        request.setValue("IPv4", forHTTPHeaderField: "X-Preferred-IP-Version")
        // 使用更保守的缓存策略，确保获取最新内容
        request.cachePolicy = .reloadIgnoringLocalCacheData
        request.setValue("no-cache", forHTTPHeaderField: "Cache-Control")
        request.setValue("\(Date().timeIntervalSince1970)", forHTTPHeaderField: "X-Initial-Load")

        webView.load(request)

        return webView
    }
    
    func updateUIView(_ uiView: WKWebView, context: Context) {
        if uiView.url != url {
            NSLog("[OptimizedWebView] 🔄 URL变化，重新加载")
            NSLog("[OptimizedWebView] 当前URL: %@", uiView.url?.absoluteString ?? "nil")
            NSLog("[OptimizedWebView] 目标URL: %@", url.absoluteString)

            // 创建新的请求，完全禁用缓存
            var request = URLRequest(url: url)
            request.setValue("IPv4", forHTTPHeaderField: "X-Preferred-IP-Version")
            request.cachePolicy = .reloadIgnoringLocalAndRemoteCacheData
            request.setValue("no-cache, no-store, must-revalidate", forHTTPHeaderField: "Cache-Control")
            request.setValue("no-cache", forHTTPHeaderField: "Pragma")
            request.setValue("0", forHTTPHeaderField: "Expires")
            request.setValue("\(Date().timeIntervalSince1970)", forHTTPHeaderField: "X-Timestamp")

            NSLog("[OptimizedWebView] 请求头设置完成，开始加载")
            uiView.load(request)
        } else {
            print("[OptimizedWebView] URL未变化，无需重新加载: \(url.absoluteString)")
        }
    }
}

extension OptimizedWebView {
    class Coordinator: NSObject, WKNavigationDelegate {
        private let webViewManager: WebViewManager
        private weak var webView: WKWebView?

        init(webViewManager: WebViewManager) {
            self.webViewManager = webViewManager
            super.init()
            setupNotificationObservers()
        }

        deinit {
            NotificationCenter.default.removeObserver(self)
        }

        private func setupNotificationObservers() {
            // 监听保存WebView状态的通知
            NotificationCenter.default.addObserver(
                self,
                selector: #selector(saveWebViewState),
                name: .saveWebViewState,
                object: nil
            )

            // 监听恢复WebView状态的通知
            NotificationCenter.default.addObserver(
                self,
                selector: #selector(restoreWebViewState),
                name: .restoreWebViewState,
                object: nil
            )
        }

        @objc private func saveWebViewState() {
            guard let webView = webView else { return }
            webViewManager.saveWebViewStateBeforeCall(webView: webView)
        }

        @objc private func restoreWebViewState() {
            guard let webView = webView else { return }
            webViewManager.restoreWebViewStateAfterCall(webView: webView) { success in
                if success {
                    print("[OptimizedWebView] WebView状态恢复成功")
                } else {
                    print("[OptimizedWebView] WebView状态恢复失败")
                }
            }
        }

        func setWebView(_ webView: WKWebView) {
            // 如果之前有WebView，先注销
            if let oldWebView = self.webView {
                WebViewManager.shared.unregisterActiveWebView(oldWebView)
            }

            self.webView = webView

            // 注册新的WebView
            WebViewManager.shared.registerActiveWebView(webView)
        }

        func webView(_ webView: WKWebView, didStartProvisionalNavigation navigation: WKNavigation!) {
            NSLog("[OptimizedWebView] 🚀 开始加载页面")
            NSLog("[OptimizedWebView] 加载URL: %@", webView.url?.absoluteString ?? "unknown")
            NSLog("[OptimizedWebView] 导航对象: %@", navigation?.description ?? "nil")
        }

        func webView(_ webView: WKWebView, didFinish navigation: WKNavigation!) {
            NSLog("[OptimizedWebView] ✅ 页面加载完成")
            NSLog("[OptimizedWebView] 最终URL: %@", webView.url?.absoluteString ?? "unknown")
            NSLog("[OptimizedWebView] 页面标题: %@", webView.title ?? "无标题")

            // 检查页面内容
            webView.evaluateJavaScript("document.body.innerHTML.length") { result, error in
                if let length = result as? Int {
                    NSLog("[OptimizedWebView] 页面内容长度: %d", length)
                    if length == 0 {
                        NSLog("[OptimizedWebView] ⚠️ 页面内容为空，可能是空白页面")
                    }
                } else if let error = error {
                    NSLog("[OptimizedWebView] 无法获取页面内容: %@", error.localizedDescription)
                }
            }

            // 检查页面是否可见
            webView.evaluateJavaScript("document.body.style.display") { result, error in
                if let display = result as? String {
                    NSLog("[OptimizedWebView] 页面显示状态: %@", display)
                } else if let error = error {
                    NSLog("[OptimizedWebView] 无法获取页面显示状态: %@", error.localizedDescription)
                }
            }

            // 注入Vue应用监听脚本
            self.injectVueMonitorScript(webView)

            // 给H5应用更多时间来渲染，特别是Vue/React应用
            print("[OptimizedWebView] 等待5秒后检查页面内容")
            DispatchQueue.main.asyncAfter(deadline: .now() + 5.0) {
                self.checkPageContentAfterLoad(webView)
            }
        }

        /// 注入Vue应用监听脚本
        private func injectVueMonitorScript(_ webView: WKWebView) {
            let script = """
                (function() {
                    console.log('[WebView] 注入Vue监听脚本');

                    // 监听DOM变化
                    var observer = new MutationObserver(function(mutations) {
                        mutations.forEach(function(mutation) {
                            if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                                var app = document.querySelector('#app');
                                if (app && app.children.length > 0) {
                                    console.log('[WebView] 检测到Vue应用内容变化');
                                }
                            }
                        });
                    });

                    // 开始观察
                    var app = document.querySelector('#app');
                    if (app) {
                        observer.observe(app, { childList: true, subtree: true });
                        console.log('[WebView] 开始监听Vue应用变化');
                    }

                    // 检查Vue是否已经存在
                    if (window.Vue) {
                        console.log('[WebView] 检测到Vue框架');
                    }

                    // 设置超时检查
                    setTimeout(function() {
                        var app = document.querySelector('#app');
                        if (app && app.children.length > 0) {
                            console.log('[WebView] Vue应用已挂载，内容数量:', app.children.length);
                        } else {
                            console.log('[WebView] Vue应用仍未挂载');
                        }
                    }, 3000);
                })();
            """

            webView.evaluateJavaScript(script) { result, error in
                if let error = error {
                    print("[OptimizedWebView] 注入Vue监听脚本失败: \(error.localizedDescription)")
                } else {
                    print("[OptimizedWebView] Vue监听脚本注入成功")
                }
            }
        }

        /// 检查页面加载完成后的内容
        private func checkPageContentAfterLoad(_ webView: WKWebView) {
            let script = """
                (function() {
                    // 检查页面是否真正加载完成
                    if (!document.body || document.body.innerHTML.trim().length === 0) {
                        return 'empty';
                    }

                    var bodyText = document.body.innerText.trim();
                    var htmlContent = document.body.innerHTML.trim();

                    // 放宽内容检测条件，考虑到H5应用可能需要时间渲染
                    if (bodyText.length < 10 && htmlContent.length < 100) {
                        return 'insufficient_content';
                    }

                    // 检查是否还在加载中
                    if (bodyText.includes('加载中') || bodyText.includes('Loading') ||
                        document.querySelector('.loading, .spinner, [class*="load"]')) {
                        return 'still_loading';
                    }

                    // 检查Vue应用状态
                    var vueApp = document.querySelector('#app');
                    if (vueApp) {
                        // 检查Vue应用是否已挂载
                        if (vueApp.children.length === 0) {
                            return 'vue_not_mounted';
                        }

                        // 检查Vue实例是否存在
                        if (window.Vue || vueApp.__vue__ || vueApp._vnode) {
                            // Vue应用已挂载且有内容
                            if (vueApp.children.length > 0) {
                                return 'vue_mounted';
                            }
                        }

                        // 检查是否有Vue 3的应用实例
                        if (vueApp.__vue_app__) {
                            return 'vue_mounted';
                        }
                    }

                    // 检查是否有错误页面
                    if (bodyText.includes('404') || bodyText.includes('500') ||
                        bodyText.includes('网络错误') || bodyText.includes('页面不存在')) {
                        return 'error_page';
                    }

                    return 'loaded';
                })();
            """

            webView.evaluateJavaScript(script) { result, error in
                if let error = error {
                    print("[OptimizedWebView] 检查页面内容失败: \(error.localizedDescription)")
                    return
                }

                if let status = result as? String {
                    print("[OptimizedWebView] 页面内容状态: \(status)")

                    switch status {
                    case "empty", "error_page":
                        print("[OptimizedWebView] 页面内容异常，运行详细诊断")
                        self.runDetailedDiagnosis(webView)
                    case "insufficient_content":
                        print("[OptimizedWebView] 页面内容不足，等待更长时间后再检查")
                        DispatchQueue.main.asyncAfter(deadline: .now() + 5.0) {
                            self.checkPageContentAfterLoad(webView)
                        }
                    case "vue_not_mounted":
                        print("[OptimizedWebView] Vue应用未挂载，但不再重试以避免无限循环")
                        // 注意：已移除重试机制，避免不断重新加载导致空白页面
                    case "vue_mounted":
                        print("[OptimizedWebView] Vue应用已成功挂载")
                    case "still_loading":
                        print("[OptimizedWebView] 页面仍在加载，等待更长时间")
                        DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
                            self.checkPageContentAfterLoad(webView)
                        }
                    case "loaded":
                        print("[OptimizedWebView] 页面内容正常")
                    default:
                        print("[OptimizedWebView] 未知内容状态: \(status)")
                    }
                }
            }
        }

        /// 运行详细诊断
        private func runDetailedDiagnosis(_ webView: WKWebView) {
            print("[OptimizedWebView] 运行详细诊断")

            // 简化的诊断逻辑，直接尝试刷新
            print("[OptimizedWebView] 页面内容异常，尝试刷新")
            DispatchQueue.main.async {
                WebViewManager.shared.refreshWebView(webView)
            }

            // TODO: 集成WebViewDebugger进行详细诊断
            /*
            WebViewDebugger.shared.diagnoseWebView(webView) { result in
                print("[OptimizedWebView] 详细诊断结果:")
                print("  URL: \(result.url)")
                print("  标题: \(result.title)")
                print("  加载进度: \(result.estimatedProgress)")
                print("  内容状态: 文本长度=\(result.contentStatus.bodyTextLength), HTML长度=\(result.contentStatus.bodyHTMLLength)")
                print("  网络状态: 可达=\(result.networkStatus.isReachable), 状态码=\(result.networkStatus.statusCode)")
                print("  建议:")
                for suggestion in result.suggestions {
                    print("    - \(suggestion)")
                }

                // 根据诊断结果决定是否刷新
                if !result.networkStatus.isReachable || result.networkStatus.statusCode >= 400 {
                    print("[OptimizedWebView] 网络问题，尝试刷新")
                    DispatchQueue.main.async {
                        WebViewManager.shared.refreshWebView(webView)
                    }
                } else if result.contentStatus.bodyTextLength < 10 && !result.contentStatus.isLoading {
                    print("[OptimizedWebView] 内容异常且非加载状态，尝试刷新")
                    DispatchQueue.main.async {
                        WebViewManager.shared.refreshWebView(webView)
                    }
                }
            }
            */
        }

        /// 强制刷新WebView（清除所有缓存和状态）
        private func forceRefreshWebView(_ webView: WKWebView) {
            guard let currentURL = webView.url else {
                print("[OptimizedWebView] 无法获取当前URL，强制刷新失败")
                return
            }

            print("[OptimizedWebView] 执行强制刷新: \(currentURL.absoluteString)")

            // 注意：已移除Vue重试计数机制

            // 清除所有网站数据
            let websiteDataStore = webView.configuration.websiteDataStore
            let dataTypes = WKWebsiteDataStore.allWebsiteDataTypes()

            websiteDataStore.removeData(ofTypes: dataTypes, modifiedSince: Date.distantPast) {
                DispatchQueue.main.async {
                    print("[OptimizedWebView] 已清除所有网站数据，重新加载页面")

                    // 创建全新的请求
                    var request = URLRequest(url: currentURL)
                    request.setValue("IPv4", forHTTPHeaderField: "X-Preferred-IP-Version")
                    request.cachePolicy = .reloadIgnoringLocalAndRemoteCacheData
                    request.setValue("no-cache, no-store, must-revalidate", forHTTPHeaderField: "Cache-Control")
                    request.setValue("no-cache", forHTTPHeaderField: "Pragma")
                    request.setValue("0", forHTTPHeaderField: "Expires")
                    request.setValue("\(Date().timeIntervalSince1970)", forHTTPHeaderField: "X-Force-Refresh")

                    webView.load(request)
                }
            }
        }

        func webView(_ webView: WKWebView, didFail navigation: WKNavigation!, withError error: Error) {
            print("[OptimizedWebView] ❌ 页面加载失败")
            print("[OptimizedWebView] 错误描述: \(error.localizedDescription)")
            print("[OptimizedWebView] 失败的URL: \(webView.url?.absoluteString ?? "unknown")")

            // 检查是否是网络连接问题
            if let nsError = error as NSError? {
                print("[OptimizedWebView] 错误详情 - 代码: \(nsError.code), 域: \(nsError.domain)")
                print("[OptimizedWebView] 用户信息: \(nsError.userInfo)")

                // NSURLErrorDomain -1001 是超时错误，可能是IPv6问题
                if nsError.domain == NSURLErrorDomain && nsError.code == -1001 {
                    print("[OptimizedWebView] ⚠️ 检测到超时错误，可能是IPv6连接问题")
                    print("[OptimizedWebView] 注意：已移除自动重试机制，避免无限循环")
                }
            }
        }

        func webView(_ webView: WKWebView, didFailProvisionalNavigation navigation: WKNavigation!, withError error: Error) {
            print("[OptimizedWebView] ❌ 预加载失败")
            print("[OptimizedWebView] 错误描述: \(error.localizedDescription)")
            print("[OptimizedWebView] 失败的URL: \(webView.url?.absoluteString ?? "unknown")")

            // 检查是否是网络连接问题
            if let nsError = error as NSError? {
                print("[OptimizedWebView] 预加载错误详情 - 代码: \(nsError.code), 域: \(nsError.domain)")
                print("[OptimizedWebView] 用户信息: \(nsError.userInfo)")

                // 对于超时错误，记录但不自动重试
                if nsError.domain == NSURLErrorDomain && nsError.code == -1001 {
                    print("[OptimizedWebView] ⚠️ 预加载超时错误")
                    print("[OptimizedWebView] 注意：已移除自动重试机制，避免无限循环")
                }
            }
        }

        func webView(_ webView: WKWebView, decidePolicyFor navigationAction: WKNavigationAction, decisionHandler: @escaping (WKNavigationActionPolicy) -> Void) {
            print("[OptimizedWebView] 导航请求: \(navigationAction.request.url?.absoluteString ?? "unknown")")
            decisionHandler(.allow)
        }
    }
}
