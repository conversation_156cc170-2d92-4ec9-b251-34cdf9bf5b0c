import SwiftUI
import WebKit
import UIKit

struct WebView: UIViewRepresentable {
    let url: URL

    func makeCoordinator() -> Coordinator {
        return Coordinator()
    }

    func makeUIView(context: Context) -> WKWebView {
        let request = URLRequest(url: url)

        let config = WKWebViewConfiguration()
        config.mediaTypesRequiringUserActionForPlayback = []
        // 使用新的API替代废弃的javaScriptEnabled
        config.defaultWebpagePreferences.allowsContentJavaScript = true
        config.preferences.javaScriptCanOpenWindowsAutomatically = false
        config.suppressesIncrementalRendering = false // 禁用增量渲染

        // 配置WebView的网络设置，完全禁用缓存
        config.websiteDataStore = WKWebsiteDataStore.nonPersistent()

        // 使用新的通用WebView桥接器
        let webViewBridge = WebViewBridge()
        context.coordinator.webViewBridge = webViewBridge

        let webView = WKWebView(frame: .zero, configuration: config)

        // 设置导航代理用于调试
        webView.navigationDelegate = context.coordinator

        // 配置WebView桥接器
        webViewBridge.configureWebView(webView)

        // 使用自定义的URLRequest来强制IPv4连接并禁用缓存
        var customRequest = request
        customRequest.setValue("IPv4", forHTTPHeaderField: "X-Preferred-IP-Version")
        customRequest.cachePolicy = .reloadIgnoringLocalAndRemoteCacheData
        customRequest.setValue("no-cache, no-store, must-revalidate", forHTTPHeaderField: "Cache-Control")
        customRequest.setValue("no-cache", forHTTPHeaderField: "Pragma")
        customRequest.setValue("0", forHTTPHeaderField: "Expires")

        webView.load(customRequest)

        return webView
    }

    func updateUIView(_ uiView: WKWebView, context: Context) {
        // 如果 URL 发生变化，重新加载
        if uiView.url != url {
            var request = URLRequest(url: url)
            request.setValue("IPv4", forHTTPHeaderField: "X-Preferred-IP-Version")
            request.cachePolicy = .reloadIgnoringLocalAndRemoteCacheData
            request.setValue("no-cache, no-store, must-revalidate", forHTTPHeaderField: "Cache-Control")
            request.setValue("no-cache", forHTTPHeaderField: "Pragma")
            request.setValue("0", forHTTPHeaderField: "Expires")
            uiView.load(request)
        }
    }
}

extension WebView {
    class Coordinator: NSObject, WKNavigationDelegate {
        var webViewBridge: WebViewBridge?

        func webView(_ webView: WKWebView, didStartProvisionalNavigation navigation: WKNavigation!) {
            print("WebView 开始加载: \(webView.url?.absoluteString ?? "unknown")")
        }

        func webView(_ webView: WKWebView, didFinish navigation: WKNavigation!) {
            print("WebView 加载完成: \(webView.url?.absoluteString ?? "unknown")")

            // 发送页面加载完成通知
            webViewBridge?.sendSystemNotification(
                event: .appDidBecomeActive,
                data: ["url": webView.url?.absoluteString ?? ""]
            )
        }

        func webView(_ webView: WKWebView, didFail navigation: WKNavigation!, withError error: Error) {
            print("WebView 加载失败: \(error.localizedDescription)")
            if let nsError = error as NSError? {
                print("错误代码: \(nsError.code), 域: \(nsError.domain)")
            }
        }

        func webView(_ webView: WKWebView, didFailProvisionalNavigation navigation: WKNavigation!, withError error: Error) {
            print("WebView 预加载失败: \(error.localizedDescription)")
            if let nsError = error as NSError? {
                print("错误代码: \(nsError.code), 域: \(nsError.domain)")
            }
        }

        func webView(_ webView: WKWebView, decidePolicyFor navigationAction: WKNavigationAction, decisionHandler: @escaping (WKNavigationActionPolicy) -> Void) {
            print("WebView 导航请求: \(navigationAction.request.url?.absoluteString ?? "unknown")")
            decisionHandler(.allow)
        }

        deinit {
            // 清理WebView桥接器
            if let webViewBridge = webViewBridge {
                webViewBridge.destroy()
            }
        }
    }
}

// MARK: - 注释：原有的YuanAppMessageHandler已被新的通用架构替代
// 新架构位于 Utils/Bridge/ 目录下，提供更好的扩展性和维护性
