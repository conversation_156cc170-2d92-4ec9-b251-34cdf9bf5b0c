//
//  SplashView.swift
//  wadar
//
//  Created by AI Assistant on 2025/6/25.
//

import SwiftUI

struct SplashView: View {
    @ObservedObject var loginVM: LoginViewModel
    @ObservedObject var navigationManager: NavigationManager
    @State private var isCheckingAutoLogin = true
    
    var body: some View {
        VStack(spacing: 20) {
            // App Logo 或图标
            Image(systemName: "phone.fill")
                .font(.system(size: 80))
                .foregroundColor(.blue)
            
            Text("Wadar")
                .font(.largeTitle)
                .fontWeight(.bold)
            
            if isCheckingAutoLogin {
                VStack(spacing: 10) {
                    ProgressView()
                        .scaleEffect(1.5)
                    Text("正在启动...")
                        .foregroundColor(.gray)
                }
            }
        }
        .onAppear {
            checkAutoLogin()
        }
    }
    
    private func checkAutoLogin() {
        print("[SplashView] 开始检查自动登录")

        // 关键：在自动登录检查前先注入CallViewModel
        loginVM.callVM = CallViewModel.shared

        // 应用启动时检查自动登录
        loginVM.checkAutoLogin { success in
            DispatchQueue.main.async {
                print("[SplashView] 自动登录检查完成，成功: \(success)")
                self.isCheckingAutoLogin = false

                if success, let webViewURL = loginVM.webViewURL {
                    print("[SplashView] 自动登录成功，导航到WebView: \(webViewURL.absoluteString)")
                    // 自动登录成功，导航到WebView
                    navigationManager.handleLoginSuccess(webViewURL: webViewURL)
                } else {
                    print("[SplashView] 自动登录失败或无WebView URL，导航到登录页面")
                    // 需要手动登录
                    navigationManager.replace(with: .login)
                }
            }
        }
    }
}

#Preview {
    SplashView(
        loginVM: LoginViewModel(),
        navigationManager: NavigationManager()
    )
}
