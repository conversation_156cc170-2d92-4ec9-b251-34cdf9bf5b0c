import Foundation
import linphonesw
import Combine
import SwiftUI


class LoginViewModel: ObservableObject {
    @Published var phoneNumber: String = ""
    @Published var verificationCode: String = ""
    @Published var userPassword: String = ""
    @Published var loggedIn: Bool = false
    @Published var errorMessage: String? = nil
    @Published var successMessage: String? = nil
    @Published var isLoadingLogin: Bool = false
    @Published var isLoadingVCode: Bool = false
    @Published var showRegisterView: Bool = false
    @Published var showForgotPasswordView: Bool = false
    @Published var showWebView: Bool = false
    @Published var webViewURL: URL? = nil

    weak var callVM: CallViewModel?

    // 导航回调
    var onLoginSuccess: ((URL) -> Void)?

    private var cancellables = Set<AnyCancellable>()
    private var apiBaseURL: String { AppConfig.shared.baseURL + "/api/pinanbao" }

    init() {
        LoggingService.Instance.logLevel = LogLevel.Debug
    }
    
    func login(){
        // 模拟登录逻辑
        if phoneNumber.count == 11 && !userPassword.isEmpty {
            // 登录成功，设置状态
            self.loggedIn = true
        }
    }

    // 获取登录验证码
    func getVerificationCode() {
        guard !isLoadingVCode else { return }

        isLoadingVCode = true
        errorMessage = nil

        VCodeService.shared.getVerificationCode(phone: phoneNumber, type: .login)
            .receive(on: DispatchQueue.main)
            .sink(receiveCompletion: { completion in
                self.isLoadingVCode = false
                if case .failure(let error) = completion {
                    self.errorMessage = (error as NSError).localizedDescription
                    print("[LoginViewModel] 获取验证码失败: \(error)")
                }
            }, receiveValue: { vCodeData in
                print("[LoginViewModel] 验证码获取成功")
                self.successMessage = "验证码已发送到您的手机，请注意查收"

                // 3秒后清除成功消息
                DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
                    self.successMessage = nil
                }
            })
            .store(in: &cancellables)
    }
    // 验证码登录
    func loginWithVerificationCode() {
        guard phoneNumber.count == 11, !verificationCode.isEmpty else {
            self.errorMessage = "请输入正确的手机号和验证码"
            return
        }

        let params: [String: Any] = [
            "code": "",
            "loginType": LoginType.verificationCode.rawValue,
            "phone": phoneNumber,
            "vcode": verificationCode
        ]

        performLogin(with: params, loginType: .verificationCode)
    }

    // 清空登录信息
    func clearLoginInfo() {
        self.phoneNumber = ""
        self.verificationCode = ""
        self.userPassword = ""
        self.loggedIn = false
        self.errorMessage = nil
        self.successMessage = nil
        self.showRegisterView = false
        self.showForgotPasswordView = false
    }
    
    // 密码登录
    func loginWithPassword() {
        guard phoneNumber.count == 11, !userPassword.isEmpty else {
            self.errorMessage = "请输入正确的手机号和密码"
            return
        }

        let params: [String: Any] = [
            "code": "",
            "loginType": LoginType.password.rawValue,
            "phone": phoneNumber,
            "password": userPassword
        ]

        performLogin(with: params, loginType: .password)
    }

    // 通用登录方法
    private func performLogin(with params: [String: Any], loginType: LoginType) {
        guard !isLoadingLogin else { return }

        isLoadingLogin = true
        errorMessage = nil

        // 直接进行登录
        NetworkManager.shared.postWithHandledError(
            url: "\(self.apiBaseURL)/login",
            body: params,
            responseType: UserData.self
        )
            .receive(on: DispatchQueue.main)
            .sink(receiveCompletion: { completion in
                self.isLoadingLogin = false
                if case .failure(let error) = completion {
                    self.loggedIn = false
                    self.errorMessage = (error as NSError).localizedDescription
                }
            }, receiveValue: { userData in
                self.handleLoginSuccess(userData: userData)
            })
            .store(in: &cancellables)
    }

    // 处理登录成功
    private func handleLoginSuccess(userData: UserData) {
        UserManager.shared.saveUser(userData)
        self.loggedIn = true
        self.errorMessage = nil
        print("[LoginViewModel] 登录成功，开始token预校验")

        // 先进行token预校验，确保token有效后再跳转到H5
        validateTokenAndNavigate(userData: userData)
    }

    // Token预校验并导航到H5
    private func validateTokenAndNavigate(userData: UserData) {
        let token = userData.token

        // 开始预加载WebView以减少白屏时间
        let webviewUrl = AppConfig.shared.webviewUrl
        if let preloadURL = URL(string: "\(webviewUrl)/pages/auth/app-auth/index?platform=iOS&appType=app&token=\(token)") {
            WebViewManager.shared.preloadWebView(for: preloadURL)
        }

        // 调用token校验接口
        NetworkManager.shared.postWithHandledError(
            url: "\(apiBaseURL)/checkToken",
            body: ["content": token],
            responseType: Member.self
        )
        .receive(on: DispatchQueue.main)
        .sink(receiveCompletion: { completion in
            if case .failure(let error) = completion {
                print("[LoginViewModel] Token校验失败: \(error)")
                // Token校验失败，清除用户数据并显示错误
                UserManager.shared.clearUser()
                self.loggedIn = false
                self.errorMessage = "登录状态已失效，请重新登录"
            }
        }, receiveValue: { validationData in
            print("[LoginViewModel] Token校验成功")
            // Token有效，导航到H5鉴权页面
            self.navigateToH5HomePage(token: token)

            // 自动登录VoIP账号 - 这是核心功能，必须成功
            self.ensureVoIPLogin(userData: userData)
        })
        .store(in: &cancellables)
    }

    // 导航到H5鉴权页面
    private func navigateToH5HomePage(token: String) {
        let webviewUrl = AppConfig.shared.webviewUrl
        NSLog("[LoginViewModel] 开始构建WebView URL")
        NSLog("[LoginViewModel] webviewUrl: %@", webviewUrl)
        NSLog("[LoginViewModel] token: %@", token)

        // 跳转到专用的鉴权页面，该页面会处理token校验并显示加载状态
        let urlString = "\(webviewUrl)/pages/auth/app-auth/index?platform=iOS&appType=app&token=\(token)"
        NSLog("[LoginViewModel] 构建的完整URL: %@", urlString)

        if let url = URL(string: urlString) {
            self.webViewURL = url
            self.showWebView = true
            NSLog("[LoginViewModel] URL构建成功，准备跳转到H5鉴权页面")
            NSLog("[LoginViewModel] 最终URL对象: %@", url.absoluteString)

            // 调用导航回调
            onLoginSuccess?(url)
        } else {
            NSLog("[LoginViewModel] ❌ URL构建失败: %@", urlString)
        }
    }

    // 检查自动登录
    func checkAutoLogin(completion: @escaping (Bool) -> Void = { _ in }) {
        print("[LoginViewModel] 检查自动登录")

        // 检查是否有保存的用户数据
        guard let savedUser = UserManager.shared.getUser() else {
            print("[LoginViewModel] 没有保存的用户数据，需要手动登录")
            completion(false)
            return
        }

        print("[LoginViewModel] 找到保存的用户数据，开始token校验")

        // 校验token是否有效
        NetworkManager.shared.postWithHandledError(
            url: "\(apiBaseURL)/checkToken",
            body: ["content": savedUser.token],
            responseType: Member.self
        )
        .receive(on: DispatchQueue.main)
        .sink(receiveCompletion: { completionResult in
            if case .failure(let error) = completionResult {
                print("[LoginViewModel] 自动登录失败，token无效: \(error)")
                // Token无效，清除用户数据
                UserManager.shared.clearUser()
                self.loggedIn = false
                // 不显示错误消息，让用户正常登录
                completion(false)
            }
        }, receiveValue: { validationData in
            print("[LoginViewModel] 自动登录成功，token有效")
            // Token有效，设置登录状态
            self.loggedIn = true

            // 导航到H5页面
            self.navigateToH5HomePage(token: savedUser.token)

            // 自动登录VoIP账号 - 这是核心功能，必须成功
            self.ensureVoIPLogin(userData: savedUser)

            completion(true)
        })
        .store(in: &cancellables)
    }

    // 确保VoIP登录成功 - 核心功能
    private func ensureVoIPLogin(userData: UserData) {
        guard let callVM = self.callVM else {
            print("[LoginViewModel] ❌ CallViewModel未初始化")
            return
        }

        guard let voipNumber = userData.member.voipNumber,
              let voipPassword = userData.member.voipPassword,
              !voipNumber.isEmpty, !voipPassword.isEmpty else {
            print("[LoginViewModel] ❌ VoIP账号信息不完整")
            // 发送VoIP不可用通知
            NotificationCenter.default.post(name: .VoIPUnavailableNotification, object: nil, userInfo: ["reason": "账号信息不完整"])
            return
        }

        print("[LoginViewModel] 🔄 开始VoIP登录 - 账号: \(voipNumber)")

        // 设置VoIP账号信息
        callVM.username = voipNumber
        callVM.passwd = voipPassword

        // 尝试登录
        callVM.login()

        // 启动VoIP状态监控
        startVoIPMonitoring()
    }

    // 启动VoIP状态监控
    private func startVoIPMonitoring() {
        // 5秒后检查VoIP登录状态
        DispatchQueue.main.asyncAfter(deadline: .now() + 5.0) {
            if let callVM = self.callVM, !callVM.loggedIn {
                print("[LoginViewModel] ⚠️ VoIP登录超时，发送不可用通知")
                NotificationCenter.default.post(name: .VoIPUnavailableNotification, object: nil, userInfo: ["reason": "登录超时"])
            }
        }
    }


}
