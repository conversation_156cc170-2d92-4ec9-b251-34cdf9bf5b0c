import Foundation
import Combine

class ForgotPasswordViewModel: ObservableObject {
    @Published var phoneNumber: String = ""
    @Published var verificationCode: String = ""
    @Published var newPassword: String = ""
    @Published var confirmPassword: String = ""
    @Published var errorMessage: String? = nil
    @Published var successMessage: String? = nil
    @Published var isLoadingChangePassword: Bool = false
    @Published var isLoadingVCode: Bool = false
    
    private var cancellables = Set<AnyCancellable>()
    
    init() {}
    
    // 获取忘记密码验证码
    func getForgotPasswordVerificationCode() {
        guard !isLoadingVCode else { return }
        
        isLoadingVCode = true
        errorMessage = nil
        
        VCodeService.shared.getVerificationCode(phone: phoneNumber, type: .forgotPassword)
            .receive(on: DispatchQueue.main)
            .sink(receiveCompletion: { completion in
                self.isLoadingVCode = false
                if case .failure(let error) = completion {
                    self.errorMessage = (error as NSError).localizedDescription
                    print("[ForgotPasswordViewModel] 获取验证码失败: \(error)")
                }
            }, receiveValue: { vCodeData in
                print("[ForgotPasswordViewModel] 验证码获取成功")
                self.successMessage = "验证码已发送到您的手机，请注意查收"
                
                // 3秒后清除成功消息
                DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
                    self.successMessage = nil
                }
            })
            .store(in: &cancellables)
    }
    
    // 修改密码
    func changePassword() {
        guard !isLoadingChangePassword else { return }
        
        isLoadingChangePassword = true
        errorMessage = nil
        
        VCodeService.shared.changePassword(
            phone: phoneNumber,
            vcode: verificationCode,
            password: newPassword,
            passwordAgain: confirmPassword
        )
        .receive(on: DispatchQueue.main)
        .sink(receiveCompletion: { completion in
            self.isLoadingChangePassword = false
            if case .failure(let error) = completion {
                self.errorMessage = (error as NSError).localizedDescription
                print("[ForgotPasswordViewModel] 修改密码失败: \(error)")
            }
        }, receiveValue: { changePasswordData in
            print("[ForgotPasswordViewModel] 密码修改成功")
            self.successMessage = "密码修改成功！请返回登录页面使用新密码登录"
        })
        .store(in: &cancellables)
    }
    
    // 清空信息
    func clearInfo() {
        phoneNumber = ""
        verificationCode = ""
        newPassword = ""
        confirmPassword = ""
        errorMessage = nil
        successMessage = nil
    }
    
    // 检查修改密码按钮是否可用
    var isChangePasswordButtonDisabled: Bool {
        return isLoadingChangePassword ||
               phoneNumber.count != 11 ||
               verificationCode.isEmpty ||
               newPassword.isEmpty ||
               confirmPassword.isEmpty ||
               newPassword != confirmPassword ||
               newPassword.count < 6
    }
    
    // 检查获取验证码按钮是否可用
    var isCodeButtonDisabled: Bool {
        return isLoadingVCode ||
               phoneNumber.count != 11
    }
    
    // 密码匹配状态
    var passwordsMatch: Bool {
        return !newPassword.isEmpty && !confirmPassword.isEmpty && newPassword == confirmPassword
    }
    
    // 密码长度是否有效
    var isPasswordLengthValid: Bool {
        return newPassword.count >= 6
    }
}
