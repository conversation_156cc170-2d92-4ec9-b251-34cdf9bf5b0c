import Foundation
import Combine

class RegisterViewModel: ObservableObject {
    @Published var phoneNumber: String = ""
    @Published var verificationCode: String = ""
    @Published var password: String = ""
    @Published var confirmPassword: String = ""
    @Published var errorMessage: String? = nil
    @Published var successMessage: String? = nil
    @Published var isLoadingRegister: Bool = false
    @Published var isLoadingVCode: Bool = false
    
    private var cancellables = Set<AnyCancellable>()
    private var apiBaseURL: String { AppConfig.shared.baseURL + "/api/pinanbao" }
    
    init() {}
    
    // 获取注册验证码
    func getRegisterVerificationCode() {
        guard !isLoadingVCode else { return }

        isLoadingVCode = true
        errorMessage = nil

        VCodeService.shared.getVerificationCode(phone: phoneNumber, type: .register)
            .receive(on: DispatchQueue.main)
            .sink(receiveCompletion: { completion in
                self.isLoadingVCode = false
                if case .failure(let error) = completion {
                    self.errorMessage = (error as NSError).localizedDescription
                    print("[RegisterViewModel] 获取验证码失败: \(error)")
                }
            }, receiveValue: { vCodeData in
                print("[RegisterViewModel] 验证码获取成功")
                self.successMessage = "验证码已发送到您的手机，请注意查收"

                // 3秒后清除成功消息
                DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
                    self.successMessage = nil
                }
            })
            .store(in: &cancellables)
    }
    
    // 注册
    func register() {
        guard validateInput() else { return }
        
        guard !isLoadingRegister else { return }
        
        isLoadingRegister = true
        errorMessage = nil
        
        let params = [
            "phone": phoneNumber,
            "vcode": verificationCode,
            "password": password
        ]
        
        print("[RegisterViewModel] 开始注册，参数: \(params)")

        // 直接进行注册
        NetworkManager.shared.getWithHandledError(
            url: "\(self.apiBaseURL)/registerPhone",
            params: params,
            responseType: RegisterData.self
        )
            .receive(on: DispatchQueue.main)
            .sink(receiveCompletion: { completion in
                self.isLoadingRegister = false
                if case .failure(let error) = completion {
                    self.errorMessage = (error as NSError).localizedDescription
                    print("[RegisterViewModel] 注册失败: \(error)")
                }
            }, receiveValue: { registerData in
                print("[RegisterViewModel] 注册成功")
                self.successMessage = "注册成功！请返回登录页面进行登录"
            })
            .store(in: &cancellables)
    }
    
    // 验证输入
    private func validateInput() -> Bool {
        if phoneNumber.count != 11 {
            errorMessage = "请输入正确的手机号"
            return false
        }
        
        if verificationCode.isEmpty {
            errorMessage = "请输入验证码"
            return false
        }
        
        if password.isEmpty {
            errorMessage = "请输入密码"
            return false
        }
        
        if password.count < 6 {
            errorMessage = "密码长度不能少于6位"
            return false
        }
        
        if confirmPassword.isEmpty {
            errorMessage = "请确认密码"
            return false
        }
        
        if password != confirmPassword {
            errorMessage = "两次输入的密码不一致"
            return false
        }
        
        return true
    }
    
    // 清空注册信息
    func clearRegisterInfo() {
        phoneNumber = ""
        verificationCode = ""
        password = ""
        confirmPassword = ""
        errorMessage = nil
        successMessage = nil
    }
    
    // 检查注册按钮是否可用
    var isRegisterButtonDisabled: Bool {
        return isLoadingRegister ||
               phoneNumber.count != 11 ||
               verificationCode.isEmpty ||
               password.isEmpty ||
               confirmPassword.isEmpty ||
               password != confirmPassword
    }
    
    // 检查获取验证码按钮是否可用
    var isCodeButtonDisabled: Bool {
        return isLoadingVCode ||
               phoneNumber.count != 11
    }
}
