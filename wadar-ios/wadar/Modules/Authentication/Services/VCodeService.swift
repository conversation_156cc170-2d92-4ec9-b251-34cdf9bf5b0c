import Foundation
import Combine

// 通用验证码服务
class VCodeService {
    static let shared = VCodeService()
    
    private let baseURL = AppConfig.shared.baseURL + "/api/pinanbao"
    
    private init() {}
    
    // 获取验证码的通用方法
    func getVerificationCode(
        phone: String,
        type: VCodeType
    ) -> AnyPublisher<VCodeData, Error> {
        
        guard phone.count == 11 else {
            let error = NSError(domain: "ValidationError", code: -1, userInfo: [NSLocalizedDescriptionKey: "请输入正确的手机号"])
            return Fail(error: error).eraseToAnyPublisher()
        }
        
        let params = [
            "type": type.rawValue,
            "phone": phone
        ]
        
        print("[VCodeService] 开始获取\(type.displayName)，手机号: \(phone)")

        // 直接获取验证码
        return NetworkManager.shared.getWithHandledError(
            url: "\(self.baseURL)/getVCode",
            params: params,
            responseType: VCodeData.self
        )
            .eraseToAnyPublisher()
    }
    
    // 修改密码
    func changePassword(
        phone: String,
        vcode: String,
        password: String,
        passwordAgain: String
    ) -> AnyPublisher<ChangePasswordData, Error> {
        
        guard phone.count == 11 else {
            let error = NSError(domain: "ValidationError", code: -1, userInfo: [NSLocalizedDescriptionKey: "请输入正确的手机号"])
            return Fail(error: error).eraseToAnyPublisher()
        }
        
        guard !vcode.isEmpty else {
            let error = NSError(domain: "ValidationError", code: -1, userInfo: [NSLocalizedDescriptionKey: "请输入验证码"])
            return Fail(error: error).eraseToAnyPublisher()
        }
        
        guard !password.isEmpty else {
            let error = NSError(domain: "ValidationError", code: -1, userInfo: [NSLocalizedDescriptionKey: "请输入新密码"])
            return Fail(error: error).eraseToAnyPublisher()
        }
        
        guard password.count >= 6 else {
            let error = NSError(domain: "ValidationError", code: -1, userInfo: [NSLocalizedDescriptionKey: "密码长度不能少于6位"])
            return Fail(error: error).eraseToAnyPublisher()
        }
        
        guard password == passwordAgain else {
            let error = NSError(domain: "ValidationError", code: -1, userInfo: [NSLocalizedDescriptionKey: "两次输入的密码不一致"])
            return Fail(error: error).eraseToAnyPublisher()
        }
        
        let params: [String: Any] = [
            "phone": phone,
            "vcode": vcode,
            "password": password,
            "passwordAgain": passwordAgain
        ]
        
        print("[VCodeService] 开始修改密码，手机号: \(phone)")

        // 直接修改密码
        return NetworkManager.shared.postWithHandledError(
            url: "\(self.baseURL)/changePwd",
            body: params,
            responseType: ChangePasswordData.self
        )
            .eraseToAnyPublisher()
    }
}
