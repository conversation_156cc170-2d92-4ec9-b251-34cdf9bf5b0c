import SwiftUI

struct ForgotPasswordView: View {
    @ObservedObject var viewModel: ForgotPasswordViewModel
    @State private var codeTimer = 60
    @State private var codeTimerActive = false
    
    // 回调函数，用于返回登录页面
    let onBackToLogin: () -> Void
    
    var body: some View {
        VStack(spacing: 24) {
            Text("忘记密码")
                .font(.largeTitle)
                .bold()
            
            // 手机号输入框
            TextField("请输入手机号", text: $viewModel.phoneNumber)
                .keyboardType(.numberPad)
                .textFieldStyle(RoundedBorderTextFieldStyle())
                .disabled(viewModel.isLoadingChangePassword)
            
            // 验证码输入框和获取按钮
            HStack {
                TextField("请输入验证码", text: $viewModel.verificationCode)
                    .keyboardType(.numberPad)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .disabled(viewModel.isLoadingChangePassword)
                
                Button(action: {
                    getVerificationCode()
                }) {
                    HStack {
                        if viewModel.isLoadingVCode {
                            ProgressView()
                                .scaleEffect(0.7)
                        }
                        Text(codeButtonText)
                    }
                    .foregroundColor(codeButtonColor)
                    .frame(minWidth: 80)
                }
                .disabled(isCodeButtonDisabled)
            }
            
            // 新密码输入框
            SecureField("请输入新密码", text: $viewModel.newPassword)
                .textFieldStyle(RoundedBorderTextFieldStyle())
                .disabled(viewModel.isLoadingChangePassword)
            
            // 确认密码输入框
            SecureField("请确认新密码", text: $viewModel.confirmPassword)
                .textFieldStyle(RoundedBorderTextFieldStyle())
                .disabled(viewModel.isLoadingChangePassword)
            
            // 密码验证提示
            if !viewModel.newPassword.isEmpty {
                VStack(alignment: .leading, spacing: 4) {
                    // 密码长度提示
                    HStack {
                        Image(systemName: viewModel.isPasswordLengthValid ? "checkmark.circle.fill" : "xmark.circle.fill")
                            .foregroundColor(viewModel.isPasswordLengthValid ? .green : .red)
                        Text("密码长度至少6位")
                            .foregroundColor(viewModel.isPasswordLengthValid ? .green : .red)
                            .font(.caption)
                        Spacer()
                    }
                    
                    // 密码匹配提示
                    if !viewModel.confirmPassword.isEmpty {
                        HStack {
                            Image(systemName: viewModel.passwordsMatch ? "checkmark.circle.fill" : "xmark.circle.fill")
                                .foregroundColor(viewModel.passwordsMatch ? .green : .red)
                            Text(viewModel.passwordsMatch ? "密码匹配" : "密码不匹配")
                                .foregroundColor(viewModel.passwordsMatch ? .green : .red)
                                .font(.caption)
                            Spacer()
                        }
                    }
                }
            }
            
            // 修改密码按钮
            Button(action: {
                viewModel.changePassword()
            }) {
                HStack {
                    if viewModel.isLoadingChangePassword {
                        ProgressView()
                            .scaleEffect(0.8)
                            .foregroundColor(.white)
                    }
                    Text("修改密码")
                        .foregroundColor(.white)
                }
                .frame(maxWidth: .infinity, minHeight: 45)
                .background(changePasswordButtonColor)
                .clipShape(RoundedRectangle(cornerRadius: 15))
            }
            .disabled(viewModel.isChangePasswordButtonDisabled)
            .padding(.top, 10)
            
            // 返回登录按钮
            Button(action: {
                onBackToLogin()
            }) {
                Text("返回登录")
                    .foregroundColor(.blue)
                    .frame(maxWidth: .infinity, minHeight: 35)
                    .overlay(
                        RoundedRectangle(cornerRadius: 10)
                            .stroke(Color.blue, lineWidth: 1)
                    )
            }
            .disabled(viewModel.isLoadingChangePassword)
            .padding(.top, 5)
        }
        .padding()
        .alert(isPresented: Binding<Bool>(
            get: { viewModel.errorMessage != nil },
            set: { if !$0 { viewModel.errorMessage = nil } }
        )) {
            Alert(title: Text("错误"), message: Text(viewModel.errorMessage ?? "未知错误"), dismissButton: .default(Text("确定")))
        }
        .alert(isPresented: Binding<Bool>(
            get: { viewModel.successMessage != nil },
            set: { if !$0 { viewModel.successMessage = nil } }
        )) {
            Alert(title: Text("成功"), message: Text(viewModel.successMessage ?? ""), dismissButton: .default(Text("确定")))
        }
    }
    
    // MARK: - 计算属性
    private var codeButtonText: String {
        if codeTimerActive {
            return "重新发送(\(codeTimer))"
        } else {
            return "获取验证码"
        }
    }
    
    private var codeButtonColor: Color {
        isCodeButtonDisabled ? .gray : .blue
    }
    
    private var isCodeButtonDisabled: Bool {
        codeTimerActive || viewModel.isCodeButtonDisabled
    }
    
    private var changePasswordButtonColor: Color {
        viewModel.isChangePasswordButtonDisabled ? .gray : .accentColor
    }
    
    // MARK: - 方法
    private func getVerificationCode() {
        viewModel.getForgotPasswordVerificationCode()
        startCodeTimer()
    }
    
    private func startCodeTimer() {
        codeTimerActive = true
        codeTimer = 60
        
        Timer.scheduledTimer(withTimeInterval: 1, repeats: true) { timer in
            codeTimer -= 1
            if codeTimer <= 0 {
                timer.invalidate()
                codeTimerActive = false
            }
        }
    }
}

#Preview {
    ForgotPasswordView(viewModel: ForgotPasswordViewModel()) {
        print("返回登录")
    }
}
