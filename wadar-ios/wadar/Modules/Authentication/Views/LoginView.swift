//
//  LoginView.swift
//  wadar
//
//  Created by <PERSON><PERSON> on 25/6/24.
//

import SwiftUI

struct LoginView: View {
    @ObservedObject var viewModel: LoginViewModel
    @State private var codeTimer = 60
    @State private var codeTimerActive = false
    @State private var selectedLoginType: LoginType = .password
    @StateObject private var registerViewModel = RegisterViewModel()
    @StateObject private var forgotPasswordViewModel = ForgotPasswordViewModel()
    var body: some View {
        ZStack {
            if viewModel.showRegisterView {
                RegisterView(viewModel: registerViewModel) {
                    // 返回登录页面
                    viewModel.showRegisterView = false
                    registerViewModel.clearRegisterInfo()
                }
            } else if viewModel.showForgotPasswordView {
                ForgotPasswordView(viewModel: forgotPasswordViewModel) {
                    // 返回登录页面
                    viewModel.showForgotPasswordView = false
                    forgotPasswordViewM<PERSON>.clearInfo()
                }
            } else {
                loginContentView
            }
        }

    }

    // MARK: - 登录页面内容
    private var loginContentView: some View {
        VStack(spacing: 24) {
            Text("登录")
                .font(.largeTitle)
                .bold()

            // 手机号输入框
            TextField("请输入手机号", text: $viewModel.phoneNumber)
                .keyboardType(.numberPad)
                .textFieldStyle(RoundedBorderTextFieldStyle())
                .disabled(viewModel.loggedIn || viewModel.isLoadingLogin)

            // 登录方式选择
            Picker("登录方式", selection: $selectedLoginType) {
                ForEach(LoginType.allCases, id: \.self) { type in
                    Text(type.displayName).tag(type)
                }
            }
            .pickerStyle(SegmentedPickerStyle())
            .disabled(viewModel.loggedIn)
            .padding(.bottom, 8)

            // 根据登录方式显示不同的输入框
            Group {
                if selectedLoginType == .verificationCode {
                    verificationCodeSection
                } else {
                    passwordSection
                }
            }

            // 登录/退出按钮
            Button(action: {
                if viewModel.loggedIn {
                    viewModel.clearLoginInfo()
                } else {
                    performLogin()
                }
            }) {
                HStack {
                    if viewModel.isLoadingLogin {
                        ProgressView()
                            .scaleEffect(0.8)
                            .foregroundColor(.white)
                    }
                    Text(viewModel.loggedIn ? "退出登录" : "登录")
                        .foregroundColor(.white)
                }
                .frame(maxWidth: .infinity, minHeight: 45)
                .background(loginButtonColor)
                .clipShape(RoundedRectangle(cornerRadius: 15))
            }
            .disabled(isLoginButtonDisabled)
            .padding(.top, 10)

            // 注册和忘记密码入口
            if !viewModel.loggedIn {
                VStack(spacing: 8) {
                    // 注册入口
                    Button(action: {
                        viewModel.showRegisterView = true
                    }) {
                        Text("还没有账号？立即注册")
                            .foregroundColor(.blue)
                            .frame(maxWidth: .infinity, minHeight: 35)
                            .overlay(
                                RoundedRectangle(cornerRadius: 10)
                                    .stroke(Color.blue, lineWidth: 1)
                            )
                    }
                    .disabled(viewModel.isLoadingLogin)

                    // 忘记密码入口
                    Button(action: {
                        viewModel.showForgotPasswordView = true
                    }) {
                        Text("忘记密码")
                            .foregroundColor(.gray)
                            .font(.footnote)
                    }
                    .disabled(viewModel.isLoadingLogin)
                }
                .padding(.top, 5)
            }
        }
        .padding()
        .onAppear {
            setupCallVM()
        }
        .alert(isPresented: Binding<Bool>(
            get: { viewModel.errorMessage != nil },
            set: { if !$0 { viewModel.errorMessage = nil } }
        )) {
            Alert(title: Text("错误"), message: Text(viewModel.errorMessage ?? "未知错误"), dismissButton: .default(Text("确定")))
        }
        .alert(isPresented: Binding<Bool>(
            get: { viewModel.successMessage != nil },
            set: { if !$0 { viewModel.successMessage = nil } }
        )) {
            Alert(title: Text("成功"), message: Text(viewModel.successMessage ?? ""), dismissButton: .default(Text("确定")))
        }
    }

    // MARK: - 验证码部分视图
    private var verificationCodeSection: some View {
        VStack(spacing: 12) {
            HStack {
                TextField("请输入验证码", text: $viewModel.verificationCode)
                    .keyboardType(.numberPad)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .disabled(viewModel.loggedIn || viewModel.isLoadingLogin)

                Button(action: {
                    getVerificationCode()
                }) {
                    HStack {
                        if viewModel.isLoadingVCode {
                            ProgressView()
                                .scaleEffect(0.7)
                        }
                        Text(codeButtonText)
                    }
                    .foregroundColor(codeButtonColor)
                    .frame(minWidth: 80)
                }
                .disabled(isCodeButtonDisabled)
            }
        }
    }

    // MARK: - 密码部分视图
    private var passwordSection: some View {
        SecureField("请输入密码", text: $viewModel.userPassword)
            .textFieldStyle(RoundedBorderTextFieldStyle())
            .disabled(viewModel.loggedIn || viewModel.isLoadingLogin)
    }

    // MARK: - 计算属性
    private var loginButtonColor: Color {
        isLoginButtonDisabled ? .gray : .accentColor
    }

    private var isLoginButtonDisabled: Bool {
        viewModel.isLoadingLogin ||
        viewModel.phoneNumber.count != 11 ||
        (selectedLoginType == .password && viewModel.userPassword.isEmpty) ||
        (selectedLoginType == .verificationCode && viewModel.verificationCode.isEmpty)
    }

    private var codeButtonText: String {
        if codeTimerActive {
            return "重新发送(\(codeTimer))"
        } else {
            return "获取验证码"
        }
    }

    private var codeButtonColor: Color {
        isCodeButtonDisabled ? .gray : .blue
    }

    private var isCodeButtonDisabled: Bool {
        codeTimerActive ||
        viewModel.isLoadingVCode ||
        viewModel.phoneNumber.count != 11 ||
        viewModel.loggedIn ||
        viewModel.isLoadingLogin
    }

    // MARK: - 方法
    private func setupCallVM() {
        // 确保callVM使用单例
        if viewModel.callVM == nil {
            viewModel.callVM = CallViewModel.shared
        }
    }

    private func performLogin() {
        switch selectedLoginType {
        case .password:
            viewModel.loginWithPassword()
        case .verificationCode:
            viewModel.loginWithVerificationCode()
        }
    }

    private func getVerificationCode() {
        viewModel.getVerificationCode()
        startCodeTimer()
    }

    private func startCodeTimer() {
        codeTimerActive = true
        codeTimer = 60

        Timer.scheduledTimer(withTimeInterval: 1, repeats: true) { timer in
            codeTimer -= 1
            if codeTimer <= 0 {
                timer.invalidate()
                codeTimerActive = false
            }
        }
    }
}

#Preview {
    LoginView(viewModel: LoginViewModel())
}
