import SwiftUI

struct RegisterView: View {
    @ObservedObject var viewModel: RegisterViewModel
    @State private var codeTimer = 60
    @State private var codeTimerActive = false
    
    // 回调函数，用于返回登录页面
    let onBackToLogin: () -> Void
    
    var body: some View {
        VStack(spacing: 24) {
            Text("用户注册")
                .font(.largeTitle)
                .bold()
            
            // 手机号输入框
            TextField("请输入手机号", text: $viewModel.phoneNumber)
                .keyboardType(.numberPad)
                .textFieldStyle(RoundedBorderTextFieldStyle())
                .disabled(viewModel.isLoadingRegister)
            
            // 验证码输入框和获取按钮
            HStack {
                TextField("请输入验证码", text: $viewModel.verificationCode)
                    .keyboardType(.numberPad)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .disabled(viewModel.isLoadingRegister)
                
                But<PERSON>(action: {
                    getVerificationCode()
                }) {
                    HStack {
                        if viewModel.isLoadingVCode {
                            ProgressView()
                                .scaleEffect(0.7)
                        }
                        Text(codeButtonText)
                    }
                    .foregroundColor(codeButtonColor)
                    .frame(minWidth: 80)
                }
                .disabled(isCodeButtonDisabled)
            }
            
            // 密码输入框
            SecureField("请输入密码", text: $viewModel.password)
                .textFieldStyle(RoundedBorderTextFieldStyle())
                .disabled(viewModel.isLoadingRegister)
            
            // 确认密码输入框
            SecureField("请确认密码", text: $viewModel.confirmPassword)
                .textFieldStyle(RoundedBorderTextFieldStyle())
                .disabled(viewModel.isLoadingRegister)
            
            // 密码匹配提示
            if !viewModel.password.isEmpty && !viewModel.confirmPassword.isEmpty {
                HStack {
                    Image(systemName: passwordMatchIcon)
                        .foregroundColor(passwordMatchColor)
                    Text(passwordMatchText)
                        .foregroundColor(passwordMatchColor)
                        .font(.caption)
                    Spacer()
                }
            }
            
            // 注册按钮
            Button(action: {
                viewModel.register()
            }) {
                HStack {
                    if viewModel.isLoadingRegister {
                        ProgressView()
                            .scaleEffect(0.8)
                            .foregroundColor(.white)
                    }
                    Text("立即注册")
                        .foregroundColor(.white)
                }
                .frame(maxWidth: .infinity, minHeight: 45)
                .background(registerButtonColor)
                .clipShape(RoundedRectangle(cornerRadius: 15))
            }
            .disabled(viewModel.isRegisterButtonDisabled)
            .padding(.top, 10)
            
            // 返回登录按钮
            Button(action: {
                onBackToLogin()
            }) {
                Text("返回登录")
                    .foregroundColor(.blue)
                    .frame(maxWidth: .infinity, minHeight: 35)
                    .overlay(
                        RoundedRectangle(cornerRadius: 10)
                            .stroke(Color.blue, lineWidth: 1)
                    )
            }
            .disabled(viewModel.isLoadingRegister)
            .padding(.top, 5)
        }
        .padding()
        .alert(isPresented: Binding<Bool>(
            get: { viewModel.errorMessage != nil },
            set: { if !$0 { viewModel.errorMessage = nil } }
        )) {
            Alert(title: Text("错误"), message: Text(viewModel.errorMessage ?? "未知错误"), dismissButton: .default(Text("确定")))
        }
        .alert(isPresented: Binding<Bool>(
            get: { viewModel.successMessage != nil },
            set: { if !$0 { viewModel.successMessage = nil } }
        )) {
            Alert(title: Text("成功"), message: Text(viewModel.successMessage ?? ""), dismissButton: .default(Text("确定")))
        }
    }
    
    // MARK: - 计算属性
    private var codeButtonText: String {
        if codeTimerActive {
            return "重新发送(\(codeTimer))"
        } else {
            return "获取验证码"
        }
    }
    
    private var codeButtonColor: Color {
        isCodeButtonDisabled ? .gray : .blue
    }
    
    private var isCodeButtonDisabled: Bool {
        codeTimerActive || viewModel.isCodeButtonDisabled
    }
    
    private var registerButtonColor: Color {
        viewModel.isRegisterButtonDisabled ? .gray : .accentColor
    }
    
    private var passwordMatchIcon: String {
        viewModel.password == viewModel.confirmPassword ? "checkmark.circle.fill" : "xmark.circle.fill"
    }
    
    private var passwordMatchColor: Color {
        viewModel.password == viewModel.confirmPassword ? .green : .red
    }
    
    private var passwordMatchText: String {
        viewModel.password == viewModel.confirmPassword ? "密码匹配" : "密码不匹配"
    }
    
    // MARK: - 方法
    private func getVerificationCode() {
        viewModel.getRegisterVerificationCode()
        startCodeTimer()
    }
    
    private func startCodeTimer() {
        codeTimerActive = true
        codeTimer = 60
        
        Timer.scheduledTimer(withTimeInterval: 1, repeats: true) { timer in
            codeTimer -= 1
            if codeTimer <= 0 {
                timer.invalidate()
                codeTimerActive = false
            }
        }
    }
}

#Preview {
    RegisterView(viewModel: RegisterViewModel()) {
        print("返回登录")
    }
}
