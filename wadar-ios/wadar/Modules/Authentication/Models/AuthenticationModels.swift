//
//  AuthenticationModels.swift
//  wadar
//
//  Created by guan on 2025/6/26.
//

import Foundation

// MARK: - 认证相关数据模型

/// 登录请求模型
struct LoginRequest: Codable {
    let phone: String
    let password: String
}

/// 注册请求模型
struct RegisterRequest: Codable {
    let phone: String
    let password: String
    let verificationCode: String
    let name: String
}

/// 找回密码请求模型
struct ForgotPasswordRequest: Codable {
    let phone: String
    let newPassword: String
    let verificationCode: String
}

/// 验证码请求模型
struct VerificationCodeRequest: Codable {
    let phone: String
    let type: String // "register" 或 "forgot_password"
}

/// 认证响应模型
struct AuthenticationResponse: Codable {
    let success: Bool
    let message: String
    let data: UserData?
}

/// 验证码响应模型
struct VerificationCodeResponse: Codable {
    let success: Bool
    let message: String
}
