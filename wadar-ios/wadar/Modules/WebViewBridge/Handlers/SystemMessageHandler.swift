/**
 * 系统消息处理器
 * 处理系统相关的消息
 * 
 * <AUTHOR>
 */

import Foundation
import UIKit

/**
 * 系统消息处理器类
 * 实现系统相关功能的消息处理
 */
class SystemMessageHandler: MessageHandler {
    
    var handlerName: String = "SystemMessageHandler"
    
    var supportedActions: [String] = [
        "system.getVersion",
        "system.openSettings",
        "system.share",
        "system.openUrl"
    ]
    
    func handleMessage(_ message: AppMessage, completion: @escaping (MessageHandlerResult) -> Void) {
        print("[SystemMessageHandler] 处理消息: \(message.action)")
        
        let (_, method) = MessageProtocol.shared.parseAction(message.action)
        
        switch method {
        case "getVersion":
            handleGetVersion(message: message, completion: completion)
        case "openSettings":
            handleOpenSettings(message: message, completion: completion)
        case "share":
            handleShare(message: message, completion: completion)
        case "openUrl":
            handleOpenUrl(message: message, completion: completion)
        default:
            completion(.failure(error: "不支持的系统操作: \(method)"))
        }
    }
    
    // MARK: - 系统操作处理
    
    /**
     * 处理获取版本信息
     */
    private func handleGetVersion(message: AppMessage, completion: @escaping (MessageHandlerResult) -> Void) {
        print("[SystemMessageHandler] 处理获取版本信息")
        
        let appVersion = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "unknown"
        let buildNumber = Bundle.main.infoDictionary?["CFBundleVersion"] as? String ?? "unknown"
        let bundleId = Bundle.main.bundleIdentifier ?? "unknown"
        
        let versionInfo: [String: Any] = [
            "appVersion": appVersion,
            "buildNumber": buildNumber,
            "bundleId": bundleId,
            "systemVersion": UIDevice.current.systemVersion,
            "platform": "iOS",
            "timestamp": Date().timeIntervalSince1970
        ]
        
        completion(.success(data: versionInfo))
    }
    
    /**
     * 处理打开设置
     */
    private func handleOpenSettings(message: AppMessage, completion: @escaping (MessageHandlerResult) -> Void) {
        print("[SystemMessageHandler] 处理打开设置")
        
        DispatchQueue.main.async {
            if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
                if UIApplication.shared.canOpenURL(settingsUrl) {
                    UIApplication.shared.open(settingsUrl) { success in
                        let responseData: [String: Any] = [
                            "action": "openSettings",
                            "success": success,
                            "timestamp": Date().timeIntervalSince1970
                        ]
                        completion(.success(data: responseData))
                    }
                } else {
                    completion(.failure(error: "无法打开设置"))
                }
            } else {
                completion(.failure(error: "设置URL无效"))
            }
        }
    }
    
    /**
     * 处理分享
     */
    private func handleShare(message: AppMessage, completion: @escaping (MessageHandlerResult) -> Void) {
        print("[SystemMessageHandler] 处理分享")
        
        guard let text = message.data["text"] as? String else {
            completion(.failure(error: "分享缺少文本内容"))
            return
        }
        
        DispatchQueue.main.async {
            var itemsToShare: [Any] = [text]
            
            // 如果有URL，添加到分享内容
            if let urlString = message.data["url"] as? String,
               let url = URL(string: urlString) {
                itemsToShare.append(url)
            }
            
            // 如果有图片，添加到分享内容
            if let imageData = message.data["image"] as? Data,
               let image = UIImage(data: imageData) {
                itemsToShare.append(image)
            }
            
            let activityViewController = UIActivityViewController(
                activityItems: itemsToShare,
                applicationActivities: nil
            )
            
            // 获取当前的根视图控制器
            if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
               let window = windowScene.windows.first,
               let rootViewController = window.rootViewController {
                
                // 对于iPad，需要设置popover
                if let popover = activityViewController.popoverPresentationController {
                    popover.sourceView = rootViewController.view
                    popover.sourceRect = CGRect(x: rootViewController.view.bounds.midX,
                                              y: rootViewController.view.bounds.midY,
                                              width: 0, height: 0)
                    popover.permittedArrowDirections = []
                }
                
                rootViewController.present(activityViewController, animated: true) {
                    let responseData: [String: Any] = [
                        "action": "share",
                        "success": true,
                        "timestamp": Date().timeIntervalSince1970
                    ]
                    completion(.success(data: responseData))
                }
            } else {
                completion(.failure(error: "无法获取根视图控制器"))
            }
        }
    }
    
    /**
     * 处理打开URL
     */
    private func handleOpenUrl(message: AppMessage, completion: @escaping (MessageHandlerResult) -> Void) {
        guard let urlString = message.data["url"] as? String,
              let url = URL(string: urlString) else {
            completion(.failure(error: "打开URL缺少有效的URL参数"))
            return
        }
        
        print("[SystemMessageHandler] 处理打开URL: \(urlString)")
        
        DispatchQueue.main.async {
            if UIApplication.shared.canOpenURL(url) {
                UIApplication.shared.open(url) { success in
                    let responseData: [String: Any] = [
                        "action": "openUrl",
                        "url": urlString,
                        "success": success,
                        "timestamp": Date().timeIntervalSince1970
                    ]
                    completion(.success(data: responseData))
                }
            } else {
                completion(.failure(error: "无法打开URL: \(urlString)"))
            }
        }
    }
}
