/**
 * 用户消息处理器
 * 处理用户相关的消息
 * 
 * <AUTHOR>
 */

import Foundation

/**
 * 用户消息处理器类
 * 实现用户相关功能的消息处理
 */
class UserMessageHandler: MessageHandler {
    
    var handlerName: String = "UserMessageHandler"
    
    var supportedActions: [String] = [
        "user.getInfo",
        "user.login",
        "user.logout",
        "user.updateProfile"
    ]
    
    func handleMessage(_ message: AppMessage, completion: @escaping (MessageHandlerResult) -> Void) {
        print("[UserMessageHandler] 处理消息: \(message.action)")
        
        let (_, method) = MessageProtocol.shared.parseAction(message.action)
        
        switch method {
        case "getInfo":
            handleGetUserInfo(message: message, completion: completion)
        case "login":
            handleUserLogin(message: message, completion: completion)
        case "logout":
            handleUserLogout(message: message, completion: completion)
        case "updateProfile":
            handleUpdateProfile(message: message, completion: completion)
        default:
            completion(.failure(error: "不支持的用户操作: \(method)"))
        }
    }
    
    // MARK: - 用户操作处理
    
    /**
     * 处理获取用户信息
     */
    private func handleGetUserInfo(message: AppMessage, completion: @escaping (MessageHandlerResult) -> Void) {
        print("[UserMessageHandler] 处理获取用户信息")
        
        if let userData = UserManager.shared.getUser() {
            let userInfo: [String: Any] = [
                "id": userData.member.id,
                "name": userData.member.name,
                "phone": userData.member.phone,
                "email": userData.member.email ?? "",
                "voipNumber": userData.member.voipNumber ?? "",
                "isLoggedIn": true,
                "timestamp": Date().timeIntervalSince1970
            ]
            
            completion(.success(data: userInfo))
        } else {
            let userInfo: [String: Any] = [
                "isLoggedIn": false,
                "timestamp": Date().timeIntervalSince1970
            ]
            
            completion(.success(data: userInfo))
        }
    }
    
    /**
     * 处理用户登录
     */
    private func handleUserLogin(message: AppMessage, completion: @escaping (MessageHandlerResult) -> Void) {
        guard let username = message.data["username"] as? String,
              let _ = message.data["password"] as? String else {
            completion(.failure(error: "用户登录缺少用户名或密码"))
            return
        }
        
        print("[UserMessageHandler] 处理用户登录: \(username)")
        
        // 这里应该调用实际的登录逻辑
        // 暂时返回模拟结果
        let responseData: [String: Any] = [
            "action": "login",
            "username": username,
            "success": true,
            "message": "用户登录功能待实现",
            "timestamp": Date().timeIntervalSince1970
        ]
        
        completion(.success(data: responseData))
    }
    
    /**
     * 处理用户登出
     */
    private func handleUserLogout(message: AppMessage, completion: @escaping (MessageHandlerResult) -> Void) {
        print("[UserMessageHandler] 处理用户登出")
        
        // 这里应该调用实际的登出逻辑
        // 可以清除用户数据、token等
        
        let responseData: [String: Any] = [
            "action": "logout",
            "success": true,
            "message": "用户登出功能待实现",
            "timestamp": Date().timeIntervalSince1970
        ]
        
        completion(.success(data: responseData))
    }
    
    /**
     * 处理更新用户资料
     */
    private func handleUpdateProfile(message: AppMessage, completion: @escaping (MessageHandlerResult) -> Void) {
        print("[UserMessageHandler] 处理更新用户资料")
        
        let profileData = message.data["profile"] as? [String: Any] ?? [:]
        
        // 这里应该调用实际的更新用户资料逻辑
        
        let responseData: [String: Any] = [
            "action": "updateProfile",
            "profile": profileData,
            "success": true,
            "message": "更新用户资料功能待实现",
            "timestamp": Date().timeIntervalSince1970
        ]
        
        completion(.success(data: responseData))
    }
}
