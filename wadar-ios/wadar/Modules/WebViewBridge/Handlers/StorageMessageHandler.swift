/**
 * 存储消息处理器
 * 处理本地存储相关的消息
 * 
 * <AUTHOR>
 */

import Foundation

/**
 * 存储消息处理器类
 * 实现本地存储相关功能的消息处理
 */
class StorageMessageHandler: MessageHandler {
    
    var handlerName: String = "StorageMessageHandler"
    
    var supportedActions: [String] = [
        "storage.get",
        "storage.set",
        "storage.remove",
        "storage.clear"
    ]
    
    private let userDefaults = UserDefaults.standard
    private let keyPrefix = "YuanApp_"
    
    func handleMessage(_ message: AppMessage, completion: @escaping (MessageHandlerResult) -> Void) {
        print("[StorageMessageHandler] 处理消息: \(message.action)")
        
        let (_, method) = MessageProtocol.shared.parseAction(message.action)
        
        switch method {
        case "get":
            handleStorageGet(message: message, completion: completion)
        case "set":
            handleStorageSet(message: message, completion: completion)
        case "remove":
            handleStorageRemove(message: message, completion: completion)
        case "clear":
            handleStorageClear(message: message, completion: completion)
        default:
            completion(.failure(error: "不支持的存储操作: \(method)"))
        }
    }
    
    // MARK: - 存储操作处理
    
    /**
     * 处理获取存储值
     */
    private func handleStorageGet(message: AppMessage, completion: @escaping (MessageHandlerResult) -> Void) {
        guard let key = message.data["key"] as? String, !key.isEmpty else {
            completion(.failure(error: "获取存储值缺少key参数"))
            return
        }
        
        let fullKey = keyPrefix + key
        let value = userDefaults.object(forKey: fullKey)
        
        let responseData: [String: Any] = [
            "key": key,
            "value": value ?? NSNull(),
            "timestamp": Date().timeIntervalSince1970
        ]
        
        completion(.success(data: responseData))
    }
    
    /**
     * 处理设置存储值
     */
    private func handleStorageSet(message: AppMessage, completion: @escaping (MessageHandlerResult) -> Void) {
        guard let key = message.data["key"] as? String, !key.isEmpty else {
            completion(.failure(error: "设置存储值缺少key参数"))
            return
        }
        
        let value = message.data["value"]
        let fullKey = keyPrefix + key
        
        userDefaults.set(value, forKey: fullKey)
        userDefaults.synchronize()
        
        let responseData: [String: Any] = [
            "key": key,
            "value": value ?? NSNull(),
            "success": true,
            "timestamp": Date().timeIntervalSince1970
        ]
        
        completion(.success(data: responseData))
    }
    
    /**
     * 处理删除存储值
     */
    private func handleStorageRemove(message: AppMessage, completion: @escaping (MessageHandlerResult) -> Void) {
        guard let key = message.data["key"] as? String, !key.isEmpty else {
            completion(.failure(error: "删除存储值缺少key参数"))
            return
        }
        
        let fullKey = keyPrefix + key
        userDefaults.removeObject(forKey: fullKey)
        userDefaults.synchronize()
        
        let responseData: [String: Any] = [
            "key": key,
            "success": true,
            "timestamp": Date().timeIntervalSince1970
        ]
        
        completion(.success(data: responseData))
    }
    
    /**
     * 处理清空存储
     */
    private func handleStorageClear(message: AppMessage, completion: @escaping (MessageHandlerResult) -> Void) {
        // 获取所有以前缀开头的key
        let allKeys = userDefaults.dictionaryRepresentation().keys
        let keysToRemove = allKeys.filter { $0.hasPrefix(keyPrefix) }
        
        // 删除所有匹配的key
        for key in keysToRemove {
            userDefaults.removeObject(forKey: key)
        }
        
        userDefaults.synchronize()
        
        let responseData: [String: Any] = [
            "removedCount": keysToRemove.count,
            "success": true,
            "timestamp": Date().timeIntervalSince1970
        ]
        
        completion(.success(data: responseData))
    }
}
