/**
 * WiFi配置消息处理器
 * 处理来自H5的WiFi配置相关消息
 * 
 * <AUTHOR>
 */

import Foundation
import SwiftUI

/**
 * WiFi配置消息处理器类
 * 实现WiFi配置相关功能的消息处理
 */
class WiFiMessageHandler: MessageHandler {
    
    var handlerName: String = "WiFiMessageHandler"
    
    var supportedActions: [String] = [
        "wifi.openConfig",
        "wifi.getStatus"
    ]
    
    func handleMessage(_ message: AppMessage, completion: @escaping (MessageHandlerResult) -> Void) {
        print("[WiFiMessageHandler] 处理消息: \(message.action)")
        
        let (_, method) = MessageProtocol.shared.parseAction(message.action)
        
        switch method {
        case "openConfig":
            handleOpenConfig(message: message, completion: completion)
        case "getStatus":
            handleGetStatus(message: message, completion: completion)
        default:
            completion(.failure(error: "不支持的WiFi操作: \(method)"))
        }
    }
    
    // MARK: - 私有方法
    
    /**
     * 处理打开WiFi配置
     */
    private func handleOpenConfig(message: AppMessage, completion: @escaping (MessageHandlerResult) -> Void) {
        print("[WiFiMessageHandler] 处理打开WiFi配置")
        
        // 获取设备编码
        guard let devCode = message.data["devCode"] as? String, !devCode.isEmpty else {
            completion(.failure(error: "设备编码不能为空"))
            return
        }
        
        print("[WiFiMessageHandler] 设备编码: \(devCode)")
        
        // 在主线程中打开WiFi配置页面
        DispatchQueue.main.async {
            self.openWiFiConfigPage(devCode: devCode, completion: completion)
        }
    }
    
    /**
     * 处理获取WiFi状态
     */
    private func handleGetStatus(message: AppMessage, completion: @escaping (MessageHandlerResult) -> Void) {
        print("[WiFiMessageHandler] 处理获取WiFi状态")
        
        // 返回WiFi配置功能的可用状态
        let responseData: [String: Any] = [
            "available": true,
            "version": "1.0",
            "supportedFeatures": [
                "deviceScan",
                "wifiConfig",
                "provisioning"
            ],
            "timestamp": Date().timeIntervalSince1970
        ]
        
        completion(.success(data: responseData))
    }
    
    /**
     * 打开WiFi配置页面
     */
    private func openWiFiConfigPage(devCode: String, completion: @escaping (MessageHandlerResult) -> Void) {
        // 获取当前的根视图控制器
        guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
              let window = windowScene.windows.first,
              let rootViewController = window.rootViewController else {
            completion(.failure(error: "无法获取根视图控制器"))
            return
        }
        
        // 创建WiFi配置视图
        let wifiConfigView = createWiFiConfigView(devCode: devCode) { [weak self] result in
            self?.handleWiFiConfigResult(result: result, completion: completion)
        }
        
        // 包装为UIHostingController
        let hostingController = UIHostingController(rootView: wifiConfigView)
        hostingController.modalPresentationStyle = .fullScreen
        
        // 呈现WiFi配置页面
        rootViewController.present(hostingController, animated: true) {
            print("[WiFiMessageHandler] WiFi配置页面已打开")
        }
    }
    
    /**
     * 创建WiFi配置视图
     */
    private func createWiFiConfigView(devCode: String, onComplete: @escaping (WiFiConfigResult) -> Void) -> some View {
        return WiFiConfigContainerView(devCode: devCode, onComplete: onComplete)
    }
    
    /**
     * 处理WiFi配置结果
     */
    private func handleWiFiConfigResult(result: WiFiConfigResult, completion: @escaping (MessageHandlerResult) -> Void) {
        switch result {
        case .success(let data):
            print("[WiFiMessageHandler] WiFi配置成功: \(data)")
            completion(.success(data: [
                "success": true,
                "message": "WiFi配置成功",
                "data": data
            ]))
        case .failure(let error):
            print("[WiFiMessageHandler] WiFi配置失败: \(error)")
            completion(.failure(error: "WiFi配置失败: \(error)"))
        case .cancelled:
            print("[WiFiMessageHandler] WiFi配置已取消")
            completion(.success(data: [
                "success": false,
                "message": "用户取消了WiFi配置",
                "cancelled": true
            ]))
        }
    }
}

// MARK: - WiFi配置结果枚举

enum WiFiConfigResult {
    case success([String: Any])
    case failure(String)
    case cancelled
}

// MARK: - WiFi配置容器视图

struct WiFiConfigContainerView: View {
    let devCode: String
    let onComplete: (WiFiConfigResult) -> Void
    
    @StateObject private var viewModel = ESPProvisionViewModel()
    @Environment(\.presentationMode) var presentationMode
    
    var body: some View {
        NavigationView {
            ESPProvisionView()
                .navigationTitle("WiFi配置")
                .navigationBarTitleDisplayMode(.inline)
                .toolbar {
                    ToolbarItem(placement: .navigationBarLeading) {
                        Button("取消") {
                            handleCancel()
                        }
                    }
                }
        }
        .onAppear {
            setupViewModel()
        }
        .onChange(of: viewModel.currentStep) { step in
            if step == .completed {
                handleSuccess()
            }
        }
        .onChange(of: viewModel.errorMessage) { error in
            if let error = error, !error.isEmpty {
                handleError(error)
            }
        }
    }
    
    private func setupViewModel() {
        print("[WiFiConfigContainerView] 设置ViewModel，设备编码: \(devCode)")
        // 这里可以根据需要设置特定的配置
    }
    
    private func handleSuccess() {
        print("[WiFiConfigContainerView] WiFi配置成功")
        presentationMode.wrappedValue.dismiss()
        onComplete(.success([
            "devCode": devCode,
            "timestamp": Date().timeIntervalSince1970
        ]))
    }
    
    private func handleError(_ error: String) {
        print("[WiFiConfigContainerView] WiFi配置错误: \(error)")
        presentationMode.wrappedValue.dismiss()
        onComplete(.failure(error))
    }
    
    private func handleCancel() {
        print("[WiFiConfigContainerView] 用户取消WiFi配置")
        presentationMode.wrappedValue.dismiss()
        onComplete(.cancelled)
    }
}
