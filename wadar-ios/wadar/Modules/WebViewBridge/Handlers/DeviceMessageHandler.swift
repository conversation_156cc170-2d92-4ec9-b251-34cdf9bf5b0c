/**
 * 设备消息处理器
 * 处理设备相关的消息
 * 
 * <AUTHOR>
 */

import Foundation
import UIKit
import CoreLocation
import AudioToolbox

/**
 * 设备消息处理器类
 * 实现设备相关功能的消息处理
 */
class DeviceMessageHandler: NSObject, MessageHandler {
    
    var handlerName: String = "DeviceMessageHandler"
    
    var supportedActions: [String] = [
        "device.getInfo",
        "device.getLocation",
        "device.vibrate",
        "device.playSound"
    ]
    
    private var locationManager: CLLocationManager?
    
    override init() {
        super.init()
        setupLocationManager()
    }
    
    func handleMessage(_ message: AppMessage, completion: @escaping (MessageHandlerResult) -> Void) {
        print("[DeviceMessageHandler] 处理消息: \(message.action)")
        
        let (_, method) = MessageProtocol.shared.parseAction(message.action)
        
        switch method {
        case "getInfo":
            handleGetDeviceInfo(message: message, completion: completion)
        case "getLocation":
            handleGetLocation(message: message, completion: completion)
        case "vibrate":
            handleVibrate(message: message, completion: completion)
        case "playSound":
            handlePlaySound(message: message, completion: completion)
        default:
            completion(.failure(error: "不支持的设备操作: \(method)"))
        }
    }
    
    // MARK: - 设备操作处理
    
    /**
     * 处理获取设备信息
     */
    private func handleGetDeviceInfo(message: AppMessage, completion: @escaping (MessageHandlerResult) -> Void) {
        print("[DeviceMessageHandler] 处理获取设备信息")
        
        let device = UIDevice.current
        let screen = UIScreen.main
        
        let deviceInfo: [String: Any] = [
            "platform": "iOS",
            "model": device.model,
            "systemName": device.systemName,
            "systemVersion": device.systemVersion,
            "name": device.name,
            "identifierForVendor": device.identifierForVendor?.uuidString ?? "",
            "screenWidth": screen.bounds.width,
            "screenHeight": screen.bounds.height,
            "screenScale": screen.scale,
            "timestamp": Date().timeIntervalSince1970
        ]
        
        completion(.success(data: deviceInfo))
    }
    
    /**
     * 处理获取位置信息
     */
    private func handleGetLocation(message: AppMessage, completion: @escaping (MessageHandlerResult) -> Void) {
        print("[DeviceMessageHandler] 处理获取位置信息")
        
        guard let locationManager = locationManager else {
            completion(.failure(error: "位置管理器未初始化"))
            return
        }
        
        // 检查位置权限
        let authorizationStatus = locationManager.authorizationStatus
        
        switch authorizationStatus {
        case .notDetermined:
            completion(.failure(error: "位置权限未确定，请先请求权限"))
        case .denied, .restricted:
            completion(.failure(error: "位置权限被拒绝"))
        case .authorizedWhenInUse, .authorizedAlways:
            // 获取当前位置
            if let location = locationManager.location {
                let locationInfo: [String: Any] = [
                    "latitude": location.coordinate.latitude,
                    "longitude": location.coordinate.longitude,
                    "altitude": location.altitude,
                    "accuracy": location.horizontalAccuracy,
                    "timestamp": location.timestamp.timeIntervalSince1970
                ]
                completion(.success(data: locationInfo))
            } else {
                completion(.failure(error: "无法获取当前位置"))
            }
        @unknown default:
            completion(.failure(error: "未知的位置权限状态"))
        }
    }
    
    /**
     * 处理震动
     */
    private func handleVibrate(message: AppMessage, completion: @escaping (MessageHandlerResult) -> Void) {
        print("[DeviceMessageHandler] 处理震动")
        
        let type = message.data["type"] as? String ?? "default"
        
        DispatchQueue.main.async {
            switch type {
            case "light":
                let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                impactFeedback.impactOccurred()
            case "medium":
                let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
                impactFeedback.impactOccurred()
            case "heavy":
                let impactFeedback = UIImpactFeedbackGenerator(style: .heavy)
                impactFeedback.impactOccurred()
            case "success":
                let notificationFeedback = UINotificationFeedbackGenerator()
                notificationFeedback.notificationOccurred(.success)
            case "warning":
                let notificationFeedback = UINotificationFeedbackGenerator()
                notificationFeedback.notificationOccurred(.warning)
            case "error":
                let notificationFeedback = UINotificationFeedbackGenerator()
                notificationFeedback.notificationOccurred(.error)
            default:
                // 默认震动
                AudioServicesPlaySystemSound(kSystemSoundID_Vibrate)
            }
            
            let responseData: [String: Any] = [
                "action": "vibrate",
                "type": type,
                "timestamp": Date().timeIntervalSince1970
            ]
            
            completion(.success(data: responseData))
        }
    }
    
    /**
     * 处理播放声音
     */
    private func handlePlaySound(message: AppMessage, completion: @escaping (MessageHandlerResult) -> Void) {
        print("[DeviceMessageHandler] 处理播放声音")
        
        let soundType = message.data["type"] as? String ?? "default"
        
        DispatchQueue.main.async {
            var systemSoundID: SystemSoundID
            
            switch soundType {
            case "message":
                systemSoundID = 1007 // 短信声音
            case "mail":
                systemSoundID = 1000 // 邮件声音
            case "alarm":
                systemSoundID = 1005 // 闹钟声音
            case "click":
                systemSoundID = 1104 // 点击声音
            default:
                systemSoundID = 1000 // 默认声音
            }
            
            AudioServicesPlaySystemSound(systemSoundID)
            
            let responseData: [String: Any] = [
                "action": "playSound",
                "type": soundType,
                "timestamp": Date().timeIntervalSince1970
            ]
            
            completion(.success(data: responseData))
        }
    }
    
    // MARK: - 辅助方法
    
    /**
     * 设置位置管理器
     */
    private func setupLocationManager() {
        locationManager = CLLocationManager()
        locationManager?.delegate = self
        locationManager?.desiredAccuracy = kCLLocationAccuracyBest
    }
}

// MARK: - CLLocationManagerDelegate

extension DeviceMessageHandler: CLLocationManagerDelegate {
    func locationManager(_ manager: CLLocationManager, didUpdateLocations locations: [CLLocation]) {
        // 位置更新处理
        print("[DeviceMessageHandler] 位置已更新")
    }
    
    func locationManager(_ manager: CLLocationManager, didFailWithError error: Error) {
        print("[DeviceMessageHandler] 位置获取失败: \(error.localizedDescription)")
    }
    
    func locationManager(_ manager: CLLocationManager, didChangeAuthorization status: CLAuthorizationStatus) {
        print("[DeviceMessageHandler] 位置权限状态变化: \(status.rawValue)")
    }
}
