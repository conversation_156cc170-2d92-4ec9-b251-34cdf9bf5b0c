/**
 * 消息协议定义
 * 负责消息的格式化、验证和序列化
 * 
 * <AUTHOR>
 */

import Foundation

/**
 * 标准消息结构
 */
struct AppMessage {
    let id: String
    let type: MessageType
    let action: String
    let data: [String: Any]
    let timestamp: Int64
    let platform: String
    let version: String
    
    enum MessageType: String {
        case request = "request"
        case response = "response"
        case notification = "notification"
    }
    
    // 简化初始化，不使用Codable
    init(id: String, type: MessageType, action: String, data: [String: Any], timestamp: Int64, platform: String, version: String) {
        self.id = id
        self.type = type
        self.action = action
        self.data = data
        self.timestamp = timestamp
        self.platform = platform
        self.version = version
    }
}

/**
 * 响应数据结构
 */
struct ResponseData {
    let requestId: String
    let success: Bool
    let data: [String: Any]?
    let error: String?

    init(requestId: String, success: Bool, data: [String: Any]? = nil, error: String? = nil) {
        self.requestId = requestId
        self.success = success
        self.data = data
        self.error = error
    }
}

/**
 * 消息协议类
 * 负责消息的创建、验证和解析
 */
class MessageProtocol {
    static let shared = MessageProtocol()
    
    private let version = "1.0"
    private var messageIdCounter: Int64 = 0
    
    private init() {}
    
    /**
     * 创建标准消息
     */
    func createMessage(
        type: AppMessage.MessageType,
        action: String,
        data: [String: Any] = [:],
        platform: String = "iOS"
    ) -> AppMessage {
        let id = generateMessageId()
        let timestamp = Int64(Date().timeIntervalSince1970 * 1000)

        return AppMessage(
            id: id,
            type: type,
            action: action,
            data: data,
            timestamp: timestamp,
            platform: platform,
            version: version
        )
    }
    
    /**
     * 创建响应消息
     */
    func createResponse(
        requestId: String,
        success: Bool,
        data: [String: Any]? = nil,
        error: String? = nil
    ) -> AppMessage {
        let responseData = ResponseData(
            requestId: requestId,
            success: success,
            data: data,
            error: error
        )
        
        // 将 ResponseData 转换为字典
        var dataDict: [String: Any] = [
            "requestId": responseData.requestId,
            "success": responseData.success
        ]
        
        if let data = responseData.data {
            dataDict["data"] = data
        }
        
        if let error = responseData.error {
            dataDict["error"] = error
        }
        
        return createMessage(
            type: .response,
            action: "response",
            data: dataDict
        )
    }
    
    /**
     * 创建通知消息
     */
    func createNotification(action: String, data: [String: Any] = [:]) -> AppMessage {
        return createMessage(
            type: .notification,
            action: action,
            data: data
        )
    }
    
    /**
     * 验证消息格式
     */
    func validateMessage(_ messageDict: [String: Any]) -> Bool {
        // 检查必需字段
        let requiredFields = ["id", "type", "action", "timestamp", "version"]
        for field in requiredFields {
            if messageDict[field] == nil {
                print("[MessageProtocol] 消息缺少必需字段: \(field)")
                return false
            }
        }
        
        // 检查消息类型
        guard let typeString = messageDict["type"] as? String,
              AppMessage.MessageType(rawValue: typeString) != nil else {
            print("[MessageProtocol] 无效的消息类型")
            return false
        }
        
        // 检查action
        guard let action = messageDict["action"] as? String, !action.isEmpty else {
            print("[MessageProtocol] 无效的action")
            return false
        }
        
        return true
    }
    
    /**
     * 解析消息字典为AppMessage对象
     */
    func parseMessage(from messageDict: [String: Any]) -> AppMessage? {
        guard validateMessage(messageDict) else {
            return nil
        }

        guard let id = messageDict["id"] as? String,
              let typeString = messageDict["type"] as? String,
              let type = AppMessage.MessageType(rawValue: typeString),
              let action = messageDict["action"] as? String,
              let timestamp = messageDict["timestamp"] as? Int64,
              let platform = messageDict["platform"] as? String,
              let version = messageDict["version"] as? String else {
            print("[MessageProtocol] 解析消息失败: 缺少必需字段")
            return nil
        }

        let data = messageDict["data"] as? [String: Any] ?? [:]

        return AppMessage(
            id: id,
            type: type,
            action: action,
            data: data,
            timestamp: timestamp,
            platform: platform,
            version: version
        )
    }
    
    /**
     * 将AppMessage对象转换为字典
     */
    func messageToDict(_ message: AppMessage) -> [String: Any] {
        let dict: [String: Any] = [
            "id": message.id,
            "type": message.type.rawValue,
            "action": message.action,
            "data": message.data,
            "timestamp": message.timestamp,
            "platform": message.platform,
            "version": message.version
        ]
        
        return dict
    }
    
    /**
     * 解析action
     */
    func parseAction(_ action: String) -> (service: String, method: String) {
        let components = action.components(separatedBy: ".")
        if components.count >= 2 {
            return (service: components[0], method: components.dropFirst().joined(separator: "."))
        } else {
            return (service: "app", method: action)
        }
    }
    
    /**
     * 构建action字符串
     */
    func buildAction(service: String, method: String) -> String {
        return "\(service).\(method)"
    }
    
    /**
     * 生成唯一消息ID
     */
    private func generateMessageId() -> String {
        messageIdCounter += 1
        let timestamp = Int64(Date().timeIntervalSince1970 * 1000)
        let random = Int.random(in: 1000...9999)
        return "msg_\(timestamp)_\(messageIdCounter)_\(random)"
    }
    
    /**
     * 检查消息是否过期
     */
    func isMessageExpired(_ message: AppMessage, timeoutMs: Int64 = 30000) -> Bool {
        let now = Int64(Date().timeIntervalSince1970 * 1000)
        return (now - message.timestamp) > timeoutMs
    }
    
    /**
     * 获取消息摘要
     */
    func getMessageSummary(_ message: AppMessage) -> String {
        let date = Date(timeIntervalSince1970: Double(message.timestamp) / 1000.0)
        let formatter = DateFormatter()
        formatter.timeStyle = .medium
        let timeString = formatter.string(from: date)
        
        return "[\(message.type.rawValue)] \(message.action) (\(message.id)) at \(timeString)"
    }
}
