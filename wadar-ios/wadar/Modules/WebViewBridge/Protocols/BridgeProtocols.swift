//
//  BridgeProtocols.swift
//  wadar
//
//  Created by guan on 2025/6/26.
//

import Foundation
import WebKit

// MARK: - WebView桥接协议

/// 消息处理器协议
protocol MessageHandlerProtocol {
    var handlerName: String { get }
    func handleMessage(_ message: BridgeMessage, completion: @escaping (BridgeResponse) -> Void)
}

/// 桥接管理器协议
protocol BridgeManagerProtocol {
    func configureWebView(_ webView: WKWebView)
    func registerHandler(_ handler: MessageHandlerProtocol)
    func sendMessage(_ message: BridgeMessage, to webView: WKWebView)
    func destroy()
}

/// 响应管理器协议
protocol ResponseManagerProtocol {
    func sendResponse(_ response: BridgeResponse, to webView: WKWebView)
    func sendSystemNotification(event: SystemEvent, data: [String: Any], to webView: WKWebView)
}

// MARK: - 桥接数据模型

/// 桥接消息模型
struct BridgeMessage {
    let id: String
    let handler: String
    let data: [String: Any]
}

/// 桥接响应模型
struct BridgeResponse {
    let id: String
    let success: Bool
    let data: [String: Any]?
    let error: String?
}

/// 系统事件枚举
enum SystemEvent: String, CaseIterable {
    case appDidBecomeActive = "appDidBecomeActive"
    case appWillResignActive = "appWillResignActive"
    case appDidEnterBackground = "appDidEnterBackground"
    case appWillEnterForeground = "appWillEnterForeground"
}
