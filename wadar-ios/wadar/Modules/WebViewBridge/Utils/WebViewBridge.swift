/**
 * 通用WebView桥接器
 * 提供WebView与原生App之间的双向通信能力
 * 
 * <AUTHOR>
 */

import Foundation
import WebKit

/**
 * WebView桥接器类
 * 处理WebView的消息接收和发送，支持多个WebView实例
 */
class WebViewBridge: NSObject, WKScriptMessageHandler {
    private let messageProtocol = MessageProtocol.shared
    private var responseManager: ResponseManager
    private var messageRouter: MessageRouter
    
    weak var webView: WKWebView? {
        didSet {
            if let webView = webView {
                responseManager.setWebView(webView)
            }
        }
    }
    
    override init() {
        self.responseManager = ResponseManager()
        self.messageRouter = MessageRouter()
        super.init()
        
        // 设置响应管理器的引用
        messageRouter.setResponseManager(responseManager)
    }
    
    /**
     * 配置WebView
     */
    func configureWebView(_ webView: WKWebView) {
        self.webView = webView
        
        // 添加消息处理器
        webView.configuration.userContentController.add(self, name: "<PERSON>A<PERSON>")
        
        print("[WebViewBridge] WebView配置完成")
    }
    
    /**
     * 移除WebView配置
     */
    func removeWebViewConfiguration(_ webView: WKWebView) {
        webView.configuration.userContentController.removeScriptMessageHandler(forName: "YuanApp")
        
        if self.webView == webView {
            self.webView = nil
        }
        
        print("[WebViewBridge] WebView配置已移除")
    }
    
    // MARK: - WKScriptMessageHandler
    
    func userContentController(_ userContentController: WKUserContentController, didReceive message: WKScriptMessage) {
        print("[WebViewBridge] 收到WebView消息: \(message.name)")
        
        guard let messageBody = message.body as? [String: Any] else {
            print("[WebViewBridge] 消息格式错误: \(message.body)")
            sendErrorResponse(error: "消息格式错误")
            return
        }
        
        print("[WebViewBridge] 消息内容: \(messageBody)")
        
        // 验证消息格式
        guard messageProtocol.validateMessage(messageBody) else {
            print("[WebViewBridge] 消息验证失败")
            sendErrorResponse(error: "消息验证失败")
            return
        }
        
        // 解析消息
        guard let appMessage = messageProtocol.parseMessage(from: messageBody) else {
            print("[WebViewBridge] 消息解析失败")
            sendErrorResponse(error: "消息解析失败")
            return
        }
        
        // 路由消息到对应的处理器
        messageRouter.routeMessage(appMessage)
    }
    
    // MARK: - 公共方法
    
    /**
     * 发送成功响应
     */
    func sendSuccessResponse(
        requestId: String,
        data: [String: Any]? = nil,
        completion: ((Bool) -> Void)? = nil
    ) {
        responseManager.sendSuccessResponse(
            requestId: requestId,
            data: data,
            completion: completion
        )
    }
    
    /**
     * 发送错误响应
     */
    func sendErrorResponse(
        requestId: String? = nil,
        error: String,
        completion: ((Bool) -> Void)? = nil
    ) {
        let id = requestId ?? "unknown"
        responseManager.sendErrorResponse(
            requestId: id,
            error: error,
            completion: completion
        )
    }
    
    /**
     * 发送通知消息
     */
    func sendNotification(
        action: String,
        data: [String: Any] = [:],
        completion: ((Bool) -> Void)? = nil
    ) {
        responseManager.sendNotification(
            action: action,
            data: data,
            completion: completion
        )
    }
    
    /**
     * 发送VoIP通知
     */
    func sendVoIPNotification(
        event: VoIPEvent,
        data: [String: Any] = [:],
        completion: ((Bool) -> Void)? = nil
    ) {
        responseManager.sendVoIPNotification(
            event: event,
            data: data,
            completion: completion
        )
    }
    
    /**
     * 发送导航通知
     */
    func sendNavigationNotification(
        event: NavigationEvent,
        data: [String: Any] = [:],
        completion: ((Bool) -> Void)? = nil
    ) {
        responseManager.sendNavigationNotification(
            event: event,
            data: data,
            completion: completion
        )
    }
    
    /**
     * 发送系统通知
     */
    func sendSystemNotification(
        event: SystemEvent,
        data: [String: Any] = [:],
        completion: ((Bool) -> Void)? = nil
    ) {
        responseManager.sendSystemNotification(
            event: event,
            data: data,
            completion: completion
        )
    }
    
    /**
     * 注册消息处理器
     */
    func registerHandler(_ handler: MessageHandler, for action: String) {
        messageRouter.registerHandler(handler, for: action)
    }
    
    /**
     * 注销消息处理器
     */
    func unregisterHandler(for action: String) {
        messageRouter.unregisterHandler(for: action)
    }
    
    /**
     * 获取已注册的处理器列表
     */
    func getRegisteredHandlers() -> [String] {
        return messageRouter.getRegisteredActions()
    }
    
    /**
     * 检查是否有处理器处理指定action
     */
    func hasHandler(for action: String) -> Bool {
        return messageRouter.hasHandler(for: action)
    }
    
    /**
     * 获取桥接器状态信息
     */
    func getStatus() -> [String: Any] {
        return [
            "hasWebView": webView != nil,
            "registeredHandlers": getRegisteredHandlers(),
            "handlerCount": getRegisteredHandlers().count
        ]
    }
    
    /**
     * 销毁桥接器
     */
    func destroy() {
        if let webView = webView {
            removeWebViewConfiguration(webView)
        }
        
        messageRouter.removeAllHandlers()
        
        print("[WebViewBridge] 桥接器已销毁")
    }
}

// MARK: - 扩展方法

extension WebViewBridge {
    /**
     * 便捷方法：发送VoIP呼叫成功通知
     */
    func notifyVoIPCallSuccess(number: String, callId: String? = nil) {
        let data: [String: Any] = [
            "number": number,
            "callId": callId ?? "",
            "timestamp": Date().timeIntervalSince1970
        ]
        
        sendVoIPNotification(event: .callConnected, data: data)
    }
    
    /**
     * 便捷方法：发送VoIP呼叫失败通知
     */
    func notifyVoIPCallFailed(number: String, error: String) {
        let data: [String: Any] = [
            "number": number,
            "error": error,
            "timestamp": Date().timeIntervalSince1970
        ]
        
        sendVoIPNotification(event: .callFailed, data: data)
    }
    
    /**
     * 便捷方法：发送页面变化通知
     */
    func notifyPageChanged(from: String, to: String) {
        let data: [String: Any] = [
            "from": from,
            "to": to,
            "timestamp": Date().timeIntervalSince1970
        ]
        
        sendNavigationNotification(event: .pageChanged, data: data)
    }
}
