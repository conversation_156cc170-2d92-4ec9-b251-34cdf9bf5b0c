/**
 * 响应管理器
 * 负责管理向WebView发送响应消息
 * 
 * <AUTHOR>
 */

import Foundation
import WebKit

/**
 * 响应管理器类
 * 管理向WebView发送响应和通知消息
 */
class ResponseManager {
    private weak var webView: WKWebView?
    private let messageProtocol = MessageProtocol.shared
    
    init(webView: WKWebView? = nil) {
        self.webView = webView
    }
    
    /**
     * 设置WebView引用
     */
    func setWebView(_ webView: WKWebView) {
        self.webView = webView
    }
    
    /**
     * 发送成功响应
     */
    func sendSuccessResponse(
        requestId: String,
        data: [String: Any]? = nil,
        completion: ((Bool) -> Void)? = nil
    ) {
        let responseMessage = messageProtocol.createResponse(
            requestId: requestId,
            success: true,
            data: data
        )
        
        sendMessageToWebView(responseMessage, completion: completion)
    }
    
    /**
     * 发送错误响应
     */
    func sendErrorResponse(
        requestId: String,
        error: String,
        completion: ((Bool) -> Void)? = nil
    ) {
        let responseMessage = messageProtocol.createResponse(
            requestId: requestId,
            success: false,
            error: error
        )
        
        sendMessageToWebView(responseMessage, completion: completion)
    }
    
    /**
     * 发送通知消息
     */
    func sendNotification(
        action: String,
        data: [String: Any] = [:],
        completion: ((Bool) -> Void)? = nil
    ) {
        let notificationMessage = messageProtocol.createNotification(
            action: action,
            data: data
        )
        
        sendMessageToWebView(notificationMessage, completion: completion)
    }
    
    /**
     * 发送自定义消息
     */
    func sendCustomMessage(
        type: AppMessage.MessageType,
        action: String,
        data: [String: Any] = [:],
        completion: ((Bool) -> Void)? = nil
    ) {
        let message = messageProtocol.createMessage(
            type: type,
            action: action,
            data: data
        )
        
        sendMessageToWebView(message, completion: completion)
    }
    
    /**
     * 向WebView发送消息
     */
    private func sendMessageToWebView(
        _ message: AppMessage,
        completion: ((Bool) -> Void)? = nil
    ) {
        guard let webView = webView else {
            print("[ResponseManager] WebView引用为空，无法发送消息")
            completion?(false)
            return
        }
        
        do {
            let messageDict = messageProtocol.messageToDict(message)
            let jsonData = try JSONSerialization.data(withJSONObject: messageDict, options: [])
            let jsonString = String(data: jsonData, encoding: .utf8) ?? "{}"
            
            let script = """
                (function() {
                    try {
                        var messageData = \(jsonString);
                        
                        // 发送postMessage事件
                        window.postMessage(messageData, '*');
                        
                        // 调用全局回调函数（如果存在）
                        if (window.YuanAppCallback && typeof window.YuanAppCallback === 'function') {
                            window.YuanAppCallback(messageData);
                        }
                        
                        return true;
                    } catch (error) {
                        console.error('[ResponseManager] 处理消息失败:', error);
                        return false;
                    }
                })();
            """
            
            DispatchQueue.main.async {
                webView.evaluateJavaScript(script) { result, error in
                    if let error = error {
                        print("[ResponseManager] 发送消息到WebView失败: \(error.localizedDescription)")
                        completion?(false)
                    } else {
                        print("[ResponseManager] 成功发送消息到WebView: \(self.messageProtocol.getMessageSummary(message))")
                        completion?(true)
                    }
                }
            }
            
        } catch {
            print("[ResponseManager] 序列化消息失败: \(error.localizedDescription)")
            completion?(false)
        }
    }
    
    /**
     * 批量发送消息
     */
    func sendBatchMessages(
        _ messages: [AppMessage],
        completion: ((Int, Int) -> Void)? = nil
    ) {
        var successCount = 0
        var failureCount = 0
        let group = DispatchGroup()
        
        for message in messages {
            group.enter()
            sendMessageToWebView(message) { success in
                if success {
                    successCount += 1
                } else {
                    failureCount += 1
                }
                group.leave()
            }
        }
        
        group.notify(queue: .main) {
            completion?(successCount, failureCount)
        }
    }
    
    /**
     * 发送VoIP相关的通知
     */
    func sendVoIPNotification(
        event: VoIPEvent,
        data: [String: Any] = [:],
        completion: ((Bool) -> Void)? = nil
    ) {
        let action = "voip.\(event.rawValue)"
        sendNotification(action: action, data: data, completion: completion)
    }
    
    /**
     * 发送导航相关的通知
     */
    func sendNavigationNotification(
        event: NavigationEvent,
        data: [String: Any] = [:],
        completion: ((Bool) -> Void)? = nil
    ) {
        let action = "navigation.\(event.rawValue)"
        sendNotification(action: action, data: data, completion: completion)
    }
    
    /**
     * 发送系统相关的通知
     */
    func sendSystemNotification(
        event: SystemEvent,
        data: [String: Any] = [:],
        completion: ((Bool) -> Void)? = nil
    ) {
        let action = "system.\(event.rawValue)"
        sendNotification(action: action, data: data, completion: completion)
    }
}

// MARK: - 事件枚举定义

/**
 * VoIP事件类型
 */
enum VoIPEvent: String, CaseIterable {
    case incomingCall = "incomingCall"
    case callConnected = "callConnected"
    case callEnded = "callEnded"
    case callFailed = "callFailed"
    case loginSuccess = "loginSuccess"
    case loginFailed = "loginFailed"
    case logoutSuccess = "logoutSuccess"
}

/**
 * 导航事件类型
 */
enum NavigationEvent: String, CaseIterable {
    case pageChanged = "pageChanged"
    case navigationStackChanged = "navigationStackChanged"
    case backButtonPressed = "backButtonPressed"
}


