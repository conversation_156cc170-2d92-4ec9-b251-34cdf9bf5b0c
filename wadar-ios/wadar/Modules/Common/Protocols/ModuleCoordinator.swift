//
//  ModuleCoordinator.swift
//  wadar
//
//  Created by guan on 2025/6/26.
//

import Foundation
import SwiftUI

// MARK: - 模块协调器协议

/// 认证模块协调器协议
protocol AuthenticationCoordinatorProtocol {
    func showLogin()
    func showRegister()
    func showForgotPassword()
    func handleLoginSuccess(token: String, userData: UserData)
}

/// VoIP模块协调器协议
protocol VoIPCoordinatorProtocol {
    func initializeVoIP(config: VoIPConfig)
    func makeCall(to number: String)
    func answerCall()
    func endCall()
    func handleIncomingCall(from number: String)
}

/// WebView模块协调器协议
protocol WebViewCoordinatorProtocol {
    func loadWebView(url: URL)
    func refreshWebView()
    func handleWebViewMessage(_ message: [String: Any])
}

/// 主模块协调器协议
protocol MainCoordinatorProtocol {
    func showSplash()
    func showMainContent()
    func handleDeepLink(_ url: URL)
}

// MARK: - 模块协调器实现

/// 应用主协调器
class AppCoordinator: ObservableObject {
    @Injected((any NavigationManagerProtocol).self) private var navigationManager
    @Injected(UserManagerProtocol.self) private var userManager
    
    private var authCoordinator: AuthenticationCoordinatorProtocol?
    private var voipCoordinator: VoIPCoordinatorProtocol?
    private var webViewCoordinator: WebViewCoordinatorProtocol?
    private var mainCoordinator: MainCoordinatorProtocol?
    
    init() {
        setupCoordinators()
    }
    
    private func setupCoordinators() {
        // 这里可以根据需要初始化各个模块的协调器
        // authCoordinator = AuthenticationCoordinator()
        // voipCoordinator = VoIPCoordinator()
        // webViewCoordinator = WebViewCoordinator()
        // mainCoordinator = MainCoordinator()
    }
    
    /// 启动应用
    func start() {
        if userManager.isLoggedIn() {
            // 用户已登录，直接进入主页面
            showMainContent()
        } else {
            // 用户未登录，显示登录页面
            showLogin()
        }
    }
    
    /// 显示登录页面
    func showLogin() {
        navigationManager.replace(with: .login)
    }
    
    /// 显示主内容
    func showMainContent() {
        guard let userData = userManager.getUser() else {
            showLogin()
            return
        }
        
        // 构建WebView URL
        let webviewUrl = DependencyContainer.shared.resolve(ConfigManagerProtocol.self)?.webviewUrl ?? ""
        let urlString = "\(webviewUrl)/pages/auth/app-auth/index?platform=iOS&appType=app&token=\(userData.token)"
        
        if let url = URL(string: urlString) {
            navigationManager.replace(with: .webView(url))
        } else {
            showLogin()
        }
    }
}

// MARK: - 模块间通信事件

/// 模块间通信事件
enum ModuleEvent {
    case authenticationSuccess(UserData)
    case authenticationFailure(Error)
    case voipCallIncoming(String)
    case voipCallEnded
    case webViewLoaded
    case webViewError(Error)
}

/// 事件总线
class EventBus: ObservableObject {
    static let shared = EventBus()
    
    private var subscribers: [String: [(ModuleEvent) -> Void]] = [:]
    
    private init() {}
    
    /// 订阅事件
    func subscribe(to eventType: String, handler: @escaping (ModuleEvent) -> Void) {
        if subscribers[eventType] == nil {
            subscribers[eventType] = []
        }
        subscribers[eventType]?.append(handler)
    }
    
    /// 发布事件
    func publish(_ event: ModuleEvent) {
        let eventType = String(describing: event)
        subscribers[eventType]?.forEach { handler in
            handler(event)
        }
    }
}
