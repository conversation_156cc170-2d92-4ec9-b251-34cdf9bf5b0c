//
//  CommonProtocols.swift
//  wadar
//
//  Created by guan on 2025/6/26.
//

import Foundation
import Combine

// MARK: - 网络管理协议
protocol NetworkManagerProtocol {
    func get<T: Decodable>(url: String, params: [String: Any]?, responseType: T.Type) -> AnyPublisher<APIResponse<T>, Error>
    func post<T: Decodable>(url: String, body: [String: Any], responseType: T.Type) -> AnyPublisher<APIResponse<T>, Error>
    func postWithHandledError<T: Decodable>(url: String, body: [String: Any], responseType: T.Type) -> AnyPublisher<T, Error>
}

// MARK: - 配置管理协议
protocol ConfigManagerProtocol {
    var baseURL: String { get }
    var webviewUrl: String { get }
    var voipDomain: String { get }
    var voipTransport: String { get }
}

// MARK: - 用户管理协议
protocol UserManagerProtocol {
    func saveUser(_ userData: UserData)
    func getUser() -> UserData?
    func clearUser()
    func isLoggedIn() -> Bool
}

// MARK: - 导航管理协议
protocol NavigationManagerProtocol: ObservableObject {
    var currentRoute: AppRoute { get }
    func navigate(to route: AppRoute)
    func replace(with route: AppRoute)
    func goBack()
    func reset()
}

// MARK: - 通用数据模型
struct UserData: Codable {
    let token: String
    let member: Member
}

struct Member: Codable {
    let id: Int
    let name: String
    let phone: String
    let voipNumber: String?
    let voipPassword: String?
}

// MARK: - 应用路由
enum AppRoute: Hashable {
    case splash
    case login
    case register
    case forgotPassword
    case webView(URL)
    case callIncoming
    case callOutgoing
    case callConnected
    case setCallee
    case espProvision

    /// 路由标题
    var title: String {
        switch self {
        case .splash:
            return "启动页"
        case .login:
            return "登录"
        case .register:
            return "注册"
        case .forgotPassword:
            return "忘记密码"
        case .webView:
            return "主页"
        case .callIncoming:
            return "来电"
        case .callOutgoing:
            return "外呼"
        case .callConnected:
            return "通话中"
        case .setCallee:
            return "拨号"
        case .espProvision:
            return "设备配网"
        }
    }
}
