//
//  ConfigTest.swift
//  wadar
//
//  Created by guan on 2025/6/27.
//

import Foundation

/// 环境配置测试工具类
/// 用于验证AppConfig的环境配置是否正确生效
class ConfigTest {
    
    /// 打印当前环境配置信息
    static func printCurrentConfig() {
        let config = AppConfig.shared

        print("=== 当前环境配置信息 ===")
        print("环境: \(config.environment.displayName)")
        print("基础URL: \(config.baseURL)")
        print("WebView URL: \(config.webviewUrl)")
        print("VoIP域名: \(config.voipDomain)")
        print("VoIP传输: \(config.voipTransport)")
        print("调试模式: \(config.isDebugMode)")
        print("启用日志: \(config.enableLogging)")
        print("网络日志: \(config.enableNetworkLogging)")
        print("开发环境: \(config.isDevelopmentEnvironment)")
        print("========================")
    }

    /// 验证VoIP配置是否正确使用AppConfig
    static func testVoIPConfig() {
        let config = AppConfig.shared
        let callViewModel = CallViewModel.shared

        print("=== VoIP配置验证 ===")
        print("AppConfig VoIP域名: \(config.voipDomain)")
        print("CallViewModel域名: \(callViewModel.domain)")
        print("AppConfig VoIP传输: \(config.voipTransport)")
        print("CallViewModel传输: \(callViewModel.transportType)")

        let domainMatch = config.voipDomain == callViewModel.domain
        let transportMatch = config.voipTransport == callViewModel.transportType

        print("域名配置匹配: \(domainMatch ? "✅" : "❌")")
        print("传输配置匹配: \(transportMatch ? "✅" : "❌")")
        print("配置验证结果: \(domainMatch && transportMatch ? "✅ 通过" : "❌ 失败")")
        print("==================")
    }

    /// 测试便利方法
    static func testUtilityMethods() {
        let config = AppConfig.shared

        print("=== 便利方法测试 ===")
        print("带token的WebView URL: \(config.getWebViewURL(with: "test_token_123"))")
        print("API URL示例: \(config.getAPIURL(for: "/api/user/info"))")
        print("环境信息: \(config.getEnvironmentInfo())")
        print("==================")
    }
    
    /// 验证编译标志是否正确设置
    static func verifyCompilationFlags() {
        print("\n=== 编译标志验证 ===")
        
        #if DEV
        print("✅ DEV 编译标志已设置")
        if AppConfig.shared.environment == .dev {
            print("✅ 环境配置与编译标志匹配")
        } else {
            print("❌ 环境配置与编译标志不匹配")
        }
        #elseif TEST
        print("✅ TEST 编译标志已设置")
        if AppConfig.shared.environment == .test {
            print("✅ 环境配置与编译标志匹配")
        } else {
            print("❌ 环境配置与编译标志不匹配")
        }
        #else
        print("✅ PROD 编译标志已设置（默认）")
        if AppConfig.shared.environment == .prod {
            print("✅ 环境配置与编译标志匹配")
        } else {
            print("❌ 环境配置与编译标志不匹配")
        }
        #endif
        
        print("==================")
    }
    
    /// 运行完整的配置测试
    static func runFullTest() {
        print("🚀 开始环境配置测试...")
        printCurrentConfig()
        testVoIPConfig()
        testUtilityMethods()
        verifyCompilationFlags()
        print("✅ 环境配置测试完成")
    }
}
