import Foundation
import Combine

class NetworkManager: NSObject, NetworkManagerProtocol {
    static let shared = NetworkManager()

    // 专门为iOS模拟器优化的URLSession配置
    private lazy var urlSession: URLSession = {
        let config = URLSessionConfiguration.default

        // 关键：强制禁用IPv6，只使用IPv4
        config.connectionProxyDictionary = [
            "HTTPEnable": 0,
            "HTTPSEnable": 0,
            "SOCKSEnable": 0,
            "ProxyAutoConfigEnable": 0
        ]

        // 额外的IPv4强制配置
        if #available(iOS 13.0, *) {
            // 在iOS 13+中，尝试禁用IPv6
            config.waitsForConnectivity = false
        }

        // 设置网络服务类型为默认
        config.networkServiceType = .default

        // 禁用多路径TCP（这在模拟器中可能导致问题）
        config.multipathServiceType = .none

        // 基本超时设置
        config.timeoutIntervalForRequest = 30.0
        config.timeoutIntervalForResource = 60.0

        // 允许蜂窝网络访问
        config.allowsCellularAccess = true

        // 禁用HTTP管道化（可能在模拟器中不稳定）
        config.httpShouldUsePipelining = false

        // 设置最大连接数
        config.httpMaximumConnectionsPerHost = 2

        return URLSession(configuration: config, delegate: self, delegateQueue: nil)
    }()

    private override init() {}

    // 检测是否在模拟器中运行
    private var isRunningOnSimulator: Bool {
        #if targetEnvironment(simulator)
        return true
        #else
        return false
        #endif
    }

    private func logRequest(_ url: String, method: String, params: [String: Any]?, response: Data?, error: Error?, httpResponse: HTTPURLResponse? = nil) {
        print("\n[NetworkManager] ==================== 请求详情 ====================")
        print("[NetworkManager] 请求方式: \(method)")
        print("[NetworkManager] 请求URL: \(url)")
        print("[NetworkManager] 请求时间: \(Date())")

        if let params = params {
            print("[NetworkManager] 请求参数: \(params)")
        }

        if let httpResponse = httpResponse {
            print("[NetworkManager] HTTP状态码: \(httpResponse.statusCode)")
            print("[NetworkManager] 响应头: \(httpResponse.allHeaderFields)")
        }

        if let error = error {
            print("[NetworkManager] ❌ 请求失败: \(error.localizedDescription)")
            if let urlError = error as? URLError {
                print("[NetworkManager] URLError代码: \(urlError.code.rawValue)")
                print("[NetworkManager] URLError描述: \(urlError.localizedDescription)")
                if let failingURL = urlError.failingURL {
                    print("[NetworkManager] 失败的URL: \(failingURL)")
                }
            }
        }

        if let response = response {
            print("[NetworkManager] 响应数据大小: \(response.count) bytes")
            if let str = String(data: response, encoding: .utf8) {
                print("[NetworkManager] ✅ 响应内容: \(str)")
            } else {
                print("[NetworkManager] ⚠️ 无法解析响应内容为UTF-8字符串")
            }
        }
        print("[NetworkManager] ================================================\n")
    }

    private func showGlobalError(_ message: String) {
        NotificationCenter.default.post(name: .NetworkErrorNotification, object: nil, userInfo: ["message": message])
    }



    func get<T: Decodable>(url: String, params: [String: Any]? = nil, responseType: T.Type) -> AnyPublisher<APIResponse<T>, Error> {
        var urlString = url
        if let params = params, !params.isEmpty {
            let query = params.map { "\($0.key)=\($0.value)" }.joined(separator: "&")
            urlString += "?" + query
        }
        guard let url = URL(string: urlString) else {
            logRequest(urlString, method: "GET", params: params, response: nil, error: URLError(.badURL))
            return Fail(error: URLError(.badURL)).eraseToAnyPublisher()
        }

        var request = URLRequest(url: url)
        request.httpMethod = "GET"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue("keep-alive", forHTTPHeaderField: "Connection")
        request.setValue("gzip, deflate", forHTTPHeaderField: "Accept-Encoding")
        request.setValue("Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15", forHTTPHeaderField: "User-Agent")

        // 添加token到请求头
        if let token = UserManager.shared.getToken() {
            request.setValue(token, forHTTPHeaderField: "token")
        }

        // 设置缓存策略
        request.cachePolicy = .reloadIgnoringLocalCacheData

        return urlSession.dataTaskPublisher(for: request)
            .handleEvents(receiveOutput: { output in
                self.logRequest(urlString, method: "GET", params: params, response: output.data, error: nil)
            }, receiveCompletion: { completion in
                if case .failure(let error) = completion {
                    self.logRequest(urlString, method: "GET", params: params, response: nil, error: error)
                }
            })
            .map { $0.data }
            .decode(type: APIResponse<T>.self, decoder: JSONDecoder())
            .eraseToAnyPublisher()
    }

    func post<T: Decodable>(url: String, body: [String: Any], responseType: T.Type) -> AnyPublisher<APIResponse<T>, Error> {
        print("[NetworkManager] 🌐 请求URL: \(url)")

        guard let url = URL(string: url) else {
            return Fail(error: URLError(.badURL)).eraseToAnyPublisher()
        }

        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue("application/json", forHTTPHeaderField: "Accept")

        // 添加token到请求头
        if let token = UserManager.shared.getToken() {
            request.setValue(token, forHTTPHeaderField: "token")
            print("[NetworkManager] 添加token到请求头")
        }

        // 模拟器特定的请求头设置
        if isRunningOnSimulator {
            print("[NetworkManager] 🔧 检测到模拟器环境，应用特殊配置")
            request.setValue("close", forHTTPHeaderField: "Connection")  // 避免keep-alive在模拟器中的问题
            request.setValue("no-cache", forHTTPHeaderField: "Cache-Control")
        } else {
            request.setValue("keep-alive", forHTTPHeaderField: "Connection")
        }

        // 设置请求体
        do {
            request.httpBody = try JSONSerialization.data(withJSONObject: body, options: [])
            print("[NetworkManager] 请求体: \(String(data: request.httpBody!, encoding: .utf8) ?? "无法解析")")
        } catch {
            print("[NetworkManager] ❌ JSON序列化失败: \(error)")
            return Fail(error: error).eraseToAnyPublisher()
        }

        print("[NetworkManager] 🚀 开始发送请求...")

        return urlSession.dataTaskPublisher(for: request)
            .handleEvents(
                receiveSubscription: { _ in
                    print("[NetworkManager] � 订阅开始")
                },
                receiveOutput: { output in
                    let httpResponse = output.response as? HTTPURLResponse
                    let statusCode = httpResponse?.statusCode ?? -1
                    print("[NetworkManager] 📥 收到响应 - 状态码: \(statusCode)")

                    if let responseString = String(data: output.data, encoding: .utf8) {
                        print("[NetworkManager] 📥 响应内容: \(responseString)")
                    }
                },
                receiveCompletion: { completion in
                    switch completion {
                    case .finished:
                        print("[NetworkManager] ✅ 请求完成")
                    case .failure(let error):
                        print("[NetworkManager] ❌ 请求失败: \(error)")
                        if let urlError = error as? URLError {
                            print("[NetworkManager] URLError代码: \(urlError.code.rawValue)")
                            print("[NetworkManager] URLError描述: \(urlError.localizedDescription)")

                            // 特别处理模拟器中的网络连接丢失错误
                            if urlError.code == .networkConnectionLost && self.isRunningOnSimulator {
                                print("[NetworkManager] ⚠️ 模拟器网络连接丢失，这可能是IPv6相关问题")
                                print("[NetworkManager] 💡 建议：检查模拟器网络设置或尝试重启模拟器")
                            }
                        }
                    }
                }
            )
            .map(\.data)
            .decode(type: APIResponse<T>.self, decoder: JSONDecoder())
            .eraseToAnyPublisher()
    }

    /// 通用POST方法，自动处理业务错误和友好提示
    func postWithHandledError<T: Decodable>(url: String, body: [String: Any], responseType: T.Type) -> AnyPublisher<T, Error> {
        post(url: url, body: body, responseType: T.self)
            .tryMap { response in
                if response.status == "00000", let data = response.data {
                    return data
                } else {
                    // 检查是否是token失效错误
                    if response.message.contains("TOKEN WAS WRONG") {
                        print("[NetworkManager] 检测到token失效，清除用户数据并发送通知")
                        // 清除用户数据
                        UserManager.shared.clearUser()
                        // 发送token失效通知
                        NotificationCenter.default.post(name: .TokenInvalidNotification, object: nil)
                    } else {
                        self.showGlobalError(response.message)
                    }
                    throw NSError(domain: "API", code: -1, userInfo: [NSLocalizedDescriptionKey: response.message])
                }
            }
            .catch { error -> AnyPublisher<T, Error> in
                self.showGlobalError((error as NSError).localizedDescription)
                return Fail(error: error).eraseToAnyPublisher()
            }
            .eraseToAnyPublisher()
    }

    /// 通用GET方法，自动处理业务错误和友好提示
    func getWithHandledError<T: Decodable>(url: String, params: [String: Any]? = nil, responseType: T.Type) -> AnyPublisher<T, Error> {
        get(url: url, params: params, responseType: T.self)
            .tryMap { response in
                if response.status == "00000", let data = response.data {
                    return data
                } else {
                    // 检查是否是token失效错误
                    if response.message.contains("TOKEN WAS WRONG") {
                        print("[NetworkManager] 检测到token失效，清除用户数据并发送通知")
                        // 清除用户数据
                        UserManager.shared.clearUser()
                        // 发送token失效通知
                        NotificationCenter.default.post(name: .TokenInvalidNotification, object: nil)
                    } else {
                        self.showGlobalError(response.message)
                    }
                    throw NSError(domain: "API", code: -1, userInfo: [NSLocalizedDescriptionKey: response.message])
                }
            }
            .catch { error -> AnyPublisher<T, Error> in
                self.showGlobalError((error as NSError).localizedDescription)
                return Fail(error: error).eraseToAnyPublisher()
            }
            .eraseToAnyPublisher()
    }




}

// MARK: - URLSessionDelegate
extension NetworkManager: URLSessionDelegate {
    func urlSession(_ session: URLSession, didBecomeInvalidWithError error: Error?) {
        if let error = error {
            print("[NetworkManager] URLSession失效: \(error)")
        }
    }

    func urlSession(_ session: URLSession, task: URLSessionTask, didCompleteWithError error: Error?) {
        if let error = error {
            print("[NetworkManager] 任务完成时出错: \(error)")

            // 特别处理CFNetwork错误
            if let nsError = error as NSError? {
                print("[NetworkManager] 错误域: \(nsError.domain)")
                print("[NetworkManager] 错误代码: \(nsError.code)")
                print("[NetworkManager] 错误信息: \(nsError.userInfo)")

                // 检查是否是IPv6相关的错误
                if nsError.domain == "kCFErrorDomainCFNetwork" && nsError.code == -1005 {
                    print("[NetworkManager] 检测到CFNetwork连接丢失错误，可能与IPv6有关")
                }
            }
        }
    }
}

// MARK: - URLSessionTaskDelegate
extension NetworkManager: URLSessionTaskDelegate {
    func urlSession(_ session: URLSession, task: URLSessionTask, willPerformHTTPRedirection response: HTTPURLResponse, newRequest request: URLRequest, completionHandler: @escaping (URLRequest?) -> Void) {
        print("[NetworkManager] HTTP重定向: \(request.url?.absoluteString ?? "unknown")")
        completionHandler(request)
    }

    func urlSession(_ session: URLSession, task: URLSessionTask, didReceive challenge: URLAuthenticationChallenge, completionHandler: @escaping (URLSession.AuthChallengeDisposition, URLCredential?) -> Void) {
        print("[NetworkManager] 收到认证挑战: \(challenge.protectionSpace.host)")

        // 对于HTTPS，使用默认处理
        if challenge.protectionSpace.authenticationMethod == NSURLAuthenticationMethodServerTrust {
            completionHandler(.performDefaultHandling, nil)
        } else {
            completionHandler(.performDefaultHandling, nil)
        }
    }
}

extension Notification.Name {
    static let NetworkErrorNotification = Notification.Name("NetworkErrorNotification")
    static let TokenInvalidNotification = Notification.Name("TokenInvalidNotification")
    static let saveWebViewState = Notification.Name("saveWebViewState")
    static let restoreWebViewState = Notification.Name("restoreWebViewState")
}
