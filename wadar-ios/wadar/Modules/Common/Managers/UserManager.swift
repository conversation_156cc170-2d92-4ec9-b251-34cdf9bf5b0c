//
//  UserManager.swift
//  wadar
//
//  Created by guan on 2025/6/26.
//

import Foundation

class UserManager: UserManagerProtocol {
    static let shared = UserManager()
    private let userKey = "currentUser"
    private let tokenKey = "userToken"

    private init() {}

    func saveUser(_ user: UserData) {
        if let encoded = try? JSONEncoder().encode(user) {
            UserDefaults.standard.set(encoded, forKey: userKey)
            UserDefaults.standard.set(user.token, forKey: tokenKey)
        }
    }

    func getUser() -> UserData? {
        if let data = UserDefaults.standard.data(forKey: userKey),
           let user = try? JSONDecoder().decode(UserData.self, from: data) {
            return user
        }
        return nil
    }

    func getToken() -> String? {
        return UserDefaults.standard.string(forKey: tokenKey)
    }

    func clearUser() {
        UserDefaults.standard.removeObject(forKey: userKey)
        UserDefaults.standard.removeObject(forKey: tokenKey)
    }

    func isLoggedIn() -> Bool {
        return getUser() != nil && getToken() != nil
    }
}
