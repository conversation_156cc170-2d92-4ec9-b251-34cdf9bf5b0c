//
//  AppConfig.swift
//  wadar
//
//  Created by guan on 2025/6/26.
//
import Foundation
import Combine

class AppConfig: ConfigManagerProtocol {
    static let shared = AppConfig()

    enum Environment: String, CaseIterable {
        case dev = "开发环境"
        case test = "测试环境"
        case prod = "生产环境"

        var displayName: String {
            return self.rawValue
        }
    }

    // 当前环境（可通过编译flag切换）
    let environment: Environment
    let baseURL: String
    let webviewUrl: String
    let voipDomain: String
    let voipTransport: String

    // 调试相关配置
    let isDebugMode: Bool
    let enableLogging: Bool
    let enableNetworkLogging: Bool
    
    private init() {
#if DEV
        self.environment = .dev
        self.baseURL = "http://*************:8090"
        self.webviewUrl = "http://localhost:1024"
        self.voipDomain = "*************:7160"
        self.voipTransport = "UDP"
        self.isDebugMode = true
        self.enableLogging = true
        self.enableNetworkLogging = true
#elseif TEST
        self.environment = .test
        self.baseURL = "https://cmcctest.rfcare.cn"
        self.webviewUrl = "https://cmcctest.rfcare.cn"
        self.voipDomain = "*************:7160"
        self.voipTransport = "UDP"
        self.isDebugMode = true
        self.enableLogging = true
        self.enableNetworkLogging = true
#else
        // 生产环境配置
        self.environment = .prod
        self.baseURL = "https://cmcc.rfcare.cn"
        self.webviewUrl = "https://cmcc.rfcare.cn/"
        self.voipDomain = "*************:7160"
        self.voipTransport = "UDP"
        self.isDebugMode = false
        self.enableLogging = false
        self.enableNetworkLogging = false
#endif
    }

    // MARK: - 便利方法

    /// 获取带token的WebView URL
    /// - Parameter token: 用户token
    /// - Returns: 完整的WebView URL
    func getWebViewURL(with token: String? = nil) -> String {
        if let token = token {
            let separator = webviewUrl.contains("?") ? "&" : "?"
            return "\(webviewUrl)\(separator)token=\(token)"
        }
        return webviewUrl
    }

    /// 获取API完整URL
    /// - Parameter endpoint: API端点
    /// - Returns: 完整的API URL
    func getAPIURL(for endpoint: String) -> String {
        let cleanEndpoint = endpoint.hasPrefix("/") ? String(endpoint.dropFirst()) : endpoint
        return "\(baseURL)/\(cleanEndpoint)"
    }

    /// 获取当前环境信息
    /// - Returns: 环境信息字符串
    func getEnvironmentInfo() -> String {
        return "当前环境: \(environment.displayName) (\(environment.rawValue))"
    }

    /// 是否为开发或测试环境
    var isDevelopmentEnvironment: Bool {
        return environment == .dev || environment == .test
    }
}

