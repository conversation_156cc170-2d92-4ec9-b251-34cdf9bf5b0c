//
//  CommonModels.swift
//  wadar
//
//  Created by guan on 2025/6/26.
//

import Foundation

// MARK: - 通用数据模型

/// 扩展的Member模型，包含所有用户信息字段
extension Member {
    
    /// 扩展的Member字段
    var nickname: String? { return nil }
    var profile: String? { return nil }
    var email: String? { return nil }
    var gender: String? { return nil }
    var shareCode: String? { return nil }
    var status: String? { return nil }
    var age: Int? { return nil }
    var addr: String? { return nil }
    var useWxOpenId: String? { return nil }
    var firstLetter: String? { return nil }
    var idcardType: String? { return nil }
    var followedMp: Bool? { return nil }
}

// MARK: - API响应模型

/// 通用API响应模型
struct APIResponse<T: Decodable>: Decodable {
    let status: String
    let message: String
    let data: T?
}

// MARK: - 验证码相关模型

/// 验证码响应数据模型
struct VCodeData: Codable {
    let vcode: String?
    let message: String?
}

/// 验证码类型枚举
enum VCodeType: String, CaseIterable {
    case register = "1"      // 注册验证码
    case forgotPassword = "2" // 忘记密码验证码
    case login = "4"         // 登录验证码

    var displayName: String {
        switch self {
        case .register:
            return "注册验证码"
        case .forgotPassword:
            return "忘记密码验证码"
        case .login:
            return "登录验证码"
        }
    }
}

/// 登录类型枚举
enum LoginType: String, CaseIterable {
    case password = "pwd"
    case verificationCode = "vcode"

    var displayName: String {
        switch self {
        case .password:
            return "密码登录"
        case .verificationCode:
            return "验证码登录"
        }
    }
}

// MARK: - 注册和密码相关模型

/// 注册响应数据模型
struct RegisterData: Codable {
    let message: String?
    let success: Bool?
}

/// 修改密码响应数据模型
struct ChangePasswordData: Codable {
    let message: String?
    let success: Bool?
}
