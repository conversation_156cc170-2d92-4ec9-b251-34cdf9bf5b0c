//
//  wadarApp.swift
//  wadar
//
//  Created by <PERSON><PERSON> on 25/6/24.
//

import SwiftUI

@main
struct wadarApp: App {
    // 集成AppDelegate
    @UIApplicationDelegateAdaptor(AppDelegate.self) var appDelegate

    init() {
        // 启动后台任务管理器
        BackgroundTaskManager.shared.startApplicationStateMonitoring()
        BackgroundTaskManager.shared.startNetworkMonitoring()

        // 注意：不在这里调度后台任务，因为AppDelegate还没有完成初始化
        // 后台任务的调度将在AppDelegate的didFinishLaunchingWithOptions中进行

        // 打印环境配置信息（仅在非生产环境）
        if AppConfig.shared.isDevelopmentEnvironment || AppConfig.shared.environment == .test {
            ConfigTest.runFullTest()

            // 测试ESP-IDF Provisioning库集成
            ESPProvisionTest.testESPProvisionIntegration()
        }
    }

    var body: some Scene {
        WindowGroup {
            ContentView()
                .onReceive(NotificationCenter.default.publisher(for: UIApplication.didEnterBackgroundNotification)) { _ in
                    print("应用进入后台 - 来自SwiftUI")
                }
                .onReceive(NotificationCenter.default.publisher(for: UIApplication.willEnterForegroundNotification)) { _ in
                    print("应用即将进入前台 - 来自SwiftUI")
                }
        }
    }
}
