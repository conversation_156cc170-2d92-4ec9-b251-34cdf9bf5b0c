name: iOS Build

on:
  push:
    branches:
      - main
      - master
      - develop

jobs:
  build:
    runs-on: macos-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Ruby for CocoaPods
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: '3.1'

      - name: Install CocoaPods
        run: sudo gem install cocoapods

      - name: Install dependencies
        run: pod install

      - name: Set up Xcode
        uses: maxim-lobanov/setup-xcode@v1
        with:
          xcode-version: '15.2'

      - name: Build app
        run: |
          xcodebuild -workspace wadar.xcworkspace \
            -scheme wadar \
            -sdk iphonesimulator \
            -destination 'platform=iOS Simulator,name=iPhone 15,OS=17.2' \
            clean build

      - name: Archive app (optional)
        run: |
          xcodebuild -workspace wadar.xcworkspace \
            -scheme wadar \
            -sdk iphoneos \
            -configuration Release \
            -archivePath ${{ github.workspace }}/build/wadar.xcarchive \
            clean archive

      - name: Export IPA (optional)
        run: |
          xcodebuild -exportArchive \
            -archivePath ${{ github.workspace }}/build/wadar.xcarchive \
            -exportOptionsPlist wadar/ExportOptions.plist \
            -exportPath ${{ github.workspace }}/build/ipa
        continue-on-error: true

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: wadar-build
          path: ${{ github.workspace }}/build/
