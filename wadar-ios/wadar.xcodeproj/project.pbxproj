// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		04FC498BF615497C84D59C56 /* CommonModels.swift in Sources */ = {isa = PBXBuildFile; fileRef = BC215C8C89EA49BE9AB9B947 /* CommonModels.swift */; };
		1A5325602C2A9C0D00B8534E /* wadarApp.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A53255F2C2A9C0D00B8534E /* wadarApp.swift */; };
		1A5325642C2A9C0E00B8534E /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 1A5325632C2A9C0E00B8534E /* Assets.xcassets */; };
		1A5325672C2A9C0E00B8534E /* Preview Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 1A5325662C2A9C0E00B8534E /* Preview Assets.xcassets */; };
		4ADF6AA13CDF4EC6A9D808B5DC1CC155 /* ConfigTest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2C9148060F1F4FA9904865BE4570BC90 /* ConfigTest.swift */; };
		5BEF7BB24DEF5FD7B0E909C6 /* ESPProvisionTest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3D0259170G2G5GB0A05976CF5681CD01 /* ESPProvisionTest.swift */; };
		ESP001A1B2C3D4E5F6G7H8I9J /* ESPProvisionManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = ESP001F1E2D3C4B5A6978H9I0J /* ESPProvisionManager.swift */; };
		ESP002B2C3D4E5F6G7H8I9J0K /* ESPProvisionViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = ESP002G2F3E4D5C6B7A89I0J1K /* ESPProvisionViewModel.swift */; };
		ESP003C3D4E5F6G7H8I9J0K1L /* ESPProvisionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = ESP003H3G4F5E6D7C8B9A0K1L2 /* ESPProvisionView.swift */; };
		ESP004D4E5F6G7H8I9J0K1L2M /* DeviceScanView.swift in Sources */ = {isa = PBXBuildFile; fileRef = ESP004I4H5G6F7E8D9C0B1L2M3 /* DeviceScanView.swift */; };
		ESP005E5F6G7H8I9J0K1L2M3N /* WiFiConfigView.swift in Sources */ = {isa = PBXBuildFile; fileRef = ESP005J5I6H7G8F9E0D1C2M3N4 /* WiFiConfigView.swift */; };
		ESP006F6G7H8I9J0K1L2M3N4O /* DeviceConnectView.swift in Sources */ = {isa = PBXBuildFile; fileRef = ESP006K6J7I8H9G0F1E2D3N4O5 /* DeviceConnectView.swift */; };
		ESP007G7H8I9J0K1L2M3N4O5P /* ProvisioningView.swift in Sources */ = {isa = PBXBuildFile; fileRef = ESP007L7K8J9I0H1G2F3E4O5P6 /* ProvisioningView.swift */; };
		509639869DE4FE3A3B4F2511 /* Pods_wadar.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = EA696D4C6A3BAFC827FA092B /* Pods_wadar.framework */; };
		56801C4C2DFAAAA100DEDAD3 /* ContentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 56801C4B2DFAAA9700DEDAD3 /* ContentView.swift */; };
		569B450E2E02C02F006A6849 /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 569B450D2E02C02F006A6849 /* AppDelegate.swift */; };
		56AFFC5F2E0D72CB00C14673 /* UserManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 56AFFC2D2E0D72CA00C14673 /* UserManager.swift */; };
		56AFFC602E0D72CB00C14673 /* AppConfig.swift in Sources */ = {isa = PBXBuildFile; fileRef = 56AFFC2A2E0D72CA00C14673 /* AppConfig.swift */; };
		56AFFC612E0D72CB00C14673 /* NetworkManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 56AFFC2C2E0D72CA00C14673 /* NetworkManager.swift */; };
		56AFFC622E0D72CB00C14673 /* VCodeService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 56AFFC1F2E0D72CA00C14673 /* VCodeService.swift */; };
		56AFFC632E0D72CB00C14673 /* WebViewBridge.swift in Sources */ = {isa = PBXBuildFile; fileRef = 56AFFC5B2E0D72CA00C14673 /* WebViewBridge.swift */; };
		56AFFC642E0D72CB00C14673 /* SplashView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 56AFFC3B2E0D72CA00C14673 /* SplashView.swift */; };
		56AFFC652E0D72CB00C14673 /* PushNotificationManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 56AFFC422E0D72CA00C14673 /* PushNotificationManager.swift */; };
		56AFFC662E0D72CB00C14673 /* ForgotPasswordView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 56AFFC252E0D72CA00C14673 /* ForgotPasswordView.swift */; };
		56AFFC672E0D72CB00C14673 /* CallManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 56AFFC412E0D72CA00C14673 /* CallManager.swift */; };
		56AFFC682E0D72CB00C14673 /* ForgotPasswordViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 56AFFC212E0D72CA00C14673 /* ForgotPasswordViewModel.swift */; };
		56AFFC6A2E0D72CB00C14673 /* CallIncomingView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 56AFFC4A2E0D72CA00C14673 /* CallIncomingView.swift */; };
		56AFFC6B2E0D72CB00C14673 /* CommonModels.swift in Sources */ = {isa = PBXBuildFile; fileRef = 56AFFC2F2E0D72CA00C14673 /* CommonModels.swift */; };
		56AFFC6C2E0D72CB00C14673 /* CallOutgoingView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 56AFFC4B2E0D72CA00C14673 /* CallOutgoingView.swift */; };
		56AFFC6D2E0D72CB00C14673 /* DeviceMessageHandler.swift in Sources */ = {isa = PBXBuildFile; fileRef = 56AFFC4F2E0D72CA00C14673 /* DeviceMessageHandler.swift */; };
		56AFFC6E2E0D72CB00C14673 /* StorageMessageHandler.swift in Sources */ = {isa = PBXBuildFile; fileRef = 56AFFC512E0D72CA00C14673 /* StorageMessageHandler.swift */; };
		56AFFC6F2E0D72CB00C14673 /* LoginView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 56AFFC262E0D72CA00C14673 /* LoginView.swift */; };
		56AFFC702E0D72CB00C14673 /* VoIPMessageHandler.swift in Sources */ = {isa = PBXBuildFile; fileRef = 56AFFC542E0D72CA00C14673 /* VoIPMessageHandler.swift */; };
		56AFFC712E0D72CB00C14673 /* MessageProtocol.swift in Sources */ = {isa = PBXBuildFile; fileRef = 56AFFC572E0D72CA00C14673 /* MessageProtocol.swift */; };
		56AFFC722E0D72CB00C14673 /* ResponseManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 56AFFC5A2E0D72CA00C14673 /* ResponseManager.swift */; };
		56AFFC732E0D72CB00C14673 /* DependencyContainer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 56AFFC342E0D72CA00C14673 /* DependencyContainer.swift */; };
		56AFFC742E0D72CB00C14673 /* UserMessageHandler.swift in Sources */ = {isa = PBXBuildFile; fileRef = 56AFFC532E0D72CA00C14673 /* UserMessageHandler.swift */; };
		56AFFC752E0D72CB00C14673 /* LoginViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 56AFFC222E0D72CA00C14673 /* LoginViewModel.swift */; };
		56AFFC762E0D72CB00C14673 /* ModuleCoordinator.swift in Sources */ = {isa = PBXBuildFile; fileRef = 56AFFC322E0D72CA00C14673 /* ModuleCoordinator.swift */; };
		56AFFC772E0D72CB00C14673 /* MessageRouter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 56AFFC592E0D72CA00C14673 /* MessageRouter.swift */; };
		56AFFC782E0D72CB00C14673 /* WebViewContainerView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 56AFFC3D2E0D72CA00C14673 /* WebViewContainerView.swift */; };
		56AFFC792E0D72CB00C14673 /* NavigationMessageHandler.swift in Sources */ = {isa = PBXBuildFile; fileRef = 56AFFC502E0D72CA00C14673 /* NavigationMessageHandler.swift */; };
		56AFFC7A2E0D72CB00C14673 /* WebView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 56AFFC3C2E0D72CA00C14673 /* WebView.swift */; };
		56AFFC7B2E0D72CB00C14673 /* VoIPModels.swift in Sources */ = {isa = PBXBuildFile; fileRef = 56AFFC442E0D72CA00C14673 /* VoIPModels.swift */; };
		56AFFC7C2E0D72CB00C14673 /* CallViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 56AFFC472E0D72CA00C14673 /* CallViewModel.swift */; };
		56AFFC7D2E0D72CB00C14673 /* CommonProtocols.swift in Sources */ = {isa = PBXBuildFile; fileRef = 56AFFC312E0D72CA00C14673 /* CommonProtocols.swift */; };
		56AFFC7E2E0D72CB00C14673 /* SetCalleeView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 56AFFC4C2E0D72CA00C14673 /* SetCalleeView.swift */; };
		56AFFC7F2E0D72CB00C14673 /* RegisterViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 56AFFC232E0D72CA00C14673 /* RegisterViewModel.swift */; };
		56AFFC802E0D72CB00C14673 /* SystemMessageHandler.swift in Sources */ = {isa = PBXBuildFile; fileRef = 56AFFC522E0D72CA00C14673 /* SystemMessageHandler.swift */; };
		56AFFC812E0D72CB00C14673 /* WebViewManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 56AFFC372E0D72CA00C14673 /* WebViewManager.swift */; };
		56AFFC822E0D72CB00C14673 /* BridgeProtocols.swift in Sources */ = {isa = PBXBuildFile; fileRef = 56AFFC562E0D72CA00C14673 /* BridgeProtocols.swift */; };
		56AFFC832E0D72CB00C14673 /* RegisterView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 56AFFC272E0D72CA00C14673 /* RegisterView.swift */; };
		56AFFC842E0D72CB00C14673 /* CallConnectView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 56AFFC492E0D72CA00C14673 /* CallConnectView.swift */; };
		56AFFC852E0D72CB00C14673 /* NavigationManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 56AFFC2B2E0D72CA00C14673 /* NavigationManager.swift */; };
		56AFFC862E0D72CB00C14673 /* BackgroundTaskManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 56AFFC402E0D72CA00C14673 /* BackgroundTaskManager.swift */; };
		56AFFC872E0D72CB00C14673 /* AuthenticationModels.swift in Sources */ = {isa = PBXBuildFile; fileRef = 56AFFC1D2E0D72CA00C14673 /* AuthenticationModels.swift */; };
		74AA1308EC50462EBD2ACA0C /* DependencyContainer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 485DAEB88A5E445FAD3A7236 /* DependencyContainer.swift */; };
		A145809A389B458B8D5AB593 /* CommonProtocols.swift in Sources */ = {isa = PBXBuildFile; fileRef = CC002C43E60044F392F0E667 /* CommonProtocols.swift */; };
		B8F7A1E5A4B84F6A9C3D2E71 /* AuthenticationModels.swift in Sources */ = {isa = PBXBuildFile; fileRef = 879C318B0B73443A93ECAC45 /* AuthenticationModels.swift */; };
		C9E8B2F6B5C95G7B0D4E3F82 /* VoIPModels.swift in Sources */ = {isa = PBXBuildFile; fileRef = 26FD46AECBCD44DABC47A9DC /* VoIPModels.swift */; };
		D0F9C3G7C6D06H8C1E5F4G93 /* BridgeProtocols.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9A1FE6D16E584D3984A5EED5 /* BridgeProtocols.swift */; };
		EF0716CA972B4E45BF9FFDD7 /* ModuleCoordinator.swift in Sources */ = {isa = PBXBuildFile; fileRef = 19AB02294FAC4B92A2713E50 /* ModuleCoordinator.swift */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		19AB02294FAC4B92A2713E50 /* ModuleCoordinator.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = wadar/Modules/Common/Protocols/ModuleCoordinator.swift; sourceTree = "<group>"; };
		1A53255C2C2A9C0D00B8534E /* wadar.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = wadar.app; sourceTree = BUILT_PRODUCTS_DIR; };
		1A53255F2C2A9C0D00B8534E /* wadarApp.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = wadarApp.swift; sourceTree = "<group>"; };
		1A5325632C2A9C0E00B8534E /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		1A5325662C2A9C0E00B8534E /* Preview Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = "Preview Assets.xcassets"; sourceTree = "<group>"; };
		26FD46AECBCD44DABC47A9DC /* VoIPModels.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = wadar/Modules/VoIP/Models/VoIPModels.swift; sourceTree = "<group>"; };
		2C9148060F1F4FA9904865BE4570BC90 /* ConfigTest.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ConfigTest.swift; sourceTree = "<group>"; };
		3D0259170G2G5GB0A05976CF5681CD01 /* ESPProvisionTest.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ESPProvisionTest.swift; sourceTree = "<group>"; };
		ESP001F1E2D3C4B5A6978H9I0J /* ESPProvisionManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "ESPProvisionManager.swift"; sourceTree = "<group>"; };
		ESP002G2F3E4D5C6B7A89I0J1K /* ESPProvisionViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "ESPProvisionViewModel.swift"; sourceTree = "<group>"; };
		ESP003H3G4F5E6D7C8B9A0K1L2 /* ESPProvisionView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "ESPProvisionView.swift"; sourceTree = "<group>"; };
		ESP004I4H5G6F7E8D9C0B1L2M3 /* DeviceScanView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "DeviceScanView.swift"; sourceTree = "<group>"; };
		ESP005J5I6H7G8F9E0D1C2M3N4 /* WiFiConfigView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "WiFiConfigView.swift"; sourceTree = "<group>"; };
		ESP006K6J7I8H9G0F1E2D3N4O5 /* DeviceConnectView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "DeviceConnectView.swift"; sourceTree = "<group>"; };
		ESP007L7K8J9I0H1G2F3E4O5P6 /* ProvisioningView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "ProvisioningView.swift"; sourceTree = "<group>"; };
		485DAEB88A5E445FAD3A7236 /* DependencyContainer.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = wadar/Modules/Common/Utils/DependencyContainer.swift; sourceTree = "<group>"; };
		4C66385879DADED059AE5D23 /* Pods-wadar.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-wadar.debug.xcconfig"; path = "Target Support Files/Pods-wadar/Pods-wadar.debug.xcconfig"; sourceTree = "<group>"; };
		56801C4B2DFAAA9700DEDAD3 /* ContentView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContentView.swift; sourceTree = "<group>"; };
		569B450D2E02C02F006A6849 /* AppDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		56AFFC1D2E0D72CA00C14673 /* AuthenticationModels.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AuthenticationModels.swift; sourceTree = "<group>"; };
		56AFFC1F2E0D72CA00C14673 /* VCodeService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VCodeService.swift; sourceTree = "<group>"; };
		56AFFC212E0D72CA00C14673 /* ForgotPasswordViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ForgotPasswordViewModel.swift; sourceTree = "<group>"; };
		56AFFC222E0D72CA00C14673 /* LoginViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LoginViewModel.swift; sourceTree = "<group>"; };
		56AFFC232E0D72CA00C14673 /* RegisterViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RegisterViewModel.swift; sourceTree = "<group>"; };
		56AFFC252E0D72CA00C14673 /* ForgotPasswordView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ForgotPasswordView.swift; sourceTree = "<group>"; };
		56AFFC262E0D72CA00C14673 /* LoginView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LoginView.swift; sourceTree = "<group>"; };
		56AFFC272E0D72CA00C14673 /* RegisterView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RegisterView.swift; sourceTree = "<group>"; };
		56AFFC2A2E0D72CA00C14673 /* AppConfig.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppConfig.swift; sourceTree = "<group>"; };
		56AFFC2B2E0D72CA00C14673 /* NavigationManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NavigationManager.swift; sourceTree = "<group>"; };
		56AFFC2C2E0D72CA00C14673 /* NetworkManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NetworkManager.swift; sourceTree = "<group>"; };
		56AFFC2D2E0D72CA00C14673 /* UserManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserManager.swift; sourceTree = "<group>"; };
		56AFFC2F2E0D72CA00C14673 /* CommonModels.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CommonModels.swift; sourceTree = "<group>"; };
		56AFFC312E0D72CA00C14673 /* CommonProtocols.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CommonProtocols.swift; sourceTree = "<group>"; };
		56AFFC322E0D72CA00C14673 /* ModuleCoordinator.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ModuleCoordinator.swift; sourceTree = "<group>"; };
		56AFFC342E0D72CA00C14673 /* DependencyContainer.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DependencyContainer.swift; sourceTree = "<group>"; };
		56AFFC372E0D72CA00C14673 /* WebViewManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WebViewManager.swift; sourceTree = "<group>"; };
		56AFFC3B2E0D72CA00C14673 /* SplashView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SplashView.swift; sourceTree = "<group>"; };
		56AFFC3C2E0D72CA00C14673 /* WebView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WebView.swift; sourceTree = "<group>"; };
		56AFFC3D2E0D72CA00C14673 /* WebViewContainerView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WebViewContainerView.swift; sourceTree = "<group>"; };
		56AFFC402E0D72CA00C14673 /* BackgroundTaskManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BackgroundTaskManager.swift; sourceTree = "<group>"; };
		56AFFC412E0D72CA00C14673 /* CallManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CallManager.swift; sourceTree = "<group>"; };
		56AFFC422E0D72CA00C14673 /* PushNotificationManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PushNotificationManager.swift; sourceTree = "<group>"; };
		56AFFC442E0D72CA00C14673 /* VoIPModels.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VoIPModels.swift; sourceTree = "<group>"; };
		56AFFC472E0D72CA00C14673 /* CallViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CallViewModel.swift; sourceTree = "<group>"; };
		56AFFC492E0D72CA00C14673 /* CallConnectView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CallConnectView.swift; sourceTree = "<group>"; };
		56AFFC4A2E0D72CA00C14673 /* CallIncomingView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CallIncomingView.swift; sourceTree = "<group>"; };
		56AFFC4B2E0D72CA00C14673 /* CallOutgoingView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CallOutgoingView.swift; sourceTree = "<group>"; };
		56AFFC4C2E0D72CA00C14673 /* SetCalleeView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SetCalleeView.swift; sourceTree = "<group>"; };
		56AFFC4F2E0D72CA00C14673 /* DeviceMessageHandler.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DeviceMessageHandler.swift; sourceTree = "<group>"; };
		56AFFC502E0D72CA00C14673 /* NavigationMessageHandler.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NavigationMessageHandler.swift; sourceTree = "<group>"; };
		56AFFC512E0D72CA00C14673 /* StorageMessageHandler.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StorageMessageHandler.swift; sourceTree = "<group>"; };
		56AFFC522E0D72CA00C14673 /* SystemMessageHandler.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SystemMessageHandler.swift; sourceTree = "<group>"; };
		56AFFC532E0D72CA00C14673 /* UserMessageHandler.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserMessageHandler.swift; sourceTree = "<group>"; };
		56AFFC542E0D72CA00C14673 /* VoIPMessageHandler.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VoIPMessageHandler.swift; sourceTree = "<group>"; };
		56AFFC562E0D72CA00C14673 /* BridgeProtocols.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BridgeProtocols.swift; sourceTree = "<group>"; };
		56AFFC572E0D72CA00C14673 /* MessageProtocol.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MessageProtocol.swift; sourceTree = "<group>"; };
		56AFFC592E0D72CA00C14673 /* MessageRouter.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MessageRouter.swift; sourceTree = "<group>"; };
		56AFFC5A2E0D72CA00C14673 /* ResponseManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ResponseManager.swift; sourceTree = "<group>"; };
		56AFFC5B2E0D72CA00C14673 /* WebViewBridge.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WebViewBridge.swift; sourceTree = "<group>"; };
		56E7F8A12E04123456789ABC /* wadar.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = wadar.entitlements; sourceTree = "<group>"; };
		7D593A36BC3AFCB07A4CB05E /* Pods-wadar.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-wadar.release.xcconfig"; path = "Target Support Files/Pods-wadar/Pods-wadar.release.xcconfig"; sourceTree = "<group>"; };
		879C318B0B73443A93ECAC45 /* AuthenticationModels.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = wadar/Modules/Authentication/Models/AuthenticationModels.swift; sourceTree = "<group>"; };
		9A1FE6D16E584D3984A5EED5 /* BridgeProtocols.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = wadar/Modules/WebViewBridge/Protocols/BridgeProtocols.swift; sourceTree = "<group>"; };
		BC215C8C89EA49BE9AB9B947 /* CommonModels.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = wadar/Modules/Common/Models/CommonModels.swift; sourceTree = "<group>"; };
		CC002C43E60044F392F0E667 /* CommonProtocols.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = wadar/Modules/Common/Protocols/CommonProtocols.swift; sourceTree = "<group>"; };
		D46F5D22026970EFEAAC9125 /* Pods-wadar.test.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-wadar.test.xcconfig"; path = "Target Support Files/Pods-wadar/Pods-wadar.test.xcconfig"; sourceTree = "<group>"; };
		EA696D4C6A3BAFC827FA092B /* Pods_wadar.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_wadar.framework; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		1A5325592C2A9C0D00B8534E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				509639869DE4FE3A3B4F2511 /* Pods_wadar.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		1A5325532C2A9C0D00B8534E = {
			isa = PBXGroup;
			children = (
				1A53255E2C2A9C0D00B8534E /* wadar */,
				1A53255D2C2A9C0D00B8534E /* Products */,
				DE17A12E87FA8AE1828F70C6 /* Pods */,
				B0916AE41E4EBD75AFEF2591 /* Frameworks */,
				56AFFC1C2E0D726400C14673 /* Recovered References */,
			);
			sourceTree = "<group>";
		};
		1A53255D2C2A9C0D00B8534E /* Products */ = {
			isa = PBXGroup;
			children = (
				1A53255C2C2A9C0D00B8534E /* wadar.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		1A53255E2C2A9C0D00B8534E /* wadar */ = {
			isa = PBXGroup;
			children = (
				56AFFC5E2E0D72CA00C14673 /* Modules */,
				569B450D2E02C02F006A6849 /* AppDelegate.swift */,
				1A53255F2C2A9C0D00B8534E /* wadarApp.swift */,
				56801C4B2DFAAA9700DEDAD3 /* ContentView.swift */,
				1A5325632C2A9C0E00B8534E /* Assets.xcassets */,
				1A5325652C2A9C0E00B8534E /* Preview Content */,
				56E7F8A12E04123456789ABC /* wadar.entitlements */,
			);
			path = wadar;
			sourceTree = "<group>";
		};
		1A5325652C2A9C0E00B8534E /* Preview Content */ = {
			isa = PBXGroup;
			children = (
				1A5325662C2A9C0E00B8534E /* Preview Assets.xcassets */,
			);
			path = "Preview Content";
			sourceTree = "<group>";
		};
		56AFFC1C2E0D726400C14673 /* Recovered References */ = {
			isa = PBXGroup;
			children = (
				CC002C43E60044F392F0E667 /* CommonProtocols.swift */,
				BC215C8C89EA49BE9AB9B947 /* CommonModels.swift */,
				485DAEB88A5E445FAD3A7236 /* DependencyContainer.swift */,
				19AB02294FAC4B92A2713E50 /* ModuleCoordinator.swift */,
				879C318B0B73443A93ECAC45 /* AuthenticationModels.swift */,
				26FD46AECBCD44DABC47A9DC /* VoIPModels.swift */,
				9A1FE6D16E584D3984A5EED5 /* BridgeProtocols.swift */,
			);
			name = "Recovered References";
			sourceTree = "<group>";
		};
		56AFFC1E2E0D72CA00C14673 /* Models */ = {
			isa = PBXGroup;
			children = (
				56AFFC1D2E0D72CA00C14673 /* AuthenticationModels.swift */,
			);
			path = Models;
			sourceTree = "<group>";
		};
		56AFFC202E0D72CA00C14673 /* Services */ = {
			isa = PBXGroup;
			children = (
				56AFFC1F2E0D72CA00C14673 /* VCodeService.swift */,
			);
			path = Services;
			sourceTree = "<group>";
		};
		56AFFC242E0D72CA00C14673 /* ViewModels */ = {
			isa = PBXGroup;
			children = (
				56AFFC212E0D72CA00C14673 /* ForgotPasswordViewModel.swift */,
				56AFFC222E0D72CA00C14673 /* LoginViewModel.swift */,
				56AFFC232E0D72CA00C14673 /* RegisterViewModel.swift */,
			);
			path = ViewModels;
			sourceTree = "<group>";
		};
		56AFFC282E0D72CA00C14673 /* Views */ = {
			isa = PBXGroup;
			children = (
				56AFFC252E0D72CA00C14673 /* ForgotPasswordView.swift */,
				56AFFC262E0D72CA00C14673 /* LoginView.swift */,
				56AFFC272E0D72CA00C14673 /* RegisterView.swift */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		56AFFC292E0D72CA00C14673 /* Authentication */ = {
			isa = PBXGroup;
			children = (
				56AFFC1E2E0D72CA00C14673 /* Models */,
				56AFFC202E0D72CA00C14673 /* Services */,
				56AFFC242E0D72CA00C14673 /* ViewModels */,
				56AFFC282E0D72CA00C14673 /* Views */,
			);
			path = Authentication;
			sourceTree = "<group>";
		};
		56AFFC2E2E0D72CA00C14673 /* Managers */ = {
			isa = PBXGroup;
			children = (
				56AFFC2A2E0D72CA00C14673 /* AppConfig.swift */,
				56AFFC2B2E0D72CA00C14673 /* NavigationManager.swift */,
				56AFFC2C2E0D72CA00C14673 /* NetworkManager.swift */,
				56AFFC2D2E0D72CA00C14673 /* UserManager.swift */,
			);
			path = Managers;
			sourceTree = "<group>";
		};
		56AFFC302E0D72CA00C14673 /* Models */ = {
			isa = PBXGroup;
			children = (
				56AFFC2F2E0D72CA00C14673 /* CommonModels.swift */,
			);
			path = Models;
			sourceTree = "<group>";
		};
		56AFFC332E0D72CA00C14673 /* Protocols */ = {
			isa = PBXGroup;
			children = (
				56AFFC312E0D72CA00C14673 /* CommonProtocols.swift */,
				56AFFC322E0D72CA00C14673 /* ModuleCoordinator.swift */,
			);
			path = Protocols;
			sourceTree = "<group>";
		};
		56AFFC352E0D72CA00C14673 /* Utils */ = {
			isa = PBXGroup;
			children = (
				56AFFC342E0D72CA00C14673 /* DependencyContainer.swift */,
				2C9148060F1F4FA9904865BE4570BC90 /* ConfigTest.swift */,
				3D0259170G2G5GB0A05976CF5681CD01 /* ESPProvisionTest.swift */,
			);
			path = Utils;
			sourceTree = "<group>";
		};
		56AFFC362E0D72CA00C14673 /* Common */ = {
			isa = PBXGroup;
			children = (
				56AFFC2E2E0D72CA00C14673 /* Managers */,
				56AFFC302E0D72CA00C14673 /* Models */,
				56AFFC332E0D72CA00C14673 /* Protocols */,
				56AFFC352E0D72CA00C14673 /* Utils */,
			);
			path = Common;
			sourceTree = "<group>";
		};
		56AFFC382E0D72CA00C14673 /* Managers */ = {
			isa = PBXGroup;
			children = (
				56AFFC372E0D72CA00C14673 /* WebViewManager.swift */,
			);
			path = Managers;
			sourceTree = "<group>";
		};
		56AFFC3E2E0D72CA00C14673 /* Views */ = {
			isa = PBXGroup;
			children = (
				56AFFC3B2E0D72CA00C14673 /* SplashView.swift */,
				56AFFC3C2E0D72CA00C14673 /* WebView.swift */,
				56AFFC3D2E0D72CA00C14673 /* WebViewContainerView.swift */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		56AFFC3F2E0D72CA00C14673 /* Main */ = {
			isa = PBXGroup;
			children = (
				56AFFC382E0D72CA00C14673 /* Managers */,
				56AFFC3E2E0D72CA00C14673 /* Views */,
			);
			path = Main;
			sourceTree = "<group>";
		};
		56AFFC432E0D72CA00C14673 /* Managers */ = {
			isa = PBXGroup;
			children = (
				56AFFC402E0D72CA00C14673 /* BackgroundTaskManager.swift */,
				56AFFC412E0D72CA00C14673 /* CallManager.swift */,
				56AFFC422E0D72CA00C14673 /* PushNotificationManager.swift */,
			);
			path = Managers;
			sourceTree = "<group>";
		};
		56AFFC452E0D72CA00C14673 /* Models */ = {
			isa = PBXGroup;
			children = (
				56AFFC442E0D72CA00C14673 /* VoIPModels.swift */,
			);
			path = Models;
			sourceTree = "<group>";
		};
		56AFFC462E0D72CA00C14673 /* Services */ = {
			isa = PBXGroup;
			children = (
			);
			path = Services;
			sourceTree = "<group>";
		};
		56AFFC482E0D72CA00C14673 /* ViewModels */ = {
			isa = PBXGroup;
			children = (
				56AFFC472E0D72CA00C14673 /* CallViewModel.swift */,
			);
			path = ViewModels;
			sourceTree = "<group>";
		};
		56AFFC4D2E0D72CA00C14673 /* Views */ = {
			isa = PBXGroup;
			children = (
				56AFFC492E0D72CA00C14673 /* CallConnectView.swift */,
				56AFFC4A2E0D72CA00C14673 /* CallIncomingView.swift */,
				56AFFC4B2E0D72CA00C14673 /* CallOutgoingView.swift */,
				56AFFC4C2E0D72CA00C14673 /* SetCalleeView.swift */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		56AFFC4E2E0D72CA00C14673 /* VoIP */ = {
			isa = PBXGroup;
			children = (
				56AFFC432E0D72CA00C14673 /* Managers */,
				56AFFC452E0D72CA00C14673 /* Models */,
				56AFFC462E0D72CA00C14673 /* Services */,
				56AFFC482E0D72CA00C14673 /* ViewModels */,
				56AFFC4D2E0D72CA00C14673 /* Views */,
			);
			path = VoIP;
			sourceTree = "<group>";
		};
		56AFFC552E0D72CA00C14673 /* Handlers */ = {
			isa = PBXGroup;
			children = (
				56AFFC4F2E0D72CA00C14673 /* DeviceMessageHandler.swift */,
				56AFFC502E0D72CA00C14673 /* NavigationMessageHandler.swift */,
				56AFFC512E0D72CA00C14673 /* StorageMessageHandler.swift */,
				56AFFC522E0D72CA00C14673 /* SystemMessageHandler.swift */,
				56AFFC532E0D72CA00C14673 /* UserMessageHandler.swift */,
				56AFFC542E0D72CA00C14673 /* VoIPMessageHandler.swift */,
			);
			path = Handlers;
			sourceTree = "<group>";
		};
		56AFFC582E0D72CA00C14673 /* Protocols */ = {
			isa = PBXGroup;
			children = (
				56AFFC562E0D72CA00C14673 /* BridgeProtocols.swift */,
				56AFFC572E0D72CA00C14673 /* MessageProtocol.swift */,
			);
			path = Protocols;
			sourceTree = "<group>";
		};
		56AFFC5C2E0D72CA00C14673 /* Utils */ = {
			isa = PBXGroup;
			children = (
				56AFFC592E0D72CA00C14673 /* MessageRouter.swift */,
				56AFFC5A2E0D72CA00C14673 /* ResponseManager.swift */,
				56AFFC5B2E0D72CA00C14673 /* WebViewBridge.swift */,
			);
			path = Utils;
			sourceTree = "<group>";
		};
		56AFFC5D2E0D72CA00C14673 /* WebViewBridge */ = {
			isa = PBXGroup;
			children = (
				56AFFC552E0D72CA00C14673 /* Handlers */,
				56AFFC582E0D72CA00C14673 /* Protocols */,
				56AFFC5C2E0D72CA00C14673 /* Utils */,
			);
			path = WebViewBridge;
			sourceTree = "<group>";
		};
		ESPMODULE001234567890ABCD /* ESPProvision */ = {
			isa = PBXGroup;
			children = (
				ESPMANAGERS01234567890ABC /* Managers */,
				ESPVIEWMODELS1234567890AB /* ViewModels */,
				ESPVIEWS123456789012345A /* Views */,
			);
			path = "ESPProvision";
			sourceTree = "<group>";
		};
		ESPMANAGERS01234567890ABC /* Managers */ = {
			isa = PBXGroup;
			children = (
				ESP001F1E2D3C4B5A6978H9I0J /* ESPProvisionManager.swift */,
			);
			path = Managers;
			sourceTree = "<group>";
		};
		ESPVIEWMODELS1234567890AB /* ViewModels */ = {
			isa = PBXGroup;
			children = (
				ESP002G2F3E4D5C6B7A89I0J1K /* ESPProvisionViewModel.swift */,
			);
			path = ViewModels;
			sourceTree = "<group>";
		};
		ESPVIEWS123456789012345A /* Views */ = {
			isa = PBXGroup;
			children = (
				ESP003H3G4F5E6D7C8B9A0K1L2 /* ESPProvisionView.swift */,
				ESP004I4H5G6F7E8D9C0B1L2M3 /* DeviceScanView.swift */,
				ESP005J5I6H7G8F9E0D1C2M3N4 /* WiFiConfigView.swift */,
				ESP006K6J7I8H9G0F1E2D3N4O5 /* DeviceConnectView.swift */,
				ESP007L7K8J9I0H1G2F3E4O5P6 /* ProvisioningView.swift */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		56AFFC5E2E0D72CA00C14673 /* Modules */ = {
			isa = PBXGroup;
			children = (
				56AFFC292E0D72CA00C14673 /* Authentication */,
				56AFFC362E0D72CA00C14673 /* Common */,
				ESPMODULE001234567890ABCD /* ESPProvision */,
				56AFFC3F2E0D72CA00C14673 /* Main */,
				56AFFC4E2E0D72CA00C14673 /* VoIP */,
				56AFFC5D2E0D72CA00C14673 /* WebViewBridge */,
			);
			path = Modules;
			sourceTree = "<group>";
		};
		B0916AE41E4EBD75AFEF2591 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				EA696D4C6A3BAFC827FA092B /* Pods_wadar.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		DE17A12E87FA8AE1828F70C6 /* Pods */ = {
			isa = PBXGroup;
			children = (
				4C66385879DADED059AE5D23 /* Pods-wadar.debug.xcconfig */,
				7D593A36BC3AFCB07A4CB05E /* Pods-wadar.release.xcconfig */,
				D46F5D22026970EFEAAC9125 /* Pods-wadar.test.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		1A53255B2C2A9C0D00B8534E /* wadar */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 1A53256A2C2A9C0E00B8534E /* Build configuration list for PBXNativeTarget "wadar" */;
			buildPhases = (
				DBB5C75751655C7ED41EA41D /* [CP] Check Pods Manifest.lock */,
				1A5325582C2A9C0D00B8534E /* Sources */,
				1A5325592C2A9C0D00B8534E /* Frameworks */,
				1A53255A2C2A9C0D00B8534E /* Resources */,
				272A3B9BFE39BB7D7348D7FB /* [CP] Embed Pods Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = wadar;
			productName = wadar;
			productReference = 1A53255C2C2A9C0D00B8534E /* wadar.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		1A5325542C2A9C0D00B8534E /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1540;
				LastUpgradeCheck = 1540;
				TargetAttributes = {
					1A53255B2C2A9C0D00B8534E = {
						CreatedOnToolsVersion = 15.4;
					};
				};
			};
			buildConfigurationList = 1A5325572C2A9C0D00B8534E /* Build configuration list for PBXProject "wadar" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 1A5325532C2A9C0D00B8534E;
			productRefGroup = 1A53255D2C2A9C0D00B8534E /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				1A53255B2C2A9C0D00B8534E /* wadar */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		1A53255A2C2A9C0D00B8534E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				1A5325672C2A9C0E00B8534E /* Preview Assets.xcassets in Resources */,
				1A5325642C2A9C0E00B8534E /* Assets.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		272A3B9BFE39BB7D7348D7FB /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-wadar/Pods-wadar-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-wadar/Pods-wadar-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-wadar/Pods-wadar-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		DBB5C75751655C7ED41EA41D /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-wadar-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		1A5325582C2A9C0D00B8534E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				56AFFC5F2E0D72CB00C14673 /* UserManager.swift in Sources */,
				56AFFC602E0D72CB00C14673 /* AppConfig.swift in Sources */,
				56AFFC612E0D72CB00C14673 /* NetworkManager.swift in Sources */,
				56AFFC622E0D72CB00C14673 /* VCodeService.swift in Sources */,
				56AFFC632E0D72CB00C14673 /* WebViewBridge.swift in Sources */,
				56AFFC642E0D72CB00C14673 /* SplashView.swift in Sources */,
				56AFFC652E0D72CB00C14673 /* PushNotificationManager.swift in Sources */,
				56AFFC662E0D72CB00C14673 /* ForgotPasswordView.swift in Sources */,
				56AFFC672E0D72CB00C14673 /* CallManager.swift in Sources */,
				56AFFC682E0D72CB00C14673 /* ForgotPasswordViewModel.swift in Sources */,
				56AFFC6A2E0D72CB00C14673 /* CallIncomingView.swift in Sources */,
				56AFFC6B2E0D72CB00C14673 /* CommonModels.swift in Sources */,
				56AFFC6C2E0D72CB00C14673 /* CallOutgoingView.swift in Sources */,
				56AFFC6D2E0D72CB00C14673 /* DeviceMessageHandler.swift in Sources */,
				56AFFC6E2E0D72CB00C14673 /* StorageMessageHandler.swift in Sources */,
				56AFFC6F2E0D72CB00C14673 /* LoginView.swift in Sources */,
				56AFFC702E0D72CB00C14673 /* VoIPMessageHandler.swift in Sources */,
				56AFFC712E0D72CB00C14673 /* MessageProtocol.swift in Sources */,
				56AFFC722E0D72CB00C14673 /* ResponseManager.swift in Sources */,
				56AFFC732E0D72CB00C14673 /* DependencyContainer.swift in Sources */,
				56AFFC742E0D72CB00C14673 /* UserMessageHandler.swift in Sources */,
				56AFFC752E0D72CB00C14673 /* LoginViewModel.swift in Sources */,
				56AFFC762E0D72CB00C14673 /* ModuleCoordinator.swift in Sources */,
				56AFFC772E0D72CB00C14673 /* MessageRouter.swift in Sources */,
				56AFFC782E0D72CB00C14673 /* WebViewContainerView.swift in Sources */,
				56AFFC792E0D72CB00C14673 /* NavigationMessageHandler.swift in Sources */,
				56AFFC7A2E0D72CB00C14673 /* WebView.swift in Sources */,
				56AFFC7B2E0D72CB00C14673 /* VoIPModels.swift in Sources */,
				56AFFC7C2E0D72CB00C14673 /* CallViewModel.swift in Sources */,
				56AFFC7D2E0D72CB00C14673 /* CommonProtocols.swift in Sources */,
				56AFFC7E2E0D72CB00C14673 /* SetCalleeView.swift in Sources */,
				56AFFC7F2E0D72CB00C14673 /* RegisterViewModel.swift in Sources */,
				56AFFC802E0D72CB00C14673 /* SystemMessageHandler.swift in Sources */,
				56AFFC812E0D72CB00C14673 /* WebViewManager.swift in Sources */,
				56AFFC822E0D72CB00C14673 /* BridgeProtocols.swift in Sources */,
				56AFFC832E0D72CB00C14673 /* RegisterView.swift in Sources */,
				56AFFC842E0D72CB00C14673 /* CallConnectView.swift in Sources */,
				56AFFC852E0D72CB00C14673 /* NavigationManager.swift in Sources */,
				56AFFC862E0D72CB00C14673 /* BackgroundTaskManager.swift in Sources */,
				56AFFC872E0D72CB00C14673 /* AuthenticationModels.swift in Sources */,
				569B450E2E02C02F006A6849 /* AppDelegate.swift in Sources */,
				56801C4C2DFAAAA100DEDAD3 /* ContentView.swift in Sources */,
				1A5325602C2A9C0D00B8534E /* wadarApp.swift in Sources */,
				A145809A389B458B8D5AB593 /* CommonProtocols.swift in Sources */,
				04FC498BF615497C84D59C56 /* CommonModels.swift in Sources */,
				74AA1308EC50462EBD2ACA0C /* DependencyContainer.swift in Sources */,
				4ADF6AA13CDF4EC6A9D808B5DC1CC155 /* ConfigTest.swift in Sources */,
				5BEF7BB24DEF5FD7B0E909C6 /* ESPProvisionTest.swift in Sources */,
				ESP001A1B2C3D4E5F6G7H8I9J /* ESPProvisionManager.swift in Sources */,
				ESP002B2C3D4E5F6G7H8I9J0K /* ESPProvisionViewModel.swift in Sources */,
				ESP003C3D4E5F6G7H8I9J0K1L /* ESPProvisionView.swift in Sources */,
				ESP004D4E5F6G7H8I9J0K1L2M /* DeviceScanView.swift in Sources */,
				ESP005E5F6G7H8I9J0K1L2M3N /* WiFiConfigView.swift in Sources */,
				ESP006F6G7H8I9J0K1L2M3N4O /* DeviceConnectView.swift in Sources */,
				ESP007G7H8I9J0K1L2M3N4O5P /* ProvisioningView.swift in Sources */,
				EF0716CA972B4E45BF9FFDD7 /* ModuleCoordinator.swift in Sources */,
				B8F7A1E5A4B84F6A9C3D2E71 /* AuthenticationModels.swift in Sources */,
				C9E8B2F6B5C95G7B0D4E3F82 /* VoIPModels.swift in Sources */,
				D0F9C3G7C6D06H8C1E5F4G93 /* BridgeProtocols.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		1A5325682C2A9C0E00B8534E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_PREPROCESS = NO;
				IPHONEOS_DEPLOYMENT_TARGET = 16.6;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG DEV $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		1A5325692C2A9C0E00B8534E /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_PREPROCESS = NO;
				IPHONEOS_DEPLOYMENT_TARGET = 16.6;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "PROD $(inherited)";
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		1A53256B2C2A9C0E00B8534E /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 4C66385879DADED059AE5D23 /* Pods-wadar.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = wadar/wadar.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"wadar/Preview Content\"";
				DEVELOPMENT_TEAM = "";
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_BGTaskSchedulerPermittedIdentifiers = com.wadar.voip.background;
				INFOPLIST_KEY_NSMicrophoneUsageDescription = "We need microphone access for calling functionality";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "需要访问相册以设置头像";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UIBackgroundModes = "voip background-processing background-app-refresh";
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = wvr.wadar;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		1A53256C2C2A9C0E00B8534E /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7D593A36BC3AFCB07A4CB05E /* Pods-wadar.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = wadar/wadar.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"wadar/Preview Content\"";
				DEVELOPMENT_TEAM = "";
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_BGTaskSchedulerPermittedIdentifiers = com.wadar.voip.background;
				INFOPLIST_KEY_NSMicrophoneUsageDescription = "We need microphone access for calling functionality";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "需要访问相册以设置头像";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UIBackgroundModes = "voip background-processing background-app-refresh";
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = wvr.wadar;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		5691B96A2E0E3441005DF727 /* Test */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_PREPROCESS = NO;
				IPHONEOS_DEPLOYMENT_TARGET = 16.6;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_SWIFT_FLAGS = "-DTEST";
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG TEST $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Test;
		};
		5691B96B2E0E3441005DF727 /* Test */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = D46F5D22026970EFEAAC9125 /* Pods-wadar.test.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = wadar/wadar.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"wadar/Preview Content\"";
				DEVELOPMENT_TEAM = "";
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_BGTaskSchedulerPermittedIdentifiers = com.wadar.voip.background;
				INFOPLIST_KEY_NSMicrophoneUsageDescription = "We need microphone access for calling functionality";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "需要访问相册以设置头像";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UIBackgroundModes = "voip background-processing background-app-refresh";
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = wvr.wadar;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Test;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		1A5325572C2A9C0D00B8534E /* Build configuration list for PBXProject "wadar" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1A5325682C2A9C0E00B8534E /* Debug */,
				5691B96A2E0E3441005DF727 /* Test */,
				1A5325692C2A9C0E00B8534E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		1A53256A2C2A9C0E00B8534E /* Build configuration list for PBXNativeTarget "wadar" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1A53256B2C2A9C0E00B8534E /* Debug */,
				5691B96B2E0E3441005DF727 /* Test */,
				1A53256C2C2A9C0E00B8534E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 1A5325542C2A9C0D00B8534E /* Project object */;
}
