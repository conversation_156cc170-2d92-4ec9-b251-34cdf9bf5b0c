// 为了让用户能够自定义主题，会逐步弃用此文件，各颜色通过css提供
// 为了给某些特殊场景使用和向后兼容，无需删除此文件(2020-06-20)
let color = {
	primary: "#2979ff",
	primaryDark: "#2b85e4",
	primaryDisabled: "#a0cfff",
	primaryLight: "#ecf5ff",
	bgColor: "#f3f4f6",
	
	info: "#909399",
	infoDark: "#82848a",
	infoDisabled: "#c8c9cc",
	infoLight: "#f4f4f5",
	
	warning: "#ff9900",
	warningDark: "#f29100",
	warningDisabled: "#fcbd71",
	warningLight: "#fdf6ec",
	
	error: "#fa3534",
	errorDark: "#dd6161",
	errorDisabled: "#fab6b6",
	errorLight: "#fef0f0",
	
	success: "#19be6b",
	successDark: "#18b566",
	successDisabled: "#71d5a1",
	successLight: "#dbf1e1",
	
	mainColor: "#303133",
	contentColor: "#606266",
	tipsColor: "#909399",
	lightColor: "#c0c4cc",
	borderColor: "#e4e7ed"
}

export default color;